<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>日食</title>
</head>
<body>
<canvas id="canvas"></canvas>
</body>
<script>
  // document.onload = function () {
  //   let canvas = document.getElementById('canvas')
  //   let ctx = canvas.getContext('2d')
  //   ctx.beginPath()
  //   ctx.arc(75, 75, 50, 0, 2 * Math.PI)
  //   ctx.fill()
  //   ctx.beginPath()
  //   ctx.arc(175, 175, 10, 0, 2 * Math.PI)
  //   ctx.fill()
  // }
  let secondhandId = '134023,134101'
  window.secondhandJump = function () {
    if (!ACT.isLogin) {
      ACT.goLogin()
      return
    }
    ACT.fetch('/web/adm/topicPagemanage/getComponentData/v1', {
      topicId: 6373, secondhandId: secondhandId, platform: 'm'
    }, 'post', 'json').then(function (res) {
      let secondhand = res.secondhand
      if (secondhand && secondhand.length) {
        window.location.href = 'https://m.9ji.com/secondHand/detail/' + secondhand[0].id
      } else {
        window.location.href = 'https://m.9ji.com/secondHand/detail/' + secondhandId.split(',')[0]
      }
    })
  }
</script>
</html>
