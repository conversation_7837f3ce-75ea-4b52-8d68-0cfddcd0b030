<script lang="jsx">
  import { defineComponent, getCurrentInstance } from 'vue'
  import SearchBox from './components/search-box'
  import TableBox from './components/table-box'
  import { createApi } from './hooks/use-api'
  import { createState } from './hooks/use-state'
  import { NiListPage } from '@jiuji/nine-ui'
  import useApiLastUpdateTime from '../hooks/useApiLastUpdateTime'

  export default defineComponent({
    components: {
      SearchBox,
      TableBox,
      NiListPage
    },
    setup () {
      const api = createApi()
      const { lastUpdateDate } = createState(api)
      const { proxy } = getCurrentInstance()
      const params = {
        id: 1,
        type_: proxy.$tnt.xtenant === 0 ? 1 : 2
      }
      const { lastNewUpdateDate, getLastUpdateDate } = useApiLastUpdateTime(params)
      getLastUpdateDate()
      return {
        lastUpdateDate,
        lastNewUpdateDate
      }
    },
    beforeRouteLeave (to, from, next) {
      if (to.path !== '/operation/statistics-report/bussiness-report/perfect-order') {
        localStorage.removeItem('bussinessReportAreaIdsScoped')
      }
      next()
    },
    render () {
      const { lastUpdateDate, lastNewUpdateDate } = this
      return <page class="statistics-report-bussiness-report">
      <div slot="extra">
      <div class="flex flex-align-center flex-justify-between">
      <span>数据更新于{ lastNewUpdateDate || lastUpdateDate}</span>
      </div>
      </div>
      <ni-list-page push-filter-to-location={false}>
        <SearchBox/>
        <table-box/>
      </ni-list-page>

      </page>
    }
  })
</script>
<style lang="scss" scoped>
.statistics-report-bussiness-report {
  position: relative;
  :deep(.ant-page-header-heading-extra){
    float: none;
    line-height: 40px;
    font-size: 12px;
    color: #9c9c9c;
  }
}
</style>
<style lang="scss">
:deep(.kt-canvas-table) {
  font-size: 60px;
  border: 1px solid red !important;
  background: red;
}
</style>
