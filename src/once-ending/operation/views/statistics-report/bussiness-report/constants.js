import moment from 'moment'
export const defaultFormData = {
  areaIds: [],
  times: [
    moment().format('YYYY-MM-DD 00:00:00'),
    moment().format('YYYY-MM-DD 23:59:59'),
  ],
  storeAttribute: [],
  orderStatus: undefined,
  orderType: [],
  chainTimes: [
    moment().subtract(1, 'days').format('YYYY-MM-DD 00:00:00'),
    moment().subtract(1, 'days').format('YYYY-MM-DD 23:59:59'),
  ],
  virtualGoods: undefined,
  costType: undefined,
  storeLevel: undefined,
  storeType: [],
  regionOnly: false,
  storeCategory: [],
  productCategory: [],
  productBrand: [],
  areaStoreIds: [],
  city: [],
  largePrice: undefined,
  largeCustomers: false,
  stalledClearance: undefined,
  gift: undefined,
  eliminateFreebie: undefined, // 输出  1：剔除赠品成本    2：不剔除赠品成本
  zeroOrder: undefined,
  discountCode: undefined,
  recycledWool: undefined,
  excludePriceProtectFanli: undefined
}

export const compareItemsNames = [
  { key: 'areaRatio', label: '区域环比' },
  { key: 'allAreaTradeData', label: '全区交易时间数据' },
  { key: 'allAreaDiffData', label: '全区差值' },
  { key: 'allAreaRatio', label: '全区环比' },
  { key: 'ratioDiffData', label: '环比差值' }
]

export const ranges = {
  今天: [moment().startOf('day'), moment().endOf('day')],
  昨天: [moment().subtract(1, 'days').startOf('day'), moment().subtract(1, 'days').endOf('day')],
  当周: [moment().startOf('week'), moment().endOf('week')],
  上周: [moment().subtract(1, 'weeks').startOf('week'), moment().subtract(1, 'weeks').endOf('week')],
  当月: [moment().startOf('month'), moment().endOf('month')],
  上月: [moment().subtract(1, 'months').startOf('month'), moment().subtract(1, 'months').endOf('month')],
}
