<script lang="jsx">
  import { ref, defineComponent } from 'vue'
  import useSettingBar from '../hooks/useSettingBar'
  import { Dropdown, Menu, Icon } from 'ant-design-vue'

  const ENUMLIST = [
    {
      value: 'default',
      label: '默认'
    },
    {
      value: 'middle',
      label: '中等'
    },
    {
      value: 'small',
      label: '紧凑'
    }
  ]

  export default defineComponent({
    setup () {
      const list = ref(ENUMLIST)
      const { val, selectItem } = useSettingBar('size')

      return {
        list,
        val,
        selectItem,
      }
    },

    render () {
      const { val, list, selectItem } = this
      return <Dropdown getPopupContainer={triggerNode => triggerNode.parentNode}>
          <Icon class="setting-icon" type="column-height" />

          <Menu slot="overlay">
            {list.map(item => (
              <Menu.Item key={item.value}>
                <div
                  class={{ 'active': item.value === val, 'size-menu-item': true }}
                  onClick={() => selectItem(item)}
                >
                  {item.label}
                </div>
              </Menu.Item>
            ))}
          </Menu>
        </Dropdown>
    }
  })

</script>
<style lang="scss" scoped>
  .size-menu-item {
    padding: 3px 10px;
  }
  .active {
    color: #1890ff;
  }
</style>
