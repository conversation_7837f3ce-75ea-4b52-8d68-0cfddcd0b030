<script lang="jsx">
  import { defineComponent, toRefs } from 'vue'
  import { useState } from '../hooks/use-state'
  import useTable from '../hooks/use-table'
  import { Switch, Button, Input, Spin, Tooltip, Icon } from 'ant-design-vue'
  import EditNote from './edit-note'
  import DisplayNote from './display-note'
  import CanvasTable from '~/components/kt-canvas-table'
  import TableSize from './table-size'
  import TableFullScreen from './table-full-screen'
  import TableBordered from './table-bordered'
  import TableColumns from './table-columns'

  import {
    utilCreateCols,
    initCheckedList
  } from './util/columns'

  export default defineComponent({
    components: {
      EditNote,
      DisplayNote,
      CanvasTable,
      TableSize,
      TableFullScreen,
      TableBordered,
      TableColumns
    },
    setup () {
      const { loading, state, hasBbxs, hasBbzs, canvasTable } = useState()
      const {
        handleRaceEditFactor,
        raceEditFactorLoading,
        factorData,
        handleRaceGetFactor,
        exportData,
        exportDataLoading,
        canvasTableAttrs,
        cols,
        toolSwitchChange
      } = useTable(utilCreateCols, initCheckedList)

      return {
        loading,
        ...toRefs(state),
        handleRaceEditFactor,
        raceEditFactorLoading,
        hasBbxs,
        hasBbzs,
        factorData,
        handleRaceGetFactor,
        exportData,
        exportDataLoading,
        canvasTableAttrs,
        cols,
        canvasTable,
        toolSwitchChange
      }
    },
    render () {
      const {
        dataSource,
        cols,
        loading,
        formData,
        handleRaceEditFactor,
        raceEditFactorLoading,
        hasBbxs,
        hasBbzs,
        factorData,
        handleRaceGetFactor,
        exportData,
        exportDataLoading,
        summary,
        canvasTableAttrs,
        toolSwitchChange
      } = this
      return (
      <div class="bussiness-report-table mt-10">
        <div class="table-bar flex flex-align-center">
          <div class="table-bar-main flex-child-average flex flex-align-center">
            <div class="tool flex-child-average flex flex-align-center">
              <div class="tool flex flex-align-center">
                <div class="mr-16">
                  <span>剔除大客户：</span>
                  <Switch v-model={formData.largeCustomers} onChange={toolSwitchChange}/>
                </div>
                <div class="mr-16">
                  <span>仅大区：</span>
                  <Switch v-model={formData.regionOnly} onChange={toolSwitchChange}/>
                </div>
                <div class="mr-16">
                  <span>剔除滞销/清仓商品：</span>
                  <Switch v-model={formData.stalledClearance} onChange={toolSwitchChange}/>
                </div>
                {
                  this.$tnt.xtenant < 1000 ? <div class="mr-16">
                    <span>剔除赠品：</span>
                    <Switch v-model={formData.gift} onChange={toolSwitchChange}/>
                  </div> : null
                }
                <div class="mr-16">
                  <span>剔除纯积分订单：</span>
                  <Switch v-model={formData.zeroOrder} onChange={toolSwitchChange}/>
                </div>
                <div class="mr-16">
                  <span>剔除优惠码<Tooltip>
              <template slot="title">不参与利润核算</template>
              <Icon style="width:20px;color:#1890ff" class="pointer" type="question-circle" />
            </Tooltip>：</span>
                  <Switch v-model={formData.discountCode} onChange={toolSwitchChange}/>
                </div>
                {
                  this.$tnt.xtenant >= 1000 ? <div class="mr-16">
                    <span>成本剔除价保：</span>
                    <Switch v-model={formData.excludePriceProtectFanli} onChange={toolSwitchChange}/>
                  </div> : null
                }
                {this.$tnt.xtenant !== 0 && hasBbxs ? (
                  <div class="factor flex flex-align-center">
                    <div class="flex flex-align-center mr-16">
                      <span style="width:130px">
                        自营服务毛利系数
                        {hasBbzs ? (
                          <EditNote
                            type={104}
                            field="selfServiceProfitNote"
                            description={factorData.selfServiceProfitNote}
                            is-edit={factorData.selfServiceProfitNoteEdit}
                            onRaceFactor={handleRaceGetFactor}
                            {...{
                              on: {
                                'update:isEdit': (val) => {
                                  factorData.selfServiceProfitNoteEdit = val
                                },
                              },
                            }}
                          />
                        ) : (
                          <DisplayNote
                            description={factorData.selfServiceProfitNote}
                          />
                        )}
                        ：
                      </span>
                      <Input
                        style="width:100px"
                        v-model={factorData.selfServiceProfitFactor}
                        allow-clear
                      />
                    </div>
                    <div class="flex flex-align-center mr-16">
                      <span style={{ width: this.$tnt.xtenant < 1000 ? '130px' : '102px' }}>
                        {`${this.$tnt.xtenant < 1000 ? '上月' : ''}`}回收单毛系数
                        {hasBbzs ? (
                          <EditNote
                            type={104}
                            field="recoverSingleProfitNote"
                            description={factorData.recoverSingleProfitNote}
                            is-edit={factorData.recoverSingleProfitNoteEdit}
                            {...{
                              on: {
                                'update:isEdit': (val) => {
                                  factorData.recoverSingleProfitNoteEdit = val
                                },
                              },
                            }}
                          />
                        ) : (
                          <DisplayNote
                            description={factorData.recoverSingleProfitNote}
                          />
                        )}
                        ：
                      </span>
                      <Input
                        style="width:100px"
                        v-model={factorData.recoverSingleProfitFactor}
                        allow-clear
                      />
                    </div>
                    <Button
                      type="primary"
                      loading={raceEditFactorLoading}
                      onClick={handleRaceEditFactor}
                    >
                      保存
                    </Button>
                  </div>
                ) : null}
              </div>
            </div>
            <div class="action">
              <Button loading={exportDataLoading} onClick={exportData}>
                {exportDataLoading ? '导出中' : '导出'}
              </Button>
            </div>
          </div>
          <div class="table-bar-setting">
            <Tooltip title="边框" class="btns-item">
              <TableBordered />
            </Tooltip>
            <Tooltip title="密度" class="btns-item">
              <TableSize />
            </Tooltip>
            <Tooltip
         title="列设置" class="btns-item">
        <TableColumns />
      </Tooltip>
            <Tooltip title="全屏" class="btns-item">
              <TableFullScreen />
            </Tooltip>
          </div>
        </div>
        <div style="padding:0 16px 16px 16px;background:#fff">
          <Spin spinning={loading}>
            <CanvasTable
              ref="canvasTable"
              data={dataSource}
              columns={cols}
              summary={summary}
              {...{ props: canvasTableAttrs }}
            />
          </Spin>
        </div>
      </div>
      )
    },
  })
</script>
<style lang="scss">
.bussiness-report-table {
  .sale-sort-icon {
    color: #bfbfbf;
    height: 1em;
    width: 1em;
    line-height: 0.5;
    font-size: 11px;
    margin-top: -0.6em;
    //通过传参请求接口排序的，但是后台接口没有排序，先隐藏排序功能
    display: none;
  }
  .sale-sort-icon-item {
    height: 0.5em;
  }
  .sale-icon-active {
    color: #1890ff;
  }
  .table-bar {
    padding: 8px 16px;
    background-color: #fff;
    .table-bar-main {
      padding: 0;
    }

    .table-bar-setting {
      padding: 0;
    }

    .btns-item {
      margin-left: 16px;
    }
  }

  .setting-icon {
    cursor: pointer;
  }

  .kt-canvas-table_border {
    .kt-canvas-table-slot {
      border-right: 0.5px solid #dfdfdf;
      border-bottom: 0.5px solid #dfdfdf;
    }
  }
}
</style>
<style lang="scss" scoped>
.tool {
  height: 100%;
}
.tool,
.factor {
  display: flex;
  align-items: center;
}
.nine-table {
  margin-top: 16px;
}
:deep(.big-area) {
  background: #bae7ff;
}
:deep(.small-area) {
  background: #e6f7ff;
}
</style>
