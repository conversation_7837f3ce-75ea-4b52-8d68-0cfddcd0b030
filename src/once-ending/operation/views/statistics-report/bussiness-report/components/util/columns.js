import { cloneDeep } from 'lodash'

export function getColumns (columns) {
  let arr = cloneDeep(columns)

  if (!Array.isArray(arr) || !arr.length) {
    return []
  }

  arr = arr.map(v => {
    v.isFirst = true
    return v
  })

  const arrFilter = arr => {
    return arr.map(v => {
      v.label = v.titleSetting || v.label
      if (v.children && v.children.length) {
        v.children = arrFilter(v.children)
      }

      return v
    })
  }
  const l = arrFilter(arr)
  return l
}

export function setColumns (columns, tableColumns = {}, tableColumnsSort = []) {
  const arr = cloneDeep(columns)

  if (!Array.isArray(arr) || !arr.length) {
    return []
  }

  const l = arr.map(v => {
    const o = tableColumns[v.dataIndex]
    if (o && o.fixed !== undefined) {
      v.fixed = o.fixed
    }

    return v
  })

  if (!tableColumnsSort.length) {
    return l
  }

  const arr1 = []
  const arr2 = []

  tableColumnsSort.forEach(v => {
    const item = l.find(e => e.dataIndex === v)
    if (item) {
      arr1.push(item)
    }
  })

  l.forEach(v => {
    const o = tableColumnsSort.includes(v.dataIndex)
    if (!o) {
      v.isNew = true
      arr2.push(v)
    }
  })

  const allArr = [...arr1, ...arr2]

  return allArr
}

export function getSortColumns (l, info) {
  const dropKey = info.node.eventKey
  const dragKey = info.dragNode.eventKey
  const dropPos = info.node.pos.split('-')
  const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1])
  const currentIndex = l.findIndex(v => v.dataIndex === dragKey)
  const tagetIndex = l.findIndex(v => v.dataIndex === dropKey)

  const max = Math.max(currentIndex, tagetIndex)
  const min = Math.min(currentIndex, tagetIndex)

  const currentItem = l[currentIndex]
  const tagetItem = l[tagetIndex]

  let firstArr = []
  let lastArr = []

  const centerArr = l.filter((v, i) => {
    return i >= min && i <= max
  })

  if (min !== 0) {
    firstArr = l.filter((v, i) => i < min)
  }
  if (max !== l.length - 1) {
    lastArr = l.filter((v, i) => i > max)
  }

  // 2 => 6
  if (currentIndex < tagetIndex) {
    if (dropPosition === 1) {
      centerArr.shift()
      centerArr.push(currentItem)
    }
    if (dropPosition === -1) {
      centerArr.shift()
      centerArr.pop()
      centerArr.push(currentItem)
      centerArr.push(tagetItem)
    }
  }

  if (currentIndex > tagetIndex) {
    if (dropPosition === -1) {
      centerArr.pop()
      centerArr.unshift(currentItem)
    }
    if (dropPosition === 1) {
      centerArr.pop()
      centerArr.shift()
      centerArr.unshift(currentItem)
      centerArr.unshift(tagetItem)
    }
  }

  const res = [...firstArr, ...centerArr, ...lastArr]

  return res
}

export const treeIntoList = function (arr) {
  return arr.length
    ? [].concat(
      ...arr.map(d => [].concat(d, ...treeIntoList(d?.children || [])))
    )
    : []
}

export function getHideColumns (columns) {
  const arr = treeIntoList(cloneDeep(columns))
  // const flatCol = col => {
  //   const res = []
  //
  //   col.forEach(item => {
  //     res.push(item)
  //     if (item.children && item.children.length) {
  //       res.push(...flatCol(item.children))
  //     }
  //   })
  //
  //   return res
  // }
  // const col = cloneDeep(columns).filter(v => {
  //   return v.hide
  // })
  return arr.filter(d => d.hide).map(v => v.dataIndex)
}

export function utilCreateCols (
  arr,
  checkboxVal,
  halfCheckboxVal
) {
  arr = cloneDeep(arr)

  if (!Array.isArray(arr) || !arr.length) {
    return []
  }
  if (!Array.isArray(checkboxVal)) {
    checkboxVal = []
  }

  arr = arr.filter(v => {
    const o = checkboxVal.some(i => {
      return (
        i === v.dataIndex ||
        halfCheckboxVal.includes(v.dataIndex) ||
        !v.dataIndex
      )
    })
    return !!o
  })

  const arrFilter = arr => {
    return arr.filter(v => {
      if (v.children && v.children.length) {
        v.children = arrFilter(v.children)
        return v.children.length > 0
      }

      const o = checkboxVal.some(i => {
        return (
          i === v.dataIndex ||
          halfCheckboxVal.includes(v.dataIndex) ||
          !v.dataIndex
        )
      })

      return !!o
    })
  }
  const l = arrFilter(arr)
  return l
}

// 初始化列设置
export function initCheckedList (columns, isAll = false, halfCheckboxVal) {
  const flatCol = (col, parent, parentRes) => {
    const res = []
    col.forEach(item => {
      if (!isAll && item.hide) {
        if (parent && parentRes && halfCheckboxVal) {
          const index = parentRes.findIndex(
            d => d.dataIndex === parent.dataIndex
          )
          if (index > -1) parentRes.splice(index, 1)
          const halfIndex = halfCheckboxVal.value.findIndex(
            d => d === parent.dataIndex
          )
          if (halfIndex === -1) {
            halfCheckboxVal.value.push(parent.dataIndex)
          }
        }
        return
      }
      res.push(item)
      if (item.children && item.children.length) {
        res.push(...flatCol(item.children, item, res))
      }
    })

    return res
  }
  const col = cloneDeep(columns).filter(v => {
    if (!isAll) {
      return !v.hide
    }
    return true
  })
  return flatCol(col).map(v => v.dataIndex)
}
