<script lang="jsx">
  import { defineComponent, computed } from 'vue'
  import { Popover } from 'ant-design-vue'
  import { compareItemsNames } from '../constants'

  export default defineComponent({
    props: {
      dataIndex: {
        type: String,
        default: ''
      },
      row: {
        type: Object,
        default: () => ({})
      }
    },
    setup (props) {
      const color = computed(() => {
        const { dataIndex, row } = props
        if (row[`${dataIndex}Scope`] === 0) {
          return 'green'
        } else if (row[`${dataIndex}Scope`] === 1) {
          return 'red'
        } else {
          return ''
        }
      })

      return {
        color
      }
    },
    render () {
      const { dataIndex, row, color } = this
      const compareItems = row[`${dataIndex}CompareItems`] || {}
      return <Popover>
    { this.$tnt.xtenant < 1000 ? <div slot="content" class={[Object.keys(compareItems)?.length ? 'popover-content' : '']}>
      <span>区域对比时间数据：<span style={{ color }}>{row[`${dataIndex}Previous`]}</span></span>
      { Object.keys(compareItems)?.length ? compareItemsNames.map(it => <span style={{ display: compareItems[it.key] === '--' ? 'none' : '' }}>{it.label}：{compareItems[it.key]}</span>) : null }
    </div> : <div slot="content">
      <span>对比时间数据：<span style={{ color }}>{row[`${dataIndex}Previous`]}{dataIndex === 'largeCount' ? <span class="ml-5">{row[`${dataIndex}Compare`]}</span> : null}</span></span>
    </div> }
    <span style={{ color }}>{this.$slots.default ? this.$slots.default : row[dataIndex]}</span>
  </Popover>
    }
  })
</script>
<style scoped lang="scss">
.popover-content {
  width: 350px;
  display: flex;
  flex-wrap: wrap;
  span {
    line-height: 28px;
    &:nth-child(2n) {
      width: 38%;
    }
    &:nth-child(2n - 1) {
      width: 62%;
    }
  }
}
</style>
