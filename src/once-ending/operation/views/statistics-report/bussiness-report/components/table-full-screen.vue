<script lang="jsx">
  import { screenfull } from '~/util/screenfull'
  import { computed, onMounted, onUnmounted, inject, defineComponent } from 'vue'
  import { Icon } from 'ant-design-vue'

  export default defineComponent({
    setup (props, context) {
      const { attrs } = context

      const name = attrs.idName

      const isFullscreen = inject('isFullscreen')
      const icon = computed(() => {
        return !isFullscreen.value ? 'fullscreen' : 'fullscreen-exit'
      })

      const openScreen = () => {
        if (screenfull.isEnabled) {
          // 表格 tableFullScreenId

          let element = document.getElementById(name)
          if (!element) {
            element = document.body
          }

          element = document.body
          screenfull.toggle(element)
        }
      }

      const change = () => {
        isFullscreen.value = screenfull.isFullscreen
      }

      const init = () => {
        screenfull.on('change', change)
      }

      onMounted(() => {
        init()
      })

      onUnmounted(() => {
        if (screenfull.enabled) {
          screenfull.off('change', change)
        }
      })

      return {
        openScreen,
        icon
      }
    },

    render () {
      const { openScreen, icon } = this

      return (
        <i>
          <Icon onClick={openScreen} type={icon} class="setting-icon" />
        </i>
      )
    }
  })

</script>
