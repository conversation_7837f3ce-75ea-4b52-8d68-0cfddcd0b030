<script lang="jsx">
  import { defineComponent, toRefs, ref, onMounted } from 'vue'
  import { useState } from '../hooks/use-state'
  import useSearch from '../hooks/use-search'
  import CitySelect from '~/components/city-select'
  import SelectCheckAll from '../components/selectCheckAll.vue'
  import { NiFilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import { DatePicker, TreeSelect, Select, Tooltip } from 'ant-design-vue'
  import CityStore from '@logistics/components/city-store'
  import { ranges } from '../constants'
  import moment from 'moment'

  export default defineComponent({
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect,
      CityStore,
      CitySelect,
      SelectCheckAll
    },
    setup () {
      const batchExportContent = ref(null)
      const { state, loading, fetchData, hasZscb, allChecked } = useState()
      const {
        set<PERSON>hain,
        productCategorySelect,
        selectOptions,
        handleSelect,
        selectSearch,
        selectPopupScroll,
        reset,
        selectFocusOrBlur,
        niFilter,
        batchExportAllChecked
      } = useSearch()

      const filter = ref(null)

      const addQuickSearchEvent = function () {
        if (filter.value) {
          filter.value.$el.querySelector('.quick-search')?.addEventListener('click', function (e) {
            if ((e.target.className || '').includes('quick-search-tag') && filter.value.isFold) {
              filter.value.toggleFold()
            }
          })
        }
      }

      const getPopupContainer = () => {
        return () => batchExportContent.value
      }

      onMounted(() => {
        addQuickSearchEvent()
      })

      return {
        batchExportContent,
        ...toRefs(state),
        allChecked,
        loading,
        fetchData,
        setChain,
        productCategorySelect,
        selectOptions,
        handleSelect,
        selectSearch,
        selectPopupScroll,
        hasZscb,
        reset,
        selectFocusOrBlur,
        niFilter,
        filter,
        batchExportAllChecked,
        getPopupContainer
      }
    },
    render () {
      const {
        loading,
        fetchData,
        formData,
        options: {
          storeAttribute,
          orderStatus,
          orderType,
          virtualGoods,
          costType,
          storeLevel,
          storeType,
          storeCategory,
          productCategory,
          largePrice,
          recycledWool,
          eliminateFreebie
        },
        setChain,
        productCategorySelect,
        selectOptions,
        handleSelect,
        selectSearch,
        selectPopupScroll,
        reset,
        hasZscb,
        selectFocusOrBlur,
        allChecked,
        batchExportAllChecked,
        getPopupContainer
      } = this
      return (
        <div ref="niFilter">
      <NiFilter
        ref="filter"
        form={formData}
        loading={loading}
        onFilter={() => {
          fetchData()
        }}
        onReset={reset}
        do-filter-when-reset={false}
        label-width={100}
        immediate={false}
      >
        <ni-filter-item label="地区">
          <NiAreaSelect
            v-model={formData.areaIds}
            allow-clear={true}
            multiple
            mode={2}
            show-search
            ranks={['2c5']}
            placeholder="请选择地区"
            class="area-selector"
            max-tag-count={1}
            showCheckedStrategy="TreeSelect.SHOW_ALL"
          />
        </ni-filter-item>
        <ni-filter-item label="交易时间">
          <DatePicker.RangePicker
            v-model={formData.times}
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            show-time
            onChange={() => {
              setChain()
            }}
            ranges={ranges}
          ></DatePicker.RangePicker>
        </ni-filter-item>
        <ni-filter-item label="门店属性">
          <TreeSelect
            show-arrow
            show-search
            tree-node-filter-prop="title"
            tree-checkable
            allow-clear
            dropdown-style={{ maxHeight: '300px' }}
            placeholder="请选择门店属性"
            get-popup-container={(triggerNode) => triggerNode.parentNode}
            max-tag-count={1}
            tree-data={storeAttribute || []}
            value={formData.storeAttribute}
            onChange={(val) => {
              formData.storeAttribute = val
            }}
          />
        </ni-filter-item>
        <ni-filter-item label="业务类型">
          <Tooltip>
            <template slot="title">支持查询新机、优品、转售、回收的退款订单数据</template>
            <Select
              v-model={formData.orderStatus}
              placeholder="请选择业务类型"
              allowClear
              options={orderStatus || []}
            />
          </Tooltip>
        </ni-filter-item>
        <ni-filter-item label="订单类型">
          {/*
          <Select
            v-model={formData.orderType}
            allow-clear={true}
            mode="multiple"
            max-tag-count={1}
            placeholder="请选择订单类型"
            options={orderType || []}
          ></Select>
          */}
          <SelectCheckAll style="width: 100% !important" v-model={formData.orderType} maxTagCount={1} options={orderType || []}/>
        </ni-filter-item>
        <ni-filter-item label="对比时间">
          <DatePicker.RangePicker
            v-model={formData.chainTimes}
            show-time
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </ni-filter-item>
        <ni-filter-item label="包含虚拟商品">
          <Select
            v-model={formData.virtualGoods}
            placeholder="请选择是否包含虚拟商品"
            options={virtualGoods || []}
          />
        </ni-filter-item>
        {costType.length && hasZscb ? (
          <ni-filter-item label="成本类型">
            <Select
              v-model={formData.costType}
              placeholder="请选择成本类型"
              options={costType || []}
            />
          </ni-filter-item>
        ) : null}

        <ni-filter-item label="行政区域">
          <CitySelect
            city-id-is-all={true}
            change-on-select={false}
            city-id={formData.city}
            onChange={(val) => {
              formData.city = val || []
            }}
          ></CitySelect>
        </ni-filter-item>
        <ni-filter-item label="区域门店">
          <CityStore
            ref="web"
            authority={true}
            use-rank={['2c5']}
            allow-clear
            value={formData.areaStoreIds}
            placeholder="请选择区域门店"
            onChange={(firstLevel, lastLevel, idList) => {
              formData.areaStoreIds = idList || []
            }}
          />
        </ni-filter-item>
        <ni-filter-item label="门店级别">
          <Select
            v-model={formData.storeLevel}
            placeholder="请选择门店级别"
            options={storeLevel || []}
            allow-clear={true}
          />
        </ni-filter-item>
        <ni-filter-item label="门店类别">
          <Select
            v-model={formData.storeType}
            placeholder="请选择门店类别"
            options={storeType || []}
            mode="multiple"
            max-tag-count={1}
            allow-clear={true}
          />
        </ni-filter-item>

        <ni-filter-item label="门店类型">
          <Select
            v-model={formData.storeCategory}
            placeholder="请选择门店类型"
            options={storeCategory || []}
            mode="multiple"
            max-tag-count={1}
            allow-clear={true}
          />
        </ni-filter-item>
        <ni-filter-item label="商品分类">
          <TreeSelect
            show-arrow
            show-search
            tree-node-filter-prop="title"
            tree-checkable
            class={{
              'more-select':
                formData.productCategory &&
                formData.productCategory.length &&
                formData.productCategory.length > 1,
            }}
            allow-clear
            dropdown-style={{ maxHeight: '300px' }}
            placeholder="请选择商品分类"
            get-popup-container={(triggerNode) => triggerNode.parentNode}
            max-tag-count={1}
            replaceFields={{ children: 'children', title: 'title', key: 'value', value: 'value' }}
            tree-data={productCategory}
            value={formData.productCategory}
            onChange={(val) => {
              productCategorySelect(val)
            }}
          />
        </ni-filter-item>
        <ni-filter-item label="商品品牌">
          <div ref="batchExportContent" class="batchExport-content">
          <Select
            placeholder="请选择商品品牌"
            get-popup-container={(triggerNode) => triggerNode.parentNode}
            show-arrow
            max-tag-count={0}
            mode="multiple"
            optionLabelProp="label"
            v-model={formData.productBrand}
            onFocus={selectFocusOrBlur}
            onBlur={selectFocusOrBlur}
            onSelect={handleSelect}
            onSearch={selectSearch}
            onPopupScroll={selectPopupScroll}
            allow-clear
            style={{ width: '100% !important' }}
            filterOption={false}
            getPopupContainer={getPopupContainer()}
            scopedSlots={{
              dropdownRender: (menu) => {
                return <div>
                  <div style={{ marginLeft: '12px' }}>
                    <a-checkbox v-model={ allChecked } onChange={(e) => batchExportAllChecked(e)}>
                      <span style={{ marginLeft: '-4px' }}>全选</span>
                    </a-checkbox>
                  </div>
                  <a-divider style={{ margin: '4px 0' }} />
                  {menu}
                </div>
              }
            }}
          >
            {
              selectOptions && selectOptions.length > 0 && selectOptions.map((item, index) => {
                return <a-select-option value={item.value} label={item.label} key={index}>
                  <label class="ant-checkbox-wrapper">
                    <span class={`ant-checkbox ${formData.productBrand.includes(item.value) ? 'ant-checkbox-checked' : ''}`} style={{ marginRight: '2px' }}>
                    <input type="checkbox" class="ant-checkbox-input" value=""/>
                    <span class="ant-checkbox-inner"></span>
                  </span>
                </label>
                {item.label}
              </a-select-option>
              })
            }
          </Select>
          </div>
        </ni-filter-item>
        {
          this.$tnt.xtenant >= 1000 ? <ni-filter-item label="剔除赠品">
            <a-select v-model={formData.eliminateFreebie} options={eliminateFreebie} allow-clear></a-select>
          </ni-filter-item> : null
        }
        {
          this.$tnt.xtenant === 0 ? <ni-filter-item label="大件价位">
            <Select
              v-model={formData.largePrice}
              placeholder="请选择大件价位"
              options={largePrice || []}
            allow-clear
          />
        </ni-filter-item> : null
        }
        {
          this.$tnt.xtenant >= 1000 ? <ni-filter-item label="回收单毛">
          <Select
            v-model={formData.recycledWool}
            placeholder="请选择回收单毛"
            options={recycledWool || []}
          />
        </ni-filter-item> : null
        }
      </NiFilter>
      </div>
      )
    },
  })
</script>
<style lang="scss" scoped>
.more-select {
  :deep(.ant-select-selection__choice) {
    max-width: 45%;
  }
}
.batchExport-content {
  :deep(.ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-selected .ant-select-selected-icon, .ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-selected:hover .ant-select-selected-icon) {
    display: none;
  }
  :deep(.ant-select-dropdown-menu-item) {
    i {
      display: none;
    }
  }
}
</style>
