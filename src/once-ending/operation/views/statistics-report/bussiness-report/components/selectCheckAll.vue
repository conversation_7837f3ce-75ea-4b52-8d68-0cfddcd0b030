<template>
  <div ref="batchExportContent" class="batchExport-content">
    <a-select
      v-model="selectValue"
      v-bind="$attrs"
      v-on="$listeners"
      class="full-width"
      mode="multiple"
      :maxTagCount="maxTagCount"
      allowClear
      :placeholder="placeholder"
      show-search
      optionLabelProp="label"
      :filter-option="filterOption"
      :getPopupContainer="getPopupContainer()"
      @change="batchExportAllHandle"
      style="width: 100% !important;"
    >
      <div slot="dropdownRender" slot-scope="menu">
        <div style="padding: 4px 8px;margin-left: 4px">
          <a-checkbox v-model="allChecked" @change="batchExportAllChecked">全选</a-checkbox>
        </div>
        <a-divider style="margin: 4px 0;" />
        <v-nodes :vnodes="menu" />
      </div>
      <a-select-option :value="item.value" :label="item.label" v-for="(item, index) in options" :key="index">
        <label class="ant-checkbox-wrapper">
          <span class="ant-checkbox" :class="selectValue.includes(item.value) ? 'ant-checkbox-checked':''">
            <input type="checkbox" class="ant-checkbox-input" value="">
            <span class="ant-checkbox-inner"></span>
          </span>
        </label>
        {{ item.label }}
      </a-select-option>
    </a-select>
  </div>
</template>
<script>
  import { defineComponent, ref, watch, h } from 'vue'
  export default defineComponent({
    components: {
      VNodes: {
        functional: true,
        render: (h, ctx) => ctx.props.vnodes
      },
    },
  })
</script>
<script setup>
  const props = defineProps({
    options: {
      type: Array,
      default: () => []
    },
    value: {
      type: Array,
      default: () => []
    },
    maxTagCount: {
      type: Number,
      default: 1
    },
    placeholder: {
      type: String,
      default: '请选择'
    }
  })

  const emit = defineEmits(['input'])

  const batchExportContent = ref(null)
  const selectValue = ref(props.value)
  const allChecked = ref(props.value.length === props.options.length)

  const filterOption = (input, option) => {
    return (
      option.componentOptions.children[1].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    )
  }

  const batchExportAllChecked = (value) => {
    allChecked.value = value.target.checked
    if (allChecked.value) {
      selectValue.value = props.options.map(item => item.value)
    } else {
      selectValue.value = []
    }
    emit('input', selectValue.value)
  }

  const getPopupContainer = () => {
    return () => batchExportContent.value
  }

  watch(() => props.value, (newVal) => {
    selectValue.value = newVal
  })

  watch(() => selectValue.value, (newVal) => {
    if (newVal.length === props.options.length) {
      allChecked.value = true
    } else {
      allChecked.value = false
    }
  })

  const batchExportAllHandle = () => {
    emit('input', selectValue.value)
  }
</script>

<style lang="scss" scoped>
.batchExport-content {
  :deep(.ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-selected .ant-select-selected-icon, .ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-selected:hover .ant-select-selected-icon) {
    display: none;
  }
  :deep(.ant-select-dropdown-menu-item) {
    i {
      display: none;
    }
  }
}
</style>
