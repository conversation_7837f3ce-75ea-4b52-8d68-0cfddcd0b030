<script lang="jsx">
  import { ref, inject, watch, computed, defineComponent } from 'vue'
  import {
    getColumns,
    getHideColumns,
    treeIntoList
  } from './util/columns'
  import { Tooltip, Popover, Checkbox, Button, Tree, Icon } from 'ant-design-vue'

  function changeModify (arr, obj, hides, halfCheckboxVal) {
    const l = arr.filter(v => {
      if (halfCheckboxVal.value.includes(v)) return false
      const o = obj[v]
      if (o) {
        return !o.hide
      }

      if (hides.includes(v)) {
        return false
      }
      return true
    })
    return l
  }

  export default defineComponent({
    setup (props, context) {
      const checkIndex = inject('checkIndex')
      const checkAll = ref(false)

      const val = ref([])
      const checkboxVal = inject('checkboxVal')
      const initCheckboxVal = inject('initCheckboxVal')
      const allCheckboxVal = inject('allCheckboxVal')
      const halfCheckboxVal = inject('halfCheckboxVal')
      const columns = inject('columns')
      const bussinessReportConfigTable = inject('bussinessReportConfigTable')
      const getBussinessReportConfigTable = inject('getBussinessReportConfigTable')
      const saveBussinessReportConfigTable = inject('saveBussinessReportConfigTable')

      const isCheckALL = ref(false)

      const tableColumnsVal = computed(() => {
        const tableColumns = bussinessReportConfigTable.value

        if (tableColumns && Object.keys(tableColumns).length) {
          const hides = getHideColumns(columns.value)
          const r = changeModify(allCheckboxVal.value, tableColumns, hides, halfCheckboxVal)

          return r
        }
        return isCheckALL.value ? allCheckboxVal.value : initCheckboxVal.value
      })

      watch(
        tableColumnsVal,
        v => {
          if (Array.isArray(v)) {
            val.value = v
          }
        },
        {
          immediate: true,
          deep: true
        }
      )

      let tableColumns = {}

      const handlerReset = list => {
        isCheckALL.value = false
        saveBussinessReportConfigTable({})
      }

      const onCheckIndexaAtive = e => {
        const checked = e.target.checked
        checkIndex.value = checked
        getBussinessReportConfigTable()
      }

      const onCheckAll = list => {
        isCheckALL.value = true
        checkAll.value = true
        halfCheckboxVal.value = []
        val.value = allCheckboxVal.value

        for (const key in tableColumns) {
          const element = tableColumns[key]
          element.hide = false
        }
        const hides = getHideColumns(columns.value)
        hides.forEach(el => {
          const o = tableColumns[el]
          if (!o) {
            tableColumns[el] = {}
            tableColumns[el].hide = false
          }
        })

        saveBussinessReportConfigTable(tableColumns)
      }

      // 选则节点事件没有节点联动但是check勾选节点事件有节点联动，可手动触发tree里面的check勾选事件，触发节点节点联动
      const handlerSelect = (arr, e) => {
        const {
          nativeEvent,
          node: { onCheck }
        } = e
        onCheck(nativeEvent)
      }

      const handlerCheck = (arr, e, list) => {
        const treeData = JSON.parse(JSON.stringify(columns.value))
        const treeDataList = treeIntoList(treeData)
        const { halfCheckedKeys } = e
        const hideList = treeDataList.filter(d => !arr.includes(d.dataIndex))
        hideList.forEach(d => {
          const { dataIndex } = d
          tableColumns[dataIndex] = tableColumns[dataIndex] || {}
          tableColumns[dataIndex].hide = true
          if (halfCheckedKeys.includes(dataIndex)) {
            tableColumns[dataIndex].half = true
          } else {
            tableColumns[dataIndex].half = false
          }
        })
        arr.forEach(d => {
          tableColumns[d] = tableColumns[d] || {}
          tableColumns[d].hide = false
          tableColumns[d].half = false
        })
        saveBussinessReportConfigTable(tableColumns)
      }

      const handlerTableColumnsFixed = (key, fixed) => {
        tableColumns[key] = tableColumns[key] || {}
        tableColumns[key].fixed = fixed

        saveBussinessReportConfigTable(tableColumns)
      }

      watch(
        () => bussinessReportConfigTable.value,
        v => {
          tableColumns = v
        },
        {
          immediate: true,
          deep: true
        }
      )

      const handlerFixed = (e, v, type) => {
        e.stopPropagation()
        const fixed = v.fixed ? '' : type
        handlerSetColumns(v, fixed)
      }

      const handlerStopPropagation = e => {
        e.stopPropagation()
      }

      const handlerSetColumns = (v, type) => {
        const key = v.dataIndex
        handlerTableColumnsFixed(key, type)
      }

      watch(
        val,
        value => {
          checkAll.value = value.length === allCheckboxVal.value.length
          checkboxVal.value = value
        },
        {
          immediate: true,
          deep: true
        }
      )

      return {
        handlerCheck,
        handlerStopPropagation,
        handlerFixed,
        handlerSelect,
        columns,
        val,
        checkIndex,
        checkAll,
        handlerReset,
        onCheckIndexaAtive,
        onCheckAll,
        tableColumnsVal
      }
    },

    render () {
      const {
        handlerStopPropagation,
        handlerFixed,
        columns,
        checkIndex,
        onCheckIndexaAtive,
        checkAll,
        onCheckAll,
        handlerReset,
        handlerCheck,
        handlerSelect
      } = this
      const list = getColumns(columns)

      const renderContentTop = function () {
        return (
        <div class="content_top border-bottom">
          <Checkbox checked={checkIndex} onChange={onCheckIndexaAtive}>
            序号
          </Checkbox>
          <Button
            type="link"
            size="small"
            disabled={checkAll}
            onClick={() => {
              onCheckAll(list)
            }}
          >
            全选
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => {
              handlerReset(list)
            }}
          >
            重置
          </Button>
        </div>
      )
      }

      return (
        <Popover
          placement="bottomRight"
          getPopupContainer={triggerNode => triggerNode.parentNode}
          arrowPointAtCenter={true}
        >
          <div slot="content">
            {renderContentTop()}

            <div class="checkList">
              <Tree
                v-model={this.val}
                onCheck={(arr, e) => {
                  handlerCheck(arr, e, list)
                }}
                onSelect={handlerSelect}
                blockNode={true}
                multiple
                draggable
                checkable
                replaceFields={{
                  children: 'children',
                  key: 'dataIndex',
                  title: 'label'
                }}
                tree-data={list}
              />
            </div>
          </div>
          <Icon class="setting-icon" type="setting" />
        </Popover>
      )
    }
  })

</script>
<style lang="scss" scoped>
  .treeTitleContainer {
    padding: 0 0 25px 0;
    cursor: move;
  }

  .treeTitleBox {
    display: flex;
    transition: all 0.2s;
    cursor: pointer;

    &:hover {
      background: rgba(0, 0, 0, 0.12);
    }

    .treeTitle {
      flex: 1;

      i {
        font-size: 12px;
        color: red;
      }
    }

    .treeTitleAction {
      min-width: 55px;
      display: inline-flex;
      padding-left: 3px;
      align-items: center;
      justify-content: space-between;

      .treeFixedBtn {
        color: #777;
        //  background: red;
        padding: 2px 5px;

        &:hover {
          box-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
        }
      }
    }

    .treeDrag {
      padding: 1px 5px;
      margin-left: 5px;
      background: rgba(0, 0, 0, 0.2);
      display: none;
    }
  }

.ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background: none !important;
  }

  .ant-tree .treeTitle {
    max-width: 130px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
</style>
