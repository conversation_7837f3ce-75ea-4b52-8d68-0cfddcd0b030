import { inject, provide } from 'vue'
import statisticsReport from '@operation/api/statistics-report'
import { to } from '@common/utils/common'
import { message } from 'ant-design-vue'
const key = Symbol('useApi')

async function commApi (type, params = {}, url = '') {
  const [err, res] = await to(statisticsReport[type](params, url))
  if (err) throw err
  const { code, userMsg } = res
  if (code !== 0 && type !== 'bussinessReportExport') {
    message.error(userMsg)
    return
  }
  return res
}

export const useApi = function () {
  return inject(key)
}

export const createApi = function () {
  const apiKey = ['raceOptions', 'editNote', 'raceHeader', 'raceGetFactor', 'raceData', 'raceEditFactor', 'bussinessReportExport']
  const api = {}

  apiKey.forEach((item) => {
    api[item] = async (params, headers = {}) => {
      const res = await commApi(item, params, item === 'bussinessReportExport' ? '/api/report/race/data/export' : '', headers)
      return res
    }
  })

  provide(key, api)
  return api
}
