import { useState } from './use-state'
import { useApi } from './use-api'
import moment from 'moment'
import { ref, watch, nextTick, onMounted } from 'vue'
import { defaultFormData } from '../constants'
import cloneDeep from 'lodash/cloneDeep'

export default function useSearch () {
  const { state, allChecked } = useState()
  const { raceOptions } = useApi()

  const setChain = function (type) {
    const times = type ? defaultFormData.times : state.formData.times
    if (!times.length) return
    // 选择未来事件，时间选择不正确
    if (new Date().getTime() < new Date(times[0]).getTime()) { return }
    const isFuture =
      new Date().getTime() < new Date(times[1]).getTime()
    const diff = isFuture
      ? new Date().getTime() - new Date(times[0]).getTime()
      : new Date(times[1]).getTime() -
        new Date(times[0]).getTime()
    const chainTimes = [
      moment(new Date(times[0]))
        .subtract(diff + 1, 'milliseconds')
        .format('YYYY-MM-DD HH:mm:ss'),
      moment(new Date(times[0]))
        .subtract(1, 'milliseconds')
        .format('YYYY-MM-DD HH:mm:ss'),
    ]
    if (type) {
      defaultFormData.chainTimes = chainTimes
    } else {
      state.formData.chainTimes = chainTimes
    }
  }

  // 设置对比时间默认值
  setChain('default')

  const productCategorySelect = function (val) {
    state.formData.productCategory = val
    if (val.length) state.formData.productBrand = []
  }

  // 显示的options
  const selectOptions = ref([])

  // 缓存的options
  const cacheOptions = ref([])
  const selectSearchValue = ref('')
  // 过滤以后的options
  const filterOptions = ref([])

  // 商品分类和品牌联动
  watch(() => state.formData.productCategory, val => {
    if (!val) return
    const allOptions = cloneDeep(state.options.productBrand)
    filterOptions.value = !val.length ? allOptions : allOptions.filter(d => {
      const s = new Set(val)
      return [...new Set(d.category || [])].filter(x => s.has(`${x}`)).length
    })
    selectFocusOrBlur()
  }, { deep: true })

  watch(() => state.formData.productBrand, (newVal, oldVal) => {
    allChecked.value = newVal.length === filterOptions.value.length
  })

  // 每次下拉框获取焦点或者失去焦点,初始化下拉框数据
  const selectFocusOrBlur = function () {
    const options = cloneDeep(filterOptions.value)
    const initOptions = options.splice(0, 50)
    if (state.formData.productBrand.length) {
      state.formData.productBrand.forEach(item => {
        const index = options.findIndex(d => d.value === item)
        if (index !== -1) {
          initOptions.unshift(options.splice(index, 1)[0])
        }
      })
    }
    selectOptions.value = initOptions.splice(0, 50)
    // cacheOptions.value = options
    cacheOptions.value = cloneDeep(filterOptions.value)
  }

  // 每次用户输入,匹配所有数据,将数据筛选出来
  const selectSearch = function (val) {
    if (!val) {
      selectFocusOrBlur()
      return
    }
    selectSearchValue.value = val
    const options = cloneDeep(filterOptions.value)
    selectOptions.value = options.filter(d => d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase()))
    cacheOptions.value = options.filter(d => !d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase()))
  }

  const handleSelect = function () {
    selectFocusOrBlur()
    selectSearchValue.value = ''
  }

  // 每次下拉框滚动条滚到底部,加载缓存数据
  const selectPopupScroll = function (e) {
    if (!cacheOptions.value.length) {
      return
    }
    const { target } = e
    const scrollHeight = target.scrollHeight - target.scrollTop
    const clientHeight = target.clientHeight
    if (scrollHeight < clientHeight + 5) {
      const options = cacheOptions.value.splice(0, 50)
      selectOptions.value = uniqueData(selectOptions.value.concat(options))
    }
  }

  const uniqueData = (data) => {
    return data.filter((item, index, self) =>
      index === self.findIndex(t => t.label === item.label && t.value === item.value)
    )
  }

  // 获取筛选项,赋默认值(先赋原始默认值,再赋本地保存的默认值)
  const getRaceOptions = async function () {
    const res = await raceOptions()
    if (res) {
      const { data } = res
      data.forEach(d => {
        const { key, list, tree } = d
        const isArr = Array.isArray(defaultFormData[key])
        state.options[key] = list.length ? list.map(k => {
          const { name: label, value, selected, ...other } = k
          if (selected) {
            if (isArr) {
              defaultFormData[key].push(value)
            } else {
              defaultFormData[key] = value
            }
          }
          return {
            label,
            value,
            ...other
          }
        }) : tree
      })
      // 赋原始默认值
      state.formData = cloneDeep(defaultFormData)
      // 赋保存默认值
      setLocalFormData()
      // 手动触发商品品牌获取焦点事件,解决默认值反显问题
      nextTick(() => {
        selectFocusOrBlur()
      })
    }
  }

  watch(() => state.formData, val => {
    const { times, chainTimes, areaIds, ...other } = state.formData
    localStorage.setItem('bussinessReportFormData', JSON.stringify(other))
  }, { deep: true })

  const setLocalFormData = function () {
    const bussinessReportFormData = localStorage.getItem('bussinessReportFormData')
    if (!bussinessReportFormData) return
    const o = JSON.parse(bussinessReportFormData)
    // 5月报表盘点优化方案：优化后没有全部:0的选项了
    o.orderStatus = o.orderStatus === 0 ? undefined : o.orderStatus
    delete o.eliminateFreebie
    Object.assign(state.formData, o)
  }

  getRaceOptions()

  const reset = function () {
    state.formData = cloneDeep(defaultFormData)
    setChain('default')
  }

  const niFilter = ref(null)

  const ro = new ResizeObserver((entries, observer) => {
    for (const entry of entries) {
      const { height } = entry.contentRect
      state.flterBoxHeight = height
    }
  })

  const batchExportAllChecked = (e) => {
    allChecked.value = e.target.checked
    if (allChecked.value) {
      state.formData.productBrand = filterOptions.value.map(item => item.value)
    } else {
      state.formData.productBrand = []
    }
  }

  onMounted(() => {
    ro.observe(niFilter.value)
  })

  return {
    setChain,
    productCategorySelect,
    selectOptions,
    selectFocusOrBlur,
    selectSearch,
    handleSelect,
    selectPopupScroll,
    reset,
    niFilter,
    batchExportAllChecked
  }
}
