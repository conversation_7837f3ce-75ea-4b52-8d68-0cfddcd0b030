import { inject, provide, reactive, computed, getCurrentInstance, ref } from 'vue'
import useCommon from '../../hooks/useCommon'
import useLastUpdateDate from '../../hooks/useLastUpdateDate'
import { defaultFormData } from '../constants'
import moment from 'moment'
import cloneDeep from 'lodash/cloneDeep'
import { message } from 'ant-design-vue'
const key = Symbol('useState')

export const useState = function () {
  return inject(key)
}

export const createState = function (api) {
  const { raceData } = api
  const { proxy } = getCurrentInstance()

  const { $store, $tnt } = proxy.$root

  const { loading, isFeatch } = useCommon()

  const { lastUpdateDate, setLastUpdateDate } = useLastUpdateDate()

  const state = reactive({
    dataSource: [],
    summary: {},
    sorter: {},
    items: [],
    formData: cloneDeep(defaultFormData),
    options: {
      storeAttribute: [],
      orderStatus: [],
      orderType: [],
      virtualGoods: [],
      costType: [],
      storeLevel: [],
      storeType: [],
      storeCategory: [],
      productCategory: [],
      productBrand: [],
      regionOnly: [],
      largePrice: [],
      recycledWool: [],
      eliminateFreebie: []
    },
    flterBoxHeight: 0,
  })
  const allChecked = ref(false)
  const ranks = computed(() => {
    return $store.state.userInfo.Rank || []
  })

  const has2c5 = computed(() => {
    return ranks.value.includes('2c5')
  })

  const hasXszm = computed(() => {
    return ranks.value.includes('xszm')
  })

  const hasBbxs = computed(() => {
    return ranks.value.includes('bbxs')
  })

  const has2c7 = computed(() => {
    return ranks.value.includes('2c7')
  })

  const hasZscb = computed(() => {
    return ranks.value.includes('zscb')
  })

  const has9o9 = computed(() => {
    return ranks.value.includes('9o9')
  })

  const hasBbzs = computed(() => {
    return ranks.value.includes('bbzs')
  })

  const getBaseParams = function () {
    const { formData: { times, city, chainTimes, areaIds, ...other } } = state
    const startTime = times.length ? times[0] : undefined
    const endTime = times.length ? times[1] : undefined
    return {
      ...other,
      startTime,
      endTime,
      provinceId: city[0],
      cityId: city[1],
      districtId: city[2],
      areaIds: areaIds?.filter(it => !isNaN(it)) || [],
      areaAlls: areaIds || []
    }
  }

  const getQueryParams = function () {
    const { items, formData: { chainTimes } } = state
    const comparisonStartTime = chainTimes.length ? chainTimes[0] : undefined
    const comparisonEndTime = chainTimes.length ? chainTimes[1] : undefined
    const params = {
      items,
      ...getBaseParams(),
      comparisonStartTime,
      comparisonEndTime
    }
    if ($tnt.xtenant >= 1000) {
      delete params.gift
    } else {
      delete params.eliminateFreebie
    }
    return params
  }

  const checkForm = function () {
    const { formData: { times }, items } = state
    // 时间段不能为空
    if (!times.length) {
      // message.warning(has9o9.value ? '请选择时间段' : `请选择时间段,且时间间隔不能超过${$tnt.xtenant < 1000 ? '四' : '三'}个月`)
      message.warning('请选择交易时间段')
      return true
    }
    if (times.length && times[0] > times[1]) {
      message.warn('交易开始时间不能大于结束时间')
      return true
    }
    // if (
    //   !has9o9.value &&
    //   moment(times[0]).add($tnt.xtenant < 1000 ? 4 : 3, 'months').format('YYYY-MM-DD HH:mm:ss') < times[1]
    // ) {
    //   message.warn(`时间间隔不能超过${$tnt.xtenant < 1000 ? '四' : '三'}个月`)
    //   return true
    // }
    if (!items.length) {
      message.warn('请至少选择一个统计项')
      return true
    }
    return false
  }

  const getItem = function (item) {
    Object.keys(item).map(d => {
      const subItem = item[d]
      if (Object.prototype.toString.call(subItem) === '[object Object]') {
        delete item[d]
        const { current, previous, scope, compare, compareItems } = subItem
        const o = {
          [d]: current,
          [`${d}Previous`]: previous,
          [`${d}Scope`]: scope,
          [`${d}Compare`]: compare,
          [`${d}CompareItems`]: compareItems || {},
        }
        item = { ...item, ...o }
      }
    })
    return item
  }

  const canvasTable = ref(null)

  // table组件的方法,重写
  const drawSummary = function (fixed) {
    if (!this.summary) return
    const list = this.bodyColumnsByFixed[fixed]
    const row = this.summary
    for (let i = 0; i < list.length; i++) {
      const col = list[i]
      let width = col.width
      if (col.summarySpan > 1) {
        const endSpan = col.summarySpan - 1 + i
        while (i < endSpan && i < list.length) {
          i++
          width += list[i].width
        }
      }
      let _left = 0
      if (col.fixed === 'left') {
        _left = col._left
      } else if (col.fixed === 'right') {
        _left = col._left - this.maxScrollWidth
      } else {
        _left = col._left - this.scrollX
        if (_left + width < this.fixedLeftWidth) continue
        if (_left > this.wrapperWidth - this.fixedRightWidth) return
      }
      const _top = this.headerHeight + this.bodyHeight
      // 画背景边框
      this.drawCellRect(_left, _top, {
        width,
        height: this.summaryHeight,
        background: this.style.summaryBackground,
        [this.border ? 'border' : 'borderTop']: [1, this.style.borderColor]
      })
      if (col.summarySlot) continue

      if (!this.summaryCellInfo[col.key]) {
        this.summaryCellInfo[col.key] = this.getCellInfo(
          this.ctx,
          width - this.style.padding * 2, this.summaryHeight,
          {},
          {
            ...col,
            type: null,
            formatter: col.summaryFormatter || col.formatter
          },
          {
            color: this.style.color,
            fontSize: this.style.fontSize,
            fontFamily: 'sans-serif',
          },
          col.summaryAlign || col.align || 'left',
          0
        )
      }
      this.summaryCellInfo[col.key].forEach(item => this.drawCellText({
        ...item,
        x: item.x + _left + this.style.padding,
        y: item.y + _top,
        text: item._text,
        textWidth: item._textWidth || 0
      }))
    }
  }

  const fetchData = async function () {
    if (checkForm()) return
    const params = {
      ...getQueryParams(),
      ...state.sorter
    }
    loading.value = true
    const res = await raceData(params)
    loading.value = false
    if (res) {
      isFeatch.value = true
      const { data: { data, total } } = res
      if (total && Object.keys(total).length) {
        state.summary = getItem(total)
      } else {
        state.summary = {}
      }
      let cacheAreaIds = []
      state.dataSource = data.map(item => {
        cacheAreaIds = cacheAreaIds.concat(item.areaIds || [])
        return getItem(item)
      })
      localStorage.setItem('bussinessReportAreaIdsScoped', JSON.stringify(cacheAreaIds))
      canvasTable.value.drawSummary = drawSummary
    }
  }

  provide(key, {
    state,
    allChecked,
    lastUpdateDate,
    setLastUpdateDate,
    loading,
    isFeatch,
    fetchData,
    has2c5,
    hasXszm,
    hasBbxs,
    has2c7,
    hasZscb,
    has9o9,
    hasBbzs,
    getBaseParams,
    getQueryParams,
    checkForm,
    canvasTable
  })
  return {
    state,
    allChecked,
    lastUpdateDate,
    setLastUpdateDate,
    loading,
    isFeatch,
    fetchData,
    has2c5,
    hasXszm,
    hasBbxs,
    has2c7,
    hasZscb,
    has9o9,
    hasBbzs,
    getBaseParams,
    getQueryParams,
    checkForm,
    canvasTable
  }
}
