import { inject, provide } from 'vue'
import statisticsReport from '@operation/api/statistics-report'
import { to } from '@common/utils/common'
import { message } from 'ant-design-vue'
const key = Symbol('useApi')

async function commApi (type, params = {}, url = '') {
  const [err, res] = await to(statisticsReport[type](params, url))
  if (err) throw err
  const { code, userMsg } = res
  if (code !== 0 && type !== 'bussinessReportExport') {
    message.error(userMsg)
    return
  }
  return res
}

export const useApi = function () {
  return inject(key)
}

export const createApi = function () {
  const apiKey = ['bussinessReportExport', 'raceDetail', 'salesStaffDetail']
  //  1赛马业绩统计明细导出 2销售人员业绩统计明细导出
  const bussinessReportExportUrlMap = new Map([[1, '/api/report/race/detail/export'], [2, '/api/report/sales/staff/detail/export']])
  const api = {}

  apiKey.forEach((item) => {
    api[item] = async (params, exportKey = undefined) => {
      const res = await commApi(item, params, bussinessReportExportUrlMap.get(exportKey))
      return res
    }
  })

  provide(key, api)
  return api
}
