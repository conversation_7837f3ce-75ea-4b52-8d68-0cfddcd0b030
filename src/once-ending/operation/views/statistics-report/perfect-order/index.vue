<script lang="jsx">
  import { defineComponent, ref, getCurrentInstance } from 'vue'
  import { NiListPage, NiTable, NiImg } from '@jiuji/nine-ui'
  import { message } from 'ant-design-vue'
  import { orderUrlMap, typesMap } from './constants'
  import { createApi } from './hooks/use-api'
  import {
    SALE_DETAIL
  } from '@operation/store/modules/statistics-report/action-types'
  export default defineComponent({
    components: {
      NiListPage,
      NiTable,
      NiImg
    },
    data () {
      return {
        noData: () => (
        <div>
          <NiImg
            class="no-data-img"
            src="https://img2.ch999img.com/newstatic/14450/04349c93014c93f0.png"
          />
          {!this.isFeatch && <p class="no-query">请点击查询按钮查看数据</p>}
          {this.isFeatch && <p class="no-data-text">抱歉，没有查询到数据</p>}
        </div>
      ),
        customRender: (path, it, getLink) => (text, record) => !getLink || getLink(text) ? <a rel="noopener noreferrer" href={`${path}${record[it.key]}`} target="_blank">{ record[it.key] }</a> : <span>{ record[it.key] }</span>
      }
    },
    computed: {
      columns () {
        const fieldType = this.isNew ? this.$route.query.item : this.$route.query.fieldType
        return this.headerData.map(it => {
          const cache = {
            title: it.title || it.name,
            dataIndex: it.key,
            align: 'center'
          }
          if (it.key === 'orderId' && this.title === '销售人员业绩明细') { // 销售人员业绩明细只跳新机单
            cache.customRender = this.customRender(orderUrlMap.get(1), it)
          } else {
            // 因除赛马以外还有其他报表也跳转该页面,下面代码不敢动,故特殊适配新版赛马赛马
            if (this.isNew) {
              if (it.key === 'subIdo') {
                cache.customRender = this.customRender(orderUrlMap.get(1), it, function (val) { return val !== '--' })
              } else if (it.key === 'orderId' || it.key === 'goodProductsOrderId') {
                let path = ''
                if (it.key === 'orderId') { // 新版赛马统一在订单号这里跳转
                  path = orderUrlMap.get(typesMap.get(fieldType))
                }
                if ((fieldType === 'goodProductsCount' || fieldType === 'recoverySingleProfit') && it.key === 'goodProductsOrderId') {
                  path = orderUrlMap.get(2)
                }
                const cacheRecovery = ['taskRecoveryVolume', 'selfRecoveryVolume']
                if (cacheRecovery.includes(fieldType)) {
                  path = orderUrlMap.get(3)
                }
                cache.customRender = this.customRender(path, it)
              }
            } else {
              if (it.key === 'orderId' || it.key === 'goodProductsOrderId') {
                let path = ''
                if (it.key === 'orderId') {
                  path = orderUrlMap.get(1) // 新机单
                  const cache = ['recycleCount', 'recycleAllCount', 'lowVauleRecycleAmount', 'recycleAmount', 'recycleSingleProfit', 'recycleLoss']
                  if (cache.find(it => it === fieldType)) {
                    path = orderUrlMap.get(3) // 回收订单详情
                  }
                  if (fieldType === 'repairProfit' || fieldType === 'repairSingleProfit' || fieldType === 'repairReceiveCount' || fieldType === 'softwareProfit') {
                    path = orderUrlMap.get(4) // 维修单详情
                  }
                }
                if ((fieldType === 'recycleSingleProfit' || fieldType === 'recycleLoss' || fieldType === 'goodProductSales') && it.key === 'goodProductsOrderId') {
                  path = orderUrlMap.get(2) // 良品订详情
                }
                cache.customRender = this.customRender(path, it)
              }
            }
          }
          return cache
        })
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const { raceDetail, salesStaffDetail, bussinessReportExport } = createApi()
      const dataSource = ref([])
      const searchData = ref({})
      const headerData = ref([])
      const title = ref('')
      const isNew = ref(false)
      const reportType = ref('')

      const pagination = ref({
        current: 1,
        pageSize: 50,
        total: 0,
        pageSizeOptions: ['20', '50', '100', '200'],
        showQuickJumper: true,
        showTotal: total => `共计${total}条`
      })
      const exportDataLoading = ref(false)
      const loading = ref(false)
      const isFeatch = ref(false)

      const handleTableChange = function (paginationObj) {
        pagination.value = { ...paginationObj }
        featchList()
      }

      const exportData = async function () {
        if (pagination.value.total > 5000) {
          message.warning('数据量较大，导出可能较慢或失败，请耐心等待')
        }
        const params = { ...searchData.value }
        exportDataLoading.value = true
        const content = isNew.value ? await bussinessReportExport(params, reportType.value === 'bussinessReport' ? 1 : 2) : await proxy.$api.order.exportDetailInfo(params)
        exportDataLoading.value = false
        const date = new Date()
        let linkNode = document.createElement('a')
        linkNode.download = `${title.value}${date.getFullYear()}年${date.getMonth() +
          1}月${date.getDate()}日${date.getHours()}时${date.getMinutes()}分${date.getSeconds()}秒.xls${isNew.value ? 'x' : ''}`
        linkNode.style.display = 'none'
        let blob = new Blob([content.data], { type: isNew.value ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' : 'application/vnd.ms-excel;charset=utf-8' })
        linkNode.href = URL.createObjectURL(blob)
        document.body.appendChild(linkNode)
        linkNode.click()
        document.body.removeChild(linkNode)
      }

      const featchList = async function () {
        const { current, pageSize: size } = pagination.value
        const params = { ...searchData.value, current, size }
        loading.value = true
        const res = isNew.value ? reportType.value === 'bussinessReport' ? await raceDetail(params) : await salesStaffDetail(params) : await proxy.$store.dispatch(
          `operation/statisticsReport/${SALE_DETAIL}`,
          params
        )
        loading.value = false
        isFeatch.value = true
        if (res) {
          console.log(res)
          const { data, header } = res.data
          headerData.value = header
          dataSource.value = data.records
          pagination.value.total = data.total
        }
      }

      const init = function () {
        const query = { ...proxy.$route.query }
        const { isNew: isNewFlag, title: reportTitle, isTotal, storeAttribute, ...other } = query
        isNew.value = isNewFlag === 'true'
        reportType.value = reportTitle === '赛马业绩统计' ? 'bussinessReport' : 'salesPerson'
        title.value = `${reportTitle}明细`
        for (const key in other) {
          if (!other[key]) {
            delete other[key]
          }
          if (isNew.value) {
            if (other[key] === 'true' || other[key] === 'false') {
              other[key] = other[key] === 'true'
            }
          }
        }
        document.title = title.value
        searchData.value = other
        other.storeAttribute = Array.isArray(storeAttribute) ? storeAttribute : (storeAttribute?.split(',') || [])
        if (isTotal === 'true') {
          const cacheAreaIds = localStorage.getItem('bussinessReportAreaIdsScoped')
          cacheAreaIds && (searchData.value.areaIds = JSON.parse(cacheAreaIds))
        }
      }
      init()
      featchList()
      return {
        dataSource,
        headerData,
        handleTableChange,
        pagination,
        exportDataLoading,
        exportData,
        loading,
        isFeatch,
        title,
        isNew,
        reportType
      }
    },
    render () {
      const { dataSource, columns, handleTableChange, pagination, noData, loading, exportDataLoading, exportData, title } = this
      return <page title={title}>
        <ni-list-page push-filter-to-location={false}>
          <ni-table
            class="statistics-report-perfect-order-table"
            dataSource={dataSource}
            locale={{ emptyText: noData }}
            columns={columns}
            loading={loading}
            onChange={handleTableChange}
            pagination={pagination}
            footerTotalNum={1}
          >
            <div slot="tool">
              <a-button
                style="float: right;"
                icon="export"
                loading={exportDataLoading}
                onClick={exportData}
              >
                导出
              </a-button>
            </div>
          </ni-table>
        </ni-list-page>
      </page>
    }
  })
</script>
<style lang="scss">
.statistics-report-perfect-order-table{
  .no-data-img {
  height: 140px;
  width: 140px;
}
.no-data-text {
  width: 160px;
  height: 16px;
  font-size: 16px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.35);
  line-height: 16px;
  margin-top: 24px;
  text-align: center;
  width: 100%;
}
.no-query {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.35);
  line-height: 32px;
  margin-top: 16px;
  text-align: center;
  width: 100%;
}
}

</style>
