// 1新机订单 2良品订单 3回收订单 4维修单
export const orderUrlMap = new Map([[1, '/addOrder/editOrder?SubID='], [3, '/recoverIndex/detail?id='], [4, '/staticpc/#/after-service/order/edit/'], [2, '/StockOut/editOrder?SubID=']])

// 新机单：
// 优惠量：changePriceCount
// 配件毛利：accessoryProfit
// 智能产品毛利：smartProductProfit
// 智能产品销量：smartProductCount
// 电脑办公大件毛利：computerOfficeLargeProfit
// 电脑配件销量：computerAccessoryCount
// 电脑配件销售额：computerAccessorySales
// 电脑配件毛利：computerAccessoryProfit
// 自营服务销量：selfOperatedServiceCount
// 自营服务毛利：selfOperatedServiceProfit
// 总单量：totalOrderCount
// 手机销量：mobileCount
// 平板销量：tabletCount
// 电脑销量：computerCount
// 年包销量：annualPackCount
// 年包销售额：annualPackSales
// 官方服务销量：officialServiceCount
// 完美一单：perfectOrder
// 大件销量合计:largeCount
// 耳机销量:specialEarphoneCount
// 音响销量:specialSoundCount
// 穿戴销量:specialWearCount
// 个护清洁销量:specialPersonalCleaningCount
// 健身出行销量:specialSportsTravelCount
// 大疆销量:specialDjiCount
// 小天才销量:specialOkiiCount
// 儿童玩具销量:specialChildToysCount
// 通用配件销量:specialGeneralAccessoriesCount
// 保护壳销量:specialProtectShellCount
// 保护膜销量:specialProtectFilmCount
// 九讯服务销量:jiuXunServiceCount
// 大件网单量（总）:largeOnlineSubCount
// 大件网单量（已完成）:completeLargeOnlineSubCount
// 网单增值毛利（已完成）:onlineSubValueAddedProfit
// 网单量（总）:networkSubCount
// 网单量（已完成）:completeNetworkSubCount
// 大件毛利合计：largeProfit
// 儿童手表销量:specialChildWatchCount
// 电子教育销量:specialElectronicEducationCount
// 潮流酷玩销量:specialCoolPlayCount
// 通用数码销量:specialGeneralDigitalCount
// 生活家电销量:specialHouseholdAppliancesCount

const addOrderTypes = ['changePriceCount', 'accessoryProfit', 'smartProductProfit', 'smartProductCount', 'computerOfficeLargeProfit', 'computerAccessoryCount', 'computerAccessorySales', 'computerAccessoryProfit', 'selfOperatedServiceCount', 'selfOperatedServiceProfit', 'totalOrderCount', 'mobileCount', 'tabletCount', 'computerCount', 'annualPackCount', 'annualPackSales', 'officialServiceCount', 'perfectOrder', 'largeCount',
  'specialAreaCount',
  'specialEarphoneCount',
  'specialSoundCount',
  'specialWearCount',
  'specialPersonalCleaningCount',
  'specialSportsTravelCount',
  'specialDjiCount',
  'specialOkiiCount',
  'specialChildToysCount',
  'specialGeneralAccessoriesCount',
  'specialProtectShellCount',
  'specialProtectFilmCount',
  'jiuXunServiceCount',
  'largeOnlineSubCount',
  'completeLargeOnlineSubCount',
  'onlineSubValueAddedProfit',
  'networkSubCount',
  'completeNetworkSubCount',
  'largeProfit',
  'specialChildWatchCount',
  'specialElectronicEducationCount',
  'specialCoolPlayCount',
  'specialGeneralDigitalCount',
  'specialHouseholdAppliancesCount',
]

// 良品单：
// 良品销量：goodProductsCount
const stockOutTypes = ['goodProductsCount']

// 回收单
// 回收单毛：recoverySingleProfit
// 有价回收量：valuableRecoveryCount
// 总回收量：totalRecoveryCount
// 低价回收金额：lowRecoveryAmount
const recoverTypes = ['recoverySingleProfit', 'valuableRecoveryCount', 'totalRecoveryCount', 'lowRecoveryAmount']

// 维修单
// 硬件维修毛利: hardwareRepairProfit
// 硬件维修单毛: hardwareRepairSingleProfit
// 软件接件毛利: softwarePickupsProfit
// 硬件维修接件量：hardwareRepairPickupsCount
const afterServiceTypes = ['hardwareRepairProfit', 'hardwareRepairSingleProfit', 'softwarePickupsProfit', 'hardwareRepairPickupsCount']

const getMapItem = function (key, arr) {
  return arr.map(d => {
    return [d, key]
  })
}

export const typesMap = new Map([...getMapItem(1, addOrderTypes), ...getMapItem(2, stockOutTypes), ...getMapItem(3, recoverTypes), ...getMapItem(4, afterServiceTypes)])
