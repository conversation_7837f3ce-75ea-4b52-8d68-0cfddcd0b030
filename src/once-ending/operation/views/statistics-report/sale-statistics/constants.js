// 商品分类下拉options
export const CUSTOM_TYING_OPTIONS = [
  {
    label: '商品分类',
    value: 'cid'
  },
  {
    label: 'SKUID',
    value: 'skuId'
  },
  {
    label: '商品ID',
    value: 'productId'
  }
]

export const tipMap = new Map([['statisticsPrice', '商品剔除优惠券、积分承担之后的销售额'], ['sumCost', '小件取平均成本，大件取的是统计成本（进价-库存调价-返利/价保）'], ['grossProfitAdjustmentPrice', '销售额-成本'], ['rebate', '大件商品的返利／价保金额，小件商品没有返利价'], ['originalPrice', '零售指导价，产品标价金额'], ['sellingPrice', '改价后金额，未剔除优惠券、积分承担'], ['giftPrice', '赠品小件的原价'], ['youHuiPrice', '订单使用的优惠券金额，分摊到订单的每个商品上让商品承担'], ['jiFenPrice', '订单使用的积分，分摊到订单的每个商品上让商品承担']])
