<script lang="jsx">
  import { NiListPage, NiTable } from '@jiuji/nine-ui'
  import {
    SALE_STATISTICS_LIST,
    SALE_STATISTICS_EDIT_NOTE,
    SALE_STATISTICS_HEADER,
    SALE_STATISTICS_DETAIL_V1
  } from '@operation/store/modules/statistics-report/action-types'
  import axios from 'axios'
  import store from '~/store'
  import { search } from './components/searchData'
  import { mapGetters } from 'vuex'

  export default {
    name: 'detail',
    components: {
      NiListPage,
      NiTable
    },
    computed: {
      ...mapGetters(['userInfo']),
      hasBbzs () {
        return this.userInfo.Rank.includes('bbzs')
      }
    },
    data () {
      return {
        searchData: {},
        dataSource: [],
        sorter: {},
        pagination: {
          current: 1,
          pageSize: 50,
          total: 0,
          pageSizeOptions: ['20', '50', '100', '200'],
          showQuickJumper: true,
          showTotal: total => `共计${total}条`
        },
        columns: [],
        loading: false,
        exportDataLoading: false
      }
    },
    render () {
      return (
      <page title={this.searchData.customTying ? '搭售详情' : '销售详情'}>
        <ni-list-page push-filter-to-location={false}>
          <ni-table
            loading={ this.loading }
            data-source={this.dataSource}
            columns={this.columns}
            onChange={this.handleTableChange}
            pagination={this.pagination}
          >
            <div slot="tool">
              <a-button
                style="float: right;"
                icon="export"
                loading={this.exportDataLoading}
                onClick={this.exportData}
              >
                导出
              </a-button>
            </div>
          </ni-table>
        </ni-list-page>
      </page>
      )
    },
    created () {
      if (this.$route.query.copySearch) {
        this.searchData = JSON.parse(this.$route.query.copySearch)
      }
      this.detailType = this.searchData.customTying ? 1 : 2
      this.getHearder()
      this.getStatisticData()
    },
    methods: {
      handleTableChange (pagination, _, sorter) {
        this.pagination = { ...pagination }
        this.sorter = { ...sorter }
        this.getStatisticData()
      },
      async getHearder () {
        const { current, pageSize: size } = this.pagination
        let params = { ...this.searchData, current, size, type: 5 }
        params.hasHighGrade = !!this.searchData.hasHighGrade
        const res = await this.$store.dispatch(
          `operation/statisticsReport/${SALE_STATISTICS_HEADER}`,
          params
        )
        if (res) {
          this.columns = []
          const { header = [] } = res.data
          header.map(d => {
            d.titleStr = d.title
            d.description = d.description || undefined
            d.oldDescription = d.description
            d.isEdit = false
            d.title = () => (
            <span>
              {d.titleStr}
              {this.hasBbzs ? (
                <a-popover overlay-class-name="title-popover">
                  <template slot="content">
                    {!d.isEdit && (
                      <p style="color:#333333;font-size:14px">
                        {d.description || '未添加注释'}
                      </p>
                    )}
                    {d.isEdit && (
                      <a-textarea
                        autoSize
                        style="border-radius: 2px;"
                        value={d.description}
                        onInput={e => {
                          d.description = e.target.value
                        }}
                        placeholder="请输入注释"
                        allowClear
                      />
                    )}
                    {d.isEdit && (
                      <div style="text-align: right;margin-top: 13px;">
                        <a-button
                          size="small"
                          onClick={() => this.cancelNote(d)}
                        >
                          取消
                        </a-button>
                        <a-button
                          size="small"
                          class="ml-8"
                          type="primary"
                          onClick={() => this.editNote(d)}
                        >
                          确定
                        </a-button>
                      </div>
                    )}
                    {!d.isEdit && (
                      <div style="text-align: right;margin-top: 13px;">
                        <a-button
                          size="small"
                          type="primary"
                          onClick={() => {
                            d.isEdit = true
                          }}
                        >
                          编辑
                        </a-button>
                      </div>
                    )}
                  </template>
                  <span>
                    <a-icon
                      onClick={e => {
                        e.preventDefault()
                      }}
                      style="color:rgba(0, 0, 0, 0.85);margin-left: 8px;"
                      type="question-circle"
                    />
                  </span>
                </a-popover>
              ) : d.description ? (
                <a-tooltip title={d.description}>
                  <span>
                    <a-icon
                      style="color:rgba(0, 0, 0, 0.85);margin-left: 8px;"
                      type="question-circle"
                    />
                  </span>
                </a-tooltip>
              ) : null}
            </span>
            )
            if (
              d.key === 'rebate' ||
              d.key === 'grossProfitAdjustmentPrice' ||
              d.key === 'sumCost'
            ) {
              d.customRender = text => <span>{text || '--'}</span>
            }
            if (d.key === 'subId') {
              d.customRender = text => {
                const reg = new RegExp('R')
                const subId = text.replace(reg, '')
                return <a href={`/addOrder/editOrder?SubID=${subId}`}>{ text }</a>
              }
            }
            d.dataIndex = d.key
            d.sorter = d.sorting
          })
          this.columns = header
        }
      },
      async getStatisticData () {
        const { current, pageSize: size } = this.pagination
        const { field, order } = this.sorter
        let params = { ...this.searchData, current, size, type: 5 }
        params.hasHighGrade = !!this.searchData.hasHighGrade
        if (order) {
          params.sorterField = field
          params.sorterOrder = order
        }
        if (this.$tnt.xtenant < 1000) {
          params.tenantId = this.$tnt.tenantId || 10000
        }
        try {
          this.loading = true
          const res = await this.$store.dispatch(
            `operation/statisticsReport/${this.$tnt.xtenant < 1000 ? SALE_STATISTICS_DETAIL_V1 : SALE_STATISTICS_LIST}`,
            params
          )
          if (res) {
            const {
              data: {
                page: { records, total }
              }
            } = res
            records.forEach(item => {
              Object.keys(item).map(d => {
                if ((Object.prototype.toString.call(item[d]) === '[object Number]' || Object.prototype.toString.call(item[d]) === '[object String]') && !isNaN(item[d]) && `${item[d]}`.includes('.')) {
                  item[d] = search.formatNum(Number(item[d]).toFixed(2))
                }
              })
            })
            this.dataSource = records
            this.pagination.total = total
          }
        } catch (e) {
          console.log(e)
        } finally {
          this.loading = false
        }
      },
      exportData () {
        const url = this.$tnt.xtenant < 1000 ? '/cloudapi_nc/ncSegments/api/report/salesStatisticsDetailExport/v1?xservicename=oa-ncSegments' : this.$api.excelAction.exportExcel('ncSegments')
        const headers = this.$tnt.xtenant < 1000 ? {} : { scene: 'sale_report_detail' }
        const date = new Date()
        const { current, pageSize: size } = this.pagination
        let params = { ...this.searchData, current, size, type: 5 }
        params.hasHighGrade = !!this.searchData.hasHighGrade
        if (this.$tnt.xtenant < 1000) {
          params.tenantId = this.$tnt.tenantId || 10000
        }
        this.exportXlsx({
          url,
          params: params,
          headers,
          fileName: `${
            this.searchData.customTying ? '搭售详情' : '销售详情'
          }${date.getFullYear()}年${date.getMonth() +
            1}月${date.getDate()}日${date.getHours()}时${date.getMinutes()}分${date.getSeconds()}秒.xlsx`
        })
      },
      /**
       * 导出表格方法
       * @param { Object } param
       * @param { Object } headers 主要是传入后端 @方鑫 所定义的表格数据导出的公共接口的场景 { scene：'' }
       */
      exportXlsx ({
        url = '',
        params = {},
        method = 'post',
        headers = {},
        fileName = '导出.xlsx'
      } = {}) {
        this.exportDataLoading = true
        axios({
          method: method,
          url: url,
          data: params,
          responseType: 'blob',
          headers: {
            Authorization: store.state.token,
            ...headers
          }
        }).then(res => {
          this.exportDataLoading = false
          const link = document.createElement('a')
          let blob = new Blob([res.data], { type: 'application/x-excel' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = fileName
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        })
      },
      async editNote (item) {
        const { key: field, description: note } = item
        const params = {
          type: 5,
          field,
          note
        }
        const res = await this.$store.dispatch(
          `operation/statisticsReport/${SALE_STATISTICS_EDIT_NOTE}`,
          params
        )
        if (res) {
          this.$message.success('修改成功')
          this.getHearder()
        }
      },
      cancelNote (item) {
        item.description = item.oldDescription
        item.isEdit = false
      }
    }
  }
</script>

<style lang="scss" scoped>
.page-title {
  padding-right: 12px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 600;
  font-size: 20px;
  margin-bottom: 8px;
}
.excel-btn {
  position: absolute;
  right: 16px;
  top: 16px;
}
</style>
