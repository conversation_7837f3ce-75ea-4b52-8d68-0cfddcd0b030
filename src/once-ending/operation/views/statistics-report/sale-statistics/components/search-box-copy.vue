<template>
  <ni-filter :form="searchData" :loading="loading" @filter="doSearch" :label-width="85" :immediate="false" v-if="searchList.length">
      <ni-filter-item v-for="(item, index) in searchList.filter(d => $tnt.xtenant >= 1000 || ($tnt.xtenant < 1000 && d.key !== 'brandLabel'))" :key="index" :label="item.title">
      <!--条件查询输入框-->
      <a-input-group class="flex" v-if="item.key === 'keyword'"
        compact>
        <a-select
          v-model="searchData[`${item.key}Key`]"
          :get-popup-container="getPopupContainer"
          style="width: 37%"
          @change="searchData[item.key] = undefined"
          showArrow>
          <template v-for="d in item.options">
            <a-select-option :title="d.label" :key="d.value" :value="d.value">{{d.label}}</a-select-option>
          </template>
        </a-select>
        <a-auto-complete
          v-if="searchData[`${item.key}Key`] === 'productName'"
          v-model="searchData[item.key]"
          :data-source="productOptions"
          style="width: 63%"
          placeholder="请输入"
          @search="productSearch"
          @change="productSearch"
          allowClear
        />
        <a-input
          v-else
          style="width: 63%"
          :placeholder="item.placeholder"
          v-model="searchData[item.key]"
          allowClear/>
      </a-input-group>
      <!--简单的下拉选择-->
      <template v-else-if="item.key === 'aggregationDimension' || item.key === 'statisticalItems' || item.key === 'brandId' || item.key === 'subType' || item.key === 'subCheck' || item.key === 'thisMobile' || item.key === 'virtualProduct' || item.key === 'subPay' || item.key === 'delivery' || item.key === 'areaType' || item.key === 'areaLevel' || item.key === 'costType'">
        <a-select v-if="item.key === 'brandId'"
          :placeholder="item.placeholder"
          :get-popup-container="getPopupContainer"
          showArrow
          :maxTagCount="1"
          :mode="item.multiple ? 'multiple' : 'default'"
          v-model="searchData[item.key]"
          optionFilterProp="children"
          @focus="selectFocus(item)"
          @blur="selectBlur(item)"
          @select="handleSelect(item)"
          @search="(val) => selectSearch(val,item)"
          @popupScroll="selectPopupScroll($event,item)"
          :dropdownStyle="{minWidth: item.dropdownWidth || 'auto'}"
          :allowClear="item.disClear ? false : true">
          <template v-for="d in selectOptions">
            <a-select-option :title="d.label" :key="d.value" :value="d.label">{{d.label}}</a-select-option>
          </template>
        </a-select>
        <a-select
            v-else-if="item.key === 'areaType'"
            :get-popup-container="getPopupContainer"
            showArrow
            :placeholder="item.placeholder"
            :maxTagCount="1"
            :mode="$tnt.xtenant !== 0 ? 'multiple' : 'default'"
            v-model="searchData[item.key]"
            :allowClear="item.disClear ? false : true">
            <template v-for="d in item.options">
            <a-select-option :title="d.label" :key="d.value" :value="d.value">{{d.label}}</a-select-option>
          </template>
            </a-select>
        <a-select v-else
          showArrow
          :get-popup-container="getPopupContainer"
          :placeholder="item.placeholder"
          :maxTagCount="1"
          :mode="item.multiple ? 'multiple' : 'default'"
          :options="item.options"
          v-model="searchData[item.key]"
          @change="val => onChangeSelect(val, item)"
          :allowClear="item.disClear ? false : true">
          <template v-for="d in item.options">
            <a-select-option :title="d.label" :key="d.value" :value="d.value">{{d.label}}</a-select-option>
          </template>
          </a-select>
      </template>
      <!--树型控件-->
      <a-tree-select v-else-if="item.key === 'cid' || item.key === 'areaAttribute' || item.key === 'brandLabel'"
        showArrow
        showSearch
        tree-node-filter-prop="title"
        :treeCheckable="item.key !== 'brandLabel'"
        :class="{'more-select':searchData[item.key] && Array.isArray(searchData[item.key]) && searchData[item.key].length && searchData[item.key].length > 1}"
        allowClear
        :dropdownStyle="{ maxHeight: '300px' }"
        :placeholder="item.placeholder"
        :get-popup-container="getPopupContainer"
        :maxTagCount="1"
        :tree-data="item.treeData"
        :value="searchData[item.key]"
        @change="onTreeSelect($event, item)"/>
      <!--分类日期选择-->
      <NiAreaSelect
        v-else-if="item.key === 'areaId'"
        multiple
        allowClear
        class="area-selector"
        placeholder="请选择地区"
        :ranks="['2c5']"
        :maxTagCount="1"
        v-model="searchData[item.key]"
      />
        <!-- v-model="searchData[item.key]" -->
      <a-input-group class="flex" v-else-if="item.key === 'payTime'"
        compact>
        <a-select :get-popup-container="getPopupContainer" v-model="searchData[`${item.key}Key`]" style="width: 37%" showArrow>
          <template v-for="d in item.options">
            <a-select-option :title="d.label" :key="d.value" :value="d.value">{{d.label}}</a-select-option>
          </template>
        </a-select>
        <a-range-picker show-time :get-calendar-container="getPopupContainer" style="width: 63%" v-model="searchData.time" value-format="YYYY-MM-DD HH:mm:ss"></a-range-picker>
        </a-input-group>
        <!-- <a-checkbox v-else-if="item.key === 'bigCustomer'" :checked="searchData[item.key]" @change="checkBoxChange($event, item)"/> -->
        <!-- <a-switch v-else-if="item.key === 'bigCustomer'" v-model="searchData[item.key]" /> -->
    </ni-filter-item>
       <!-- 自定义搭售 -->
      <ni-filter-item label="自定义搭售" v-if="!customTyingIsHide">
        <a-tooltip>
          <template slot="title">
            <p>请勿选择与主商品相同的搭售商品分类</p>
            <p>SKUID和商品ID支持输入多个，用英文逗号隔开</p>
          </template>
          <a-input-group class="flex" compact>
            <a-select
            style="width:37%"
              v-model="searchData.customTying"
              @change="customTyingChange"
              :get-popup-container="getPopupContainer"
              class="customTying"
              showArrow
              >
              <template v-for="d in constants.CUSTOM_TYING_OPTIONS">
            <a-select-option :title="d.label" :key="d.value" :value="d.value">{{d.label}}</a-select-option>
          </template>
              </a-select>
            <a-input
              v-if='searchData.customTying !== "cid"'
              style="width: 63%"
              v-model="searchData.customTyingKey"
              allowClear/>
            <a-tree-select v-else
              showArrow
              :get-popup-container="getPopupContainer"
              :class="{'more-select':searchData.customTyingCid && searchData.customTyingCid.length && searchData.customTyingCid.length > 1}"
              treeCheckable
              tree-node-filter-prop="title"
              showSearch
              allowClear
              :dropdownStyle="{ maxHeight: '300px' }"
              style="width: 63%;"
              :maxTagCount="1"
              :tree-data="cidOptions"
              @change="onChangeCustomTyingCid($event)"/>
          </a-input-group>
        </a-tooltip>
      </ni-filter-item>
      <ni-filter-item label="采购渠道" v-if="$tnt.xtenant >= 1000">
        <channel-select
              v-model="searchData.insourceid2List"
              :kind="3"
              :allowClear="true"
              mode="multiple"
              :immediate="true"
            />
      </ni-filter-item>
</ni-filter>
</template>

<script type="text/jsx">
  import { NiFilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import * as constants from '../constants'
  import ChannelSelect from './channel-select'
  export default {
    name: 'search',
    props: {
      searchList: {
        type: Array,
        default: ([])
      },
      searchData: {
        type: Object,
        default: () => ({})
      },
      loading: {
        type: Boolean,
        default: false
      }
    },
    data () {
      return {
        constants,
        selectOptions: [],
        selectSearchValue: '',
        productOptions: [],
      }
    },
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect,
      ChannelSelect
    },
    computed: {
      cidOptions () {
        const cid = this.searchList.find(item => item.key === 'cid')
        return cid?.treeData
      },
      customTyingIsHide () {
        return [9, -101].includes(this.searchData.subCheck)
      }
    },
    watch: {
      'searchData.cid': {
        deep: true,
        handler (val) {
          if (!val) return
          const searchItem = this.searchList.find(d => d.key === 'brandId')
          const allOptions = JSON.parse(JSON.stringify(searchItem.allOptions))
          if (!val.length) {
            searchItem.options = allOptions
            return
          }
          searchItem.options = allOptions.filter(d => {
            const s = new Set(val)
            return [...new Set(d.category || [])].filter(x => s.has(`${x}`)).length
          })
        }
      }
    },
    methods: {
      productSearch (keyWord) {
        this.productOptions = []
        if (keyWord) {
          this.keyWord = keyWord
          this.$api.store.productPid({ world: keyWord, limit: 20 }).then(res => {
            if (res.code === 0) {
              this.productOptions = res.data?.map(item => item.productName)
            } else {
              this.$message.error(res.userMsg)
            }
          })
        }
      },
      customTyingChange (val) {
        this.searchData.customTyingCid = []
        this.searchData.customTyingKey = ''
      },
      onChangeSelect (value, item) {
        const needClear = [9, -101].includes(value)
        if (item.key === 'subCheck' && needClear) {
          // 订单状态为[退款、返销]，隐藏自定义搭售
          this.searchData.customTyingCid = []
          this.searchData.customTyingKey = ''
        }
      },
      onTreeSelect (selectedKeys, info) {
        this.searchData[info.key] = selectedKeys
        if (info.key === 'cid') {
          if (this.searchData[info.key].length) this.searchData.brandId = []
        }
      },
      changeArea (selectedKeys, info) {
        // 对选中的值进行处理，将所有的id存放到数组中
        let selectKey = []
        selectedKeys.forEach(item => {
          if (item.children) {
            item.children.forEach(ite => {
              selectKey = ite.children ? selectKey.concat(Array.from(ite.children, ({ id }) => id)) : selectKey.concat([ite.id])
            })
          } else {
            selectKey.push(item.id)
          }
        })
        this.searchData[info.key] = selectKey
      },
      changeDepartment (selectedKeys, info) {
        this.searchData[info.key] = selectedKeys
      },
      onChangeCustomTyingCid (ids) {
        this.searchData.customTyingCid = ids
      },
      // 查询按钮被点击，收集查询参数发送给父组件
      doSearch () {
        this.$emit('search')
      },
      // 每次下拉框获取焦点,初始化下拉框数据
      selectFocus (item) {
        if (this.selectOptions.length) {
          return
        }
        const options = JSON.parse(JSON.stringify(item.options))
        const initOptions = options.splice(0, 50)
        this.selectOptions = initOptions
        item.cacheOptions = options
      },
      // 每次下拉框失去焦点,置空下拉框数据
      selectBlur (item) {
        this.selectOptions = []
        item.cacheOptions = []
      },
      // 每次用户输入,匹配所有数据,将数据筛选出来
      selectSearch (val, item) {
        this.selectSearchValue = val
        const options = JSON.parse(JSON.stringify(item.options))
        this.selectOptions = options.filter(d => d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase()))
        item.cacheOptions = options.filter(d => !d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase()))
      },
      // 每次用户选择以后判断是否为筛选以后查询,如果是,重置下拉数据,
      handleSelect (item) {
        if (this.selectSearchValue) {
          const options = JSON.parse(JSON.stringify(item.options))
          const initOptions = options.splice(0, 50)
          this.selectOptions = initOptions
          item.cacheOptions = options
          this.selectSearchValue = ''
        }
      },
      // 每次下拉框滚动条滚到底部,加载缓存数据
      selectPopupScroll (e, item) {
        if (!item.cacheOptions.length) {
          return
        }
        const { target } = e
        const scrollHeight = target.scrollHeight - target.scrollTop
        const clientHeight = target.clientHeight
        if (scrollHeight < clientHeight + 5) {
          const options = item.cacheOptions.splice(0, 50)
          this.selectOptions = this.selectOptions.concat(options)
        }
      },
      checkBoxChange (e, item) {
        this.searchData[item.key] = e.target.checked
      },
      getPopupContainer () {
        return document.querySelector('.statistics-report')
      }
    }
  }
</script>
<style lang="scss" scoped>
.more-select{
  :deep(.ant-select-selection__choice){
    max-width: 45%;
  }
}
.flex{
  display: flex !important;
}
</style>
