import cloneDeep from 'lodash/cloneDeep'
export function dealTreeData (arry, onlyLast = false) {
  let cache = []
  arry.forEach(ite => {
    const o = {
      title: ite.key || ite.label,
      key: ite.value,
      value: ite.value,
      children: (ite.children && ite.children.length) ? dealTreeData(ite.children, onlyLast) : []
    }
    if (onlyLast && ite.children && ite.children.length) {
      o.selectable = false
    }
    cache.push(o)
  })
  return cache
}
function compare (key, sort) {
  return function (m, n) {
    let a = m[key]
    let b = n[key]
    if (sort === 'ascend') {
      return a - b // 升序
    } else {
      return b - a // 降序
    }
  }
}

const search = {
  list: [{
    key: 'aggregationDimension',
    multiple: false,
    options: [],
    placeholder: '',
    selected: null,
    title: '统计方案',
    disClear: true,
    treeData: null,
    type: 'select',
    value: 'area'
  },
  {
    key: 'areaId',
    multiple: false,
    options: [],
    placeholder: '',
    selected: null,
    title: '地区',
    treeData: [],
    type: 'tree',
    value: undefined
  },
  {
    key: 'payTime',
    multiple: false,
    options: [],
    placeholder: '请选择日期',
    selected: '',
    title: '交易时间',
    treeData: null,
    type: 'selectDate',
    value: 'payTime'
  },
  {
    key: 'subCheck',
    multiple: false,
    options: [],
    placeholder: '',
    disClear: true,
    selected: null,
    title: '订单状态',
    treeData: null,
    type: 'select',
    value: null
  },
  {
    key: 'cid',
    multiple: false,
    options: [],
    placeholder: '',
    selected: null,
    title: '商品分类',
    treeData: [],
    type: 'tree',
    value: null
  },
  {
    key: 'brandId',
    multiple: true,
    options: [],
    allOptions: [],
    placeholder: '',
    selected: null,
    title: '品牌',
    treeData: null,
    type: 'select',
    value: null
  },
  {
    key: 'brandLabel',
    multiple: false,
    options: [],
    placeholder: '',
    selected: null,
    title: '统计标签',
    treeData: null,
    type: 'select',
    value: null
  },
  {
    key: 'thisMobile',
    multiple: false,
    options: [],
    placeholder: '',
    selected: null,
    disClear: true,
    title: '大小件',
    treeData: null,
    type: 'select',
    value: null
  },
  {
    key: 'subType',
    multiple: true,
    options: [],
    placeholder: '',
    selected: null,
    title: '订单类型',
    treeData: null,
    type: 'select',
    value: []
  },
  {
    key: 'virtualProduct',
    multiple: false,
    options: [],
    placeholder: '',
    selected: null,
    disClear: true,
    title: '是否包含虚拟商品',
    treeData: null,
    type: 'select',
    value: null
  },
  {
    key: 'keyword',
    multiple: false,
    options: [],
    placeholder: '请输入',
    selected: 'productName',
    title: '关键字',
    treeData: null,
    type: 'selectInput',
    value: null
  },

  {
    key: 'subPay',
    multiple: true,
    options: [],
    placeholder: '',
    selected: null,
    title: '支付方式',
    treeData: null,
    type: 'select',
    value: ['sub_pay01']
  },
  {
    key: 'delivery',
    multiple: true,
    options: [],
    placeholder: '',
    selected: null,
    title: '配送方式',
    treeData: null,
    type: 'select',
    value: []
  },
  {
    key: 'areaType',
    multiple: true,
    options: [],
    placeholder: '',
    selected: null,
    title: '门店类别',
    treeData: null,
    type: 'select',
    value: []
  },
  {
    key: 'areaLevel',
    multiple: true,
    options: [],
    placeholder: '',
    selected: null,
    title: '门店级别',
    treeData: null,
    type: 'select',
    value: []
  },
  {
    key: 'areaAttribute',
    multiple: true,
    options: [],
    placeholder: '',
    selected: null,
    title: '门店属性',
    treeData: [],
    type: 'tree',
    value: []
  }
  ],
  dealData: function (data, list) {
    let cacheData = cloneDeep(data)
    let cacheList = cloneDeep(list)
    const dataList = []
    if (list.find(it => it.title === '成本类型')) {
      cacheData.splice(cacheData.length - 8, 0, {
        key: 'costType',
        multiple: false,
        options: [],
        placeholder: '请选择成本类型',
        selected: null,
        title: '成本类型',
        disClear: true,
        treeData: null,
        type: 'select',
        value: null
      })
    }
    cacheData.forEach(it => {
      cacheList.forEach(item => {
        // 按此判断，返回的数据title要与筛选项的title完全一致
        if (it.title === item.title) {
          it.key = item.key
          if (item.list.length) {
            it.options = item.list.map(its => {
              const o = {
                label: its.name,
                value: its.value
              }
              if (it.key === 'brandId') o.category = its.category
              return o
            })
            if (it.allOptions) {
              it.allOptions = JSON.parse(JSON.stringify(it.options))
            }
            if (item.moreSelect === 2) {
              let cache = []
              item.list.forEach(i => {
                if (i.selected) {
                  cache.push(i.value)
                }
              })
              it.value = cache
            }
            if (item.moreSelect === 1) {
              item.list.forEach(i => {
                if (i.selected) {
                  it.value = i.value
                }
              })
            }
            if (it.title === '交易时间') {
              it.title = '日期'
              it.time = item.defaultValue
            }
            if (it.title === '是否包含虚拟商品') {
              it.title = '含虚拟产品'
            }
          }
          if (it.type === 'tree' && item.tree.length) {
            it.treeData = dealTreeData(item.tree)
          }
          dataList.push(it)
        }
      })
    })
    return dataList
  },
  formatNum: function (value) {
    if (isNaN(value)) return value
    let _value = value
    if (!_value) {
      return '0'
    }
    if (typeof parseFloat(_value) !== 'number') {
      return '0'
    }
    if (typeof _value === 'string') {
      // 处理大于2位的小数字符串
      if (_value.match('.') && _value.split('.')[1]?.length > 2) {
        _value = parseFloat(_value).toFixed(2)
      } else {
        // _value = parseFloat(_value).toString()
      }
    } else if (typeof _value === 'number') {
      // 处理大于2位的小数
      if (
        _value.toString().match('.') &&
        _value.toString().split('.')[1]?.length > 2
      ) {
        _value = _value.toFixed(2)
      } else {
        _value = _value.toString()
      }
    }
    const cache = new RegExp(/\B(?=(\d{3})+(?!\d))/g)
    return _value.replace(cache, ',') // 转千分位
  },
  sortData: function (key, sort, arry) {
    return arry.sort(compare(key, sort))
  }
}
export {
  search
}
