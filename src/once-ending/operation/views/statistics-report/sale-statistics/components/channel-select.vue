<!--渠道搜素-->
<template>
  <a-select
  class="channel-select"
    show-search
    placeholder="搜索渠道"
   :filter-option="false"
    style="width: 100%"
    @change="onChangeChannel"
    @search="searchData"
    :allowClear="allowClear"
    :getPopupContainer="getPopupContainer"
    :mode="mode"
    :maxTagCount="maxTagCount"
    v-model="channelData"
  >
    <a-select-option v-for="d in searchResult" :key="d.id" :value="d.id">
      {{ d.companyJc+'（'+d.id+'）' }}
    </a-select-option>
  </a-select>
</template>

<script>
  import { debounce } from 'lodash'
  export default {
    props: {
      value: {
        type: Number | Array,
        default: undefined,
      },
      kind: { // 0 小件渠道， 1 维修配件 2 行政物品， 3 大件， 4 媒介， 5 营销物料， 6 竞价合作商， 10 商品发货商， 11 运营商渠道
        type: Number,
        default: 3,
      },
      allowClear: {
        type: <PERSON><PERSON><PERSON>,
        default: false,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      maxTagCount: {
        type: Number,
        default: 1,
      },
      mode: {
        validator: function (value) {
          return ['default', 'multiple', 'tags', 'combobox'].indexOf(value) !== -1
        },
        default: 'default'
      },
      getPopupContainer: {
        type: Function,
        default: () => document.body
      },
      immediate: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      value (e) {
        this.channelData = e
      }
    },
    data () {
      this.searchData = debounce(this.searchData, 300)
      return {
        channelData: this.value,
        searchResult: [],
        insourceId: null
      }
    },
    created () {
      if (this.immediate) this.searchData('')
    },
    methods: {
      // 搜索渠道名称
      onSearchChannel (insourceId, searchKey) {
        let data = { kinds: this.kind, q: searchKey, limit: 100 }
        if (insourceId) {
          this.insourceId = insourceId
          data.insourceid = insourceId
        }
        this.$api.channel
          .getInsourceChannelList(data)
          .then((res) => {
            if (res.code === 0) {
              this.searchResult = res.data
            } else {
              this.$message.error(res.msg || '查询渠道数据出错')
            }
          })
      },
      onChangeChannel (val) {
        this.$emit('input', val)
      },
      searchData (data) {
        this.onSearchChannel(this.insourceId, data)
      }
    },
  }
</script>

<style lang="scss" scoped>
.channel-select{
  :deep(.ant-select-selection__choice){
    max-width: 68%;
  }
}
</style>
