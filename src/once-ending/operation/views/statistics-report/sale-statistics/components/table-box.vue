<template>
  <ni-table
  class="statistics-report-table"
    :locale="{ emptyText: noData }"
    :dataSource="dataSource"
    :columns="columns"
    :loading="loading"
    @change="tableChange"
    :pagination="pagination"
    :footerTotalNum="1"
  >
    <div slot="tool">
      <div class="tool">
        <div><span>剔除大客户：</span><a-switch v-model="searchData.bigCustomer" /></div>
        <a-button
        icon="export"
        :loading="exportDataLoading"
        @click="exportData"
        >导出</a-button
      >
      </div>

    </div>
  </ni-table>
</template>
<script lang="jsx">
  import { NiTable, NiImg } from '@jiuji/nine-ui'
  export default {
    components: {
      NiTable,
      NiImg
    },
    props: {
      searchData: {
        type: Object,
        default: () => ({})
      },
      dataSource: {
        type: Array,
        default: () => []
      },
      columns: {
        type: Array,
        default: () => []
      },
      loading: {
        type: Boolean,
        default: false
      },
      exportDataLoading: {
        type: Boolean,
        default: false
      },
      isFeatch: {
        type: Boolean,
        default: false
      },
      pagination: {
        type: Object,
        default: () => {
          return {
            current: 1,
            pageSize: 50,
            total: 0,
            pageSizeOptions: ['20', '50', '100', '200'],
            showQuickJumper: true,
            showTotal: total => `共计${total}条`
          }
        }
      }
    },
    data () {
      return {
        noData: () => (
        <div>
          <NiImg
            class="no-data-img"
            src="https://img2.ch999img.com/newstatic/14450/04349c93014c93f0.png"
          />
          {!this.isFeatch && <p class="no-query">请点击查询按钮查看数据</p>}
          {this.isFeatch && <p class="no-data-text">抱歉，没有查询到数据</p>}
        </div>
      )
      }
    },
    methods: {
      exportData () {
        this.$emit('exportData')
      },
      tableChange (pagination, filters, sorter) {
        this.$emit('tableChange', { paginationObj: pagination, filters, sorter })
      }
    }
  }
</script>
<style lang="scss">
.statistics-report-table{
  .no-data-img {
  height: 140px;
  width: 140px;
}
.no-data-text {
  width: 160px;
  height: 16px;
  font-size: 16px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.35);
  line-height: 16px;
  margin-top: 24px;
  text-align: center;
  width: 100%;
}
.no-query {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.35);
  line-height: 32px;
  margin-top: 16px;
  text-align: center;
  width: 100%;
}
.tool{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
}

</style>
