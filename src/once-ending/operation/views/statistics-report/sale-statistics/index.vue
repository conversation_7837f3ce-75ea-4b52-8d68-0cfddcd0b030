<template>
  <page class="statistics-report">
    <div slot="extra">数据更新于{{ lastUpdateDate }}</div>
    <ni-list-page :push-filter-to-location="false">
      <search-box
        :search-list="searchList"
        :search-data="searchData"
        @search="featchHeader(true)"
        :loading="loading"
      ></search-box>
      <table-box
        style="margin-top:16px"
        :search-data="searchData"
        :data-source="dataSource"
        :columns="columns"
        :table-sort="tableSort"
        :loading="loading"
        @tableChange="tableChange"
        @exportData="exportData"
        :export-data-loading="exportDataLoading"
        :is-featch="isFeatch"
        :pagination="pagination"
      ></table-box>
    </ni-list-page>
  </page>
</template>

<script type="text/javascript" lang="jsx">
  import searchBox from './components/search-box'
  import TableBox from './components/table-box'
  import { search, dealTreeData } from './components/searchData'
  import moment from 'moment'
  import { NiListPage } from '@jiuji/nine-ui'
  import { dateFormat, to } from '~/util/common'
  import * as constants from './constants'
  import axios from 'axios'
  import store from '~/store'
  import useReportHearder from '../hooks/useReportHearder'
  import {
    SALE_STATISTICS,
    SALE_STATISTICS_LIST,
    SALE_STATISTICS_LIST_V1
  } from '@operation/store/modules/statistics-report/action-types'
  import { h, ref, onMounted, getCurrentInstance } from 'vue'
  import { message } from 'ant-design-vue'
  import useCommon from '../hooks/useCommon'
  import statisticsReport from '@operation/api/statistics-report'
  export default {
    setup (props) {
      const { proxy } = getCurrentInstance()
      const { loading, isFeatch } = useCommon()
      const { hearderData, getHearder } = useReportHearder({ proxy, h, type: 4 })
      const searchList = ref([])
      const searchData = ref({})
      const dataSource = ref([])
      const tableSort = ref({})
      const exportDataLoading = ref(false)
      const lastUpdateDate = ref('')
      const statisticalItems = ref([])
      const pagination = ref({
        current: 1,
        pageSize: 50,
        total: 0,
        pageSizeOptions: ['20', '50', '100', '200'],
        showQuickJumper: true,
        showTotal: total => `共计${total}条`
      })
      const copyData = ref([])
      const copyTotal = ref({})
      const isShowCustomDatail = ref(false)
      onMounted(() => {
        lastUpdateDate.value = dateFormat(new Date(), 'YYYY-MM-DD hh:mm:ss')
      })
      const gotoDetailQuery = function (record, iscustom) {
        // 链接跳转
        const statisticalItems = getSelectStatisticalItems()
        const params = { ...getParams() }
        params.statisticalItems = statisticalItems
        params.aggregationId = record.aggregationId
        params.hasHighGrade = '1'
        if (record.twoAggregationId) {
          params.twoAggregationId = record.twoAggregationId
        }
        if (record.threeAggregationId) params.threeAggregationId = record.threeAggregationId
        if (!iscustom) {
          if (params.customTying) delete params.customTying // 删除自定义搭售参数
        }
        return { copySearch: JSON.stringify(params) }
      }
      const getProductMarksTree = async function () {
        const [err, res] = await to(statisticsReport.getProductMarksTree())
        if (err) throw err
        const { code, data, userMsg } = res
        if (code === 0) {
          const item = searchList.value.find(d => d.key === 'brandLabel')
          item.treeData = dealTreeData(data, true)
        } else {
          message(userMsg)
        }
      }
      // 获取统计列表的头
      const commonStatisticHeader = async function (params) {
        const res = await proxy.$store.dispatch(
          `operation/statisticsReport/${SALE_STATISTICS}`,
          params
        )

        if (res) {
          const list = search.dealData(search.list, res.data)
          // 统计项不放在筛选条件上,默认查询全部,表格显示使用组件配置
          const statisticalItem = res.data.find(
            d => d.key === 'statisticalItems'
          )
          statisticalItems.value = statisticalItem
            ? statisticalItem.list.map(d => d.value)
            : []
          searchList.value = list
          // 查统计标签
          if (proxy.$tnt.xtenant >= 1000) {
            getProductMarksTree()
          }
          // 设置默认值
          setSearchData(res.data)
          // 查询默认表头
          featchHeader()
        }
      }
      const setSearchData = function (data) {
        const dataObj = {}
        dataObj.customTying = 'cid'
        dataObj.customTyingKey = ''
        dataObj.customTyingCid = []
        searchList.value.forEach(item => {
          if (item.key === 'payTime') {
            dataObj[`${item.key}Key`] = item.value
            dataObj.time = item.time
          } else if (item.key === 'keyword') {
            dataObj[`${item.key}Key`] = item.selected
            dataObj[item.key] = item.value
          } else {
            dataObj[item.key] = item.value
          }
        })
        // 大客户筛选取值
        const bigCustomer = data.find(d => d.key === 'bigCustomer')
        dataObj.bigCustomer = bigCustomer ? bigCustomer.checked : false
        // 设置采购渠道默认值
        if (proxy.$tnt.xtenant >= 1000) dataObj.insourceid2List = []
        searchData.value = dataObj
      }

      const getParams = function () {
        const params = { ...searchData.value }
        const {
          keywordKey,
          keyword,
          payTimeKey,
          time,
          customTying,
          customTyingCid,
          customTyingKey
        } = searchData.value
        params[payTimeKey] = time
        params[keywordKey] = keyword
        delete params.payTimeKey
        delete params.keywordKey
        delete params.time
        delete params.keyword
        delete params.customTying
        delete params.customTyingCid
        delete params.customTyingKey
        // 自定义搭售
        if (customTyingCid?.length) {
          params.customTying = { cid: customTyingCid }
        } else if (customTyingKey) {
          params.customTying =
            customTying === 'productId'
              ? { productId: customTyingKey }
              : { skuId: customTyingKey }
        }
        // 门店类别输出为多选,九机为单选
        if (typeof params.areaType === 'number') {
          params.areaType = [params.areaType]
        }
        params.type = 4
        params.hasHighGrade = true
        // 过滤参数
        for (let key in params) {
          if (
            (!params[key] && params[key] !== 0) ||
            (params[key] && Array.isArray(params[key]) && !params[key].length)
          ) {
            delete params[key]
          }
        }
        return params
      }

      const checkForm = function () {
        const {
          aggregationDimension,
          subCheck,
          virtualProduct,
          thisMobile,
          keywordKey,
          keyword,
          time,
          customTying,
          customTyingKey
        } = searchData.value
        if (!aggregationDimension) {
          message.warning('请选择统计方案')
          return true
        }
        if (!subCheck) {
          message.warning('请选择订单状态')
          return true
        }
        if (!virtualProduct && virtualProduct !== 0) {
          message.warning('请选择包含虚拟商品')
          return true
        }
        if (!thisMobile && thisMobile !== 0) {
          message.warning('请选择大小件')
          return true
        }
        if (
          (keywordKey === 'skuId' || keywordKey === 'productId') &&
          keyword &&
          Number(keyword) + '' === 'NaN'
        ) {
          message.warning('SKUID或商品ID须为数字')
          return true
        }
        if (!time.length) {
          message.warning('请选择时间段')
          return true
        }
        if (time[0] > time[1]) {
          message.warn('开始时间不能大于结束时间')
          return true
        }
        if (
          moment(time[0])
            .add(3, 'months')
            .format('YYYY-MM-DD HH:mm:ss') < time[1]
        ) {
          message.warn('时间间隔不能超过三个月')
          return true
        }
        if (
          (customTying === 'productId' || customTying === 'skuId') &&
          customTyingKey && customTyingKey.replace(/,/g, '').replace(/[\d]+/, '').length) {
          message.warning('SKUID或商品ID须为英文逗号分隔的数字')
          return true
        }
        return false
      }

      const featchHeader = async function (type) {
        const {
          aggregationDimension,
          customTying,
          customTyingCid,
          customTyingKey
        } = searchData.value
        if (!aggregationDimension) {
          return message.warning('请选择统计方案')
        }
        if (
          (customTying === 'productId' || customTying === 'skuId') &&
          customTyingKey && customTyingKey.replace(/,/g, '').replace(/[\d]+/, '').length) {
          message.warning('SKUID或商品ID须为英文逗号分隔的数字')
          return true
        }
        const params = {
          hasHighGrade: true,
          aggregationDimension
        }
        // 自定义搭售
        if (customTyingCid?.length) {
          params.customTying = { cid: customTyingCid }
        } else if (customTyingKey) {
          params.customTying =
            customTying === 'productId'
              ? { productId: customTyingKey }
              : { skuId: customTyingKey }
        }
        await getHearder(params, featchHeader)
        hearderData.value.map(d => {
          if (d.titleStr.length >= 6) {
            d.width = 175 + 14 * (d.titleStr.length - 6)
          }
        })
        if (type) featchList(1)
      }

      const featchList = async function (current) {
        const statisticalItems = getSelectStatisticalItems()
        if (statisticalItems.length === 0) {
          return message.warn('请选择统计项')
        }
        if (checkForm()) return
        const params = getParams()
        params.statisticalItems = statisticalItems
        if (proxy.$tnt.xtenant < 1000) {
          params.tenantId = proxy.$tnt.tenantId || 10000
        }
        loading.value = true
        const res = await proxy.$store.dispatch(
          `operation/statisticsReport/${proxy.$tnt.xtenant < 1000 ? SALE_STATISTICS_LIST_V1 : SALE_STATISTICS_LIST}`,
          params
        )
        loading.value = false
        if (res) {
          if (params.customTying) {
            isShowCustomDatail.value = true
          } else {
            isShowCustomDatail.value = false
          }
          if (current) pagination.value.current = current
          isFeatch.value = true
          const { data = [], total = {} } = res.data
          copyData.value = JSON.parse(JSON.stringify(data))
          copyTotal.value = JSON.parse(JSON.stringify(total))
          pagination.value.total = copyData.value.length
          setTableData()
          lastUpdateDate.value = dateFormat(new Date(), 'YYYY-MM-DD hh:mm:ss')
        }
      }
      const setTableData = function () {
        const data = JSON.parse(JSON.stringify(copyData.value))
        const total = JSON.parse(JSON.stringify(copyTotal.value))
        sortData(data)
        const { current, pageSize: size } = pagination.value
        const start = size * current - size
        const end = size * current
        const tableData = data.slice(start, end)
        const hasMoneyToConvert = hearderData.value
          .filter(d => d.moneyToConvert)
          .map(d => d.key)
        tableData.map(d => {
          hasMoneyToConvert.map(k => {
            if (d.hasOwnProperty(k)) {
              d[k] = search.formatNum(d[k].toFixed(2))
            }
          })
        })
        const columnsKey = hearderData.value.map(d => d.key)
        for (const k in total) {
          if (columnsKey.includes(k)) {
            const columnItem = hearderData.value.find(d => d.key === k)
            total[k] = hearderData.value[0].key === k ? '合计' : columnItem.moneyToConvert
              ? search.formatNum(total[k].toFixed(2))
              : total[k]
          }
        }
        total.isTotal = true
        tableData.push(total)
        dataSource.value = tableData
      }
      const sortData = function (data) {
        if (!tableSort.value.field) return
        search.sortData(tableSort.value.field, tableSort.value.order, data)
      }
      const tableChange = function ({ paginationObj, filters, sorter }) {
        pagination.value = { ...paginationObj }
        tableSort.value = { ...sorter }
        setTableData()
      }

      const getSelectStatisticalItems = function () {
        const listPageConfig = localStorage.getItem('listPageConfig')
          ? JSON.parse(localStorage.getItem('listPageConfig'))
          : null
        if (!listPageConfig) return statisticalItems.value
        const tableColumns = listPageConfig[proxy.$route.path]?.tableColumns || {}
        const selectStatisticalItems = JSON.parse(
          JSON.stringify(statisticalItems.value)
        )
        for (const k in tableColumns) {
          if (tableColumns[k].hide && selectStatisticalItems.includes(k)) {
            const index = selectStatisticalItems.findIndex(d => d === k)
            selectStatisticalItems.splice(index, 1)
          }
        }
        return selectStatisticalItems
      }

      const exportData = function () {
        const statisticalItems = getSelectStatisticalItems()
        if (statisticalItems.length === 0) {
          return message.warn('请选择统计项')
        }
        if (checkForm()) return
        const params = getParams()
        params.statisticalItems = statisticalItems
        if (proxy.$tnt.xtenant < 1000) {
          params.tenantId = proxy.$tnt.tenantId || 10000
        }
        const url = proxy.$tnt.xtenant < 1000 ? '/cloudapi_nc/ncSegments/api/report/salesStatisticsExport/v1?xservicename=oa-ncSegments' : proxy.$api.excelAction.exportExcel('ncSegments')
        const headers = proxy.$tnt.xtenant < 1000 ? {} : { scene: 'sale_report' }
        const date = new Date()
        exportXlsx({
          url,
          params: params,
          headers,
          fileName: `销售数据${date.getFullYear()}年${date.getMonth() +
            1}月${date.getDate()}日${date.getHours()}时${date.getMinutes()}分${date.getSeconds()}秒.xlsx`
        })
      }

      /**
       * 导出表格方法
       * @param { Object } param
       * @param { Object } headers 主要是传入后端 @方鑫 所定义的表格数据导出的公共接口的场景 { scene：'' }
       */
      const exportXlsx = function ({
        url = '',
        params = {},
        method = 'post',
        headers = {},
        fileName = '导出.xlsx'
      } = {}) {
        exportDataLoading.value = true
        axios({
          method: method,
          url: url,
          data: params,
          responseType: 'blob',
          headers: {
            Authorization: store.state.token,
            ...headers
          }
        }).then(res => {
          exportDataLoading.value = false
          const link = document.createElement('a')
          let blob = new Blob([res.data], { type: 'application/x-excel' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = fileName
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        })
      }
      commonStatisticHeader({ isApp: false })
      return {
        searchList,
        searchData,
        dataSource,
        tableSort,
        loading,
        exportDataLoading,
        lastUpdateDate,
        isFeatch,
        hearderData,
        tableChange,
        featchHeader,
        exportData,
        gotoDetailQuery,
        isShowCustomDatail,
        pagination
      }
    },
    data () {
      return {
        constants,
        search,
        operationCustomRender: (text, record) => {
          return !record.isTotal ? (
            <div>
              <router-link
                target="_blank"
                to={{
                  path: '/operation/statistics-report/saleStatisticsDetail',
                  query: this.gotoDetailQuery(record)
                }}
              >
                销售详情
              </router-link>
              {this.isShowCustomDatail && (
                <router-link
                  style="margin-left:15px"
                  target="_blank"
                  to={{
                    path: '/operation/statistics-report/saleStatisticsDetail',
                    query: this.gotoDetailQuery(record, true)
                  }}
                >
                  搭售详情
                </router-link>
              )}
            </div>
            ) : null
        }
      }
    },
    computed: {
      columns () {
        if (!this.hearderData.length) return []
        const hearderData = this.hearderData
        hearderData.push({
          title: '操作',
          titleStr: '操作',
          key: 'operation',
          dataIndex: 'operation',
          fixed: 'right',
          customRender: this.operationCustomRender
        })
        return hearderData
      }
    },
    components: {
      searchBox,
      NiListPage,
      TableBox
    }
  }
</script>
<style lang="scss">
.title-popover {
  width: 172px;
}
a:visited {
  color: #d46b08 !important;
}
.statistics-report {
  position: relative;
  .ant-page-header-heading-extra {
    float: none;
    line-height: 40px;
    font-size: 12px;
    color: #9c9c9c;
  }
}
</style>
<style lang="less" scoped>
.ml-15 {
  margin-left: 15px;
}
.mt-13 {
  margin-top: 13px;
}
.ml-8 {
  margin-left: 8px;
}
.text-align-right {
  text-align: right;
}
.thead-cell {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
  padding: 4px;
}
td,
th {
  padding: 8px;
}

.table-box {
  position: relative;
  left: 0;
  top: 0;
  overflow: hidden;
}
.body-box {
  overflow: auto;
}
.header-box,
.footer-box {
  /*overflow: hidden;*/
  margin-right: 10px;
}
table {
  display: table;
  /*width: 100%;*/
  text-align: center;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  font-feature-settings: "tnum";
  border-radius: 4px 4px 0 0;
  border-spacing: 0;
  border: 1px solid #e8e8e8;
  border-right: 0;
  border-bottom: 0;
  table-layout: fixed;

  :deep(.ant-table-thead > tr > th) {
    /*display: inline-block;*/
    font-weight: bold;
    padding: 4px 4px;
    width: 100px;
  }
  :deep(.ant-table-tbody > tr > td) {
    padding: 4px 4px;
    width: 100px;
    /*display: inline-block;*/
  }
}
.sorted {
  color: #1890ff;
}
</style>
