<template>
  <page class="statistics-report">
    <div slot="extra">数据更新于{{ lastUpdateDate }}</div>
    <ni-list-page :push-filter-to-location="false">
      <search-box
        :search-list="searchList"
        :search-data="searchData"
        @search="fetchList"
        :loading="loading"
      ></search-box>
      <ni-table
        :dataSource="dataSource"
        :columns="columns"
        :loading="loading"
        @change="tableChange"
        :pagination="pagination"
        :footerTotalNum="1"
      >
        <div slot="tool">
          <div class="flex flex-justify-between">
            <div><span>剔除大客户：</span><a-switch v-model="searchData.bigCustomer" /></div>
            <a-button
            icon="export"
            :loading="exportDataLoading"
            @click="exportData"
            >导出</a-button
          >
          </div>

        </div>
      </ni-table>
    </ni-list-page>
  </page>
</template>

<script type="text/javascript" lang="jsx">
  import searchBox from './components/search-box-copy.vue'
  import TableBox from './components/table-box'
  import { search } from './components/searchData'
  import { NiListPage } from '@jiuji/nine-ui'
  import { dateFormat } from '~/util/common'
  import * as constants from './constants'
  import {
    SALE_STATISTICS,
    SALE_STATISTICS_LIST_COPY
  } from '@operation/store/modules/statistics-report/action-types'
  import { h, ref, onMounted, getCurrentInstance } from 'vue'
  import { message } from 'ant-design-vue'
  import useCommon from '../hooks/useCommon'

  export default {
    setup (props) {
      const { proxy } = getCurrentInstance()
      const { loading } = useCommon()
      const searchList = ref([])
      const searchData = ref({})
      const dataSource = ref([])
      const exportDataLoading = ref(false)
      const lastUpdateDate = ref('')
      const statisticalItems = ref([])
      const pagination = ref({
        current: 1,
        pageSize: 50,
        total: 0,
        pageSizeOptions: ['20', '50', '100', '200'],
        showQuickJumper: true,
        showTotal: total => `共计${total}条`
      })

      onMounted(() => {
        lastUpdateDate.value = dateFormat(new Date(), 'YYYY-MM-DD hh:mm:ss')
      })

      const fetchList = async () => {
        const params = {
          startTime: searchData.value.time[0],
          endTime: searchData.value.time[1],
          brandNameList: searchData.value.brandId,
          size: pagination.value.pageSize,
          current: pagination.value.current
        }
        const { code, data, userMsg } = await proxy.$store.dispatch(
          `operation/statisticsReport/${SALE_STATISTICS_LIST_COPY}`,
          params
        )
        if (code === 0) {
          dataSource.value = data.records || []
          pagination.value.total = data.total || 0
        } else {
          message.error(userMsg)
        }
      }
      // 获取统计列表的头
      const commonStatisticHeader = async function (params) {
        const res = await proxy.$store.dispatch(
          `operation/statisticsReport/${SALE_STATISTICS}`,
          params
        )
        if (res) {
          const list = search.dealData(search.list, res.data)
          // 统计项不放在筛选条件上,默认查询全部,表格显示使用组件配置
          const statisticalItem = res.data.find(
            d => d.key === 'statisticalItems'
          )
          statisticalItems.value = statisticalItem
            ? statisticalItem.list.map(d => d.value)
            : []
          searchList.value = list
          // 设置默认值
          setSearchData(res.data)
          fetchList()
        }
      }
      const setSearchData = function (data) {
        const dataObj = {}
        dataObj.customTying = 'cid'
        dataObj.customTyingKey = ''
        dataObj.customTyingCid = []
        searchList.value.forEach(item => {
          if (item.key === 'payTime') {
            dataObj[`${item.key}Key`] = item.value
            dataObj.time = item.time
          } else if (item.key === 'keyword') {
            dataObj[`${item.key}Key`] = item.selected
            dataObj[item.key] = item.value
          } else {
            dataObj[item.key] = item.value
          }
        })
        // 大客户筛选取值
        const bigCustomer = data.find(d => d.key === 'bigCustomer')
        dataObj.bigCustomer = bigCustomer ? bigCustomer.checked : false
        // 设置采购渠道默认值
        if (proxy.$tnt.xtenant >= 1000) dataObj.insourceid2List = []
        searchData.value = dataObj
      }

      const tableChange = function (e) {
        pagination.value = e
        fetchList()
      }

      commonStatisticHeader({ isApp: false })
      return {
        searchList,
        searchData,
        dataSource,
        loading,
        exportDataLoading,
        lastUpdateDate,
        tableChange,
        pagination,
        fetchList
      }
    },
    data () {
      return {
        constants,
        search,
        columns: [
          {
            title: '品牌',
            key: 'brandName',
            dataIndex: 'brandName',
          },
          {
            title: '销量',
            key: 'saleCount',
            dataIndex: 'saleCount',
          },
          {
            title: '销售额',
            key: 'salePrice',
            dataIndex: 'salePrice',
          }
        ]
      }
    },
    components: {
      searchBox,
      NiListPage,
      TableBox
    }
  }
</script>
<style lang="scss">
.title-popover {
  width: 172px;
}
a:visited {
  color: #d46b08 !important;
}
.statistics-report {
  position: relative;
  .ant-page-header-heading-extra {
    float: none;
    line-height: 40px;
    font-size: 12px;
    color: #9c9c9c;
  }
}
</style>
<style lang="less" scoped>
.ml-15 {
  margin-left: 15px;
}
.mt-13 {
  margin-top: 13px;
}
.ml-8 {
  margin-left: 8px;
}
.text-align-right {
  text-align: right;
}
.thead-cell {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
  padding: 4px;
}
td,
th {
  padding: 8px;
}

.table-box {
  position: relative;
  left: 0;
  top: 0;
  overflow: hidden;
}
.body-box {
  overflow: auto;
}
.header-box,
.footer-box {
  /*overflow: hidden;*/
  margin-right: 10px;
}
table {
  display: table;
  /*width: 100%;*/
  text-align: center;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  font-feature-settings: "tnum";
  border-radius: 4px 4px 0 0;
  border-spacing: 0;
  border: 1px solid #e8e8e8;
  border-right: 0;
  border-bottom: 0;
  table-layout: fixed;

  :deep(.ant-table-thead > tr > th) {
    /*display: inline-block;*/
    font-weight: bold;
    padding: 4px 4px;
    width: 100px;
  }
  :deep(.ant-table-tbody > tr > td) {
    padding: 4px 4px;
    width: 100px;
    /*display: inline-block;*/
  }
}
.sorted {
  color: #1890ff;
}
</style>
