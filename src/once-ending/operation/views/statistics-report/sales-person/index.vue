<script lang="jsx">
  import { defineComponent, getCurrentInstance } from 'vue'
  import SearchBox from './components/search-box'
  import TableBox from './components/table-box'
  import { createApi } from './hooks/use-api'
  import { createState } from './hooks/use-state'
  import { NiListPage } from '@jiuji/nine-ui'
  import useApiLastUpdateTime from '../hooks/useApiLastUpdateTime'

  export default defineComponent({
    components: {
      SearchBox,
      TableBox,
      NiListPage
    },
    setup () {
      const api = createApi()
      const { lastUpdateDate } = createState(api)
      const { proxy } = getCurrentInstance()
      const params = {
        id: 1,
        type_: proxy.$tnt.xtenant === 0 ? 1 : 2
      }
      const { lastNewUpdateDate, getLastUpdateDate } = useApiLastUpdateTime(params)
      getLastUpdateDate()
      return {
        lastUpdateDate,
        lastNewUpdateDate
      }
    },
    render () {
      const { lastUpdateDate, lastNewUpdateDate } = this
      return <page class="statistics-report-bussiness-report">
      <div slot="extra">数据更新于{lastNewUpdateDate || lastUpdateDate}</div>
      <ni-list-page push-filter-to-location={false}>
        <SearchBox/>
        <table-box/>
      </ni-list-page>

      </page>
    }
  })
</script>
<style lang="scss" scoped>
.statistics-report-bussiness-report {
  position: relative;
  :deep(.ant-page-header-heading-extra){
    float: none;
    line-height: 40px;
    font-size: 12px;
    color: #9c9c9c;
  }
}
</style>
