import moment from 'moment'
export const defaultFormData = {
  areaIds: [],
  times: [
    moment().format('YYYY-MM-DD 00:00:00'),
    moment().format('YYYY-MM-DD 23:59:59'),
  ],
  orderStatus: undefined,
  orderType: [],
  virtualGoods: undefined,
  costType: undefined,
  storeLevel: undefined,
  storeType: [],
  productCategory: [],
  productBrand: [],
  areaStoreIds: [],
  largePrice: undefined,
  largeCustomers: false,
  stalledClearance: undefined,
  gift: undefined,
  eliminateFreebie: undefined, // 输出  1：剔除赠品成本    2：不剔除赠品成本
  purePointsOrder: undefined,
  couponCodeOrder: undefined,
  statisticsDimension: undefined,
  keyWordType: 'productName',
  keyWord: undefined,
  staffCategory: undefined,
  recycledWool: undefined,
  excludePriceProtectFanli: undefined
}

export const keyWordType = [
  { label: '商品名称', value: 'productName' },
  { label: '商品ID', value: 'productId' },
  { label: 'ppid', value: 'ppId' },
]

export const ranges = {
  今天: [moment().startOf('day'), moment().endOf('day')],
  昨天: [moment().subtract(1, 'days').startOf('day'), moment().subtract(1, 'days').endOf('day')],
  当周: [moment().startOf('week'), moment().endOf('week')],
  上周: [moment().subtract(1, 'weeks').startOf('week'), moment().subtract(1, 'weeks').endOf('week')],
  当月: [moment().startOf('month'), moment().endOf('month')],
  上月: [moment().subtract(1, 'months').startOf('month'), moment().subtract(1, 'months').endOf('month')],
}
