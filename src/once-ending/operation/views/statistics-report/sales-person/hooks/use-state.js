import { inject, provide, reactive, computed, getCurrentInstance, ref } from 'vue'
import useCommon from '../../hooks/useCommon'
import useLastUpdateDate from '../../hooks/useLastUpdateDate'
import { defaultFormData } from '../constants'
import moment from 'moment'
import cloneDeep from 'lodash/cloneDeep'
import { message } from 'ant-design-vue'
const key = Symbol('useState')

export const useState = function () {
  return inject(key)
}

export const createState = function (api) {
  const { salesStaffData } = api
  const { proxy } = getCurrentInstance()

  const { $store, $tnt } = proxy.$root

  const { loading, isFeatch } = useCommon()

  const { lastUpdateDate, setLastUpdateDate } = useLastUpdateDate()

  const allChecked = ref(false)

  const state = reactive({
    dataSource: [],
    sorter: {},
    items: [],
    formData: cloneDeep(defaultFormData),
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      pageSizeOptions: ['10', '20', '50', '100'],
      showSizeChanger: true,
      showTotal: total => `总共 ${total} 条`,
    },
    copyData: [],
    copyTotal: {},
    options: {
      orderStatus: [],
      orderType: [],
      virtualGoods: [],
      costType: [],
      storeLevel: [],
      storeType: [],
      productCategory: [],
      productBrand: [],
      largePrice: [],
      statisticsDimension: [],
      staffCategory: [],
      recycledWool: [],
      eliminateFreebie: []
    },
  })

  const ranks = computed(() => {
    return $store.state.userInfo.Rank || []
  })

  const hasZscb = computed(() => {
    return ranks.value.includes('zscb')
  })

  const has9o9 = computed(() => {
    return ranks.value.includes('9o9')
  })

  const hasBbzs = computed(() => {
    return ranks.value.includes('bbzs')
  })

  const getBaseParams = function () {
    const { formData: { times, ...other } } = state
    const startTime = times.length ? times[0] : undefined
    const endTime = times.length ? times[1] : undefined
    return {
      ...other,
      startTime,
      endTime
    }
  }

  const getQueryParams = function () {
    const { items } = state
    const params = {
      items,
      ...getBaseParams()
    }
    if ($tnt.xtenant >= 1000) {
      delete params.gift
    } else {
      delete params.eliminateFreebie
    }
    return params
  }

  const checkForm = function () {
    const { formData: { times }, items } = state
    // 时间段不能为空
    if (!times.length) {
      // message.warning(has9o9.value ? '请选择时间段' : `请选择时间段,且时间间隔不能超过${$tnt.xtenant < 1000 ? '四' : '三'}个月`)
      message.warning('请选择交易时间段')
      return true
    }
    if (times.length && times[0] > times[1]) {
      message.warn('交易开始时间不能大于结束时间')
      return true
    }
    // if (
    //   !has9o9.value &&
    //   moment(times[0]).add($tnt.xtenant < 1000 ? 4 : 3, 'months').format('YYYY-MM-DD HH:mm:ss') < times[1]
    // ) {
    //   message.warn(`时间间隔不能超过${$tnt.xtenant < 1000 ? '四' : '三'}个月`)
    //   return true
    // }
    if (!items.length) {
      message.warn('请至少选择一个统计项')
      return true
    }
    return false
  }

  const fetchData = async function (cur) {
    if (checkForm()) return
    if (cur) state.pagination.current = cur
    const params = {
      ...getQueryParams(),
      ...state.sorter
    }
    loading.value = true
    const res = await salesStaffData(params)
    loading.value = false
    if (res) {
      isFeatch.value = true
      const { data: { data, total } } = res
      state.copyData = JSON.parse(JSON.stringify(data))
      state.copyTotal = total ? JSON.parse(JSON.stringify(total)) : {}
      state.pagination.total = state.copyData.length
      setTableData()
    }
  }

  const setTableData = function () {
    const data = JSON.parse(JSON.stringify(state.copyData))
    const total = JSON.parse(JSON.stringify(state.copyTotal))
    const { current, pageSize: size } = state.pagination
    const start = size * current - size
    const end = size * current
    const tableData = data.slice(start, end)
    if (Object.keys(total).length && tableData.length) {
      tableData.push(total)
    }
    state.dataSource = tableData
  }

  provide(key, {
    allChecked,
    state,
    lastUpdateDate,
    setLastUpdateDate,
    loading,
    isFeatch,
    fetchData,
    hasZscb,
    has9o9,
    hasBbzs,
    getBaseParams,
    getQueryParams,
    checkForm,
    setTableData
  })
  return {
    allChecked,
    state,
    lastUpdateDate,
    setLastUpdateDate,
    loading,
    isFeatch,
    fetchData,
    hasZscb,
    has9o9,
    hasBbzs,
    getBaseParams,
    getQueryParams,
    checkForm,
    setTableData
  }
}
