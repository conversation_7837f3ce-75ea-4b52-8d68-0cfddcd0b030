import { useState } from './use-state'
import { useApi } from './use-api'
import { ref, watch, nextTick } from 'vue'
import { defaultFormData } from '../constants'
import cloneDeep from 'lodash/cloneDeep'
import moment from 'moment/moment'

export default function useSearch () {
  const { state, allChecked } = useState()
  const { salesStaffOptions, productPid } = useApi()

  const productCategorySelect = function (val) {
    state.formData.productCategory = val
    if (val.length) state.formData.productBrand = []
  }

  // 显示的options
  const selectOptions = ref([])

  // 缓存的options
  const cacheOptions = ref([])
  const selectSearchValue = ref('')
  // 过滤以后的options
  const filterOptions = ref([])

  // 商品分类和品牌联动
  watch(() => state.formData.productCategory, val => {
    if (!val) return
    const allOptions = cloneDeep(state.options.productBrand)
    if (!val.length) {
      filterOptions.value = allOptions
      return
    }
    filterOptions.value = allOptions.filter(d => {
      const s = new Set(val)
      return [...new Set(d.category || [])].filter(x => s.has(`${x}`)).length
    })
  }, { deep: true })

  watch(() => state.formData.productBrand, (newVal, oldVal) => {
    allChecked.value = newVal.length === filterOptions.value.length
  })

  // 每次下拉框获取焦点或者失去焦点,初始化下拉框数据
  const selectFocusOrBlur = function () {
    if (selectOptions.value.length) {
      return
    }
    const options = cloneDeep(filterOptions.value)
    const initOptions = options.splice(0, 50)
    if (state.formData.productBrand.length) {
      state.formData.productBrand.forEach(item => {
        const index = options.findIndex(d => d.value === item)
        if (index !== -1) {
          initOptions.unshift(options.splice(index, 1)[0])
        }
      })
    }
    // selectOptions.value = initOptions
    // cacheOptions.value = options
    selectOptions.value = initOptions.splice(0, 50)
    cacheOptions.value = cloneDeep(filterOptions.value)
  }

  // 每次用户输入,匹配所有数据,将数据筛选出来
  const selectSearch = function (val) {
    selectSearchValue.value = val
    const options = cloneDeep(filterOptions.value)
    selectOptions.value = options.filter(d => d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase()))
    cacheOptions.value = options.filter(d => !d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase()))
  }

  const handleSelect = function () {
    const options = cloneDeep(filterOptions.value)
    const initOptions = options.splice(0, 50)
    if (state.formData.productBrand.length) {
      state.formData.productBrand.forEach(item => {
        const index = options.findIndex(d => d.value === item)
        if (index !== -1) {
          initOptions.unshift(options.splice(index, 1)[0])
        }
      })
    }
    selectOptions.value = initOptions
    cacheOptions.value = options
    selectSearchValue.value = ''
  }

  // 每次下拉框滚动条滚到底部,加载缓存数据
  const selectPopupScroll = function (e) {
    if (!cacheOptions.value.length) {
      return
    }
    const { target } = e
    const scrollHeight = target.scrollHeight - target.scrollTop
    const clientHeight = target.clientHeight
    if (scrollHeight < clientHeight + 5) {
      const options = cacheOptions.value.splice(0, 50)
      selectOptions.value = uniqueData(selectOptions.value.concat(options))
    }
  }

  const uniqueData = (data) => {
    return data.filter((item, index, self) =>
      index === self.findIndex(t => t.label === item.label && t.value === item.value)
    )
  }

  // 获取筛选项,赋默认值(先赋原始默认值,再赋本地保存的默认值)
  const getSalesStaffOptions = async function () {
    const res = await salesStaffOptions()
    if (res) {
      const { data } = res
      data.forEach(d => {
        const { key, list, tree } = d
        const isArr = Array.isArray(defaultFormData[key])
        state.options[key] = list.length ? list.map(k => {
          const { name: label, value, selected, ...other } = k
          if (selected) {
            if (isArr) {
              defaultFormData[key].push(value)
            } else {
              defaultFormData[key] = value
            }
          }
          return {
            label,
            value,
            ...other
          }
        }) : tree
      })
      // 赋原始默认值
      state.formData = cloneDeep(defaultFormData)
      // 赋保存默认值
      setLocalFormData()
      // 手动触发商品品牌获取焦点事件,解决默认值反显问题
      nextTick(() => {
        selectFocusOrBlur()
      })
    }
  }

  watch(() => state.formData, val => {
    const { times, areaIds, keyWord, keyWordType, ...other } = state.formData
    localStorage.setItem('salesPersonFormData', JSON.stringify(other))
  }, { deep: true })

  const setLocalFormData = function () {
    const salesPersonFormData = localStorage.getItem('salesPersonFormData')
    if (!salesPersonFormData) return
    const o = JSON.parse(salesPersonFormData)
    // 5月报表盘点优化方案：优化后没有全部:0的选项了
    o.orderStatus = o.orderStatus === 0 ? undefined : o.orderStatus
    delete o.eliminateFreebie
    Object.assign(state.formData, o)
  }

  getSalesStaffOptions()

  const reset = function () {
    state.formData = cloneDeep(defaultFormData)
  }

  const changeType = function () {
    state.formData.keyWord = undefined
    keyWord.value = undefined
  }

  const loadProduct = ref(false)
  const keyWord = ref('')
  const productOptions = ref([])
  const getProductPid = async function (text) {
    productOptions.value = []
    if (text) {
      keyWord.value = text
      loadProduct.value = true
      const params = {
        world: text,
        limit: 20
      }
      const res = await productPid(params)
      loadProduct.value = false
      if (res) {
        productOptions.value = res.data
      }
    }
  }

  const productPidChange = function () {
    keyWord.value = ''
  }

  const productPidBlur = function (e) {
    setTimeout(() => {
      if (!state.formData.keyWord && keyWord.value) {
        state.formData.keyWord = keyWord.value
        keyWord.value = ''
      }
    }, 1)
  }

  const timesChange = () => {
    state.formData.times[0] = moment(state.formData.times[0]).format('YYYY-MM-DD 00:00:00')
    state.formData.times[1] = moment(state.formData.times[1]).format('YYYY-MM-DD 23:59:59')
  }
  const batchExportAllChecked = (e) => {
    allChecked.value = e.target.checked
    if (allChecked.value) {
      state.formData.productBrand = filterOptions.value.map(item => item.value)
    } else {
      state.formData.productBrand = []
    }
  }
  return {
    productCategorySelect,
    selectOptions,
    selectFocusOrBlur,
    selectSearch,
    handleSelect,
    selectPopupScroll,
    reset,
    changeType,
    getProductPid,
    loadProduct,
    productPidChange,
    productPidBlur,
    productOptions,
    timesChange,
    batchExportAllChecked
  }
}
