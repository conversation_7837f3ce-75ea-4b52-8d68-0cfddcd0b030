import { ref, h } from 'vue'
import { useState } from './use-state'
import { useApi } from './use-api'
import ToDetail from '../components/to-detail.vue'
import ContrastData from '../components/contrast-data.vue'
import EditNote from '../components/edit-note.vue'
import DisplayNote from '../components/display-note.vue'
import { Icon, message } from 'ant-design-vue'

export default function useTable () {
  const { salesStaffHeader, bussinessReportExport } = useApi()
  const { state, hasBbzs, checkForm, getQueryParams, fetchData, setTableData } = useState()

  const columns = ref([])

  const getChineseReg = /[\u4e00-\u9fa5]/g

  const tableChange = function (paginationObj, filters, sorter) {
    const { sortField: oldSortField, sortBy: oldSortBy } = state.sorter
    state.sorter = sorter.order ? { sortField: sorter.field, sortBy: sorter.order === 'ascend' ? 'asc' : 'desc' } : {}
    state.pagination = {
      ...paginationObj
    }
    const { sortField, sortBy } = state.sorter
    if (sortField !== oldSortField || sortBy !== oldSortBy) {
      fetchData()
    } else {
      setTableData()
    }
  }

  const eachData = function (arr, sorter) {
    return arr.map(d => {
      const {
        key: dataIndex,
        name: titleSetting,
        children,
        defaultItem,
        description,
        hasDetail,
      } = d
      const o = {
        dataIndex,
        titleSetting,
        hide: defaultItem === false,
        description,
        isEdit: false,
        customRender:
        hasDetail
          ? (text, row) => (
            row.area !== '合计' ? <ContrastData data-index={dataIndex} row={row}>
              <ToDetail data-index={dataIndex} row={row} />
            </ContrastData> : <ContrastData data-index={dataIndex} row={row} />
          )
          : (text, row) => <ContrastData data-index={dataIndex} row={row} />,
        title: () => (
          <span>
            {titleSetting}
            {hasBbzs.value ? (
              <EditNote
                field={dataIndex}
                description={description}
                is-edit={o.isEdit}
                onFetchHearder={fetchHearder}
                {...{
                  on: {
                    'update:isEdit': (val) => {
                      o.isEdit = val
                    },
                  },
                }}
              />
            ) : (
              <DisplayNote description={description} />
            )}
          </span>
        ),
      }
      const chineseStr = titleSetting.match(getChineseReg).join('')
      const otherStr = titleSetting.replace(getChineseReg, '')
      const chineseStrWidth = chineseStr.length * 14
      const otherStrWidth = otherStr.length * 7
      o.width = chineseStrWidth + otherStrWidth + 70
      if (children && children.length) {
        o.children = eachData(children, true)
      }
      if (sorter) o.sorter = true
      return o
    })
  }

  const fetchHearder = async function () {
    const res = await salesStaffHeader()
    const { data } = res
    columns.value = eachData(data)
  }

  fetchHearder()

  const columnsSetting = (checkedColumns) => {
    const columnsKeys = columns.value.filter(d => d.children && d.children.length).map(d => d.dataIndex)
    // 统计项去除一级表头
    state.items = checkedColumns.filter(d => !columnsKeys.includes(d))
  }

  const exportDataLoading = ref(false)
  const exportData = async function () {
    if (checkForm()) return
    const params = getQueryParams()
    exportDataLoading.value = true
    const content = await bussinessReportExport(params, 1, { scene: 'race_performance_statistics' })
    exportDataLoading.value = false
    const date = new Date()
    let linkNode = document.createElement('a')
    linkNode.download = `销售人员业绩统计${date.getFullYear()}年${date.getMonth() +
      1}月${date.getDate()}日${date.getHours()}时${date.getMinutes()}分${date.getSeconds()}秒.xlsx`
    linkNode.style.display = 'none'
    let blob = new Blob([content.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    linkNode.href = URL.createObjectURL(blob)
    document.body.appendChild(linkNode)
    linkNode.click()
    document.body.removeChild(linkNode)
  }

  const toolSwitchChange = function () {
    fetchData()
  }

  return {
    columns,
    exportData,
    exportDataLoading,
    tableChange,
    columnsSetting,
    toolSwitchChange
  }
}
