<script lang="jsx">
  import { defineComponent, getCurrentInstance } from 'vue'
  import { useState } from '../hooks/use-state'
  export default defineComponent({
    props: {
      dataIndex: {
        type: String,
        default: ''
      },
      row: {
        type: Object,
        default: () => ({})
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const { $tnt } = proxy
      const { getBaseParams } = useState()

      const toDetail = function () {
        const query = {
          item: props.dataIndex,
          ...getBaseParams(),
          staffId: props.row.staffId,
          title: '销售人员业绩',
          isNew: true
        }
        if ($tnt.xtenant >= 1000) {
          delete query.gift
        } else {
          delete query.eliminateFreebie
        }
        const routeData = proxy.$router.resolve({
          path: '/operation/statistics-report/bussiness-report/perfect-order',
          query: query
        })
        return routeData.href
      }

      return {
        toDetail
      }
    },
    render () {
      const { dataIndex, row, toDetail } = this
      return <a class="to-detail" href={toDetail()} target="_blank">{row[dataIndex]}</a>
    }
  })
</script>
<style lang="scss" scoped>
.to-detail{
  color: #20a0ff;
  &:hover{
    text-decoration: underline;
    cursor: pointer;
  }
}
</style>
