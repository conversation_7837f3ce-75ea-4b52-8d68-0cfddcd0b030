<script lang="jsx">
  import { defineComponent, toRefs, ref } from 'vue'
  import { useState } from '../hooks/use-state'
  import useSearch from '../hooks/use-search'
  import { Ni<PERSON>ilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import { DatePicker, TreeSelect, Select, Tooltip, Spin, Input } from 'ant-design-vue'
  import CityStore from '@logistics/components/city-store'
  import { keyWordType, ranges } from '../constants'
  import SelectCheckAll from '../../bussiness-report/components/selectCheckAll.vue'

  export default defineComponent({
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect,
      CityStore,
      SelectCheckAll
    },
    setup () {
      const batchExportContent = ref(null)
      const { state, loading, fetchData, hasZscb, allChecked } = useState()
      const {
        productCategorySelect,
        selectOptions,
        handleSelect,
        selectSearch,
        selectPopupScroll,
        reset,
        selectFocusOrBlur,
        changeType,
        getProductPid,
        loadProduct,
        productPidChange,
        productPidBlur,
        productOptions,
        timesChange,
        batchExportAllChecked
      } = useSearch()

      const getPopupContainer = () => {
        return () => batchExportContent.value
      }

      return {
        allChecked,
        batchExportContent,
        ...toRefs(state),
        loading,
        fetchData,
        productCategorySelect,
        selectOptions,
        handleSelect,
        selectSearch,
        selectPopupScroll,
        hasZscb,
        reset,
        selectFocusOrBlur,
        changeType,
        getProductPid,
        loadProduct,
        productPidChange,
        productPidBlur,
        productOptions,
        timesChange,
        getPopupContainer,
        batchExportAllChecked
      }
    },
    render () {
      const {
        allChecked,
        loading,
        fetchData,
        formData,
        options: {
          orderStatus,
          orderType,
          virtualGoods,
          costType,
          storeLevel,
          storeType,
          productCategory,
          largePrice,
          statisticsDimension,
          staffCategory,
          recycledWool,
          eliminateFreebie
        },
        productCategorySelect,
        selectOptions,
        handleSelect,
        selectSearch,
        selectPopupScroll,
        reset,
        hasZscb,
        selectFocusOrBlur,
        changeType,
        getProductPid,
        loadProduct,
        productPidChange,
        productPidBlur,
        productOptions,
        timesChange,
        getPopupContainer,
        batchExportAllChecked
      } = this
      return (
        <div ref="niFilter">
      <NiFilter
        form={formData}
        loading={loading}
        onFilter={() => {
          fetchData(1)
        }}
        onReset={reset}
        do-filter-when-reset={false}
        label-width={100}
        immediate={false}
      >
        <ni-filter-item label="地区">
          <NiAreaSelect
            v-model={formData.areaIds}
            allow-clear={true}
            multiple
            mode={2}
            show-search
            ranks={['2c5']}
            placeholder="请选择地区"
            class="area-selector"
            max-tag-count={1}
          />
        </ni-filter-item>
        <ni-filter-item label="交易时间">
          <DatePicker.RangePicker
            v-model={formData.times}
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            ranges={ranges}
            onChange={timesChange}
            show-time
          />
        </ni-filter-item>
        <ni-filter-item label="业务类型">
          <Tooltip>
            <template slot="title">只支持查询新机和优品的退款订单数据</template>
            <Select
              v-model={formData.orderStatus}
              placeholder="请选择业务类型"
              allowClear
              options={orderStatus || []}
            />
          </Tooltip>
        </ni-filter-item>
        <ni-filter-item label="订单类型">
          {/*
          <Select
            v-model={formData.orderType}
            allow-clear={true}
            mode="multiple"
            max-tag-count={1}
            placeholder="请选择订单类型"
            options={orderType || []}
          ></Select>
          */}
          <SelectCheckAll style="width: 100% !important" v-model={formData.orderType} maxTagCount={1} options={orderType || []}/>
        </ni-filter-item>
        <ni-filter-item label="统计维度">
          <Select
            v-model={formData.statisticsDimension}
            placeholder="请选择是否包含虚拟商品"
            options={statisticsDimension || []}
          />
        </ni-filter-item>
        <ni-filter-item label="门店级别">
          <Select
            v-model={formData.storeLevel}
            placeholder="请选择门店级别"
            options={storeLevel || []}
            allow-clear={true}
          />
        </ni-filter-item>
        <ni-filter-item label="门店类别">
          <Select
            v-model={formData.storeType}
            placeholder="请选择门店类别"
            options={storeType || []}
            mode="multiple"
            max-tag-count={1}
            allow-clear={true}
          />
        </ni-filter-item>
        <ni-filter-item label="区域门店">
          <CityStore
            ref="web"
            authority={true}
            use-rank={['2c5']}
            allow-clear
            value={formData.areaStoreIds}
            placeholder="请选择区域门店"
            onChange={(firstLevel, lastLevel, idList) => {
              formData.areaStoreIds = idList || []
            }}
          />
        </ni-filter-item>
        <ni-filter-item label="包含虚拟商品">
          <Select
            v-model={formData.virtualGoods}
            placeholder="请选择是否包含虚拟商品"
            options={virtualGoods || []}
          />
        </ni-filter-item>
        <ni-filter-item label="商品分类">
          <TreeSelect
            show-arrow
            show-search
            tree-node-filter-prop="title"
            tree-checkable
            class={{
              'more-select':
                formData.productCategory &&
                formData.productCategory.length &&
                formData.productCategory.length > 1,
            }}
            allow-clear
            dropdown-style={{ maxHeight: '300px' }}
            placeholder="请选择商品分类"
            get-popup-container={(triggerNode) => triggerNode.parentNode}
            max-tag-count={1}
            tree-data={productCategory}
            value={formData.productCategory}
            onChange={(val) => {
              productCategorySelect(val)
            }}
          />
        </ni-filter-item>
        <ni-filter-item label="商品品牌">
          <div ref="batchExportContent" class="batchExport-content">
            <Select
              placeholder="请选择商品品牌"
              get-popup-container={(triggerNode) => triggerNode.parentNode}
              show-arrow
              max-tag-count={0}
              mode="multiple"
              v-model={formData.productBrand}
              option-filter-prop="label"
              onFocus={selectFocusOrBlur}
              onBlur={selectFocusOrBlur}
              onSelect={handleSelect}
              onSearch={selectSearch}
              onPopupScroll={selectPopupScroll}
              allow-clear
              style={{ width: '100% !important' }}
              filterOption={false}
              getPopupContainer={getPopupContainer()}
              scopedSlots={{
                dropdownRender: (menu) => {
                  return <div>
                    <div style={{ marginLeft: '12px' }}>
                      <a-checkbox v-model={ allChecked } onChange={(e) => batchExportAllChecked(e)}>
                        <span style={{ marginLeft: '-4px' }}>全选</span>
                      </a-checkbox>
                    </div>
                    <a-divider style={{ margin: '4px 0' }} />
                    {menu}
                  </div>
                }
              }}
            >
              {
                selectOptions && selectOptions.length > 0 && selectOptions.map((item, index) => {
                  return <a-select-option value={item.value} label={item.label} key={index}>
                    <label class="ant-checkbox-wrapper">
                    <span class={`ant-checkbox ${formData.productBrand.includes(item.value) ? 'ant-checkbox-checked' : ''}`} style={{ marginRight: '2px' }}>
                    <input type="checkbox" class="ant-checkbox-input" value=""/>
                    <span class="ant-checkbox-inner"></span>
                  </span>
                    </label>
                    {item.label}
                  </a-select-option>
                })
              }
            </Select>
          </div>
        </ni-filter-item>
        <ni-filter-item class="no-label">
          <Input.Group compact style="width: 380px">
            <Select
              onChange={changeType}
              style="width:38%"
              v-model={formData.keyWordType}
              options={keyWordType}
            />
            {formData.keyWordType === 'productName' ? (
              <Select
                ref="product"
                style="width: 62%"
                onSearch={getProductPid}
                onChange={productPidChange}
                onBlur={productPidBlur}
                v-model={formData.keyWord}
                placeholder="请输入"
                allowClear
                filter-option={false}
                default-active-first-option={false}
                showArrow={false}
                showSearch={true}
                dropdownMatchSelectWidth={false}
                getPopupContainer={triggerNode => triggerNode.parentNode}
              >
                {productOptions.map(item => (
                  <Select.Option key={item.pid}>
                    {item.productName}
                  </Select.Option>
                ))}
                <template slot="notFoundContent">
                  {loadProduct ? (
                    <div
                      class="full-width flex flex-center"
                      style="height: 60px"
                    >
                      <Spin />
                    </div>
                  ) : null}
                </template>
              </Select>
            ) : (
              <Input
                style="width: 62%"
                allowClear
                v-model={formData.keyWord}
                placeholder="请输入"
              />
            )}
          </Input.Group>
        </ni-filter-item>
        {
          this.$tnt.xtenant >= 1000 ? <ni-filter-item label="剔除赠品">
            <a-select v-model={formData.eliminateFreebie} options={eliminateFreebie} allow-clear></a-select>
          </ni-filter-item> : null
        }
        <ni-filter-item label="员工类别">
          <Select
            v-model={formData.staffCategory}
            placeholder="请选择员工类别"
            options={staffCategory || []}
            allowClear
          />
        </ni-filter-item>
        {
          this.$tnt.xtenant === 0 ? <ni-filter-item label="大件价位">
          <Select
            v-model={formData.largePrice}
            placeholder="请选择大件价位"
            options={largePrice || []}
            allow-clear
          />
        </ni-filter-item> : null
        }
        {costType.length && hasZscb ? (
          <ni-filter-item label="成本类型">
            <Select
              v-model={formData.costType}
              placeholder="请选择成本类型"
              options={costType || []}
            />
          </ni-filter-item>
        ) : null}
        {
          this.$tnt.xtenant >= 1000 ? <ni-filter-item label="回收单毛">
          <Select
            v-model={formData.recycledWool}
            placeholder="请选择回收单毛"
            options={recycledWool || []}
          />
        </ni-filter-item> : null
        }
      </NiFilter>
      </div>
      )
    },
  })
</script>
<style lang="scss" scoped>
:deep(.no-label){
  > .label{
    display: none;
  }
}
.more-select {
  :deep(.ant-select-selection__choice) {
    max-width: 45%;
  }
}
.batchExport-content {
  :deep(.ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-selected .ant-select-selected-icon, .ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-selected:hover .ant-select-selected-icon) {
    display: none;
  }
  :deep(.ant-select-dropdown-menu-item) {
    i {
      display: none;
    }
  }
}
</style>
