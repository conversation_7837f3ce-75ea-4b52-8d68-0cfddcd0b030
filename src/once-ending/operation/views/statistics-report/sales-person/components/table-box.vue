<script lang="jsx">
  import { defineComponent, toRefs } from 'vue'
  import { useState } from '../hooks/use-state'
  import useTable from '../hooks/use-table'
  import { Switch, Button, Tooltip, Icon } from 'ant-design-vue'
  import { NiTable } from '@jiuji/nine-ui'
  import NoData from './no-data.vue'

  export default defineComponent({
    components: {
      NiTable,
      NoData,
    },
    setup () {
      const { loading, isFeatch, state } = useState()
      const {
        exportData,
        exportDataLoading,
        columns,
        columnsSetting,
        tableChange,
        toolSwitchChange
      } = useTable()

      return {
        loading,
        ...toRefs(state),
        exportData,
        exportDataLoading,
        isFeatch,
        columns,
        columnsSetting,
        tableChange,
        toolSwitchChange
      }
    },
    render () {
      const {
        dataSource,
        columns,
        loading,
        isFeatch,
        formData,
        exportData,
        exportDataLoading,
        tableChange,
        columnsSetting,
        pagination,
        toolSwitchChange
      } = this
      return (
      <NiTable
        class="mt-20"
        locale={{ emptyText: <NoData is-featch={isFeatch} /> }}
        dataSource={dataSource}
        columns={columns}
        loading={loading}
        onChange={tableChange}
        pagination={pagination}
        footerTotalNum={1}
        onColumnsSetting={columnsSetting}
      >
        <div slot="tool">
          <div class="tool flex flex-align-center">
            <div class="mr-16">
              <span>剔除大客户：</span>
              <Switch v-model={formData.largeCustomers} onChange={toolSwitchChange}/>
            </div>
            <div class="mr-16">
              <span>剔除滞销/清仓商品：</span>
              <Switch v-model={formData.stalledClearance} onChange={toolSwitchChange}/>
            </div>
            {
              this.$tnt.xtenant < 1000 ? <div class="mr-16">
                <span>剔除赠品：</span>
                <Switch v-model={formData.gift} onChange={toolSwitchChange}/>
              </div> : null
            }
            <div class="mr-16">
              <span>剔除纯积分订单：</span>
              <Switch v-model={formData.purePointsOrder} onChange={toolSwitchChange}/>
            </div>
            <div class="mr-16">
              <span>
                剔除优惠码
                <Tooltip>
                  <template slot="title">不参与利润核算</template>
                  <Icon style="width:20px" type="question-circle" />
                </Tooltip>
                ：
              </span>
              <Switch v-model={formData.couponCodeOrder} onChange={toolSwitchChange}/>
            </div>
            {
              this.$tnt.xtenant >= 1000 ? <div class="mr-16">
                <span>成本剔除价保：</span>
                <Switch v-model={formData.excludePriceProtectFanli} onChange={toolSwitchChange}/>
              </div> : null
            }
          </div>
        </div>
        <div slot="action">
          <Button loading={exportDataLoading} onClick={exportData}>
            {exportDataLoading ? '导出中' : '导出'}
          </Button>
        </div>
      </NiTable>
      )
    },
  })
</script>
