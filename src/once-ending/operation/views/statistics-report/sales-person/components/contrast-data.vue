<script lang="jsx">
  import { defineComponent } from 'vue'

  export default defineComponent({
    props: {
      dataIndex: {
        type: String,
        default: ''
      },
      row: {
        type: Object,
        default: () => ({})
      }
    },
    render () {
      const { dataIndex, row } = this
      return <span>{this.$slots.default ? this.$slots.default : row[dataIndex]}</span>
    }
  })
</script>
