<script lang="jsx">
  import { defineComponent, computed, watch, ref } from 'vue'
  import { Popover, Icon, Input, Button, message } from 'ant-design-vue'
  import { useApi } from '../hooks/use-api'

  export default defineComponent({
    props: {
      description: {
        type: String,
        default: '',
      },
      isEdit: {
        type: Boolean,
        default: false,
      },
      field: {
        type: String,
        default: '',
      },
      type: {
        type: Number,
        default: 106,
      },
    },
    setup (props, { emit }) {
      const { editNote } = useApi()

      const localDescription = ref('')

      watch(
        () => props.description,
        (val) => {
          localDescription.value = val
        },
        { immediate: true }
      )

      const edit = computed({
        get: () => props.isEdit,
        set: (val) => emit('update:isEdit', val),
      })

      const cancelNote = function () {
        localDescription.value = props.description
        edit.value = false
      }

      const handleEditNote = async function () {
        const { type, field } = props
        const params = {
          type,
          field,
          note: localDescription.value,
        }
        const res = await editNote(params)
        if (res) {
          message.success('修改成功')
          edit.value = false
          emit('fetchHearder')
        }
      }

      return {
        edit,
        cancelNote,
        localDescription,
        handleEditNote,
      }
    },
    render () {
      const { localDescription, edit, cancelNote, handleEditNote } = this
      return (
      <Popover overlay-class-name="title-popover">
        <div slot="content">
          {!edit && (
            <p style="color:#333333;font-size:14px;max-width:200px">
              {localDescription || '未添加注释'}
            </p>
          )}
          {edit && (
            <Input.TextArea
              auto-size
              style="border-radius: 2px;"
              value={localDescription}
              onInput={(e) => {
                this.localDescription = e.target.value
              }}
              placeholder="请输入注释"
              allow-clear
            />
          )}
          {edit && (
            <div style="text-align: right;margin-top: 13px;">
              <Button size="small" onClick={cancelNote}>
                取消
              </Button>
              <Button
                size="small"
                class="ml-8"
                type="primary"
                onClick={handleEditNote}
              >
                确定
              </Button>
            </div>
          )}
          {!edit && (
            <div style="text-align: right;margin-top: 13px;">
              <Button
                size="small"
                type="primary"
                onClick={() => {
                  this.edit = true
                }}
              >
                编辑
              </Button>
            </div>
          )}
        </div>
        <span>
          <Icon
            style="color:rgba(0, 0, 0, 0.85);margin-left: 8px;"
            type="question-circle"
          />
        </span>
      </Popover>
      )
    },
  })
</script>
