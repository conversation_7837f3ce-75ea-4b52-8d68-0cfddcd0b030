<template>
  <ni-table
    class="statistics-report-table"
    :locale="local"
    :dataSource="dataSource"
    :columns="columns"
    :loading="loading"
    :footerTotalNum="1"
  >
    <div slot="tool">
      <div class="tool">
        <transition>
          <div class="flex flex-align-center">
            <div class="tab pointer"
                 @click="changeTab(tab.value)"
                 :class="checkTab === tab.value ? 'select' : 'default'"
                 v-for="tab in constants.tabs" :key="tab.value">
              {{ tab.label }}
            </div>
          </div>
        </transition>
        <div class="flex flex-justify-center flex-align-center mr-8">
          <div class="flex flex-justify-center flex-align-center mr-8" v-if="$tnt.xtenant < 1000">
            <span class="full-width text-right">超期库龄：</span>
            <a-input
              addon-after="天"
              class="mr-8"
              v-model="overdueStockAge"
              @input="overdueStockAge = overdueStockAge.replace(/\D/g, '').replace(/^0{1,}/g, '')"
              allowClear
            />
            <a-button type="primary" @click="addOverdueStockAge">保存</a-button>
          </div>
          <a-button
            icon="export"
            :loading="exportDataLoading"
            @click="exportDataAll"
          >导出</a-button>
        </div>
      </div>
    </div>
  </ni-table>
</template>
<script type="text/jsx" lang="jsx">
  import { NiTable } from '@jiuji/nine-ui'
  import * as constants from '../constants'
  import useExportXlsx from '../../hooks/useExportXlsx'
  import { h, ref, computed, inject, getCurrentInstance } from 'vue'
  import NoData from '../../value-added-analysis/list/components/table-box/no-data.vue'
  import brandSaleApi from '../../../../api/statistics-report'
  import usesearchProps from '../../hooks/usesearchProps'
  import moment from 'moment'
  import { getExportItems } from '@operation/util/common'
  export default {
    components: {
      NiTable,
      NoData
    },
    props: {
      searchData: {
        type: Object,
        default: () => ({})
      },
      dataSource: {
        type: Array,
        default: () => []
      },
      columns: {
        type: Array,
        default: () => []
      },
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const { $message, $tnt } = proxy
      const fetchList = inject('fetchList')
      const isFeatch = inject('isFeatch')
      const loading = inject('loading')
      const checkTab = ref('1')
      const overdueStockAge = ref(undefined)
      const cacheParams = inject('cacheParams')
      const { exportDataLoading, exportData } = useExportXlsx({ proxy, type: 12 })
      // 获取枚举
      const { searchOptions, getSearchOptions } = usesearchProps({ proxy, type: 12 })
      function changeTab (value) {
        if (checkTab.value !== value) {
          checkTab.value = value
          proxy.$emit('changeTab', value)
        }
      }
      const getOverdueStockAge = async () => {
        await getSearchOptions()
        overdueStockAge.value = searchOptions?.value?.overNStockAge?.value
      }
      if ($tnt.xtenant < 1000) {
        getOverdueStockAge()
      }
      function exportDataAll () {
        const exportItems = getExportItems(proxy.$route.path, '', props.columns)
        if (!exportItems?.length) {
          return false
        }
        const param = {
          params: {
            ...cacheParams.value,
            exportItems,
            scene: checkTab.value === '1' ? 'big_brand_report' : 'big_brand_report_price',
            newFileName: `${checkTab.value === '1' ? '销售统计-' : '价位占比-'}${moment().format('YYYYMMDDHHmmss')}.xlsx`
          }
        }
        exportData(param)
      }
      const addOverdueStockAge = () => {
        if (overdueStockAge.value) {
          let params = {
            overNStockAge: overdueStockAge.value
          }
          brandSaleApi.changeOverNStockAge(params).then(async (res) => {
            if (res.code === 0) {
              $message.success('修改成功', 1, () => {
                proxy.$router.go(0)
              })
            } else {
              $message.error(res.userMsg)
            }
          })
        } else {
          $message.warn('请填写超期库龄')
        }
      }
      return {
        overdueStockAge,
        isFeatch,
        loading,
        checkTab,
        exportDataLoading,
        changeTab,
        exportDataAll,
        addOverdueStockAge,
        fetchList
      }
    },
    data () {
      return {
        constants,
        local: {}
      }
    },
    created () {
      this.local = { emptyText: <NoData onfetchDataHandle={() => this.fetchList()}/> }
    }
  }
</script>
<style lang="scss">
.statistics-report-table{
  .no-data-img {
    height: 140px;
    width: 140px;
  }
  .no-data-text {
    width: 160px;
    height: 16px;
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.35);
    line-height: 16px;
    margin-top: 24px;
    text-align: center;
    width: 100%;
  }
  .no-query {
    font-size: 16px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.35);
    line-height: 32px;
    margin-top: 16px;
    text-align: center;
    width: 100%;
  }
  .tool{
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .tab {
    padding: 10px 0;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    margin-right: 32px;
  }
  .default {
    color: rgba(0, 0, 0, 0.65);
    border-bottom: 2px solid #FFFFFF;
  }
  .select {
    color: #1890FF;
    border-bottom: 2px solid #1890FF;
  }
}

</style>
