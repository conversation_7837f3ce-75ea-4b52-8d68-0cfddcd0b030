<template>
  <ni-filter :form="searchData" :loading="loading" :label-width="85" @filter="doSearch" :immediate="false" :unfoldCount="6">
    <ni-filter-item label="地区">
      <ni-area-select
        v-model="searchData.departAreaIds"
        :allow-clear="true"
        multiple
        :mode="2"
        show-search
        :ranks="['2c5']"
        placeholder="请选择地区"
        class="area-selector"
        :max-tag-count="1"
      />
    </ni-filter-item>

    <ni-filter-item label="时间节点">
      <a-input-group class="flex" compact>
        <a-select v-model="searchData.timeType"
                  :get-popup-container="getPopupContainer"
                  :options="$tnt.xtenant == 0 ? constants.timeType : constants.outTimeType"
                  style="width: 100px" showArrow/>
        <a-range-picker v-model="searchData.timeRange"
                        :get-calendar-container="getPopupContainer"
                        style="width: calc(100% - 100px)"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        show-time
                        :allow-clear="false"
                        @change="timeRangeChange"
                        :ranges="constants.ranges"/>
      </a-input-group>
    </ni-filter-item>

    <ni-filter-item label="分类">
      <a-select
        :options="getCategory(searchOptions.categoryIds)"
        :maxTagCount="1"
        allowClear
        mode="multiple"
        show-search
        optionFilterProp="children"
        placeholder="请选择分类"
        v-model="searchData.categoryIds"
      />
    </ni-filter-item>

    <ni-filter-item label="对比时间">
      <a-range-picker v-model="searchData.chainTimeRange"
                      :get-calendar-container="getPopupContainer"
                      format="YYYY-MM-DD HH:mm:ss"
                      :allow-clear="false"
                      :ranges="constants.ranges"
                      show-time
                      value-format="YYYY-MM-DD HH:mm:ss"/>
    </ni-filter-item>

    <ni-filter-item label="门店类别">
      <a-select :options="searchOptions.areaType ? searchOptions.areaType.list : []"
                placeholder="请选择门店类别"
                allowClear
                :mode="$tnt.xtenant < 1000 ? 'multiple':'default'"
                v-model="searchData.storeTypes"/>
    </ni-filter-item>

    <ni-filter-item label="品牌">
      <a-select
        :options="brandOptions"
        :maxTagCount="1"
        allowClear
        mode="multiple"
        optionFilterProp="children"
        placeholder="请选择品牌"
        v-model="searchData.brandIds"
      />
    </ni-filter-item>

    <ni-filter-item label="门店级别">
      <a-select :options="searchOptions.areaLevel ? searchOptions.areaLevel.list : []"
                placeholder="请选择门店级别"
                allowClear
                v-model="searchData.areaLevel"/>
    </ni-filter-item>

    <ni-filter-item label="区域门店">
      <city-store
        ref="web"
        :authority="true"
        :useRank="['2c5']"
        allowClear
        :value="searchData.regionAreaIds"
        placeholder="请选择门店"
        @change="administrativeAreaChange"/>
    </ni-filter-item>

    <ni-filter-item label="分销选项">
      <a-select :options="searchOptions.distributionStore ? searchOptions.distributionStore.list : []"
                placeholder="请选择分销选项"
                v-model="searchData.distributionStore"
                :maxTagCount="1"
                multiple
                allowClear/>
    </ni-filter-item>

    <ni-filter-item label="门店属性">
      <a-tree-select
        v-model="searchData.areaAttributeIds"
        tree-checkable
        :maxTagCount="1"
        allowClear
        placeholder="请选择门店属性"
        treeNodeFilterProp="title"
        :get-popup-container="getPopupContainer"
        :treeData="searchOptions.areaAttribute ? searchOptions.areaAttribute.tree : []"/>
    </ni-filter-item>

    <ni-filter-item label="订单类型">
       <a-select
        showArrow
        allowClear
        placeholder="请选择订单类型"
        mode="multiple"
        :maxTagCount="1"
        v-model="searchData.subType"
        :options="searchOptions.subType ? searchOptions.subType.list : []"
        optionFilterProp="children"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
    </ni-filter-item>
  </ni-filter>
</template>

<script>
  import { NiFilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import * as constants from '../constants'
  import { TreeSelect } from 'ant-design-vue'
  import { inject, ref, watch, computed, getCurrentInstance } from 'vue'
  import cityStore from '@logistics/components/city-store'
  import { BRAND_OPTIONS } from '@operation/store/modules/statistics-report/action-types'
  import moment from 'moment/moment'
  const SHOW_PARENT = TreeSelect.SHOW_PARENT
  export default {
    name: 'search-box',
    components: { NiFilter, NiFilterItem, NiAreaSelect, cityStore },
    props: {
      searchData: {
        type: Object,
        default: () => {
          return {
            categoryIds: []
          }
        }
      }
    },
    data () {
      return {
        constants,
        SHOW_PARENT,
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const brandOptions = ref([])
      const searchOptions = inject('searchOptions')
      const loading = inject('loading')
      function getPopupContainer () {
        return document.querySelector('.statistics-report')
      }
      const getBrandOptions = async (cids) => {
        if (cids.length === 0) return
        brandOptions.value = []
        const res = await proxy.$store.dispatch(`operation/statisticsReport/${BRAND_OPTIONS}`, { cids })
        if (res && res.data) {
          brandOptions.value = res.data.map(it => ({
            label: it.name,
            value: it.id
          }))
        }
      }
      const timeRangeChange = () => {
        props.searchData.timeRange[0] = moment(props.searchData.timeRange[0]).format('YYYY-MM-DD HH:mm:ss')
        props.searchData.timeRange[1] = moment(props.searchData.timeRange[1]).format('YYYY-MM-DD HH:mm:ss')
      }
      watch(() => props.searchData.categoryIds, (newVal) => {
        getBrandOptions(newVal)
      }, { immediate: true })
      function getOptions (key, type) {
        if (searchOptions[key]) {
          return searchOptions[key][type]
        } else {
          return []
        }
      }
      function doSearch () {
        proxy.$emit('search')
      }
      function administrativeAreaChange (firstLevel, lastLevel, idList) {
        props.searchData.regionAreaIds = idList || []
      }
      function getCategory (categoryIds) {
        if (categoryIds && categoryIds.tree) {
          return categoryIds.tree.map(it => ({
            label: it.title,
            value: it.value
          }))
        } else {
          return []
        }
      }
      return {
        searchOptions,
        loading,
        brandOptions,
        getCategory,
        getPopupContainer,
        doSearch,
        getOptions,
        administrativeAreaChange,
        timeRangeChange
      }
    }
  }
</script>

<style lang="scss" scoped>
:deep(.area-selector) {
  max-width: 100%;
  .ant-select-selection--multiple{
    max-height: 32px;
    .ant-select-selection__choice{
      max-width: calc(100% - 90px) !important;
    }
  }
}
</style>
