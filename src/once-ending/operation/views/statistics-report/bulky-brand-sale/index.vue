<template>
  <page class="statistics-report">
    <div slot="extra" v-if="lastUpdateDate">数据更新于{{ lastUpdateDate }}</div>
    <ni-list-page :push-filter-to-location="false">
      <search-box
        :search-data="searchData"
        @search="fetchList"
      ></search-box>
      <table-box
        style="margin-top:16px"
        :search-data="searchData"
        :data-source="dataSource"
        :columns="columns"
        @exportData="exportData"
        :export-data-loading="exportDataLoading"
        @changeTab="changeTab"
      ></table-box>
    </ni-list-page>
  </page>
</template>

<script type="text/jsx" lang="jsx">
  import searchBox from './components/search-box'
  import TableBox from './components/table-box'
  import moment from 'moment'
  import useCommon from '../hooks/useCommon'
  import { NiListPage, NiPrice } from '@jiuji/nine-ui'
  import { h, provide, ref, computed, getCurrentInstance, watch } from 'vue'
  import useReportHearder from '../hooks/useReportHearder'
  import usesearchProps from '../hooks/usesearchProps'
  import { QUERY_STAT_DATA } from '@operation/store/modules/statistics-report/action-types'
  import * as constants from './constants'
  import { mapState } from 'vuex'
  import debounce from 'lodash/debounce'
  import castArray from 'lodash/castArray'
  export default {
    name: 'index',
    components: { searchBox, TableBox, NiListPage, NiPrice },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const { loading, isFeatch } = useCommon()
      const lastUpdateDate = ref('')
      const xtenant = proxy.$tnt.xtenant
      const searchData = ref({
        timeType: xtenant === 0 ? 1 : 2,
        timeRange: [],
        chainTimeRange: [],
        departAreaIds: [],
        areaLevel: undefined,
        storeTypes: xtenant < 1000 ? [1] : 1,
        regionAreaIds: [],
        distributionStore: undefined,
        areaAttributeIds: [],
        categoryIds: ['2']
      })
      const date = new Date()
      searchData.value.timeRange = [
        moment(date).format('YYYY-MM-DD 00:00:00'),
        moment(date).format('YYYY-MM-DD 23:59:59'),
      ]
      if (xtenant < 1000) {
        searchData.value.chainTimeRange = [
          moment(date).subtract(1, 'd').format('YYYY-MM-DD 00:00:00'),
          moment(date).subtract(1, 'd').format('YYYY-MM-DD 23:59:59'),
        ]
      } else {
        searchData.value.chainTimeRange = [
          moment(date).subtract(1, 'months').format('YYYY-MM-DD 00:00:00'),
          moment(date).subtract(1, 'months').format('YYYY-MM-DD 23:59:59'),
        ]
      }
      const checkTab = ref('1')
      const dataSourceSale = ref([])
      const dataSourcePrice = ref([])
      const exportDataLoading = ref(false)
      const loadingDetail = ref(false)
      const detailContent = ref([])
      const cacheParams = ref({})
      provide('cacheParams', cacheParams)
      const contentIndex = ref(-1)
      const featchHeader = async function (type) {
        const cache = {
          type: 12,
        }
        const paramsSale = { ...cache, innerType: 1 }
        const paramsPrice = { ...cache, innerType: 2 }
        if (type === 'first') { // 初始获取两个表头
          await getHearderSale(paramsSale)
          await getHearderPrice(paramsPrice)
        } else { // 更改表头刷新对应表头
          checkTab.value === '1' ? await getHearderSale(paramsSale) : await getHearderPrice(paramsPrice)
        }
      }
      // hearderDataSale: 销售统计表头, getHearderSale: 获取销售统计表头
      const { hearderData: hearderDataSale, getHearder: getHearderSale } = useReportHearder({ proxy, h, type: 12, editNoteCallback: featchHeader })
      // hearderDataPrice: 销售统计表头, getHearderPrice: 获取销售统计表头
      const { hearderData: hearderDataPrice, getHearder: getHearderPrice } = useReportHearder({ proxy, h, type: 12, editNoteCallback: featchHeader })
      // 获取枚举
      const { searchOptions, getSearchOptions } = usesearchProps({ proxy, type: 12 })
      function exportData () {}
      const fetchList = async () => {
        const params = { ...searchData.value, type: 12 }
        const compareTimePass = constants.compareTimePass
        const timeRange = searchData.value.timeRange
        const chainTimeRange = searchData.value.chainTimeRange
        if (timeRange.length) {
          const text = searchData.value.timeType === 1 ? '销售' : xtenant === 0 ? '收款' : '交易'
          if (compareTimePass(timeRange, text)) {
            params.startTime = timeRange[0]
            params.endTime = timeRange[1]
          } else {
            return
          }
        }
        if (chainTimeRange.length) {
          if (compareTimePass(chainTimeRange, '环比')) {
            params.chainStartTime = chainTimeRange[0]
            params.chainEndTime = chainTimeRange[1]
          } else {
            return
          }
        }
        delete params.timeRange
        delete params.chainTimeRange
        cacheParams.value = { ...params }
        loading.value = true
        if (xtenant < 1000 && params.storeTypes.length < 1) {
          delete params.storeTypes
        }
        if (xtenant >= 1000 && params.storeTypes) {
          params.storeTypes = castArray(params.storeTypes)
        }
        const res = await proxy.$store.dispatch(`operation/statisticsReport/${QUERY_STAT_DATA}`, params)
        loading.value = false
        if (res) {
          isFeatch.value = true
          const data = res.data.data
          lastUpdateDate.value = data?.date
          dataSourceSale.value = data?.bigBrand?.list || []
          dataSourcePrice.value = data?.bigBrandPrice?.list || []
          const dataSourceSaleTotal = data?.bigBrand?.total || ''
          const dataSourcePriceTotal = data?.bigBrandPrice?.total || ''
          dataSourceSaleTotal && dataSourceSale.value.push({ ...dataSourceSaleTotal })
          dataSourcePriceTotal && dataSourcePrice.value.push({ ...dataSourcePriceTotal })
        }
      }
      provide('fetchList', fetchList)
      const dataSource = computed(() => {
        return checkTab.value === '1' ? dataSourceSale.value : dataSourcePrice.value
      })
      // 获取统计列表的头
      const commonStatisticHeader = async function () {
        // 查询默认表头
        featchHeader('first')
        await getSearchOptions()
        searchOptions.value.categoryIds.tree = searchOptions.value?.categoryIds?.tree.sort((a, b) => a.rank - b.rank) || []
      }
      commonStatisticHeader()
      function changeTab (value) { // 切换tab
        checkTab.value = value
      }
      function getNeedShowDetail (item) {
        const isJiuji = proxy.$tnt.xtenant === 0
        return item.key === 'saleCountRatio' || (!isJiuji && (item.key === 'salePrice' || item.key === 'salePriceRatio'))
      }
      let loadingContent = async (item, record, index) => {
        loadingDetail.value = true
        detailContent.value = []
        contentIndex.value = index
        const detailParams = {
          ...cacheParams.value,
          type: 13,
          startPrice: record.startPrice,
          endPrice: record.endPrice,
          brandId: record.brand ? [record.brand?.brandId] : undefined
        }
        if (item.key === 'saleCountRatio') {
          detailParams.innerType = checkTab.value === '1' ? 2 : 1
        } else if (item.key === 'salePriceRatio' || item.key === 'salePrice') {
          detailParams.innerType = 3
        }
        if (xtenant < 1000 && detailParams.storeTypes.length < 1) {
          delete detailParams.storeTypes
        }
        if (xtenant >= 1000 && detailParams.storeTypes) {
          detailParams.storeTypes = castArray(detailParams.storeTypes)
        }
        const res = await proxy.$store.dispatch(`operation/statisticsReport/${QUERY_STAT_DATA}`, detailParams)
        if (res) {
          if (contentIndex.value === index) { // 接口请求缓慢，保证数据展示的准确性
            detailContent.value = res.data?.data || []
            loadingDetail.value = false
          }
        }
      }
      loadingContent = debounce(loadingContent, 200)
      const setDetailContent = (item, record, index, canSeeDetail) => {
        const needShowDetail = getNeedShowDetail(item)
        if (!needShowDetail || !canSeeDetail) return
        loadingContent(item, record, index)
      }
      watch(() => searchData.value.timeRange, (newVal, oldVal) => {
        if (newVal && xtenant < 1000) {
          // 获取时间差
          const timeDiffer = moment(newVal[1]).diff(newVal[0])
          const data1 = moment(newVal[0]).subtract(timeDiffer + 1, 'ms')
          const data2 = moment(newVal[1]).subtract(timeDiffer + 1, 'ms')
          searchData.value.chainTimeRange = [moment(data1).format('YYYY-MM-DD 00:00:00'), moment(data2).format('YYYY-MM-DD 23:59:59')]
        }
      })
      return {
        searchData,
        dataSource,
        exportDataLoading,
        lastUpdateDate,
        hearderDataSale,
        hearderDataPrice,
        loadingDetail,
        detailContent,
        featchHeader,
        exportData,
        fetchList,
        changeTab,
        checkTab,
        setDetailContent,
        getNeedShowDetail,
      }
    },
    computed: {
      columns () {
        if (!this.hearderDataSale.length || !this.hearderDataPrice.length) return []
        this.addSlots(this.hearderDataSale)
        this.addSlots(this.hearderDataPrice)
        return this.checkTab === '1' ? this.hearderDataSale.filter(d => this.$tnt.xtenant >= 1000 || (this.$tnt.xtenant < 1000 && d.key !== 'comprehensiveGrossProfit')) : this.hearderDataPrice
      },
      ...mapState({
        hasRanktj1: state => state.userInfo.Rank.includes('tj1'),
        hasRank35: state => state.userInfo.Rank.includes('35')
      }),
      canSeeDetail () {
        return this.hasRanktj1 || this.hasRank35
      }
    },
    data () {
      return {
      }
    },
    methods: {
      showDetail (item, text) {
        // 品牌展示brandId
        if (item.key === 'brand') return text?.brandId
        let differentText = '环比数据'
        const differentKeys = ['grossProfit', 'singleGrossProfit', 'comprehensiveGrossProfit', 'mobileGrossProfit']
        differentKeys.includes(item.key) && (differentText = '环比')
        const countsTwo = !!(item.key === 'counts' && this.checkTab === '2')
        const countsOne = !!(item.key === 'counts' && this.checkTab === '1')
        const singleGrossProfitFlag = (item.key === 'singleGrossProfit' && this.checkTab === '1' && this.$tnt.xtenant < 1000)
        const singleGrossProfit2 = (item.key === 'singleGrossProfit2' && this.checkTab === '1' && this.$tnt.xtenant < 1000)
        const comprehensiveSingleGrossProfit = (item.key === 'comprehensiveSingleGrossProfit' && this.checkTab === '1' && this.$tnt.xtenant < 1000)
        countsTwo && (differentText = '相差')
        // normal不展示明细
        const normal = <span><span v-show={text?.chain}>对比时间数据：{text?.chain}</span>
          <span class="ml-16" v-show={(text?.different || text?.differentRatio) && !countsOne && !singleGrossProfitFlag && !singleGrossProfit2 && !comprehensiveSingleGrossProfit }>{differentText}：{!text ? '' : text.hasOwnProperty('different') ? text?.different : text?.differentRatio}</span>
          <span class="ml-16" v-show={text?.chainRate && countsTwo}>环比：{!text ? '' : text?.chainRate}</span>
          <span class="ml-16" v-show={(text?.different || text?.differentRatio) && singleGrossProfitFlag}>差额：{!text ? '' : text.hasOwnProperty('different') ? text?.different : text?.differentRatio}</span>
          <span class="ml-16" v-show={text?.valueDifferent && (singleGrossProfit2 || comprehensiveSingleGrossProfit)}>差额：{!text ? '' : text.valueDifferent}</span>
        </span>
        // 销量占比、销售额（仅输出）、销售额占比（仅输出）字段上时时显示其对比时期数据与当前时间的明细数据
        const { canSeeDetail } = this
        const needShowDetail = this.getNeedShowDetail(item)
        return (needShowDetail && canSeeDetail) ? <div class="details">
          <div class="flex weight">{ normal }</div>
          <div class="mt-16 weight">当前时间明细数据：</div>
          <div class="data-detail">
            { this.loadingDetail ? <div class="flex flex-center" style="height: 60px"><a-icon type="loading"/>
            </div> : <div class="detail-item">{ this.detailContent.map(it => <div class="flex mt-8 nowrap" style="line-height: 22px">
              [{ it.productName }]<span style="margin-left: 4px">{ it.saleCountRatio }</span>
            </div>) }</div> }
          </div>
        </div> : normal
      },
      addSlots (array) {
        if (!this.dataSource.length) return
        const { canSeeDetail } = this
        array.forEach((item, index) => {
          // const needShowDetail = this.getNeedShowDetail(item)
          let color = 'rgba(0, 0, 0, 0.65)'
          // if (item.key === 'counts') {
          //   color = '#1890ff'
          // } else if (needShowDetail && canSeeDetail) {
          //   color = '#ff0000'
          // }
          if (index !== 0) {
            // 排序处理
            item.sorter = (a, b) => {
              const hasPercent = item.key.indexOf('Ratio') > -1 || item.key.indexOf('Chain') > -1
              const hasSplic = item.key.indexOf('Splic') > -1
              if (typeof a[item.key] === 'object' || typeof b[item.key] === 'object') {
                if (hasPercent) {
                  const cacheA = (a[item.key]?.current || '').replace(/%/g, '')
                  const cacheB = (b[item.key]?.current || '').replace(/%/g, '')
                  return cacheA - cacheB
                }
                if (hasSplic) {
                  const splicA = a[item.key]?.sort ? (a[item.key]?.sort.toString() || '').replace(/%/g, '') : 0
                  const splicB = b[item.key]?.sort ? (b[item.key]?.sort.toString() || '').replace(/%/g, '') : 0
                  return Number(splicA) - Number(splicB)
                }
                return a[item.key]?.current - b[item.key]?.current
              } else if (hasPercent) {
                const cacheA = (a[item.key] || '').replace(/%/g, '')
                const cacheB = (b[item.key] || '').replace(/%/g, '')
                return cacheA - cacheB
              } else {
                return a[item.key] - b[item.key]
              }
            }
          }
          function ninePrice (value) { // 显示千分位金额
            const price = ['salePrice', 'grossProfit', 'singleGrossProfit', 'singleGrossProfit2', 'comprehensiveGrossProfit']
            if (price.includes(item.key)) {
              return <NiPrice decimalFontSize={ 14 }	 prefixColor={ color } color={ color } decimalColor={ color } value={ value } thousand={ true }/>
            } else {
              return value
            }
          }
          item.customRender = (text, record, index) => (!text && text !== 0) ? '-' : typeof text === 'object' ? <a-popover placement="bottom" getPopupContainer={() => document.querySelector('.statistics-report')}>
            <div slot="content" style="color: #333333">{ this.showDetail(item, text) }</div>
              <span><span style={{ color: color }} onMouseenter={ () => this.setDetailContent(item, record, index, canSeeDetail) }>
                { ninePrice((text?.brandName || text?.current)) }
              </span></span>
            </a-popover>
            : <span style={{ color: color }}>{ ninePrice(text) }</span>
        })
      }
    }
  }
</script>

<style lang="scss">
.title-popover {
  width: 172px;
}
a:visited {
  color: #d46b08 !important;
}
.statistics-report {
  position: relative;
  .ant-page-header-heading-extra {
    float: none;
    line-height: 40px;
    font-size: 12px;
    color: #9c9c9c;
  }
}
</style>
<style lang="less" scoped>
.details {
  min-width: 300px;
  .weight {
    font-weight: 500;
  }
  .data-detail {
    min-height: 100px;
  }
}
.ml-15 {
  margin-left: 15px;
}
.mt-13 {
  margin-top: 13px;
}
.ml-8 {
  margin-left: 8px;
}
.text-align-right {
  text-align: right;
}
.thead-cell {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
  padding: 4px;
}
td,
th {
  padding: 8px;
}

.table-box {
  position: relative;
  left: 0;
  top: 0;
  overflow: hidden;
}
.body-box {
  overflow: auto;
}
.header-box,
.footer-box {
  /*overflow: hidden;*/
  margin-right: 10px;
}
table {
  display: table;
  /*width: 100%;*/
  text-align: center;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  font-feature-settings: "tnum";
  border-radius: 4px 4px 0 0;
  border-spacing: 0;
  border: 1px solid #e8e8e8;
  border-right: 0;
  border-bottom: 0;
  table-layout: fixed;

  :deep(.ant-table-thead > tr > th) {
    /*display: inline-block;*/
    font-weight: bold;
    padding: 4px 4px;
    width: 100px;
  }
  :deep(.ant-table-tbody > tr > td) {
    padding: 4px 4px;
    width: 100px;
    /*display: inline-block;*/
  }
}
.sorted {
  color: #1890ff;
}
:deep(.ant-spin-nested-loading > div > .ant-spin) {
  z-index: 99;
}
</style>
