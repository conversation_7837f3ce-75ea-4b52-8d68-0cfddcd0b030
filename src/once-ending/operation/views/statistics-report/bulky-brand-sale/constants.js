import moment from 'moment'
import { message } from 'ant-design-vue'

moment.locale('zh-cn', {
  week: {
    dow: 1,
  }
})

function dealTreeData (array) {
  let cache = []
  array.forEach(item => {
    const hasChildren = (item.children && item.children.length)
    cache.push({
      title: `${item.title}${hasChildren ? '' : `(${item.value})`}`,
      key: item.key,
      value: item.value,
      children: hasChildren ? dealTreeData(item.children) : []
    })
  })
  return cache
}

export const timeType = [
  { label: '销售时间', value: 1 },
  { label: '收款时间', value: 2 }
]

export const outTimeType = [
  { label: '销售时间', value: 1 },
  { label: '交易时间', value: 2 }
]

export const tabs = [
  { label: '销售统计', value: '1' },
  { label: '价位占比', value: '2' }
]

export const compareTimePass = function (arrayTime, text) {
  const startTime = arrayTime[0]
  const endTime = arrayTime[1]
  const compareStartTime = moment(startTime).add(3, 'months')
  if (moment(compareStartTime).unix() < moment(endTime).unix()) {
    message.warning(`${text}时间间隔不能超过三个月`)
    return false
  } else {
    return true
  }
}

export const ranges = {
  今天: [moment().startOf('day'), moment().endOf('day')],
  昨天: [moment().subtract(1, 'days').startOf('day'), moment().subtract(1, 'days').endOf('day')],
  当周: [moment().startOf('week'), moment().endOf('week')],
  上周: [moment().subtract(1, 'weeks').startOf('week'), moment().subtract(1, 'weeks').endOf('week')],
  当月: [moment().startOf('month'), moment().endOf('month')],
  上月: [moment().subtract(1, 'months').startOf('month'), moment().subtract(1, 'months').endOf('month')],
}
