import { inject, provide } from 'vue'
import statisticsReport from '@operation/api/statistics-report'
import { to } from '@common/utils/common'
import { message } from 'ant-design-vue'
const key = Symbol('useApi')

async function commApi (type, params = {}) {
  const [err, res] = await to(statisticsReport[type](params))
  if (err) throw err
  const { code, userMsg } = res
  if (code !== 0) {
    message.error(userMsg)
    return
  }
  return res
}

export const useApi = function () {
  return inject(key)
}

export const createApi = function () {
  const apiKey = ['getDjData', 'getArea']
  const api = {}

  apiKey.forEach((item) => {
    api[item] = async (params) => {
      const res = await commApi(item, params)
      return res
    }
  })

  provide(key, api)
  return api
}
