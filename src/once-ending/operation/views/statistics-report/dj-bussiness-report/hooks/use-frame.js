import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useState } from './use-state'

export default function useFrame () {
  const { niFilter } = useState()
  const top = ref(0)
  const ob = ref(null)
  const setTop = function () {
    const head = document.querySelector('.ant-page-header')
    if (head && niFilter.value) {
      const headHeight = head.getBoundingClientRect().height
      const searchBoxHeight = niFilter.value.getBoundingClientRect().height
      top.value = headHeight + searchBoxHeight + 16
    }
  }

  onMounted(() => {
    ob.value = new ResizeObserver(setTop)
    ob.value.observe(niFilter.value)
  })
  onBeforeUnmount(() => {
    ob.value.disconnect()
    ob.value = null
  })

  return {
    top
  }
}
