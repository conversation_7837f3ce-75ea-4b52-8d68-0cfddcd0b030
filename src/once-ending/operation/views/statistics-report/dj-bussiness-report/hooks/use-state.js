import {
  inject,
  provide,
  reactive,
  computed,
  getCurrentInstance,
  ref,
  onMounted,
} from 'vue'
import useCommon from '../../hooks/useCommon'
import useLastUpdateDate from '../../hooks/useLastUpdateDate'
import useApiLastUpdateTime from '../../hooks/useApiLastUpdateTime'
import moment from 'moment'
import { message } from 'ant-design-vue'
const key = Symbol('useState')

export const useState = function () {
  return inject(key)
}

export const createState = function (api) {
  const { proxy } = getCurrentInstance()

  const { $store } = proxy.$root

  const { getDjData, getArea } = api

  const { lastUpdateDate, setLastUpdateDate } = useLastUpdateDate()

  const { lastNewUpdateDate, getLastUpdateDate } = useApiLastUpdateTime({
    id: 1,
    type_: proxy.$tnt.xtenant === 0 ? 1 : 2,
  })
  getLastUpdateDate()

  const { loading, isFeatch } = useCommon()

  const niFilter = ref(null)

  const state = reactive({
    formData: {
      times: [
        moment().format('YYYY-MM-DD'),
        moment().format('YYYY-MM-DD'),
      ],
      areaIds: [],
      sub_check: 0,
      subType: [1, 2, 5, 6, 11, 16, 17, 7, 18, 19, 22, -10, 9],
      is_virtual_goods: 0,
      type_: 6446,
    },
    frameUrl: '',
    allAreaIds: '',
  })

  const ranks = computed(() => {
    return $store.state.userInfo.Rank || []
  })

  const has9o9 = computed(() => {
    return ranks.value.includes('9o9')
  })

  const getParams = function () {
    const {
      formData: { times, areaIds, subType, ...other },
      allAreaIds
    } = state
    const starTime = times.length ? times[0] : ''
    const endTime = times.length ? times[1] : ''
    const params = {
      start_time: starTime,
      end_time: endTime,
      area_ids: areaIds.length ? areaIds.join(',') : allAreaIds,
      sub_type: subType.join(','),
      ...other,
    }
    return params
  }

  const checkForm = function () {
    const {
      formData: { times },
    } = state
    // 时间段不能为空
    if (!times.length) {
      message.warning(
        has9o9.value ? '请选择时间段' : '请选择时间段,且时间间隔不能超过三个月'
      )
      return true
    }
    if (times.length && times[0] > times[1]) {
      message.warn('开始时间不能大于结束时间')
      return true
    }
    if (
      !has9o9.value &&
      moment(times[0]).add(3, 'months').format('YYYY-MM-DD') < times[1]
    ) {
      message.warn('时间间隔不能超过三个月')
      return true
    }
    return false
  }

  const fetchData = async function () {
    if (checkForm()) return
    const params = getParams()
    loading.value = true
    const res = await getDjData(params)
    loading.value = false
    if (res) {
      const { data } = res
      isFeatch.value = true
      setLastUpdateDate()
      state.frameUrl = data
    }
  }

  const getAllArea = async function () {
    const params = {
      mode: 2,
      ranks: ['2c5'],
      strategy: 1,
      author: false,
    }
    const res = await getArea(params)
    if (res) {
      const { data } = res
      const arr = []
      const f = function (a) {
        a.forEach((d) => {
          if (d.children && d.children.length) {
            f(d.children)
          } else {
            arr.push(d.value)
          }
        })
      }
      f(data)
      state.allAreaIds = arr.join(',')
    }
  }

  const init = async function () {
    await getAllArea()
    fetchData()
  }
  init()

  const o = {
    state,
    lastUpdateDate,
    setLastUpdateDate,
    loading,
    isFeatch,
    fetchData,
    checkForm,
    lastNewUpdateDate,
    getLastUpdateDate,
    niFilter,
  }
  provide(key, o)
  return o
}
