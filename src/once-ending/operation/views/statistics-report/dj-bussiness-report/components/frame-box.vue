<script lang="jsx">
  import { defineComponent, toRefs } from 'vue'
  import NoData from '../../components/no-data'
  import { useState } from '../hooks/use-state'
  import useFrame from '../hooks/use-frame'

  export default defineComponent({
    components: {
      NoData
    },
    setup () {
      const { state } = useState()
      const { top } = useFrame()

      return {
        top,
        ...toRefs(state)
      }
    },
    render () {
      const { frameUrl, isFeatch, top } = this
      const frameBoxStyle = { top: `${top}px` }
      return <div class="frame-box" style={frameBoxStyle}>
      {
        frameUrl ? <div class="frame"><iframe width="100%" height="100%" src={frameUrl}></iframe></div> : <div class="no-data-box"><NoData is-featch={isFeatch} /></div>
      }
      </div>
    }
  })
</script>
<style lang="scss" scoped>
.frame-box{
    background: #fff;
    padding: 16px 8px;
    position: absolute;
    right: 24px;
    left: 24px;
    bottom: 16px;
    .frame{
      height: 100%;
      iframe{
        border: none;
      }
    }
    .no-data-box{
      display: flex;
      justify-content: center;
      align-items: center;
      height: 300px;
    }
}
</style>
