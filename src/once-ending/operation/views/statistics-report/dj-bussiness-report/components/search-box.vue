<script lang="jsx">
  import { defineComponent, toRefs } from 'vue'
  import { useState } from '../hooks/use-state'
  import { NiFilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import { DatePicker, Select } from 'ant-design-vue'
  import {
    typeOptions,
    subCheckOptions,
    subTypeOptions,
    virtualGoodsOptions,
  } from '../constants'

  export default defineComponent({
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect,
    },
    setup () {
      const { state, loading, fetchData, niFilter } = useState()

      return {
        ...toRefs(state),
        loading,
        fetchData,
        niFilter
      }
    },
    render () {
      const { loading, fetchData, formData } = this
      return (
      <div ref="niFilter">
        <NiFilter
          form={formData}
          loading={loading}
          onFilter={() => {
            fetchData()
          }}
          do-filter-when-reset={false}
          label-width={100}
          immediate={false}
        >
          <ni-filter-item label="统计维度">
            <Select
              placeholder="统计维度"
              v-model={formData.type_}
              options={typeOptions}
              getPopupContainer={(triggerNode) => triggerNode.parentNode}
            />
          </ni-filter-item>
          <ni-filter-item label="地区">
            <NiAreaSelect
              ref="areaSelect"
              v-model={formData.areaIds}
              allow-clear={true}
              multiple
              mode={2}
              show-search
              ranks={['2c5']}
              placeholder="请选择地区"
              class="area-selector"
              max-tag-count={1}
              onChange={val => { console.log(val) }}
            />
          </ni-filter-item>
          <ni-filter-item label="交易时间">
            <DatePicker.RangePicker
              v-model={formData.times}
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              show-time
            />
          </ni-filter-item>
          <ni-filter-item label="订单状态">
            <Select
              v-model={formData.sub_check}
              placeholder="请选择订单状态"
              options={subCheckOptions}
            />
          </ni-filter-item>
          <ni-filter-item label="订单类型">
            <Select
              v-model={formData.subType}
              allow-clear={true}
              mode="multiple"
              max-tag-count={1}
              placeholder="请选择订单类型"
              options={subTypeOptions}
            ></Select>
          </ni-filter-item>
          <ni-filter-item label="包含虚拟商品">
            <Select
              v-model={formData.is_virtual_goods}
              placeholder="请选择是否包含虚拟商品"
              options={virtualGoodsOptions}
            />
          </ni-filter-item>
        </NiFilter>
      </div>
      )
    },
  })
</script>
