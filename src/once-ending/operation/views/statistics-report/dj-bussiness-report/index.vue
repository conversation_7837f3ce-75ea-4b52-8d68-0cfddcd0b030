<script lang="jsx">
  import { defineComponent } from 'vue'
  import { NiListPage } from '@jiuji/nine-ui'
  import SearchBox from './components/search-box'
  import FrameBox from './components/frame-box'
  import { Button } from 'ant-design-vue'
  import { createApi } from './hooks/use-api'
  import { createState } from './hooks/use-state'
  export default defineComponent({
    components: {
      NiListPage,
      SearchBox,
      FrameBox,
    },
    setup () {
      const api = createApi()
      const { lastUpdateDate, lastNewUpdateDate } = createState(api)

      const toLink = function () {
        window.open(
          'https://oa.9ji.com/DataAnalysisPlatform/public/question/03297fdd-cd07-4444-860a-f630ced77b68'
        )
      }

      return {
        lastUpdateDate,
        lastNewUpdateDate,
        toLink,
      }
    },
    render () {
      const { lastUpdateDate, lastNewUpdateDate, toLink } = this

      return (
      <page class="store-reception">
        <div slot="extra">
          <div class="header-box">
            <span>数据更新于{lastNewUpdateDate || lastUpdateDate}</span>
            <div>
              <Button type="link" onClick={toLink}>
                交易明细数据
              </Button>
            </div>
          </div>
        </div>
        <ni-list-page push-filter-to-location={false}>
          <SearchBox />
          <FrameBox />
        </ni-list-page>
      </page>
      )
    },
  })
</script>
<style lang="scss">
.store-reception {
  position: relative;
  height: 100vh;
  .ant-page-header-heading-extra {
    float: none;
    line-height: 40px;
    font-size: 12px;
    color: #9c9c9c;
    .header-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>
