import { provide, ref } from 'vue'
import statisticsReport from '@operation/api/statistics-report'
import { to } from '@common/utils/common'
export default function useApiLastUpdateTime (params) {
  const lastNewUpdateDate = ref('')
  const getLastUpdateDate = async () => {
    const [err, res] = await to(statisticsReport.getLastUpdateDate(params))
    if (err) throw err
    const { code, data } = res
    if (code === 0 && data?.length) {
      lastNewUpdateDate.value = data[0]?.updateTime
    }
  }
  provide('lastNewUpdateDate', lastNewUpdateDate)
  provide('getLastUpdateDate', getLastUpdateDate)
  return {
    lastNewUpdateDate,
    getLastUpdateDate
  }
}
