import { ref } from 'vue'
import axios from 'axios'
import castArray from 'lodash/castArray'
export default function useExportXlsx ({ proxy = {}, type }) {
  const exportDataLoading = ref(false)

  const exportData = function ({ params = { fileName: '导出', newFileName: '' }, exportApiType = '' }) {
    if (type) {
      params.type = type
    }
    const { scene, fileName, newFileName } = params
    const url = proxy.$api.excelAction.exportExcel('ncSegments', exportApiType)
    const headers = { scene: scene }
    const date = new Date()
    delete params.fileName
    delete params.scene
    if (proxy.$tnt.xtenant < 1000 && params.storeTypes && params.storeTypes.length < 1) {
      delete params.storeTypes
    }
    if (proxy.$tnt.xtenant >= 1000 && params.storeTypes) {
      params.storeTypes = castArray(params.storeTypes)
    }
    exportXlsx({
      url,
      params: params,
      headers,
      fileName: newFileName || `${fileName}${date.getFullYear()}年${date.getMonth() +
        1}月${date.getDate()}日${date.getHours()}时${date.getMinutes()}分${date.getSeconds()}秒.xlsx`
    })
  }

  /**
   * 导出表格方法
   * @param { Object } param
   * @param { Object } headers 主要是传入后端 @方鑫 所定义的表格数据导出的公共接口的场景 { scene：'' }
   */
  const exportXlsx = function ({
    url = '',
    params = {},
    method = 'post',
    headers = {},
    fileName = ''
  } = {}) {
    const { timeout } = params
    delete params.timeout
    exportDataLoading.value = true
    axios({
      method: method,
      url: url,
      data: params,
      timeout: timeout || 1000 * 180,
      responseType: 'blob',
      headers: {
        Authorization: proxy.$store.state.token,
        ...headers
      }
    }).then(res => {
      exportDataLoading.value = false
      const link = document.createElement('a')
      let blob = new Blob([res.data], { type: 'application/x-excel' })
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    })
  }
  return {
    exportDataLoading,
    exportData
  }
}
