import { ref, onMounted, provide } from 'vue'
import { dateFormat } from '~/util/common'
export default function useLastUpdateDate () {
  const lastUpdateDate = ref('')
  const setLastUpdateDate = function () {
    lastUpdateDate.value = dateFormat(new Date(), 'YYYY-MM-DD hh:mm:ss')
  }
  provide('lastUpdateDate', lastUpdateDate)
  provide('setLastUpdateDate', setLastUpdateDate)
  onMounted(() => {
    setLastUpdateDate()
  })
  return {
    lastUpdateDate,
    setLastUpdateDate
  }
}
