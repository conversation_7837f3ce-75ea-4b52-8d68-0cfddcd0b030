import { ref, computed, provide, getCurrentInstance } from 'vue'
import { QUERY_STAT_DATA_HEADER, SALE_STATISTICS_EDIT_NOTE, SALE_STATISTICS_HEADER } from '@operation/store/modules/statistics-report/action-types'
import { message } from 'ant-design-vue'

// h参数不能删,这里手动注入h,编译的时候会把jsx编译为虚拟dom对象,渲染函数别名是h,如果不手动注入,会报h is not defined
export default function useReportHearder ({ proxy, type, h, canEdit = true }) {
  const hearderData = ref([])
  let root = getCurrentInstance()
  const { $tnt } = root.proxy.$root
  provide('hearderData', hearderData)
  const hasBbzs = computed(() => proxy.$store.getters.userInfo.Rank.includes('bbzs') && canEdit)
  const getHearder = async function (params, editNoteCallback = undefined) {
    params.type = type
    const res = await proxy.$store.dispatch(
      `operation/statisticsReport/${type === 4 ? SALE_STATISTICS_HEADER : QUERY_STAT_DATA_HEADER}`,
      params
    )
    if (res) {
      hearderData.value = []
      const { header = [] } = res.data
      header.map(d => {
        d.titleStr = d.title
        d.description = d.description || undefined
        d.oldDescription = d.description
        d.isEdit = false
        d.title = () => (
          <span>
            {d.titleStr}
            {hasBbzs.value ? (
              <a-popover overlay-class-name="title-popover">
                <template slot="content">
                  {!d.isEdit && (
                    <p style="color:#333333;font-size:14px">
                      {d.description || '未添加注释'}
                    </p>
                  )}
                  {d.isEdit && (
                    <a-textarea
                      autoSize
                      style="border-radius: 2px;"
                      value={d.description}
                      onInput={e => { d.description = e.target.value }}
                      placeholder="请输入注释"
                      allowClear
                    />
                  )}
                  {d.isEdit && (
                    <div style="text-align: right;margin-top: 13px;">
                      <a-button size="small" onClick={() => cancelNote(d)}>取消</a-button>
                      <a-button size="small" class="ml-8" type="primary" onClick={() => editNote(d, editNoteCallback)}>
                        确定
                      </a-button>
                    </div>
                  )}
                  {!d.isEdit && (
                    <div style="text-align: right;margin-top: 13px;">
                      <a-button
                        size="small"
                        type="primary"
                        onClick={() => {
                          d.isEdit = true
                        }}
                      >
                        编辑
                      </a-button>
                    </div>
                  )}
                </template>
                <span>
                  <a-icon
                    style="color:rgba(0, 0, 0, 0.85);margin-left: 8px;"
                    type="question-circle"
                  />
                </span>
              </a-popover>
            ) : d.description ? (
              <a-tooltip title={d.description}>
                <span>
                  <a-icon
                    style="color:rgba(0, 0, 0, 0.85);margin-left: 8px;"
                    type="question-circle"
                  />
                </span>
              </a-tooltip>
            ) : null}
          </span>
        )
        d.dataIndex = d.key
        d.sorter = d.sorting
        if ($tnt.xtenant < 1000) {
          if (d.dataIndex === 'overNStockCountSplic' || d.dataIndex === 'avgStockAgeSplic') {
            d.width = 220
          }
          if (params.innerType === 1 && ['grossProfit', 'grossProfitRatio', 'stockSaleRatio', 'inStockRate'].includes(d.dataIndex)) {
            d.hide = true
          }
        }
      })
      hearderData.value = header
    }
  }
  const editNote = async function (item, editNoteCallback) {
    const { fieldNote, key, description: note } = item
    const params = {
      type,
      field: fieldNote || key,
      note
    }
    const res = await proxy.$store.dispatch(
      `operation/statisticsReport/${SALE_STATISTICS_EDIT_NOTE}`,
      params
    )
    if (res) {
      message.success('修改成功')
      editNoteCallback && typeof editNoteCallback === 'function' && editNoteCallback()
    }
  }
  const cancelNote = function (item) {
    item.description = item.oldDescription
    item.isEdit = false
  }
  return {
    hearderData,
    getHearder
  }
}
