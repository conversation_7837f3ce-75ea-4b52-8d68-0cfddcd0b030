import { ref } from 'vue'
import { QUERY_STAT_DATA } from '@operation/store/modules/statistics-report/action-types'
export default function useTableProps ({ proxy, type, loading, isFeatch, setLastUpdateDate, hearderData, usePagination = true }) {
  const dataSource = ref([])
  const tableSort = ref({})
  const copyData = ref([])
  const copyTotal = ref({})
  const isWebPagination = ref(false)
  const pagination = ref({
    current: 1,
    pageSize: 50,
    total: 0,
    pageSizeOptions: ['20', '50', '100', '200'],
    showQuickJumper: true,
    showTotal: total => `共计${total}条`
  })

  const featchList = async function ({ params = {}, current = undefined, totalKey = '' }) {
    params.type = type
    // 如果后端分页，则写入分页信息和排序信息
    if (!isWebPagination.value) {
      if (current) pagination.value.current = current
      const { current: pageCurrent, pageSize: size } = pagination.value
      params.current = pageCurrent
      params.size = size
      if (tableSort.value.field) params.sort = tableSort.value.order
    }
    loading.value = true
    const res = await proxy.$store.dispatch(
      `operation/statisticsReport/${QUERY_STAT_DATA}`,
      params
    )
    loading.value = false
    if (res) {
      isFeatch.value = true
      const { values = [], total, count = 0 } = res.data
      if (!isWebPagination.value) {
        if (total) {
          setTotal(total, totalKey)
          values.push(total)
        }
        dataSource.value = values
        pagination.value.total = count
      } else {
        if (current) pagination.value.current = current
        copyData.value = JSON.parse(JSON.stringify(values))
        copyTotal.value = total ? JSON.parse(JSON.stringify(total)) : {}
        pagination.value.total = copyData.value.length
        setTableData(hearderData, totalKey)
      }
      setLastUpdateDate()
    }
  }

  const tableChange = function ({ paginationObj, filters, sorter }, tableChangeCallback = undefined) {
    pagination.value = { ...paginationObj }
    tableSort.value = { ...sorter }
    tableChangeCallback && typeof tableChangeCallback === 'function' && tableChangeCallback()
  }
  const setTableData = function (hearderData, totalKey = '') {
    const data = JSON.parse(JSON.stringify(copyData.value))
    const total = JSON.parse(JSON.stringify(copyTotal.value))
    sortData(data)
    const { current, pageSize: size } = pagination.value
    const start = usePagination ? size * current - size : 0
    const end = usePagination ? size * current : data.length
    const tableData = data.slice(start, end)
    const hasMoneyToConvert = hearderData.value
      .filter(d => d.moneyToConvert)
      .map(d => d.key)
    tableData.map(d => {
      hasMoneyToConvert.map(k => {
        if (d.hasOwnProperty(k)) {
          d[k] = formatNum(d[k].toFixed(2))
        }
      })
    })
    if (Object.keys(total).length && tableData.length) {
      setTotal(total, totalKey)
      tableData.push(total)
    }
    dataSource.value = tableData
  }

  const setTotal = function (total, totalKey) {
    const columnsKey = hearderData.value.map(d => d.key)
    for (const k in total) {
      if (columnsKey.includes(k)) {
        const columnItem = hearderData.value.find(d => d.key === k)
        total[k] = columnItem.moneyToConvert
          ? formatNum(total[k].toFixed(2))
          : total[k]
      }
    }
    if (totalKey) total[totalKey] = '合计'
  }

  const formatNum = function (value) {
    if (isNaN(value)) return value
    let _value = value
    if (!_value) {
      return '0'
    }
    if (typeof parseFloat(_value) !== 'number') {
      return '0'
    }
    if (typeof _value === 'string') {
      // 处理大于2位的小数字符串
      if (_value.match('.') && _value.split('.')[1]?.length > 2) {
        _value = parseFloat(_value).toFixed(2)
      } else {
        // _value = parseFloat(_value).toString()
      }
    } else if (typeof _value === 'number') {
      // 处理大于2位的小数
      if (
        _value.toString().match('.') &&
        _value.toString().split('.')[1]?.length > 2
      ) {
        _value = _value.toFixed(2)
      } else {
        _value = _value.toString()
      }
    }
    const cache = new RegExp(/\B(?=(\d{3})+(?!\d))/g)
    return _value.replace(cache, ',') // 转千分位
  }

  const sortData = function (data) {
    if (!tableSort.value.field) return
    sortfun(tableSort.value.field, tableSort.value.order, data)
  }

  const sortfun = function (key, sort, arry) {
    return arry.sort(compare(key, sort))
  }

  const compare = function (key, sort) {
    return function (m, n) {
      let a = m[key]
      let b = n[key]
      if (sort === 'ascend') {
        return a - b // 升序
      } else {
        return b - a // 降序
      }
    }
  }

  return {
    dataSource,
    tableSort,
    pagination,
    tableChange,
    copyData,
    copyTotal,
    setTableData,
    formatNum,
    featchList,
    isWebPagination
  }
}
