import { ref, provide } from 'vue'
import { QUERY_CONDITION } from '@operation/store/modules/statistics-report/action-types'
export default function usesearchProps ({ proxy, type }) {
  const searchData = ref({})
  const searchOptions = ref({})
  provide('searchOptions', searchOptions)
  const getSearchOptions = async function () {
    const res = await proxy.$store.dispatch(
      `operation/statisticsReport/${QUERY_CONDITION}`,
      { type }
    )
    if (res) {
      const { data } = res
      const o = {}
      data.forEach(item => {
        const treeValue = []
        const { key = '', list = [], tree = [], value } = item
        o[key] = {
          list: list.map(d => {
            return {
              label: d.name,
              value: d.code
            }
          }),
          tree: setTree(tree, treeValue),
          treeValue: treeValue,
          value
        }
      })
      searchOptions.value = o
    }
  }
  provide('getSearchOptions', getSearchOptions)
  const setTree = function (arr, treeValue) {
    return arr.map(d => {
      d.selected && treeValue.push(d.value)
      d.title = d.key
      d.key = d.value
      if (d.children && d.children.length) {
        d.children = setTree(d.children, treeValue)
      }
      return d
    })
  }
  return {
    searchData,
    searchOptions,
    getSearchOptions
  }
}
