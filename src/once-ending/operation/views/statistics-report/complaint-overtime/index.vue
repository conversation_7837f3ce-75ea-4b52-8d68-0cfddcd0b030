<script lang="jsx">
  import { defineComponent, ref, provide, getCurrentInstance } from 'vue'
  import { NiListPage } from '@jiuji/nine-ui'
  import useCommon from '../hooks/useCommon'
  import useLastUpdateDate from '../hooks/useLastUpdateDate'
  import SearchBox from './components/search-box'
  import TableBox from './components/table-box'
  import { dateFormat } from '~/util/common'
  import { LIST_ALL_ROLES, TOU_SU_TIME_OUT } from '@operation/store/modules/statistics-report/action-types'
  import * as constants from './constants'
  export default defineComponent({
    components: {
      NiListPage,
      SearchBox,
      TableBox
    },
    data () {
      return {
        tousuIdCustomRender: (text, record) => {
          return (
          <div>
            <router-link
              target="_blank"
              to={{
                path: `/complaint/detail/${text}`
              }}
            >
              {text}
            </router-link>
          </div>
          )
        },
        statusCustomRender: (text, record) => {
          return (
          <div class="status">
          <span style={{ background: constants.statusBgMap.get(text) ? constants.statusBgMap.get(text)[1] : '', padding: '4px', color: constants.statusBgMap.get(text) ? constants.statusBgMap.get(text)[2] : '' }}>{constants.statusBgMap.get(text) ? constants.statusBgMap.get(text)[0] : ''}</span>
          </div>
          )
        },
        typeCustomRender: (text, record) => {
          return (
          <div>{constants.typeMap.get(text) || ''}</div>
          )
        }
      }
    },
    computed: {
      columns () {
        const columns = constants.columns
        const typeItem = columns.find(d => d.dataIndex === 'type')
        const tousuIdItem = columns.find(d => d.dataIndex === 'tousuId')
        const statusItem = columns.find(d => d.dataIndex === 'status')
        typeItem.customRender = this.typeCustomRender
        tousuIdItem.customRender = this.tousuIdCustomRender
        statusItem.customRender = this.statusCustomRender
        return columns
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const { loading, isFeatch } = useCommon()
      const { lastUpdateDate, setLastUpdateDate } = useLastUpdateDate()
      const dataSource = ref([])
      const pagination = ref({
        current: 1,
        pageSize: 50,
        total: 0,
        pageSizeOptions: ['20', '50', '100', '200'],
        showQuickJumper: true,
        showTotal: total => `共计${total}条`
      })

      const searchData = ref({
        type: [],
        followUserIds: [],
        departIds: [],
        roleIds: [],
        time: [
          dateFormat(new Date(), 'YYYY-MM-DD 00:00:00'),
          dateFormat(new Date(), 'YYYY-MM-DD 23:59:59')
        ]
      })

      const tableChange = function ({ paginationObj, filters, sorter }) {
        pagination.value = { ...paginationObj }
        featchList()
      }

      const getParams = function () {
        const { time, type, followUserIds, departIds, roleIds } = searchData.value
        const params = { }
        if (type.length) params.type = type
        if (followUserIds.length) params.followUserIds = followUserIds
        if (departIds.length) params.departIds = departIds
        if (roleIds.length) params.roleIds = roleIds
        if (time && time.length) {
          params.startTime = time[0]
          params.endTime = time[1]
        }
        delete params.time
        return params
      }
      provide('getParams', getParams)

      const featchList = async function (cur) {
        if (cur) pagination.value.current = cur
        const { current, pageSize: size } = pagination.value
        const params = { ...getParams(), current, size }
        loading.value = true
        const res = await proxy.$store.dispatch(
          `operation/statisticsReport/${TOU_SU_TIME_OUT}`, params
        )
        loading.value = false
        if (res) {
          isFeatch.value = true
          setLastUpdateDate()
          const { data: { records, total } } = res
          dataSource.value = records
          pagination.value.total = total
        }
      }

      const rolesOptions = ref([])
      const listAllRoles = async function () {
        const res = await proxy.$store.dispatch(
          `operation/statisticsReport/${LIST_ALL_ROLES}`
        )
        if (res) {
          const { data } = res
          rolesOptions.value = data.map(d => {
            return {
              label: d.name,
              value: d.value
            }
          })
        }
      }
      listAllRoles()
      featchList()

      return {
        lastUpdateDate,
        searchData,
        featchList,
        dataSource,
        pagination,
        tableChange,
        loading,
        isFeatch,
        rolesOptions
      }
    },
    render () {
      const {
        lastUpdateDate,
        searchData,
        featchList,
        dataSource,
        columns,
        tableChange,
        pagination,
        loading,
        isFeatch,
        rolesOptions
      } = this

      return (
      <page class="complaint-overtime">
        <div slot="extra">数据更新于{lastUpdateDate}</div>
        <ni-list-page push-filter-to-location={false}>
          <search-box
            search-data={searchData}
            onSearch={() => featchList(1)}
            loading={loading}
            roles-options={rolesOptions}
          ></search-box>
          <table-box
            style="margin-top:16px"
            data-source={dataSource}
            columns={columns}
            onTableChange={({ pagination, filters, sorter }) => {
              tableChange({ paginationObj: pagination, filters, sorter })
            }}
            loading={loading}
            pagination={pagination}
            is-featch={isFeatch}
          ></table-box>
        </ni-list-page>
      </page>
      )
    }
  })
</script>
<style lang="scss">
.complaint-overtime {
  position: relative;
  .ant-page-header-heading-extra {
    float: none;
    line-height: 40px;
    font-size: 12px;
    color: #9c9c9c;
  }
  .status{
    border-radius: 2px;
    padding: 4px;
  }
}
</style>
