export const typeOptions = [
  {
    label: '受理超时',
    value: 1
  },
  {
    label: '还原超时',
    value: 2
  },
  {
    label: '跟进超时',
    value: 3
  },
  {
    label: '完结超时',
    value: 4
  }
]

export const typeMap = new Map([[1, '受理超时'], [2, '还原超时'], [3, '跟进超时'], [4, '完结超时']])

export const columns = [
  {
    title: '投诉ID',
    dataIndex: 'tousuId'
  },
  {
    title: '投诉性质',
    dataIndex: 'tagName'
  },
  {
    title: '投诉时间',
    dataIndex: 'tousuTime'
  },
  {
    title: '超时类型',
    dataIndex: 'type'
  },
  {
    title: '状态',
    dataIndex: 'status'
  },
  {
    title: '超时责任人',
    dataIndex: 'followUser'
  },
  {
    title: '超时责任部门',
    dataIndex: 'depart'
  },
  {
    title: '责任人主要角色',
    dataIndex: 'role'
  },
  {
    title: '超时时间点',
    dataIndex: 'timeout'
  },
  {
    title: '超时时长',
    dataIndex: 'overTime'
  }
]

export const statusBgMap = new Map([[1, ['已处理', 'rgb(11 190 105 / 10%)', '#0BBE69']], [0, ['未处理', 'rgb(250 100 0 / 10%)', '#FA6400']]])
