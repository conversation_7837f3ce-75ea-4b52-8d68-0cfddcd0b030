<script lang="jsx">
  import { defineComponent, ref } from 'vue'
  import {
    NiFilter,
    NiFilterItem,
    NiStaffSelect
  } from '@jiuji/nine-ui'
  import { Select, DatePicker } from 'ant-design-vue'
  import { typeOptions } from '../constants'
  import AreaSelector from '~/components/staff/area-selector'
  export default defineComponent({
    props: {
      searchData: {
        type: Object,
        default: () => ({})
      },
      rolesOptions: {
        type: Array,
        default: () => [],
      },
      loading: {
        type: Boolean,
        default: false,
      }
    },
    components: {
      NiFilter,
      NiFilterItem,
      NiStaffSelect,
      AreaSelector
    },
    setup (props) {
      const selectOptions = ref([])
      const cacheOptions = ref([])
      const selectSearchValue = ref('')

      // 每次下拉框获取焦点,初始化下拉框数据
      const selectFocus = function () {
        if (selectOptions.value.length) {
          return
        }
        const options = JSON.parse(JSON.stringify(props.rolesOptions))
        const initOptions = options.splice(0, 50)
        if (props.searchData.roleIds.length) {
          props.searchData.roleIds.forEach(item => {
            const index = options.findIndex(d => d.value === item)
            if (index !== -1) {
              initOptions.unshift(options.splice(index, 1)[0])
            }
          })
        }
        selectOptions.value = initOptions
        cacheOptions.value = options
      }

      // 每次下拉框失去焦点,置空下拉框数据
      const selectBlur = function () {
        selectOptions.value = []
        cacheOptions.value = []
      }

      // 每次用户输入,匹配所有数据,将数据筛选出来
      const selectSearch = function (val) {
        selectSearchValue.value = val
        const options = JSON.parse(JSON.stringify(props.rolesOptions))
        selectOptions.value = options.filter(d => d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase()))
        cacheOptions.value = options.filter(d => !d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase()))
      }

      const handleSelect = function () {
        const options = JSON.parse(JSON.stringify(props.rolesOptions))
        const initOptions = options.splice(0, 50)
        if (props.searchData.roleIds.length) {
          props.searchData.roleIds.forEach(item => {
            const index = options.findIndex(d => d.value === item)
            if (index !== -1) {
              initOptions.unshift(options.splice(index, 1)[0])
            }
          })
        }
        selectOptions.value = initOptions
        cacheOptions.value = options
        selectSearchValue.value = ''
      }

      // 每次下拉框滚动条滚到底部,加载缓存数据
      const selectPopupScroll = function (e) {
        if (!cacheOptions.value.length) {
          return
        }
        const { target } = e
        const scrollHeight = target.scrollHeight - target.scrollTop
        const clientHeight = target.clientHeight
        if (scrollHeight < clientHeight + 5) {
          const options = cacheOptions.value.splice(0, 50)
          selectOptions.value = selectOptions.value.concat(options)
        }
      }

      const selectDepart = function (val) {
        props.searchData.departIds = val
      }

      return {
        selectFocus,
        selectBlur,
        selectSearch,
        handleSelect,
        selectPopupScroll,
        selectOptions,
        selectDepart
      }
    },
    render () {
      const {
        searchData,
        loading,
        selectFocus,
        selectBlur,
        selectSearch,
        handleSelect,
        selectPopupScroll,
        selectOptions,
        selectDepart
      } = this
      return (
      <div>
        {Object.keys(searchData).length ? (
          <ni-filter
            form={searchData}
            loading={loading}
            onFilter={() => {
              this.$emit('search')
            }}
            label-width={100}
            immediate={false}
          >
            <ni-filter-item label="投诉时间">
              <DatePicker.RangePicker
                v-model={searchData.time}
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD HH:mm:ss"
                allow-clear={true}
              ></DatePicker.RangePicker>
            </ni-filter-item>
            <ni-filter-item label="超时类型">
              <Select
                v-model={searchData.type}
                allow-clear={true}
                mode="multiple"
                placeholder="请选择超时类型"
                max-tag-count={1}
                options={typeOptions}
              ></Select>
            </ni-filter-item>
            <ni-filter-item label="超时责任人">
              <NiStaffSelect
                class={{
                  'more-select':
                    searchData.followUserIds &&
                    searchData.followUserIds.length &&
                    searchData.followUserIds.length > 1
                }}
                v-model={searchData.followUserIds}
                max-tag-count={1}
                multiple={true}
                option-filter-prop="children"
                allow-clear={true}
                show-search={true}
                placeholder="请输入"
              ></NiStaffSelect>
            </ni-filter-item>
            <ni-filter-item label="超时责任部门">
            <AreaSelector
            selectedLastNode
            allowClear
            value={searchData.departIds}
            dropdownStyle={{ maxHeight: '300px' }}
            onChange={(list, node, selectedid, checkedNodes, allIdList) => { searchData.departIds = allIdList || [] }}
            type="department"/>

            </ni-filter-item>
            <ni-filter-item label="角色">
              <Select
                placeholder="请输入角色"
                show-arrow
                allow-clear={true}
                option-filter-prop="children"
                max-tag-count={1}
                mode="multiple"
                v-model={searchData.roleIds}
                onFocus={selectFocus}
                onBlur={selectBlur}
                onSelect={handleSelect}
                onSearch={selectSearch}
                onPopupScroll={selectPopupScroll}
                options={selectOptions}
              ></Select>
            </ni-filter-item>
          </ni-filter>
        ) : null}
      </div>
      )
    }
  })
</script>
<style lang="scss" scoped>
.more-select {
  :deep(.ant-select-selection__choice) {
    max-width: 45%;
  }
}
</style>
