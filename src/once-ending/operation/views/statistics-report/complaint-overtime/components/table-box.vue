<script lang="jsx">
  import { defineComponent, inject, ref, getCurrentInstance } from 'vue'
  import { NiTable } from '@jiuji/nine-ui'
  import NoData from '../../components/no-data'
  import axios from 'axios'
  import statisticsReport from '@operation/api/statistics-report'
  export default defineComponent({
    props: {
      searchData: {
        type: Object,
        default: () => ({})
      },
      dataSource: {
        type: Array,
        default: () => []
      },
      columns: {
        type: Array,
        default: () => []
      },
      pagination: {
        type: Object,
        default: () => {
          return {
            current: 1,
            pageSize: 50,
            total: 0,
            pageSizeOptions: ['20', '50', '100', '200'],
            showQuickJumper: true,
            showTotal: total => `共计${total}条`
          }
        }
      },
      loading: {
        type: Boolean,
        default: false
      },
      isFeatch: {
        type: Boolean,
        default: false
      },
      partner: {
        type: <PERSON>olean,
        default: false
      }
    },
    components: {
      NiTable,
      NoData
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const getParams = inject('getParams')
      const exportDataLoading = ref(false)
      const url = statisticsReport.exportExcelUrl()
      const handleExport = function () {
        const params = { ...getParams() }
        props.partner && (params.partner = true)
        axios({
          method: 'post',
          url: url,
          data: params,
          timeout: 1000 * 180,
          responseType: 'blob',
          headers: {
            Authorization: proxy.$store.state.token
          }
        }).then(res => {
          exportDataLoading.value = false
          const link = document.createElement('a')
          let blob = new Blob([res.data], { type: 'application/x-excel' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          const date = new Date()
          link.download = `投诉跟进超时统计${date.getFullYear()}年${date.getMonth() +
            1}月${date.getDate()}日${date.getHours()}时${date.getMinutes()}分${date.getSeconds()}秒.xls`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        }).finally(() => {
          exportDataLoading.value = false
        })
      }

      return {
        exportDataLoading,
        handleExport
      }
    },
    render () {
      const {
        dataSource,
        columns,
        pagination,
        isFeatch,
        exportDataLoading,
        handleExport,
        loading
      } = this
      return (
      <NiTable
        class="add-sub-table"
        locale={{ emptyText: <NoData is-featch={isFeatch} /> }}
        dataSource={dataSource}
        columns={columns}
        loading={loading}
        onChange={(pagination, filters, sorter) => {
          this.$emit('tableChange', { pagination, filters, sorter })
        }}
        pagination={pagination}
      >
        <div slot="tool">
          <div class="tool">
            <a-button
              class="btn"
              icon="export"
              loading={exportDataLoading}
              onClick={handleExport}
            >
              导出
            </a-button>
          </div>
        </div>
      </NiTable>
      )
    }
  })
</script>
<style lang="scss" scoped>
.tool {
  float: right;
  .btn {
    margin-left: 8px;
  }
}
</style>
