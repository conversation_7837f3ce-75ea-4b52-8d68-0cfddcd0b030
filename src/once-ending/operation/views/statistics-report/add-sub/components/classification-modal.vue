<script lang="jsx">
  import { defineComponent, computed, inject, getCurrentInstance } from 'vue'
  import { Modal, Form, TreeSelect, message } from 'ant-design-vue'
  import { SAVE_EXCLUDED_CATEGORY } from '@operation/store/modules/statistics-report/action-types'
  const SHOW_ALL = TreeSelect.SHOW_ALL

  export default defineComponent({
    props: {
      showClassification: {
        type: Boolean,
        default: false
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const searchOptions = inject('searchOptions')
      const getSearchOptions = inject('getSearchOptions')
      const visible = computed({
        get: () => {
          return props.showClassification
        },
        set: (val) => {
          proxy.$emit('update:showClassification', val)
        }
      })
      const save = async function () {
        const params = {
          category: searchOptions.value.cid.treeValue.join(',')
        }
        const res = await proxy.$store.dispatch(
          `operation/statisticsReport/${SAVE_EXCLUDED_CATEGORY}`,
          params
        )
        if (res) {
          message.success('保存成功')
          getSearchOptions()
          visible.value = false
        }
      }

      const afterClose = function () {
        proxy.$emit('afterClose')
      }
      return {
        visible,
        save,
        afterClose,
        searchOptions
      }
    },
    render () {
      const { visible, save, searchOptions, afterClose } = this
      return <Modal after-close={afterClose} mask-closable={false} visible={visible} title="配置排除分类" onOk={save} onCancel={() => { this.visible = false }} okText="保存">
        {
          Object.keys(searchOptions).length > 0 ? <Form>
          <Form.Item label="选择分类">
            <TreeSelect
              show-arrow
              showCheckedStrategy={SHOW_ALL}
              show-search
              tree-node-filter-prop="title"
              tree-checkable
              allow-clear
              dropdown-style={{ maxHeight: '300px' }}
              placeholder="选择分类"
              maxTag-count={1}
              v-model={searchOptions.cid.treeValue}
              tree-data={searchOptions.cid.tree || []} />
          </Form.Item>
        </Form> : null
        }
      </Modal>
    }
  })
</script>
