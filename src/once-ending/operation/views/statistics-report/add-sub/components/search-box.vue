<script lang="jsx">
  import { defineComponent } from 'vue'
  import { NiFilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import { Select, DatePicker } from 'ant-design-vue'
  export default defineComponent({
    props: {
      searchData: {
        type: Object,
        default: () => ({})
      }
    },
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect
    },
    inject: ['searchOptions', 'loading'],
    render () {
      const { searchData, loading, searchOptions } = this
      return (
      <div>
      {
        Object.keys(searchData).length ? <ni-filter
        form={searchData}
        loading={loading}
        onFilter={() => {
          this.$emit('search')
        }}
        label-width={85}
        immediate={false}
      >
      <ni-filter-item label="地区">
        <ni-area-select
        v-model={searchData.areaId}
        allow-clear={true}
        multiple={true}
        placeholder="请选择地区"
        search-placeholder="请输入"
        max-tag-count={1}
        >
        </ni-area-select>
      </ni-filter-item>
       <ni-filter-item label="店面类别">
        <Select
        v-model={searchData.shopType}
        allow-clear={true}
        mode="multiple"
        placeholder="请选择店面类别"
        max-tag-count={1}
        options={searchOptions.shopType?.list || []}
        >
        </Select>
      </ni-filter-item>
        <ni-filter-item label="订单类型">
        <Select
        v-model={searchData.subType}
        allow-clear={true}
        mode="multiple"
        placeholder="请选择订单类型"
        max-tag-count={1}
        options={searchOptions.subType?.list || []}
        >
        </Select>
      </ni-filter-item>
      <ni-filter-item label="订单日期">
        <DatePicker.RangePicker
         show-time
        v-model={searchData.time}
        value-format="YYYY-MM-DD HH:mm:ss"
        allow-clear={true}
        >
        </DatePicker.RangePicker>
      </ni-filter-item>
      </ni-filter> : null
      }
      </div>
      )
    }
  })
</script>
