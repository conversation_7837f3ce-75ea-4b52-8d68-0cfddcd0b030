<script lang="jsx">
  import { defineComponent, ref, nextTick, inject, getCurrentInstance } from 'vue'
  import { NiTable } from '@jiuji/nine-ui'
  import NoData from '../../components/no-data'
  import useExportXlsx from '../../hooks/useExportXlsx'
  export default defineComponent({
    props: {
      searchData: {
        type: Object,
        default: () => ({})
      },
      dataSource: {
        type: Array,
        default: () => []
      },
      columns: {
        type: Array,
        default: () => []
      },
      pagination: {
        type: Object,
        default: () => {
          return {
            current: 1,
            pageSize: 50,
            total: 0,
            pageSizeOptions: ['20', '50', '100', '200'],
            showQuickJumper: true,
            showTotal: total => `共计${total}条`
          }
        }
      }
    },
    inject: ['loading', 'isFeatch'],
    components: {
      NiTable,
      NoData
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const getParams = inject('getParams')
      const { exportDataLoading, exportData } = useExportXlsx({ proxy })

      const handleExport = function (flag) {
        const result = flag === 'add_sub_report' ? 'add_sub_report' : 'add_sub_area_employee'
        const params = {
          fileName: '识别码加单统计',
          scene: result,
          ...getParams(result)
        }
        exportData({ params })
      }

      return {
        exportDataLoading,
        handleExport
      }
    },
    render () {
      const {
        dataSource,
        columns,
        pagination,
        isFeatch,
        exportDataLoading,
        handleExport,
        loading,
      } = this
      return (
      <ni-table
        class="add-sub-table"
        locale={{ emptyText: <NoData is-featch={isFeatch} /> }}
        dataSource={dataSource}
        columns={columns}
        loading={loading}
        onChange={(pagination, filters, sorter) => {
          this.$emit('tableChange', { pagination, filters, sorter })
        }}
        pagination={false}
        rowClassName={(r) => `level-${r.level}`}
        footerTotalNum={1}
      >
        <div slot="tool">
          <div class="tool">
            <a-dropdown placement="bottomCenter">
            <a-button class="btn" icon="export">导出</a-button>
            <a-menu slot="overlay">
              <a-menu-item>
                <span onClick={() => handleExport('add_sub_report')}>导出门店数据</span>
              </a-menu-item>
              <a-menu-item>
                <span onClick={() => handleExport()}>导出人员数据</span>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
          </div>
        </div>

      </ni-table>
      )
    }
  })
</script>
<style lang="scss" scoped>
.tool{
  float: right;
  .btn{
    margin-left: 8px;
  }
}
:deep(.level-1) { // 大区
  background: #40a9ff;
  font-weight: 600;
}
:deep(.level-2) { // 小区
  background: #bae7ff;
}
:deep(.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td) {
  background: none;
}
:deep(.ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td) {
  background: none;
}

</style>
