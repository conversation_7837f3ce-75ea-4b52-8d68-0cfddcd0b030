<script lang="jsx">
  import { defineComponent, h, watch, getCurrentInstance } from 'vue'
  import { NiListPage, NiTable } from '@jiuji/nine-ui'
  import useTableProps from '../hooks/useTableProps'
  import useCommon from '../hooks/useCommon'
  import useReportHearder from '../hooks/useReportHearder'
  import useLastUpdateDate from '../hooks/useLastUpdateDate'
  import usesearchProps from '../hooks/usesearchProps'
  import NoData from '../components/no-data'
  import useExportXlsx from '../hooks/useExportXlsx'
  export default defineComponent({
    components: {
      NiListPage,
      NiTable,
      NoData
    },
    data () {
      return {
        customRenderMap: new Map([['userNumber', (text, record) => {
          return <div><a href={`/member/index?actionName=fromid&key=${record.userNumber}`}>{record.userNumber}</a></div>
        }], ['orderNumber', (text, record) => {
          return <div>
        {
          record.subType === 4 || record.subType !== 7 ? <a href={record.subType === 4 ? `/staticpc/#/after-service/order/edit/${record.orderNumber}` : `/addOrder/editOrder?subid=${record.orderNumber}`}>{record.orderNumber}</a> : <span>{record.orderNumber}</span>
        }
        </div>
        }], ['addSubType', (text, record) => {
          return <div><span>{record.addSubType === 1 ? '识别码加单' : '号码加单'}</span></div>
        }]])
      }
    },
    computed: {
      columns () {
        if (!this.hearderData.length) return []
        const hearderData = this.hearderData
        hearderData.forEach(item => {
          const customRender = this.customRenderMap.get(item.key)
          if (customRender) {
            item.customRender = customRender
          }
        })
        return hearderData
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const { loading, isFeatch } = useCommon()
      const { lastUpdateDate, setLastUpdateDate } = useLastUpdateDate()
      const { exportDataLoading, exportData } = useExportXlsx({ proxy, type: 11 })
      const { searchData } = usesearchProps({
        proxy,
        type: 11
      })
      const { hearderData, getHearder } = useReportHearder({
        proxy,
        h,
        type: 11
      })
      const { dataSource, tableSort, pagination, tableChange, featchList } = useTableProps({
        proxy,
        type: 11,
        loading,
        isFeatch,
        setLastUpdateDate,
        hearderData
      })
      watch(() => pagination.value.total, () => {
        document.title = `销售员识别码加单统计 - ${searchData.value.inUser} (共${pagination.value.total}单)`
      })
      const featchHeader = async function () {
        const params = {
          type: 11
        }
        await getHearder(params, featchHeader)
      }
      const setDefaultSearchData = function () {
        const { copySearch: copySearchStr } = proxy.$route.query
        const dataObj = copySearchStr ? JSON.parse(copySearchStr) : {}
        searchData.value = dataObj
      }

      const handleExport = function () {
        const params = {
          fileName: `销售员识别码加单统计-${searchData.value.inUser}-`,
          scene: 'add_sub_employee_detail',
          ...getParams()
        }
        exportData({ params })
      }

      const getParams = function () {
        const params = {
          type: 11,
          ...searchData.value
        }
        return params
      }

      setDefaultSearchData()
      featchHeader()
      featchList({ params: { ...getParams() } })
      return {
        lastUpdateDate,
        loading,
        searchData,
        featchList,
        hearderData,
        isFeatch,
        dataSource,
        tableSort,
        pagination,
        tableChange,
        exportDataLoading,
        handleExport,
        getParams
      }
    },
    render () {
      const {
        lastUpdateDate,
        loading,
        dataSource,
        tableChange,
        isFeatch,
        pagination,
        columns,
        featchList,
        exportDataLoading,
        handleExport,
        getParams,
        searchData
      } = this

      return (
    <page class="add-sub-people" title={`销售员识别码加单统计 - ${searchData.inUser} (共${pagination.total}单)`}>
      <div slot="extra">数据更新于{lastUpdateDate}</div>
      <ni-list-page push-filter-to-location={false}>
        <ni-table
          class="add-sub-people-table"
          locale={{ emptyText: <NoData is-featch={isFeatch} /> }}
          dataSource={dataSource}
          loading={loading}
          columns={columns}
          onChange={(pagination, filters, sorter) => { tableChange({ paginationObj: pagination, filters, sorter }, () => { featchList({ params: { ...getParams() } }) }) }}
          pagination={pagination}
        >
        <div slot="tool">
        <a-button
            style="float: right;"
            class="btn"
            icon="export"
            loading={exportDataLoading}
            onClick={handleExport}
          >
            导出
          </a-button>
        </div>
        </ni-table>
      </ni-list-page>
    </page>
    )
    }
  })
</script>
<style lang="scss">
.add-sub-people {
  position: relative;
  .ant-page-header-heading-extra {
    float: none;
    line-height: 40px;
    font-size: 12px;
    color: #9c9c9c;
  }
}
</style>
