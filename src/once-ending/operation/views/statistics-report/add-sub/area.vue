<script lang="jsx">
  import { defineComponent, h, ref, getCurrentInstance } from 'vue'
  import { NiListPage, NiTable } from '@jiuji/nine-ui'
  import useTableProps from '../hooks/useTableProps'
  import useCommon from '../hooks/useCommon'
  import useReportHearder from '../hooks/useReportHearder'
  import useLastUpdateDate from '../hooks/useLastUpdateDate'
  import usesearchProps from '../hooks/usesearchProps'
  import useExportXlsx from '../hooks/useExportXlsx'
  import NoData from '../components/no-data'
  export default defineComponent({
    components: {
      NiListPage,
      NiTable,
      NoData
    },
    data () {
      return {
        operationCustomRender: (text, record) => {
          return record.employee !== '合计' ? (
          <div>
            <router-link
              target="_blank"
              to={{
                path: `/operation/statistics-report/add-sub-people`,
                query: this.gotoDetailQuery(record)
              }}
            >
              查看详细
            </router-link>
          </div>
            ) : null
        },
        codeAddSubRateCustomRender: (text, record) => {
          return <div><span>{`${text}%`}</span></div>
        }
      }
    },
    computed: {
      columns () {
        if (!this.hearderData.length) return []
        const hearderData = this.hearderData
        const codeAddSubRate = hearderData.find(d => d.key === 'codeAddSubRate')
        if (codeAddSubRate) codeAddSubRate.customRender = this.codeAddSubRateCustomRender
        hearderData.push({
          title: '操作',
          titleStr: '操作',
          key: 'operation',
          dataIndex: 'operation',
          fixed: 'right',
          customRender: this.operationCustomRender
        })
        return hearderData
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const { loading, isFeatch } = useCommon()
      const title = ref('')
      const { lastUpdateDate, setLastUpdateDate } = useLastUpdateDate()
      const { exportDataLoading, exportData } = useExportXlsx({ proxy, type: 10 })
      const { searchData } = usesearchProps({
        proxy,
        type: 10
      })
      const { hearderData, getHearder } = useReportHearder({
        proxy,
        h,
        type: 10
      })
      const { dataSource, tableSort, pagination, tableChange, featchList, isWebPagination, setTableData } = useTableProps({
        proxy,
        type: 10,
        loading,
        isFeatch,
        setLastUpdateDate,
        hearderData
      })
      // 设置当前表格是否分页
      isWebPagination.value = true
      const featchHeader = async function () {
        const params = {
          type: 10
        }
        await getHearder(params, featchHeader)
      }
      const setDefaultSearchData = function () {
        const { copySearch: copySearchStr } = proxy.$route.query
        const dataObj = copySearchStr ? JSON.parse(copySearchStr) : {}
        title.value = `门店识别码加单统计 - ${dataObj.area}`
        document.title = title.value
        delete dataObj.area
        searchData.value = dataObj
      }
      const gotoDetailQuery = function (record) {
        const { subType, areaId, startTime, endTime } = searchData.value
        const params = {
          inUser: record.employee,
          areaId,
          startTime,
          endTime,
          subType
        }
        return { copySearch: JSON.stringify(params) }
      }

      const getParams = function () {
        const params = {
          type: 10,
          ...searchData.value
        }
        return params
      }

      const handleExport = function () {
        const { copySearch: copySearchStr } = proxy.$route.query
        const dataObj = copySearchStr ? JSON.parse(copySearchStr) : {}
        const params = {
          fileName: `门店识别码加单统计-${dataObj.area}-`,
          scene: 'add_sub_employee',
          ...getParams()
        }
        exportData({ params })
      }

      const getSetTableDataFunParams = function () {
        return hearderData
      }

      setDefaultSearchData()
      featchHeader()
      featchList({ params: { ...getParams() }, totalKey: 'employee' })
      return {
        lastUpdateDate,
        loading,
        searchData,
        hearderData,
        isFeatch,
        dataSource,
        tableSort,
        pagination,
        tableChange,
        gotoDetailQuery,
        exportDataLoading,
        handleExport,
        title,
        setTableData,
        getSetTableDataFunParams
      }
    },
    render () {
      const {
        lastUpdateDate,
        loading,
        dataSource,
        columns,
        tableChange,
        isFeatch,
        pagination,
        exportDataLoading,
        handleExport,
        title,
        setTableData,
        getSetTableDataFunParams
      } = this

      return (
      <page class="add-sub-area" title={title}>
        <div slot="extra">数据更新于{lastUpdateDate}</div>
        <ni-list-page push-filter-to-location={false}>
          <ni-table
            class="add-sub-area-table"
            locale={{ emptyText: <NoData is-featch={isFeatch} /> }}
            dataSource={dataSource}
            loading={loading}
            columns={columns}
            onChange={(pagination, filters, sorter) => { tableChange({ paginationObj: pagination, filters, sorter }, () => { setTableData(getSetTableDataFunParams(), 'employee') }) }}
            pagination={pagination}
            footerTotalNum={1}
          >
          <div slot="tool">
          <a-button
              style="float: right;"
              class="btn"
              icon="export"
              loading={exportDataLoading}
              onClick={handleExport}
            >
              导出
            </a-button>
          </div>
          </ni-table>
        </ni-list-page>
      </page>
      )
    }
  })
</script>
<style lang="scss">
.add-sub-area {
  position: relative;
  .ant-page-header-heading-extra {
    float: none;
    line-height: 40px;
    font-size: 12px;
    color: #9c9c9c;
  }
}
</style>
