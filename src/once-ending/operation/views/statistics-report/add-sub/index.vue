<script lang="jsx">
  import { defineComponent, h, provide, getCurrentInstance, ref, nextTick } from 'vue'
  import { NiListPage } from '@jiuji/nine-ui'
  import useCommon from '../hooks/useCommon'
  import useTableProps from '../hooks/useTableProps'
  import usesearchProps from '../hooks/usesearchProps'
  import useReportHearder from '../hooks/useReportHearder'
  import useLastUpdateDate from '../hooks/useLastUpdateDate'
  import SearchBox from './components/search-box'
  import TableBox from './components/table-box'
  import { dateFormat } from '~/util/common'
  import { message } from 'ant-design-vue'
  import moment from 'moment'
  import ClassificationModal from './components/classification-modal.vue'
  export default defineComponent({
    components: {
      NiListPage,
      SearchBox,
      TableBox,
      ClassificationModal
    },
    data () {
      return {
        operationCustomRender: (text, record) => {
          return record.area !== '合计' && record.viewDetail ? (
          <div>
            <router-link
              target="_blank"
              to={{
                path: `/operation/statistics-report/add-sub-area`,
                query: this.gotoDetailQuery(record)
              }}
            >
              查看详细
            </router-link>
          </div>
            ) : null
        },
        codeAddSubRateCustomRender: (text, record) => {
          return <div><span>{`${text}%`}</span></div>
        }
      }
    },
    computed: {
      columns () {
        if (!this.hearderData.length) return []
        const hearderData = this.hearderData
        const codeAddSubRate = hearderData.find(d => d.key === 'codeAddSubRate')
        if (codeAddSubRate) codeAddSubRate.customRender = this.codeAddSubRateCustomRender
        hearderData.push({
          title: '操作',
          titleStr: '操作',
          key: 'operation',
          dataIndex: 'operation',
          fixed: 'right',
          customRender: this.operationCustomRender
        })
        return hearderData
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const { loading, isFeatch } = useCommon()
      const { lastUpdateDate, setLastUpdateDate } = useLastUpdateDate()
      const showClassification = ref(false)
      const destroyModal = ref(true)
      const { searchData, searchOptions, getSearchOptions } = usesearchProps({
        proxy,
        type: 9
      })
      const { hearderData, getHearder } = useReportHearder({
        proxy,
        h,
        type: 9
      })
      const { dataSource, tableSort, pagination, tableChange, featchList, isWebPagination, setTableData } = useTableProps({
        proxy,
        type: 9,
        loading,
        isFeatch,
        setLastUpdateDate,
        hearderData,
        usePagination: false
      })
      // 设置当前表格是否分页
      isWebPagination.value = true
      const featchHeader = async function () {
        const params = {
          type: 9
        }
        await getHearder(params, featchHeader)
      }

      const setDefaultSearchData = function () {
        const dataObj = {
          shopType: [],
          areaId: []
        }
        // 设置订单类型默认值
        const subTypeOptions = searchOptions.value?.subType?.list || []
        const subType = subTypeOptions
          .filter(d => d.label === '销售单' || d.label === '硬件售后单')
          .map(d => d.value)
        dataObj.subType = subType
        // 设置日期默认值,默认当天
        dataObj.time = [
          dateFormat(new Date(), 'YYYY-MM-DD 00:00:00'),
          dateFormat(new Date(), 'YYYY-MM-DD 23:59:59')
        ]
        searchData.value = dataObj
      }

      const gotoDetailQuery = function (record) {
        const { subType, time } = searchData.value
        const params = {
          areaId: [`${record.areaId}`],
          area: record.area,
          subType
        }
        if (time && time.length) {
          params.startTime = time[0]
          params.endTime = time[1]
        }
        return { copySearch: JSON.stringify(params) }
      }

      const checkForm = function () {
        const {
          time
        } = searchData.value
        if (!time.length) {
          message.warning('请选择时间段')
          return true
        }
        if (
          moment(time[0])
            .add(3, 'months')
            .format('YYYY-MM-DD HH:mm:ss') < time[1]
        ) {
          message.warn('时间间隔不能超过三个月')
          return true
        }
        return false
      }

      const getParams = function (flag) {
        const { shopType, subType, areaId, time } = searchData.value
        const params = {
          type: flag === 'add_sub_area_employee' ? 910 : 9,
          shopType,
          areaId,
          subType
        }
        if (time && time.length) {
          params.startTime = time[0]
          params.endTime = time[1]
        }
        return params
      }
      provide('getParams', getParams)

      const getSearch = async function () {
        await getSearchOptions()
        setDefaultSearchData()
      }

      const getSetTableDataFunParams = function () {
        return hearderData
      }

      const getData = function () {
        if (checkForm()) return
        featchList({ current: 1, params: { ...getParams() }, totalKey: 'area' })
      }

      featchHeader()
      getSearch()
      const classification = function () {
        showClassification.value = true
      }

      const addSubConfig = function () {
        window.open('/zitidian/areaSubAddConfig', '_blank')
      }

      const afterClose = function () {
        destroyModal.value = false
        nextTick(() => {
          destroyModal.value = true
        })
      }
      return {
        lastUpdateDate,
        searchData,
        getData,
        hearderData,
        dataSource,
        tableSort,
        pagination,
        tableChange,
        gotoDetailQuery,
        setTableData,
        getSetTableDataFunParams,
        classification,
        addSubConfig,
        showClassification,
        afterClose,
        destroyModal
      }
    },
    render () {
      const {
        lastUpdateDate,
        searchData,
        getData,
        dataSource,
        columns,
        tableChange,
        pagination,
        setTableData,
        getSetTableDataFunParams,
        classification,
        addSubConfig,
        destroyModal,
        afterClose
      } = this
      const classificationModalOn = {
        'update:showClassification': val => {
          this.showClassification = val
        }
      }
      return (
      <page class="add-sub">
        <div slot="extra" class="flex flex-justify-between">
          <div>数据更新于{lastUpdateDate}</div>
          <div>
            <a-button
              class="mr-8"
              onClick={classification}
            >
              配置排除分类
            </a-button>
            <a-button
              onClick={addSubConfig}
            >
              门店默认加单配置
            </a-button>
          </div>
        </div>
        <ni-list-page push-filter-to-location={false}>
          <search-box
            search-data={searchData}
            onSearch={getData}
          ></search-box>
          <table-box
            style="margin-top:16px"
            data-source={dataSource}
            columns={columns}
            search-data={searchData}
            onTableChange={({ pagination, filters, sorter }) => { tableChange({ paginationObj: pagination, filters, sorter }, () => { setTableData(getSetTableDataFunParams(), 'area') }) }}
            pagination={pagination}
          ></table-box>
        </ni-list-page>
        {
          destroyModal && <ClassificationModal showClassification={this.showClassification} {...{ on: { ...classificationModalOn } }} onAfterClose={afterClose} />
        }
      </page>
      )
    }
  })
</script>
<style lang="scss">
.add-sub {
  position: relative;
  .ant-page-header-heading-extra {
    float: none;
    line-height: 40px;
    font-size: 12px;
    color: #9c9c9c;
  }
}
</style>
