<template>
  <a-auto-complete
    v-model="valueLocal"
    defaultOpen
    :dataSource="productOptions"
    placeholder="请输入商品名称"
    class="name-select"
    :getPopupContainer="trigger => trigger.parentElement"
    :filterOption="false"
    @search="productSearch"
    @change="changeValue"
  />
</template>
<script>
  import { defineComponent, reactive, watch, toRefs, getCurrentInstance } from 'vue'
  export default defineComponent({
    props: {
      value: {
        type: [String, Number],
        default: undefined
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const state = reactive({
        valueLocal: undefined,
        productOptions: [],
      })

      watch(() => props.value, (val) => {
        state.valueLocal = val
      }, { immediate: true })

      function productSearch (keyWord) {
        console.log('keyWord', keyWord)
        state.productOptions = []
        if (keyWord) {
          proxy.$api.store.productPid({ world: keyWord, limit: 20 }).then(res => {
            if (res.code === 0) {
              state.productOptions = res.data?.map(item => item.productName)
            } else {
              state.$message.error(res.userMsg)
            }
          })
        }
      }
      function changeValue (val) {
        proxy.$emit('input', val)
      }
      return {
        ...toRefs(state),
        productSearch,
        changeValue
      }
    }
  })
</script>
<style lang="scss" scoped>
</style>
