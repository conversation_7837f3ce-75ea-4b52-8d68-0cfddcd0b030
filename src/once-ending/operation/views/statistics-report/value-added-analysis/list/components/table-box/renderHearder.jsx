import { computed } from 'vue'
import { SALE_STATISTICS_EDIT_NOTE } from '@operation/store/modules/statistics-report/action-types'
import { message } from 'ant-design-vue'

export default (ctx) => {
  const { props: { record, type }, parent } = ctx
  const hasRank = computed(() => parent.$store.getters.userInfo.Rank.includes('bbzs')).value

  const editNote = async function () {
    const { key: field, description: note, isEdit } = record
    if (!isEdit) {
      record.isEdit = true
      return
    }
    const params = {
      type,
      field,
      note
    }
    const res = await parent.$store.dispatch(
      `operation/statisticsReport/${SALE_STATISTICS_EDIT_NOTE}`,
      params
    )
    if (res) message.success('修改成功')
    record.isEdit = false
  }
  const cancelNote = function () {
    record.description = record.oldDescription
    record.isEdit = false
  }
  return (
    <span>
      { record.titleStr }
      { hasRank
        ? <a-popover overlay-class-name="title-popover">
          <template slot="content">
            <div>
              {
                record.isEdit
                  ? <span style='width:300px'>
                    <a-textarea
                      autoSize={{ minRows: 2, maxRows: 4 }}
                      style={{ borderRadius: '2px', width: '280px' }}
                      value={ record.description }
                      maxLength={ 500 }
                      onInput={e => { record.description = e.target.value }}
                      placeholder="请输入500字以下注释"
                      allowClear
                    />
                    <p class="font-12 text-right">{ record.description?.length ?? 0 } / 500 </p>
                  </span>
                  : <p class="font-14 black" style={{ minWidth: '180px', maxWidth: '280px' }}>{ record.description || '未添加注释' } </p>
              }
              <div class="text-right mt-10">
                { record.isEdit && <a-button size="small" onClick={ cancelNote }>取消</a-button> }
                <a-button
                  size="small"
                  class="ml-8"
                  type="primary"
                  onClick={ editNote }>
                  { record.isEdit ? '确定' : '编辑' }
                </a-button>
              </div>
            </div>
          </template>
          <a-icon type="question-circle" class="ml-8 grey"/>
        </a-popover>
        : record.description && (
          <a-tooltip title={ record.description }>
            <a-icon type="question-circle" class="ml-8 grey"/>
          </a-tooltip>
        )
      }
    </span>
  )
}
