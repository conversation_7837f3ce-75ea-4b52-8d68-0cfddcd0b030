<script type="jsx" lang="jsx">
  import { toRefs, getCurrentInstance, ref } from 'vue'
  import { NiTable } from '@jiuji/nine-ui'
  import FormRecovery from './form-recovery.jsx'
  import NoData from './no-data.vue'

  import { useValueAddedState } from '../../model/useState.js'
  import useActions from '../../model/useActions'
  import axios from 'axios'
  import statisticsReport from '@operation/api/statistics-report'

  export default {
    components: {
      NiTable,
      FormRecovery,
      NoData
    },
    setup (_) {
      const { proxy } = getCurrentInstance()
      const {
        state,
        setFormFilter,
      } = useValueAddedState()
      const { tableChange, featchList } = useActions()

      const exportExcel = () => {
        if (!state.searchParams) {
          proxy.$message.info('请先点击查询')
          return
        }
        if (!state.dataSource?.length) {
          proxy.$message.info('未查询到相关数据')
          return
        }
        const params = { ...state.searchParams }
        exportData(params)
      }

      const exportDataLoading = ref(false)
      const exportData = function (params) {
        axios({
          method: 'post',
          url: statisticsReport.exportValueAddedAnalysisData(),
          data: params,
          timeout: 1000 * 180,
          responseType: 'blob',
          headers: {
            Authorization: proxy.$store.state.token
          }
        }).then(res => {
          exportDataLoading.value = false
          const date = new Date()
          const link = document.createElement('a')
          let blob = new Blob([res.data], { type: 'application/x-excel' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = `增值业务分析${date.getFullYear()}年${date.getMonth() +
            1}月${date.getDate()}日${date.getHours()}时${date.getMinutes()}分${date.getSeconds()}秒.xlsx`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        })
      }

      return {
        ...toRefs(state),
        exportDataLoading,
        exportExcel,
        setFormFilter,
        tableChange,
        featchList
      }
    },
    render () {
      const {
        formFilter,
        isFeatch,
        loading,
        hearderData,
        dataSource,
        pagination,
        exportDataLoading,

        setFormFilter,
        exportExcel,
        tableChange,
        featchList
      } = this
      const tips = () => (
        <div>
          <p>基准值代表某项业务全公司的平均水平，显示在单独门店或地区后方的基准值是根据公司基准值和地区销量进行计算的,如果该地区的某项业务未达到基准值,则说明没有达到公司平均水平。</p>
          <p>公司基准值=单项业务全公司自营总量/公司自营手机总销量</p>
          <p>显示列表中的地区基准值=公司基准值*地区总销量</p>
          <p>基准值的取值规则:</p>
          <p>①每天凌晨,系统自动取前30天的数据计算，算好的基准值存入今天的表格</p>
          <p>②调整完成后，填充2018年1月1日起的基准值数据，更早的数据不做基准值计算</p>
          <p>③如果选取一段时间做统计，基准值计算以取该时间段所有基准值的平均数为准</p>
        </div>
      )
      return (
         <NiTable
          class="data-table"
          locale={{ emptyText: <NoData onfetchDataHandle={() => featchList(1)}/> }}
          loading={ loading }
          footerTotalNum={ 1 }
          dataSource={ dataSource }
          columns={ hearderData }
          pagination={ pagination }
          onChange={ tableChange }
          onColumnsSetting={ checkedKeys => {
            setFormFilter(checkedKeys, 'items')
          } }
        >
          <div slot="tool" class="flex">
            {/* <a-form-item wrapper-col={{ span: 24 }} class="mb-0">
              <a-checkbox
                class="mr-16"
                disabled={ formFilter.basicStandardIsDisabled }
                checked={ formFilter.basicStandard }
                onChange={ e => {
                  setFormFilter(e.target.checked, 'basicStandard')
                } } >
                <a-tooltip style={{ width: '500px' }} title={ tips } arrowPointAtCenter>
                  基准值
                  <a-icon type="question-circle" class="grey" style="margin: 0 5px"/>
                </a-tooltip>
              </a-checkbox>
            </a-form-item> */}
            <FormRecovery/>
          </div>
          <template slot="action">
            <a-button
              icon="export"
              loading={ exportDataLoading }
              onClick={ exportExcel }>
              导出
            </a-button>
          </template>
        </NiTable>
      )
    }
  }
</script>

<style lang="scss" scoped>
  .mb-0{
    margin-bottom: 0;
  }
  :deep(.nine-table-bar-action) {
    display: flex;
    align-items: center;
  }
  :deep(.ant-table-thead > tr > th)  {
    font-weight: bold;
  }
  :deep(.checkList) {
    max-height: 60vh;
    overflow: auto;
  }
  .data-table{
    .ant-btn-link{
      color: #333;
    }
  }
</style>
