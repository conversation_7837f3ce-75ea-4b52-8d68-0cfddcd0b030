import { message } from 'ant-design-vue'
import { useValueAddedState } from '../../model/useState.js'
import useActions from '../../model/useActions'
import { SAVE_AVGRECOVER_PROFIT } from '@operation/store/modules/statistics-report/action-types'
import { getCurrentInstance } from 'vue'
export default (ctx) => {
  const stateObj = useValueAddedState()
  const {
    state: { saveLoading, formFilter },
    setSaveLoading,
    setFormFilter
  } = stateObj
  const { featchEnums } = useActions(stateObj)
  const { proxy } = getCurrentInstance()
  const hasbbxs = proxy.$tnt.xtenant === 0 || proxy.$store.state.userInfo.Rank.includes('bbxs')
  const handleSubmit = async () => {
    const { avgRecoverProfit } = formFilter
    if (Number(avgRecoverProfit).toString() === 'NaN') {
      message.error('请输入数字')
      return
    }
    setSaveLoading(true)
    const res = await ctx.parent.$store.dispatch(
      `operation/statisticsReport/${SAVE_AVGRECOVER_PROFIT}`,
      { avgRecoverProfit: Number(avgRecoverProfit) }
    )
    setSaveLoading(false)
    if (res) {
      featchEnums()
    }
  }
  const renderLabel = () => (
    <span>
      <a-tooltip placement='bottom' title={ '影响“回收单毛”字段，回收单毛=有价回收转化*平均回收单毛，可根据不同公司的状况自行设置，预估产生的利润。' }>
        平均回收单毛
        <a-icon
          type="question-circle"
          class="grey"
          style="margin:2px 5px 0"/>
      </a-tooltip>
    </span>
  )
  return (
    <a-form
      layout="inline"
      form={ formFilter }
      label-col={{ span: 9 }}
      wrapper-col={{ span: 15 }}
    >
      <a-form-item label={ renderLabel }>
        <a-input
          allowClear
          disabled={ !hasbbxs }
          placeholder="请输入数字"
          value={ formFilter.avgRecoverProfit }
          onChange={ e => {
            setFormFilter(e.target.value, 'avgRecoverProfit')
          } }
        />
      </a-form-item>
      <a-form-item >
        <a-button
          type="primary"
          disabled={ !formFilter.avgRecoverProfit || !hasbbxs }
          loading={ saveLoading }
          onClick={ handleSubmit }>
          保存
        </a-button>
      </a-form-item>
    </a-form>
  )
}
