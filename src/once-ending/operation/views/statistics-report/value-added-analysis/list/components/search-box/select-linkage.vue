<template>
  <div class="inline-flex select-linkage">
    <ni-filter-item label="分类" style="width:50%" >
      <a-tree-select
        allowClear
        showArrow
        treeCheckable
        placeholder='请选择分类'
        treeNodeFilterProp='title'
        :maxTagCount="1"
        :dropdownStyle="{ maxHeight: '300px' }"
        :treeData="cidTree"
        v-model="form.cid"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
    </ni-filter-item>
    <ni-filter-item label="品牌" style="width:50%" >
      <a-select
        showArrow
        allowClear
        placeholder="请先选择分类"
        mode="multiple"
        :maxTagCount="1"
        :options="brandOptions"
        v-model="form.brandIds"
        optionFilterProp="children"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
    </ni-filter-item>
  </div>
</template>
<script type="text/jsx">
  import { ref, watch, getCurrentInstance } from 'vue'
  import { BRAND_OPTIONS } from '@operation/store/modules/statistics-report/action-types'
  import { NiFilterItem } from '@jiuji/nine-ui'

  export default {
    name: 'select-linkage',
    components: {
      NiFilterItem,
    },
    props: {
      form: {
        type: Object,
        default: () => ({})
      },
      setForm: { // 重新赋值form字段值function
        type: Function,
      },
      cidTree: { // 分类[cid]枚举数据[cidTree]暂无单独接口（后续可添加单独接口）
        type: Array,
        default: () => ([])
      },
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const brandOptions = ref([])
      const setBrandOptions = val => { brandOptions.value = val || [] }

      const getBrandOptions = async (cids) => {
        if (!cids?.length) {
          setBrandOptions()
          return
        }
        setBrandOptions()
        const res = await proxy.$store.dispatch(`operation/statisticsReport/${BRAND_OPTIONS}`, { cids })
        if (res && res.data) {
          const brandOptions = res.data.map(it => ({
            label: it.name,
            value: it.id
          }))
          setBrandOptions(brandOptions)
        }
      }
      watch(
        () => props.form.cid,
        val => {
          getBrandOptions(val) // 所选分类的变动，请求品牌枚举数据
          if (!val?.length) { // 清空分类，则清除品牌枚举
            props.setForm([], 'brandIds')
          }
        },
        { immediate: true }
      )

      return {
        brandOptions
      }
    }
  }
</script>
<style>
  @media screen and (min-width: 2045px) and (max-width: 2374px) {
    .select-linkage {
      width: 33.33%;
    }
  }
  @media screen and (min-width: 1725px) and (max-width: 2044px) {
    .select-linkage {
      width: 40%;
    }
  }
  @media screen and (min-width: 1395px) and (max-width: 1724px) {
    .select-linkage {
      width: 50%;
    }
  }
  @media screen and (min-width: 1065px) and (max-width: 1394px) {
    .select-linkage {
      width: 66.66%;
    }
  }
  @media screen and (min-width: 735px) and (max-width: 1064px) {
    .select-linkage {
      width: 100%;
    }
  }
</style>
