<template>
  <ni-filter
    class="relative"
    :form="formFilter"
    :loading="loading"
    :unfoldCount="5"
    @filter="doSearch"
    :label-width="85"
    :immediate="false"
    >
    <ni-filter-item label="业务类型" v-if="$tnt.xtenant < 1000">
      <a-select
        showArrow
        placeholder="请选择业务类型"
        :options="bizTypeOptions"
        v-model="formFilter.bizType"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
    </ni-filter-item>
    <ni-filter-item label="统计方案">
      <a-select
        showArrow
        placeholder="请选择统计方案"
        :options="aggregationDimensionOptions"
        v-model="formFilter.aggregationDimension"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
    </ni-filter-item>
    <ni-filter-item label="地区">
      <ni-area-select
        multiple
        allowClear
        class="area-selector"
        placeholder="请选择地区"
        showCheckedStrategy="TreeSelect.SHOW_ALL"
        :ranks="['2c5']"
        :maxTagCount="1"
        v-model="formFilter.areaIds"
      />
    </ni-filter-item>
    <ni-filter-item label="交易时间">
      <a-range-picker
        v-model="formFilter.times"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        :allow-clear="false"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
        :ranges="ranges"
      />
    </ni-filter-item>
    <ni-filter-item label="门店类别">
      <a-select
        showArrow
        allowClear
        placeholder="请选择门店类别"
        v-model="formFilter.areaType"
        :options="areaTypeOptions"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
    </ni-filter-item>
    <ni-filter-item label="分类">
      <a-tree-select
        allowClear
        showArrow
        treeCheckable
        placeholder='请选择分类'
        treeNodeFilterProp='title'
        :maxTagCount="1"
        :dropdownStyle="{ maxHeight: '300px' }"
        :treeData="cidTree"
        v-model="formFilter.cid"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
    </ni-filter-item>
    <ni-filter-item label="品牌">
      <div ref="batchExportContent" class="batchExport-content">
      <a-select
            style="width: 100%;"
            placeholder="请先选择分类"
            :get-popup-container="(triggerNode) => triggerNode.parentNode"
            show-arrow
            :max-tag-count="0"
            mode="multiple"
            v-model="formFilter.brandIds"
            option-filter-prop="label"
            @focus="selectFocusOrBlur"
            @blur="selectFocusOrBlur"
            @select="handleSelect"
            @search="selectSearch"
            :filterOption="false"
            @popupScroll="selectPopupScroll"
            allow-clear
            :getPopupContainer='getPopupContainer()'
          >
        <div slot="dropdownRender" slot-scope="menu">
          <div style="padding: 4px 8px;margin-left: 4px">
            <a-checkbox v-model="allChecked" @change="batchExportAllChecked">全选</a-checkbox>
          </div>
          <a-divider style="margin: 4px 0;" />
          <v-nodes :vnodes="menu" />
        </div>
        <a-select-option :value="item.value" :label="item.label" v-for="(item, index) in selectOptions" :key="index">
          <label class="ant-checkbox-wrapper">
          <span class="ant-checkbox" :class="formFilter.brandIds.includes(item.value) ? 'ant-checkbox-checked':''">
            <input type="checkbox" class="ant-checkbox-input" value="">
            <span class="ant-checkbox-inner"></span>
          </span>
          </label>
          {{ item.label }}
        </a-select-option>
      </a-select>
      </div>
    </ni-filter-item>
    <ni-filter-item label="商品名称">
      <Product
        allowClear
        v-model="formFilter.goodsName"
      />
    </ni-filter-item>
    <ni-filter-item label="门店级别">
      <a-select
        showArrow
        allowClear
        placeholder="请选择门店级别"
        v-model="formFilter.areaLevel"
        :options="areaLevelOptions"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
    </ni-filter-item>
    <ni-filter-item label="门店属性">
      <a-tree-select
        showArrow
        showSearch
        treeCheckable
        allowClear
        placeholder="请选择门店属性"
        tree-node-filter-prop="title"
        v-model="formFilter.areaAttribute"
        :dropdownStyle="{ maxHeight: '300px' }"
        :maxTagCount="1"
        :treeData="areaAttributeTree"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
        :replaceFields="{
          title:'key'
        }"
      />
    </ni-filter-item>
    <ni-filter-item label="门店类型">
      <a-select
        showArrow
        allowClear
        placeholder="请选择门店类型"
        v-model="formFilter.areaCategory"
        :options="areaCategoryOptions"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
    </ni-filter-item>
    <ni-filter-item label="订单类型">
<!--       <a-select-->
<!--        showArrow-->
<!--        allowClear-->
<!--        placeholder="请选择订单类型"-->
<!--        mode="multiple"-->
<!--        :maxTagCount="1"-->
<!--        v-model="formFilter.orderType"-->
<!--        :options="orderTypeOptions"-->
<!--        optionFilterProp="children"-->
<!--        :getPopupContainer="triggerNode => triggerNode.parentNode"-->
<!--      />-->
      <SelectCheckAll v-model="formFilter.orderType" :maxTagCount="1" :options="orderTypeOptions || []"/>
    </ni-filter-item>
    <ni-filter-item label="大件价位" v-if="$tnt.xtenant === 0">
      <a-select
        showArrow
        allowClear
        placeholder="请选择大件价位"
        v-model="formFilter.priceFilter"
        :options="mockOptions.priceFilter"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
        />
    </ni-filter-item>
    <ni-filter-item label="大件单价" v-if="formFilter.aggregationDimension === 'mobile'">
       <a-select
        showArrow
        allowClear
        placeholder="请选择大件单价区间"
        v-model="formFilter.bigPrices"
        :options="mockOptions.bigPrices"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
    </ni-filter-item>
    <ni-filter-item label="自定义搭售" v-if="this.$tnt.xtenant < 1000">
      <a-input-group class="flex" compact>
            <a-select
            style="width:40%"
              v-model="formFilter.customTying"
              @change="customTyingChange"
              :get-popup-container="triggerNode => triggerNode.parentNode"
              class="customTying"
              showArrow
              >
              <template v-for="d in typeOptions">
            <a-select-option :title="d.label" :key="d.value" :value="d.value">{{d.label}}</a-select-option>
          </template>
              </a-select>
              <productNameSelect v-model="formFilter.customTyingKey" style="width: 60%;" v-if="formFilter.customTying === 'productName'"/>
              <ni-category
              v-else-if="formFilter.customTying === 'cid'"
              ref="category"
              treeCheckable
              tree-node-filter-prop="title"
              showSearch
              allowClear
              :dropdownStyle="{ maxHeight: '300px' }"
              style="width: 60%;"
              :maxTagCount="1"
              class="category"
              v-model="formFilter.customTyingKey" />
              <a-input
              v-else
              style="width: 60%"
              :placeholder="getPlaceholder"
              v-model="formFilter.customTyingKey"
              allowClear/>
          </a-input-group>
      </ni-filter-item>
  </ni-filter>
</template>

<script type="text/jsx">
  import moment from 'moment'
  import { computed, onMounted, toRefs, ref } from 'vue'
  import Product from '~/components/staff/auto-goods-name'
  import { NiFilter, NiFilterItem, NiAreaSelect, NiCategory } from '@jiuji/nine-ui'
  import { mockOptions, typeOptions, bizTypeOptions, ranges } from '../../model/constants.js'
  import { useValueAddedState } from '../../model/useState.js'
  import useActions from '../../model/useActions'
  import useSelectLinkage from '../../model/useSelectLinkage.js'
  import productNameSelect from './product-name-select'
  import SelectCheckAll from '../../../../bussiness-report/components/selectCheckAll.vue'

  export default {
    name: 'search-box',
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect,
      Product,
      NiCategory,
      productNameSelect,
      SelectCheckAll,
      VNodes: {
        functional: true,
        render: (h, ctx) => ctx.props.vnodes
      },
    },
    setup () {
      const batchExportContent = ref(null)
      const {
        state,
        state: { formFilter },
        setFormFilter
      } = useValueAddedState()
      const { featchEnums, featchList } = useActions()
      const { cateState, selectPopupScroll, selectFocusOrBlur, selectSearch, handleSelect, allChecked, batchExportAllChecked } = useSelectLinkage(formFilter, setFormFilter)

      /**
       * 转换后端返回的选项下拉枚举
       * @field { String }
       * @isTree { Boolean } treeData树形选项传true
       * @return { Array } 转换成ant组件所需字段数据
       */
      const selectOptions = (field, isTree) => {
        const data = state.optionsEnum?.find(item => item.key === field)
        const toOptions = (arr = []) => arr.map(item => ({ value: item.code, label: item.name, ...item }))
        return isTree ? (data?.tree.map(it => ({ ...it, title: it.key })) || []) : toOptions(data?.list)
      }

      const doSearch = () => { featchList(1) }

      const getPopupContainer = () => {
        return () => batchExportContent.value
      }

      onMounted(() => {
        featchEnums()
      })

      const getPlaceholder = computed(() => {
        const { customTying } = formFilter
        let placeholder = '请输入商品名称'
        const label = typeOptions.find(it => it.value === customTying)?.label
        if (customTying === 'productId' || customTying === 'skuId') {
          placeholder = `多个${label}之间用英文,分隔`
        }
        return placeholder
      })
      function customTyingChange () {
        formFilter.customTyingKey = undefined
      }
      return {
        batchExportContent,
        moment,
        ...toRefs(cateState),
        mockOptions,
        typeOptions,
        formFilter,
        loading: computed(() => state.loading),
        cidTree: computed(() => selectOptions('cid', true)),
        areaAttributeTree: computed(() => selectOptions('areaAttribute', true)),
        areaLevelOptions: computed(() => selectOptions('areaLevel')),
        areaTypeOptions: computed(() => selectOptions('areaType')),
        itemsOptions: computed(() => selectOptions('items')),
        aggregationDimensionOptions: computed(() => selectOptions('aggregationDimension')),
        areaCategoryOptions: computed(() => selectOptions('areaCategory')),
        orderTypeOptions: computed(() => selectOptions('orderType')),

        doSearch,
        setFormFilter,
        selectPopupScroll,
        handleSelect,
        selectSearch,
        selectFocusOrBlur,
        getPlaceholder,
        customTyingChange,
        ranges,
        bizTypeOptions,
        getPopupContainer,
        allChecked,
        batchExportAllChecked
      }
    },
  }
</script>
<style lang="scss" scoped>
  :deep(.ant-select-selection--multiple){
    max-height: 32px;
    .ant-select-selection__choice {
      .ant-select-selection__choice__content {
        max-width: 110px;
      }
    }
  }
  :deep(.category) {
    .ant-select-selection--multiple .ant-select-selection__choice {
      max-width: 45%;
      .ant-select-selection__choice__content {
        max-width: 50px;
      }
    }
  }
  .batchExport-content {
    :deep(.ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-selected .ant-select-selected-icon, .ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-selected:hover .ant-select-selected-icon) {
      display: none;
    }
    :deep(.ant-select-dropdown-menu-item) {
      i {
        display: none;
      }
    }
  }
</style>
