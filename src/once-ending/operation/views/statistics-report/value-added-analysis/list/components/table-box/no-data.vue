<script lang="jsx">
  import { defineComponent } from 'vue'

  export default defineComponent({
    setup (props, { emit }) {
      const fetchDataHandle = () => {
        emit('fetchDataHandle')
      }
      return {
        fetchDataHandle
      }
    },
    render () {
      const { fetchDataHandle } = this
      return (
        <div class="no-data">
          <img
            class="no-data-img"
            src="https://img2.ch999img.com/newstatic/14450/04349c93014c93f0.png"
          />
          <p class="no-query">
            点击<span class="blue pointer" onClick={() => fetchDataHandle()}>查询</span>数据
          </p>
        </div>
      )
    }
  })
</script>
<style lang="scss" scoped>
.no-data-img {
  height: 140px;
  width: 140px;
}
.no-data-text {
  width: 160px;
  height: 16px;
  font-size: 16px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.35);
  line-height: 16px;
  margin-top: 24px;
  text-align: center;
  width: 100%;
}
.no-query {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.35);
  line-height: 32px;
  margin-top: 16px;
  text-align: center;
  width: 100%;
}
</style>
