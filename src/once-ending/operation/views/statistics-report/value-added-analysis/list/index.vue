<script type="text/javascript" lang="jsx">
  import { provide, getCurrentInstance } from 'vue'
  import { createValueAddedState } from './model/useState.js'
  import uselastUpdateDate from './model/uselastUpdateDate.js'
  import useApiLastUpdateTime from '../../hooks/useApiLastUpdateTime'

  import { NiListPage } from '@jiuji/nine-ui'
  import SearchBox from './components/search-box'
  import TableBox from './components/table-box'

  export default {
    components: {
      NiListPage,
      SearchBox,
      TableBox
    },
    setup () {
      const { proxy } = getCurrentInstance()
      createValueAddedState(provide)
      const { lastUpdateDate } = uselastUpdateDate()
      const params = {
        id: 4,
        type_: proxy.$tnt.xtenant === 0 ? 1 : 2
      }
      const { lastNewUpdateDate, getLastUpdateDate } = useApiLastUpdateTime(params)
      getLastUpdateDate()
      return {
        lastUpdateDate,
        lastNewUpdateDate
      }
    },
    render () {
      const { lastUpdateDate, lastNewUpdateDate } = this
      return (
        <page class="value-added">
        <div slot="extra">数据更新于{ lastNewUpdateDate || lastUpdateDate }</div>
          <NiListPage push-filter-to-location={ false }>
            <SearchBox class="mb-16"/>
            <TableBox/>
          </NiListPage>
        </page>
      )
    }
  }
</script>
<style scoped>
  :deep(.ant-page-header-heading-extra) {
    float: left;
    font-size: 12px;
    margin-top: 12px;
    color: #777;
  }
</style>
