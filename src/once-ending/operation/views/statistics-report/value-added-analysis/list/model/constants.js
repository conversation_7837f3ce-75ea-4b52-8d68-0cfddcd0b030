import moment from 'moment/moment'

moment.locale('zh-cn', {
  week: {
    dow: 1,
  }
})

export const mockOptions = {
  priceFilter: [
    {
      label: '>=1800',
      value: 1800
    }
  ],
  bigPrices: [
    {
      label: '8000以上',
      value: '8000-'
    },
    {
      label: '7500-7999',
      value: '7500-7999'
    },
    {
      label: '7000-7499',
      value: '7000-7499'
    },
    {
      label: '6500-6999',
      value: '6500-6999'
    },
    {
      label: '6000-6499',
      value: '6000-6499'
    },
    {
      label: '5500-5999',
      value: '5500-5999'
    },
    {
      label: '5000-5499',
      value: '5000-5499'
    },
    {
      label: '4500-4999',
      value: '4500-4999'
    },
    {
      label: '4000-4499',
      value: '4000-4499'
    },
    {
      label: '3500-3999',
      value: '3500-3999'
    },
    {
      label: '3000-3499',
      value: '3000-3499'
    },
    {
      label: '2500-2999',
      value: '2500-2999'
    },
    {
      label: '2000-2499',
      value: '2000-2499'
    },
    {
      label: '1500-1999',
      value: '1500-1999'
    },
    {
      label: '1000-1499',
      value: '1000-1499'
    },
    {
      label: '700-999',
      value: '700-999'
    },
    {
      label: '400-699',
      value: '400-699'
    },
    {
      label: '0-399',
      value: '0-399'
    }
  ]
}
export const noNeedColorKey = [
  'count', // 1销量
  'orderCount', // 2总单量
  'onlyMobileOrderCount', // 3裸单量
  'accessoriesProfit', // 4手机配件毛利
  'smartAccessoriesProfit', // 5智能产品毛利
  'recyclingCount', // 6总回收量
  'validRecyclingCount', // 7有价回收量
  'recyclingPerformance', // 8回收业绩量
  'officialServiceGrossProfit', // 9官方服务毛利
  'selfServiceSale', // 10自营服务销售额
  'averageProfit'
]

// 商品分类下拉options
export const typeOptions = [
  {
    label: '分类',
    value: 'cid'
  },
  {
    label: '商品ID',
    value: 'productId'
  },
  {
    label: 'PPID',
    value: 'skuId'
  },
  {
    label: '商品名称',
    value: 'productName'
  },
]

export const bizTypeOptions = [
  {
    label: '新机',
    value: 0
  },
  {
    label: '良品',
    value: 1
  }
]

export const ranges = {
  今天: [moment(), moment()],
  昨天: [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
  当周: [moment().startOf('week'), moment().endOf('week')],
  上周: [moment().subtract(1, 'weeks').startOf('week'), moment().subtract(1, 'weeks').endOf('week')],
  当月: [moment().startOf('month'), moment().endOf('month')],
  上月: [moment().subtract(1, 'months').startOf('month'), moment().subtract(1, 'months').endOf('month')],
}
