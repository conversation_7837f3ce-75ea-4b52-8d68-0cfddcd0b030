
import { getCurrentInstance, ref } from 'vue'
import { message } from 'ant-design-vue'
import moment from 'moment'
import { cloneDeep } from '~/util/common.js'
import { noNeedColorKey } from './constants.js'
import useLastUpdateDate from './uselastUpdateDate.js'
import {
  QUERY_CONDITION,
  QUERY_STAT_DATA_HEADER,
  QUERY_VALUE_ADDDED_ANALYSIS_DATA
} from '@operation/store/modules/statistics-report/action-types'

import RenderHearder from '../components/table-box/renderHearder.jsx'
import { useValueAddedState } from './useState.js'
import styled from '../style.scss'
import BigNumber from 'bignumber.js'

export default function useActions () {
  const instance = getCurrentInstance().proxy

  const { setLastUpdateDate } = useLastUpdateDate()
  const {
    state,
    setLoading,
    setIsFeatch,
    setOptionsEnum,
    setCopyAvgRecoverProfit,
    setPagination,
    setTableSort,
    setHearderData,
    setAllItems,
    setDefaultShowItems,
    setAllAreaData,
    setDataSource,
    setDataSourceAll,
    setDataSourceTotal,
    setSearchParams,
    setFormFilter,
  } = useValueAddedState()
  const defaultShowItems = ref([])
  const cacheA = ['DIYTyingSaleCount', 'DIYTyingSaleRatio']

  const featchEnums = async () => {
    const res = await instance.$store.dispatch(
      `operation/statisticsReport/${QUERY_CONDITION}`,
      { type: state.formFilter.type }
    )
    if (res) {
      const { data = [] } = res
      setOptionsEnum(data)
      // 设置统计项
      const itemEnums = data?.find(item => item.key === 'items')
      const itemCodeAarry = itemEnums?.list?.map(item => item.code)
      defaultShowItems.value = itemEnums?.list?.filter(item => item.selected)?.map(item => item.code)

      setAllItems(itemCodeAarry) // 统计项所有值
      setDefaultShowItems(defaultShowItems.value) // 默认需要显示的数据
      // 设置平均回收单毛
      const avgRecoverProfit = data?.find(item => item.key === 'avgRecoverProfit')
      setCopyAvgRecoverProfit(avgRecoverProfit?.value) // 点击重置参数时需要
      setFormFilter(avgRecoverProfit?.value, 'avgRecoverProfit')
      featchHeader() // 查询时再触发请求头，拿到checkedKeys会滞后，页面加载时触发请求表头，得到checkedKeys（items）再用此参数去请求数据
    }
  }
  const selectOptions = (field, isTree) => {
    const data = state.optionsEnum?.find(item => item.key === field)
    const toOptions = (arr = []) => arr.map(item => ({ value: item.code, label: item.name }))
    return isTree ? (data?.tree.map(it => ({ ...it, title: it.key })) || []) : toOptions(data?.list)
  }
  // 查询默认表头
  const featchHeader = async () => {
    const {
      formFilter: { type, aggregationDimension, customTyingKey },
      allItems
    } = state

    const payload = {
      type,
      items: allItems,
      aggregationDimension,
    }
    payload.items = allItems.filter(it => !cacheA.includes(it))
    if (customTyingKey?.length) {
      payload.items = allItems.concat(cacheA)
    }
    const res = await instance.$store.dispatch(
      `operation/statisticsReport/${QUERY_STAT_DATA_HEADER}`,
      payload
    )
    if (res) {
      setHearderData()
      const { header = [] } = res.data
      const h = instance.$createElement
      // 添加百分号：门店-->裸单率[onlyMobileOrderRatio]、有价回收转化[validRecyclingTransferRatio]、总回收转化[recyclingTransferRatio]、
      const needPercentFeild = ['onlyMobileOrderRatio', 'validRecyclingTransferRatio', 'recyclingTransferRatio', 'shelTyingRate', 'filmTyingRate', 'shelPackageTyingRate', 'filmPackageTyingRate', 'careTyingRate', 'shieldTyingRate', 'earphoneTyingRate', 'annualCardTyingRate', 'chargerTyingRate', 'DIYTyingSaleRatio']
      header.forEach(d => {
        d.titleStr = d.title
        d.description = d.description || undefined
        d.oldDescription = d.description
        d.isEdit = false
        d.title = () => <RenderHearder record={d} type={type}/>
        d.dataIndex = d.key
        d.sorter = d.sorting
        d.hide = !state.defaultShowItems.includes(d.key)
        d.customRender = (text, record) => {
          if (text == null) return '-'
          const needPercent = needPercentFeild.includes(d.key)
          if (record.area === '合计' || Number(text).toString() === 'NaN') return <i>{text}</i>
          if (instance.$tnt.xtenant < 1000) {
            if (['area', 'bigArea', 'smallArea'].includes(aggregationDimension)) {
              // 门店维度出了员工：需要与全地区做数据对比。大于全地区数量为'green'，小于为'red'
              const cacheAllData = state.allAreaData[d.key]
              let color = ''
              if (cacheAllData && text) {
                const compareData = new BigNumber(text).minus(cacheAllData).div(cacheAllData)
                color = compareData >= 0.05 ? 'green' : compareData <= -0.05 ? 'red' : ''
              }
              const aggregationDimensionOptions = selectOptions('aggregationDimension')
              const needShowTip = !aggregationDimensionOptions?.find(it => it.label === d.title) && record.areaAttributeFlag === '1'
              const title = () => <i class={ needPercent ? 'percent' : '' }>全区平均数据: { state.allAreaData[d.key] }</i>
              return <span>
                {
                  needShowTip ? ( // 需要颜色提示的
                    <a-tooltip title={ title }>
                      <i class={ [color, needPercent ? 'percent' : ''] }>{ text }</i>
                    </a-tooltip>
                  ) : text
                }
              </span>
            } else {
              return <i class={ needPercent ? 'percent' : '' }>{ text }</i>
            }
          }
          if (aggregationDimension === 'area') {
            // 门店维度【area】：需要与全地区做数据对比。大于全地区数量为'green'，小于为'red'
            const color = text > state.allAreaData[d.key]
              ? 'green'
              : text === state.allAreaData[d.key] ? '' : 'red'
            const noNeedColor = noNeedColorKey.includes(d.key)
            const title = () => <i>全区平均数据: { state.allAreaData[d.key] }</i>
            return <span>
              {
                noNeedColor ? text : ( // 需要颜色提示的
                  <a-tooltip title={ title}>
                    <i class={ [color, needPercent ? 'percent' : ''] }>{ text }</i>
                  </a-tooltip>
                )
              }
            </span>
          } else if (aggregationDimension === 'mobile') {
            // 商品维度【mobile】：显示基准值
            const title = record[`${d.key}BasicStandard`] !== undefined && <i>基准值：{ record[`${d.key}BasicStandard`] }</i>
            return (
              <a-tooltip title={ title }>
                <i class={ needPercent ? 'percent' : '' }>{ text }</i>
              </a-tooltip>
            )
          } else {
            return <i class={ needPercent ? 'percent' : '' }>{ text }</i>
          }
        }
      })
      setHearderData(header)
    }
  }
  const featchList = async function (current) {
    featchHeader()
    setDataSource()
    if (_checkForm()) return // 检查筛选必要或参数类型
    const params = _getParams() // 获取筛选参数
    if (current) {
      setPagination(current, 'current')
    }
    if (!params.avgRecoverProfit && state.copyAvgRecoverProfit) { // 解决点击重置按钮【平均回收单毛】置空问题
      setFormFilter(state.copyAvgRecoverProfit, 'avgRecoverProfit')
      params.avgRecoverProfit = state.copyAvgRecoverProfit
    }
    if (!params.items?.length) {
      params.items = defaultShowItems.value
    }
    params.customTyingKey?.length && (params.items = params.items.concat(cacheA))
    delete params.basicStandardIsDisabled

    params.startTime = moment(params.times[0]).format('YYYY-MM-DD')
    params.endTime = moment(params.times[1]).format('YYYY-MM-DD')
    delete params.times

    if (params.bigPrices) {
      const bigPricesArry = params.bigPrices.split('-')
      params.bigPriceMin = parseInt(bigPricesArry[0])
      params.bigPriceMax = parseInt(bigPricesArry[1]) || undefined
      delete params.bigPrices
    }
    const { customTying, customTyingKey } = state.formFilter
    if (customTyingKey) {
      params.customTying = {}
      const values = (['productId', 'skuId'].includes(customTying) ? customTyingKey.replace(/，/g, ',') : customTyingKey)
      params.customTying[customTying] = values
    }
    params.tenantId = instance.$tnt.tenantId || 10000
    setSearchParams(cloneDeep(params)) // 数据导出时需要的最终查询参数
    setLoading(true)
    const res = await instance.$store.dispatch(
      `operation/statisticsReport/${QUERY_VALUE_ADDDED_ANALYSIS_DATA}`,
      params
    )
    setLoading(false)
    if (res) {
      setIsFeatch(true)
      setLastUpdateDate()
      const { data = [], total = {}, allAreaData = {} } = res.data || {}
      // 门店维度需要做数据对比（赋值所有区域数据）
      setAllAreaData(allAreaData || {})
      // 后端未做分页，前端分页
      setPagination(data?.length, 'total') // 数据总条数
      setDataSourceAll(cloneDeep(data)) // 所有数据
      if (total) { // 添加百分号
        const handleTotal = {
          ...total,
          onlyMobileOrderRatio: `${total.onlyMobileOrderRatio}%`,
          validRecyclingTransferRatio: `${total.validRecyclingTransferRatio}%`,
          recyclingTransferRatio: `${total.recyclingTransferRatio}%`,
          shelTyingRate: `${total.shelTyingRate}%`,
          filmTyingRate: `${total.filmTyingRate}%`,
          shelPackageTyingRate: `${total.shelPackageTyingRate}%`,
          filmPackageTyingRate: `${total.filmPackageTyingRate}%`,
          careTyingRate: `${total.careTyingRate}%`,
          shieldTyingRate: `${total.shieldTyingRate}%`,
          earphoneTyingRate: `${total.earphoneTyingRate}%`,
          annualCardTyingRate: `${total.annualCardTyingRate}%`,
          chargerTyingRate: `${total.chargerTyingRate}%`,
          DIYTyingSaleRatio: total.DIYTyingSaleRatio ? `${total.DIYTyingSaleRatio}%` : '-'
        }
        setDataSourceTotal(handleTotal) // 底部合计栏数据
      }
      setTableData()
    }
  }
  // 后端未分页数据处理
  const setTableData = function () {
    const { dataSourceAll } = state
    sortData(dataSourceAll)
    const { current, pageSize: size } = state.pagination
    const start = size * current - size
    const end = size * current
    const tableData = dataSourceAll.slice(start, end)
    // 把底部合计栏放到当前页数据里
    if (state.dataSourceTotal && tableData?.length) {
      tableData.push({ ...state.dataSourceTotal, area: '合计', mobile: '合计' })
    }
    setDataSource(tableData) // 页面当前页显示的数据
  }
  const sortData = (arrayData = []) => {
    if (!arrayData?.length) return
    const { field, order } = state.tableSort
    if (!field) return
    arrayData.sort(compare(field, order))
  }
  const compare = (key, sort) => (a, b) => sort === 'ascend' ? a[key] - b[key] : b[key] - a[key]

  const tableChange = (pagination, filters, sorter) => {
    setPagination({ ...pagination })
    setTableSort({ ...sorter })
    // 后端未做分页，若后端分布则用 featchList
    setTableData()
  }
  // 筛选参数过滤空选项
  const _getParams = function () {
    const { areaIds, ...other } = state.formFilter
    const params = {
      ...other,
      areaIds: areaIds?.filter(it => !isNaN(it)) || [],
      areaAlls: areaIds || []
    }
    for (let key in params) {
      if (
        (!params[key] && params[key] !== 0) ||
        (params[key] && Array.isArray(params[key]) && !params[key].length)
      ) {
        delete params[key]
      }
    }
    if (instance.$tnt.xtenant >= 1000) {
      delete params.bizType
    }
    return params
  }
  // 筛选参数检查
  const _checkForm = function () {
    const params = _getParams()
    const {
      times,
    } = params
    if (!times?.length) {
      message.warning('请选择时间段')
      return true
    }
    if (moment(times[0]).add(3, 'months') < moment(times[1])) {
      message.warning('时间间隔不能超过三个月')
      return true
    }
    return false
  }
  return {
    tableChange,
    featchEnums,
    featchHeader,
    featchList
  }
}
