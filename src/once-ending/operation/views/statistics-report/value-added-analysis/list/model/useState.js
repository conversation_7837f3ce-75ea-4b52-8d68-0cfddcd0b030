import { reactive, inject, watch } from 'vue'
import moment from 'moment'

const startTime = moment().format('YYYY-MM-DD')
const endTime = moment().format('YYYY-MM-DD')
const key = Symbol('valueAddedAnalysis')

export function useValueAddedState () {
  return inject(key)
}

export function createValueAddedState (provide) {
  const state = reactive({
    loading: false,
    isFeatch: false,
    optionsEnum: undefined,
    // 查询参数
    copyAvgRecoverProfit: undefined, // 重置参数暂存
    formFilter: { // searchData
      type: 14, // 报表type 14：增值业务分析
      bizType: 0, // 业务类型
      aggregationDimension: 'mobile', // 统计方案:商品 [单选 string]
      items: undefined, // 统计项 [多选 array]
      areaIds: undefined, // 地区集合 [多选 array]
      times: [startTime, endTime], // 时间:今天 传api需转换成两个字段

      cid: undefined, // 分类 [多选 array]
      brandIds: undefined, // 品牌ids [多选 array]
      goodsName: undefined, // 商品名字 [string]
      orderType: undefined, // 订单类型 [多选 array]

      areaLevel: undefined, // 店面级别 [单选 number]
      areaType: 1, // 店面类别:自营 [单选 number]
      areaCategory: undefined, // 门店分类 [单选 number]
      areaAttribute: undefined, // 门店属性 [多选 array]

      priceFilter: undefined, // 大件价位:全部价位 [单选 number]
      bigPrices: undefined, // 大件价位:全部价位(不传后端) 传api需转换成两个字段[bigPriceMax, bigPriceMin]
      basicStandard: false, // 基准值 [boolean]
      avgRecoverProfit: undefined, // 平均回收单毛form

      basicStandardIsDisabled: false, // 基准值是否可操作(不传后端)
      customTying: 'cid',
      customTyingKey: undefined, // 自定义搭售
    },
    searchParams: undefined, // 查询参数(查询时设置，表格数据导出时用)
    saveLoading: false,
    // 表格
    hearderData: [], // 表头
    allItems: [], // all统计项枚举值,
    defaultShowItems: [], // 默认显示的字段,
    // 后端未做分页
    dataSource: [], // 当前页的数据
    dataSourceAll: [], // 所有数据
    dataSourceTotal: undefined, // 底部合计栏数据
    allAreaData: undefined, // 所有地区的销量

    tableSort: {},
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0,
      pageSizeOptions: ['20', '50', '100', '200'],
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: total => `共计${total}条`
    },
  })

  const setLoading = val => { state.loading = val }
  const setIsFeatch = val => { state.isFeatch = val }
  const setOptionsEnum = val => { state.optionsEnum = val || [] }
  const setFormFilter = (val, key) => {
    if (key && typeof key === 'string') {
      state.formFilter[key] = val
    } else {
      state.formFilter = val
    }
  }
  const setSearchParams = val => { state.searchParams = val }
  const setSaveLoading = val => { state.saveLoading = val }
  const setCopyAvgRecoverProfit = val => { state.copyAvgRecoverProfit = val }
  const setHearderData = val => { state.hearderData = val || [] }
  const setAllItems = val => { state.allItems = val || [] }
  const setDefaultShowItems = val => { state.defaultShowItems = val || [] }
  const setDataSource = val => { state.dataSource = val || [] }
  const setDataSourceAll = val => { state.dataSourceAll = val || [] }
  const setDataSourceTotal = val => { state.dataSourceTotal = val }
  const setAllAreaData = val => { state.allAreaData = val }
  const setTableSort = val => { state.tableSort = val }
  const setPagination = (val, key) => {
    if (key && typeof key === 'string') {
      state.pagination[key] = val
    } else {
      state.pagination = val
    }
  }

  // 状态变化联动
  watch(
    () => state.formFilter.aggregationDimension, // 统计方案
    val => {
      setFormFilter(val !== 'mobile', 'basicStandardIsDisabled') // 统计方案变动重设基准值
      if (val !== 'mobile') {
        setFormFilter(false, 'basicStandard') // 清除基准值
        setFormFilter(undefined, 'bigPrices') // 清除大件价位
      }
    },
    { immediate: true }
  )
  const valueAddedAnalysis = {
    state,
    setLoading,
    setIsFeatch,
    setOptionsEnum,
    setCopyAvgRecoverProfit,
    setFormFilter,
    setSearchParams,
    setSaveLoading,
    setHearderData,
    setAllItems,
    setDefaultShowItems,
    setDataSource,
    setDataSourceAll,
    setDataSourceTotal,
    setAllAreaData,
    setTableSort,
    setPagination,
  }
  provide(key, valueAddedAnalysis)
  return valueAddedAnalysis
}
