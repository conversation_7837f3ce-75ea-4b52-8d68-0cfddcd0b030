import { reactive, watch, getCurrentInstance, nextTick, ref, onMounted } from 'vue'
import { BRAND_OPTIONS } from '@operation/store/modules/statistics-report/action-types'
import cloneDeep from 'lodash/cloneDeep'

export default function useSelectLinkage (form, setForm) {
  const instance = getCurrentInstance().proxy
  const cateState = reactive({
    allOptions: [],
    selectOptions: [],
    cacheOptions: [],
    selectSearchValue: ''
  })

  console.log('useSelectLinkage', cateState)

  const allChecked = ref(false)

  const getBrandOptions = async (cids) => {
    const res = await instance.$store.dispatch(`operation/statisticsReport/${BRAND_OPTIONS}`, { cids })
    if (!res) return
    const { data } = res
    if (data) {
      const options = res.data.map(it => ({
        label: it.name,
        value: it.id
      }))
      cateState.allOptions = options
      // 手动触发商品品牌获取焦点事件,解决默认值反显问题
      nextTick(() => {
        selectFocusOrBlur()
      })
    }
  }

  // 每次下拉框获取焦点或者失去焦点,初始化下拉框数据
  const selectFocusOrBlur = function () {
    const options = cloneDeep(cateState.allOptions)
    const initOptions = options.splice(0, 50)
    if (form.brandIds.length) {
      form.brandIds.forEach(item => {
        const index = options.findIndex(d => d.value === item)
        if (index !== -1) {
          initOptions.unshift(options.splice(index, 1)[0])
        }
      })
    }
    // cateState.selectOptions = initOptions
    // cateState.cacheOptions = options
    cateState.selectOptions = initOptions.splice(0, 50)
    cateState.cacheOptions = cloneDeep(cateState.allOptions)
  }

  // 每次用户输入,匹配所有数据,将数据筛选出来
  const selectSearch = function (val) {
    if (!val) {
      selectFocusOrBlur()
      return
    }
    cateState.selectSearchValue = val
    const options = cloneDeep(cateState.allOptions)
    cateState.selectOptions = options.filter(d => d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase()))
    cateState.cacheOptions = options.filter(d => !d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase()))
  }

  const handleSelect = function () {
    selectFocusOrBlur()
    cateState.selectSearchValue = ''
  }

  // 每次下拉框滚动条滚到底部,加载缓存数据
  const selectPopupScroll = function (e) {
    if (!cateState.cacheOptions.length) {
      return
    }
    const { target } = e
    const scrollHeight = target.scrollHeight - target.scrollTop
    const clientHeight = target.clientHeight
    if (scrollHeight < clientHeight + 5) {
      const options = cateState.cacheOptions.splice(0, 50)
      cateState.selectOptions = cateState.selectOptions.concat(options)
    }
  }

  const batchExportAllChecked = (e) => {
    if (e.target.checked) {
      form.brandIds = cateState.cacheOptions.map(item => item.value)
    } else {
      form.brandIds = []
    }
  }

  onMounted(() => {
    if (form.brandIds === cateState.allOptions.length) {
      allChecked.value = true
    } else {
      allChecked.value = false
    }
  })

  watch(() => form.brandIds, (newVal, oldVal) => {
    allChecked.value = newVal.length === cateState.cacheOptions.length
  })

  watch(
    () => form.cid,
    val => {
      getBrandOptions(val) // 所选分类的变动，请求品牌枚举数据
      setForm([], 'brandIds')
    },
    { immediate: true }
  )

  return {
    allChecked,
    cateState,
    selectFocusOrBlur,
    selectSearch,
    handleSelect,
    selectPopupScroll,
    batchExportAllChecked
  }
}
