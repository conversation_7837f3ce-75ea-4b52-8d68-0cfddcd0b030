:deep(.group-compact),
.filter-item.neat.group-compact{
  .content{
    display: flex;
    > *:first-child{
      width: 7em !important;
      flex-shrink: 0;
      .ant-select-selection{
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }
    .ni-area-content,
    .ant-input,
    .ant-input-number,
    .el-input__inner,
    .ant-select-selection--multiple,
    .product-select .ant-select-selection--single,
    .ant-input-affix-wrapper{
      margin-left: -1px;
    }

    .ni-area-content .ant-select-selection,
    .ant-input,
    .ant-input-number,
    .el-input__inner,
    .ant-select-selection--multiple,
    .product-select .ant-select-selection--single,
    .ant-input-affix-wrapper .ant-input{
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      min-width: 10em;
    }
  }
}
:deep(.ant-select-selection--multiple){
  max-height: 32px;
  .ant-select-selection__choice {
    .ant-select-selection__choice__content {
      max-width: 110px;
    }
    &:nth-child(2){
      padding-right: 12px;
    }
  }
}
