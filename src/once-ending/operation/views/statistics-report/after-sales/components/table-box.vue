<script type="jsx" lang="jsx">
  import { getCurrentInstance } from 'vue'
  import { message } from 'ant-design-vue'
  import { NiTable } from '@jiuji/nine-ui'
  import NoData from '@operation/views/statistics-report/components/no-data.vue'

  import useExportXlsx from '@operation/views/statistics-report/hooks/useExportXlsx'
  import { useAfterSalesState } from '../model/useState.js'
  import useActions from '../model/useActions'

  export default {
    components: {
      NiTable,
      NoData
    },
    setup (_) {
      const { proxy } = getCurrentInstance()
      const { state } = useAfterSalesState()
      const { exportDataLoading, exportData } = useExportXlsx({ proxy, type: state.formFilter.type })
      const { tableChange } = useActions()

      const exportExcel = () => {
        if (!state.searchParams) {
          message.info('请先点击查询')
          return
        }
        if (!state.dataSource?.length) {
          message.info('未查询到相关数据')
          return
        }
        const params = { ...state.searchParams }
        params.fileName = '售后费用报表'
        params.scene = 'after_cost'
        exportData({ params })
      }
      return {
        state,
        exportDataLoading,
        exportExcel,
        tableChange
      }
    },
    render () {
      const {
        state: {
          loading,
          isFeatch,
          hearderData,
          dataSource,
          pagination,
          exportDataLoading,
          dataSourceTotal = {},
        },
        exportExcel,
        tableChange
      } = this
      if (!hearderData) return
      return (
         <NiTable
          class="data-table"
          locale={{ emptyText: <NoData isFeatch={ isFeatch }/> }}
          loading={ loading }
          dataSource={ dataSource }
          columns={ hearderData }
          pagination={ pagination }
          onChange={ tableChange }
        >
          <template slot="action">
            <a-button
              icon="export"
              loading={ exportDataLoading }
              onClick={ exportExcel }>
              导出
            </a-button>
          </template>
          <div slot="statistics" class='statistics'>
            <span class='total-item'><span>总计：</span><em>{dataSourceTotal.totalOrder ?? 0}单</em></span>
            <ul class='total-ul'>
              <li class='total-li'><span>维修配件费用：</span><em>{dataSourceTotal.repairAccessoryAmount}</em></li>
              <li class='total-li'><span>手工费用：</span><em>{dataSourceTotal.manualAmount}</em></li>
              <li class='total-li'><span>回收抵扣金额：</span><em>{dataSourceTotal.deductionAmount}</em></li>
              <li class='total-li'><span>出险优惠金额：</span><em>{dataSourceTotal.accidentDiscountAmount}</em></li>
              <li class='total-li'><span>优惠码金额：</span><em>{dataSourceTotal.coupon}</em></li>
              <li class='total-li'><span>维修配件成本：</span><em>{dataSourceTotal.repairAccessoryCost}</em></li>
              <li class='total-li'><span>手工费用成本：</span><em>{dataSourceTotal.manualAmountCost}</em></li>
            </ul>
            <ul class='total-ul'>
              <li class='total-li'><span>维修配件毛利：</span><em>{dataSourceTotal.repairAccessoryProfit}</em></li>
              <li class='total-li'><span>手工费用毛利：</span><em>{dataSourceTotal.manualAmountProfit}</em></li>
              <li class='total-li'><span>总毛利：</span><em>{dataSourceTotal.totalProfit}</em></li>
            </ul>
          </div>
        </NiTable>
      )
    }
  }
</script>

<style lang="scss" scoped>
  .mb-0{
    margin-bottom: 0;
  }
  :deep(.nine-table-bar-action) {
    display: flex;
    align-items: center;
  }
  :deep(.ant-table-thead > tr > th)  {
    font-weight: bold;
  }
  :deep(.checkList) {
    max-height: 60vh;
    overflow: auto;
  }
  .data-table{
    .ant-btn-link{
      color: #333;
    }
  }
  .statistics{
    padding-left: 120px;
    padding-right: 24px;
    .total-item,
    .total-li{
      line-height: 2.2em;
      em {color: red;}
    }
    .total-item{
      position: absolute;
      left: 8px;
    }
    .total-ul{
      display: flex;
      flex-wrap: wrap;
      font-size: 13px;
      .total-li {
        width: 20%;
        flex-shrink: 0;
      }
    }
  }
</style>
