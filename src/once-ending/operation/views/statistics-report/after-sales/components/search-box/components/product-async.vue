<script lang="jsx">
  import { reactive, toRefs } from 'vue'
  import debounce from 'lodash/debounce'
  import apiSReport from '@operation/api/statistics-report'
  import { to } from '@common/utils'
  export default {
    name: 'search-select-async',
    props: {
      value: {
        type: String,
        default: ''
      },
      placeholder: {
        type: String,
        default: '请输入维修机型设备，如p30/商品id/skuid'
      }
    },
    setup (_, { emit }) {
      let lastFetchId = 0
      const state = reactive({
        inputKey: '',
        selectOptions: [], // 下拉选项数据
        idsValue: undefined,
        fetching: false,
        pages: {
          current: 1,
          size: 50,
          total: 0,
        }
      })

      const getData = async (inputKey) => {
        if (inputKey !== state.inputKey) state.inputKey = inputKey
        const { pages: { current, size } } = state
        lastFetchId += 1
        const fetchId = lastFetchId
        state.fetching = true
        const params = {
          current,
          size,
          world: inputKey,
        }
        const [err, res] = await to(apiSReport.searchMobileProduct(params))
        if (err) throw err
        state.fetching = false
        if (fetchId !== lastFetchId) return

        const { code, data } = res
        if (code !== 0) return
        state.pages.total = parseInt(data.total)
        const options = data.records.map(item => {
          return {
            key: item.ppid,
            label: item.productName + item.productColor
          }
        })
        state.selectOptions = current === 1 ? options : state.selectOptions.concat(options)
      }
      const handleChange = (value) => {
        emit('change', value)
        state.fetching = false
      }
      // 失去焦点初始化
      const handleBlur = () => {
        state.selectOptions = []
        state.pages.current = 1
        state.pages.total = 0
        state.inputKey = ''
      }
      const scrollEnd = (e) => {
        const { target } = e
        const { current, size, total } = state.pages
        const scrollHeight = target.scrollHeight - target.scrollTop
        const clientHeight = target.clientHeight
        if (scrollHeight < clientHeight + 5) {
          if (current * size < total) {
            state.pages.current = state.pages.current + 1
            getData(state.inputKey)
          }
        }
      }
      const getOptions = debounce(getData, 1000)

      return {
        ...toRefs(state),
        handleChange,
        getOptions,
        getData,
        handleBlur,
        scrollEnd
      }
    },
    render () {
      const {
        placeholder,
        fetching,
        selectOptions,

        getOptions,
        getData,
        handleChange,
        handleBlur,
        scrollEnd
      } = this
      return (
          <a-select
            allowClear
            showSearch
            class="product-select"
            dropdownMatchSelectWidth={ false }
            label-in-value
            filterOption={false}
            searchPlaceholder='输入关键字进行搜索'
            placeholder={ placeholder }
            v-model={ this.idsValue }
            notFoundContent={ fetching ? undefined : '暂未查询到相关数据' }
            onPopupScroll={ scrollEnd }
            onFocus={ getData }
            onBlur={ handleBlur }
            onSearch={ getOptions }
            onChange={ handleChange }
          >
          { fetching && <a-spin slot="notFoundContent" size="small" /> }
          {
            selectOptions.map(item => (
              <a-select-option key={item.key}>
              { item.label }
              </a-select-option>
            ))
          }
          </a-select>
      )
    }
  }
</script>
