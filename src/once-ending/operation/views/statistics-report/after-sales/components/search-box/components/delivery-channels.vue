<script lang="jsx">
  import { defineComponent, ref, getCurrentInstance } from 'vue'
  import debounce from 'lodash/debounce'
  import { SEARCH_CHANNELS } from '@operation/store/modules/statistics-report/action-types'
  export default defineComponent({
    name: 'delivery-channels',
    props: {
      formFilter: {
        type: Object,
        default: () => {
          return {
            repairKey: undefined
          }
        }
      }
    },
    setup (props) {
      const options = ref([])
      const fetching = ref(false)
      const { proxy } = getCurrentInstance()

      const getData = async function (inputValue) {
        fetching.value = true
        options.value = []
        const res = await proxy.$store.dispatch(`operation/statisticsReport/${SEARCH_CHANNELS}`, { searchKey: inputValue || '', kind: 8 })
        fetching.value = false
        if (res) {
          options.value = res.data || []
          if (inputValue && !options.value.find(it => it.id === inputValue)) {
            options.value.push({ id: inputValue, })
            props.formFilter.repairKey = inputValue
          }
        }
      }
      const getOptions = debounce(getData, 800)
      getData()

      return {
        options,
        fetching,
        getData,
        getOptions
      }
    },
    render (createElement, context) {
      const { options, fetching, getData, getOptions, formFilter } = this
      return <a-select
        allowClear
        showSearch
        dropdownMatchSelectWidth={ false }
        filterOption={false}
        autoClearSearchValue={false}
        defaultActiveFirstOption={false}
        notFoundContent={ fetching ? undefined : '暂未查询到相关数据' }
        searchPlaceholder='输入关键字进行搜索'
        placeholder='输入关键字进行搜索'
        v-model={formFilter.repairKey}
        onSearch={ getOptions }
      >
        { fetching && <a-spin slot="notFoundContent" size="small" /> }
        {
          options.map(item => (
            <a-select-option key={item.id}>
              { item.id }{ item.companyJc ? `-${item.companyJc}` : '' }
            </a-select-option>
          ))
        }
      </a-select>
    }
  })
</script>

<style scoped>

</style>
