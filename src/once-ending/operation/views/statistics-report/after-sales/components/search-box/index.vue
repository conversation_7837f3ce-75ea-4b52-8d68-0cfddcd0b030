<template>
  <ni-filter
    class="relative"
    :form="formFilter"
    :loading="loading"
    @filter="doSearch"
    layout="neat"
    :unfoldCount="6"
    :label-width="85"
    :immediate="false"
    :settingAble="false"
    :fold="false"
    :itemMinWidth="400"
    >
    <ni-filter-item label="地区" class="group-compact">
      <a-select
        showArrow
        placeholder="请选择店面级别"
        v-model="formFilter.area"
        :options="optObj.area"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
      <ni-area-select
        multiple
        allowClear
        show-search
        class="area-selector"
        placeholder="请选择地区"
        :maxTagCount="1"
        v-model="formFilter.areaIds"
      />
    </ni-filter-item><ni-filter-item label="详情查询" class="group-compact">
      <a-select
        showArrow
        placeholder="请选择查询详情类型"
        v-model="formFilter.repairType"
        :options="optObj.repairType"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
      <a-input-number
        allowClear
        :min="1"
        v-if='formFilter.repairType === 1'
        placeholder="请输入数字类型的维修单号"
        v-model="formFilter.repairKey"
      />
      <ProductAsync
        allowClear
        v-if='formFilter.repairType === 2'
        v-model="formFilter.repairKey"
        @change="productChange"
      />
      <StaffInput
        :value="formFilter.repairKey"
        :dataIsObjArr="true"
        v-if='[3, 4].includes(formFilter.repairType)'
        :placeholder="userPlaceholder"
        :format='3'
        @input='handleStaffInput'
        allow-clear/>
    <DeliveryChannels :formFilter="formFilter" v-if='formFilter.repairType === 5'/>
    </ni-filter-item><ni-filter-item label="保修状态">
      <a-select
        showArrow
        allowClear
        mode="multiple"
        :maxTagCount="1"
        optionFilterProp='children'
        placeholder="请选择保修状态"
        v-model="formFilter.repairStatus"
        :options="optObj.repairStatus"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
    </ni-filter-item><ni-filter-item label="出险服务">
      <a-select
        showArrow
        allowClear
        mode="multiple"
        optionFilterProp='children'
        :maxTagCount="1"
        placeholder="请选择出险服务"
        v-model="formFilter.accidentService"
        :options="optObj.accidentService"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
    </ni-filter-item><ni-filter-item label="业务类型">
       <a-select
        showArrow
        allowClear
        placeholder="请选择业务类型"
        optionFilterProp='children'
        v-model="formFilter.businessType"
        :options="optObj.businessType"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
    </ni-filter-item>
    <ni-filter-item label="设备类型">
      <a-select
        showArrow
        allowClear
        placeholder="请选择订单类型"
        optionFilterProp='children'
        v-model="formFilter.shopType"
        :options="optObj.shopType"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
    </ni-filter-item>
    <ni-filter-item label="维修组别">
      <a-select
        showArrow
        allowClear
        placeholder="请选择维修组别"
        optionFilterProp='children'
        v-model="formFilter.repairGroup"
        :options="optObj.repairGroup"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
    </ni-filter-item>
    <ni-filter-item label="办理时间">
      <a-range-picker
        allowClear
        v-model="formFilter.timesRange"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        :allow-clear="false"
        :getPopupContainer="triggerNode => triggerNode.parentNode"
      />
    </ni-filter-item>
  </ni-filter>
</template>

<script type="text/jsx">
  import { computed, onMounted, reactive, watch } from 'vue'
  import { NiFilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import { useAfterSalesState } from '../../model/useState.js'
  import useActions from '../../model/useActions'
  import ProductAsync from './components/product-async'
  import DeliveryChannels from './components/delivery-channels'
  import StaffInput from '@operation/components/staff-input'

  export default {
    name: 'search-box',
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect,
      StaffInput,
      ProductAsync,
      DeliveryChannels
    },
    setup () {
      const optObj = reactive({
        area: [],
        repairType: [],
        repairStatus: [],
        accidentService: [],
        businessType: [],
        shopType: [],
        repairGroup: [],
      })
      const {
        state,
        state: { formFilter },
        setFormFilter
      } = useAfterSalesState()
      const {
        featchList,
        pageInit
      } = useActions()
      const productChange = (val) => {
        setFormFilter(val?.label, 'repairKey')
      }
      const handleStaffInput = val => {
        setFormFilter(val, 'repairKey')
      }
      /**
       * 转换后端返回的选项下拉枚举
       * @field { String }
       * @return { Array } 转换成ant组件所需字段数据
       */
      const selectOptions = (field) => {
        const data = state.optionsEnum?.find(item => item.key === field)
        const toOptions = (arr = []) => arr.map(item => ({ value: item.code, label: item.name }))
        return toOptions(data?.list)
      }
      const doSearch = () => { featchList(1) }
      onMounted(() => {
        pageInit()
      })
      watch(
        () => state.optionsEnum,
        () => {
          Object.keys(optObj).forEach(item => {
            optObj[item] = selectOptions(item)
          })
        }
      )
      watch(
        () => formFilter.repairType,
        () => {
          setFormFilter(undefined, 'repairKey')
        }
      )
      const userPlaceholder = computed(() => {
        return `请选择${optObj.repairType.find(it => it?.value === formFilter.repairType)?.label || '维修人'}`
      })
      return {
        formFilter,
        loading: computed(() => state.loading),
        optObj,

        productChange,
        doSearch,
        setFormFilter,
        handleStaffInput,
        userPlaceholder
      }
    },
  }
</script>
<style lang="scss" scoped>
@import '../../style.scss';
</style>
