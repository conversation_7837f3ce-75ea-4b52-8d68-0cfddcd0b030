<script type="text/javascript" lang="jsx">
  import { createAfterSalesState } from './model/useState.js'
  import uselastUpdateDate from '@operation/components/uselastUpdateDate.js'

  import { NiListPage } from '@jiuji/nine-ui'
  import SearchBox from './components/search-box'
  import TableBox from './components/table-box.vue'

  export default {
    components: {
      NiListPage,
      SearchBox,
      TableBox
    },
    setup () {
      createAfterSalesState()
      const { lastUpdateDate } = uselastUpdateDate()

      return {
        lastUpdateDate
      }
    },
    render () {
      return (
        <page class="value-added">
        { this.lastUpdateDate && <div slot="extra">数据更新于{ this.lastUpdateDate }</div> }
          <NiListPage push-filter-to-location={ false }>
            <SearchBox class="mb-16"/>
            <TableBox/>
          </NiListPage>
        </page>
      )
    }
  }
</script>
<style scoped>
  :deep(.ant-page-header-heading-extra) {
    float: left;
    font-size: 12px;
    margin-top: 12px;
    color: #777;
  }
</style>
