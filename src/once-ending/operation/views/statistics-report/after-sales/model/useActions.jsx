
import { getCurrentInstance, h } from 'vue'
import moment from 'moment'
import { cloneDeep } from 'lodash'
import { compareTimePass } from '@operation/util/common.js'
import useLastUpdateDate from '@operation/components/uselastUpdateDate.js'
import {
  QUERY_CONDITION,
  BUSINESS_LIST,
  QUERY_STAT_DATA_HEADER
} from '@operation/store/modules/statistics-report/action-types'
import { useAfterSalesState } from './useState.js'
import useReportHearder from '../../hooks/useReportHearder'

export default function useActions () {
  const { proxy } = getCurrentInstance()

  const { setLastUpdateDate } = useLastUpdateDate()
  const {
    state,
    setLoading,
    setIsFeatch,
    setOptionsEnum,
    setPagination,
    setHearderData,
    setDataSource,
    setDataSourceTotal,
    setSearchParams,
    setFormFilter,
  } = useAfterSalesState()

  const featchEnums = async () => {
    const res = await proxy.$store.dispatch(
      `operation/statisticsReport/${QUERY_CONDITION}`,
      { type: state.formFilter.type }
    )
    if (!res) return
    const { data = [] } = res
    setOptionsEnum(data)
  }
  // 查询默认表头
  const { type } = state.formFilter
  const canEdit = !!(window.tenant.xtenant === 0 || window.tenant.xtenant === 67000)
  const { hearderData, getHearder } = useReportHearder({ proxy, h, type, canEdit })
  const featchHeader = async () => {
    await getHearder({ type })
    const need100 = ['currentArea', 'coupon', 'dealTime']
    const need150 = [
      'repairAccessoryAmount',
      'deductionAmount',
      'accidentDiscountAmount',
      'repairAccessoryCost',
      'manualAmountCost',
      'repairAccessoryProfit',
      'manualAmountProfit'
    ]
    console.log('hearderData', hearderData.value)
    hearderData.value.forEach(d => {
      d.width = d.key === 'repairModel' ? 220 : d.key === 'type' ? 90 : need150.includes(d.key) ? 140 : need100.includes(d.key) ? 120 : 100
      d.sorter = false
      d.customRender = text => {
        if (d.key === 'repairOrderId') {
          const id = text.replace('R', '')
          return <a
            rel="noopener noreferrer"
            href={`${proxy.$tnt.oaHost}/staticpc/#/after-service/order/edit/${id}`}
            target="_blank"
          >{ text }</a>
        } else {
          return <i>{ text ?? '-'}</i>
        }
      }
    })
    setHearderData(hearderData.value)
  }
  const featchList = async function (current) {
    if (current) setPagination(current, 'current')
    let params = _paramsFilterNull() // 获取有值参数
    const isPass = compareTimePass(params.timesRange, '办理') // 时间段三个月检验
    if (!isPass) return
    params.current = state.pagination.current
    params.size = state.pagination.pageSize
    if (params.timesRange?.length) {
      params.startTime = moment(params.timesRange[0]).startOf('d').format('YYYY-MM-DD HH:mm:ss')
      params.endTime = moment(params.timesRange[1]).endOf('d').format('YYYY-MM-DD HH:mm:ss')
    }
    delete params.timesRange
    if (!params.areaIds) delete params.area
    if (!params.repairKey) delete params.repairType
    localStorage.setItem('afterSalesParams', JSON.stringify(params)) // 参数记忆
    // ----- 数据导出时需要的最终查询参数 start ------
    const exportParams = cloneDeep(params)
    delete exportParams.size
    delete exportParams.current
    setSearchParams(exportParams)
    // ------------------ end --------------------
    setDataSource()
    setDataSourceTotal()
    setPagination(0, 'total')

    setLoading(true)
    const res = await proxy.$store.dispatch(
      `operation/statisticsReport/${BUSINESS_LIST}`,
      params
    )
    setLoading(false)
    if (!res) return
    setIsFeatch(true)
    setLastUpdateDate()
    const { data = [], total, totalOrder, totalRecord } = res.data
    totalRecord && setPagination(totalRecord, 'total') // 数据总条数
    setDataSource(data)
    if (total) {
      const handleTotal = {
        ...total,
        totalOrder
      }
      setDataSourceTotal(handleTotal) // 底部合计栏数据
    }
  }
  const tableChange = pagination => {
    setPagination({ ...pagination })
    featchList()
  }
  // 筛选参数过滤空选项
  const _paramsFilterNull = () => {
    const params = { ...state.formFilter }
    for (let key in params) {
      if (
        (!params[key] && params[key] !== 0) ||
        (params[key] && Array.isArray(params[key]) && !params[key].length)
      ) {
        delete params[key]
      }
    }
    return params
  }
  const pageInit = () => {
    let storageParams = JSON.parse(localStorage.getItem('afterSalesParams'))
    if (storageParams) {
      Object.keys(storageParams).map(item => {
        setFormFilter(storageParams[item], item)
      })
    }
    featchEnums()
    featchHeader()
    featchList(1)
  }
  return {
    tableChange,
    featchEnums,
    featchHeader,
    featchList,
    pageInit
  }
}
