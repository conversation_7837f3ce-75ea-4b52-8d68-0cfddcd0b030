import { reactive, inject, provide } from 'vue'
import moment from 'moment'

const startTime = moment().format('YYYY-MM-DD')
const endTime = moment().format('YYYY-MM-DD')
const key = Symbol('afterSalesState')

export function useAfterSalesState () {
  return inject(key)
}

export function createAfterSalesState () {
  const state = reactive({
    loading: false,
    isFeatch: false,
    optionsEnum: undefined,
    // 查询参数
    formFilter: { // searchData
      type: 105, // 售后费用报表type 105
      area: 1, // 地区类型
      areaIds: undefined, // 地区集合 [多选 array]
      repairType: 1, // 维修详情类型
      repairKey: undefined, // 维修详情关键字
      repairStatus: undefined, // 保修状态
      accidentService: undefined, // 出险服务
      businessType: 1, // 业务类型 [多选 array]
      timesRange: [startTime, endTime], // 办理时间

      productId: undefined,
      productText: undefined,
      shopType: 1,
      repairGroup: undefined,
    },
    searchParams: undefined, // 查询参数(查询时设置，表格数据导出时用)
    // 表格
    hearderData: [], // 表头
    defaultShowItems: [], // 默认显示的字段,
    // 后端未做分页
    dataSource: [], // 当前页的数据
    dataSourceAll: [], // 所有数据
    dataSourceTotal: undefined, // 底部合计栏数据
    allAreaData: undefined, // 所有地区的销量

    tableSort: {},
    pagination: {
      size: 'small',
      current: 1,
      pageSize: 50,
      total: 0,
      pageSizeOptions: ['20', '50', '100', '200'],
      showQuickJumper: true,
      showTotal: total => `共计${total}条`
    },
  })

  const setLoading = val => { state.loading = val }
  const setIsFeatch = val => { state.isFeatch = val }
  const setOptionsEnum = val => { state.optionsEnum = val || [] }
  const setFormFilter = (val, key) => {
    if (key && typeof key === 'string') {
      state.formFilter[key] = val
    } else {
      state.formFilter = val
    }
  }
  const setSearchParams = val => { state.searchParams = val }
  const setHearderData = val => { state.hearderData = val || [] }
  const setDataSource = val => { state.dataSource = val || [] }
  const setDataSourceAll = val => { state.dataSourceAll = val || [] }
  const setDataSourceTotal = val => { state.dataSourceTotal = val || {} }
  const setTableSort = val => { state.tableSort = val }
  const setPagination = (val, key) => {
    if (key && typeof key === 'string') {
      state.pagination[key] = val
    } else {
      state.pagination = val
    }
  }
  const afterSalesState = {
    state,
    setLoading,
    setIsFeatch,
    setOptionsEnum,
    setFormFilter,
    setSearchParams,
    setHearderData,
    setDataSource,
    setDataSourceAll,
    setDataSourceTotal,
    setTableSort,
    setPagination,
  }
  provide(key, afterSalesState)
  return afterSalesState
}
