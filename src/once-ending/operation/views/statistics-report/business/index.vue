
<script lang="jsx">
  import searchBox from './components/search-box'
  import tableBox from './components/table-box'
  import { NiListPage, NiPrice } from '@jiuji/nine-ui'
  import { h, provide, ref, computed, reactive, getCurrentInstance } from 'vue'
  import useCommon from '../hooks/useCommon'
  import useReportHearder from '../hooks/useReportHearder'
  import moment from 'moment'
  import { BUSINESS_LIST, BUSINESS_EDIT } from '@operation/store/modules/statistics-report/action-types'
  import { mapState } from 'vuex'
  import storeApi from '@operation/api/store'
  import { to } from '~/util/common'

  export default {
    name: 'index',
    components: { searchBox, tableBox, NiListPage, NiPrice },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const { loading, isFeatch } = useCommon()
      const dataSource = ref([])
      const searchData = reactive({
        areaIds: [],
        yearMonth: moment(new Date()).format('YYYY-MM')
      })
      provide('searchData', searchData)
      const fetchList = async () => {
        const params = { ...searchData, type: 15 }
        loading.value = true
        const res = await proxy.$store.dispatch(`operation/statisticsReport/${BUSINESS_LIST}`, params)
        loading.value = false
        if (res && res.code === 0) {
          isFeatch.value = true
          const { values, total } = res.data
          total.area = '总计'
          values.length && (values.push(total))
          dataSource.value = values
        } else {
          proxy.$message.error(res.userMsg)
        }
      }
      fetchList()
      const featchHeader = async () => {
        await getHearder({ type: 15 })
      }
      // 获取表头
      let { hearderData, getHearder } = useReportHearder({ proxy, h, type: 15, editNoteCallback: featchHeader })
      featchHeader()
      const originItem = ref({})
      function changeValue (record, key, value) {
        record[key] = value
      }
      function focus (value, record) {
        originItem.value = { ...record }
      }
      const blur = async (e, record, key) => {
        const newValue = e.target.value
        const originValue = originItem.value[key]
        if (!newValue) return proxy.$message.warning('输入值不能为空！')
        if (record.areaId === originItem.value.areaId && +newValue !== +originValue) {
          loading.value = true
          const params = {
            areaId: record.areaId,
            yearMonth: record.yearMonth
          }
          params[key] = newValue.toString()
          const res = await proxy.$store.dispatch(`operation/statisticsReport/${BUSINESS_EDIT}`, params)
          loading.value = false
          if (res.code === 0) {
            proxy.$message.success('修改成功')
            fetchList()
          } else {
            proxy.$message.error(res.userMsg)
          }
        }
      }

      return {
        searchData,
        dataSource,
        hearderData,
        fetchList,
        changeValue,
        focus,
        blur
      }
    },
    computed: {
      columns () {
        if (!this.hearderData.length) return []
        this.addSlots(this.hearderData)
        return this.hearderData
      },
      ...mapState({
        hasRankjyfx: state => state.userInfo.Rank.includes('jyfx'),
      }),
      canEdit () {
        return this.hasRankjyfx
      }
    },
    methods: {
      addSlots (array) {
        if (!this.dataSource.length) return
        const { canEdit } = this
        const actionItem = {
          dataIndex: 'actions',
          key: 'actions',
          title: '操作',
          fixed: 'right',
          width: 30,
        }
        const isActionItem = array.find(it => it.dataIndex === 'actions')
        if (!isActionItem) array.push(actionItem)
        const editArray = ['profitDeductionPercentage', 'examineDeductionPercentage', 'otherProfit']
        array.forEach((item, index) => {
          let color = 'rgba(0, 0, 0, 0.65)'
          const needEdit = canEdit && editArray.includes(item.key)
          const percentage = item.key.indexOf('Percentage') > -1
          item.width = '150px'
          if (item.sorting) {
            // 排序处理
            item.sorter = (a, b) => {
              const hasPercent = item.key.indexOf('Percentage') > -1
              if (hasPercent) {
                const cacheA = (a[item.key] || '').replace(/%/g, '')
                const cacheB = (b[item.key] || '').replace(/%/g, '')
                return cacheA - cacheB
              } else {
                return a[item.key] - b[item.key]
              }
            }
          }
          function ninePrice (value) { // 显示千分位金额
            if (item.showPrice) {
              return <NiPrice decimalFontSize={ 14 }	prefixColor={ color } color={ color } decimalColor={ color } value={ value } thousand={ true }/>
            } else {
              return value
            }
          }
          item.customRender = (text, record, index) => (needEdit && record.area !== '总计') ? <div class="flex flex-align-center">
              <a-input-number
                value={record[item.key]}
                style="width: 150px"
                step={ 0.01 }
                max={ percentage ? 100 : 100000 * 100000 }
                min={ percentage ? 0 : -100000 * 100000 }
                onFocus={ () => this.focus(text, record) }
                onBlur={ (e) => this.blur(e, record, item.key) }
                onChange={ (e) => this.changeValue(record, item.key, e) }
              >
              </a-input-number>
            <span class="ml-4">{ percentage ? '%' : '' }</span>
            </div>
            : item.dataIndex === 'actions'
              ? record.area === '总计'
                ? null
                : record.userId > 0
                  ? <a rel="noopener noreferrer"
                      target="_blank"
                      href={`${this.$tnt.oaHost}/member/surplus?id=${record.userId}`}
                    >
                      已结算<br/>
                      userId: <i class='blue'>{record.userId}</i>
                    </a>
                  : canEdit ? <a-button
                      type='primary'
                      onClick={ () => this.handleSettlement(record) }
                    >
                      结算
                    </a-button> : '-'
              : <span style={{ color: color }}>{ ninePrice(text) }</span>
        })
      },
      // 结算
      handleSettlement (record) {
        const h = this.$createElement
        this.$confirm({
          title: '提示',
          content: <i class='red'>您正在操作结算加盟店反利余额，点击“确定”操作，系统将自动给对应加盟店账户充值 “返利金额” 额度，是否确定结算？</i>,
          onOk: () => {
            this.okHandler(record)
          }
        })
      },
      async okHandler (record) {
        console.log('确定核算', record)
        const params = {
          areaId: record.areaId, // 地区id
          rechargeTime: this.searchData.yearMonth, // 充值年月
          rechargeAmount: record.profitDeduction // 充值金额
        }
        const [err, res] = await to(storeApi.recharge(params))
        if (err) throw err
        const { code, userMsg } = res
        if (code !== 0) return this.$message.error(userMsg)
        this.$message.success(userMsg || '操作成功')
        this.fetchList()
      }
    },
    render () {
      const { fetchList, dataSource, columns } = this
      return <page class="statistics-report">
        <ni-list-page push-filter-to-location={false}>
          <search-box
            onSearch={() => fetchList()}
          ></search-box>
          <table-box
            columns={ columns }
            dataSource={ dataSource }
            class="my-16"
          ></table-box>
        </ni-list-page>
      </page>
    }
  }
</script>

<style scoped lang="scss">
.my-16 {
  margin-top: 16px;
  margin-bottom: 16px;
}
.ml-4 {
  margin-left: 5px;
}
.statistics-report {
  position: relative;
}
</style>
