
<script lang="jsx">
  import { NiTable } from '@jiuji/nine-ui'
  import { h, provide, ref, computed, reactive, inject } from 'vue'
  import NoData from '../../components/no-data'
  export default {
    name: 'table-box',
    components: { NiTable, NoData },
    props: {
      columns: {
        type: Array,
        default: () => []
      },
      dataSource: {
        type: Array,
        default: () => []
      }
    },
    inject: ['isFeatch'],
    data () {
      return {
      }
    },
    setup (props, ctx) {
      const loading = inject('loading')
      const originItem = ref({})
      function changeValue (record, key, e) {
        record[key] = e.target.value
      }
      function focus (value, record) {
        originItem.value = { ...record }
      }
      const blur = (e, record, key) => {
        const newValue = e.target.value
        const originValue = originItem.value[key]
        if (record.id === originItem.value.id && newValue !== originValue) {
          loading.value = true
          setTimeout(() => {
            loading.value = false
            record[key] = originItem.value[key]
          }, 1000)
        }
      }
      return {
        changeValue,
        focus,
        blur,
        loading
      }
    },
    render () {
      const { dataSource, columns, loading, isFeatch } = this
      return <NiTable
        locale={{ emptyText: <NoData is-featch={isFeatch} /> }}
        dataSource={ dataSource }
        columns={ columns }
        loading={ loading }
        ></NiTable>
    }
  }
</script>

<style scoped lang="scss">
:deep(.ant-table-footer) {
  display: none;
}
</style>
