
<script type="text/jsx" lang="jsx">
  import { Ni<PERSON>ilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import { inject, ref, getCurrentInstance } from 'vue'
  import { QUERY_CONDITION } from '@operation/store/modules/statistics-report/action-types'
  import moment from 'moment'
  export default {
    name: 'search-box',
    components: { NiFilter, NiFilterItem, NiAreaSelect },
    inject: ['loading', 'isFeatch'],
    setup (props) {
      const { proxy } = getCurrentInstance()
      const searchData = inject('searchData')
      const area = ref([])
      function doSearch () {
        proxy.$emit('search')
      }
      function disabledDate (current) {
        const endTime = moment('2022-05').format('YYYY-MM')
        return current && current < moment(endTime)
      }
      const getArea = async () => {
        const params = { type: 15 }
        const res = await proxy.$store.dispatch(`operation/statisticsReport/${QUERY_CONDITION}`, params)
        if (res && res.code === 0) {
          area.value = res.data[0]?.tree || []
        } else {
          proxy.$message.error(res.userMsg)
        }
      }
      // getArea()
      function getPopupContainer () {
        return document.querySelector('.statistics-report')
      }
      return {
        searchData,
        doSearch,
        disabledDate,
        area,
        getPopupContainer
      }
    },
    render () {
      const { searchData, loading, disabledDate, getPopupContainer } = this
      return <ni-filter
        form={searchData}
        loading={loading}
        label-width={85}
        onFilter={() => this.$emit('search')}
        immediate={false}>
        <ni-filter-item label="地区" class="areas-select">
          <NiAreaSelect
            placeholder="请选择地区"
            className="area-selector"
            max-tag-count={1}
            multiple
            allowClear
            treeNodeFilterProp="title"
            v-model={searchData.areaIds}
            getPopupContainer={getPopupContainer}
            style="min-width: 300px"
          />
        </ni-filter-item>

        <ni-filter-item label="年月">
          <a-month-picker
            get-calendar-container={getPopupContainer}
            v-model={ searchData.yearMonth }
            valueFormat="YYYY-MM"
            disabledDate={ disabledDate }
            placeholder="请选择年月"/>
        </ni-filter-item>
      </ni-filter>
    }
  }
</script>

<style scoped>

</style>
