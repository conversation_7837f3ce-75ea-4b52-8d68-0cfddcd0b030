<script lang="jsx">
  import { defineComponent, onMounted, ref, onBeforeUnmount } from 'vue'
  import NoData from '../../components/no-data'

  export default defineComponent({
    components: {
      NoData
    },
    props: {
      frameUrl: {
        type: String,
        default: ''
      },
      isFeatch: {
        type: <PERSON>olean,
        default: false
      }
    },
    setup () {
      const top = ref(0)
      const ob = ref(null)
      const setTop = function () {
        const head = document.querySelector('.ant-page-header')
        const searchBox = document.querySelector('.search-box')
        if (head && searchBox) {
          const headHeight = head.getBoundingClientRect().height
          const searchBoxHeight = searchBox.getBoundingClientRect().height
          top.value = headHeight + searchBoxHeight + 16
        }
      }

      onMounted(() => {
        ob.value = new ResizeObserver(setTop)
        const searchBox = document.querySelector('.search-box')
        ob.value.observe(searchBox)
      })
      onBeforeUnmount(() => {
        ob.value.disconnect()
        ob.value = null
      })
      return {
        top
      }
    },
    render () {
      const { frameUrl, isFeatch, top } = this
      const frameBoxStyle = { top: `${top}px` }
      return <div class="frame-box" style={frameBoxStyle}>
      {
        frameUrl ? <div class="frame"><iframe width="100%" height="100%" src={frameUrl}></iframe></div> : <div class="no-data-box"><NoData is-featch={isFeatch} /></div>
      }
      </div>
    }
  })
</script>
<style lang="scss" scoped>
.frame-box{
    background: #fff;
    padding: 16px 8px;
    position: absolute;
    right: 24px;
    left: 24px;
    bottom: 16px;
    .frame{
      height: 100%;
      iframe{
        border: none;
      }
    }
    .no-data-box{
      display: flex;
      justify-content: center;
      align-items: center;
      height: 300px;
    }
}
</style>
