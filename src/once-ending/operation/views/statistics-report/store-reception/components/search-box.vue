<script lang="jsx">
  import { defineComponent, computed, ref, nextTick, getCurrentInstance } from 'vue'
  import AreaDepartSelector from '@operation/components/area-depart-selector'
  import {
    showLevelMap,
    typeOptionsMap,
    shopCategoryOptions,
    kindOptions,
    options
  } from '../constants'
  import { NiFilter, NiFilterItem, NiStaffSelect, NiAreaSelect } from '@jiuji/nine-ui'
  import { Select, DatePicker, TreeSelect } from 'ant-design-vue'
  export default defineComponent({
    inject: ['pageType'],
    props: {
      searchData: {
        type: Object,
        default: () => ({}),
      },
      loading: {
        type: Boolean,
        default: false,
      },
      attributeTree: {
        type: Array,
        default: () => [],
      },
      rolesOptions: {
        type: Array,
        default: () => [],
      },
    },
    components: {
      NiFilter,
      NiFilterItem,
      NiStaffSelect,
      AreaDepartSelector,
      NiAreaSelect,
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const getPopupContainer = function () {
        return document.querySelector('.store-reception')
      }
      const showLevel = computed(
        () => showLevelMap.get(props.searchData.type) || 0
      )
      const areaIdRef = ref(null)
      const areaDepartSelectorIsLoad = function () {
        proxy.$emit('isLoad')
      }

      const selectOptions = ref([])
      const cacheOptions = ref([])
      const selectSearchValue = ref('')

      // 因为角色下拉框数据不是同时绑定到下拉组件上,导致如果有值从url上传过来取到值以后绑定在组件上无法显示出名称,而是显示id,故初始的时候将options的值绑定上,然后再置空,让名称能够正常显示
      const setDefaultRolesOptions = function () {
        const options = JSON.parse(JSON.stringify(props.rolesOptions))
        selectOptions.value = options.filter(d => props.searchData.roleId.includes(d.value))
        nextTick(() => {
          selectOptions.value = []
        })
      }

      // 每次下拉框获取焦点,初始化下拉框数据
      const selectFocus = function () {
        if (selectOptions.value.length) {
          return
        }
        const options = JSON.parse(JSON.stringify(props.rolesOptions))
        const initOptions = options.splice(0, 50)
        selectOptions.value = initOptions
        cacheOptions.value = options
      }

      // 每次下拉框失去焦点,置空下拉框数据
      const selectBlur = function () {
        selectOptions.value = []
        cacheOptions.value = []
      }

      // 每次用户输入,匹配所有数据,将数据筛选出来
      const selectSearch = function (val) {
        selectSearchValue.value = val
        const options = JSON.parse(JSON.stringify(props.rolesOptions))
        selectOptions.value = options.filter(d => d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase()))
        cacheOptions.value = options.filter(d => !d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase()))
      }

      // 每次用户选择以后判断是否为筛选以后查询,如果是,重置下拉数据,
      const handleSelect = function () {
        if (selectSearchValue.value) {
          const options = JSON.parse(JSON.stringify(props.rolesOptions))
          const initOptions = options.splice(0, 50)
          selectOptions.value = initOptions
          cacheOptions.value = options
          selectSearchValue.value = ''
        }
      }

      // 每次下拉框滚动条滚到底部,加载缓存数据
      const selectPopupScroll = function (e) {
        if (!cacheOptions.value.length) {
          return
        }
        const { target } = e
        const scrollHeight = target.scrollHeight - target.scrollTop
        const clientHeight = target.clientHeight
        if (scrollHeight < clientHeight + 5) {
          const options = cacheOptions.value.splice(0, 50)
          selectOptions.value = selectOptions.value.concat(options)
        }
      }

      return {
        getPopupContainer,
        showLevel,
        areaIdRef,
        areaDepartSelectorIsLoad,
        selectFocus,
        selectBlur,
        selectSearch,
        handleSelect,
        selectPopupScroll,
        selectOptions,
        setDefaultRolesOptions
      }
    },
    render () {
      const {
        searchData,
        loading,
        attributeTree,
        getPopupContainer,
        showLevel,
        areaDepartSelectorIsLoad,
        pageType,
        selectFocus,
        selectBlur,
        selectSearch,
        handleSelect,
        selectPopupScroll,
        selectOptions
      } = this
      return (
      <ni-filter
        form={searchData}
        loading={loading}
        onFilter={() => {
          this.$emit('search')
        }}
        label-width={85}
        immediate={false}
        class="search-box"
      >
        {pageType !== 2 && (
          <ni-filter-item label="统计方案">
            <Select
              v-model={searchData.type}
              placeholder="统计方案"
              show-search={true}
              option-filter-prop="children"
              options={typeOptionsMap.get(pageType)}
              onChange={() => { searchData.areaId = []; searchData.ch999Id = [] }}
            ></Select>
          </ni-filter-item>
        )}
        <ni-filter-item label="地区">
          <ni-area-select
          v-model={searchData.areaId}
          multiple
          allowClear
          maxTagCount={1}
          ref="areaIdRef"
          placeholder="选择地区"
          style="width: 200px;"
          />
        </ni-filter-item>
        <ni-filter-item label="接待时间">
          <DatePicker.RangePicker
            v-model={searchData.time}
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD HH:mm:ss"
            allow-clear={true}
          ></DatePicker.RangePicker>
        </ni-filter-item>
        {pageType !== 3 && (
          <ni-filter-item label="员工角色">
          <Select
          placeholder="请输入员工角色"
          show-arrow
          allow-clear={true}
          option-filter-prop="children"
           max-tag-count={1}
          mode="multiple"
          v-model={searchData.roleId}
          onFocus={selectFocus}
          onBlur={selectBlur}
          onSelect={handleSelect}
          onSearch={selectSearch}
          onPopupScroll={selectPopupScroll}
          options={selectOptions}
          >
        </Select>
          </ni-filter-item>
        )}
        {pageType !== 3 && (
          <ni-filter-item label="门店属性">
            <TreeSelect
              show-arrow
              show-search
              tree-node-filter-prop="title"
              tree-checkable
              allow-clear
              dropdown-style={{ maxHeight: '300px' }}
              placeholder="请选择门店属性"
              get-popup-container={getPopupContainer}
              max-tag-count={1}
              tree-data={attributeTree || []}
              value={searchData.attribute}
              onChange={(val) => {
                searchData.attribute = val
              }}
            />
          </ni-filter-item>
        )}
        {pageType !== 3 && (
          <ni-filter-item label="门店类型">
            <Select
              v-model={searchData.shopCategory}
              allow-clear={true}
              mode="multiple"
              option-filter-prop="children"
              placeholder="请选择门店类型"
              max-tag-count={1}
              options={shopCategoryOptions}
            ></Select>
          </ni-filter-item>
        )}
        {pageType !== 3 && (
          <ni-filter-item label="门店类别">
            <Select
              v-model={searchData.kind}
              option-filter-prop="children"
              allow-clear={true}
              show-search={true}
              placeholder="请选择门店类别"
              options={kindOptions}
            ></Select>
          </ni-filter-item>
        )}
        {pageType === 2 && (
          <ni-filter-item label="服务类型">
            <a-select placeholder="请选择"
                      allowClear
                      mode="multiple"
                      v-model={searchData.serviceType}
                      get-popup-container={getPopupContainer}
                      options={ options.serviceType }/>
          </ni-filter-item>
        )}
        {pageType === 2 && (
          <ni-filter-item label="符合CF">
            <a-select placeholder="请选择"
                      allowClear
                      v-model={searchData.isCF}
                      get-popup-container={getPopupContainer}
                      options={ options.conformCf }/>
          </ni-filter-item>
        )}
        {pageType === 2 && (
          <ni-filter-item label="是否成交">
            <a-select placeholder="请选择"
                      allowClear
                      v-model={searchData.isSuccess}
                      get-popup-container={getPopupContainer}
                      options={ options.clinch }/>
          </ni-filter-item>
        )}
        {((pageType === 1 && searchData.type === 3469) || (pageType === 2 && searchData.type === 4667)) ? (
          <ni-filter-item label="员工">
            <NiStaffSelect
            class={{ 'more-select': searchData.ch999Id && searchData.ch999Id.length && searchData.ch999Id.length > 1 }}
              v-model={searchData.ch999Id}
              max-tag-count={1}
              multiple={true}
              option-filter-prop="children"
              allow-clear={true}
              show-search={true}
              placeholder="请输入"
            ></NiStaffSelect>
          </ni-filter-item>
        ) : null}
      </ni-filter>
      )
    },
  })
</script>
<style lang="scss" scoped>
.more-select{
  :deep(.ant-select-selection__choice){
    max-width: 45%;
  }
}
</style>
