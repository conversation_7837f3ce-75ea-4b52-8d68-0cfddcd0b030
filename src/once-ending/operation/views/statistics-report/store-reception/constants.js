const xtenant = window.tenant.xtenant
export const showLevelMap = new Map([[3470, 3], [3471, 4], [3459, 0], [3469, 0], [3462, 0], [3465, 0], [3463, 4], [3464, 3]])
export const typeOptionsMap = new Map([[1, [
  {
    label: '大区',
    value: 3470
  },
  {
    label: '小区',
    value: 3471
  },
  {
    label: '门店',
    value: 3459
  },
  {
    label: '员工',
    value: 3469
  }
]], [3, [{
  label: '时间',
  value: 3465
},
{
  label: '小区',
  value: 3463
},
{
  label: '大区',
  value: 3464
}]]])
export const shopCategoryOptions = window.tenant.xtenant < 1000 ? [
  {
    label: '省会店',
    value: 1
  },
  {
    label: '市级店',
    value: 2
  },
  {
    label: '县级店',
    value: 3
  }
] : [
  {
    label: '商超店',
    value: 1
  },
  {
    label: '手机商圈店',
    value: 3
  },
  {
    label: '临街店',
    value: 5
  }
]
export const kindOptions = [
  {
    label: '自营',
    value: 1
  },
  {
    label: '加盟',
    value: 2
  },
  {
    label: '小店',
    value: 3
  }
]
export const pageTypeMap = new Map([['/operation/statistics-report/store-reception', 1], ['/operation/statistics-report/store-reception/detail', 2], ['/operation/statistics-report/store-reception/chart', 3]])

export const options = {
  serviceType: [
    {
      label: '大件',
      value: 1
    },
    {
      label: '小件',
      value: 2
    },
    {
      label: '回收',
      value: 7
    },
    {
      label: '小件售后',
      value: 3
    },
    {
      label: '维修',
      value: 4
    },
    {
      label: '技术服务',
      value: 8
    }
  ],
  conformCf: [
    {
      label: '是',
      value: '1'
    },
    {
      label: '否',
      value: '0'
    }
  ],
  clinch: [
    {
      label: '成交',
      value: '1'
    },
    {
      label: '未成交',
      value: '0'
    }
  ]
}
xtenant === 0 && (options.serviceType.push({
  label: '业务咨询',
  value: 9
}))
export const newType = [
  {
    label: '大区',
    value: 3470,
    newValue: 4655
  },
  {
    label: '小区',
    value: 3471,
    newValue: 4656
  },
  {
    label: '门店',
    value: 3459,
    newValue: 4657
  },
  {
    label: '员工',
    value: 3469,
    newValue: 4647
  }
]
