<script lang="jsx">
  import { defineComponent, ref, computed, provide, nextTick, getCurrentInstance } from 'vue'
  import { NiListPage } from '@jiuji/nine-ui'
  import useLastUpdateDate from '../hooks/useLastUpdateDate'
  import SearchBox from './components/search-box'
  import FrameBox from './components/frame-box'
  import { message, Button } from 'ant-design-vue'
  import { dateFormat } from '~/util/common'
  import { ATTRIBUTE, AREACALL_DATA, AREACALL_DATA_DETAIL, AREACALL_DATA_GRAPH, LIST_ALL_ROLES } from '@operation/store/modules/statistics-report/action-types'
  import * as constants from './constants'
  import moment from 'moment'
  export default defineComponent({
    components: {
      NiListPage,
      SearchBox,
      FrameBox
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const pageType = computed(() => constants.pageTypeMap.get(proxy.$route.path))
      provide('pageType', pageType)
      const loading = ref(false)
      const isFeatch = ref(false)
      const { lastUpdateDate, setLastUpdateDate } = useLastUpdateDate()
      const searchData = ref({
        type: pageType.value === 1 ? 3469 : pageType.value === 2 ? 4667 : 3465,
        areaId: [],
        roleId: [],
        attribute: [],
        shopCategory: [],
        kind: undefined,
        time: [
          dateFormat(new Date(), 'YYYY-MM-DD 00:00:00'),
          dateFormat(new Date(), 'YYYY-MM-DD 23:59:59')
        ],
        ch999Id: [],
        isSuccess: undefined,
        isCF: undefined,
        serviceType: undefined
      })
      const frameUrl = ref('')
      const attributeTree = ref([])
      const rolesOptions = ref([])
      const searchBoxRef = ref(null)

      const getParams = function () {
        const { type, areaId, roleId, attribute, time, shopCategory, kind, ch999Id, isSuccess, isCF, serviceType } = searchData.value
        const params = {
          type
        }
        if (areaId && areaId.length) {
          params.area_id = areaId.map(d => d.replace(/[A-Za-z]/g, '')).join(',')
        } else {
          params.area_id = getTreeValue(proxy.$refs.searchBoxRef.$refs.areaIdRef.$refs.vcNiAreaSelectRef.comTreeDatas).map(d => d.replace(/[A-Za-z]/g, '')).join(',')
        }
        if (pageType.value === 1 || pageType.value === 2) {
          if (roleId && roleId.length) {
            params.role_id = roleId.join(',')
          } else {
            params.role_id = -1
          }
          if (attribute && attribute.length) {
            params.attribute = attribute.join(',')
          } else {
            params.attribute = -1
          }
          if (shopCategory && shopCategory.length) {
            params.shop_category = shopCategory.join(',')
          } else {
            params.shop_category = -1
          }
          if (kind) {
            params.kind1 = kind
          } else {
            params.kind1 = -1
          }
          if ((pageType.value === 1 && type === 3469) || (pageType.value === 2 && type === 4667)) {
            params.ch999_id = ch999Id.length ? ch999Id.join(',') : -1
          }
        }
        if (pageType.value === 2) {
          params.is_success = isSuccess ? +isSuccess : -1
          params.is_cf = isCF ? +isCF : -1
          params.service_type = (serviceType && serviceType.length) ? serviceType.join(',') : -1
        }
        const name = proxy.$tnt.name
        if (name === 'dev') {
          if (pageType.value === 1 && params.type) {
            params.type = constants.newType.find(it => it.value === params.type)?.newValue
          } else if (pageType.value === 2) {
            params.type = 4658
          }
        }
        if (time && time.length) {
          params.start_time = time[0]
          params.end_time = time[1]
        }
        delete params.time
        return params
      }

      const getTreeValue = function (treeData = []) {
        const treeValue = []
        mapTree(treeData, treeValue)
        return treeValue
      }

      const mapTree = function (arr, list) {
        arr.map(d => {
          if (d.children && d.children.length) {
            mapTree(d.children, list)
          } else {
            list.push(d.value)
          }
        })
      }

      const toLink = function (type) {
        let query = {}
        if (type === '/detail') {
          const params = { ...searchData.value }
          delete params.type
          query = { copySearch: JSON.stringify({ ...params }) }
        }
        const routeData = proxy.$router.resolve({
          path: `/operation/statistics-report/store-reception${type}`,
          query
        })
        window.open(routeData.href, '_blank')
      }

      const areacallData = async function () {
        if (checkForm()) return
        const params = { ...getParams() }
        const API_KEY = pageType.value === 1 ? AREACALL_DATA : pageType.value === 2 ? AREACALL_DATA_DETAIL : AREACALL_DATA_GRAPH
        loading.value = true
        const res = await proxy.$store.dispatch(
          `operation/statisticsReport/${API_KEY}`,
          params
        )
        loading.value = false
        isFeatch.value = true
        setLastUpdateDate()
        if (res) {
          const { data } = res
          frameUrl.value = data
        }
      }

      const checkForm = function () {
        const {
          time,
          areaId,
          type
        } = searchData.value
        if (pageType.value === 3 && (type === 3471 || type === 3463) && areaId.length > 34) {
          message.warning('最多支持展示34个小区数据，请删减后再查询！')
          return true
        }
        if (!time.length) {
          message.warning('请选择时间段')
          return true
        }
        if (pageType.value === 3 &&
          moment(time[0])
            .add(31, 'day')
            .format('YYYY-MM-DD HH:mm:ss') < time[1]
        ) {
          message.warn('最多支持展示31天数据，请删减后再查询！')
          return true
        }
        return false
      }

      const getAttribute = async function () {
        const res = await proxy.$store.dispatch(
          `operation/statisticsReport/${ATTRIBUTE}`)
        if (res) {
          const { data } = res
          attributeTree.value = setTree(data)
        }
      }

      const listAllRoles = async function () {
        const res = await proxy.$store.dispatch(
          `operation/statisticsReport/${LIST_ALL_ROLES}`)
        if (res) {
          const { data } = res
          rolesOptions.value = data.map(d => {
            return {
              label: d.name,
              value: d.value
            }
          })
          nextTick(() => {
            proxy.$refs.searchBoxRef.setDefaultRolesOptions()
          })
        }
      }

      const setTree = function (arr) {
        return arr.map(d => {
          d.title = d.key
          if (d.children && d.children.length) {
            d.children = setTree(d.children)
          }
          return d
        })
      }

      const setDefaultQuery = function () {
        const { copySearch: copySearchStr } = proxy.$route.query
        const dataObj = copySearchStr ? JSON.parse(copySearchStr) : {}
        searchData.value = { ...searchData.value, ...dataObj }
      }
      if (pageType.value === 2) {
        setDefaultQuery()
      }

      const init = async function () {
        await listAllRoles()
        await getAttribute()
        areacallData()
      }

      return {
        lastUpdateDate,
        searchData,
        frameUrl,
        loading,
        toLink,
        attributeTree,
        areacallData,
        searchBoxRef,
        init,
        rolesOptions,
        pageType,
        isFeatch
      }
    },
    render () {
      const {
        lastUpdateDate,
        searchData,
        frameUrl,
        loading,
        toLink,
        attributeTree,
        areacallData,
        init,
        rolesOptions,
        pageType,
        isFeatch
      } = this

      return (
      <page class="store-reception">
        <div slot="extra"><div class="header-box"><span>数据更新于{lastUpdateDate}</span>
        {
          pageType === 1 && <div><Button type="link" onClick={() => { toLink('/detail') }}>接待明细</Button><Button type="link" onClick={() => { toLink('/chart') }}>门店接单与订单转化走势图</Button></div>
        }
        {
          pageType === 3 && <Button type="link" onClick={() => { toLink('') }}>门店接待统计</Button>
        }
        </div></div>
        <ni-list-page push-filter-to-location={false}>
          <search-box
          ref="searchBoxRef"
            search-data={searchData}
            onSearch={areacallData}
            loading={loading}
            attribute-tree={attributeTree}
            roles-options={rolesOptions}
            onIsLoad={init}
          ></search-box>
          <frame-box
            frame-url={frameUrl}
            is-featch={isFeatch}
          ></frame-box>
        </ni-list-page>
      </page>
      )
    }
  })
</script>
<style lang="scss">
.store-reception {
  position: relative;
  height: 100vh;
  .ant-page-header-heading-extra {
    float: none;
    line-height: 40px;
    font-size: 12px;
    color: #9c9c9c;
    .header-box{
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>
