<script lang="jsx">
  import { provide } from 'vue'
  import { createState } from './model/useState.js'

  import { NiListPage } from '@jiuji/nine-ui'
  import FilterBox from './components/filter-box'
  import TableBox from './components/table-box'

  export default {
    components: {
      NiListPage,
      FilterBox,
      TableBox
    },
    setup (_) {
      createState()
    },
    render () {
      return (
        <page class="evaluate-list">
          <NiListPage push-filter-to-location={false}>
            <FilterBox/>
            <TableBox/>
          </NiListPage>
        </page>
      )
    }
  }
</script>
