<script type="text/jsx" lang="jsx">
  export default ({ props }) => {
    const {
      contentFeild, // 日志内容字段
      newLog, // 日志form
      addLogs,
      maxLength = 200,
    } = props

    return (
      <a-form wrapper-col={{ span: 24 }}>
        <a-form-item label="添加备注">
          <a-textarea
            allowClear
            placeholder={`请输入${maxLength}字以下日志内容`}
            value={ newLog[contentFeild] }
            rows={2}
            maxLength={maxLength}
            onChange={ e => { newLog[contentFeild] = e.target.value } }
          />
        </a-form-item>
        <div class="text-right">
          <a-button type="primary" onClick={ addLogs }>添加</a-button>
        </div>
      </a-form>
    )
  }
</script>
