<script type="text/jsx" lang="jsx">
  import { Empty } from 'ant-design-vue'
  export default ({ props }) => {
    const {
      logs
    } = props
    return (
      <ul class="logs-warp">
        { !logs?.length
          ? <Empty />
          : logs.map((item, index) => (
            <li key={ index }
              class={[index === logs.length - 1 ? 'last-record' : '']}>
              <p class="description">{ item.content }</p>
            </li>
          ))
        }
      </ul>
    )
  }
</script>

<style lang="scss" scope>
  $timeline-icon-border-width: 3px;
  $timeline-icon-content-size: 4px;
  $timeline-icon-size: $timeline-icon-content-size + $timeline-icon-border-width * 2;

  .logs-warp{
    margin-bottom: 16px;
    max-height: 55vh;
    overflow-y: auto;
    li {
      padding: 10px 0 10px 30px;
      position: relative;

      &:before,
      &:after {
        display: block;
        content: '';
        position: absolute;
        z-index: 4;
      }

      &:before {
        width: $timeline-icon-size;
        height: $timeline-icon-size;
        border-radius: 50%;
        background: #ffffff;
        border: $timeline-icon-border-width solid #239DFC;
        left: 10px;
        top: 18px;
      }

      &:after {
        height: 100%;
        width: 1px;
        left: 15px;
        background: #239DFC;
        top: 1.5em;
        z-index: 3;
      }

      &.last-record:after {
        display: none;
      }
      .description {
        line-height: 24px;
        color: #828282;
      }
    }
  }
</style>
