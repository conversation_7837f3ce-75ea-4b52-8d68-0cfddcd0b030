<script type="text/jsx" lang="jsx">
  import { reactive, onMounted, getCurrentInstance, computed } from 'vue'
  import AddLogs from './add-logs'
  import Logs from './logs'
  export default {
    name: 'modal-logs',
    components: {
      AddLogs,
      Logs
    },
    props: {
      visible: {
        type: <PERSON><PERSON>an,
        default: false
      },
      recordId: {
        type: Number,
        default: null
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const logState = reactive({
        newLog: {
          content: '', // 每个添加日志接口对应的内容字段不一样
          loading: false
        },
        update: 1,
      })

      const featchLogs = async () => {
        const params = { id: props.recordId }
        const { flag, res } = await proxy.$store.dispatch(
          'operation/evaluate/getJifenLogs',
          params
        )
        if (!flag) return
        logState.update = logState.update + 1 // 刷新日志显示组件
      }
      const addLogsAsync = async () => { // 添加日志
        if (!logState.newLog.content) {
          proxy.$message.error('备注内容不能为空')
          return
        }
        const payload = {
          id: props.recordId,
          content: logState.newLog.content
        }
        const isSuccess = await proxy.$store.dispatch(
          'operation/evaluate/addJifenLogs',
          payload
        )
        if (!isSuccess) return
        logState.newLog.content = ''
        featchLogs()
      }
      const closeModal = () => {
        proxy.$store.commit('operation/evaluate/setJifenLogs', [])
        proxy.$emit('handleCancel')
      }

      onMounted(() => {
        featchLogs()
      })

      return {
        logState,
        jifenLogs: computed(() => proxy.$store.state.operation.evaluate.jifenLogs),

        addLogsAsync,
        closeModal
      }
    },
    render () {
      const {
        visible,
        logState: {
          newLog,
          update
        },
        jifenLogs,
        addLogsAsync,
        closeModal
      } = this
      return (
        <a-modal
          title="查看日志"
          visible={ visible }
          width={ 600 }
          onCancel={ closeModal }
          footer={ false }
        >
          { !!update && <Logs logs={ jifenLogs || [] }/> }
          <AddLogs
            contentFeild='content'
            newLog={ newLog }
            addLogs={ addLogsAsync }
          />
        </a-modal>
      )
    }
  }
</script>
