
<script type='text/jsx' lang="jsx">
  import { getCurrentInstance, toRefs, ref, computed } from 'vue'
  import { useState } from '../model/useState'
  import useActions from '../model/useActions.js'
  import { statuEnums } from '../../constants.js'

  import { NiTable } from '@jiuji/nine-ui'
  import NoData from '~/components/no-data'
  import ModalLogs from './logs/modal-logs'

  export default {
    name: 'display-table',
    components: {
      NiTable,
      NoData,
      ModalLogs
    },
    data () {
      return {
        columns: [
          {
            key: 'createTime',
            title: '导入时间',
            dataIndex: 'createTime',
            width: 100
          },
          {
            key: 'name',
            title: '文件名称',
            dataIndex: 'name',
            width: 500
          },
          {
            key: 'status',
            title: '状态',
            dataIndex: 'status',
            width: 70,
            customRender: text => {
              if (text == null) return '-'
              return <i>{ statuEnums.map.get(text) }</i>
            }
          },
          {
            key: 'action',
            title: '操作',
            dataIndex: 'action',
            width: 160,
            align: 'center',
            customRender: (_, record) => {
              return (
                <span>
                  {
                    record.status === 0 && this.mangeRank &&
                    <span>
                      <a-button onClick={ () => { this.handleJifenEffect(record.id) } } class='mr-8' type="primary" size='small'>
                        积分生效
                      </a-button>
                      <a-popconfirm
                        title='确认删除这条记录？'
                        okText="删除"
                        cancelText="取消"
                        okType="danger"
                        onConfirm={ () => { this.handleDeleteJifen(record.id) } }
                      >
                        <a-button class='mr-8' type="primary" size='small'>
                          删除
                        </a-button>
                      </a-popconfirm>
                    </span>
                  }
                  <a-button onClick={ () => { this.toDetail(record.id) } } class='mr-8' type="primary" size='small'>
                    详情
                  </a-button>
                  <a-button onClick={ () => { this.showLogs(record.id) } } class='mr-8' type="primary" size='small'>
                    查看日志
                  </a-button>
                </span>
              )
            }
          }
        ]
      }
    },
    setup (_) {
      const { proxy } = getCurrentInstance()
      const {
        state,
        setModalLogsIsShow,
        setCurId
      } = useState()
      const {
        handleImportJifenData,
        handleDownloadJifenTemplate,
        handleJifenEffect,
        handleDeleteJifen,
        handleTableChange
      } = useActions()
      const fileRef = ref('')

      const showLogs = id => {
        setCurId(id)
        setModalLogsIsShow(true)
      }
      const closeLogs = () => {
        setCurId()
        setModalLogsIsShow(false)
      }
      const toDetail = id => {
        let routeData = proxy.$router.resolve({ path: `integral-manage/${id}` })
        window.open(routeData.href, '_blank')
      }
      const upload = () => {
        fileRef.value.click()
      }

      return {
        fileRef,
        ...toRefs(state),
        setModalLogsIsShow,
        handleImportJifenData,
        handleDownloadJifenTemplate,
        showLogs,
        closeLogs,
        toDetail,
        handleJifenEffect,
        handleDeleteJifen,
        handleTableChange,
        upload,
        mangeRank: computed(() => proxy.$store.state.userInfo.Rank.includes('jfgl'))
      }
    },
    render () {
      const {
        mangeRank,
        columns,
        isFeatch,
        loading,
        importLoading,
        downLoading,
        pagination,
        curId,
        modalLogsIsShow,

        closeLogs,
        handleTableChange,
        upload,
        handleImportJifenData,
        handleDownloadJifenTemplate,
      } = this

      const locale = { emptyText: <NoData is-featch={ isFeatch } /> }
      const dataList = this.$store.state.operation.evaluate.jifenList

      return (
        <div>
          <NiTable
            locale={ locale }
            dataSource={ dataList }
            columns={ columns }
            loading={ loading }
            rowKey={ (record, index) => index }
            pagination={ pagination }
            onChange={ handleTableChange }
          >
            <template slot="action">
              <input
                style="display: none"
                ref='fileRef'
                type="file"
                accept=".xls,.xlsx"
                name="file"
                onChange={handleImportJifenData}/>
              {
                mangeRank && <a-button
                    class="ml-20 btn-success"
                    loading={ importLoading }
                    onClick={ upload }
                  >
                    { !importLoading ? '导入' : '导入中...' }
                </a-button>
              }
              <a-button
                class="ml-8 btn-success"
                loading={ downLoading }
                onClick={ handleDownloadJifenTemplate }
              >
                { !downLoading ? '下载模板' : '下载中...' }
              </a-button>
            </template>
          </NiTable>
          {
            modalLogsIsShow && <ModalLogs
              visible={ modalLogsIsShow }
              recordId={ curId }
              onHandleCancel={ closeLogs }
            />
          }
        </div>
      )
    }
  }
</script>
