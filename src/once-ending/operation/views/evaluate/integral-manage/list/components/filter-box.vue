
<template>
  <ni-filter
    class="mb-16"
    :form="query"
    :loading="loading"
    :label-width="0"
    :immediate="false"
    :saveAble="false"
    @filter="doSearch"
  >
    <ni-filter-item>
      <a-range-picker
        v-model="query.rangeTims"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
      />
    </ni-filter-item>
    <ni-filter-item>
      <a-select
        allowClear
        placeholder="状态"
        :maxTagCount="1"
        :options="statuEnums.options"
        v-model="query.status"
      />
    </ni-filter-item>
  </ni-filter>
</template>
<script type='text/jsx'>
  import { onMounted, toRefs } from 'vue'
  import { useState } from '../model/useState'
  import useActions from '../model/useActions'
  import { statuEnums } from '../../constants.js'
  import { NiFilter, NiFilterItem } from '@jiuji/nine-ui'

  export default {
    name: 'filter-box',
    components: {
      <PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON>ilter<PERSON><PERSON>
    },
    props: {
      options: {
        type: Object,
        default: () => ({})
      }
    },
    setup (_) {
      const { state } = useState()
      const { fetchData } = useActions()

      const doSearch = () => { fetchData(1) }

      onMounted(() => {
        doSearch()
      })

      return {
        statuEnums,
        ...toRefs(state),
        doSearch,
      }
    },
  }
</script>
<style lang="scss" scoped>
.ant-input-group-compact{
  display: flex;
 }
:deep(.filter-item) {
  .ant-select{
    min-width: 0;
  }
  .label{
    display: none;
  }
}
.ant-input-group-compact{
  display: flex;
}
</style>
