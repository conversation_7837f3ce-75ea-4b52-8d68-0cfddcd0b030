import { reactive, inject, provide } from 'vue'

const key = Symbol('jifengManageList')

export function useState () {
  return inject(key)
}

export function createState () {
  const state = reactive({
    query: {
      rangeTims: [],
      status: undefined,
    },
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      pageSizeOptions: ['10', '20', '50', '100'],
      showSizeChanger: true,
      showTotal: total => `共 ${total} 条数据`
    },
    isFeatch: false,
    loading: false,
    downLoading: false,
    importLoading: false,
    modalLogsIsShow: false,
    curId: undefined,
  })
  const setObj = objKey => (val, key) => {
    if (key && typeof key === 'string') {
      state[objKey][key] = val
    } else {
      state[objKey] = val
    }
  }
  const setQuery = setObj('query') // 筛选参数相关
  const setQueryParams = val => { state.queryParams = val }
  const setPagination = setObj('pagination')
  const setIsFeatch = val => { state.isFeatch = val || false }
  const setLoading = val => { state.loading = val || false }
  const setImportLoading = val => { state.importLoading = val || false }
  const setDownLoading = val => { state.downLoading = val || false }
  const setModalLogsIsShow = val => { state.modalLogsIsShow = val || false }
  const setCurId = val => { state.curId = val }

  const jifengManageList = {
    state,

    setQuery,
    setQueryParams,
    setPagination,
    setIsFeatch,
    setLoading,
    setImportLoading,
    setDownLoading,
    setModalLogsIsShow,
    setCurId
  }
  provide(key, jifengManageList)
  return jifengManageList
}
