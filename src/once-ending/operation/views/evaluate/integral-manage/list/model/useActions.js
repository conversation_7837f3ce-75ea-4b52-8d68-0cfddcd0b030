
import { getCurrentInstance } from 'vue'
import operationApi from '@operation/api'
import { useState } from './useState'
import { newExportXlsx } from '@operation/util/export'

export default function useActions () {
  const { proxy } = getCurrentInstance()
  const {
    state,
    setPagination,
    setIsFeatch,
    setDownLoading,
    setImportLoading
  } = useState()
  const fetchData = async (curPage) => {
    curPage && setPagination(curPage, 'current')
    const params = {
      current: state.pagination.current,
      size: state.pagination.pageSize,
      ...state.query,
    }
    const { rangeTims } = params
    if (rangeTims.length) {
      params.startDate = `${rangeTims[0]} 00:00:00`
      params.endDate = `${rangeTims[1]} 23:59:59`
    }
    delete params.rangeTims
    proxy.$indicator.open()
    const { flag, res } = await proxy.$store.dispatch(
      `operation/evaluate/getJifenList`,
      params
    )
    proxy.$indicator.close()
    if (!flag) return
    setPagination(res.data.total, 'total')
    if (!state.isFeatch) setIsFeatch(true)
  }
  const handleTableChange = (pagination) => {
    setPagination(pagination)
    fetchData()
  }
  const handleImportJifenData = async (e) => {
    const selectFile = e.target.files[0]
    if (selectFile && selectFile.length === 0) return
    const regExcel = /.(xls|xlsx)$/i
    if (!regExcel.test(selectFile.name)) {
      message.error('数据导入失败，请重新导入Excel表格文件')
      return
    }
    const formData = new FormData()
    formData.append('file', selectFile)
    setImportLoading(true)
    const res = await proxy.$store.dispatch('operation/evaluate/importJifenData', formData)
    setImportLoading(false)
    if (!res || res.code !== 0) return
    e.target.value = ''
    fetchData()
  }
  const handleDownloadJifenTemplate = () => {
    let url = operationApi.evaluate.downloadJifenTemplate()
    const fileName = `客评投诉积分管理模板.xlsx`
    setDownLoading(true)
    newExportXlsx({
      url,
      method: 'get',
      fileName
    }).finally(() => {
      setDownLoading(false)
    })
  }
  const handleJifenEffect = async (id) => {
    const res = await proxy.$store.dispatch(
      'operation/evaluate/jifenEffect', { id }
    )
    if (!res || res.code !== 0) return
    fetchData()
  }
  const handleDeleteJifen = async (id) => {
    const res = await proxy.$store.dispatch( // 删除
      'operation/evaluate/deleteJifen', { id }
    )
    if (!res || res.code !== 0) return
    fetchData()
  }

  return {
    fetchData,
    handleTableChange,
    handleImportJifenData,
    handleDownloadJifenTemplate,
    handleJifenEffect,
    handleDeleteJifen,
  }
}
