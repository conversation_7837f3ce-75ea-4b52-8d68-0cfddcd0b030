
import { getCurrentInstance } from 'vue'
import { useState } from './useState'

export default function useActions () {
  const instance = getCurrentInstance().proxy
  const { state } = useState()

  const fetchData = async () => {
    const params = { ...state.query }
    instance.$indicator.open()
    await instance.$store.dispatch(
      'operation/evaluate/getJifenDetailList',
      params
    )
    instance.$indicator.close()
  }
  return {
    fetchData,
  }
}
