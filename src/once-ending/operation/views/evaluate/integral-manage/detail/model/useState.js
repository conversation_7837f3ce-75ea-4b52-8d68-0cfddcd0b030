import { reactive, inject, provide, getCurrentInstance } from 'vue'
const key = Symbol('jifenList')

export function useState () {
  return inject(key)
}

export function createState () {
  const { proxy } = getCurrentInstance()
  const { id } = proxy.$route.params
  const state = reactive({
    query: {
      id,
      keyWord: '',
    },
  })
  const setObj = objKey => (val, key) => {
    if (key && typeof key === 'string') {
      state[obj<PERSON><PERSON>][key] = val
    } else {
      state[objKey] = val
    }
  }
  const setQuery = setObj('query')

  const jifenList = {
    state,
    setQuery,
  }
  provide(key, jifenList)
  return jifenList
}
