<script type='text/jsx' lang="jsx">
  import { onMounted, toRefs, getCurrentInstance } from 'vue'
  import { useState } from '../model/useState'
  import useActions from '../model/useActions.js'
  import { NiFilter, NiFilterItem } from '@jiuji/nine-ui'

  import * as constants from '../../constants.js'

  export default {
    name: 'filter-box',
    components: {
      NiFilter,
      NiFilterItem
    },
    setup (_) {
      const { state } = useState()
      const { fetchData } = useActions()

      const doSearch = () => { fetchData(1) }

      onMounted(() => {
        doSearch()
      })

      return {
        constants,
        ...toRefs(state),
        doSearch,
      }
    },
    render () {
      const {
        query,
        loading,
        doSearch
      } = this
      return (
        <ni-filter
          class="mb-16"
          form={ query }
          loading={ loading }
          label-width={ 0 }
          immediate={ false }
          saveAble={ false }
          onFilter={ doSearch }
        >
          <ni-filter-item>
            <a-input
              v-model={ query.keyWord }
              placeholder="员工工号/姓名"
            />
          </ni-filter-item>
        </ni-filter>
      )
    }
  }
</script>
<style lang="scss" scoped>
.ant-input-group-compact{
  display: flex;
 }
:deep(.filter-item) {
  .ant-select{
    min-width: 0;
  }
  .label{
    display: none;
  }
}
.ant-input-group-compact{
  display: flex;
}
</style>
