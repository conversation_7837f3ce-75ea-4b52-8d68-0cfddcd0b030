
<script type='text/jsx' lang="jsx">

  import { NiTable } from '@jiuji/nine-ui'

  export default {
    name: 'display-table',
    components: {
      NiTable
    },
    data () {
      return {
        columns: [
          {
            key: 'ch999Id',
            title: '员工工号',
            dataIndex: 'ch999Id',
            customRender: text => <span>{ text || '_'}</span>
          },
          {
            key: 'ch999Name',
            title: '姓名',
            dataIndex: 'ch999Name',
            customRender: text => <span>{ text || '_'}</span>
          },
          {
            key: 'jifen',
            title: '积分',
            dataIndex: 'jifen',
            customRender: text => <span>{ text ?? '_'}</span>
          }
        ]
      }
    },
    render () {
      const dataList = this.$store.state.operation.evaluate.jifenDetaiList
      return (
        <ni-table
          dataSource={ dataList }
          columns={ this.columns }
          rowKey={ (record, index) => index }
          pagination={ false }
        />
      )
    }
  }
</script>
