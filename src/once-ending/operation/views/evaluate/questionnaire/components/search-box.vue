<script lang="jsx">
  import { defineComponent } from 'vue'
  import { Ni<PERSON><PERSON>er, NiFilterItem } from '@jiuji/nine-ui'
  import { Input, DatePicker } from 'ant-design-vue'
  export default defineComponent({
    props: {
      searchData: {
        type: Object,
        default: () => ({})
      },
      loading: {
        type: Boolean,
        default: false
      }
    },
    components: {
      NiFilter,
      NiFilterItem
    },
    render () {
      const { searchData, loading } = this
      return (
        <ni-filter
        form={searchData}
        loading={loading}
        onFilter={() => {
          this.$emit('search')
        }}
        label-width={85}
        immediate={false}
      >
      <ni-filter-item label="问卷名称">
        <Input v-model={searchData.surveyName} placeholder="请输入问卷名称" allow-clear={true}/>
      </ni-filter-item>
       <ni-filter-item label="创建时间">
        <DatePicker.RangePicker
        v-model={searchData.time}
        value-format="YYYY-MM-DD"
        allow-clear={true}
        >
        </DatePicker.RangePicker>
      </ni-filter-item>
      </ni-filter>
      )
    }
  })
</script>
