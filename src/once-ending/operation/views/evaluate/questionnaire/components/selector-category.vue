<script lang="jsx">
  import { defineComponent, ref, watch, getCurrentInstance } from 'vue'
  import { TreeSelect, message } from 'ant-design-vue'
  import { to } from '@common/utils/common'

  export default defineComponent({
    model: {
      event: 'change',
      props: 'value'
    },
    props: {
      value: {
        type: Array,
        default: () => ([])
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const treeData = ref([])

      const checkedList = ref([])

      const change = function (value, label, extra) {
        proxy.$emit('change', value)
      }

      watch(() => props.value, val => {
        checkedList.value = JSON.parse(JSON.stringify(val))
      })

      const getTreeData = async function () {
        const [err, res] = await to(proxy.$api.common.getGoodsCategory())
        if (err) throw err
        const { code, data, userMsg } = res
        if (code === 0) {
          treeData.value = data
        } else {
          message.error(userMsg)
        }
      }

      getTreeData()

      return {
        treeData,
        checkedList,
        change
      }
    },
    render () {
      const { treeData, change } = this
      return <TreeSelect
      tree-data={treeData}
      v-model={this.checkedList}
      show-search
      allow-clear
      tree-node-filter-prop="title"
      placeholder="商品分类"
      search-placeholder="商品分类"
      dropdown-style={{
          maxHeight: '300px'
        }}
      style="width: 100%"
      tree-checkable={true}
      onChange={change}
      replace-fields={
        {
          label: 'title'
        }
      }
  />
    }
  })
</script>
