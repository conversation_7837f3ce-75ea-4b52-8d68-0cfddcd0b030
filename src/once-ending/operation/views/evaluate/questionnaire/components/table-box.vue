<script lang="jsx">
  import { defineComponent, getCurrentInstance, inject } from 'vue'
  import { NiTable } from '@jiuji/nine-ui'
  import { columns } from '../constants'
  import { Switch, Button, message, Modal } from 'ant-design-vue'
  import evaluate from '@operation/api/evaluate'
  import axios from 'axios'
  import {
    SURVEY_DELETE,
    SURVEY_OPEN
  } from '@operation/store/modules/evaluate/action-types'
  export default defineComponent({
    props: {
      dataSource: {
        type: Array,
        default: () => []
      },
      pagination: {
        type: Object,
        default: () => {
          return {
            current: 1,
            pageSize: 50,
            total: 0,
            pageSizeOptions: ['20', '50', '100', '200'],
            showQuickJumper: true,
            showTotal: total => `共计${total}条`
          }
        }
      },
      loading: {
        type: Boolean,
        default: false
      }
    },
    components: {
      NiTable
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const condition = function (row) {
        proxy.$router.push({
          path: `/operation/evaluate/questionnaire/${row.id}`
        })
      }

      const del = function (row) {
        Modal.confirm({
          title: '温馨提示',
          content: '确定删除该问卷配置?',
          onOk () {
            surveyDelete(row)
          }
        })
      }

      const surveyDelete = async function (row) {
        const params = {
          id: row.id
        }
        const res = await proxy.$store.dispatch(`operation/evaluate/${SURVEY_DELETE}`, params)
        if (res) {
          message.success('删除成功')
          proxy.$emit('getData')
        }
      }

      const stateChange = async function (checked, row) {
        const params = {
          id: row.id,
          flag: checked
        }
        const res = await proxy.$store.dispatch(`operation/evaluate/${SURVEY_OPEN}`, params)
        if (res) {
          message.success(`${checked ? '启用' : '停用'}成功`)
          proxy.$emit('getData')
        }
      }

      const download = async function (row) {
        const params = {
          id: row.id
        }
        row.exportDataLoading = true
        axios({
          method: 'post',
          url: evaluate.surveyExport(),
          data: params,
          responseType: 'blob',
          headers: {
            Authorization: proxy.$store.state.token,
            scene: 'survey_import'
          }
        }).then((res) => {
          row.exportDataLoading = false
          const link = document.createElement('a')
          let blob = new Blob([res.data], { type: 'application/x-excel' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = `${row.surveyName}.xlsx`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        })
      }

      const upload = inject('upload')
      const uploadExcelLoading = inject('uploadExcelLoading')
      const downloadIndex = inject('downloadIndex')
      return {
        condition,
        del,
        stateChange,
        download,
        upload,
        uploadExcelLoading,
        downloadIndex
      }
    },
    render () {
      const {
        dataSource,
        pagination,
        loading,
        condition,
        del,
        stateChange,
        download,
        upload,
        uploadExcelLoading,
        downloadIndex
      } = this
      return (
      <ni-table
        dataSource={dataSource}
        columns={columns}
        loading={loading}
        onChange={(pagination, filters, sorter) => {
          this.$emit('tableChange', { pagination, filters, sorter })
        }}
        pagination={pagination}
        scopedSlots={{
          state: (text, record) => {
            return (
              <Switch
                v-model={record.state}
                checked-children="启用"
                un-checked-children="停用"
                onChange={val => stateChange(val, record)}
              ></Switch>
            )
          },
          operation: (text, record) => {
            return (
              <div style="width:300px">
                <Button
                  type="primary"
                  onClick={() => {
                    condition(record)
                  }}
                  style="margin-right:10px"
                >
                  问卷使用条件
                </Button>
                <Button
                  style="margin-right:10px"
                  type="primary"
                  loading={record.exportDataLoading}
                  onClick={() => {
                    download(record)
                  }}
                >
                  {record.exportDataLoading ? '下载中' : '下载问卷'}
                </Button>
                <Button
                  type="primary"
                  onClick={() => {
                    del(record)
                  }}
                >
                  删除
                </Button>
              </div>
            )
          }
        }}
      >
      <div class="flex flex-justify-end" slot="tool">
        <Button type="primary" loading={uploadExcelLoading} onClick={upload} style="margin-right:10px">导入问卷</Button>
        <Button type="primary" onClick={downloadIndex}>下载问卷模板</Button>
      </div>
      </ni-table>
      )
    }
  })
</script>
<style lang="scss" scoped>
.tool {
  float: right;
  .btn {
    margin-left: 8px;
  }
}
</style>
