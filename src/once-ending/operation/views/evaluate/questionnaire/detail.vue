<script lang="jsx">
  import { defineComponent, ref, getCurrentInstance } from 'vue'
  import { Form, TreeSelect, Checkbox, Input, message, Button, Select } from 'ant-design-vue'
  import { NiAreaSelect } from '@jiuji/nine-ui'
  import SelectorCategory from './components/selector-category.vue'
  import { SURVEY_GET_ENUMS, SURVEY_GET, SURVEY_UPDATE } from '@operation/store/modules/evaluate/action-types'

  export default defineComponent({
    components: {
      NiAreaSelect,
      SelectorCategory
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const form = ref({
        areaIds: [],
        memberLevels: [],
        orderTypes: [],
        productCategories: [],
        productIds: [],
        ppriceIds: []
      })

      const areaChange = function (val) {
        form.value.areaIds = val
      }

      const options = ref({
        memberLevel: [],
        orderType: []
      })

      const getEnums = async function () {
        const res = await proxy.$store.dispatch(`operation/evaluate/${SURVEY_GET_ENUMS}`)
        if (res) {
          const { data: { memberLevelEnum, orderTypeEnum } } = res
          options.value.memberLevel = memberLevelEnum
          options.value.orderType = orderTypeEnum
        }
      }

      const surveyGet = async function () {
        const params = {
          id: proxy.$route.params.id
        }
        const res = await proxy.$store.dispatch(`operation/evaluate/${SURVEY_GET}`, params)
        if (res) {
          const { data } = res
          data.productIds = data.productIds ? data.productIds.split(';') : []
          data.ppriceIds = data.ppriceIds ? data.ppriceIds.split(';') : []
          form.value = data
        }
      }

      const surveyUpdate = async function () {
        const { id, orderTypes, areaIds, memberLevels, productCategories, productIds, ppriceIds } = form.value
        const params = {
          id,
          orderTypes,
          areaIds,
          memberLevels,
          productCategories,
          productIds: productIds.join(';'),
          ppriceIds: ppriceIds.join(';')
        }
        const res = await proxy.$store.dispatch(`operation/evaluate/${SURVEY_UPDATE}`, params)
        if (res) {
          message.success('保存成功')
          surveyGet()
        }
      }

      getEnums()
      surveyGet()

      return {
        form,
        areaChange,
        options,
        surveyUpdate
      }
    },
    render () {
      const { form, areaChange, options, surveyUpdate } = this
      return <page>
      <div class="questionnaire-detail">
      <div class="name">{form.surveyName}</div>
        <div class="tip"><span class="tip-title">问卷使用条件配置</span><span class="tip-content">(订单信息及对订单的评价内容同时满足配置的使用条件才能使用问卷)</span></div>
        <div class="form-box">
        <Form layout="vertical">
          <Form.Item label="订单分类">
          <TreeSelect
            treeData={options.orderType}
            v-model={form.orderTypes}
            show-search
            allow-clear
            search-placeholder="请输入"
            style="width: 100%"
            tree-checkable
            tree-node-label-prop="label"
            tree-node-filter-prop="label"
        />
          </Form.Item>
          <Form.Item label="地区">
          <NiAreaSelect style="width:100%" allow-clear={true} show-search={true} value={form.areaIds} multiple onChange={areaChange} />
          </Form.Item>
          <Form.Item label="会员等级">
          <Checkbox.Group v-model={form.memberLevels} options={options.memberLevel}></Checkbox.Group>
          </Form.Item>
          <Form.Item label="商品分类">
          <SelectorCategory v-model={form.productCategories}/>
          </Form.Item>
          <Form.Item label="商品id（输入后按回车确认）">
          <Select v-model={form.productIds} mode="tags" placeholder="请输入商品id" allow-clear options={[]}/>
          </Form.Item>
          <Form.Item label="ppid（输入后按回车确认）">
          <Select v-model={form.ppriceIds} mode="tags" placeholder="请输入商品ppid" allow-clear options={[]}/>
          </Form.Item>
        </Form>
        <div style="text-align: right;">
          <Button type="primary" onClick={surveyUpdate}>保存</Button>
        </div>
        </div>
      </div>
      </page>
    }
  })
</script>
<style lang="scss" scoped>
.questionnaire-detail{
  background: #fff;
  padding: 10px;
  .name,.tip{
    font-size: 18px;
  }
  .tip{
    margin-top: 10px;
    .tip-title{
      font-weight: 600;
    }
    .tip-content{
      color: red;
    }
  }
}
.form-box{
  margin-top: 20px;
}
:deep(.ni-area-content) {
  .ant-select-selection{
    min-height: 150px
  }
}
</style>
