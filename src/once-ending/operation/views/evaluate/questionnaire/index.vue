<script lang="jsx">
  import { defineComponent, ref, getCurrentInstance, provide } from 'vue'
  import { NiListPage } from '@jiuji/nine-ui'
  import SearchBox from './components/search-box'
  import TableBox from './components/table-box'
  import { message, Button } from 'ant-design-vue'
  import { SURVEY_LIST, SURVEY_IMPORT } from '@operation/store/modules/evaluate/action-types'

  export default defineComponent({
    components: {
      NiListPage,
      SearchBox,
      TableBox
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const searchData = ref({
        surveyName: undefined,
        time: []
      })

      const loading = ref(false)

      const file = ref(null)

      const uploadExcelLoading = ref(false)
      provide('uploadExcelLoading', uploadExcelLoading)

      const dataSource = ref([])

      const pagination = ref({
        current: 1,
        pageSize: 50,
        total: 0,
        pageSizeOptions: ['20', '50', '100', '200'],
        showQuickJumper: true,
        showTotal: total => `共计${total}条`
      })

      const tableChange = function ({ paginationObj, filters, sorter }) {
        pagination.value = { ...paginationObj }
        getData()
      }

      const getData = async function (cur) {
        if (cur) pagination.value.current = cur
        const { current, pageSize: size } = pagination.value
        const { surveyName, time } = searchData.value
        const params = {
          surveyName,
          current,
          size
        }
        if (time && time.length) {
          params.startTime = `${time[0]} 00:00:00`
          params.endTime = `${time[1]} 23:59:59`
        }
        loading.value = true
        const res = await proxy.$store.dispatch(`operation/evaluate/${SURVEY_LIST}`, params)
        loading.value = false
        if (res) {
          const { data: { total, records } } = res
          records.forEach(item => {
            item.exportDataLoading = false
          })
          dataSource.value = records
          pagination.value.total = total
        }
      }

      const upload = function () {
        file.value.click()
      }
      provide('upload', upload)

      const selectFile = async function (e) {
        const selectFile = e.target.files[0]
        if (selectFile && selectFile.length === 0) return
        const regExcel = /.(xls|xlsx)$/i
        if (!regExcel.test(selectFile.name)) {
          message.error('导入模板不符合导入规则，数据导入失败，请重新导入')
          return
        }
        const formData = new FormData()
        formData.append('file', selectFile)
        uploadExcelLoading.value = true
        try {
          const res = await proxy.$store.dispatch(`operation/evaluate/${SURVEY_IMPORT}`, formData)
          if (res) {
            message.success('导入成功')
            getData()
          }
        } catch (e) {
          console.error(e)
        } finally {
          e.target.value = ''
          uploadExcelLoading.value = false
        }
      }

      const download = function () {
        window.location.href = 'https://img2.ch999img.com/newstatic/14451/050c65c845f4a741.xlsx'
      }
      provide('downloadIndex', download)

      getData()

      return {
        searchData,
        loading,
        tableChange,
        dataSource,
        pagination,
        getData,
        upload,
        download,
        selectFile,
        file,
        uploadExcelLoading
      }
    },
    render () {
      const { searchData, loading, tableChange, pagination, dataSource, getData, upload, download, selectFile, uploadExcelLoading } = this
      return <page>
      <div slot="extra">
      <div>
      <input style="display: none" ref="file" type="file" accept=".xls,.xlsx" name="file"
             onChange={selectFile}></input>
      </div>
      </div>
        <ni-list-page push-filter-to-location={false}>
          <search-box
            search-data={searchData}
            loading={loading}
            onSearch={() => { getData(1) }}
          ></search-box>
          <table-box
            style="margin-top:16px"
            data-source={dataSource}
            onTableChange={({ pagination, filters, sorter }) => { tableChange({ paginationObj: pagination, filters, sorter }) }}
            pagination={pagination}
            onGetData={() => getData()}
          ></table-box>
        </ni-list-page>
      </page>
    }
  })
</script>
