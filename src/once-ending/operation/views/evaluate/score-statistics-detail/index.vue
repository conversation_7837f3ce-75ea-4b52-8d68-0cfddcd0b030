<script lang="jsx">
  import { provide } from 'vue'
  import { createState } from './model/useState.js'
  import { NiListPage } from '@jiuji/nine-ui'
  import TableBox from './components/table-box'

  export default {
    components: {
      NiListPage,
      TableBox,
    },
    setup () {
      createState(provide)
    },
    render () {
      return (
        <page class="evaluate-list">
          <NiListPage push-filter-to-location={false}>
            <TableBox/>
          </NiListPage>
        </page>
      )
    }
  }
</script>
