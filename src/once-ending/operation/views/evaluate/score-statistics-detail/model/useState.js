import { reactive, inject } from 'vue'

const key = Symbol('evaluateList')

export function useState () {
  return inject(key)
}

export function createState (provide) {
  const state = reactive({
    routeParams: {},
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      pageSizeOptions: ['10', '20', '30', '50'],
      showSizeChanger: true,
      showTotal: total => `共 ${total} 条数据`
    },
    loading: false,
    list: [],
  })
  const setObj = objKey => (val, key) => {
    if (key && typeof key === 'string') {
      state[objKey][key] = val
    } else {
      state[objKey] = val
    }
  }
  const setRouteParams = val => { state.routeParams = val }
  const setPagination = setObj('pagination')

  const setIsFeatch = val => { state.isFeatch = val || false }
  const setLoading = val => { state.loading = val || false }
  const setList = val => { state.list = val || [] }

  const evaluateList = {
    state,

    setRouteParams,
    setPagination,
    setIsFeatch,
    setLoading,
    setList,
  }
  provide(key, evaluateList)
  return evaluateList
}
