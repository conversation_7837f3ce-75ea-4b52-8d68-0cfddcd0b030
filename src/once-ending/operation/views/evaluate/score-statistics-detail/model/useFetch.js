import { getCurrentInstance } from 'vue'
import { useState } from './useState.js'
export default function useFetch () {
  const { proxy } = getCurrentInstance()
  const {
    state,
    setRouteParams,
    setPagination,
    setList,
    setIsFeatch,
  } = useState()

  const fetchData = async () => {
    const { routeParams, pagination } = state
    const params = {
      ...routeParams,
      page: pagination.current,
      size: pagination.pageSize,
    }
    proxy.$indicator.open()
    const res = await proxy.$store.dispatch(
      'operation/evaluate/getEvaluateStatisticsList',
      params
    )
    proxy.$indicator.close()
    if (!res) return
    const { code, data } = res
    if (code !== 0) return
    setList(data.records)
    setPagination(data.total, 'total')
    if (!state.isFeatch) setIsFeatch(true)
  }

  const handleTableChange = (pagination) => {
    setPagination(pagination)
    fetchData()
  }
  const initPage = (routeQuery) => {
    const routeParams = {
      evaluateIdsKey: routeQuery.evaluateIdsKey
    }
    setRouteParams(routeParams)
    fetchData()
  }
  return {
    fetchData,
    initPage,
    handleTableChange
  }
}
