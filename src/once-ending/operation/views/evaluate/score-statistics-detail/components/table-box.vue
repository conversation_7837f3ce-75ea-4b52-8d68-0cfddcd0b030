
<script type='text/jsx' lang="jsx">
  import { computed, toRefs, onMounted, getCurrentInstance } from 'vue'
  import { useState } from '../model/useState.js'
  import useFetch from '../model/useFetch.js'
  import { getEnums } from '../../common/useEnums'

  import { NiTable } from '@jiuji/nine-ui'
  import NoData from '~/components/no-data'
  import RenderSubId from '../../common/renderSubId.jsx'

  export default {
    name: 'display-table',
    components: {
      NiTable,
      NoData,
      RenderSubId
    },
    data () {
      return {
        columns: [
          {
            key: 'id',
            title: '评价ID',
            dataIndex: 'id',
            width: 60,
            customRender: text => <a
              href={`${this.$tnt.oaHost}/staticpc/#/evaluate/detail?evaluateId=${text}`}
              target="_blank"
              >{ text }</a>
          },
          {
            key: 'area',
            title: '地区',
            dataIndex: 'area',
            width: 70,
          },
          {
            key: 'evaluateTypeName',
            title: '类型',
            dataIndex: 'evaluateTypeName',
            width: 60,
          },
          {
            key: 'subId',
            title: '单号',
            dataIndex: 'subId',
            width: 90,
            customRender: (text, record) => <RenderSubId subId={text} record={record}/>
          },
          {
            key: 'dTime',
            title: '评价时间',
            dataIndex: 'dTime',
            width: 100,
          },
          {
            key: 'userClassName',
            title: '会员级别',
            dataIndex: 'userClassName',
            width: 80,
            align: 'center'
          },
          {
            key: 'serviceStaff',
            title: '服务员工',
            dataIndex: 'serviceStaff',
            width: 174,
            customRender: (_, record) => {
              if (!record?.jobList.length) return
              return record.jobList.map((item, index) => {
                if (!item.score) return
                return <div key={ index }>{ item.jobName }: { item.userName }({ item.userId })</div>
              })
            }
          },
          {
            key: 'serviceScore',
            title: '服务分',
            dataIndex: 'serviceScore',
            width: 85,
            customRender: (_, record) => {
              if (!record?.jobList.length) return
              return record.jobList.map((item, index) => {
                if (!item.score) return
                return <div key={ index }>
                  { item.jobName }: { item.score }星
                  { item.fen && <span style="color:red">(积分：{ item.fen })</span>}
                </div>
              })
            }
          },
          {
            key: 'serviceTags',
            title: '服务标签',
            dataIndex: 'scoreTags',
            width: 140,
            customRender: text => {
              const tags = text.filter(_ => _.scoreKinds === 1)
              return tags.map((item, index) => (
                <a-tag key={ index }
                  class="evaluate-tag mb-5"
                  color={ item.tagType === 1 ? 'red' : 'green' }>
                  { item.name }
                </a-tag>
              ))
            }
          },
          {
            key: 'areaScore',
            title: '门店分',
            dataIndex: 'areaScore',
            width: 70,
            align: 'center',
            show: this.$tnt.xtenant >= 1000
          },
          {
            key: 'areaTags',
            title: '门店标签',
            dataIndex: 'areaTags',
            width: 150,
            customRender: text => {
              if (!text.length) return
              return text.map((item, index) => (
                 <a-tag
                    key={index}
                    class="evaluate-tag mb-5"
                    color={item.tagType === 1 ? 'red' : 'green'}
                  >
                  { item.name }
                </a-tag>
              ))
            },
            show: this.$tnt.xtenant >= 1000
          },
          {
            key: 'productScore',
            title: '商品分',
            dataIndex: 'productScore',
            width: 70,
            align: 'center',
            customRender: text => <span> {text && text.toFixed(1)} </span>
          },
          {
            key: 'commendedScore',
            title: '推荐值',
            width: 70,
            align: 'center',
            dataIndex: 'commendedScore',
            customRender: text => <span >{ text >= 0 ? text : text === -1 ? '未评价' : '-' }</span>
          },
          {
            key: 'content',
            title: '员工建议',
            width: 230,
            dataIndex: 'content',
            align: 'center'
          },
          {
            key: 'areaRemark',
            title: '门店建议',
            dataIndex: 'areaRemark',
            width: 230,
            align: 'center',
            show: this.$tnt.xtenant >= 1000
          },
          {
            key: 'evaluateTag',
            title: '评价类别',
            dataIndex: 'evaluateTag',
            width: 90,
            align: 'center',
            customRender: text => {
              if (text == null) return
              const target = this.options.tagType.find(item => item.value === text)
              return <span>{target?.label ?? null}</span>
            }
          },
          {
            key: 'sourceFromName',
            title: '来源',
            width: 60,
            align: 'center',
            dataIndex: 'sourceFromName'
          },
        ]
      }
    },
    setup () {
      const { proxy } = getCurrentInstance()
      const { state } = useState()
      const { initPage, handleTableChange } = useFetch()
      const { options } = getEnums()

      const rowClass = (record, index) => {
        if (
          record.dueDate != null &&
          record.dueDate >= 0 &&
          record.dueDate <= 7
        ) {
          return 'yellow'
        } else if (record.dueDate < 0) {
          return 'red'
        } else {
          return ''
        }
      }
      onMounted(() => {
        const routeQuery = proxy.$route.query
        initPage(routeQuery)
      })
      return {
        ...toRefs(state),
        oaHost: computed(() => proxy.$tnt.oaHost),
        options,
        rowClass,
        handleTableChange,
      }
    },
    render () {
      const {
        list,
        columns,
        loading,
        rowClass,
        pagination,
        handleTableChange,
      } = this

      return (
        <ni-table
          dataSource={ list }
          columns={ columns.filter(d => d.show === undefined || d.show)}
          loading={ loading }
          rowClassName={ rowClass }
          rowKey={ (record, index) => index }
          pagination={ pagination }
          onChange={ handleTableChange }
        >
        </ni-table>
      )
    }
  }
</script>
<style lang="scss" scoped>
  .evaluate-tag {
    font-size: 15px;
  }
  :deep(.ant-table-thead > tr > th .ant-table-header-column) {
    font-weight: 600;
  }
  :deep(pre) {
    --antd-wave-shadow-color: #1890ff;
    font: 14px/1.5 "BlinkMacSystemFont", "-apple-system", "Microsoft YaHei UI", "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", "sans-serif";
    -webkit-text-size-adjust: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: 'tnum';
    text-align: left;
    border-collapse: separate;
    border-spacing: 0;
    margin: 0;
    word-break: break-word;
    box-sizing: border-box;
    transition: background 0.3s;
    overflow-wrap: break-word;
    white-space: pre-wrap;
    word-wrap: break-word;
  }
</style>
