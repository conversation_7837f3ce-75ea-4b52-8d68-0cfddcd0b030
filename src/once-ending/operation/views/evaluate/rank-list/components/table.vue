<script type='text/jsx' lang="jsx">
  import { getCurrentInstance } from 'vue'
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions.jsx'
  import useExportXlsx from '@operation/views/statistics-report/hooks/useExportXlsx'

  import { NiTable } from '@jiuji/nine-ui'
  import { getExportItems } from '@operation/util/common'

  export default {
    name: 'index-table',
    components: {
      NiTable
    },
    setup () {
      const { proxy } = getCurrentInstance()
      const { state } = useState()
      const { showAllText, handleTableChange } = useActions()
      const { exportDataLoading, exportData } = useExportXlsx({ proxy })

      const handleExport = () => {
        const { currentTab, isJiuJi } = state
        const exportItems = getExportItems(proxy.$route.path, 'action', state.cacheColumns)
        if (!exportItems?.length) {
          return false
        }
        const params = {
          fileName: isJiuJi ? '服务&运营管控分排行' : '绩效排行',
          scene: 'evaluate_rank',
          exportItems,
          ...state.queryParams && state.queryParams
        }
        const exportApiType = isJiuJi ? (currentTab === 1 ? 'exportServiceRanks' : 'exportOperateControlRanks') : ''
        exportData({ params, exportApiType })
      }

      return {
        state,
        exportDataLoading,
        showAllText,
        handleTableChange,
        handleExport
      }
    },

    render () {
      const { state } = this
      const {
        tableData,
        loading,
        exportDataLoading,
        tableColumns
      } = state

      return (
        <NiTable
          rowKey={ (record, index) => index }
          dataSource={tableData}
          columns={tableColumns}
          loading={loading}
        >
          <div slot="tool" class='table-tool'>
            <a-button onClick={this.handleExport} loading={exportDataLoading}>
              导出
            </a-button>
          </div>
        </NiTable>
      )
    }
  }
</script>
<style scoped>
  .table-tool{
    text-align: right;
  }
</style>
