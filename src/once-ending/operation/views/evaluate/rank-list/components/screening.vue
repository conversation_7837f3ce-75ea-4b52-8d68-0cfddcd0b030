<template>
  <ni-filter
    :settingAble="false"
    :unfoldCount='4'
    class="relative"
    :form="state.screening"
    @filter="fetchData"
    :label-width="85"
    :immediate="false"
    >
      <ni-filter-item label='年月'>
        <a-month-picker
          v-model="state.screening.month"
          valueFormat="YYYYMM"
          placeholder="请选择年月"/>
      </ni-filter-item><ni-filter-item label='区域类别'>
        <a-select
          v-model="state.screening.kind"
          :options="areaTypeOpt"
          placeholder="区域类别"
        />
      </ni-filter-item><ni-filter-item label='地区'>
        <NiAreaSelect
          multiple
          allowClear
          style="width: 330px"
          :maxTagCount="1"
          class="area-depart-selector"
          treeNodeFilterProp='label'
          placeholder="请选择地区"
          showType="SHOW_CHILD"
          v-model="state.screening.areaIds"
        />
      </ni-filter-item><ni-filter-item label='分组'>
        <a-select
          v-model="state.screening.areaLevelType"
          :maxTagCount='1'
          :options="state.groupOpt"
          placeholder="请选择分组"
          allowClear/>
      </ni-filter-item>
  </ni-filter>
</template>

<script type='text/jsx'>
  import { onMounted, watch, computed, nextTick } from 'vue'
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions.jsx'
  import { options } from '../model/constants.js'

  import { NiFilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'

  export default {
    name: 'index-screening',
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect,
    },
    setup () {
      const { state } = useState()
      const { watchKind, fetchData, pageInit } = useActions()

      watch(
        () => state.screening.kind,
        (val) => {
          if (val || val === 0) {
            watchKind()
          }
        }
      )
      onMounted(() => {
        pageInit()
      })

      const areaTypeOpt = computed(() => {
        const { currentTab } = state
        const excludeValues = window.tenant.xtenant < 1000 ? currentTab === 1 ? [5, 6] : [0] : []
        return options.areaTypeOpt.filter(it => !excludeValues.includes(it.value))
      })

      function setKind () {
        const { kind } = state.screening
        if (!areaTypeOpt.value.find(it => it.value === kind)) {
          state.screening.kind = areaTypeOpt.value[0].value
        }
        nextTick(() => {
          fetchData()
        })
      }

      return {
        state,
        fetchData,
        options,
        areaTypeOpt,
        setKind
      }
    }
  }
</script>

<style lang='scss' scoped>
:deep(.ant-select-selection__choice){
  max-width: 70%;
}
</style>
