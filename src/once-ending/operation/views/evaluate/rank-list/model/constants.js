export const options = {
  areaTypeOpt: [
    {
      value: 1,
      label: '店面'
    },
    {
      value: 2,
      label: '小区'
    },
    {
      value: 3,
      label: '大区'
    },
    {
      value: 5,
      label: '小区售后'
    },
    {
      value: 6,
      label: '大区售后'
    },
  ],
  groupOpt: [
    {
      value: 1,
      label: '接收人'
    },
    {
      value: 2,
      label: '发送人'
    },
  ]
}
window.tenant.xtenant < 1000 && (options.areaTypeOpt.push({
  value: 0,
  label: '人员'
}))
export const columns = [
  {
    dataIndex: 'bigDepartName',
    key: 'bigDepartName',
    title: '大区',
    width: '3em',
  },
  {
    dataIndex: 'smallDepartName',
    key: 'smallDepartName',
    title: '小区',
    width: '3em',
  },
  {
    dataIndex: 'area',
    key: 'area',
    title: '地区',
    width: '4em',
  },
  {
    dataIndex: 'ch999Id',
    key: 'ch999Id',
    title: '工号',
    width: '3em',
  },
  {
    dataIndex: 'cha999Name',
    key: 'cha999Name',
    title: '姓名',
    width: '3em',
  },
  {
    dataIndex: 'mobileXishu',
    key: 'mobileXishu',
    title: '手机台量系数',
    width: '5em',
  },
  {
    dataIndex: 'areaXishu',
    key: 'areaXishu',
    title: '门店系数',
    width: '4em',
  },
  {
    dataIndex: 'coefficient',
    key: 'coefficient',
    title: '系数',
    width: '3em'
  },
  {
    dataIndex: 'totalScores',
    key: 'totalScores',
    title: '绩效分数',
    width: '4em'
  },
  {
    dataIndex: 'comprehensiveRank',
    key: 'comprehensiveRank',
    title: '综合排名',
    width: '4em'
  },
  {
    dataIndex: 'customerComplaint',
    key: 'customerComplaint',
    title: '客评投诉',
    width: '4em',
  },
  {
    dataIndex: 'customerComplaintRank',
    key: 'customerComplaintRank',
    title: '客评投诉排名',
    width: '6em',
  },
  {
    dataIndex: 'operatingControl1',
    key: 'operatingControl1',
    title: '运营管控(远程巡店)',
    width: '7em'
  },
  {
    dataIndex: 'operatingControlRank1',
    key: 'operatingControlRank1',
    title: '运营管控(远程巡店)排名',
    width: '9em'
  },
  {
    dataIndex: 'operatingControl2',
    key: 'operatingControl2',
    title: '运营管控(触发扣分)',
    width: '7em'
  },
  {
    dataIndex: 'operatingControlRank2',
    key: 'operatingControlRank2',
    title: '运营管控(触发扣分)排名',
    width: '9em'
  },
  {
    dataIndex: 'extraPoint',
    key: 'extraPoint',
    title: '额外加减分',
    width: '5em'
  },
  {
    dataIndex: 'extraPointRank',
    key: 'extraPointRank',
    title: '额外加减分排名',
    width: '7em'
  },
]

export const tabsOptions = [
  { key: 1, tab: '服务分排行' },
  { key: 2, tab: '运营管控分排行' }
]
