import { reactive, inject, provide } from 'vue'
import { cloneDeep } from 'lodash'
import moment from 'moment'

const key = Symbol('workOrder')
const month = moment().format('YYYYMM')

export function useState () {
  return inject(key)
}

export function createState () {
  const state = reactive({
    tableData: [],
    groupOpt: [],
    screening: {
      month: month,
      kind: 1,
      areaIds: undefined,
      areaLevelType: undefined,
    },
    tableColumns: [],
    screeningReset: undefined,
    loading: false,
    exportDataLoading: undefined,
    queryParams: undefined,
    cacheColumns: [],
    isJiuJi: window.tenant.xtenant < 1000,
    currentTab: 1,
    jiuJiColumns: []
  })

  const setObj = objKey => (val, key) => {
    if (key && typeof key === 'string') {
      state[objKey][key] = val
    } else {
      state[objKey] = val
    }
  }
  const setScreening = setObj('screening')
  const resetScreening = () => { setScreening(cloneDeep(state.screeningReset)) }
  const initScreeningReset = () => { state.screeningReset = cloneDeep(state.screening) }

  const setLoading = val => { state.loading = val }
  const setQueryParams = val => { state.queryParams = val }
  const setTableData = val => { state.tableData = val || [] }
  const setTableColumns = val => { state.tableColumns = val || [] }
  const setGroupOpt = val => { state.groupOpt = val || [] }

  const workOrder = {
    state,
    setLoading,

    setScreening,
    resetScreening,
    initScreeningReset,

    setQueryParams,
    setTableData,
    setTableColumns,
    setGroupOpt
  }
  provide(key, workOrder)
  return workOrder
}
