
import { getCurrentInstance, h } from 'vue'
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'
import {
  GET_EVALUATE_RANKS,
  GET_EVALUATE_RANKS_JIUJI,
  GET_GROUP_KINDS
} from '@operation/store/modules/evaluate/action-types'
import { useState } from './useState'
import { columns } from './constants.js'

export default function useActions () {
  const instance = getCurrentInstance().proxy
  const {
    state,
    setScreening,
    setQueryParams,
    initScreeningReset,

    setTableData,
    setTableColumns,
    setGroupOpt
  } = useState()

  const fetchGroupKinds = async () => {
    const { kind } = state.screening
    const res = await instance.$store.dispatch(`operation/evaluate/${GET_GROUP_KINDS}`, { kind })
    if (!res) return
    const opt = res.data.map(it => ({
      value: it.id,
      label: it.value
    }))
    setGroupOpt(opt)
  }
  function dealColumns (array) {
    const customRenderMap = new Map([
      [
        'text',
        text => (<i>{ text || '--'}</i>)
      ],
    ])
    return array.map(item => {
      const cacheItem = { ...item, title: item.name, dataIndex: item.key, align: 'center' }
      item.sort && (cacheItem.sorter = (a, b) => (a[item.dataIndex] - b[item.dataIndex]))
      cacheItem.customRender = customRenderMap.get(item.dataIndex) || customRenderMap.get('text')
      return cacheItem
    })
  }
  const fetchData = async () => {
    const { isJiuJi } = state
    const isPass = _checkForm()
    if (!isPass) return
    const params = _getParams()
    instance.$indicator.open()
    setQueryParams(params)
    let res
    if (isJiuJi) {
      res = await instance.$store.dispatch(`operation/evaluate/${GET_EVALUATE_RANKS_JIUJI}`, params)
    } else {
      // kind不同，表头不同。计算表头
      computColumns()
      res = await instance.$store.dispatch(`operation/evaluate/${GET_EVALUATE_RANKS}`, params)
    }
    instance.$indicator.close()
    if (!res) return
    const { code, data } = res
    if (code === 0) {
      if (isJiuJi) {
        const tableColumns = dealColumns(data.head || [])
        setTableColumns(tableColumns)
        setTableData(data.data || [])
        state.cacheColumns = cloneDeep(tableColumns)
      } else {
        setTableData(data)
      }
    }
  }
  // 计算表头start
  const computColumns = () => {
    const { kind } = state.queryParams
    // 需要customRender的字段
    const renderArry = [
      'bigDepartName',
      'smallDepartName',
      'area',
      'ch999Id',
      'cha999Name'
    ]
    // 需要sort排序的字段
    const sortArry = [
      'comprehensiveRank',
      'customerComplaintRank',
      'operatingControlRank1',
      'operatingControlRank2',
      'extraPointRank'
    ]
    const col = columns.map(it => ({
      ...it,
      ...sortArry.includes(it.dataIndex) && {
        sorter: (a, b) => (a[it.dataIndex] - b[it.dataIndex]),
      },
      ...renderArry.includes(it.dataIndex) && {
        customRender: text => (<i>{ text || '--'}</i>)
      },
    }))
    const colObj = {
      1: col.filter(it => it.dataIndex !== 'areaXishu'), // 店面头
      2: col.filter(it => it.dataIndex !== 'area'), // 小区头
      3: col.filter(it => !['smallDepartName', 'area'].includes(it.dataIndex)), // 大区头
    }
    const tableColumns = [5, 6].includes(kind) ? col : colObj[kind] // 小区售后、大区售后
    setTableColumns(tableColumns)
    state.cacheColumns = cloneDeep(tableColumns)
  }
  // 筛选参数过滤空选项
  const _getParams = function () {
    const { currentTab } = state
    const params = { ...state.screening, currentTab }
    for (let key in params) {
      if (
        (!params[key] && params[key] !== 0) ||
        (params[key] && Array.isArray(params[key]) && !params[key].length)
      ) {
        delete params[key]
      }
    }
    return params
  }
  // 筛选参数检查
  const _checkForm = function () {
    const params = _getParams()
    const {
      month,
    } = params
    if (!month) {
      message.warning('请选择时间')
      return false
    }
    return true
  }
  const watchKind = async () => {
    await fetchGroupKinds()
    // 把所请求的数据的第一项设置为分组参数
    const areaLevelType = state.groupOpt?.[0]?.value
    setScreening(areaLevelType, 'areaLevelType')
  }
  const pageInit = async () => {
    // init查询条件、工单表单。（重置与新建的时候使用）
    initScreeningReset()
    await watchKind() // 根据区域类别选项设置分组的默认参数，再进行数据查询
    fetchData()
  }

  return {
    watchKind,
    pageInit,
    fetchData,
    fetchGroupKinds
  }
}
