<script type='text/jsx' lang="jsx">
  import { getCurrentInstance, toRefs } from 'vue'
  import { createState } from './model/useState.js'
  import { NiListPage } from '@jiuji/nine-ui'
  import Screening from './components/screening'
  import Table from './components/table'
  import { tabsOptions } from './model/constants.js'

  export default {
    name: 'PageWrap',
    components: {
      Screening,
      Table,
      NiListPage
    },
    setup (_) {
      const { proxy } = getCurrentInstance()
      const { state } = createState()
      const toConfig = () => {
        proxy.$router.push({ path: `/rank-config` })
      }
      function changeTab () {
        proxy.$refs.screeningRef.setKind()
      }
      return {
        ...toRefs(state),
        toConfig,
        changeTab
      }
    },
    render () {
      const userRank = this.$store.state.userInfo.Rank
      const canConfig = userRank.includes('gkph')
      const { isJiuJi, changeTab } = this
      return (
        <page>
          { isJiuJi ? <a-tabs onChange={() => changeTab()} v-model={this.currentTab}>
            { tabsOptions.map(item => <a-tab-pane key={item.key} tab={item.tab} />) }
          </a-tabs> : null }
          {
            canConfig && <a-button v-if='canConfig' slot="extra" type="primary" onClick={ this.toConfig }>规则配置</a-button>
          }
          <NiListPage push-filter-to-location={ false }>
            <Screening ref="screeningRef" class='mb-8'/>
            <Table/>
          </NiListPage>
        </page>
      )
    },
  }
</script>
