<script lang="jsx">
  import { defineComponent } from 'vue'
  import useDetail from '../model/useDetail.js'
  import imgViewer from '~/components/img-viewer'

  export default defineComponent({
    components: {
      imgViewer
    },
    setup () {
      const { state } = useDetail()
      return {
        state
      }
    },
    render () {
      const { standbyProduct } = this.state?.info
      if (!standbyProduct) return
      return (
      <a-card class="mb-20">
        <div class="title-evaluate">备用机信息</div>
        <div class="flex spare-phone">
          <div class="img-box mt-8">
            <img-viewer>
              <img alt="" class="mr-16 img" src={standbyProduct.pic} />
            </img-viewer>
          </div>
          <div class="content-box mt-8">
            <div class="name">{standbyProduct.productName}</div>
            <div class="star-box flex">
              <div class="one">本次评价分：{standbyProduct.sore || 0}星</div>
              <div class="week">当月评价分：{standbyProduct.thisMonthScore || 0}星</div>
            </div>
          </div>
        </div>
        {
            standbyProduct.content
            ? <div class="evaluate pl-20" style="margin-top: 16px;">
              意见/建议：{ standbyProduct.content }
            </div> : null
          }
      </a-card>
      )
    }
  })
</script>
<style scoped lang="scss">
@import '../style.scss';
.spare-phone {
  padding: 0 8px;
  .img{
    width: 95px;
    height: 55px
  }
  .content-box{
        display: flex;
    flex-direction: column;
    .name{
      padding-bottom: 13px;
    }
    .star-box{
      .week{
      margin-left: 20px;
    }
    }

  }
}
</style>
