<script lang="jsx">
  import { onMounted, toRefs, inject } from 'vue'
  import moment from 'moment'
  import StaffInput from '~/components/staff/staff-input'
  import useDetail from '../model/useDetail.js'
  import useProposal from '../model/useProposal.js'

  export default {
    components: {
      StaffInput
    },
    setup (props) {
      const { state, updateEvaluateTag } = useDetail()
      const {
        stateProposal,
        setYhForm,
        initFormReset,
        initYhFormReset,

        addEvaluateProcess,
        updateEvaluateState,
        sendMa,
        changeNoticeUser
      } = useProposal()

      onMounted(() => {
        initFormReset()
        const endTime = moment().add(6, 'month').format('YYYY-MM-DD')
        setYhForm(endTime, 'endTime')
        initYhFormReset()
      })

      return {
        state,
        ...toRefs(stateProposal),
        changeNoticeUser,
        addEvaluateProcess,
        updateEvaluateState,
        updateEvaluateTag,
        sendMa
      }
    },
    render () {
      const {
        state: { info },
        form,
        yhForm,

        addEvaluateProcess,
        updateEvaluateState,
        updateEvaluateTag,
        sendMa
      } = this
      const {
        processLogs = [],
        evaluateStatus
      } = info
      const isEvaluateManage = inject('isEvaluateManage')
      const isRank6c5 = inject('isRank6c5')

      return (
        <a-card class="mb-24">
          <div class="title-evaluate">处理进程</div>
          <div class="container">
            <a-timeline>
              {
                processLogs.map(item => (
                  <a-timeline-item>
                    <div>
                      { item.content }
                      <span class="margin-left">{ item.logTime }</span>
                      <span class="margin-left">【{ item.inuser }】</span>
                    </div>
                  </a-timeline-item>
                ))
              }
            </a-timeline>

            <div class="flex flex-align-center mb-24">
              添加处理进程：
              <a-textarea
                style="width:500px;"
                class='mr-16'
                v-model= { form.process }/>
              <a-button type='primary' onClick={ addEvaluateProcess }>
                添加
              </a-button>
            </div>

            <div class="flex flex-align-center ">
              <div class="flex flex-align-center mr-24">
                <a-checkbox v-model={ form.isNotice }>通知</a-checkbox>
                { form.isNotice &&
                  <div class="flex flex-align-center">
                    <i class="label">接收人：</i>
                    <staff-input
                      style="width: 150px;"
                      class="mr-16"
                      multiple={true}
                      v-model={ form.noticeUserIds }
                      />
                    <a-checkbox v-model={ form.tongZhi }>
                      内部聊天
                    </a-checkbox>
                  </div>
                }
              </div>
              <div class="mr-24">
                状态更改：
                {
                  ([0, 3].includes(evaluateStatus) || (evaluateStatus === 4 && isEvaluateManage)) &&
                  <a-button type='link'
                    onClick={ () => { updateEvaluateState(2) }}>
                    【转未处理】
                  </a-button>
                }
                {
                  evaluateStatus === 4 && isEvaluateManage &&
                  <a-button type='link'
                    onClick={ () => { updateEvaluateState(1) }}>
                    【转未通过】
                  </a-button>
                }
                { evaluateStatus !== 5 && (isEvaluateManage || isRank6c5) &&
                  <a-button type='link'
                    onClick={ () => { updateEvaluateState(5) }}>
                    【标记完成】
                  </a-button>
                }
              </div>
              {
                isEvaluateManage &&
                <span>类别：
                  <a-button type='link'
                    onClick={ () => { updateEvaluateTag({ evaluateTag: 0 }) }}>
                    【恢复】
                  </a-button>
                </span>
              }
            </div>
            {
              this.$tnt.xtenant === 0 && isEvaluateManage &&
              <div class="mt-20 flex flex-align-center">
                <span>代金券金额：</span>
                <a-input-number min={ 0 } v-model={ yhForm.price } style="width: 100px" />

                <span class="ml-16">截止日期：</span>
                <a-date-picker
                  v-model={ yhForm.endTime }
                  valueFormat="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  placeholder="请选择日期"/>

                <span class="ml-16">通知方式：</span>
                <a-checkbox v-model={ yhForm.isSms } class="mr-20">短信</a-checkbox>
                <a-checkbox v-model={ yhForm.isApp } class="mr-20">APP消息</a-checkbox>

                <a-button type='primary' onClick={ sendMa }>
                  发送代金券
                </a-button>
              </div>
            }
          </div>
        </a-card>
      )
    }
  }
</script>

<style lang="scss" scoped>
@import '../style.scss';
.mr-24{
  margin-right: 24px;
}
.mb-24{
  margin-bottom: 24px;
}
</style>
