
import { getCurrentInstance, h } from 'vue'
import { cloneDeep, isNil, castArray } from 'lodash'
import {
  GET_EVALUATE_TAG,
  GET_EVALUATE_SCORE_LIST,
} from '@operation/store/modules/evaluate/action-types'
import dataUtils from '../../common/data-utils'
import { fetchBrands } from '../../common/useEnums'
import { useState } from './useState.js'
import useReportHearder from '@operation/views/statistics-report/hooks/useReportHearder'

export default function useActions () {
  const instance = getCurrentInstance().proxy
  const {
    state = {},
    resetQuery,
    setQueryParams,
    initDefaultTime,
    setBrands,
    setSelectOptions,
    setChartData,
    setModalVisible,
    setLoading,
    setOnlyChoose,
    setExpandedRowKeys,
    setList,
  } = useState()
  // 品牌
  const getBrands = async (values) => {
    const brands = await fetchBrands(values)
    if (!brands) return
    setBrands(brands)
    _setDefaultRolesOptions()
  }
  // 因为角色下拉框数据不是同时绑定到下拉组件上,导致如果有值从url上传过来取到值以后绑定在组件上无法显示出名称,而是显示id,故初始的时候将options的值绑定上,然后再置空,让名称能够正常显示
  const _setDefaultRolesOptions = () => {
    const options = cloneDeep(state.brands)
    const selectOptions = options.filter(d => state.query.brandIds.includes(d.value))
    setSelectOptions(selectOptions)
    instance.$nextTick(() => {
      setSelectOptions([])
    })
  }
  const fetchData = async () => {
    const params = getParams()
    setOnlyChoose(params.onlyChoose)
    dataUtils.deleteEmpty(params)
    setQueryParams(params)

    setLoading(true)
    const res = await instance.$store.dispatch(
      `operation/evaluate/${GET_EVALUATE_SCORE_LIST}`,
      params
    )
    setLoading(false)

    if (!res) return
    const { code, data } = res
    if (code !== 0) return
    if (!data.info.length) {
      setList([])
      return
    }
    const list = _delBigDeparts(data.info)
    let newList = []
    list.forEach(item => {
      newList.push({ ...item })
      if (item.areas?.length) {
        newList.push(...item.areas)
      }
      if (item.smallDeparts?.length) {
        const smallDeparts = item.smallDeparts.map(it => ({
          ...it,
          smallArea: true
        }))
        newList.push(...smallDeparts)
      }
    })
    setExpandedRowKeys([])
    const listMap = newList.map((item, i) => {
      state.expandedRowKeys.push(`${i}`)
      // 大区-->小区-->门店
      if (!item.bigArea && item.smallArea) {
        return {
          ...item,
          index: `${i}`,
          children: item.areas.map((it, k) => {
            return {
              ...it,
              index: `${i}_${k}`,
              children: it.employees.map((d, j) => {
                return {
                  ...d,
                  index: `${i}_${k}_${j}`
                }
              })
            }
          })
        }
      } else if (!item.smallArea && item.employees?.length) {
        // 大区-->门店
        return {
          ...item,
          index: `${i}`,
          children: item.employees.map((d, j) => {
            return {
              ...d,
              index: `${i}_${j}`
            }
          })
        }
      } else {
        return { ...item, index: `${i}` }
      }
    })
    setList(listMap)
    const listSum = data.sum
      ? [{ ...data.sum, employeeName: '总合计', index: 'total' }]
      : []
    const allList = state.list.concat(listSum)
    setList(allList)
  }
  // 评价得分标签分析 getEvaluateTag
  const tagAnalysis = async () => {
    const params = getParams()
    dataUtils.deleteEmpty(params)
    const res = await instance.$store.dispatch(
      `operation/evaluate/${GET_EVALUATE_TAG}`,
      params
    )
    if (!res) return
    const { code, data } = res
    if (code !== 0) return
    setChartData(data, 'rows')
    setModalVisible(true)
  }
  const toDetail = (evaluateIdsKey) => {
    // const query = { evaluateIds: evaluateIds.join(',') }
    const query = { evaluateIdsKey }
    const { href } = instance.$router.resolve({
      path: `/evaluate/score-statistics-detail`,
      query
    })
    window.open(href, '_blank')
  }
  const rowClassName = (record) => {
    // 产品说选了门店,大区,小区,表格背景用employee
    if (state.onlyChoose || record.employeeId) {
      return 'employee'
    } else if (record.bigArea) {
      return 'bigArea'
    } else if (record.bigDepartsLeve === 1) {
      return 'bigAreaLeve1'
    } else if (record.bigDepartsLeve === 2) {
      return 'bigAreaLeve2'
    } else if (record.bigDepartsLeve === 3) {
      return 'bigAreaLeve3'
    } else if (record.smallArea) {
      return 'smallArea'
    } else if (record.employees) {
      return 'store'
    } else {
      return ''
    }
  }
  const getParams = () => {
    const params = cloneDeep(state.query)
    if (instance.$tnt.xtenant >= 1000) {
      delete params.evaluateTypeList
    } else {
      delete params.evaluateType
    }
    if (params.time.length) {
      params.startTime = params.time[0]
      params.endTime = params.time[1]
      delete params.time
    }
    if (params.invalid) params.invalid = 1
    if (!isNil(params.storeLevel) && !Array.isArray(params.storeLevel)) {
      params.storeLevel = castArray(params.storeLevel)
    }
    if (instance.$tnt.xtenant >= 1000) {
      delete params.timeType
    }
    return params
  }
  const _delBigDeparts = (list) => {
    const listTemp = []
    let newList = []
    list.forEach(item => {
      if (item.bigDeparts?.length) {
        listTemp.push({ ...item, bigArea: true })

        item.bigDeparts.forEach(it => {
          listTemp.push({
            ...it,
            bigDepartsLeve: 1,
            bigChild: it.bigDeparts
          })
        })
      } else {
        listTemp.push({ ...item, bigArea: true })
      }
    })
    listTemp.forEach(item => {
      newList.push({ ...item })
      if (item.bigDepartsLeve && item.bigChild?.length) {
        item.bigChild.forEach(it => {
          newList.push({ ...it, bigDepartsLeve: 2 })
          if (it.bigDeparts?.length) {
            it.bigDeparts.forEach(d => {
              newList.push({ ...d, bigDepartsLeve: 3 })
            })
          }
        })
      }
    })
    return newList
  }
  const pageInit = () => {
    featchHeader()
    resetQuery()
    initDefaultTime()
    // fetchData()
  }
  function featchHeader () {
    Promise.all([getHearderUser({ type: 624, innerType: 1 }), getHearderArea({ type: 624, innerType: 2 }), getHearderEvaluate({ type: 624, innerType: 3 })]).then(res => {
      state.hearderDataUser = hearderDataUser
      state.hearderDataArea = hearderDataArea
      state.hearderDataEvaluate = hearderDataEvaluate
    })
  }
  // hearderDataUser: 员工表头, getHearderSale: 获取员工表头
  const { hearderData: hearderDataUser, getHearder: getHearderUser } = useReportHearder({ proxy: instance, h, type: 624 })
  // hearderDataArea: 门店表头, getHearderArea: 获取门店表头
  const { hearderData: hearderDataArea, getHearder: getHearderArea } = useReportHearder({ proxy: instance, h, type: 624 })
  // hearderDataEvaluate: 客评表头, getHearderEvaluate: 获取客评表头
  const { hearderData: hearderDataEvaluate, getHearder: getHearderEvaluate } = useReportHearder({ proxy: instance, h, type: 624 })

  // 获取枚举
  return {
    getBrands,
    fetchData,
    tagAnalysis,
    getParams,
    toDetail,
    rowClassName,
    pageInit
  }
}
