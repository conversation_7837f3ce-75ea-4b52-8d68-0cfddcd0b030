import { reactive, inject } from 'vue'
import { cloneDeep } from 'lodash'
import * as constants from '../../common/constants'
import { initTimeRange } from '../../common/utils'

const key = Symbol('scoreStatistics')

export function useState () {
  return inject(key)
}

export function createState (provide) {
  const state = reactive({
    modalVisible: false, // 标签分析modal
    brands: [],
    selectOptions: [],
    cacheOptions: [],
    selectSearchValue: '',
    // v-chats列表数据
    chartData: {
      columns: ['info', 'cnt'],
      rows: []
    },
    // oaHost: this.$tnt.oaHost,
    query: undefined, // 查询参数
    queryParams: undefined, // 查询时处的最终参数
    loading: false,
    spinning: false, // 导出数据ling
    onlyChoose: null,
    expandedRowKeys: [],
    list: [],
    childList: [],
    cacheColumns: [],
    hearderDataUser: [], // 员工表头
    hearderDataArea: [], // 门店表头
    hearderDataEvaluate: [] // 客评表头
  })
  const setObj = objKey => (val, key) => {
    if (!key) {
      state[objKey] = val
    } else {
      state[objKey][key] = val
    }
  }
  const setQuery = setObj('query') // 筛选参数相关
  const originQuery = cloneDeep(constants.originQuery)
  originQuery.dimension = window.tenant.xtenant < 1000 ? 0 : 1
  originQuery.timeType = window.tenant.xtenant < 1000 ? 2 : 1
  const resetQuery = () => { setQuery(originQuery) }
  const setQueryParams = val => { state.queryParams = val }

  const setChartData = setObj('chartData') // 筛选区枚举值
  const setModalVisible = val => { state.modalVisible = val }
  const setBrands = val => { state.brands = val || [] }
  const setSelectOptions = val => { state.selectOptions = val || [] }
  const setCacheOptions = val => { state.cacheOptions = val || [] }
  const setSelectSearchValue = val => { state.selectSearchValue = val || [] }

  const setLoading = val => { state.loading = val }
  const setSpinning = val => { state.spinning = val }
  const setOnlyChoose = val => { state.onlyChoose = val }
  const setExpandedRowKeys = val => { state.expandedRowKeys = val || [] }
  const setList = val => { state.list = val || [] }
  const setChildList = val => { state.childList = val || [] }
  const initDefaultTime = () => {
    const times = initTimeRange(1, 'd')
    setQuery(times, 'time')
  }

  const scoreStatistics = {
    state,
    setQuery,
    resetQuery,
    setQueryParams,

    setChartData,
    setModalVisible,
    setBrands,
    setSelectOptions,
    setCacheOptions,
    setSelectSearchValue,

    setLoading,
    setSpinning,
    setOnlyChoose,
    setExpandedRowKeys,
    setList,
    setChildList,
    initDefaultTime
  }
  provide(key, scoreStatistics)
  return scoreStatistics
}
