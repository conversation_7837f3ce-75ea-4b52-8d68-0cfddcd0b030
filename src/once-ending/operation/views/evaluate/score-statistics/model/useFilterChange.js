import { cloneDeep } from 'lodash'
import useActions from './useActions.js'
import { useState } from './useState.js'
const xtenant = window.tenant.xtenant

export default function useFilterChange () {
  const {
    state,
    setQuery,
    resetQuery,
    setSelectOptions,
    setCacheOptions,
    setSelectSearchValue,
    initDefaultTime
  } = useState()
  const {
    fetchData,
    getBrands
  } = useActions()

  // 商品分类选择
  const changeCategory = (val1, val2, val3) => {
    if (val1[0]) {
      setQuery(val3, 'cids')
      getBrands(val3)
    } else {
      setQuery([], 'cids')
    }
    setQuery([], 'brandIds')
  }
  // 维度选择
  const changeDimension = (val) => {
    resetQuery() // 维度切换，需要重设参数
    setQuery(val, 'dimension')
    initDefaultTime()
    val === 0 && (state.query.timeType = 2)
    if (xtenant === 0 && state.query.onlyChoose === 4 && state.query.dimension === 0) {
      setQuery(null, 'onlyChoose')
    }
    // fetchData()
  }
  // 每次下拉框获取焦点,初始化下拉框数据
  const selectFocus = () => {
    if (state.selectOptions.length) return
    const options = cloneDeep(state.brands)
    const initOptions = options.splice(0, 50)
    setSelectOptions(initOptions)
    setCacheOptions(options)
  }
  // 每次下拉框失去焦点,置空下拉框数据
  const selectBlur = () => {
    setSelectOptions([])
    setCacheOptions([])
  }
  // 每次用户输入,匹配所有数据,将数据筛选出来
  const selectSearch = (val) => {
    setSelectSearchValue(val)
    const options = cloneDeep(state.brands)
    const selectOptions = options.filter(d => d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase()))
    const cacheOptions = options.filter(d => !d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase()))
    setSelectOptions(selectOptions)
    setCacheOptions(cacheOptions)
  }
  // 每次用户选择以后判断是否为筛选以后查询,如果是,重置下拉数据,
  const handleSelect = () => {
    if (state.selectSearchValue) {
      const options = cloneDeep(state.brands)
      const initOptions = options.splice(0, 50)
      setSelectOptions(initOptions)
      setCacheOptions(options)
      setSelectSearchValue('')
    }
  }
  // 每次下拉框滚动条滚到底部,加载缓存数据
  const selectPopupScroll = (e) => {
    if (!state.cacheOptions.length) return
    const { target } = e
    const scrollHeight = target.scrollHeight - target.scrollTop
    const clientHeight = target.clientHeight
    if (scrollHeight < clientHeight + 5) {
      const options = state.cacheOptions.splice(0, 50)
      const selectOptions = state.selectOptions.concat(options)
      setSelectOptions(selectOptions)
    }
  }
  // 大区、小区、门店单选
  const changeOnlyChoose = (value) => {
    let onlyChoose = value === state.query.onlyChoose ? null : value
    setQuery(onlyChoose, 'onlyChoose')
  }
  return {
    changeCategory,
    changeDimension,
    selectFocus,
    selectBlur,
    selectSearch,
    handleSelect,
    selectPopupScroll,
    changeOnlyChoose,
  }
}
