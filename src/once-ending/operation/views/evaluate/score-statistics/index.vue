<script lang="jsx">
  import { provide, getCurrentInstance } from 'vue'
  import { createState } from './model/useState.js'

  import { NiListPage } from '@jiuji/nine-ui'
  import FilterBox from './components/filter-box'
  import TableBox from './components/table-box'
  import { Button } from 'ant-design-vue'

  export default {
    name: 'score-statistics',
    components: {
      NiListPage,
      FilterBox,
      TableBox
    },
    setup () {
      const { proxy } = getCurrentInstance()
      createState(provide)

      const to = function () {
        proxy.$router.push('/operation/evaluate/service-score-statistics')
      }

      return {
        to
      }
    },
    render () {
      const { to } = this
      return (
        <page class="score-statistics">
        {
          this.$tnt.xtenant === 0 ? <div slot="extra">
          <Button type="primary" onClick={to}>服务分统计</Button>
        </div> : null
        }
          <ni-list-page pushFilterToLocation={ false }>
            <FilterBox class="mb-16"/>
            <TableBox/>
          </ni-list-page>
        </page>
      )
    }
  }
</script>
