<script lang="jsx">
  import { useState } from '../model/useState.js'
  import VeHistogram from 'v-charts/lib/histogram.common'

  export default {
    name: 'model-chart',
    components: {
      [VeHistogram.name]: VeHistogram,
    },
    data () {
      return {
        vchartsConfig: {
          legend: {
            show: false
          },
          setting: {
            // 别称
            labelMap: {
              info: '评价标签',
              cnt: '评价数量'
            }
          },
          extend: {
            xAxis: {
              // name: "评价标签",
              type: 'category',
              show: true,
              // 坐标轴轴线
              axisLine: {
                show: true
              },
              // 坐标轴每项的文字
              axisLabel: {
                showMaxLabel: true,
                showMinLabel: true,
                color: '#3a3a3a',
                fontSize: 16,
                rotate: 270, // 刻度文字旋转，防止文字过多不显示
                margin: 8, // 文字离x轴的距离
                boundaryGap: true,
                formatter: v => {
                  if (v.length > 8) {
                    return v.substring(0, 8) + '...'
                  }
                  return v
                }
              },
              // X轴下面的刻度小竖线
              axisTick: {
                show: false,
                alignWithLabel: true, // axisLabel.boundaryGap=true时有效
                interval: 0,
                length: 4 // 长度
              },
              // x轴对应的竖线
              splitLine: {
                show: false,
                interval: 0,
                lineStyle: {
                  color: 'red',
                  backgroundColor: 'red'
                }
              }
            },
            yAxis: {},
            // 柱形区域
            grid: {
              left: 30,
              top: 30,
              bottom: 30,
              backgroundColor: '#FFF6F3',
              borderColor: '#FFF6F3'
            },
            // 每个柱子
            series (v) {
              if (!v) return
              v?.forEach(i => {
                i.barWidth = 30
                i.itemStyle = {
                  color: '#FF6633',
                  borderWidth: 0
                }
                i.label = {
                  color: '#666',
                  fontSize: '16px',
                  show: true,
                  position: 'top'
                }
              })
              return v
            }
          }
        }
      }
    },
    setup () {
      const { state, setModalVisible } = useState()

      return {
        state,
        setModalVisible
      }
    },
    render () {
      const { state, setModalVisible, vchartsConfig } = this
      const {
        query,
        chartData,
      } = state
      if (!chartData) return
      const modalTitle = query.dimension === 1 ? '人员评价标签排行(Top10)' : '门店评价标签排行(Top10)'
      return (
        <a-modal
          width="960px"
          v-model={ state.modalVisible }
          title={ modalTitle }
          footer={ null }
          onCancel={ () => { setModalVisible(false) }}
        >
          <div class="chart">
            <div class="chart-box">
              <ve-histogram
                class="myve"
                data={ chartData }
                legend={ vchartsConfig.legend }
                settings={ vchartsConfig.setting }
                extend={ vchartsConfig.extend }
              />
            </div>
            <div class="legend-box">
            {
              chartData.rows.map((item, index) => (
                <a-tooltip
                  key={index}
                  scopedSlots={{
                    title: () => <span>{ `${item.info}：${item.cnt}` }</span>
                  }}
                >
                  <div class="legend-item">
                    <div class="name">{ item.info }</div> ：
                    <span>{ item.cnt }</span>
                  </div>
                </a-tooltip>
              ))
            }
            </div>
          </div>
        </a-modal>
      )
    }
  }
</script>
<style lang="scss" scoped>

.chart {
  display: flex;
  .chart-box {
    width: 80%;
  }
  .legend-box {
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    padding: 25px 10px;
    .legend-item {
      display: flex;
      margin-bottom: 10px;
      font-size: 16px;
      .name {
        max-width: 70%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
}
</style>
