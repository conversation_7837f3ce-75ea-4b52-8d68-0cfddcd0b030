<script lang="jsx">
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions.js'
  import useExport from '../../common/useExport.js'
  import dataUtils from '../../common/data-utils'

  import { NiListPage, NiTable } from '@jiuji/nine-ui'
  import FilterBox from './filter-box'
  import ModelChart from './model-chart'
  import uuidv4 from 'uuid/v4'
  import { getCurrentInstance, ref } from 'vue'
  import { cloneDeep } from 'lodash'
  import { getExportItems } from '@operation/util/common'
  export default {
    name: 'table-box',
    components: {
      FilterBox,
      ModelChart,
      NiListPage,
      NiTable
    },
    setup () {
      const { proxy } = getCurrentInstance()
      const {
        state,
        setSpinning,
        setExpandedRowKeys
      } = useState()
      const {
        rowClassName,
        tagAnalysis,
        toDetail
      } = useActions()
      const { exportScoreStatistics } = useExport()

      const expandedRowsChange = (expandedRowKeys) => {
        setExpandedRowKeys(expandedRowKeys)
      }
      const exportExcel = () => {
        const exportItems = getExportItems(proxy.$route.path, 'action', state.cacheColumns)
        if (!exportItems?.length) {
          return false
        }
        state.queryParams && (state.queryParams.exportItems = exportItems)
        exportScoreStatistics(
          state.queryParams,
          setSpinning
        )
      }

      const toEvaluate = function (record, keyFlag) {
        const uuid = uuidv4()
        let key = ''
        // d.key === 'goodEvaluateCount' ? 'goodEvaluateIds' : d.key === 'complaintEvaluateAvgScore' ? 'complaintEvaluateIds' : 'negativeEvaluateIds')
        if (keyFlag === 'goodEvaluateCount') {
          key = 'goodEvaluateIds'
        } else if (keyFlag === 'complaintEvaluateAvgScore') {
          key = 'complaintEvaluateIds'
        } else if (keyFlag === 'fourScoreCount') {
          key = 'fourStarEvaluateIds'
        } else if (keyFlag === 'oneScoreCount') {
          key = 'oneStarEvaluateIds'
        } else if (keyFlag === 'twoScoreCount') {
          key = 'twoStarEvaluateIds'
        } else if (keyFlag === 'threeScoreCount') {
          key = 'threeStarEvaluateIds'
        } else if (keyFlag === 'beyondFiveCount') {
          key = 'beyondFiveEvaluateIds'
        } else {
          key = 'negativeEvaluateIds'
        }
        localStorage.setItem(uuid, JSON.stringify(record[key]))
        const query = { scoreStatisticsKey: uuid }
        const { href } = proxy.$router.resolve({
          path: `/evaluate`,
          query
        })
        window.open(href, '_blank')
      }

      const columnsCount = ref(0)
      const columnsSetting = function (keys) {
        columnsCount.value = keys.length - 1
      }

      return {
        state,
        expandedRowsChange,
        exportExcel,
        rowClassName,
        tagAnalysis,
        toDetail,
        toEvaluate,
        columnsCount,
        columnsSetting
      }
    },
    methods: {
      expandIcon (props) {
        if (!props.record?.children?.length) return null
        return (
          <a class="block" onClick={e => { props.onExpand(props.record, e) }}>
            <a-icon type={props.expanded ? 'caret-down' : 'caret-right'}/>
            { !props.expanded && '展开' }
          </a>
        )
      },
      columnsComputed (columns) {
        columns.map(d => {
          if (d.key === 'action') {
            const actionCustomRender = (text, record) => {
              return (
              <span>
                {record.evaluateIdsKey ? (
                  <a rel="noopener norefferrer" onClick={() => { this.toDetail(record.evaluateIdsKey) }} >
                    查看详情
                  </a>
                ) : null}
              </span>
              )
            }
            d.customRender = actionCustomRender
          } else if (d.key === 'avgEvaluateScore') {
            const avgEvaluateScoreCustomRender = text => <i>{text}</i>
            d.customRender = avgEvaluateScoreCustomRender
          } else if (this.$tnt.xtenant < 1000 && (d.key === 'goodEvaluateCount' || d.key === 'chaPing' || d.key === 'complaintEvaluateAvgScore' || d.key === 'fourScoreCount' || d.key === 'oneScoreCount' || d.key === 'twoScoreCount' || d.key === 'threeScoreCount' || d.key === 'beyondFiveCount')) {
            d.customRender = (text, record) => <a rel="noopener norefferrer" target="_blank" onClick={() => { this.toEvaluate(record, d.key) }}>
                    {text}
                  </a>
          }
          return d
        })
        return columns
      }
    },
    render () {
      const {
        state,
        expandIcon,
        columnsComputed,
        expandedRowsChange,
        exportExcel,
        rowClassName,
        tagAnalysis,
        columnsCount,
        columnsSetting
      } = this
      const { query, loading, spinning, list, expandedRowKeys } = state
      if (!query) return
      const columnsKeys = {
        0: 'hearderDataEvaluate',
        1: 'hearderDataUser',
        2: 'hearderDataArea',
      }
      const cacheC = state[columnsKeys[query.dimension]]
      let columns = columnsComputed(cacheC)
      // if (this.$tnt.xtenant >= 1000) {
      //   columns = columns.filter(item => item.dataIndex !== 'praise')
      //   columns.forEach(item => {
      //     if (item.dataIndex === 'goodEvaluateCount') {
      //       item.title = '好评量'
      //     }
      //     if (item.dataIndex === 'goodEvaluateRate') {
      //       item.title = '好评率'
      //     }
      //   })
      // }
      state.cacheColumns = cloneDeep(columns)

      return (
        <div>
          { loading }
          <ni-table
            pagination={ false }
            defaultExpandAllRows={ true }
            expandIcon={ expandIcon }
            onExpandedRowsChange={ expandedRowsChange }
            expandedRowKeys={ expandedRowKeys }
            footerTotalNum={ 1 }
            rowKey={ record => record.index }
            loading={ loading }
            rowClassName={ rowClassName }
            columns={ columns }
            dataSource={ list }
            expandIconColumnIndex={columnsCount}
            onColumnsSetting={columnsSetting}
          >
            <template slot="tool">
              <div style="float:right">
                <a-button
                  icon={ spinning ? 'loading' : 'download' }
                  class="ml-8"
                  onClick={ exportExcel }
                >
                  <span>{ spinning ? '导出中' : '导出' }</span>
                </a-button>
                <a-button
                  icon="share-alt"
                  class="ml-8"
                  onClick={ tagAnalysis }
                >
                  标签分析
                </a-button>
              </div>
            </template>
          </ni-table>
          <ModelChart/>
        </div>
      )
    }
  }
</script>
<style lang="scss" scoped>
// 大区层级颜色配置
$color-bigArea: #40a9ff; //大区
$color-leve1: #40a9ff;
$color-leve2: #69c0ff;
$color-leve3: #91d5ff;
$color-leve4: #bae7ff; //小区
$color-leve5: #e6f7ff; //门店
$leve-colors: (
  1: $color-leve1,
  2: $color-leve2,
  3: $color-leve3
);
@each $level, $color in $leve-colors {
  :deep(.bigAreaLeve#{$level}) {
    font-weight: 600;
    background: $color;
    td:first-child {
      padding-left: $level + 1 + em;
    }
  }
}
:deep(.bigArea) {
  background: $color-bigArea;
  color: #fff;
  font-weight: 600;
}
:deep(.smallArea) {
  background: $color-leve4;
  td:first-child {
    padding-left: 4em;
  }
}
:deep(.store) {
  background: $color-leve5;
  td:first-child {
    padding-left: 5em;
  }
}
:deep(.employee) {
  td:first-child {
    padding-left: 6em;
  }
}

:deep(.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td) {
  background: none;
}
:deep(.ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td) {
  background: none;
}

:deep(.select-group) {
  .ant-select {
    width: 110px;

    &:not(:first-child) {
      margin-left: -4px;

      .ant-select-selection {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
  }
}
.evaluate-tag {
  font-size: 15px;
}
table {
  border-collapse: collapse;
}
thead th {
  border: 1px solid black;
  text-align: center;
  background: #f0f0f0;
  padding: 0 8px;
}

table,
td {
  border: 1px solid black;
  text-align: center;
}
.relative {
  .ant-form-item {
    margin-right: 16px;
    margin-bottom: 6px;
  }
}
</style>
