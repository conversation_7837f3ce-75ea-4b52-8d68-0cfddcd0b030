<template>
  <div>
    <ni-filter
      class="relative"
      :form="query"
      :loading="loading"
      :label-width="70"
      :item-min-width="420"
      :saveAble="false"
      :immediate="false"
      @filter="fetchData"
    >
      <ni-filter-item label="选择维度">
        <a-select
          placeholder="门店维度"
          v-model="query.dimension"
          :options="dimensionOptions"
          @change="changeDimension"
          :getPopupContainer="triggerNode => triggerNode.parentNode"
        />
      </ni-filter-item>
      <ni-filter-item label="时间">
        <a-input-group compact>
          <a-select
            v-if="$tnt.xtenant < 1000"
            v-model="query.timeType"
          >
            <a-select-option :value="1">订单时间</a-select-option>
            <a-select-option :value="2">评价时间</a-select-option>
          </a-select>
          <a-range-picker
            :style="{ width: $tnt.xtenant < 1000 ? '200px' : '' }"
            value-format="YYYY-MM-DD"
            v-model="query.time"
            :getPopupContainer="triggerNode => triggerNode.parentNode"
          />
        </a-input-group>
      </ni-filter-item>
      <ni-filter-item label="地区">
        <ni-area-select
          allowClear
          multiple
          show-search
          :max-tag-count="1"
          v-model="query.areaIds"
        />
      </ni-filter-item>
      <ni-filter-item label="门店">
        <a-input-group compact>
          <a-select
            v-model="query.storeLevel"
            :options="dataUtils.storLevelOptions"
            placeholder="级别"
            allowClear
            :getPopupContainer="triggerNode => triggerNode.parentNode"
          />
          <a-select
            style="width: 200px"
            v-model="query.storeType"
            :options="dataUtils.storTypeOptions"
            placeholder="类别"
            :mode="$tnt.xtenant < 1000 ? 'multiple':'default'"
            :maxTagCount="1"
            allowClear
            :getPopupContainer="triggerNode => triggerNode.parentNode"
          />
        </a-input-group>
      </ni-filter-item>
      <ni-filter-item label="评价类型">
        <a-select
            v-if="$tnt.xtenant < 1000"
            v-model="query.evaluateTypeList"
            mode="multiple"
            :maxTagCount="1"
            :options="options.type"
            placeholder="评价类型"
            allowClear
            :getPopupContainer="triggerNode => triggerNode.parentNode"
          />
          <a-select
          v-else
            v-model="query.evaluateType"
            :options="options.type"
            placeholder="评价类型"
            allowClear
            :getPopupContainer="triggerNode => triggerNode.parentNode"
          />
      </ni-filter-item>
      <ni-filter-item label="评价类别">
       <a-select
            v-model="query.evaluateTagType"
            :options="options.tagType"
            placeholder="评价类别"
            allowClear
            :getPopupContainer="triggerNode => triggerNode.parentNode"
          />
      </ni-filter-item>
      <ni-filter-item label="评价状态">
       <a-select
          optionFilterProp="children"
            v-model="query.evaluateStatusList"
            placeholder="评价状态"
            mode="multiple"
            :maxTagCount="1"
            :options="options.status"
            allowClear
            :getPopupContainer="triggerNode => triggerNode.parentNode"
          />
      </ni-filter-item>
      <ni-filter-item label="评价来源">
       <a-select
          optionFilterProp="children"
            v-model="query.evaluateSourceFromList"
            :options="options.sourceFrom"
            :maxTagCount="1"
            mode="multiple"
            placeholder="评价来源"
            allowClear
            :getPopupContainer="triggerNode => triggerNode.parentNode"
          />
      </ni-filter-item>
      <ni-filter-item label="门店分" v-if="query.dimension === 2">
        <a-select
          v-model="query.areaScore"
          mode="multiple"
          :options="dataUtils.areaScoreOptions"
          placeholder="门店分"
          allowClear
          :getPopupContainer="triggerNode => triggerNode.parentNode"
        />
      </ni-filter-item>
      <ni-filter-item label="岗位" v-if="query.dimension === 1">
        <a-input-group compact>
          <a-select
          optionFilterProp="children"
            mode="multiple"
            :maxTagCount="0"
            placeholder="岗位"
            style="width:50%"
            v-model="query.evaluateJob"
            :options="options.job"
            :dropdownMatchSelectWidth="false"
            allowClear
            :getPopupContainer="triggerNode => triggerNode.parentNode"
          >
          </a-select>
          <a-select
            style="width:50%"
            optionFilterProp="children"
            mode="multiple"
            :maxTagCount="0"
            placeholder="服务星级"
            :options="dataUtils.starOptions"
            v-model="query.scores"
            allowClear
            :getPopupContainer="triggerNode => triggerNode.parentNode"
          />
        </a-input-group>
      </ni-filter-item>
      <ni-filter-item label="推荐指数">
        <a-select
          mode="multiple"
          :maxTagCount="0"
          placeholder="推荐指数"
          :options="dataUtils.newCommendScoresOptions"
          v-model="query.commendScores"
          allowClear
          :getPopupContainer="triggerNode => triggerNode.parentNode"
        />
      </ni-filter-item>
      <ni-filter-item label="会员等级">
        <a-select
          mode="multiple"
          :maxTagCount="0"
          placeholder="会员等级"
          :options="options.userClassNewC4"
          v-model="query.userClassList"
          allowClear
          :getPopupContainer="triggerNode => triggerNode.parentNode"
        />
      </ni-filter-item>
      <ni-filter-item class="no-lable">
        <a-input v-model="query.searchValue">
          <a-select
            slot="addonBefore"
            v-model="query.searchKey"
            :options="dataUtils.searchValueOptions"
            :getPopupContainer="triggerNode => triggerNode.parentNode"
          />
        </a-input>
      </ni-filter-item>
      <ni-filter-item label="商品分类">
        <selector-category
          placeholder="商品分类"
          @change="changeCategory"
        />
      </ni-filter-item>
      <ni-filter-item label="品牌">
        <a-select
          placeholder="请选择品牌"
          show-arrow
          allow-clear
          option-filter-prop="children"
          :max-tag-count="1"
          mode="multiple"
          v-model="query.brandIds"
          :options="selectOptions"
          @focus="selectFocus"
          @blur="selectBlur"
          @select="handleSelect"
          @search="selectSearch"
          @popupScroll="selectPopupScroll"
          >
        </a-select>
      </ni-filter-item>
      <ni-filter-item class="no-lable">
        <div class="flex flex-wrap checkboxs">
          <template v-for="(item,index) in dataUtils.onlyChooseOptions">
            <a-checkbox :key="index" @change="changeOnlyChoose(item.value)"
                        :disabled="query.dimension === 0 && $tnt.xtenant === 0 && item.value === 4"
                        :checked="item.value === query.onlyChoose">
              {{ item.label }}
            </a-checkbox>
          </template>
        </div>
<!--        <a-checkbox-group-->
<!--          :value="query.onlyChoose ? [query.onlyChoose] : []"-->
<!--          :options="dataUtils.onlyChooseOptions"-->
<!--          @change="changeOnlyChoose"-->
<!--        />-->
      </ni-filter-item>
      <ni-filter-item class="no-lable">
        <a-checkbox v-model="query.invalid">
          去除无效
        </a-checkbox>
      </ni-filter-item>
    </ni-filter>
  </div>
</template>

<script>
  import { NiFilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import SelectorCategory from '~/components/goods/SelectorCategory'
  import { toRefs, onMounted, getCurrentInstance } from 'vue'
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions.js'
  import useFilterChange from '../model/useFilterChange.js'

  import dataUtils from '../../common/data-utils'
  import { getEnums } from '../../common/useEnums'

  export default {
    name: 'filter-box',
    components: {
      SelectorCategory,
      NiFilter,
      NiFilterItem,
      NiAreaSelect,
    },
    setup () {
      const { proxy } = getCurrentInstance()
      const { state } = useState()
      const { options } = getEnums()
      const { fetchData, pageInit } = useActions()
      const dimensionOptions = proxy.$tnt.xtenant < 1000 ? dataUtils.dimensionOptions.filter(d => d.value !== 2) : dataUtils.dimensionOptions.slice(0, 2)
      const {
        changeCategory,
        changeDimension,
        selectFocus,
        selectBlur,
        selectSearch,
        handleSelect,
        selectPopupScroll,
        changeOnlyChoose,
      } = useFilterChange()
      onMounted(() => {
        pageInit()
      })

      return {
        dataUtils,
        options,
        ...toRefs(state),

        fetchData,
        selectFocus,
        selectBlur,
        selectSearch,
        handleSelect,
        selectPopupScroll,
        changeDimension,
        changeCategory,
        changeOnlyChoose,
        dimensionOptions
      }
    },
  }
</script>
<style lang="scss" scoped>
.ant-input-group-compact{
  display: flex;
 }
.evaluate{
  .filter-item{
    .ant-select{
      min-width: 0;
    }
  }
}
.no-lable{
  :deep(.label) {
    opacity: 0;
  }
}
.checkboxs {
  :deep(.ant-checkbox-wrapper) {
    margin-left: 0px;
    margin-right: 8px;
  }
}
</style>
