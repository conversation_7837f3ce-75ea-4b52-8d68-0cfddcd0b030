<script lang="jsx">
  import {
    defineComponent,
    toRefs
  } from 'vue'
  import { useState } from '../hooks/use-state'
  import useTable from '../hooks/use-table'
  import { NiTable } from '@jiuji/nine-ui'
  import NoData from '~/components/no-data'
  import { columns } from '../constants'
  import { Button } from 'ant-design-vue'
  export default defineComponent({
    components: {
      NiTable,
      NoData,
    },
    setup () {
      const { state, loading, isFeatch } = useState()

      const { footerTotalNum, tableChange, exportDataLoading, handleExportData } = useTable()

      return {
        ...toRefs(state),
        loading,
        isFeatch,
        footerTotalNum,
        tableChange,
        exportDataLoading,
        handleExportData
      }
    },
    render () {
      const {
        dataSource,
        pagination,
        isFeatch,
        loading,
        footerTotalNum,
        tableChange,
        exportDataLoading,
        handleExportData,
        hearderData
      } = this
      return (
      <ni-table
        class="service-score-statistics-table"
        locale={{ emptyText: <NoData is-featch={isFeatch} /> }}
        dataSource={dataSource}
        columns={hearderData}
        loading={loading}
        onChange={(pagination) => {
          tableChange(pagination)
        }}
        pagination={pagination}
        footerTotalNum={footerTotalNum}
      >
      <div slot="action" class="action">
              <Button loading={exportDataLoading} onClick={handleExportData}>
                {exportDataLoading ? '导出中' : '导出'}
              </Button>
            </div>
      </ni-table>
      )
    },
  })
</script>
