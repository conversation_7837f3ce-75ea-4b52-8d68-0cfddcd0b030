<script lang="jsx">
  import { defineComponent, toRefs } from 'vue'
  import { useState } from '../hooks/use-state'
  import useSearch from '../hooks/use-search'
  import CitySelect from '~/components/city-select'
  import { NiFilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import {
    DatePicker,
    Checkbox,
  } from 'ant-design-vue'
  import CityStore from '@logistics/components/city-store'

  export default defineComponent({
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect,
      CityStore,
      CitySelect,
    },
    setup () {
      const { state, loading, fetchData } = useState()
      const {
        areaChange
      } = useSearch()

      return {
        ...toRefs(state),
        loading,
        fetchData,
        areaChange
      }
    },
    render () {
      const {
        loading,
        fetchData,
        formData,
        areaChange
      } = this
      return (
      <div ref="niFilter">
        <NiFilter
          form={formData}
          loading={loading}
          onFilter={() => {
            fetchData(1)
          }}
          do-filter-when-reset={false}
          label-width={100}
          immediate={false}
        >
          <ni-filter-item label="地区">
            <NiAreaSelect
              v-model={formData.areaIds}
              allow-clear={true}
              multiple
              show-search
              placeholder="请选择地区"
              max-tag-count={1}
            />
          </ni-filter-item>
          <ni-filter-item label="时间">
            <DatePicker.MonthPicker
              v-model={formData.date}
              format="YYYY-MM"
              value-format="YYYY-MM"
            />
          </ni-filter-item>
          <ni-filter-item label="区域">
          <div class="flex flex-align-center">
            <Checkbox class="mr-10" v-model={formData.bigArea} onChange={(e) => { areaChange(e, 'smallArea') }}>大区</Checkbox>
            <Checkbox v-model={formData.smallArea} onChange={(e) => { areaChange(e, 'bigArea') }}>小区</Checkbox></div>
          </ni-filter-item>
          <ni-filter-item class="no-label">
            <Checkbox v-model={formData.invalid}>去除无效</Checkbox>
          </ni-filter-item>
        </NiFilter>
      </div>
      )
    },
  })
</script>
<style lang="scss" scoped>
.no-label {
  :deep(.label) {
    display: none;
  }
}
</style>
