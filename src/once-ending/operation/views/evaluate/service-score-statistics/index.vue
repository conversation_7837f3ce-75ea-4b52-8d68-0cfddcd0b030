<script lang="jsx">
  import { createState } from './hooks/use-state'
  import { createApi } from './hooks/use-api'
  import { NiListPage } from '@jiuji/nine-ui'
  import SearchBox from './components/search-box'
  import TableBox from './components/table-box'

  export default {
    components: {
      NiListPage,
      SearchBox,
      TableBox,
    },
    setup () {
      const api = createApi()
      createState(api)
    },
    render () {
      return (
      <page class="score-statistics">
        <ni-list-page pushFilterToLocation={false}>
          <SearchBox class="mb-16" />
          <TableBox />
        </ni-list-page>
      </page>
      )
    },
  }
</script>
