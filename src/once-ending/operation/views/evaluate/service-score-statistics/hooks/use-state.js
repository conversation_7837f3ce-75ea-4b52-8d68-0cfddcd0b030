import { inject, provide, reactive, ref, getCurrentInstance, h } from 'vue'
import moment from 'moment'
import useReportHearder from '@operation/views/statistics-report/hooks/useReportHearder'
const key = Symbol('useState')

export const useState = function () {
  return inject(key)
}

export const createState = function (api) {
  const { queryData } = api

  const { proxy } = getCurrentInstance()

  const loading = ref(false)

  const isFeatch = ref(false)

  const state = reactive({
    formData: {
      areaIds: [],
      date: moment().format('YYYY-MM'),
      bigArea: false,
      smallArea: false,
      invalid: false
    },
    dataSource: [],
    originalDataSource: [],
    originalTotal: {},
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      pageSizeOptions: ['10', '20', '50', '100'],
      showSizeChanger: true,
      showTotal: total => `共 ${total} 条数据`
    },
    hearderData: []
  })

  const getQueryParams = function () {
    const { bigArea, smallArea, ...other } = state.formData
    const params = {
      ...other
    }
    if (bigArea) {
      params.onlyChoose = 1
    } else if (smallArea) {
      params.onlyChoose = 2
    }
    return params
  }

  const fetchData = async function (cur) {
    if (cur) state.pagination.current = cur
    const params = getQueryParams()
    loading.value = true
    const res = await queryData(params)
    loading.value = false
    if (res) {
      isFeatch.value = true
      const { data } = res
      if (!data.length) {
        state.dataSource = []
        state.originalDataSource = []
        state.originalTotal = {}
        state.pagination.total = 0
        return
      }
      if (state.formData.bigArea) {
        const total = data.splice(-1)[0]
        total.area = '合计'
        state.originalTotal = total
      } else {
        state.originalTotal = {}
      }
      state.originalDataSource = data
      state.pagination.total = data.length
      setTableData()
    }
  }

  const setTableData = function () {
    loading.value = true
    const { current, pageSize: size } = state.pagination
    const start = size * current - size
    const end = size * current
    const tableData = state.originalDataSource.slice(start, end)
    if (Object.keys(state.originalTotal).length) {
      tableData.push(state.originalTotal)
    }
    setTimeout(() => {
      loading.value = false
      state.dataSource = tableData
    }, 100)
  }

  fetchData()

  // hearderData: 表头, getHearder: 获取表头
  const { hearderData, getHearder } = useReportHearder({ proxy, h, type: 625 })
  function featchHeader () {
    getHearder({ type: 625 }).then(res => {
      state.hearderData = hearderData
    })
  }
  featchHeader()

  provide(key, {
    state,
    loading,
    isFeatch,
    fetchData,
    setTableData,
    getQueryParams
  })
  return {
    state,
    loading,
    isFeatch,
    fetchData,
    setTableData,
    getQueryParams
  }
}
