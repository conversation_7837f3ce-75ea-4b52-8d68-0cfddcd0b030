import { computed, ref } from 'vue'
import { useState } from './use-state'
import { useApi } from './use-api'

export default function useTable () {
  const { state, setTableData, getQueryParams } = useState()
  const { exportData } = useApi()

  const footerTotalNum = computed(() => state.formData.bigArea ? 1 : 0)

  const tableChange = function (pagination) {
    state.pagination = {
      ...pagination
    }
    setTableData()
  }

  const exportDataLoading = ref(false)
  const handleExportData = async function () {
    const params = getQueryParams()
    exportDataLoading.value = true
    const content = await exportData(params)
    exportDataLoading.value = false
    const date = new Date()
    let linkNode = document.createElement('a')
    linkNode.download = `客评服务分-${date.getFullYear()}年${date.getMonth() +
      1}月${date.getDate()}日${date.getHours()}时${date.getMinutes()}分${date.getSeconds()}秒.xlsx`
    linkNode.style.display = 'none'
    let blob = new Blob([content.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    linkNode.href = URL.createObjectURL(blob)
    document.body.appendChild(linkNode)
    linkNode.click()
    document.body.removeChild(linkNode)
  }

  return {
    footerTotalNum,
    tableChange,
    handleExportData,
    exportDataLoading,
  }
}
