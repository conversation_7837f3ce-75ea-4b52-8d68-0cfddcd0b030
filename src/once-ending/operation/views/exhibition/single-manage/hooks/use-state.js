import { ref, reactive, provide, inject } from 'vue'
import { to } from '@common/utils/common'
import { message } from 'ant-design-vue'
import exhibition from '@operation/api/exhibition'
import axios from 'axios'
import moment from 'moment'
import { commApi, disabledDate, disabledTime } from '../../single-feedback/hooks/use-state'
import uniq from 'lodash/uniq'

const { singleDisplay } = exhibition

const key = Symbol('useState')
export function useState () {
  return inject(key)
}
const orginEditForm = {
  id: undefined,
  title: undefined,
  areaIds: undefined,
  productType: 1,
  pidsOrPpids: undefined,
  displayStandard: undefined,
  displayDescription: undefined,
  feedbackEndTime: undefined
}
export function createState ({ proxy }) {
  const state = reactive({
    searchForm: {
      areaIds: undefined,
      searchKey: 1,
      searchValue: undefined,
      timeType: 1,
      timeValue: undefined
    },
    pagination: {
      pageSize: 10,
      current: 1,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      pageSizeOptions: ['10', '20', '30', '50']
    },
    loading: false,
    dataSource: [],
    editForm: {
      ...orginEditForm
    },
    showEdit: false,
    editType: undefined,
    editLoading: false,
    showFeedback: false,
    feedbackForm: {
      reason: undefined,
      feedbackTime: undefined
    },
    feedbackLoading: false,
    showLog: false,
    logs: [],
    importLoading: false,
    fileList: undefined
  })
  async function fetchData (currentPage) {
    currentPage && (state.current = currentPage)
    const { timeValue } = state.searchForm
    const { current, pageSize } = state.pagination
    const params = {
      ...state.searchForm,
      startTime: timeValue?.length ? timeValue[0] + ' 00:00:00' : undefined,
      endTime: timeValue?.length ? timeValue[1] + ' 23:59:59' : undefined,
      current,
      size: pageSize
    }
    state.cacheParams = { ...params }
    state.loading = true
    const res = await commApi(singleDisplay, 'getManageList', params)
    state.loading = false
    if (res) {
      const { records, total } = res.data
      state.dataSource = records
      state.pagination.total = total
    }
  }
  fetchData(1)
  function handleTableChange (newPage) {
    Object.assign(state.pagination, newPage)
    fetchData(null)
  }
  function toShowEdit (record) {
    state.editForm = record ? { ...record } : { ...orginEditForm }
    proxy.$set(state.editForm, 'areaIds', record?.displayAreaIds?.split(',') || [])
    state.showEdit = true
    state.editType = record ? 2 : 1 // 1添加，2编辑
  }

  async function toSave () {
    const isAdd = (state.editType === 1)
    const { id, title, areaIds, productType, pidsOrPpids, displayStandard, displayDescription, feedbackEndTime, activateTime } = state.editForm
    if (!title) {
      return proxy.$message.warning('请输入标题')
    }
    if (!areaIds?.length) {
      return proxy.$message.warning('请选择陈列区域')
    }
    if (!pidsOrPpids && isAdd) {
      return proxy.$message.warning(`请输入商品信息`)
    }
    if (!displayStandard) {
      return proxy.$message.warning('请上传门店陈列标准')
    }
    const params = {
      id,
      title,
      areaIds,
      productType,
      pidsOrPpids,
      displayStandard,
      displayDescription,
      feedbackEndTime,
      activateTime: activateTime ? moment(activateTime).format(`YYYY-MM-DD${proxy.$tnt.xtenant < 1000 ? ' HH:mm:ss' : ''}`) : undefined
    }
    state.editLoading = true
    const res = await commApi(singleDisplay, 'saveSingleProduct', params)
    state.editLoading = false
    if (res) {
      message.success('保存成功')
      state.showEdit = false
      fetchData(null)
    }
  }

  function toDelete (record) {
    proxy.$confirm({
      title: '确定删除此条数据吗？',
      onOk: async () => {
        const res = await commApi(singleDisplay, 'deleteSingleProduct', record.id)
        if (res) {
          message.success('删除成功')
          const { pagination: { total, current }, dataSource } = state
          if (total % 10 === 1 && record.id === dataSource[dataSource.length - 1].id && current > 1) {
            state.pagination.current -= 1
          }
          fetchData(null)
        }
      }
    })
  }
  const downloadSingleFile = () => {
    window.open('https://img2.ch999img.com/newstatic/25800/0840f3326be42fdf.xlsx?dl=1')
  }
  const handleFileChange = (files) => {
    if (state.importLoading) return

    state.importLoading = true
    state.fileList = files?.fileList?.length ? files.file : ''
    console.log(files)
    const formData = new FormData()
    formData.append('file', state.fileList)

    singleDisplay.importVouter(formData).then(res => {
      if (res.code === 0) {
        let importData = []
        if (res?.data.length) {
          importData = res?.data.map(String)
          if (state.editForm.areaIds && state.editForm.areaIds.length) {
            const areaIds = state.editForm.areaIds.concat(...importData)
            const newAreaIds = uniq(areaIds)
            state.editForm.areaIds = newAreaIds
          } else {
            state.editForm.areaIds = importData
          }
        }
        proxy.$message.success(res.userMsg || '导入成功')
        state.fileList = undefined
      } else {
        proxy.$message.error(res.userMsg || '导入失败')
      }
    }).catch(err => {
      console.error('importVouter:', err)
      state.importLoading = false
    }).finally(() => {
      state.importLoading = false
    })
  }
  // 上传前的前端校验
  const beforeUpload = (file) => {
    const isXlsOrXlsx = file.name.includes('.xls') || file.name.includes('.xlsx') || file.name.includes('.csv')
    if (!isXlsOrXlsx) {
      proxy.$message.error('只支持.xls和.xlsx格式文件!')
    }
    return false
  }

  provide(key, {
    state,
    fetchData,
    handleTableChange,
    toShowEdit,
    toSave,
    toDelete,
    disabledDate,
    disabledTime,
    downloadSingleFile,
    handleFileChange,
    beforeUpload
  })
  return {
    state,
    fetchData,
    handleTableChange,
    toShowEdit,
    toSave,
    toDelete,
    disabledDate,
    disabledTime,
    downloadSingleFile,
    handleFileChange,
    beforeUpload
  }
}
