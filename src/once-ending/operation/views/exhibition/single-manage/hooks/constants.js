export const options = {
  searchType: [
    { label: '标题', value: 1 },
    { label: 'ppid', value: 2 },
    { label: '商品id', value: 3 }
  ],
  timeType: [
    { label: '反馈截止时间', value: 1 },
    { label: '创建时间', value: 2 },
  ],
  idType: [
    { label: '商品id', value: 1 },
    { label: 'ppid', value: 2 }
  ]
}

export const columns = [
  {
    title: '陈列ID',
    dataIndex: 'id',
    width: '70px'
  },
  {
    title: '标题',
    dataIndex: 'title',
    width: '140px'
  },
  {
    title: '门店陈列标准',
    dataIndex: 'displayStandard',
    width: '140px'
  },
  {
    title: '陈列标准说明',
    dataIndex: 'displayDescription',
    width: '160px'
  },
  {
    title: 'ppid',
    dataIndex: 'ppid',
    width: '100px'
  },
  {
    title: '商品id',
    dataIndex: 'productId',
    width: '120px'
  },
  {
    title: '陈列区域',
    dataIndex: 'displayAreaCodes',
    width: '160px'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: '150px'
  },
  {
    title: '激活时间',
    dataIndex: 'activateTime',
    width: '150px'
  },
  {
    title: '反馈截止时间',
    dataIndex: 'feedbackEndTime',
    width: '150px'
  },
  {
    title: '需陈列门店数量',
    dataIndex: 'displayAreaNum',
    width: '120px'
  },
  {
    title: '已反馈数量',
    dataIndex: 'areaNumReceived',
    width: '120px'
  },
  {
    title: '待反馈数量',
    dataIndex: 'areaNumPending',
    width: '120px'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '220px'
  },
]
