<script lang="jsx">
  import { defineComponent, ref, computed, inject, getCurrentInstance, toRefs } from 'vue'
  import { NiFilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import { useState } from '../hooks/use-state'
  import { options } from '../hooks/constants'
  export default defineComponent({
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect
    },
    setup () {
      const { state, fetchData } = useState()
      console.log(123, state.searchForm)
      return {
        ...toRefs(state),
        fetchData
      }
    },
    render () {
      const { searchForm, loading, fetchData } = this
      return <ni-filter
        form={searchForm}
        onFilter={() => {
          fetchData(1)
        }}
        immediate={false}
        loading={loading}
      >
        <ni-filter-item label="陈列区域">
          <NiAreaSelect
            multiple
            allowClear
            mode={1}
            placeholder="请选择地区"
            maxTagCount={1}
            v-model={searchForm.areaIds}
            />
        </ni-filter-item>
        <ni-filter-item class="no-label">
          <a-input-group style="display: flex" compact={true}>
            <a-select
            options={options.searchType}
            v-model={searchForm.searchKey}
            class="flex-child-noshrink"
            style="width: 80px"/>
            <a-input v-model={searchForm.searchValue} allowClear placeholder="请输入"/>
          </a-input-group>
        </ni-filter-item>
        <ni-filter-item class="no-label">
          <a-input-group style="display: flex" compact={true}>
            <a-select
              options={options.timeType}
              v-model={searchForm.timeType}
              class="flex-child-noshrink"
              style="width: 125px"/>
            <a-range-picker style="width: 250px" allowClear v-model={searchForm.timeValue}
                        valueFormat="YYYY-MM-DD" show-time={false}/>
          </a-input-group>
        </ni-filter-item>
      </ni-filter>
    }
  })
</script>
<style scoped lang="scss">
.no-label {
  :deep(.label) {
    display: none;
  }
}
</style>
