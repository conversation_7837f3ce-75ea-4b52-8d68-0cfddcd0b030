<script lang="jsx">
  import { defineComponent, toRefs } from 'vue'
  import { NiAreaSelect } from '@jiuji/nine-ui'
  import { useState } from '../../hooks/use-state'
  import { options } from '../../hooks/constants'
  import standard from '@operation/views/exhibition/single-feedback/components/modals/standard.vue'
  import moment from 'moment'

  export default defineComponent({
    components: { NiAreaSelect, standard },
    setup () {
      const { state, toSave, disabledDate, disabledTime, downloadSingleFile, handleFileChange, beforeUpload } = useState()
      return {
        ...toRefs(state),
        disabledDate,
        disabledTime,
        toSave,
        downloadSingleFile,
        handleFileChange,
        beforeUpload
      }
    },
    render () {
      const { editLoading, editForm, disabledDate, disabledTime, toSave, editType, downloadSingleFile, handleFileChange, beforeUpload, importLoading } = this
      const disabled = (editType === 2)
      return <div>
        <a-modal
          title={`${editType === 1 ? '添加' : '编辑'}需反馈陈列的商品`}
          destroyOnClose
          maskClosable={false}
          width={650}
          onOk={toSave}
          onCancel={() => { this.showEdit = false }}
          confirmLoading={editLoading}
          v-model={this.showEdit}>
          <a-form-model ref="editFormRef" wrapperCol={{ span: 16 }} labelCol={{ span: 6 }}>
            <a-form-model-item label="标题" required>
              <a-input placeholder={`请输入标题`} v-model={editForm.title}/>
            </a-form-model-item>
            <a-form-model-item label="陈列区域" required>
              <NiAreaSelect maxTagCount={1} mode={3} placeholder="请选择陈列区域" multiple allowClear
                            style="width: 255px" v-model={editForm.areaIds}/>
              <a-upload
                name="file"
                accept=".xls,.xlsx"
                before-upload={beforeUpload}
                showUploadList={false}
                onChange={handleFileChange}
              >
                <a-button type="link" loading={importLoading}>导入</a-button>
              </a-upload>
              <span class="pointer blue" onClick={downloadSingleFile}>下载模板</span>
            </a-form-model-item>
            <a-form-model-item label="商品信息" required={!disabled}>
              <a-radio-group disabled={disabled} options={options.idType} v-model={editForm.productType}/>
              <a-input disabled={disabled} allowClear placeholder={`请输入`} v-model={editForm.pidsOrPpids}/>
            </a-form-model-item>
            <a-form-model-item label="激活时间">
              <a-date-picker
                allowClear
                v-model={editForm.activateTime}
                format={`YYYY-MM-DD${this.$tnt.xtenant < 1000 ? ' HH:mm:ss' : ''}`}
                valueFormat={`YYYY-MM-DD${this.$tnt.xtenant < 1000 ? ' HH:mm:ss' : ''}`}
                showTime={this.$tnt.xtenant < 1000}
              />
            </a-form-model-item>
            <a-form-model-item label="门店陈列标准" required>
              <standard v-model={editForm.displayStandard}/>
            </a-form-model-item>
            <a-form-model-item label="门店陈列说明" prop="displayDescription">
              <div class="relative input-box">
                <a-textarea placeholder="请输入门店陈列说明" maxLength={200} autoSize={{
                  minRows: 2,
                  maxRows: 6
                }} v-model={editForm.displayDescription}/>
                <span class="text">{editForm.displayDescription?.length || 0}/200</span>
              </div>
            </a-form-model-item>
            <a-form-model-item label="反馈截止时间">
              <a-date-picker
                allowClear
                v-model={editForm.feedbackEndTime}
                show-time={true}
                valueFormat="YYYY-MM-DD HH:mm:ss"
                disabledDate={disabledDate}
                disabledTime={() => disabledTime(editForm.feedbackEndTime)}
              />
            </a-form-model-item>
          </a-form-model>
        </a-modal>
      </div>
    }
  })
</script>
<style lang="scss" scoped>
:deep(.ant-form-item) {
  display: flex;
}

.input-box {
  .text {
    position: absolute;
    right: 2px;
    bottom: -12px;
    font-size: 12px;
    color: #8b8a8a;
    line-height: 14px;

  }
}
</style>
