<script lang="jsx">
  import Vue, { defineComponent, toRefs, ref, nextTick, getCurrentInstance, computed } from 'vue'
  import { NiTable, NiImg } from '@jiuji/nine-ui'
  import { useState } from '../hooks/use-state'
  import { columns } from '../hooks/constants'
  import Viewer from 'v-viewer'
  import 'viewerjs/dist/viewer.css'
  Vue.use(Viewer)
  export default defineComponent({
    components: { NiTable, NiImg },
    setup () {
      const { proxy } = getCurrentInstance()
      const { state, handleTableChange, toShowEdit, toDelete } = useState()
      const hashpcl = computed(() => proxy.$store.state.userInfo.Rank.includes('hpcl'))
      const imgUrl = ref('')
      const viewImage = function (img) {
        imgUrl.value = img
        nextTick(() => {
          // 找到.images的div挂载到$viewer上
          const viewer = proxy.$el.querySelector('.images').$viewer
          console.log(viewer)
          viewer.show()
        })
      }
      function toDetail (record) {
        const { productType, pidsOrPpids, displayAreaIds } = record
        displayAreaIds?.split(',')
        const search = {
          searchKey: productType,
          searchValue: pidsOrPpids,
          areaIds: displayAreaIds?.split(',') || [],
          singleId: record.id
        }
        return `/store/exhibition/single-feedback?search=${JSON.stringify(search)}`
      }
      return {
        ...toRefs(state),
        handleTableChange,
        viewImage,
        imgUrl,
        hashpcl,
        toShowEdit,
        toDelete,
        toDetail
      }
    },
    data () {
      return {
        customRenderMap: new Map([
          [
            'normal',
            (text, record) => <span>{ text || '--' }</span>
          ],
          [
            'productId',
            (text, record) => <span>{ record.productType === 1 ? record.pidsOrPpids : '--' }</span>
          ],
          [
            'ppid',
            (text, record) => <span>{ record.productType === 2 ? record.pidsOrPpids : '--' }</span>
          ],
          [
            'texts',
            (text, record) => <div class="flex flex-center">
              {text?.length > 10 ? <a-popover>
                <div slot="content" style="maxWidth: 500px">{ text }</div>
                <div>{ text.substring(0, 10) }...</div>
              </a-popover> : (text || '--')}
            </div>
          ],
          [
            'showImg',
            (text, record) => text ? <NiImg class="image-single-feedback-scoped" src={text} onTap={() => { this.viewImage(text) }}/> : '--'
          ],
          [
            'operation',
            (text, record) => <div class="flex flex-center buttons">
              { this.hashpcl ? <a-button type="link" onClick={() => { this.toShowEdit(record) }}>编辑</a-button> : null }
              { this.hashpcl ? <a-button type="link" onClick={() => { this.toDelete(record) }}>删除</a-button> : null }
              <router-link to={this.toDetail(record)} target="_blank" style="margin-left: 4px">反馈详情</router-link>
            </div>
          ]
        ])
      }
    },
    computed: {
      newColumns () {
        return columns.map(it => {
          const cache = { ...it }
          cache.customRender = this.customRenderMap.get('normal')
          const customRenders = this.customRenderMap.get(it.dataIndex)
          customRenders && (cache.customRender = customRenders)
          if (it.dataIndex === 'displayStandard' || it.dataIndex === 'displayCurrent') {
            cache.customRender = this.customRenderMap.get('showImg')
          }
          if (it.dataIndex === 'displayDescription' || it.dataIndex === 'displayAreaCodes') {
            cache.customRender = this.customRenderMap.get('texts')
          }
          return cache
        })
      }
    },
    render () {
      const { loading, pagination, handleTableChange, newColumns, dataSource, imgUrl } = this
      return <div>
        <ni-table
          rowKey={ (r, i) => i }
          loading={loading}
          columns={newColumns}
          dataSource={dataSource}
          pagination={pagination}
          bordered
          align={'center'}
          onChange={handleTableChange}
          class="mt-20"
        >
        <div slot="tool">
          <div class="flex flex-align-center" style="float:right">
            <a-button onClick={() => this.toShowEdit(null)} type="primary">添加陈列</a-button>
          </div>
        </div>
        </ni-table>
        <div class="images" v-viewer={{ movable: false }} style="display: none">
          <img src={imgUrl}/>
        </div>
      </div>
    }
  })
</script>
<style lang="scss">
.image-single-feedback-scoped {
  width: 80px;
  height: 60px;
  cursor: pointer;
}
</style>
<style scoped lang="scss">
:deep(.buttons .ant-btn) {
  padding: 0px 8px;
}
.showText {
  width: 100px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
</style>
