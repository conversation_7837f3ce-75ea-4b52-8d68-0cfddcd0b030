<script lang="jsx">
  import { defineComponent, getCurrentInstance } from 'vue'
  import { NiListPage } from '@jiuji/nine-ui'
  import searchBox from './components/search-box.vue'
  import tableBox from './components/table-box.vue'
  import edit from './components/modals/edit'
  import { createState } from './hooks/use-state'
  import BtnRouter from '@operation/components/btn-router'
  export default defineComponent({
    components: { NiListPage, searchBox, tableBox, BtnRouter, edit },
    setup () {
      const { proxy } = getCurrentInstance()
      const aaa = createState({ proxy })
      console.log(aaa)
    },
    render () {
      return <page>
        <ni-list-page pushFilterToLocation={false}>
          <searchBox/>
          <tableBox/>
        </ni-list-page>
        <edit/>
      </page>
    }
  })
</script>
<style scoped>

</style>
