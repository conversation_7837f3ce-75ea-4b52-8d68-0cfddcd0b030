export const displayStatusOptions = [
  {
    label: '待上传',
    value: 2
  },
  {
    label: '已上传',
    value: 4
  }
]

export const searchTypeOptions = [
  { label: '商品名称', value: 'productName' },
  { label: 'ppid', value: 'ppId' },
  { label: '商品ID', value: 'pid' },
  { label: 'mkc_id', value: 'mkcId' },
  { label: '陈列编号', value: 'displayId' },
]

export const statusStyle = new Map([
  [
    2,
    { background: 'rgba(241, 86, 67, 0.1)', color: 'rgb(241, 86, 67)' }
  ],
  [
    4,
    { background: 'rgba(11, 190, 105, 0.1)', color: 'rgb(11, 190, 105)' }
  ]
])

export const displayLevelOptions = [
  {
    label: 'A类门店',
    value: 1
  },
  {
    label: 'B类门店',
    value: 2
  },
  {
    label: 'C类门店',
    value: 3
  },
  {
    label: 'S类门店',
    value: 4
  },
]

export const statusMap = new Map([[2, '待上传'], [4, '已上传']])

export const goodsColumns = [
  {
    title: '门店',
    titleStr: '门店',
    titleSetting: '门店',
    note: '',
    oldNote: '',
    isEdit: false,
    dataIndex: 'area',
    key: 'area',
    width: 140,
    fixed: 'left'
  },
  {
    title: '陈列柜/专区',
    titleStr: '陈列柜/专区',
    titleSetting: '陈列柜/专区',
    note: '',
    oldNote: '',
    isEdit: false,
    dataIndex: 'name',
    width: 230,
    key: 'name',
    showDimension: 1,
    fixed: 'left'
  },
  {
    title: '陈列专区',
    titleStr: '陈列专区',
    titleSetting: '陈列专区',
    note: '',
    oldNote: '',
    isEdit: false,
    dataIndex: 'name',
    width: 230,
    key: 'name',
    showDimension: 2,
    fixed: 'left'
  },
  {
    title: '已陈列商品总数',
    titleStr: '已陈列商品总数',
    titleSetting: '已陈列商品总数',
    note: '',
    oldNote: '',
    isEdit: false,
    dataIndex: 'productTotalNum',
    width: 140,
    key: 'productTotalNum'
  },
  // {
  //   title: '陈列位',
  //   titleStr: '陈列位',
  //   titleSetting: '陈列位',
  //   note: '',
  //   oldNote: '',
  //   isEdit: false,
  //   dataIndex: 'exhibitNum',
  //   width: 120,
  //   key: 'exhibitNum'
  // },
  {
    title: '待陈列商品总数',
    titleStr: '待陈列商品总数',
    titleSetting: '待陈列商品总数',
    note: '',
    oldNote: '',
    isEdit: false,
    dataIndex: 'toBeProductTotalNum',
    width: 140,
    key: 'toBeProductTotalNum',
    showDimension: 2
  },
  {
    title: '机位数',
    titleStr: '机位数',
    titleSetting: '机位数',
    note: '',
    oldNote: '',
    isEdit: false,
    dataIndex: 'displayBitNum',
    width: 120
  },
  {
    title: '近7天销量',
    titleStr: '近7天销量',
    titleSetting: '近7天销量',
    note: '',
    oldNote: '',
    isEdit: false,
    dataIndex: 'last7DaysSales',
    key: 'last7DaysSales',
    width: 140,
    textCustomRender: true,
    hide: true
  },

  {
    title: '近7天毛利',
    titleStr: '近7天毛利',
    titleSetting: '近7天毛利',
    note: '',
    oldNote: '',
    isEdit: false,
    dataIndex: 'last7DaysProfit',
    key: 'last7DaysProfit',
    width: 140,
    textPrice: true,
    hide: true
  },
  {
    title: '近30天销量',
    titleStr: '近30天销量',
    titleSetting: '近30天销量',
    note: '',
    oldNote: '',
    isEdit: false,
    dataIndex: 'last30DaysSales',
    key: 'last30DaysSales',
    width: 150,
    textCustomRender: true
  },
  {
    title: '近30天毛利',
    titleStr: '近30天毛利',
    titleSetting: '近30天毛利',
    note: '',
    oldNote: '',
    isEdit: false,
    dataIndex: 'last30DaysProfit',
    key: 'last30DaysProfit',
    width: 150,
    textPrice: true,
  },
  {
    title: '累计销量',
    titleStr: '累计销量',
    titleSetting: '累计销量',
    note: '',
    oldNote: '',
    isEdit: false,
    dataIndex: 'lastSales',
    key: 'lastSales',
    width: 150,
    hide: true
  },
  {
    title: '累计毛利',
    titleStr: '累计毛利',
    titleSetting: '累计毛利',
    note: '',
    oldNote: '',
    isEdit: false,
    dataIndex: 'lastProfit',
    key: 'lastProfit',
    width: 150,
    textPrice: true,
    hide: true
  },
  {
    title: '门店库存',
    titleStr: '门店库存',
    titleSetting: '门店库存',
    note: '',
    oldNote: '',
    isEdit: false,
    dataIndex: 'stockNum',
    key: 'stockNum',
    width: 140
  },
  {
    title: '陈列总成本',
    titleStr: '陈列总成本',
    titleSetting: '陈列总成本',
    note: '',
    oldNote: '',
    isEdit: false,
    dataIndex: 'displayCost',
    key: 'displayCost',
    textPrice: true,
    width: 140
  },
  {
    title: '专区负责人',
    titleStr: '专区负责人',
    titleSetting: '专区负责人',
    note: '',
    oldNote: '',
    isEdit: false,
    dataIndex: 'specialAreaCharge',
    key: 'specialAreaCharge',
    width: 140
  },
  {
    title: '专区陈列图',
    titleStr: '专区陈列图',
    titleSetting: '专区陈列图',
    note: '',
    oldNote: '',
    isEdit: false,
    dataIndex: 'specialAreaDpImages',
    key: 'specialAreaDpImages',
    width: 120
  },
  {
    title: '陈列图状态',
    titleStr: '陈列图状态',
    titleSetting: '陈列图状态',
    note: '',
    oldNote: '',
    isEdit: false,
    dataIndex: 'specialAreaDpImageStatus',
    key: 'specialAreaDpImageStatus',
    width: 120
  },
  {
    title: '操作',
    titleStr: '操作',
    titleSetting: '操作',
    dataIndex: 'tableAction',
    key: 'tableAction',
    width: 300,
    fixed: 'right'
  }
]

export const logColumns = [
  {
    title: '地区',
    dataIndex: 'areaBig',
    key: 'areaBig',
  },
  {
    title: '门店',
    dataIndex: 'area',
    key: 'area',
  },
  {
    title: '操作次数',
    dataIndex: 'counts',
    key: 'counts'
  },
  {
    title: '最后操作人',
    dataIndex: 'ch999Name',
    key: 'ch999Name',
  },
  {
    title: '最后操作时间',
    dataIndex: 'time',
    key: 'time',
  }
]

export const productFlagOptions = [
  {
    label: '是',
    value: 1
  },
  {
    label: '否',
    value: 0
  }
]

export const addColumns = function (type) {
  const columns = [
    {
      title: type === 1 ? '陈列编号' : 'mkc_id',
      dataIndex: 'relationId',
      key: 'relationId',
      width: '15%'
    },
    {
      title: 'ppid',
      dataIndex: 'ppid',
      key: 'ppid',
      align: 'center',
      width: '20%',
      show: [1]
    },
    {
      title: '串号',
      dataIndex: 'imei',
      key: 'imei',
      align: 'center',
      width: '20%',
      show: [2]
    },
    {
      title: '商品名称',
      dataIndex: 'productName',
      key: 'productName'
    },
    {
      title: '是否陈列',
      dataIndex: 'displayFlag',
      key: 'displayFlag',
      width: '10%'
    }
  ]
  return columns.filter(d => !d.show || d.show.includes(type))
}

export const selectProductColumns = [
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '陈列编号/mkc_id',
    dataIndex: 'relationId',
    key: 'relationId',
    width: 150
  },
  {
    title: 'ppid',
    dataIndex: 'ppid',
    key: 'ppid',
    align: 'center',
    width: 100
  },
  {
    title: '串号',
    dataIndex: 'imei',
    key: 'imei',
    align: 'center',
    width: 150
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
    key: 'productName',
    width: 300
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action'
  }
]

export const tabs = [
  {
    label: '小件',
    value: 1
  },
  {
    label: '大件',
    value: 2
  }
]

export const keyOptions = [
  {
    label: '陈列编号',
    value: 'smallDispalyId',
    show: [1]
  },
  {
    label: 'ppid',
    value: 'ppid',
    show: [1]
  },
  {
    label: 'mkc_id',
    value: 'mkcId',
    show: [2]
  },
  {
    label: '串号',
    value: 'imei',
    show: [2]
  },
  {
    label: '商品名称',
    value: 'productName',
    show: [1, 2]
  }
]

export const machineFilterOptions = [
  {
    label: '商品总数多于机位数',
    value: 0
  },
  {
    label: '商品总数等于机位数',
    value: 1
  },
  {
    label: '商品总数少于机位数',
    value: 2
  },
  {
    label: '未添加机位数',
    value: 3
  }
]

export const cateIdsOptions = [
  {
    label: '手机',
    value: '2'
  },
  {
    label: '平板',
    value: '21'
  },
  {
    label: '电脑',
    value: '20,22,201'
  }
]

export const statisticsOptions = [
  { label: '总计', value: ' ', key: 'total' },
  { label: '门店总数', value: '', key: 'areaCountTotal' },
  { label: '柜子总数', value: '', key: 'photoCountTotal' },
  { label: '陈列商品总数', value: '', key: 'productTotalNumTotal' },
  { label: '近7天销量', value: '', key: 'countSalesSevenTotal' },
  { label: '近7天毛利', textPrice: true, value: '', key: 'grossProfitSevenTotal' },
  { label: '近30天销量', value: '', key: 'countSalesThirtyTotal' },
  { label: '近30天毛利', textPrice: true, value: '', key: 'grossProfitThirtyTotal' },
  { label: '累计销量', value: '', key: 'countSalesAllTotal' },
  { label: '累计毛利', textPrice: true, value: '', key: 'grossProfitAllTotal' },
  { label: '陈列商品总成本', textPrice: true, value: '', key: 'productCostTotal' }
]

export const originSearchForm = {
  tag: 'productName',
  cabinetTypes: [], // 柜子类型
  cabinetName: undefined,
  displayStatus: [], // 展柜状态
  areaIds: [],
  keyWord: undefined,
  machineFilter: undefined,
  timeRange: undefined,
  showDimension: 2,
  stockFilter: 1,
  photoLevels: undefined,
  specialAreaIds: undefined,
  stockFilterValue: undefined,
  cidList: [],
  brandIdList: [],
  toBeDpNumFilter: 1,
  toBeDpNumValue: undefined,
}

const commonRule = { required: true, trigger: ['blur', 'change'] }
export const manegeRules = {
  level: [{ ...commonRule, message: '请选择陈列柜等级' }],
  specialAreaIds: [{ ...commonRule, message: '请选择专区' }]
}
