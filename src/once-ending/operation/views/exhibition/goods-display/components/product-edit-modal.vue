<script lang="jsx">
  import { defineComponent, computed, ref, watch, inject, getCurrentInstance } from 'vue'
  import {
    Modal,
    Select,
    Table,
    Icon,
    message,
    Button,
    Tabs,
    Input,
    Checkbox,
    Popover
  } from 'ant-design-vue'
  import {
    addColumns,
    tabs,
    keyOptions,
    selectProductColumns,
    cateIdsOptions
  } from '../constants'
  import Uploader from '~/components/custom-uploader'
  import {
    GET_NC_ATTACHMENTS_SIMPLE_VO,
    ADD_PRODUCT_DATA,
    GET_PRODUCT_RELATION_AND_DISPLAY_KC
  } from '@operation/store/modules/exhibition/action-types'

  export default defineComponent({
    components: {
      Uploader
    },
    data () {
      return {
        customRenderMap: new Map([
          [
            'displayFlag',
            (value, row, index) => {
              return (
              <Checkbox
                checked={row.displayFlag}
                onChange={e => {
                  this.displayFlagChange(e, row)
                }}
              ></Checkbox>
              )
            }
          ]
        ]),
        selectProductCustomRenderMap: new Map([
          [
            'action',
            (value, row) => {
              return (
              <Button type="link" onClick={() => this.delProduct(row)}>
                删除
              </Button>
              )
            }
          ],
          ['type', text => <span>{text === 1 ? '小件' : '大件'}</span>],
          ['ppid', text => <span>{text || '--'}</span>],
          ['imei', text => <span>{text || '--'}</span>]
        ])
      }
    },
    computed: {
      columns () {
        const columns = addColumns(this.tabValue)
        columns.forEach(d => {
          if (this.customRenderMap.get(d.dataIndex)) {
            d.customRender = this.customRenderMap.get(d.dataIndex)
          }
        })
        return columns
      },
      selectProductColumns () {
        selectProductColumns.map(d => {
          const customRender = this.selectProductCustomRenderMap.get(d.dataIndex)
          if (customRender) d.customRender = customRender
        })
        return selectProductColumns
      }
    },
    props: {
      showProductEditModal: {
        type: Boolean,
        default: false
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const handleDestroyModal = inject('handleDestroyModal')
      const getData = inject('getData')
      const selectRow = inject('selectRow')

      // message.config({
      //   getContainer: () => document.querySelector('.product-edit-modal') || document.body
      // })

      const visible = computed({
        get: () => {
          return props.showProductEditModal
        },
        set: val => {
          proxy.$emit('update:showProductEditModal', val)
        }
      })

      const selectProduct = ref([])

      const orgSelectProductFlag = ref([])

      const selectProductChange = computed(() => {
        const selectProductFlag = selectProduct.value.map(d => d.flag)
        return (
          [
            ...selectProductFlag.filter(
              x => !orgSelectProductFlag.value.includes(x)
            ),
            ...orgSelectProductFlag.value.filter(
              x => !selectProductFlag.includes(x)
            )
          ].length > 0
        )
      })

      const delProduct = function (row) {
        const index = selectProduct.value.findIndex(d => d.flag === row.flag)
        selectProduct.value.splice(index, 1)
        setChecked(row)
      }

      const clearTabAllSelect = function () {
        const flags = selectProduct.value.filter(d => d.type === tabValue.value).map(d => d.flag)
        flags.forEach(d => {
          const index = selectProduct.value.findIndex(k => k.flag === d)
          selectProduct.value.splice(index, 1)
        })
        setChecked()
      }

      const setSelectProduct = function (type) {
        const dataSource =
          type === 1 ? smallDataSource.value : bigDataSource.value
        dataSource.forEach(item => {
          if (item.displayFlag) {
            selectProduct.value.push(item)
          }
        })
      }

      const tabValue = ref(1)

      const filter = ref({
        key: 'smallDispalyId',
        value: undefined,
        cateIds: undefined
      })

      const smallDataSource = ref([])

      const bigDataSource = ref([])

      const imgUrls = ref([])

      const orgImgFids = ref([])

      const imgUrlsChange = computed(() => {
        const imgFids = imgUrls.value.map(d => d.fid)
        return (
          [
            ...imgFids.filter(x => !orgImgFids.value.includes(x)),
            ...orgImgFids.value.filter(x => !imgFids.includes(x))
          ].length > 0
        )
      })

      const loading = ref(false)

      const dataSourceLimit = computed(() => imgUrls.value?.length >= 4)

      const getNcAttachmentsSimpleVO = async function () {
        const params = {
          linkId: selectRow.value.fixPhotoId,
          type: 60
        }
        const res = await proxy.$store.dispatch(
          `operation/exhibition/${GET_NC_ATTACHMENTS_SIMPLE_VO}`,
          params
        )
        if (res) {
          const { data } = res
          data.map(d => {
            d.checkId = d.id
          })
          imgUrls.value = data
          orgImgFids.value = data.map(d => d.fid)
        }
      }

      watch(
        () => visible.value,
        val => {
          if (val) {
            getNcAttachmentsSimpleVO()
            init()
          }
        },
        {
          immediate: true
        }
      )

      const addProductData = async function () {
        if (!selectProduct.value.length) {
          message.error('请选择需要陈列的商品')
          return
        }
        if (imgUrls.value.length === 0) {
          message.error('请上传陈列图')
          return
        }
        if (imgUrls.value?.length > 4) {
          message.error('最多上传4张，请适当删减')
          return
        }
        if (!selectProductChange.value && !imgUrlsChange.value) {
          visible.value = false
          return
        }
        const displayPhotoList = imgUrls.value.map(d => {
          const { fid, fileName, filePath, checkId } = d
          return {
            linkId: selectRow.value.fixPhotoId,
            fid,
            fileName,
            filePath,
            id: checkId
          }
        })

        const stockList = selectProduct.value.map(d => {
          const {
            id,
            ppid,
            productName,
            relationId,
            categoryId,
            brandId,
            imei,
            type,
            productId
          } = d
          return {
            id,
            ppid,
            productName,
            relationId,
            categoryId,
            brandId,
            imei,
            type,
            productId
          }
        })
        const { areaId, fixPhotoId } = selectRow.value
        const params = {
          areaId,
          fixPhotoId,
          displayPhotoList,
          stockList
        }
        const res = await proxy.$store.dispatch(
          `operation/exhibition/${ADD_PRODUCT_DATA}`,
          params
        )
        if (res) {
          message.success('添加成功')
          visible.value = false
          getData()
        }
      }

      const setAttach = function (fileList) {
        if (fileList.length > 4) {
          message.info('最多上传4张展陈图片')
          imgUrls.value = fileList.slice(0, 4)
        }
      }

      const afterClose = function () {
        handleDestroyModal('productEdit')
      }

      const tabsChange = function (key) {
        filter.value.key = key === 1 ? 'smallDispalyId' : 'mkcId'
        filter.value.value = undefined
        filter.value.cateIds = undefined
        fetchList()
      }

      const fetchList = async function (type) {
        const { value, key, cateIds } = filter.value
        const { areaId, fixPhotoId } = selectRow.value
        const params = {
          areaId,
          value,
          type: type || tabValue.value,
          key,
          fixPhotoId
        }
        if ((type && type === 2) || (!type && tabValue.value === 2)) {
          params.cateIds = cateIds ? cateIds.split(',') : []
        }
        loading.value = true
        const res = await proxy.$store.dispatch(
          `operation/exhibition/${GET_PRODUCT_RELATION_AND_DISPLAY_KC}`,
          params
        )
        loading.value = false
        if (res) {
          const { data } = res
          data.forEach(d => {
            d.displayFlag = !!d.id
            d.flag = `${d.type}_${d.relationId}`
          })
          if (params.type === 1) {
            smallDataSource.value = data
          } else {
            bigDataSource.value = data
          }
          if (type) { // 初始化,获取选择项
            setSelectProduct(type)
          } else { // 如果不是初始化,处理表格勾选项
            setChecked()
          }
        }
      }

      const setChecked = function (row) {
        const dataSource =
          tabValue.value === 1 ? smallDataSource.value : bigDataSource.value
        if (row) {
          const item = dataSource.find(k => k.flag === row.flag)
          if (item) item.displayFlag = false
        } else {
          dataSource.forEach(d => {
            const item = selectProduct.value.find(k => k.flag === d.flag)
            if (item) {
              if (item.displayFlag !== d.displayFlag) d.displayFlag = item.displayFlag
            } else {
              d.displayFlag = false
            }
          })
        }
      }

      const displayFlagChange = function (e, row) {
        row.displayFlag = e.target.checked
        if (e.target.checked) {
          if (!selectProduct.value.find(d => d.flag === row.flag)) { selectProduct.value.push(row) }
        } else {
          if (selectProduct.value.find(d => d.flag === row.flag)) {
            const index = selectProduct.value.findIndex(d => d.flag === row.flag)
            selectProduct.value.splice(index, 1)
          }
        }
      }

      const getOrgSelectProductFlag = function () {
        orgSelectProductFlag.value = selectProduct.value.map(d => d.flag)
      }

      const init = async function () {
        await fetchList(1)
        await fetchList(2)
        getOrgSelectProductFlag()
      }

      const uploaderOn = {
        'update:fileList': val => {
          if (val.length > 4) {
            message.info('最多上传4张展陈图片')
            imgUrls.value = val.slice(0, 4)
          } else {
            imgUrls.value = val
          }
        }
      }
      return {
        visible,
        addProductData,
        smallDataSource,
        bigDataSource,
        imgUrls,
        setAttach,
        dataSourceLimit,
        afterClose,
        tabValue,
        tabsChange,
        filter,
        loading,
        fetchList,
        selectProduct,
        delProduct,
        displayFlagChange,
        clearTabAllSelect,
        selectRow,
        uploaderOn
      }
    },
    render () {
      const {
        addProductData,
        bigDataSource,
        smallDataSource,
        imgUrls,
        dataSourceLimit,
        afterClose,
        columns,
        tabsChange,
        filter,
        loading,
        selectRow,
        fetchList,
        selectProduct,
        selectProductColumns,
        clearTabAllSelect,
        uploaderOn
      } = this

      return (
      <Modal
        title="编辑陈列商品"
        v-model={this.visible}
        onOk={addProductData}
        width={800}
        maskClosable={false}
        afterClose={afterClose}
        wrapClassName="product-edit-modal"
      >
        <div class="require">陈列柜名称：{selectRow.cabinetName}</div>
        <Tabs
          style="margin-top:10px"
          v-model={this.tabValue}
          onChange={tabsChange}
        >
          {tabs.map(d => (
            <Tabs.TabPane key={d.value} tab={d.label}></Tabs.TabPane>
          ))}
        </Tabs>
        <div class="filter-box flex">
          <div style="flex:1" class="flex">
            <Input.Group compact style="width:60%">
              <Select
                style="width:30%"
                v-model={filter.key}
                options={keyOptions.filter(d => d.show.includes(this.tabValue))}
              ></Select>
              <Input
                style="width:70%"
                v-model={filter.value}
                allow-clear
              ></Input>
            </Input.Group>
            {
              this.tabValue === 2 ? <Select
                style="width:30%;margin-left:10px"
                v-model={filter.cateIds}
                options={cateIdsOptions}
                placeholder="商品分类"
                allow-clear
              ></Select> : null
            }
            <Button
              style="margin-left: 10px;"
              type="primary"
              onClick={() => {
                fetchList()
              }}
              loading={loading}
            >
              查询
            </Button>
            <Popover
              get-popup-container={triggerNode => triggerNode.parentNode}
            >
              <template slot="content">
                <Table
                  columns={selectProductColumns}
                  bordered
                  rowKey={(r, index) => {
                    return index
                  }}
                  data-source={selectProduct}
                  pagination={false}
                ></Table>
              </template>
              <Button type="link">已选{`${selectProduct.length}项`}</Button>
            </Popover>
          </div>
          <Button type="primary" onClick={clearTabAllSelect}>
            取消所有{this.tabValue === 1 ? '小' : '大'}件陈列
          </Button>
        </div>
        <div class="table-box">
          <Table
            columns={columns}
            bordered
            rowKey={(r, index) => {
              return index
            }}
            scroll={{ y: 325 }}
            data-source={this.tabValue === 1 ? smallDataSource : bigDataSource}
            pagination={false}
            loading={loading}
          ></Table>
        </div>
        <div class="upload-box flex">
          <div style="margin-top: 15px;" class="require">
            展陈图片
          </div>
          <div>
            <p>
              {imgUrls && imgUrls.length > 4 ? (
                <span class="font-12 grey-9">
                  <Icon class="yellow font-14" type="exclamation-circle" />{' '}
                  最多上传4张图片
                </span>
              ) : null}
            </p>

            <div id="upload-img-push">
              <Uploader
                qr-tip="上传过程中请勿关闭二维码,关闭二维码会造成上传失败"
                file-list={imgUrls}
                {...{
                  on: uploaderOn,
                  props: { onlyShowPhone: true, showRename: false }
                }}
                accept={'image/*'}
                ref="uploader"
                path-key="filePath"
                disabled={dataSourceLimit}
              />
            </div>
          </div>
        </div>
      </Modal>
      )
    }
  })
</script>
<style lang="scss" scoped>
.require {
  &::before {
    display: inline-block;
    margin-right: 4px;
    color: #f5222d;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: "*";
  }
}
.table-box {
  margin-top: 10px;
}
.upload-box {
  margin-top: 10px;
}
:deep(.ant-modal-wrap) {
  z-index: 1000;
}
</style>
