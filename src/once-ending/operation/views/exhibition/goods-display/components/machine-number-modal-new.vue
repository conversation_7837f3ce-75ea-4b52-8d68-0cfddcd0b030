<template>
  <a-modal
    :visible="visible"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :afterClose="afterClose"
    width="700px"
  >
    <template #title>
      <div>{{machineNumberTitle}}</div>

    </template>
    <div class="content-wrap">
<!--      <div class="tip mb-8">真机桌陈列位 = 亚克力台签数/2，边柜陈列位：1.2m为一个； 机位数 = 真机数+机模数；小件陈列数量较多时，机位数的品牌可选择‘其他’统一填写。</div>-->
      <a-form-model
        ref="editRef"
        class="editRefClass"
        :labelCol="{ span: 5 }"
        :model="machineNumConfig"
        :wrapperCol="{ span: 16 }">
<!--        <a-form-model-item-->
<!--          label="陈列位">-->
<!--          <a-input-number v-model="exhibitNum" placeholder="请输入陈列位" :min="0.1" :max="99999999" :precision="1" style="width:520px"></a-input-number>-->
<!--        </a-form-model-item>-->
        <div class="flex" v-for="(item, index) in machineNumConfig" :key="index">
          <a-form-model-item
            :prop="'brandId' + index"
            label="品牌"
            :autoLink="false"
            :class="'brandIdClass' + index"
            :id="'#brandId' + index"
            :ref="'brandIdRef' + index"
            :rules="specialRules('brandId', index)">
            <brand
              :machineNumConfig="machineNumConfig"
              :index="index"
              showSearch
              :container="'.brandIdClass' + index"
              class="full-width"
              :multiple="false"
              v-model="item.brandId"
              @blur="blurOrChange('brandIdRef' + index, 'blur')"
              @change="(value, names, list) => brandChange(value, names, list, index, childIndex)"/>
          </a-form-model-item>
          <a-form-model-item :prop="'machineNumber' + index" label="机位数" :autoLink="false" :ref="'machineNumberRef' + index" :rules="specialRules('machineNumber', index)">
            <a-input-number
              placeholder="请输入机位数"
              :min="0"
              :max="99999999"
              :precision="0"
              v-model="item.machineNumber"
              class="full-width"
              @blur="blurOrChange('machineNumberRef' + index, 'blur')"
              @change="blurOrChange('machineNumberRef' + index, 'change')"
            ></a-input-number>
          </a-form-model-item>
          <span class="red pointer mt-10 relative" style="left: -12px" @click="delItem('1', index)">删除</span>
        </div>
      </a-form-model>
      <a-button type="primary" class="ml-16" icon="plus" @click="addHandle('1')">添加</a-button>
    </div>
  </a-modal>
</template>

<script>
  import { getCurrentInstance, computed, reactive, toRefs, inject, watch, ref } from 'vue'
  import { SALE_STATISTICS } from '@operation/store/modules/statistics-report/action-types'
  import { SAVE_OR_UPDATE_MACHINE } from '@operation/store/modules/exhibition/action-types'
  import brand from './brand.vue'
  import { message } from 'ant-design-vue'
  import { cloneDeep } from 'lodash'
  import { rules } from '@/operation/views/insurance/edit/constants'
  import exhibition from '@/operation/api/exhibition'
  export default {
    props: {
      showMachineNumberModal: {
        type: Boolean,
        default: false
      }
    },
    components: {
      brand,
    },
    setup (props, { emit }) {
      const root = getCurrentInstance().proxy
      const { $store, $message, $indicator, $set } = root
      const visible = computed({
        get: () => props.showMachineNumberModal,
        set: val => emit('update:showMachineNumberModal', val)
      })
      const handleDestroyModal = inject('handleDestroyModal')
      const machineNumberTitle = inject('machineNumberTitle')
      const getData = inject('getData')
      const selectRow = inject('selectRow')
      const state = reactive({
        confirmLoading: false,
        cidOptions: [],
        fixPhotoId: undefined
      })

      const exhibitNum = ref(undefined)
      const machineNumConfig = ref([])

      const handleOk = () => {
        root.$refs.editRef.validate(async valid => {
          if (valid) {
            let pass = true
            for (let i = 0; i < machineNumConfig.value.length; i++) {
              const currentItem = machineNumConfig.value[i]
              const hasBrand = machineNumConfig.value.filter((it, index) => index !== i)?.find(it => it.brandId === currentItem.brandId)
              if (hasBrand) {
                pass = false
                message.warning('请勿重复选择品牌')
                break
              }
            }
            if (pass) {
              const { id, fixPhotoId } = selectRow.value
              const params = {
                specialAreaId: id,
                fixPhotoId,
                exhibitNum: exhibitNum.value,
                brandList: machineNumConfig.value
              }
              const res = await exhibition.saveMachineNumConfig(params).catch(err => {
                root.$message.error(err.message)
              })
              if (res) {
                visible.value = false
                getData()
                message.success('保存成功')
              }
            }
          } else {
            message.warning('请先完善数据')
          }
        })
      }
      const handleCancel = async () => {
        visible.value = false
      }
      const afterClose = async () => {
        handleDestroyModal('machineNumber')
      }

      const delItem = (type, index) => {
        const props = ['brandId' + index, 'machineNumber' + index]
        root.$refs.editRef.clearValidate(props)
        machineNumConfig.value.splice(index, 1)
      }
      const addHandle = () => {
        machineNumConfig.value.push({
          brandId: undefined,
          machineNumber: undefined,
        })
      }
      const brandChange = (value, name, index) => {
        machineNumConfig.value[index].brandId = value
        machineNumConfig.value[index].brandName = name
        blurOrChange('brandIdRef' + index, 'change')
      }
      watch(() => visible.value, val => {
        if (val) {
          getMachineNumConfig()
        }
      })

      async function getMachineNumConfig () {
        const { id, fixPhotoId } = selectRow.value
        const res = await exhibition.getMachineNumConfig({ specialAreaId: id, fixPhotoId }).catch(err => {
          root.$message.error(err.message)
        })
        if (res) {
          const { brandList } = res
          exhibitNum.value = res.exhibitNum || undefined
          machineNumConfig.value = brandList?.length ? brandList : [{ brandId: undefined, machineNumber: undefined }]
        }
      }

      const specialRules = (key, index) => {
        return [{
          required: true,
          trigger: ['blur', 'change'],
          validator: (rule, value, callback) => {
            const item = machineNumConfig.value[index]
            if (!item[key] && item[key] !== 0) {
              return callback(new Error(key === 'brandId' ? '请选择品牌' : '请输入机位数'))
            }
            return callback()
          }
        }]
      }

      function blurOrChange (ref, type) {
        const el = root.$refs[ref][0]
        el && (type === 'blur' ? el.onFieldBlur() : el.onFieldChange())
      }
      return {
        ...toRefs(state),
        visible,
        machineNumConfig,
        handleOk,
        handleCancel,
        afterClose,
        delItem,
        brandChange,
        addHandle,
        machineNumberTitle,
        specialRules,
        blurOrChange,
        exhibitNum
      }
    }
  }
</script>

<style lang="scss" scoped>
.tip{
  font-weight: 400;
  font-size: 14px;
  color: #F15643;
  line-height: 17px;
}
:deep(.ant-modal-body) {
  position: relative;
  max-height: 70vh;
  min-height: 30vh !important;
  overflow-y: auto;
}
:deep(.ant-form-item) {
  width: calc((100% - 28px) / 2);
  box-sizing: border-box;
}
</style>
