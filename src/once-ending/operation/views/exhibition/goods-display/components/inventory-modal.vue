<script lang="jsx">
  import { defineComponent, computed, ref, inject, getCurrentInstance } from 'vue'
  import { Modal, Form, Select, message, Input } from 'ant-design-vue'
  import AreaDepartSelector from '@operation/components/area-depart-selector'
  import { NiAreaSelect } from '@jiuji/nine-ui'
  import {
    PHOTO_INVENTORY
  } from '@operation/store/modules/exhibition/action-types'

  export default defineComponent({
    components: {
      AreaDepartSelector,
      NiAreaSelect
    },
    props: {
      showInventoryModal: {
        type: Boolean,
        default: false
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const cupboardOptions = inject('cupboardOptions')
      const handleDestroyModal = inject('handleDestroyModal')
      const getData = inject('getData')
      const visible = computed({
        get: () => props.showInventoryModal,
        set: val => proxy.$emit('update:showInventoryModal', val)
      })

      const loading = ref(false)

      const form = ref({
        cabinetTypes: [],
        areaIds: [],
        productIds: undefined
      })

      const closeModal = function () {
        visible.value = false
      }

      const afterClose = function () {
        handleDestroyModal('inventory')
      }

      const confirmFun = async function () {
        const { areaIds, productIds } = form.value
        if (!areaIds.length) return message.error('请选择地区')
        // if (!form.value.cabinetTypes.length) return message.error('请选择柜子类型')
        // if (!form.value.productIds) return message.error('请输入商品id')
        const params = {
          ...form.value,
          productIds: productIds ? productIds.replace(/(^[ \t\n\r]+)|([ \t\n\r]+$)/g, '').replace(/，/g, ',') : ''
        }
        const res = await proxy.$store.dispatch(
          `operation/exhibition/${PHOTO_INVENTORY}`, params
        )
        if (res) {
          message.success('盘点成功')
          getData()
          visible.value = false
        }
      }

      return {
        visible,
        loading,
        closeModal,
        afterClose,
        confirmFun,
        form,
        cupboardOptions
      }
    },
    render () {
      const {
        visible,
        closeModal,
        afterClose,
        confirmFun,
        loading,
        form,
        cupboardOptions
      } = this
      return (
      <Modal
        title="盘点"
        visible={visible}
        width={600}
        onCancel={closeModal}
        afterClose={afterClose}
        onOk={confirmFun}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          label-col={{ span: 4 }}
          wrapper-col={{ span: 20 }}
        >
          <Form.Item label="地区" required>
            <NiAreaSelect
              multiple
              allowClear
              mode={1}
              placeholder="请选择门店或者搜索"
              maxTagCount={2}
              class="full-width"
              v-model={form.areaIds}
            />
          </Form.Item>
          <Form.Item label="柜子类型">
            <Select
              allowClear
              mode="multiple"
              placeholder="柜子类型"
              v-model={form.cabinetTypes}
              options={cupboardOptions || []}
              optionFilterProp="children"
            />
          </Form.Item>
          <Form.Item label="商品id">
            <Input
              allowClear
              placeholder="输入多个商品id时用英文的,隔开"
              v-model={form.productIds}
            />
          </Form.Item>
        </Form>
      </Modal>
      )
    }
  })
</script>
<style lang="scss" scoped>
//:deep(.ant-form-item-label) {
//  label {
//    &::before {
//      display: inline-block;
//      margin-right: 4px;
//      color: #f5222d;
//      font-size: 14px;
//      font-family: SimSun, sans-serif;
//      line-height: 1;
//      content: "*";
//    }
//  }
//}
</style>
