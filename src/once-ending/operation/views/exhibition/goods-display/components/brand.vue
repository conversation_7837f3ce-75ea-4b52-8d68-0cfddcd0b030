<template>
  <div class="full-width" ref="selectRef">
    <a-select
      class="full-width"
      v-bind="$attrs"
      :value="value"
      :options="selectOptions"
      :filterOption="false"
      allowClear
      showSearch
      :allowClear="true"
      placeholder="请选择品牌"
      :dropdownStyle="{zIndex: '999', width: '120%'}"
      :dropdownMatchSelectWidth="false"
      :getPopupContainer="getPopupContainer"
      :mode="multiple ? 'multiple' : ''"
      v-on="listeners"
      @focus="selectFocusOrBlur"
      @blur="selectFocusOrBlur"
      @search="selectSearch"
      @popupScroll="selectPopupScroll"
      @change="onChange"
      :maxTagCount="1"
      :maxTagPlaceholder="(omittedValues)=>`已选中${value && value.length}条`"
    >
      <div slot="notFoundContent">
        <a-empty :image="emptyImg"/>
        <a-button v-if="showNotFoundAdd" size="small" icon="plus" @click="$emit('add')">新增品牌</a-button>
      </div>
    </a-select>
  </div>
</template>

<script>
  import Vue, { inject, getCurrentInstance, ref, computed, watch } from 'vue'
  import { Empty } from 'ant-design-vue'
  import cloneDeep from 'lodash/cloneDeep'

  Vue.use(Empty)

  // 支持select的所有属性
  export default {
    name: 'Brand',
    props: {
      cateId: {
        type: [String, Number, Array],
        default: () => []
      },
      // 是否多选
      multiple: {
        type: Boolean,
        default: true
      }, // 默认选中的值
      value: {
        type: [String, Number, Array],
        default: undefined
      },
      // 是否显示添加button
      showNotFoundAdd: {
        type: Boolean,
        default: false
      },
      defalutValue: {
        type: Array | Object,
        default: () => []
      },
      machineNumConfig: {
        type: Array | Object,
        default: () => []
      },
      index: {
        type: Number,
        default: true
      },
      container: {
        type: String,
      }
    },
    data () {
      return {
        emptyImg: Empty.PRESENTED_IMAGE_SIMPLE,
        cids: []
      }
    },
    computed: {
      listeners () {
        const { change, ...listeners } = this.$listeners
        return listeners
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const selectOptions = ref([])
      const cacheOptions = ref([])
      const selectSearchValue = ref('')
      const originProductBrandOptions = inject('productBrandOptions')

      const hasBrandIds = computed(() => props.machineNumConfig?.map(it => it.brandId).filter(brandId => brandId !== props.value) || [])
      function filterFunc (arr) {
        return Array.isArray(arr) ? arr.filter(it => !hasBrandIds.value.includes(it.value)) : []
      }

      // 每次下拉框获取焦点或者失去焦点,初始化下拉框数据
      const selectFocusOrBlur = function () {
        const options = cloneDeep(originProductBrandOptions.value)
        const initOptions = options.splice(0, 50)
        if (props.value) {
          const index = options.findIndex(d => d.value === props.value)
          if (index !== -1) {
            initOptions.unshift(options.splice(index, 1)[0])
          }
        }
        selectOptions.value = filterFunc(initOptions.splice(0, 50))
        cacheOptions.value = filterFunc(cloneDeep(originProductBrandOptions.value))
      }
      watch(() => props.value, () => {
        if (props.value && !selectOptions.value.find(d => d.value === props.value)) {
          selectFocusOrBlur()
        }
      }, { immediate: true })

      const selectSearch = function (val) {
        if (!val) {
          selectFocusOrBlur()
          return
        }
        selectSearchValue.value = val
        const options = filterFunc(cloneDeep(originProductBrandOptions.value))
        selectOptions.value = options.filter(d => d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase()))
        cacheOptions.value = options.filter(d => !d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase()))
      }

      const selectPopupScroll = function (e) {
        if (!cacheOptions.value.length) {
          return
        }
        const { target } = e
        const scrollHeight = target.scrollHeight - target.scrollTop
        const clientHeight = target.clientHeight
        if (scrollHeight < clientHeight + 20) {
          const options = filterFunc(cacheOptions.value.splice(0, 50))
          selectOptions.value = uniqueData(selectOptions.value.concat(options))
        }
      }

      const uniqueData = (data) => {
        return data.filter((item, index, self) =>
          index === self.findIndex(t => t.label === item.label && t.value === item.value)
        )
      }
      function onChange (value) {
        console.log('select change', typeof value, value)
        const name = selectOptions.value?.find(it => it.value === value)?.label || undefined
        proxy.$emit('input', value, name, props.index)
        proxy.$emit('change', value, name, props.index)
      }

      const selectRef = ref()
      function getPopupContainer () {
        return selectRef.value
      }
      return {
        selectOptions,
        selectFocusOrBlur,
        selectSearch,
        selectPopupScroll,
        onChange,
        getPopupContainer,
        selectRef
      }
    }
  }
</script>

<style scoped>
:deep(.ant-select-dropdown-menu) {
  max-height: 150px;
}
</style>
