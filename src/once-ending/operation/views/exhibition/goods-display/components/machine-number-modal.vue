<script lang="jsx">
  import { defineComponent, watch, computed, ref, inject, getCurrentInstance } from 'vue'
  import { Modal, InputNumber, message, Form } from 'ant-design-vue'
  import {
    SAVE_OR_UPDATE_MACHINE
  } from '@operation/store/modules/exhibition/action-types'

  export default defineComponent({
    props: {
      showMachineNumberModal: {
        type: Boolean,
        default: false
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const handleDestroyModal = inject('handleDestroyModal')
      const getData = inject('getData')
      const selectRow = inject('selectRow')
      const visible = computed({
        get: () => props.showMachineNumberModal,
        set: val => proxy.$emit('update:showMachineNumberModal', val)
      })

      watch(() => visible.value, val => {
        if (val) formNumber.value = { ...selectRow.value }
      })

      const formNumber = ref({
        phoneMachineNumber: 0,
        tabletMachineNumber: 0,
        pcMachineNumber: 0,
      })

      const loading = ref(false)

      const closeModal = function () {
        visible.value = false
      }

      const afterClose = function () {
        handleDestroyModal('machineNumber')
      }

      const confirmFun = async function () {
        const { phoneMachineNumber, tabletMachineNumber, pcMachineNumber } = formNumber.value
        // if (!phoneMachineNumber && phoneMachineNumber !== 0) return message.error('请输入有效的手机机位数')
        // if (!tabletMachineNumber && tabletMachineNumber !== 0) return message.error('请输入有效的平板机位数')
        // if (!pcMachineNumber && pcMachineNumber !== 0) return message.error('请输入有效的电脑机位数')
        const params = {
          phoneMachineNumber,
          tabletMachineNumber,
          pcMachineNumber,
          fixPhotoId: selectRow.value.fixPhotoId
        }
        const res = await proxy.$store.dispatch(
          `operation/exhibition/${SAVE_OR_UPDATE_MACHINE}`, params
        )
        if (res) {
          message.success('保存成功')
          getData()
          visible.value = false
        }
      }

      return {
        visible,
        loading,
        closeModal,
        afterClose,
        confirmFun,
        formNumber
      }
    },
    render () {
      const {
        visible,
        closeModal,
        afterClose,
        confirmFun,
        loading,
        formNumber
      } = this
      return (
        <div class="machine-number">
          <Modal
            title="编辑机位数"
            visible={visible}
            width={600}
            onCancel={closeModal}
            afterClose={afterClose}
            okText="保存"
            onOk={confirmFun}
            confirmLoading={loading}
          >
            {/*
              <Form layout="vertical">
                <Form.Item label="手机机位数">
                  <InputNumber placeholder="请输入机位数" style="width:100%" v-model={formNumber.phoneMachineNumber} min={0} allow-clear></InputNumber>
                </Form.Item>
                <Form.Item label="平板机位数">
                  <InputNumber placeholder="请输入机位数" style="width:100%" v-model={formNumber.tabletMachineNumber} min={0} allow-clear></InputNumber>
                </Form.Item>
                <Form.Item label="电脑机位数">
                  <InputNumber placeholder="请输入机位数" style="width:100%" v-model={formNumber.pcMachineNumber} min={0} allow-clear></InputNumber>
                </Form.Item>
              </Form>
             */}
            <a-button type="primary" icon="plus" size="small">添加品类机位</a-button>
          </Modal>
        </div>
      )
    }
  })
</script>
<style lang="scss" scoped>
.machine-number {
  :deep(.ant-modal-body) {
    max-height: 800px;
    overflow-y: auto;
  }
}
</style>
