<script lang="jsx">
  import { defineComponent, ref, nextTick, getCurrentInstance } from 'vue'
  import { Empty, Modal, Timeline, Input, Button, message } from 'ant-design-vue'
  import {
    DP_FIX_PHOTO_LOG,
    ADD_DP_FIX_PHOTO_LOG,
  } from '@operation/store/modules/exhibition/action-types'

  export default defineComponent({
    setup (props) {
      const currentInstance = getCurrentInstance().proxy

      const logs = ref([])

      const visible = ref(false)

      const remakes = ref(undefined)

      const loading = ref(false)

      const images = ref([])

      const selectRow = ref({})

      const show = function (row) {
        selectRow.value = row
        visible.value = true
        dpFixPhotoLog()
      }

      const dpFixPhotoLog = async function () {
        const params = {
          id: selectRow.value.id,
        }
        const res = await currentInstance.$store.dispatch(
          `operation/exhibition/${DP_FIX_PHOTO_LOG}`,
          params
        )
        if (res) {
          const { data } = res
          logs.value = data
        }
      }

      const closeModal = function () {
        // 置空日志
        logs.value = []
        collapse.value = true
        visible.value = false
      }

      const addLog = async function () {
        if (!remakes.value) return message.warning('请输入备注')
        const { areaId, id } = selectRow.value
        const params = {
          areaId,
          fixPhotoId: id,
          remark: remakes.value,
        }
        const res = await currentInstance.$store.dispatch(
          `operation/exhibition/${ADD_DP_FIX_PHOTO_LOG}`,
          params
        )
        if (res) {
          message.success('添加成功')
          dpFixPhotoLog()
          remakes.value = undefined
        }
      }

      const viewImage = function (item) {
        images.value = item.files
        nextTick(() => {
          // 找到.images的div挂载到$viewer上
          const viewer =
            currentInstance.$el.querySelector('.images-modal').$viewer
          viewer.show()
          viewer.view(0)
        })
      }

      const collapse = ref(true)

      return {
        logs,
        visible,
        remakes,
        loading,
        images,
        selectRow,
        show,
        closeModal,
        addLog,
        viewImage,
        collapse,
      }
    },
    render () {
      const { logs, visible, addLog, viewImage, images, closeModal, collapse } =
        this
      return (
      <Modal
        title="查看日志"
        visible={visible}
        width={600}
        onCancel={closeModal}
      >
        <div
          class="images-modal"
          v-viewer={{ movable: false }}
          style="display: none"
        >
          {images.map((d) => (
            <img src={d} key={d} />
          ))}
        </div>
        <template slot="footer">
          <Button type="primary" onClick={addLog}>
            添加
          </Button>
        </template>
        <div class="logs-warp">
          {!logs?.length ? (
            <Empty />
          ) : (
            <div class="relative">
              <Timeline>
                {logs.slice(0, collapse ? 20 : 999999).map((item, index) => (
                  <Timeline.Item key={index}>
                    <span>{item.comment}</span>
                    {item.files.length ? (
                      <span
                        onClick={() => {
                          viewImage(item)
                        }}
                        class="files"
                      >
                        附件
                      </span>
                    ) : null}
                  </Timeline.Item>
                ))}
              </Timeline>
              {collapse && logs.length > 20 ? (
                <div class="show-all absolute">
                  <a
                    onClick={() => {
                      this.collapse = !this.collapse
                    }}
                    class="show-all-btn"
                  >
                    查看全部
                  </a>
                </div>
              ) : null}
            </div>
          )}
        </div>
        <div>添加备注：</div>
        <Input.TextArea
          v-model={this.remakes}
          placeholder="请输入日志内容"
          auto-size={{ minRows: 2 }}
        ></Input.TextArea>
      </Modal>
      )
    },
  })
</script>

<style lang="scss" scoped>
.logs-warp{
  max-height: 510px;
    overflow-y: auto;
    padding: 5px 0;
    margin-bottom: 10px;
}
.files {
  margin-left: 10px;
  color: #2a82e4;
  cursor: pointer;
}
.show-all {
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.6) 0%,
    #ffffff 100%
  );
  text-align: center;
  left: 0;
  right: 0;
  bottom: 1vh;
  line-height: 40px;
  padding-top: 40px;
  .show-all-btn {
    position: relative;
    font-family: "PingFangSC-Semibold, PingFang SC";
    font-weight: 600;
    color: #1890ff;
    font-size: 14px;
    &::before {
      background: #1890ff;
      position: absolute;
      content: "";
      top: calc(50% - 2px);
      height: 3px;
      width: 30px;
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      -webkit-transform-origin: 50% 100%;
      transform-origin: 50% 100%;
      left: -35px;
    }
    &::after {
      background: #1890ff;
      position: absolute;
      content: "";
      top: calc(50% - 2px);
      height: 3px;
      width: 30px;
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      -webkit-transform-origin: 50% 100%;
      transform-origin: 50% 100%;
      right: -35px;
    }
  }
}
</style>
