<template>
  <a-modal :title="title" v-model="visible" :width="800" @cancel="visible = false" @ok="submit" :confirmLoading="loading" :afterClose="afterClose">
    <a-form-model :model="fromData" ref="ruleForm">
      <a-row :gutter="16" v-for="(item, index) in fromData.localCabinetList" :key="index">
        <div  class="anchor" :id="`anchor-${index}`"></div>
        <a-col :span="12">
          <a-form-model-item label="陈列柜类型" :prop="`localCabinetList.${index}.cabinetType`" :rules="[{required: true,message: '请选择陈列柜类型',trigger: 'change'}]">
            <a-select v-model="item.cabinetType" placeholder="请选择陈列柜类型" @change="selectType(item)" :options="typeOptions"></a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="陈列柜名称" :prop="`localCabinetList.${index}.fixPhotoId`" :rules="[{required: true,message: '请选择陈列柜名称',trigger: 'change'}]">
            <a-select v-model="item.fixPhotoId" placeholder="请选择陈列柜名称" :options="nameOptions[item.oldFixPhotoId] || []"></a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </a-modal>
</template>

<script setup>
  import { cloneDeep } from 'lodash'
  import { ref, computed, defineProps, defineEmits, watch } from 'vue'
  import api from '@operation/api/exhibition'
  import { message } from 'ant-design-vue'
  const props = defineProps({
    value: {
      type: Boolean,
      default: false
    },
    cabinetList: {
      type: Array,
      default: () => ([])
    },
    id: {
      type: [String, Number],
      default: undefined
    },
    name: {
      type: String,
      default: ''
    },
    areaId: {
      type: [String, Number],
      default: undefined
    }
  })

  const emits = defineEmits(['input', 'afterClose', 'success'])

  const loading = ref(false)

  const visible = computed({
    get () {
      return props.value
    },
    set (val) {
      emits('input', val)
    }
  })

  const title = computed(() => `更换【${props.name}】陈列柜`)

  const fromData = ref({
    localCabinetList: []
  })

  watch(() => visible.value, async val => {
    if (val) {
      await fetchDpFixType()
      const o = {}
      fromData.value.localCabinetList = cloneDeep(props.cabinetList).map((d) => {
        o[d.oldFixPhotoId] = []
        fetchDpFixName(d)
        return d
      })
      nameOptions.value = o
    }
  })

  const afterClose = function () {
    emits('afterClose')
  }

  const errHandle = function (err) {
    message.error(err.message)
  }

  const ruleForm = ref(null)
  const submit = async function () {
    ruleForm.value.validate(async (valid, object) => {
      if (valid) {
        const params = {
          specialAreaId: props.id,
          areaId: props.areaId,
          fixPhotoChangeList: fromData.value.localCabinetList.map(d => ({
            oldFixPhotoId: d.oldFixPhotoId,
            newFixPhotoId: d.fixPhotoId
          })),
        }
        const res = await api.batchChangeDpFixPhotoFixPhoto(params).catch(errHandle)
        if (res) {
          message.success('保存成功')
          visible.value = false
          emits('success')
        }
      } else {
        const errorFields = Object.keys(object)
        let target = ''
        let top = ''
        errorFields.map(d => {
          const list = d.split('.')
          const ref = `#anchor-${list[1]}`
          const targetTop = document.querySelector(ref).getBoundingClientRect().top
          // top越小,越靠近顶部
          if (top === '' || targetTop < top) {
            top = targetTop
            target = ref
          }
        })
        if (target) {
          document.querySelector(target).scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })
        }

        return false
      }
    })
  }

  const typeOptions = ref([])
  const fetchDpFixType = async () => {
    const params = {
      areaId: props.areaId
    }
    const res = await api.getDpFixType(params).catch(errHandle)
    if (res) {
      typeOptions.value = res.map(d => ({ label: d.name, value: d.id }))
    }
  }

  const selectItem = ref({})
  const selectType = function (item) {
    item.fixPhotoId = undefined
    selectItem.value = item
    fetchDpFixName(selectItem.value)
  }

  const nameOptions = ref({})
  const fetchDpFixName = async (item) => {
    const params = {
      areaId: props.areaId,
      dpFixPhotoType: item.cabinetType,
    }
    const res = await api.getDpFixName(params).catch(errHandle)
    if (res) {
      nameOptions.value[item.oldFixPhotoId] = res.map(d => ({ label: d.name, value: d.id }))
    }
  }

</script>

<style scoped lang="scss">

</style>
