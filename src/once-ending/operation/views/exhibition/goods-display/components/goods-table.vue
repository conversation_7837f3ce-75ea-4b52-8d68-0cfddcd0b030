<script lang="jsx">
  import { defineComponent, computed, ref, inject, getCurrentInstance, reactive, nextTick } from 'vue'
  import {
    Checkbox,
    Popover,
    Input,
    Button,
    Tooltip,
    Icon,
    message
  } from 'ant-design-vue'
  import BtnRouter from '@operation/components/btn-router'
  import { statusMap, goodsColumns, manegeRules, statusStyle } from '../constants'
  import uuidv4 from 'uuid/v4'
  import {
    FIELD_NOTE_GET,
    DELETE_FIX_EDIT,
    DELETE_SPECIAL_AREA,
    FIELD_NOTE_EDIT,
    UPLOAD_SMALL_PRODUCT
  } from '@operation/store/modules/exhibition/action-types'
  import { NiTable, NiPrice } from '@jiuji/nine-ui'
  import axios from 'axios'
  import exhibition from '@operation/api/exhibition'
  import moment from 'moment'
  import qrcode from '~/components/qrcode'
  import { cloneDeep } from 'lodash'
  import { saveAs } from 'file-saver'
  import ChangeSpecialZoneCabinetModal from './change-special-zone-cabinet-modal'

  export default defineComponent({
    components: {
      BtnRouter,
      NiTable,
      NiPrice,
      qrcode,
    },
    data () {
      return {
        goodsColumns,
        customRenderMap: new Map([
          [
            'area',
            (value, row, index) => {
              return {
                children: row.showTotal ? <span>合计</span> : <div>
                  <p>{ row.area }</p>
                  <p style="color: #1890ff;cursor: pointer;"
                    class="mt-8"
                    onClick={() => {
                      this.toImgPre(value, row)
                    }}>工程图</p>
                  <p style="color: #1890ff;cursor: pointer;"
                    class="mt-8"
                    onClick={() => {
                      this.toScheme(row)
                    }}>展陈方案</p>
                </div>,
                attrs: {
                  rowSpan: row.showArea ? row.areaLen : row.showTotal ? 1 : 0,
                  colSpan: row.showTotal ? 2 : 1
                }
              }
            }
          ],
          [
            'name',
            (text, record) => {
              return {
                children: text && text.length ? <div class="expand-box">
                    <div class="expand-icon">{record.children?.length ? <a-icon onClick={() => this.changeExpand(record)} type={this.expandedRowKeys.includes(record.id) ? 'minus-square' : 'plus-square'}/> : null }</div>
                    <div class="flex flex-wrap">{text}{ record.dpType === 2 && record.label ? <span class="label-text">（<span>{record.label}</span>）</span> : '' }</div>
                </div> : <span>--</span>,
                attrs: {
                  colSpan: record.showTotal ? 0 : 1
                }
              }
            }
          ],
          [
            'cabinetTypeNamea',
            (text, record) => {
              return {
                children: <span>{ text || '--' }</span>,
                attrs: {
                  colSpan: record.showTotal ? 0 : 1
                }
              }
            }
          ],
          [
            'productTotalNum',
            (text, record) => {
              return {
                children: (text || text === 0) ? <router-link to={`/store/exhibition/product-detail?form=${JSON.stringify(this.toDetail(record))}`}>{(text || text === 0) ? text : '--'}</router-link> : '--',
              }
            }
          ],
          [
            'toBeProductTotalNum',
            (text, record) => {
              return {
                children: (text || text === 0) ? <router-link to={`/store/exhibition/stay-display-product?form=${JSON.stringify(this.toDetail(record))}`}>{(text || text === 0) ? text : '--'}</router-link> : '--',
              }
            }
          ],
          [
            'specialAreaDpImages',
            (text, record) => {
              return {
                children: !text?.length || record.dpType !== 2 ? (
                  '--'
                ) : (
                <span
                  style="color: #1890ff;cursor: pointer;"
                  onClick={() => {
                    this.viewImage(text)
                  }}
                >
                  查看
                </span>
                )
              }
            }
          ],
          ['textCustomRender', text => (text || text === 0) ? text : '--'],
          ['specialAreaCharge', (text, record) => (text && record.dpType === 2) ? text : '--'],
          [
            'specialAreaDpImageStatus',
            (text, record) => {
              return {
                children: record.dpType === 2 && record.specialAreaDpImageStatusName ? <div class="status" style={statusStyle.get(text)}>{record.specialAreaDpImageStatusName}</div> : '--',
              }
            }
          ],
          [
            'tableAction',
            (text, record) => {
              const { dpType } = record
              const { showDimension } = this.searchForm
              return {
                children: record.id && !record.showTotal ? (
                    <span>
                      { dpType === 1 ? <span>
                        <a onClick={() => this.toManage(record)} class="actions">陈列柜管理</a>
                        <a-popover placement="bottom" trigger="click">
                          <div slot="content">
                            <qrcode style="width: 120px;height: 120px" value={`${this.$tnt.moaHost}/new/#/operation/store/goods-display/detail?title=${record.name} ${record.levelName}&areaId=${record.areaId}&fixPhotoId=${record.id}`}/>
                            <p class="text-center">{record.name} <span class="ml-16">{record.levelName}</span></p>
                            <div class="flex flex-justify-center">
                              <a-button loading={record.downloadQrcodeLoading} type="primary" size="small" onClick={() => this.downloadQrcode(record)}>下载二维码</a-button>
                            </div>
                          </div>
                          <a class="actions">二维码</a>
                        </a-popover>
                      </span> : null }
                      {this.hasjwgl && dpType === 2 && showDimension === 1 ? (
                        <a
                          onClick={() => this.machineNumberEdit(record)}
                          style="margin-right: 8px;display: inline-block;"
                        >
                          机位管理
                        </a>
                      ) : null}
                      {
                        (dpType === 2 && showDimension === 1) || showDimension === 2 ? <a
                          onClick={() => this.handleChangeSpecialZoneCabinet(record)}
                          style="margin-right: 8px;display: inline-block;"
                        >
                          更换陈列柜
                        </a> : null
                      }
                      {this.hasjwgl ? (
                        <a class="actions" onClick={() => {
                          this.deleteData(record)
                        }}>
                          删除
                        </a>
                      ) : null}
                      { dpType === 1 ? <a onClick={() => { this.checkLog(record) }}
                                           class="actions">日志</a> : null }
                    </span>
                  ) : (
                <span></span>
                ),
                // attrs
              }
            }
          ],
          [
            'textPrice',
            (text, record) => <NiPrice
              decimalFontSize={ 14 }
              value={ text }
              color="rgba(0, 0, 0, 0.65)"
              prefixColor="rgba(0, 0, 0, 0.65)"
              decimalColor="rgba(0, 0, 0, 0.65)"
              thousand={ true }/>
          ],
          [
            'cabinetLabelStr',
            (text, record) => text && text.length > 8 ? <Tooltip title={text}>
                <span>{`${text.slice(0, 8)}...`}</span>
              </Tooltip> : <span>{text || '-'}</span>
          ],
          [
            'ringRatio',
            (d) => (text, record) => {
              const key = `${d.dataIndex}RingRatio`
              return text || text === 0 ? <Tooltip title={`环比：${record[key]}`}>
                <span>{text}</span>
              </Tooltip> : <span>--</span>
            }
          ],
          [
            'priceRingRatio',
            (d) => (text, record) => {
              const key = `${d.dataIndex}RingRatio`
              return <Tooltip title={`环比：${record[key]}`}>
                <NiPrice
              decimalFontSize={ 14 }
              value={ text }
              color="rgba(0, 0, 0, 0.65)"
              prefixColor="rgba(0, 0, 0, 0.65)"
              decimalColor="rgba(0, 0, 0, 0.65)"
              thousand={ true }/>
              </Tooltip>
            }
          ]
        ]),
        zsTitle: d => {
          return () => (
          <span>
            {d.titleStr}
            {this.hasBbzs ? (
              <Popover overlay-class-name="title-popover">
                <template slot="content">
                  {!d.isEdit && (
                    <p style="color:#333333;font-size:14px">
                      {d.note || '未添加注释'}
                    </p>
                  )}
                  {d.isEdit && (
                    <Input.TextArea
                      autoSize
                      style="border-radius: 2px;"
                      value={d.note}
                      onInput={e => {
                        d.note = e.target.value
                      }}
                      placeholder="请输入注释"
                      allowClear
                    />
                  )}
                  {d.isEdit && (
                    <div style="text-align: right;margin-top: 13px;">
                      <Button size="small" onClick={() => this.cancelNote(d)}>
                        取消
                      </Button>
                      <Button
                        size="small"
                        class="ml-8"
                        type="primary"
                        onClick={() => this.editNote(d)}
                      >
                        确定
                      </Button>
                    </div>
                  )}
                  {!d.isEdit && (
                    <div style="text-align: right;margin-top: 13px;">
                      <Button
                        size="small"
                        type="primary"
                        onClick={() => {
                          d.isEdit = true
                        }}
                      >
                        编辑
                      </Button>
                    </div>
                  )}
                </template>
                <span>
                  <Icon
                    style="color:rgba(0, 0, 0, 0.85);margin-left: 8px;"
                    type="question-circle"
                  />
                </span>
              </Popover>
            ) : d.note ? (
              <Tooltip title={d.note}>
                <span>
                  <Icon
                    style="color:rgba(0, 0, 0, 0.85);margin-left: 8px;"
                    type="question-circle"
                  />
                </span>
              </Tooltip>
            ) : null}
          </span>
          )
        }
      }
    },
    computed: {
      columns () {
        const { showDimension } = this.searchForm
        const columnsData = this.goodsColumns
        columnsData.forEach(d => {
          d.align = 'center'
          if (this.customRenderMap.get(d.dataIndex)) {
            d.customRender = this.customRenderMap.get(d.dataIndex)
          }
          if (d.textCustomRender) { d.customRender = this.customRenderMap.get('textCustomRender') }
          if (d.textPrice) { d.customRender = this.customRenderMap.get('textPrice') }
          if (d.ringRatio) {
            d.customRender = this.customRenderMap.get('ringRatio')(d)
          }
          if (d.priceRingRatio) {
            d.customRender = this.customRenderMap.get('priceRingRatio')(d)
          }
          if (d.dataIndex !== 'tableAction') {
            d.title = this.zsTitle(d)
          }
          if (d.dataIndex === 'tableAction') {
            d.width = showDimension === 1 ? 300 : 100
          }
          d.forceHide = d.showDimension && d.showDimension !== showDimension
        })
        return columnsData
      },
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const loading = inject('loading')
      const dataSource = inject('dataSource')
      const pagination = inject('pagination')
      const oAuthManage = inject('oAuthManage')
      const hasjwgl = inject('hasjwgl')
      const viewImage = inject('viewImage')
      const checkLog = inject('checkLog')
      const handleTableChange = inject('handleTableChange')
      const productEdit = inject('productEdit')
      const getData = inject('getData')
      const machineNumberEdit = inject('machineNumberEdit')
      const queryParams = inject('queryParams')
      const batchDel = inject('batchDel')
      const inventory = inject('inventory')
      const productTotalCount = inject('productTotalCount')
      const statistics = inject('statistics')
      const modalVisible = inject('modalVisible')
      const expandedRowKeys = inject('expandedRowKeys')
      const searchForm = inject('searchForm')
      const photoLevelOptions = inject('photoLevelOptions')
      const specialAreaOptions = inject('specialAreaOptions')
      const modalData = ref([])

      function changeExpand (record) {
        const { rowKey, areaId } = record
        const needRecord = dataSource.value.find(d => d.areaId === areaId && d.showArea)
        if (expandedRowKeys.value.includes(rowKey)) {
          expandedRowKeys.value = expandedRowKeys.value.filter(k => k !== rowKey)
          needRecord.areaLen -= (record.children?.length || 0)
        } else {
          expandedRowKeys.value.push(rowKey)
          needRecord.areaLen += (record.children?.length || 0)
        }
      }

      const hasBbzs = computed(() => proxy.$store.state.userInfo.Rank?.includes('bbzs'))

      const hasHpcl = computed(() => proxy.$store.state.userInfo.Rank?.includes('hpcl'))

      const checkAll = ref(false)
      const indeterminate = ref(false)

      const columnsData = ref(goodsColumns)

      const cancelNote = function (item) {
        item.note = item.oldNote
        item.isEdit = false
      }

      const fieldNoteGet = async function () {
        const params = {
          type: 100
        }
        const res = await proxy.$store.dispatch(
          `operation/exhibition/${FIELD_NOTE_GET}`, params
        )
        if (res) {
          const { data } = res
          for (const key in data) {
            const item = columnsData.value.find(k => k.dataIndex === key)
            if (item) {
              item.note = data[key]
              item.oldNote = data[key]
            }
          }
        }
      }
      fieldNoteGet()

      const editNote = async function (item) {
        const { dataIndex: field, note } = item
        const params = {
          type: 100,
          field,
          note
        }
        const res = await proxy.$store.dispatch(
          `operation/exhibition/${FIELD_NOTE_EDIT}`, params
        )
        if (res) {
          item.isEdit = false
          fieldNoteGet()
        }
      }

      // 删除数据
      const deleteData = async function (record) {
        const { dpType } = record
        if (record.productTotalNum > 0) {
          return message.info((dpType === 1 ? '陈列柜' : '专区') + '已绑定商品，不能删除')
        }
        proxy.$confirm({
          title: dpType === 1 ? '删除后，此陈列柜下的所有专区都会被删除，确认删除陈列柜吗？' : '确定删除此专区吗？',
          onOk: async () => {
            const params = dpType === 1 ? {
              photoId: record.id
            } : {
              areaId: record.areaId,
              fixPhotoId: record.fixPhotoId,
              specialAreaId: record.id
            }
            const apiKey = dpType === 1 ? DELETE_FIX_EDIT : DELETE_SPECIAL_AREA
            const res = await proxy.$store.dispatch(
              `operation/exhibition/${apiKey}`, params
            )
            if (res) {
              message.success('删除成功')
              getData()
            }
          }
        })
      }

      const checkedChange = function (e, row) {
        row.checked = e.target.checked
        indeterminate.value =
          !!dataSource.value.filter(d => d.checked).length &&
          dataSource.value.filter(d => d.checked).length <
          dataSource.value.length
        checkAll.value =
          dataSource.value.filter(d => d.checked).length ===
          dataSource.value.length
      }

      const checkedAllChange = function (e) {
        checkAll.value = e.target.checked
        dataSource.value.map(d => {
          if (d.fixPhotoId) d.checked = e.target.checked
        })
        indeterminate.value = false
      }

      const toImgPre = function (url, record) {
        if (!url) {
          return message.info('门店未上传工程图')
        }
        const routeData = proxy.$router.resolve({
          path: `/store/exhibition?areaId=${record.areaId}`
        })
        window.open(routeData.href)
      }

      const downloadLoading = ref(false)
      const downloadSlotLoading = ref(false)

      const exportsParams = computed(() => {
        return {
          ...queryParams.value,
          size: pagination.value.total,
          current: 1
        }
      })

      const download = function () {
        const cacheParams = { ...exportsParams.value }
        delete cacheParams.tag
        delete cacheParams.keyWord
        downloadLoading.value = true
        axios({
          method: 'post',
          url: exhibition.exportExcel(),
          data: cacheParams,
          responseType: 'blob',
          timeout: 60000,
          headers: {
            Authorization: proxy.$store.state.token
          }
        }).then((res) => {
          const link = document.createElement('a')
          let blob = new Blob([res.data], { type: 'application/x-excel' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = `${proxy.$tnt.xtenant < 1000 ? '门店展陈' : '货品陈列管理'}(${moment().format('YYYY-MM-DD HH：mm')}).xlsx`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        }).finally(() => {
          downloadLoading.value = false
        })
      }

      const downloadSlot = function () {
        const cacheParams = { ...exportsParams.value }
        delete cacheParams.tag
        delete cacheParams.keyWord
        downloadSlotLoading.value = true
        axios({
          method: 'post',
          url: exhibition.exportExcelSlot(),
          data: cacheParams,
          responseType: 'blob',
          timeout: 60000,
          headers: {
            Authorization: proxy.$store.state.token
          }
        }).then((res) => {
          const link = document.createElement('a')
          let blob = new Blob([res.data], { type: 'application/x-excel' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = `样机机位明细(${moment().format('YYYY-MM-DD HH：mm')}).xlsx`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        }).finally(() => {
          downloadSlotLoading.value = false
        })
      }

      const uploadFiles = function () {
        proxy.$refs.uploadFile.click()
      }
      const loadingFiles = ref(false)
      const changeFile = async function (e) {
        let file = e.target.files[0]
        e.target.value = ''
        if (file) {
          let form = new window.FormData()
          form.append('file', file)
          loadingFiles.value = true
          const res = await proxy.$store.dispatch(`operation/exhibition/${UPLOAD_SMALL_PRODUCT}`, form)
          loadingFiles.value = false
          if (res) {
            message.success('导入成功')
          }
        }
      }
      const toDetail = function (record) {
        const { areaId, cabinetType, name = '', id, dpType } = record
        const {
          playStartTime,
          playEndTime,
          tag,
          keyWord,
          specialAreaIds,
          photoLevels,
          stockFilter,
          stockFilterValue,
          cabinetTypes,
          productName,
          pid
        } = queryParams.value
        const form = {
          areaIds: areaId ? [areaId] : [],
          cabinetName: name,
          tag,
          keyWord,
          photoLevels,
          stockFilter,
          stockFilterValue,
          cabinetTypes,
          specialAreaIds
        }
        if (dpType === 1) {
          form.cabinetName = name
        } else if (dpType === 2) {
          form.cabinetName = record.parentCabinetName
          form.specialAreaIds = [id]
        } else {
          form.areaIds = dataSource.value[0]?.areaId || []
          delete form.cabinetName
        }
        if (tag === 'productName' && productName && pid) {
          form.productName = productName
        }
        if (playStartTime && playEndTime) {
          form.playStartTime = playStartTime
          form.playEndTime = playEndTime
        }
        return form
      }

      const manage = reactive({
        show: false,
        title: '',
        loading: false,
        form: {
          level: undefined,
          specialAreaIds: undefined,
        }
      })
      async function toManage (record) {
        manage.title = `【${record.area} ${record.name}设置】`
        manage.show = true
        const res = await exhibition.getCabinetInfo({ fixPhotoId: record.id }).catch(err => {
          proxy.$message.error(err.message)
        })
        if (res) {
          Object.assign(manage.form, res)
        }
      }
      async function saveManage () {
        proxy.$refs.manageRef.validate(async valid => {
          if (valid) {
            manage.loading = true
            const res = await exhibition.saveCabinetInfo(manage.form).catch(err => {
              proxy.$message.error(err.message)
            })
            manage.loading = false
            if (res) {
              manage.show = false
              message.success('保存成功')
              getData()
            }
          } else {
            message.warning('请先完善数据')
          }
        })
      }
      const scheme = reactive({
        show: false,
        title: '',
        displayProgram: undefined
      })
      function toScheme (record) {
        scheme.title = `【${record.area}】展陈方案`
        scheme.displayProgram = record.displayProgram
        scheme.show = true
      }

      const downloadTemplate = function () {
        window.location.href = 'https://img.9xun.com/newstatic/18407/05ba3166828c5116.xlsx'
      }
      const cancelHandle = function () {
        modalVisible.value = false
      }

      const openHandle = function (flag, row) {
        console.log(row)
        if (flag === '1') {
          modalData.value = row.machineNumConfigObj || []
        } else {
          modalData.value = row.machineNumConfig || []
        }
        modalVisible.value = true
      }

      async function downloadQrcode (record) {
        proxy.$set(record, 'downloadQrcodeLoading', true)
        const res = await exhibition.downloadQrcode({ fixPhotoId: record.id }).catch(err => {
          proxy.$message.error(err.message)
        })
        proxy.$set(record, 'downloadQrcodeLoading', false)
        if (res) {
          saveAs(res, record.name + '二维码.png')
        }
      }

      const showChangeSpecialZoneCabinet = ref(false)
      const destroyedPopup = ref(false)
      const selectItem = ref({})
      const cabinetList = computed(() => {
        const { showDimension } = searchForm.value
        const { dpPhotoList = [], cabinetType, fixPhotoId } = selectItem.value
        return showDimension === 2 ? dpPhotoList.map(d => ({
          cabinetType: Number(d.cabinetType),
          oldFixPhotoId: Number(d.id),
          fixPhotoId: Number(d.id)
        })) : [{
          cabinetType: Number(cabinetType),
          oldFixPhotoId: Number(fixPhotoId),
          fixPhotoId: Number(fixPhotoId)
        }]
      })
      const handleChangeSpecialZoneCabinet = function (item) {
        selectItem.value = item
        showChangeSpecialZoneCabinet.value = true
      }

      const closedChangeSpecialZoneCabinet = function () {
        destroyedPopup.value = true
        nextTick(() => {
          destroyedPopup.value = false
        })
      }

      return {
        hasBbzs,
        checkAll,
        indeterminate,
        productEdit,
        cancelNote,
        editNote,
        viewImage,
        deleteData,
        checkedChange,
        checkedAllChange,
        toImgPre,
        machineNumberEdit,
        checkLog,
        columnsData,
        loading,
        dataSource,
        pagination,
        oAuthManage,
        hasjwgl,
        handleTableChange,
        batchDel,
        inventory,
        download,
        downloadLoading,
        downloadSlot,
        downloadSlotLoading,
        productTotalCount,
        statistics,
        uploadFiles,
        changeFile,
        toDetail,
        downloadTemplate,
        loadingFiles,
        toManage,
        hasHpcl,
        modalVisible,
        cancelHandle,
        openHandle,
        modalData,
        expandedRowKeys,
        changeExpand,
        searchForm,
        manage,
        saveManage,
        scheme,
        toScheme,
        photoLevelOptions,
        specialAreaOptions,
        downloadQrcode,
        selectItem,
        showChangeSpecialZoneCabinet,
        destroyedPopup,
        handleChangeSpecialZoneCabinet,
        closedChangeSpecialZoneCabinet,
        cabinetList,
        getData
      }
    },
    render () {
      const {
        columns,
        statistics,
        dataSource,
        loading,
        pagination,
        handleTableChange,
        download,
        downloadLoading,
        downloadSlot,
        downloadSlotLoading,
        oAuthManage,
        hasjwgl,
        inventory,
        batchDel,
        productTotalCount,
        uploadFiles,
        changeFile,
        downloadTemplate,
        loadingFiles,
        modalVisible,
        cancelHandle,
        modalData,
        expandedRowKeys,
        changeExpand,
        manage,
        saveManage,
        scheme,
        photoLevelOptions,
        specialAreaOptions,
        selectItem,
        destroyedPopup,
        closedChangeSpecialZoneCabinet,
        cabinetList,
        searchForm,
        getData
      } = this
      const { showDimension } = searchForm
      return (<div>
      <div style="margin-top:10px">
        <NiTable
          bordered
          align={'center'}
          rowKey={(r) => r.rowKey}
          columns={columns}
          dataSource={dataSource}
          pagination={{ ...pagination }}
          loading={loading}
          onChange={handleTableChange}
          footerTotalNum={1}
          expandedRowKeys={expandedRowKeys}
        >
        <div slot="tool">
        <div class="flex flex-align-center flex-justify-end">
          <a-button class="mr-8" type="primary" loading={downloadLoading} onClick={download}>导出</a-button>
        </div>
      </div>
        </NiTable>
        <input onChange={changeFile} accept=".xlsx,.xls" type="file" ref="uploadFile" style="display: none"/>
      </div>
        <a-modal
          v-model={ manage.show }
          title={ manage.title }
          destroyOnClose={true}
          confirmLoading={manage.loading}
          onOk={saveManage}
          width="500px">
          <a-form-model
            { ...{ props: { model: manage.form } } }
            rules={manegeRules}
            labelCol={{ span: 5 }}
            ref="manageRef"
            wrapperCol={{ span: 19 }}>
            <a-form-model-item label="陈列柜等级" prop="level">
              <a-select options={photoLevelOptions} v-model={manage.form.level} placeholder="请选择" allowClear/>
            </a-form-model-item>
            <a-form-model-item label="专区" prop="specialAreaIds">
              <a-select mode="multiple"
                         maxTagCount={1}
                         option-filter-prop="children"
                         options={specialAreaOptions}
                         v-model={manage.form.specialAreaIds}
                         placeholder="请选择"
                         allowClear/>
            </a-form-model-item>
          </a-form-model>
        </a-modal>
        <a-modal
          v-model={ scheme.show }
          title={ scheme.title }
          destroyOnClose={true}
          footer={null}
          width="520px">
          <p>展陈方案：</p>
          <div class="scheme-box">{scheme.displayProgram || '暂无数据'}</div>
        </a-modal>
        {
          !destroyedPopup ? <ChangeSpecialZoneCabinetModal cabinetList={cabinetList} id={selectItem.id} name={selectItem.name} areaId={selectItem.areaId} onSuccess={() => { getData() }} v-model={this.showChangeSpecialZoneCabinet} onClosed={closedChangeSpecialZoneCabinet}/> : null
        }
      </div>)
    }
  })
</script>

<style lang="scss" scoped>
.ant-btn-link {
  padding-left: 0;
  padding-right: 8px;
}
.img-wrapper {
  position: relative;
  float: left;

  .chacha {
    position: absolute;
    right: 0;
    top: 0;
  }
}
:deep(.reset-padding) {
  margin: -12px;
}
:deep(.imgPre) {
  object-fit: cover;
  height: 200px;
  margin: 0 auto;
  width: 100%;
}
:deep(.table-img) {
  object-fit: cover;
  width: 70px;
  height: 70px;
  padding: 5px;
}
:deep(.text-wrap) {
  display: flex;
  overflow: hidden;

  .text {
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: justify;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    position: relative;

    &::before {
      content: "";
      float: right;
      width: 0;
      height: 100%;
      margin-bottom: -21px;
    }

    .btn-showAll {
      float: right;
      clear: both;
      line-height: 21px;
      cursor: pointer;
      border: 0;
      color: #31a6ff;
      background: none;
    }
  }
}
:deep(.ant-table-thead > tr > th .ant-table-header-column) {
  font-weight: 600;
}
:deep(.ant-table-body-inner, .ant-table-body) {
  &::-webkit-scrollbar-track-piece,
  &::-webkit-scrollbar-thumb {
    visibility: hidden;
  }
}
:deep(.wrapper:hover) {
  .ant-table-body-inner,
  .ant-table-body {
    &::-webkit-scrollbar-track-piece,
    &::-webkit-scrollbar-thumb {
      visibility: visible;
    }
  }
}
:deep(.ant-table-body, .ant-table-tbody) {
  td.line-bottom {
    border-bottom: 1px solid #e8e8e8;
  }
}
:deep(.nine-table-footer) {
  display: block;
}
:deep(.ant-pagination) {
  margin-top: 8px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.link {
  color: #1890ff;
  cursor: pointer;
}
.mr-12 {
  margin-right: 12px;
}
:deep(.ant-table-tbody > tr > td) {
  padding: 12px 8px !important;
}

.border-top {
  border-top: 1px solid #5c5d5e;
}

.border-right {
  border-right: 1px solid #5c5d5e;
}

.border-left{
  border-left: 1px solid #5c5d5e;
}

.border-bottom {
  border-bottom: 1px solid #5c5d5e;
}

.width-short {
  width: 120px;
}

.pt-5 {
  padding-top: 5px;
}

.pb-5 {
  padding-bottom: 5px;
}

.border-bottom {
  border: 1px solid #5c5d5e;
}

:deep(.ant-table-row-expand-icon) {
  display: none;
}
:deep(.expand-box) {
  display: flex;
  width: 100%;
  box-sizing: border-box;
  line-height: 16px;
  padding: 0 12px;
  .expand-icon {
    font-size: 16px;
    width: 16px;
    margin-right: 6px;
    cursor: pointer;
    &:hover {
      color: #1890ff;
    }
  }
}
:deep(.ant-table-column-sorter-inner) {
  margin: -7px 0 0 6px !important;
}
:deep(.actions) {
  margin-right: 8px;
}
.scheme-box {
  background: #f5f5f5;
  padding: 10px 12px;
  border-radius: 4px;
  margin-top: 4px;
  min-height: 56px;
}
:deep(.status) {
  height: 20px;
  border-radius: 2px;
  font-size: 12px;
  line-height: 12px;
  padding: 4px;
  box-sizing: border-box;
  display: inline-block;
}
:deep(.ant-table-fixed-left .ant-table-body-inner) {
  margin-right: 0 !important;
}
:deep(.label-text) {
  white-space: nowrap;
  span {
    color: red;
  }
}
</style>
