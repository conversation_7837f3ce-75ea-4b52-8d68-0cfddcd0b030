<script lang="jsx">
  import Vue, {
    defineComponent,
    ref,
    computed,
    nextTick,
    getCurrentInstance,
    provide
    } from 'vue'
  import 'viewerjs/dist/viewer.css'
  import Viewer from 'v-viewer'
  import BtnRouter from '@operation/components/btn-router'
  import GoodsScreen from './components/goods-screen.vue'
  import GoodsTable from './components/goods-table.vue'
  import { NiListPage } from '@jiuji/nine-ui'
  import MachineNumberModalNew from './components/machine-number-modal-new.vue'
  import ProductEditModal from './components/product-edit-modal.vue'
  import InventoryModal from './components/inventory-modal.vue'
  import ModalLogs from './components/modal-logs.vue'
  import { message } from 'ant-design-vue'
  import moment from 'moment'
  import { originSearchForm } from './constants'
  import {
    GET_BY_AREA_IDS,
    BATCH_DELETE,
    GET_DP_FIX_PHOTO_ENUMS,
  } from '@operation/store/modules/exhibition/action-types'
  import { cloneDeep } from 'lodash'
  Vue.use(Viewer)

  export default defineComponent({
    components: {
      BtnRouter,
      GoodsScreen,
      GoodsTable,
      NiListPage,
      ProductEditModal,
      ModalLogs,
      InventoryModal,
    },
    setup (_) {
      const { proxy } = getCurrentInstance()
      const oAuthManage = computed(() =>
        proxy.$store.state.userInfo.Rank?.includes('hpcl')
      )
      const hasjwgl = computed(() =>
        proxy.$store.state.userInfo.Rank?.includes('jwgl')
      )
      const userAreaId = computed(() => proxy.$store.state.userInfo.areaid)
      const canWatchAll = computed(() => oAuthManage.value || userAreaId.value === 22)
      provide('canWatchAll', canWatchAll)

      provide('oAuthManage', oAuthManage)
      provide('hasjwgl', hasjwgl)

      const productTotalCount = ref(0)
      provide('productTotalCount', productTotalCount)

      const pagination = ref({
        current: 1,
        pageSize: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: false,
      })
      provide('pagination', pagination)
      const statistics = ref([])
      provide('statistics', statistics)
      const modalVisible = ref(false)
      provide('modalVisible', modalVisible)

      // 所查询到的数据控制：除管理员与HQ人员外,根据人员所属门店，查询数据
      if (!oAuthManage.value && userAreaId.value !== 22) {
        originSearchForm.areaIds = [userAreaId.value + '']
      }

      const searchForm = ref(cloneDeep(originSearchForm))
      provide('searchForm', searchForm)

      function changeShowDimension () {
        pagination.value.current = 1
        reset()
      }
      provide('changeShowDimension', changeShowDimension)
      function reset () {
        const { showDimension } = searchForm.value
        searchForm.value = {
          ...cloneDeep(originSearchForm),
          showDimension
        }
        getData(1)
      }
      provide('reset', reset)

      const { areaid, productFlag } = proxy.$route.query
      if (areaid) {
        searchForm.value.areaIds = [areaid]
      }
      if (productFlag) {
        searchForm.value.productFlag = Number(productFlag)
      }

      const cupboardOptions = ref([])
      provide('cupboardOptions', cupboardOptions)

      const displayLevelOptions = ref([])
      const productBrandOptions = ref([])
      const specialAreaOptions = ref([])
      const showDimensionOptions = ref([])
      const photoLevelOptions = ref([])
      const stockFilterOptions = ref([])
      const auditStatusOptions = ref([])
      const toBeDpNumFilter = ref([])
      provide('displayLevelOptions', displayLevelOptions)
      provide('productBrandOptions', productBrandOptions)
      provide('specialAreaOptions', specialAreaOptions)
      provide('showDimensionOptions', showDimensionOptions)
      provide('photoLevelOptions', photoLevelOptions)
      provide('stockFilterOptions', stockFilterOptions)
      provide('auditStatusOptions', auditStatusOptions)
      provide('toBeDpNumFilter', toBeDpNumFilter)

      const getEnums = async function () {
        const res = await proxy.$store.dispatch(
          `operation/exhibition/${GET_DP_FIX_PHOTO_ENUMS}`
        )
        if (res) {
          cupboardOptions.value = res.data.FixPhotoCabinetTypeEnum
          displayLevelOptions.value = res.data.DisplayLevelEnum
          specialAreaOptions.value = res.data.specialArea
          showDimensionOptions.value = res.data.showDimension
          photoLevelOptions.value = res.data.photoLevel
          stockFilterOptions.value = res.data.stockFilter
          auditStatusOptions.value = res.data.auditStatus
          toBeDpNumFilter.value = res.data.toBeDpNumFilter
          productBrandOptions.value = res.data.productBrand?.map(it => ({
            label: it.name,
            value: it.value,
          })) || []
        }
      }
      getEnums()

      const showMachineNumberModal = ref(false)
      provide('showMachineNumberModal', showMachineNumberModal)

      const showProductEditModal = ref(false)
      provide('showProductEditModal', showProductEditModal)

      const showInventoryModal = ref(false)
      provide('showInventoryModal', showInventoryModal)

      const modalKey = ref({
        productEdit: true,
        productCountTotal: true,
        inventory: true,
      })

      const handleDestroyModal = function (val) {
        modalKey.value[val] = false
        nextTick(() => {
          modalKey.value[val] = true
        })
      }
      provide('handleDestroyModal', handleDestroyModal)

      const selectRow = ref({})
      provide('selectRow', selectRow)

      const queryParams = ref(undefined)
      provide('queryParams', queryParams)

      const dataSource = ref([])
      provide('dataSource', dataSource)

      const loading = ref(false)
      provide('loading', loading)

      const expandedRowKeys = ref([])
      provide('expandedRowKeys', expandedRowKeys)

      const goodsTable = ref(null)
      const handleClearSelectedRows = function () {
        dataSource.value.map((d) => {
          d.checked = false
        })
        goodsTable.value.indeterminate = false
        goodsTable.value.checkAll = false
      }

      const batchDel = function () {
        if (!dataSource.value.filter((d) => d.checked).length) {
          message.info('请选择至少一个柜子')
          return
        }
        if (dataSource.value.filter((d) => d.checked).find(record => record.productCount > 0)) {
          return message.info('陈列柜上绑定了商品，不能删除')
        }
        proxy.$confirm({
          title: '确认删除陈列？',
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            handleBatchDel()
          },
        })
      }
      provide('batchDel', batchDel)

      const handleBatchDel = async function () {
        const res = await proxy.$store.dispatch(
          `operation/exhibition/${BATCH_DELETE}`,
          dataSource.value.filter((d) => d.checked).map((item) => item.fixPhotoId)
        )
        if (res) {
          message.success('批量删除成功')
          handleClearSelectedRows()
          getData(1)
        }
      }

      const getData = async function (cur) {
        if (cur) pagination.value.current = cur
        const { current, pageSize } = pagination.value
        const {
          keyWord,
          tag,
          timeRange,
        } = searchForm.value
        const params = {
          current,
          size: pageSize,
          ...searchForm.value
        }
        if (keyWord) {
          if (tag === 'productName') {
            params.pid = keyWord
          } else {
            params[tag] = keyWord
          }
        }
        if (timeRange && timeRange.length) {
          const playStartTime = timeRange[0]
          const playEndTime = timeRange[1]
          if (moment(playStartTime).add(3, 'months') < moment(playEndTime)) {
            return proxy.$message.warning('时间间隔不能超过三个月')
          }
          params.playStartTime = playStartTime + ' 00:00:00'
          params.playEndTime = playEndTime + ' 23:59:59'
        }
        loading.value = true
        queryParams.value = { ...params, tag, keyWord }
        const res = await proxy.$store.dispatch(
          `operation/exhibition/${GET_BY_AREA_IDS}`,
          params
        )
        loading.value = false
        if (res) {
          const {
            data: { records, totals, total },
          } = res
          const cacheData = []
          if (records?.length) {
            records.forEach((first, index) => {
              const { area, areaId, displays, displayProgram } = first
              let areaLen = 0
              displays?.forEach((second, secondIndex) => {
                areaLen += (1 + (expandedRowKeys.value.includes(second.id) ? second.children?.length || 0 : 0))
                cacheData.push({
                  area,
                  areaId,
                  displayProgram,
                  showArea: secondIndex === 0,
                  areaLen: 0,
                  ...second,
                  id: second.id,
                  rowKey: second.id,
                  children: second.children?.map((third, thirdIndex) => ({
                    area,
                    areaId,
                    displayProgram,
                    ...third,
                    parentCabinetName: second.name,
                    rowKey: 'rowKey' + third.id + index + secondIndex + thirdIndex,
                    fixPhotoId: second.id, // 陈列柜id
                    cabinetType: second.dataType
                  })) || []
                })
              })
              const item = cacheData.find(it => it.id === displays[0].id)
              item && (item.areaLen = areaLen)
            })
            if (records?.find(it => it.displays?.length)) {
              cacheData.push({
                ...(totals || {}),
                area: '合计',
                showTotal: true,
                rowKey: 'totalRowKey'
              })
            }
            console.log('cacheData', cacheData.map(it => it.rowKey))
            dataSource.value = cacheData
          } else {
            dataSource.value = []
          }
          pagination.value.total = total
        }
      }
      getData()
      provide('getData', getData)

      const images = ref([])
      const viewImage = function (urls) {
        images.value = urls
        nextTick(() => {
          // 找到.images的div挂载到$viewer上
          const viewer = proxy.$el.querySelector('.images').$viewer
          viewer.show()
          viewer.view(0)
        })
      }
      provide('viewImage', viewImage)

      const logs = ref(null)
      const checkLog = function (row) {
        logs.value.show(row)
      }
      provide('checkLog', checkLog)

      const handleTableChange = function (paginationObj, filters, sorter) {
        pagination.value = { ...paginationObj }
        getData()
      }
      provide('handleTableChange', handleTableChange)

      const inventory = function () {
        showInventoryModal.value = true
      }
      provide('inventory', inventory)

      const productEdit = function (row) {
        selectRow.value = row
        showProductEditModal.value = true
      }
      provide('productEdit', productEdit)

      const machineNumberTitle = ref('')
      const machineNumberEdit = function (row) {
        machineNumberTitle.value = (row.name ? `【${row.name}】` : '') + '机位数管理'
        selectRow.value = cloneDeep(row)
        showMachineNumberModal.value = true
      }
      provide('machineNumberTitle', machineNumberTitle)
      provide('machineNumberEdit', machineNumberEdit)

      return {
        goodsTable,
        modalKey,
        showMachineNumberModal,
        showProductEditModal,
        showInventoryModal,
        logs,
        oAuthManage,
        images,
      }
    },
    render () {
      const {
        modalKey,
        showMachineNumberModal,
        showProductEditModal,
        showInventoryModal,
        oAuthManage,
        images,
      } = this
      return (
      <page>
        <template slot="extra">
          <div class="flex">
          {oAuthManage ? (
            <div>
              <BtnRouter
                  routerName="STORE_ExhibitionUpperLimit"
                  type="primary"
                  text="陈列上限设置"
                />
              <BtnRouter
                routerName="STORE_ExhibitionDetail"
                type="primary"
                text="陈列商品明细"
                class='mx-8'
              />
              <BtnRouter
                routerName="STORE_AlertManage"
                type="primary"
                text="预警管理"
                class='mx-8'
              />
              <BtnRouter
                routerName="STORE_ExhibitionCad"
                type="primary"
                text="门店工程图"
                class='mx-8'
              />
            </div>
          ) : null}
          <BtnRouter
            routerName="STORE_ExhibitionFeedback"
            type="primary"
            text="单品陈列反馈"
            class='mr-16 margin-left'
          />
            { oAuthManage ? <BtnRouter
              routerName="STORE_ExhibitionZoneManage"
              type="primary"
              text="专区管理"
            /> : null }
          </div>
        </template>
        <ni-list-page pushFilterToLocation={false}>
          <GoodsScreen />
          <GoodsTable ref="goodsTable" />
        </ni-list-page>
        <ModalLogs ref="logs" />
        {modalKey.productEdit ? (
          <ProductEditModal
            show-product-edit-modal={showProductEditModal}
            {...{
              on: {
                'update:showProductEditModal': (val) => {
                  this.showProductEditModal = val
                },
              },
            }}
          ></ProductEditModal>
        ) : null}
        {modalKey.productCountTotal ? (
          <MachineNumberModalNew
            show-machine-number-modal={showMachineNumberModal}
            {...{
              on: {
                'update:showMachineNumberModal': (val) => {
                  this.showMachineNumberModal = val
                },
              },
            }}
          >
          </MachineNumberModalNew>
        ) : null}
        {modalKey.inventory ? (
          <InventoryModal
            show-inventory-modal={showInventoryModal}
            {...{
              on: {
                'update:showInventoryModal': (val) => {
                  this.showInventoryModal = val
                },
              },
            }}
          ></InventoryModal>
        ) : null}

        <div class="images" v-viewer={{ movable: false }} style="display: none">
          {images.map((src) => (
            <img src={src} key={src} />
          ))}
        </div>
      </page>
      )
    },
  })
</script>

<style lang="scss" scoped>
:deep(.ant-modal-body) {
  min-height: 5em;
}
:deep(.ant-table-tbody
    > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)
    > td, .ant-table-tbody
    > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)
    > td, .ant-table-thead
    > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)
    > td, .ant-table-thead
    > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)
    > td) {
  background: #fff;
}
.mx-8{
  margin-left: 8px;
  margin-right: 8px;
}
</style>
