<script lang="jsx">
  import {
    defineComponent,
    reactive,
    computed,
    toRefs,
    getCurrentInstance,
    onMounted,
    ref,
    nextTick
  } from 'vue'
  import { Form, Card, Button, Icon, message } from 'ant-design-vue'
  import VueDraggableResizable from 'vue-draggable-resizable'
  import 'vue-draggable-resizable/dist/VueDraggableResizable.css'
  import cloneDeep from 'lodash/cloneDeep'
  import { NiAreaSelect } from '@jiuji/nine-ui'
  import {
    GET_DP_FIX_PHOTO_ENUMS,
    GET_ALL_BY_AREA_ID,
    DELETE_FIX_EDIT,
    ADD_BATCH,
  } from '@operation/store/modules/exhibition/action-types'
  import { useLabelList } from '../common/use-label-list'

  const rules = {
    level: [{ required: true, message: '请选择等级', trigger: ['blur', 'change'] }],
    specialAreaIds: [{ required: true, message: '请选择专区', trigger: ['blur', 'change'] }],
  }

  export default defineComponent({
    components: { VueDraggableResizable, NiAreaSelect },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const state = reactive({
        bgFixColor: 'rgba(39,181,234,.6)',
        imgUrl: '',
        isEdit: false,
        areaId: '',
        cabinetList: [],
        editItem: {},
        cabinetTypeExist: [],
        orgCabinetList: [],
        colorList: [],
        proportion: 1,
        canvasWidth: '', // 图片容器宽度
        imgWidth: '', // 图片真实宽度
        clientImgWidth: '', // 保存的图片宽度
        specialArea: [],
        photoLevel: [],
      })

      const { labelList } = useLabelList()

      const { areaId } = proxy.$route.query
      state.areaId = areaId || `${proxy.$store.state.userInfo?.areaid}` || ''

      const rank = computed(() => proxy.$store.state.userInfo?.Rank || [])

      // 编辑时图片宽度
      const editImgWidth = computed(() =>
        state.canvasWidth - 430 > 1050 ? 1050 : state.canvasWidth - 430
      )

      // 获取柜子类型
      const getEnums = async function () {
        const colors = [
          'rgba(219, 216, 235, 0.6)',
          'rgba(160, 155, 178, 0.6)',
          'rgba(123, 160, 221, 0.6)',
          'rgba(121, 193, 219, 0.6)',
          'rgba(186, 225, 211, 0.6)',
          'rgba(223, 217, 211, 0.6)',
          'rgba(245, 188, 188, 0.6)',
          'rgba(160, 174, 186, 0.6)',
          'rgba(245, 235, 178, 0.6)',
          'rgba(255, 157, 137, 0.6)',
          'rgba(184, 158, 244, 0.6)',
        ]
        const res = await proxy.$store.dispatch(
          `operation/exhibition/${GET_DP_FIX_PHOTO_ENUMS}`
        )
        if (res) {
          const {
            data: { FixPhotoCabinetTypeEnum, photoLevel, specialArea },
          } = res
          state.cabinetTypeExist = FixPhotoCabinetTypeEnum
          state.photoLevel = photoLevel
          state.specialArea = specialArea
          state.colorList = state.cabinetTypeExist.map((it, i) => {
            return {
              colorType: it.value,
              color: i < colors.length ? colors[i] : colors[0],
            }
          })
        }
      }

      getEnums()

      // 获取柜子定位
      const getElementStyle = function (style) {
        return style * state.proportion
      }

      // 因编辑和查看画布图片宽度会变,切换时需要才重新计算柜子定位
      const setCabinetPostion = function (isEdit) {
        const imgWidth = isEdit ? state.canvasWidth - 430 > 1050 ? 1050 : state.canvasWidth - 430 : state.imgWidth > state.canvasWidth ? state.canvasWidth : state.imgWidth
        state.proportion = imgWidth / state.clientImgWidth
        state.cabinetList = cloneDeep(state.orgCabinetList)
        state.cabinetList.forEach((d) => {
          d.coordinateX = getElementStyle(d.coordinateX)
          d.coordinateY = getElementStyle(d.coordinateY)
          d.high = getElementStyle(d.high)
          d.withs = getElementStyle(d.withs)
        })
      }

      // 获取图片真实宽度
      const getImgWidth = function (url) {
        return new Promise((resolve) => {
          const img = new Image()
          img.src = url
          img.onload = function () {
            state.imgWidth = img.width
            resolve(img.width)
          }
          img.onerror = function () {
            resolve(1050)
          }
        })
      }

      // 获取展柜列表
      const getFixPhotos = async function () {
        const params = {
          areaIds: [state.areaId],
        }
        proxy.$indicator.open()
        const res = await proxy.$store.dispatch(
          `operation/exhibition/${GET_ALL_BY_AREA_ID}`,
          params
        )
        proxy.$indicator.close()
        if (res) {
          const { data } = res
          state.imgUrl = data.imgPre || ''
          if (!state.imgUrl) return
          // 获取图片的真实宽度
          state.imgWidth = await getImgWidth(data.imgPre || '')
          const clientData = data.clientData
            ? JSON.parse(data.clientData)
            : {
              imgWidth: 1050,
            }
          // 保存的图片宽度
          state.clientImgWidth = clientData.imgWidth
          const fixPhotos = data.fixPhotos || []
          state.orgCabinetList = fixPhotos
          setCabinetPostion()
        }
      }

      const resetIndex = function (item) {
        const cabinetList = state.cabinetList.filter(
          (d) => d.cabinetType === item.cabinetType
        )
        cabinetList.forEach((d, i) => {
          const name = state.cabinetTypeExist.find(
            (v) => v.value === item.cabinetType
          )?.label
          d.name = `${name}${i + 1}`
        })
      }

      const deleteCabinet = async function (item, i) {
        if (item.productCount > 0) {
          return message.info('陈列柜上绑定了商品，不能删除')
        }
        proxy.$confirm({
          title: `${item.name}将被删除，剩余展柜会重新编号,是否确认删除？`,
          onOk: async function () {
            if (item.id) {
              const params = {
                photoId: item.id,
              }
              proxy.$indicator.open()
              const res = await proxy.$store.dispatch(
                `operation/exhibition/${DELETE_FIX_EDIT}`,
                params
              )
              proxy.$indicator.close()
              if (res) {
                message.success('成功删除')
                state.cabinetList.splice(i, 1)
                resetIndex(item)
              }
            } else {
              state.cabinetList.splice(i, 1)
              resetIndex(item)
            }
          },
        })
      }

      const addArea = function (item) {
        const data = {
          withs: getElementStyle(90),
          high: getElementStyle(90),
          coordinateY: getElementStyle(100),
          coordinateX: getElementStyle(480),
          id: 0,
          i: state.cabinetList.length + 1,
          name: item.label + ' ',
          colorType: item.value,
          type: item.value,
          cabinetType: item.value,
          labelIds: [],
          labelModifyFlag: true,
          level: undefined,
          specialAreaIds: undefined
        }
        const cabinetList = state.cabinetList.filter(
          (d) => d.cabinetType === item.value
        )
        data.name = `${item.label}${cabinetList.length + 1}`
        state.cabinetList.push(data)
      }

      const editItemRef = ref()
      function save () {
        if (!state.cabinetList.length) return
        const cabinetList = cloneDeep(state.cabinetList)
        state.editItem?.id && (cabinetList.unshift(state.editItem))
        if (cabinetList.find(item => {
          if (!item.level) {
            proxy.$message.warning(`请选择${item.name}的等级`)
            return true
          }
          if (!item.specialAreaIds?.length) {
            proxy.$message.warning(`请选择${item.name}的专区`)
            return true
          }
        })) {
          return
        }
        toSave()
        // if (editItemRef.value) {
        //   editItemRef.value?.validate(valid => {
        //     if (valid) {
        //       toSave()
        //     } else {
        //       proxy.$message.warning(`请完善数据`)
        //     }
        //   })
        // } else {
        //   toSave()
        // }
      }
      const toSave = async function () {
        // if (!state.cabinetList.length) return
        const clientData = {
          imgWidth: editImgWidth.value,
        }
        const params = {
          areaId: state.areaId,
          list: state.cabinetList,
          clientData: JSON.stringify(clientData),
        }
        proxy.$indicator.open()
        const res = await proxy.$store.dispatch(
          `operation/exhibition/${ADD_BATCH}`,
          params
        )
        proxy.$indicator.close()
        if (res) {
          message.success('保存成功')
          getFixPhotos()
          state.isEdit = false
        }
      }

      const onDragLayer = function (x, y) {
        state.editItem.coordinateX = x
        state.editItem.coordinateY = y
      }

      const onResizeLayer = function (x, y, width, height) {
        state.editItem.withs = width
        state.editItem.high = height
        state.editItem.coordinateX = x
        state.editItem.coordinateY = y
      }

      const onActivateLayer = function (layer) {
        state.editItem = layer
        setTimeout(() => {
          editItemRef.value && (editItemRef.value.clearValidate())
        }, 300)
      }

      const cancel = function () {
        state.isEdit = false
        setCabinetPostion()
      }
      onMounted(() => {
        // 获取画布盒子宽度
        state.canvasWidth = document.querySelector('.ant-card-body').getBoundingClientRect().width - 48
        getFixPhotos()
      })

      const edit = function () {
        state.isEdit = true
        setCabinetPostion(true)
      }

      const selectLabel = function (item) {
        state.editItem.labelModifyFlag = true
        if (state.editItem.labelIds.includes(item.value)) {
          const index = state.editItem.labelIds.findIndex(d => d === item.value)
          state.editItem.labelIds.splice(index, 1)
        } else {
          state.editItem.labelIds.push(item.value)
        }
      }

      return {
        ...toRefs(state),
        rank,
        editImgWidth,
        deleteCabinet,
        addArea,
        save,
        onDragLayer,
        onResizeLayer,
        onActivateLayer,
        cancel,
        getFixPhotos,
        edit,
        selectLabel,
        labelList,
        editItemRef
      }
    },
    render () {
      const {
        cabinetList,
        cabinetTypeExist,
        editItem,
        bgFixColor,
        getFixPhotos,
        imgUrl,
        isEdit,
        rank,
        save,
        cancel,
        editImgWidth,
        colorList,
        onDragLayer,
        onResizeLayer,
        onActivateLayer,
        deleteCabinet,
        addArea,
        edit,
        imgWidth,
        canvasWidth,
        selectLabel,
        labelList,
        photoLevel,
        specialArea
      } = this
      return (
      <page>
        <Form layout="inline">
          <Form.Item>
            <NiAreaSelect
              v-model={this.areaId}
              show-search
              style="width: 200px;"
              placeholder="选择店名查询"
            />
          </Form.Item>
          <Form.Item>
            <Button type="primary" onClick={getFixPhotos} icon="search">
              查询
            </Button>
          </Form.Item>
        </Form>
        <Card style="margin-top:10px">
          {imgUrl ? (
            <div class="flex flex-row margin-bottom">
              <div style="margin-left: auto;">
                {!isEdit && rank.includes('zcbj') ? (
                  <Button
                    type="primary"
                    onClick={() => {
                      edit()
                    }}
                  >
                    编辑
                  </Button>
                ) : null}
                {isEdit && rank.includes('zcbj') ? (
                  <Button type="primary" onClick={save} class="mr-16">
                    保存
                  </Button>
                ) : null}
                {isEdit ? (
                  <Button type="primary" onClick={cancel}>
                    取消
                  </Button>
                ) : null}
              </div>
            </div>
          ) : null}
          {
            imgUrl ? <div class={{ 'flex': true, 'flex-justify-center': !isEdit }}>
            <div class="viewport flex-child-noshrink" draggable={false} style={{ width: isEdit ? `${editImgWidth}px` : imgWidth > canvasWidth ? '100%' : 'auto' }}>
              <div class="content" style="width:100%">
                <div

                  style="transform: `scale(1)`;transformOrigin: '0 0 0';width: '100%'"
                  class="layout"
                  onDragstart={() => false}
                >
                  <img
                    src={imgUrl}
                    alt=""
                    style="display:block;width:100%"
                  />
                  <div
                    class="layers"
                    {...{
                      on: {
                        mousemove: (e) => {
                          e.stopPropagation()
                        },
                      },
                    }}
                  >
                    {cabinetList.map((layer, i) => (
                      <div key={i}>
                        {!isEdit ? (
                          <div
                            class="layer font-15 flex flex-center"
                            title={layer.name}
                            style={{
                              top: `${layer.coordinateY}px`,
                              left: `${layer.coordinateX}px`,
                              width: `${layer.withs}px`,
                              height: `${layer.high}px`,
                              fontWeight: 600,
                              background: colorList.find(
                                (it) => it.colorType === layer.colorType
                              )
                                ? colorList.find(
                                    (it) => it.colorType === layer.colorType
                                  ).color
                                : bgFixColor,
                            }}
                            {...{
                              on: {
                                mousemove: (e) => {
                                  e.stopPropagation()
                                },
                              },
                            }}
                          >
                            {layer.name}
                          </div>
                        ) : null}
                        {isEdit ? (
                          <VueDraggableResizable
                            style={{
                              background: colorList.find(
                                (it) => it.colorType === layer.colorType
                              )
                                ? colorList.find(
                                    (it) => it.colorType === layer.colorType
                                  ).color
                                : bgFixColor,
                            }}
                            class={{ 'layer-box': true, 'layer-edit': layer.id === editItem.id }}
                            w={layer.withs}
                            h={layer.high}
                            minw={10}
                            minh={10}
                            x={layer.coordinateX}
                            y={layer.coordinateY}
                            parent={false}
                            active={layer.name === editItem.name}
                            onDragging={onDragLayer}
                            onResizing={onResizeLayer}
                            onActivated={() => {
                              onActivateLayer(layer)
                            }}
                            ref={'layer-' + layer.id}
                            {...{
                              on: {
                                mousemove: (e) => {
                                  e.stopPropagation()
                                },
                              },
                            }}
                          >
                            <div
                              class="flex flex-center full-width full-height relative"
                              title={layer.name}
                            >
                              <div
                                class="font-15 flex flex-center"
                                type="text"
                                style="width:100%;height:100%;border:0;background:transparent;font-weight: 600"
                              >
                                {layer.name}
                              </div>
                              <img
                                class="m-img"
                                onClick={() => {
                                  deleteCabinet(layer, i)
                                }}
                                src={require('~/assets/images/close.png')}
                              />
                            </div>
                          </VueDraggableResizable>
                        ) : null}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            {isEdit ? (
              <div style="min-width:400px;margin-left:30px">
                <div class="material-type">
                  <div class="title">陈列柜类型</div>
                  <div class="flex flex-wrap">
                  {cabinetTypeExist.map((item) => (
                    <div
                    onClick={() => {
                          addArea(item)
                        }}
                      class="flex flex-align-center material-box pointer"
                    >
                      <div>{item.label}</div>
                      <Icon
                        class="ml-5 font-14"
                        type="plus-circle"
                      />
                    </div>
                  ))}
                  </div>
                </div>
                {
                  editItem.name ? <div class="material-type" style="margin-top:16px">
                    <a-form-model
                      { ...{ props: { model: editItem } } }
                      rules={rules}
                      ref="editItemRef">
                      <a-form-model-item label={editItem.name + '的等级'} prop="level">
                        <a-radio-group v-model={editItem.level} options={photoLevel}/>
                      </a-form-model-item>
                      <a-form-model-item label={editItem.name + '的专区'} prop="specialAreaIds">
                        <a-select
                          allowClear
                          mode="multiple"
                          max-tag-count={5}
                          option-filter-prop="children"
                          v-model={editItem.specialAreaIds}
                          placeholder="请选择"
                          options={specialArea}/>
                      </a-form-model-item>
                    </a-form-model>
                </div> : null
                }
              </div>
            ) : null}
          </div> : null
          }

          {!imgUrl ? (
            <div style="text-align: center; font-size: 24px; margin-top: 200px;">
              该门店还未上传工程图，请联系工程部同事上传。
            </div>
          ) : null}
        </Card>
      </page>
      )
    },
  })
</script>

<style scoped lang="scss">
.ml-5{
  margin-left: 5px;
}
.font-14{
  font-size: 14px;
}
:deep(.ant-modal-body) {
  max-height: 65vh;
  overflow-y: scroll;
}
.layout {
  position: relative;
  cursor: move;
  .layers {
    position: absolute;
    top: 0;
    left: 0;
    .layer {
      position: absolute;
      cursor: pointer;
    }
  }
  .m-img {
    position: absolute;
    top: -7px;
    right: -7px;
    width: 15px;
    height: 15px;
    z-index: 2;
    cursor: pointer;
  }
}
.material-type {
  padding: 12px 12px 2px 12px;
  background: #fafafa;
  border-radius: 2px;
  .title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 16px;
  }
  .material-box{
    border-radius: 2px;
    border: 1px solid #DEDEDE;
    background: #F5F5F5;
    height: 22px;
    padding: 0 8px;
    margin-bottom: 10px;
    margin-right: 10px;
  }
  .material-box.active{
    background: #EEF4FD;
    border: 1px solid #1890FF;
    color: #1890FF;
  }
}
</style>
