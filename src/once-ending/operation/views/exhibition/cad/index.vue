<script lang="jsx">
  import Vue, { defineComponent, ref, getCurrentInstance, nextTick, reactive } from 'vue'
  import 'viewerjs/dist/viewer.css'
  import Viewer from 'v-viewer'
  import { cadColumns, rules } from './constants'
  import UploadPreModal from './components/upload-pre-modal.vue'
  import { NiAreaSelect } from '@jiuji/nine-ui'
  import { Button, Card, Table, message } from 'ant-design-vue'
  import exhibitionApi from '../../../api/exhibition'
  import {
    GET_BY_AREA_ID,
    SAVE_BACK_MAP,
    UPDATE_BACK_MAP
  } from '@operation/store/modules/exhibition/action-types'
  import { cloneDeep } from 'lodash'
  import axios from 'axios'
  import moment from 'moment'
  Vue.use(Viewer)

  export default defineComponent({
    components: {
      UploadPreModal,
      NiAreaSelect
    },
    data () {
      return {
        customRenderMap: new Map([
          [
            'imgPre',
            (text, record) => {
              return (
                <div>
                  <img
                    style={{
                      width: '30px',
                      height: '30px',
                      display: text ? 'inline' : 'none'
                    }}
                    onClick={() => this.viewLargerImage([text])}
                    src={record.imgPre}
                  />
                  <a-button
                    onClick={() => this.showCateWin(record)}
                    type="link"
                    ghost
                    style="color: #1890ff;"
                  >
                    {text ? '更换工程图' : '上传工程图'}
                  </a-button>
                </div>
              )
            }
          ],
          [
            'displayProgram',
            (text) => text?.length > 50 ? <a-popover>
              <p slot="content" style="maxWidth: 600px">{text}</p>
              {text?.slice(0, 50)}...
            </a-popover> : text || '--'
          ]
        ])
      }
    },
    computed: {
      columns () {
        return cadColumns.map(item => {
          const cache = { ...item, align: 'center' }
          const customRender = this.customRenderMap.get(item.dataIndex)
          customRender && (cache.customRender = customRender)
          return cache
        })
      }
    },
    setup (props) {
      const currentInstance = getCurrentInstance().proxy
      const proxy = currentInstance

      const exportLoading = ref(false)
      const cacheParams = ref({})
      async function toExport () {
        const url = exhibitionApi.exportDisplayProgram()
        const fileName = '展陈方案'
        exportLoading.value = true
        exportData(url, fileName, true, cacheParams.value).catch(err => {
          throw new Error(err)
        }).finally(() => {
          exportLoading.value = false
        })
      }

      function exportData (url, fileName = '', useTime = true, params = {}) {
        return new Promise((resolve, reject) => {
          axios({
            method: 'post',
            url: url,
            responseType: 'blob',
            timeout: 60 * 1000,
            data: params,
            headers: {
              Authorization: proxy.$store.state.token
            }
          })
            .then(res => {
              const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
              const reader = new FileReader()
              reader.readAsText(blob, 'utf8')
              reader.addEventListener('loadend', () => {
                try {
                  const result = JSON.parse(reader.result)
                  proxy.$message.error(result.userMsg)
                  reject(result.userMsg)
                } catch (e) {
                  const objectUrl = URL.createObjectURL(blob)
                  const link = document.createElement('a')
                  link.download = `${fileName}${useTime ? moment().format('YYYYMMDDHHmmss') : ''}.xlsx`
                  link.style.display = 'none'
                  link.href = objectUrl
                  document.body.appendChild(link)
                  link.click()
                  resolve()
                }
              })
            })
            .catch(e => {
              console.log(e)
              reject(e)
              proxy.$message.error('下载失败')
            })
        })
      }
      const pagination = ref({
        total: 0,
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '50', '100'],
        showSizeChanger: true
      })

      const dataSource = ref([])

      const addData = ref({
        areaId: '',
        imgPre: '',
        fidPre: '',
        imgPreId: ''
      })

      const loading = ref(false)

      const uploadPreModal = ref(false)

      const searchId = ref([])

      const images = ref([])

      const destroyUploadPreModal = ref(true)

      const getData = async function (cur) {
        if (cur) pagination.value.current = cur
        const { current, pageSize: size } = pagination.value
        const params = {
          current,
          size
        }
        if (searchId.value.length) {
          params.areaIds = searchId.value
        }
        cacheParams.value = params
        loading.value = true
        const res = await currentInstance.$store.dispatch(
          `operation/exhibition/${GET_BY_AREA_ID}`,
          params
        )
        loading.value = false
        if (res) {
          const { data: { records, total } } = res
          dataSource.value = records
          pagination.value.total = total
        }
      }

      getData()

      const handleTableChange = function (paginationObj) {
        pagination.value = paginationObj
        getData()
      }

      const showCateWin = function (record) {
        addData.value = { ...record }
        uploadPreModal.value = true
      }

      const viewLargerImage = function (url) {
        // 清空图片数组
        images.value = url
        nextTick(() => {
          // 找到.images的div挂载到$viewer上
          const viewer = currentInstance.$el.querySelector('.images').$viewer
          viewer.show()
        })
      }

      const edit = function (record) {
        const routeData = currentInstance.$router.resolve({
          path: `/store/exhibition?areaId=${record.areaId}`
        })
        window.open(routeData.href)
      }

      const destroyModal = function () {
        destroyUploadPreModal.value = false
        nextTick(() => {
          destroyUploadPreModal.value = true
        })
      }

      const initialization = (record) => {
        let params = {
          areaId: record.areaId
        }
        currentInstance.$indicator.open()
        exhibitionApi.dataInit(params).then(res => {
          if (res.code === 0) {
            currentInstance.$message.success(res.userMsg)
            getData()
          } else {
            currentInstance.$message.error(res.userMsg)
          }
        }).finally(() => {
          currentInstance.$indicator.close()
        })
      }

      const displayProgramRef = ref()
      const manageDisplayProgram = reactive({
        show: false,
        title: '',
        loading: false,
        form: {
          displayProgram: undefined,
          areaId: undefined
        }
      })
      function editDisplayProgram (record) {
        manageDisplayProgram.form.displayProgram = record.displayProgram || undefined
        manageDisplayProgram.form.areaId = record.areaId
        manageDisplayProgram.title = `${record.area}展陈方案`
        manageDisplayProgram.show = true
      }
      async function saveDisplayProgram () {
        currentInstance.$refs.displayProgramRef.validate(async valid => {
          if (valid) {
            manageDisplayProgram.loading = true
            const params = {
              content: manageDisplayProgram.form.displayProgram,
              areaId: manageDisplayProgram.form.areaId,
            }
            const res = await exhibitionApi.saveDisplayProgram(params).catch(err => {
              currentInstance.$message.error(err.message)
            })
            manageDisplayProgram.loading = false
            if (res) {
              currentInstance.$message.success('保存成功')
              manageDisplayProgram.show = false
              getData()
            }
          } else {
            currentInstance.$message.warning('请先完善数据')
          }
        })
      }

      return {
        pagination,
        dataSource,
        addData,
        loading,
        uploadPreModal,
        searchId,
        images,
        destroyUploadPreModal,
        getData,
        handleTableChange,
        showCateWin,
        viewLargerImage,
        edit,
        destroyModal,
        initialization,
        manageDisplayProgram,
        displayProgramRef,
        editDisplayProgram,
        saveDisplayProgram,
        toExport,
        exportLoading
      }
    },
    render () {
      const {
        dataSource,
        getData,
        columns,
        pagination,
        loading,
        handleTableChange,
        edit,
        images,
        destroyUploadPreModal,
        destroyModal,
        uploadPreModal,
        addData,
        initialization,
        manageDisplayProgram,
        saveDisplayProgram,
        editDisplayProgram,
        toExport,
        exportLoading
      } = this
      return (
      <page>
        <div
          class="action-btns flex flex-justify-between"
          style="margin: 0 5px 20px 5px;"
        >
          <div style="width:400px;display:flex;">
            <NiAreaSelect
              v-model={this.searchId}
              allow-clear={true}
              multiple
              show-search
              max-tag-count={1}
              style="width: 300px;"
              placeholder="选择店名查询"
            />
            <Button
              style="margin-left: 10px"
              type="primary"
              key="search"
              onClick={() => {
                getData(1)
              }}
              icon="search"
            >
              查询
            </Button>
          </div>
          <a-button icon="export" loading={exportLoading} onClick={toExport}>导出展陈方案</a-button>
        </div>

        <Card class="table-list">
          <Table
            columns={columns}
            rowKey="area"
            bordered
            dataSource={dataSource}
            pagination={pagination}
            loading={loading}
            onChange={handleTableChange}
            scopedSlots={{
              tableAction: (text, record) => (
                <div>
                  <Button
                    type="link"
                    disabled={!record.imgPre}
                    size="small"
                    onClick={() => {
                      edit(record)
                    }}
                    class="mr-16"
                  >
                    陈列柜管理
                  </Button>
                  <Button
                    type="link"
                    size="small"
                    onClick={() => {
                      editDisplayProgram(record)
                    }}
                    class="mr-16">
                    展陈方案
                  </Button>
                  <Button
                    type="link"
                    disabled={!record.dataInitFlag}
                    size="small"
                    onClick={() => initialization(record)}
                  >
                    数据初始化
                  </Button>
                </div>
              )
            }}
          ></Table>
        </Card>
        <div class="images" v-viewer={{ movable: false }} style="display: none">
          {images.map(src => (
            <img src={src} key={src} />
          ))}
        </div>
        {destroyUploadPreModal ? (
          <UploadPreModal
            onDestroyModal={destroyModal}
            upload-pre-modal={uploadPreModal}
            {...{
              on: {
                'update:uploadPreModal': val => {
                  this.uploadPreModal = val
                }
              }
            }}
            add-data={addData}
            onGetData={getData}
          ></UploadPreModal>
        ) : null}
        <a-modal
          v-model={ manageDisplayProgram.show }
          title={ manageDisplayProgram.title }
          destroyOnClose={true}
          confirmLoading={manageDisplayProgram.loading}
          onOk={saveDisplayProgram}
          width="500px">
          <a-form-model
            { ...{ props: { model: manageDisplayProgram.form } } }
            rules={rules}
            ref="displayProgramRef">
            <a-form-model-item label="展陈方案" prop="displayProgram" class="relative">
              <a-textarea maxLength={500} autoSize={{ minRows: 1, maxRows: 5 }} v-model={manageDisplayProgram.form.displayProgram} placeholder="请输入" allowClear/>
              <div class="flex flex-justify-end grey-9 font-12 tip">{manageDisplayProgram.form.displayProgram?.length || 0}/500</div>
            </a-form-model-item>
          </a-form-model>
        </a-modal>
      </page>
      )
    }
  })
</script>
<style lang="scss" scoped>
.tip {
  line-height: 12px;
  position: absolute;
  right: 0;
  bottom: -14px;
}
</style>
