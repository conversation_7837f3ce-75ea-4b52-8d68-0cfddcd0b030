<script lang="jsx">
  import { defineComponent, computed, ref, getCurrentInstance } from 'vue'
  import { Modal, Form, Icon, message } from 'ant-design-vue'
  import nineUpload from '@jiuji/nine-upload'
  import AreaSelector from '~/components/staff/area-selector'
  import {
    GET_BY_AREA_ID,
    SAVE_BACK_MAP,
    UPDATE_BACK_MAP
  } from '@operation/store/modules/exhibition/action-types'
  export default defineComponent({
    components: {
      AreaSelector
    },
    props: {
      addData: {
        type: Object,
        default: () => {
          return {
            areaId: '',
            imgPre: '',
            fidPre: '',
            imgPreId: ''
          }
        }
      },
      uploadPreModal: {
        type: Boolean,
        default: false
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const visible = computed({
        get: () => props.uploadPreModal,
        set: val => proxy.$emit('update:uploadPreModal', val)
      })

      const loading = ref(false)

      const onSubmit = function () {
        if (props.addData.imgPreId) {
          editEmgPre()
        } else {
          saveBackMap()
        }
      }

      const saveBackMap = async function () {
        if (props.addData.areaId === '') {
          message.error('请选择门店')
          return
        }
        if (props.addData.imgPre === '') {
          message.error('请上传工程图')
          return
        }
        const params = {
          areaId: props.addData.areaId,
          fid: props.addData.fidPre,
          type: 1
        }
        loading.value = true
        const res = await proxy.$store.dispatch(
          `operation/exhibition/${SAVE_BACK_MAP}`,
          params
        )
        loading.value = false
        if (res) {
          message.success('添加成功')
          visible.value = false
          proxy.$emit('getData')
        }
      }

      const editEmgPre = async function () {
        const params = {
          id: props.addData.imgPreId,
          fid: props.addData.fidPre,
          type: 1
        }
        loading.value = true
        const res = await proxy.$store.dispatch(
          `operation/exhibition/${UPDATE_BACK_MAP}`,
          params
        )
        loading.value = false
        if (res) {
          message.success('原始工程图修改成功')
          visible.value = false
          proxy.$emit('getData')
        }
      }

      // 上传附件
      const doUpload = function (dest) {
        nineUpload({
          accept: 'image/*,.pdf,.pdfx,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt',
          multiple: false,
          onPickFiles: async files => {
            // files是文件对象，如果要对文件进行处理，处理完成之后要在返回对象里加上files对象
            if (window.nineUploadData) {
              return window.nineUploadData
            }
            try {
              // 获取appId和token。可以把这个appId和token缓存起来，没必要每次都请求
              const {
                code = 0,
                userMsg = '',
                data = { appId: '', token: '' }
              } = await proxy.$api.common.getUploadToken()
              if (code === 0) {
                window.nineUploadData = data
                setTimeout(() => {
                  // appId和token30分钟过期，要清理一下
                  window.nineUploadData = null
                }, 30 * 60 * 1000)
                return data
              } else {
                message.error(userMsg)
              }
            } catch (e) {
              message.error(e)
            }
          },
          form: {
            collection: 'javaweb'
          }
        }).then(({ res, err }) => {
          if (res) {
            props.addData['fid' + dest] = res.fid.split('.')[0]
            props.addData['img' + dest] = res.fileName
            message.success('上传成功')
          }
          err?.map(i => {
            message.info(`${i.name}上传失败,${i.err.message}`)
          })
        })
      }

      const getImgName = function (url) {
        return url.replace(/(.*\/)*([^.]+)/i, '$2')
      }

      const afterClose = function () {
        proxy.$emit('destroyModal')
      }
      return {
        visible,
        loading,
        onSubmit,
        doUpload,
        getImgName,
        afterClose
      }
    },
    render () {
      const { loading, onSubmit, addData, doUpload, getImgName, afterClose } = this
      return <Modal
      title="上传工程图"
      v-model={this.visible}
      confirmLoading={loading}
      onOk={onSubmit}
      maskClosable={false}
      afterClose={afterClose}
    >
      <Form labelCol={{ span: 5 }} wrapperCol={{ span: 15 }}>
        <Form.Item label="店名" required>
          <AreaSelector
            value={addData.areaId}
            radio={true}
            ref="areaSelector"
            onlyCheckLast={true}
            placeholder={addData.deptName}
            disabled={true}
          ></AreaSelector>
        </Form.Item>
        <Form.Item label="工程图" required>
          <div
            style="width:295px;border:dashed 3px #dfdfdf;"
            class="flex flex-center pointer"
            onClick={() => { doUpload('Pre') }}
          >
          {
            addData.imgPre ? <div
              style="color:blue"
              title="点击重新上传图片"
            >
              { getImgName(addData.imgPre) }
            </div> : <div>
              <Icon type="upload" style="font-size:20px;"></Icon>
              <span>点击选择要上传的工程图</span>
            </div>
          }

          </div>
        </Form.Item>
      </Form>
    </Modal>
    }
  })
</script>
