<script lang="jsx">
  import filterBox from './components/filter-box'
  import tableBox from './components/table-box'
  import { NiListPage } from '@jiuji/nine-ui'
  import { originSearchForm } from './constants'
  import { ref, reactive, defineComponent, provide, getCurrentInstance, computed } from 'vue'
  import {
    GET_DP_FIX_PHOTO_ENUMS,
    GET_PRODUCT_DETAIL
  } from '@operation/store/modules/exhibition/action-types'
  import { message } from 'ant-design-vue'
  import moment from 'moment'
  import { cloneDeep } from 'lodash'

  export default defineComponent({
    name: 'index',
    components: { filterBox, tableBox, NiListPage },
    setup () {
      const { proxy } = getCurrentInstance()
      const loading = ref(false)
      const dataSource = ref([])
      const searchForm = ref(cloneDeep(originSearchForm))
      const pagination = reactive({
        pageSize: 1,
        current: 1,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ['1', '3', '5', '10']
      })
      provide('dataSource', dataSource)
      provide('pagination', pagination)
      provide('loading', loading)
      provide('searchForm', searchForm)

      const getForm = function () {
        const { form } = proxy.$route.query
        if (form) {
          const { playStartTime, playEndTime, ...other } = JSON.parse(form)
          Object.assign(searchForm.value, {
            ...other
          })
          if (playStartTime && playEndTime) {
            const startTime = moment(playStartTime).format('YYYY-MM-DD')
            const endTime = moment(playEndTime).format('YYYY-MM-DD')
            searchForm.value.timeRange = [startTime, endTime]
          }
        }
      }
      getForm()

      const oAuthManage = computed(() =>
        proxy.$store.state.userInfo.Rank?.includes('hpcl')
      )
      const userAreaId = computed(() => proxy.$store.state.userInfo.areaid)
      const canWatchAll = computed(() => oAuthManage.value || userAreaId.value === 22)
      provide('canWatchAll', canWatchAll)

      const allOptions = reactive({
        cupboardOptions: [],
        specialAreaOptions: [],
        showDimensionOptions: [],
        photoLevelOptions: [],
        stockFilterOptions: [],
        auditStatusOptions: []
      })
      provide('allOptions', allOptions)
      const getEnums = async function () {
        const res = await proxy.$store.dispatch(
          `operation/exhibition/${GET_DP_FIX_PHOTO_ENUMS}`
        )
        if (res) {
          allOptions.cupboardOptions = res.data.FixPhotoCabinetTypeEnum
          allOptions.specialAreaOptions = res.data.specialArea
          allOptions.showDimensionOptions = res.data.showDimension
          allOptions.photoLevelOptions = res.data.photoLevel
          allOptions.stockFilterOptions = res.data.stockFilter
          allOptions.auditStatusOptions = res.data.auditStatus
        }
      }
      getEnums()

      const exportsParams = ref()
      provide('exportsParams', exportsParams)
      const getData = async function (cur) {
        if (cur) pagination.current = cur
        const { current, pageSize } = pagination
        const {
          keyWord,
          tag,
          timeRange,
        } = searchForm.value
        const params = {
          current,
          size: pageSize,
          ...searchForm.value
        }
        if (keyWord) {
          if (tag === 'productName') {
            params.pid = keyWord
          } else {
            params[tag] = keyWord
          }
        }
        if (timeRange && timeRange.length) {
          const playStartTime = timeRange[0]
          const playEndTime = timeRange[1]
          if (moment(playStartTime).add(3, 'months') < moment(playEndTime)) {
            return proxy.$message.warning('时间间隔不能超过三个月')
          }
          params.playStartTime = playStartTime + ' 00:00:00'
          params.playEndTime = playEndTime + ' 23:59:59'
        }
        exportsParams.value = params
        loading.value = true
        const res = await proxy.$store.dispatch(`operation/exhibition/${GET_PRODUCT_DETAIL}`, params)
        loading.value = false
        if (res) {
          const {
            data: { records, total, totals },
          } = res
          expandedRowKeys.value = []
          pagination.total = total
          const cacheData = records || []
          dealData(cacheData)
          if (totals && records.length) {
            cacheData.push({ ...totals, showTotal: true })
          }
          dataSource.value = cacheData
          console.log('dataSource.value', dataSource.value)
        }
      }
      provide('getData', getData)
      getData()

      const expandedRowKeys = ref([])
      provide('expandedRowKeys', expandedRowKeys)
      function dealData (arr) {
        arr?.forEach((first, index) => {
          const { id } = first
          expandedRowKeys.value.push(id)
          if (first.children?.length) {
            const cacheData = []
            first.children.forEach((second, secondIndex) => {
              const { name } = second
              second.children?.forEach((third, thirdIndex) => {
                cacheData.push({
                  ...third,
                  name,
                  show: thirdIndex === 0,
                  rowLength: thirdIndex === 0 ? second.children?.length || 0 : 0,
                  id: 'id' + third.id + index + secondIndex + thirdIndex
                })
              })
            })
            first.children = cacheData
          }
        })
      }

      const handleTableChange = function (paginationObj) {
        Object.assign(pagination, paginationObj)
        getData()
      }
      provide('handleTableChange', handleTableChange)
      return {
        getData
      }
    },
    render (createElement, context) {
      const { getData } = this
      return <page>
        <ni-list-page pushFilterToLocation={false}>
          <filter-box/>
          <table-box ref='refTableBox'
                     class="mt-16"
                     ongetData={() => getData()}/>
        </ni-list-page>
      </page>
    }
  })
</script>

<style scoped>

</style>
