<script lang="jsx">
  import Vue, {
    defineComponent,
    computed,
    ref,
    inject,
    getCurrentInstance, nextTick
    } from 'vue'
  import { NiTable, NiPrice } from '@jiuji/nine-ui'
  import { originColumns, areaShow } from '../constants'
  import { cloneDeep } from 'lodash'
  import Viewer from 'v-viewer'
  import 'viewerjs/dist/viewer.css'
  import { Tooltip } from 'ant-design-vue'
  import axios from 'axios'
  import exhibition from '@/operation/api/exhibition'
  import moment from 'moment/moment'
  import { formatNum } from '@/operation/views/exhibition/upper-limit/constants'
  Vue.use(Viewer)
  export default defineComponent({
    name: 'table-box',
    components: {
      NiTable, NiPrice
    },
    setup () {
      const { proxy } = getCurrentInstance()
      const length = originColumns.length
      const colSpanLength = ref(length)
      const loading = inject('loading')
      const dataSource = inject('dataSource')
      const pagination = inject('pagination')
      const handleTableChange = inject('handleTableChange')
      const expandedRowKeys = inject('expandedRowKeys')
      const images = ref([])
      const viewImage = function (imgUrls) {
        images.value = imgUrls
        nextTick(() => {
          // 找到.images的div挂载到$viewer上
          const viewer = proxy.$el.querySelector('.images').$viewer
          viewer.show()
        })
      }
      const toManage = function (record) {
        console.log(record)
        const { areaId, cabinetType, cabinetName, fixPhotoId, productCount, machineNumber, cabinetLabelStr } = record
        const form = {
          areaId,
          cabinetName,
          fixPhotoId,
          cabinetType,
          showCount: ((cabinetType === 4 || cabinetType === 3) && machineNumber) ? `（${productCount}/${machineNumber}）` : '',
          cabinetLabelStr
        }
        console.log(form)
        proxy.$router.push(`/store/exhibition/product-manage?form=${JSON.stringify(form)}`)
      }

      function changeExpand (record) {
        const { id } = record
        if (expandedRowKeys.value.includes(id)) {
          expandedRowKeys.value = expandedRowKeys.value.filter(k => k !== id)
        } else {
          expandedRowKeys.value.push(id)
        }
      }

      const exportsParams = inject('exportsParams')
      const downloadLoading = ref(false)
      const download = function () {
        const cacheParams = { ...exportsParams.value }
        downloadLoading.value = true
        axios({
          method: 'post',
          url: exhibition.exportExcelDetail(),
          data: cacheParams,
          responseType: 'blob',
          timeout: 60000,
          headers: {
            Authorization: proxy.$store.state.token
          }
        }).then((res) => {
          const link = document.createElement('a')
          let blob = new Blob([res.data], { type: 'application/x-excel' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = `陈列商品明细(${moment().format('YYYY-MM-DD HH：mm')}).xlsx`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        }).finally(() => {
          downloadLoading.value = false
        })
      }
      return {
        loading,
        dataSource,
        pagination,
        colSpanLength,
        images,
        viewImage,
        handleTableChange,
        toManage,
        expandedRowKeys,
        changeExpand,
        download,
        downloadLoading,
      }
    },
    data () {
      return {
        customRenderMap: new Map([
          [
            'name',
            (text, record) => {
              return {
                children: record.showTotal ? <span>合计</span> : text && text.length ? <div class="expand-box">
                    <div class="expand-icon" style={{ marginRight: `6px` }}>{record.children?.length ? <a-icon onClick={() => this.changeExpand(record)} type={this.expandedRowKeys.includes(record.id) ? 'minus-square' : 'plus-square'}/> : null }</div>
                    {text}{ record.dpType === 1 && record.area ? `（${record.area}）` : null }
                  </div> : <span>--</span>,
                attrs: {
                  rowSpan: record.show ? record.rowLength : record.showTotal || record.dpType === 1 ? 1 : 0,
                  colSpan: record.showTotal ? 8 : 1
                }
              }
            }
          ],
          [
            'colSpanRender',
            dataIndex => (text, record) => {
              const { dpType } = record
              return {
                children: dpType === 1 || (!text && text !== 0) ? '--' : <span>
                  { dataIndex === 'storeStockAge' ? `${formatNum(text)}天` : text }
                </span>,
                attrs: {
                  colSpan: record.showTotal ? 0 : 1
                }
              }
            }
          ],
          [
            'showNumber',
            (item, record, text) => <p>{ (text || text === 0) ? text : '--' }</p>
          ],
          [
            'textPrice',
            (text, record) => <NiPrice
              decimalFontSize={ 14 }
              value={ text }
              color="rgba(0, 0, 0, 0.65)"
              prefixColor="rgba(0, 0, 0, 0.65)"
              decimalColor="rgba(0, 0, 0, 0.65)"
              thousand={ true }/>
          ]
        ])
      }
    },
    computed: {
      columns () {
        return cloneDeep(originColumns).map(d => {
          const { dataIndex } = d
          d.align = 'center'
          if (this.customRenderMap.get(dataIndex)) {
            d.customRender = this.customRenderMap.get(dataIndex)
          }
          if (d.textCustomRender) { d.customRender = this.customRenderMap.get('textCustomRender') }
          if (d.textPrice) { d.customRender = this.customRenderMap.get('textPrice') }
          const spanKeys = ['productName', 'ppid', 'mkcId', 'displayInfoId', 'displayTime', 'storeStockAge', 'cidName']
          if (spanKeys.includes(dataIndex)) {
            d.customRender = this.customRenderMap.get('colSpanRender')(dataIndex)
          }
          return d
        })
      }
    },
    render (createElement, context) {
      const {
        loading,
        dataSource,
        pagination,
        columns,
        images,
        handleTableChange,
        expandedRowKeys,
        download,
        downloadLoading,
      } = this
      return <div>
        <ni-table
          rowKey={ (r, i) => r.id }
          loading={loading}
          columns={columns}
          footerTotalNum={1}
          dataSource={dataSource}
          pagination={pagination}
          bordered
          align={'center'}
          expandedRowKeys={expandedRowKeys}
          onChange={handleTableChange}>
          <div slot="action">
            <a-button class="mr-8" type="primary" loading={downloadLoading} onClick={download}>导出</a-button>
          </div>
        </ni-table>
        <div class="images" v-viewer={{ movable: false }} style="display: none">
          {images.map((src) => (
            <img src={src} key={src}/>
          ))}
        </div>
      </div>
    }
  })
</script>

<style scoped lang="scss">
:deep(.first-level) {
  background: #c4e3fb;
}
:deep(.ant-table-tbody
  > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)
  > td) {
  background: none;
}
:deep(.ant-table-tbody > tr > td) {
  padding: 12px 8px !important;
}
:deep(.indent-level-1) {
  display: none;
}
.link {
  color: #1890ff;
  cursor: pointer;
  font-weight: 400;
}
:deep(.totals) {
  font-weight: 600;
}
.center {
  text-align: center;
}
.left {
  text-align: left;
}
.mr-26 {
  margin-right: 26px;
}
.fw-600{
  font-weight: 600;
}
:deep(.ant-table-row-expand-icon) {
  display: none;
}
:deep(.expand-box) {
  display: flex;
  width: 100%;
  box-sizing: border-box;
  line-height: 16px;
  padding: 0 12px;
  .expand-icon {
    font-size: 16px;
    width: 16px;
    cursor: pointer;
    &:hover {
      color: #1890ff;
    }
  }
}
</style>
