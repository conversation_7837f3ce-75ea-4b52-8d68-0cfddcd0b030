<script lang="jsx">
  import { defineComponent, ref, inject, getCurrentInstance } from 'vue'
  import { Select, Input, Spin, DatePicker } from 'ant-design-vue'
  import ExcelAction from '@operation/components/excel-action'
  import AreaDepartSelector from '@operation/components/area-depart-selector'
  import { NiFilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import mbaSelect from '@operation/components/mba-select'
  import { PRODUCT_PID } from '@operation/store/modules/exhibition/action-types'
  import { searchTypeOptions } from '../../goods-display/constants'
  import { useLabelList } from '../../common/use-label-list'
  import { debounce } from 'lodash'
  import Category from '@/operation/components/category.vue'
  import Brand from '@/operation/components/brand.vue'

  export default defineComponent({
    components: {
      Brand,
      Category,
      ExcelAction,
      AreaDepartSelector,
      NiFilter,
      NiFilterItem,
      mbaSelect,
      NiAreaSelect
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const { labelList } = useLabelList()
      const searchForm = inject('searchForm')
      const loading = inject('loading')
      const getData = inject('getData')
      const allOptions = inject('allOptions')
      const canWatchAll = inject('canWatchAll')

      const productOptions = ref([])
      const keyWord = ref('')
      const loadProduct = ref(false)

      const { form } = proxy.$route.query
      if (form) {
        const { productName, keyWord } = JSON.parse(form)
        if (productName && keyWord) {
          productOptions.value = [{ productName, pid: keyWord }]
        }
      }
      let getProductPid = async function (text) {
        productOptions.value = []
        if (text) {
          keyWord.value = text
          loadProduct.value = true
          let timestamp = new Date().getTime()
          let params = {
            timestamp: timestamp,
            kind: 'all', // 所有商品 不区分业务场景
            isproduct: 1, // 仅搜索商品维度，不包含规格
            limit: 10,
            q: encodeURIComponent(text)
          }
          const res = await proxy.$api.recovery.productAutoData(params)
          loadProduct.value = false
          if (res) {
            productOptions.value = res.data
          }
        }
      }
      getProductPid = debounce(getProductPid, 500)

      const getUserArea = function (areaIds) {
        console.log(123456789)
        const arr = [...areaIds]
        const arrIds = [...new Set(arr)]
        searchForm.value.rankAreaIds = arrIds.filter(item => item !== 'null')
        getData()
      }

      const changeType = function () {
        searchForm.value.keyWord = undefined
      }

      const productPidBlur = function (e) {
        setTimeout(() => {
          if (!searchForm.value.keyWord && keyWord.value) {
            searchForm.value.keyWord = keyWord.value
            keyWord.value = ''
          }
        }, 1)
      }

      const productPidChange = function () {
        keyWord.value = ''
      }

      return {
        searchForm,
        loading,
        productOptions,
        getProductPid,
        getUserArea,
        changeType,
        productPidBlur,
        productPidChange,
        loadProduct,
        allOptions,
        getData,
        labelList,
        canWatchAll
      }
    },
    render () {
      const {
        searchForm,
        changeType,
        getProductPid,
        productPidChange,
        productPidBlur,
        loadProduct,
        loading,
        productOptions,
        getData,
        labelList,
        canWatchAll,
        allOptions: {
          cupboardOptions,
          specialAreaOptions,
          showDimensionOptions,
          photoLevelOptions,
          stockFilterOptions,
          auditStatusOptions,
        }
      } = this
      return (
        <ni-filter
          form={searchForm}
          onFilter={() => {
            getData(1)
          }}
          immediate={false}
          label-width={130}
          loading={loading}>
         <ni-filter-item label="地区">
              <NiAreaSelect
                multiple
                allowClear
                mode={ 2 }
                placeholder="请选择门店或者搜索"
                maxTagCount={1}
                v-model={searchForm.areaIds}/>
          </ni-filter-item>
          <ni-filter-item label="陈列专区">
            <a-select
              placeholder="请选择"
              mode="multiple"
              maxTagCount={1}
              allowClear
              option-filter-prop="children"
              options={specialAreaOptions}
              v-model={searchForm.specialAreaIds}/>
          </ni-filter-item>
          <ni-filter-item>
            <Input.Group compact>
              <Select
                onChange={changeType}
                style="width:30%"
                v-model={searchForm.tag}
                options={searchTypeOptions}
              />
              {searchForm.tag === 'productName' ? (
                <Select
                  ref="product"
                  style="width: 70%"
                  onSearch={getProductPid}
                  onChange={productPidChange}
                  v-model={searchForm.keyWord}
                  placeholder="请输入"
                  allowClear
                  filter-option={false}
                  default-active-first-option={false}
                  showArrow={false}
                  showSearch={true}
                  dropdownMatchSelectWidth={false}
                  getPopupContainer={triggerNode => triggerNode?.parentNode}
                >
                  {productOptions.map(item => (
                    <Select.Option key={item.productId}>
                      {item.name}
                    </Select.Option>
                  ))}
                  <template slot="notFoundContent">
                    {loadProduct ? (
                      <div
                        class="full-width flex flex-center"
                        style="height: 60px"
                      >
                        <Spin />
                      </div>
                    ) : null}
                  </template>
                </Select>
              ) : (
                <a-input
                  type="number"
                  style="width: 70%"
                  allowClear
                  v-model={searchForm.keyWord}
                  placeholder="请输入"
                />
              )}
            </Input.Group>
          </ni-filter-item>
          <ni-filter-item label="陈列柜等级">
            <a-select
              placeholder="请选择"
              mode="multiple"
              allowClear
              maxTagCount={1}
              options={photoLevelOptions}
              v-model={searchForm.photoLevels}/>
          </ni-filter-item>
          <ni-filter-item>
            <Input.Group compact>
              <Select
                onChange={() => { searchForm.stockFilterValue = undefined }}
                style="width:30%"
                v-model={searchForm.stockFilter}
                options={stockFilterOptions}/>
              <a-input type="number" min={1} precision={0} v-model={searchForm.stockFilterValue} placeholder="请输入" allowClear style="width: 70%"/>
            </Input.Group>
          </ni-filter-item>
          <ni-filter-item label="陈列时间">
            <DatePicker.RangePicker
              placeholder={['开始时间', '结束时间']}
              v-model={searchForm.timeRange}
              value-format="YYYY-MM-DD"
              allow-clear={true}
            >
            </DatePicker.RangePicker>
          </ni-filter-item>
          <ni-filter-item label="陈列柜类型">
            <Select
              allowClear
              mode="multiple"
              placeholder="请选择"
              optionFilterProp="children"
              maxTagCount={1}
              v-model={searchForm.cabinetTypes}
              options={cupboardOptions}
            />
          </ni-filter-item>
          <ni-filter-item label="陈列柜">
            <Input placeholder="请输入" allowClear v-model={searchForm.cabinetName}/>
          </ni-filter-item>
          <ni-filter-item label="分类">
            <Category
              max-tag-count={1}
              v-model={searchForm.cidList}
              onChange={() => { searchForm.brandIdList = [] }}
            />
          </ni-filter-item>
          <ni-filter-item label="品牌">
            <Brand
              cateId={searchForm.cidList ? searchForm.cidList.join() : ''}
              v-model={searchForm.brandIdList}/>
          </ni-filter-item>
        </ni-filter>
      )
    }
  })
</script>

<style lang="scss" scoped>
.form-item {
  display: flex;
  // flex-wrap: wrap;
  align-items: center;
  padding-right: 16px;

  .label {
    width: 60px;
    text-align: right;
    margin-right: 5px;
  }
  .control,
  .ant-select {
    width: calc(100% - 65px);
    height: 32px;
    :deep(.ant-select-selection--multiple) {
      height: 32px;
    }
  }
}
.label {
  text-align: right;
}

:deep(.ant-input) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
:deep(.ant-select-selection__choice__remove) {
  visibility: hidden;
}
:deep(.ant-select-selection__choice) {
  max-width: 45%;
}
</style>
