export const originColumns = [
  {
    title: '陈列柜/专区',
    dataIndex: 'name',
    width: 150,
    key: 'name'
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
    key: 'productName',
    width: 160
  },
  {
    title: 'ppid',
    dataIndex: 'ppid',
    key: 'ppid',
    width: 90
  },
  {
    title: 'mkc_id',
    dataIndex: 'mkcId',
    key: 'mkcId',
    width: 90
  },
  {
    title: '到店库龄',
    dataIndex: 'storeStockAge',
    key: 'storeStockAge',
    width: 90
  },
  {
    title: '商品分类',
    dataIndex: 'cidName',
    key: 'cidName',
    width: 180
  },
  {
    title: '陈列编号',
    dataIndex: 'displayInfoId',
    key: 'displayInfoId',
    width: 90
  },
  {
    title: '陈列时间',
    dataIndex: 'displayTime',
    key: 'displayTime',
    width: 180
  },
  {
    title: '陈列成本',
    dataIndex: 'displayCost',
    key: 'displayCost',
    width: 100,
    textPrice: true,
  },
  {
    title: '近7天销量',
    dataIndex: 'last7DaysSales',
    key: 'last7DaysSales',
    width: 140,
    align: 'center',
    showNumber: true,
  },

  {
    title: '近7天毛利',
    dataIndex: 'last7DaysProfit',
    key: 'last7DaysProfit',
    width: 140,
    textPrice: true,
  },
  {
    title: '近30天销量',
    dataIndex: 'last30DaysSales',
    key: 'last30DaysSales',
    width: 150,
    showNumber: true,
  },
  {
    title: '近30天毛利',
    dataIndex: 'last30DaysProfit',
    key: 'last30DaysProfit',
    width: 150,
    textPrice: true,
  },

  {
    title: '累计销量',
    dataIndex: 'lastSales',
    key: 'lastSales',
    showNumber: true,
    width: 100,
    hide: true
  },
  {
    title: '累计毛利',
    dataIndex: 'lastProfit',
    key: 'lastProfit',
    textPrice: true,
    width: 100,
    hide: true
  },
  {
    title: '门店库存',
    dataIndex: 'stockNum',
    key: 'stockNum',
    showNumber: true,
    width: 100
  },
]

export const areaShow = [
  { label: '门店', key: 'area' },
  { label: '柜子名称', key: 'cabinetName' },
  { label: '陈列商品数量', key: 'productCount' },
]
export const statisticsOptions = [
  { tableKey: 'countSalesSeven', statisticsKey: 'countSalesSevenTotal' },
  { tableKey: 'grossProfitSeven', statisticsKey: 'grossProfitSevenTotal' },
  { tableKey: 'countSalesThirty', statisticsKey: 'countSalesThirtyTotal' },
  { tableKey: 'grossProfitThirty', statisticsKey: 'grossProfitThirtyTotal' },
  { tableKey: 'countSalesAll', statisticsKey: 'countSalesAllTotal' },
  { tableKey: 'grossProfitAll', statisticsKey: 'grossProfitAllTotal' },
  { tableKey: 'prototypeHits', statisticsKey: 'prototypeHitsTotal' },
  { tableKey: 'productCost', statisticsKey: 'productCostTotal' },
  { tableKey: 'shopStock', statisticsKey: 'shopStockTotal' },
  { tableKey: 'allStock', statisticsKey: 'allStockTotal' },
]

export const originSearchForm = {
  areaIds: [],
  specialAreaIds: undefined,
  tag: 'productName',
  keyWord: undefined,
  photoLevels: undefined,
  stockFilter: 1,
  stockFilterValue: undefined,
  timeRange: undefined,
  cabinetTypes: undefined, // 柜子类型
  cabinetName: undefined,
  cidList: undefined,
  brandIdList: undefined
}
