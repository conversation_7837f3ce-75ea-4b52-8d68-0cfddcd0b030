<script lang="jsx">
  import { defineComponent, toRefs, getCurrentInstance } from 'vue'
  import { NiTable, NiLog, NiAreaSelect } from '@jiuji/nine-ui'
  import { options, originColumns, rules, formatNum } from '../constants'
  import { useState } from '../hooks/useState'
  import uploadTemplate from './uploadTemplate.vue'
  import uploader from '~/components/uploader'
  export default defineComponent({
    name: 'table-box',
    components: {
      NiTable,
      uploadTemplate,
      NiLog,
      uploader,
      NiAreaSelect
    },
    setup () {
      const { proxy } = getCurrentInstance()
      const {
        state,
        getData,
        handleTableChange,
        toShowEdit,
        save,
        editRef,
        deleteItem,
        toShowLog,
        toExport,
        formatter,
        parser,
        openImport,
        importVisOk,
        importVisCancel,
        importVisChange,
        downloadCheckFile,
        allOptions
      } = useState()
      const checkTitle = <span>
          <a-icon type="warning" class="red font-24"/>
          <span class="font-16 ml-10 bold">数据校验失败</span>
        </span>

      const specialRules = (key, item) => {
        return [{
          required: true,
          trigger: ['blur', 'change'],
          validator: (rule, value, callback) => {
            if (!item[key] && item[key] !== 0) {
              return callback(new Error(key === 'areaId' ? '请选择门店' : '请输入陈列上限'))
            }
            return callback()
          }
        }]
      }

      function valueChange (type, ref) {
        const eventKey = type === 1 ? 'onFieldChange' : 'onFieldBlur'
        const el = proxy.$refs[ref]
        el && (el[eventKey]())
      }

      function removeLimit (index) {
        state.editForm.limits.splice(index, 1)
        editRef.value.clearValidate([`areaId${index}`, `limit${index}`])
      }
      function addLimit () {
        state.editForm.limits.push({ areaId: undefined, limit: undefined })
      }
      return {
        ...toRefs(state),
        getData,
        handleTableChange,
        toShowEdit,
        save,
        editRef,
        deleteItem,
        toShowLog,
        toExport,
        formatter,
        parser,
        openImport,
        importVisOk,
        importVisCancel,
        importVisChange,
        downloadCheckFile,
        checkTitle,
        allOptions,
        specialRules,
        valueChange,
        removeLimit,
        addLimit
      }
    },
    data () {
      return {
        customRenderMap: new Map([
          [
            'number',
            (text) => <span>{ text || text === 0 ? formatNum(text) : '--' }</span>
          ],
          [
            'areaStr',
            (text) => <span class="lines-2">{ text || '--' }</span>
          ],
          [
            'text',
            (text) => <span>{ text || '--' }</span>
          ],
          [
            'action',
            (text, record) => <span>
              <a-button type="link" onClick={() => this.toShowEdit(record)}>编辑</a-button>
              <a-button type="link" onClick={() => this.deleteItem(record.configId)}>删除</a-button>
              <a-button type="link" onClick={() => this.toShowLog(record.configId)}>查看日志</a-button>
            </span>
          ]
        ])
      }
    },
    computed: {
      columns () {
        return originColumns.map(item => {
          const cacheItem = { ...item }
          cacheItem.align = 'center'
          cacheItem.customRender = this.customRenderMap.get(item.showKey) || this.customRenderMap.get(item.dataIndex) || this.customRenderMap.get('text')
          return cacheItem
        })
      }
    },
    render (createElement, context) {
      const {
        loading,
        dataSource,
        pagination,
        columns,
        handleTableChange,
        toShowEdit,
        editForm,
        save,
        logs,
        toExport,
        downloadLoading,
        exportLoading,
        formatter,
        parser,
        isAdd,
        logLoading,
        openImport,
        importVisOk,
        importVisCancel,
        importVisChange,
        downloadCheckFile,
        checkTitle,
        exportSingleLoading,
        allOptions,
        specialRules,
        valueChange,
        removeLimit,
        addLimit
      } = this
      return <div>
        <ni-table
          rowKey={(r, i) => i}
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={pagination}
          bordered
          align={'center'}
          onChange={handleTableChange}>
          <span slot="action" class="flex">
            <a-button type="primary" icon="import" onClick={() => openImport()}>导入</a-button>
            <a-button type="primary" class="ml-16" icon="export" loading={exportLoading} onClick={() => toExport(false)}>导出</a-button>
            <a-button type="primary" class="ml-16" icon="export" loading={exportSingleLoading} onClick={() => toExport(false, 'single')}>导出单店数据</a-button>
            <a-button type="primary" class="ml-16" onClick={() => toShowEdit(null)}>添加</a-button>
          </span>
        </ni-table>
        <a-modal
          title="导入"
          v-model={this.importVis}
          onOk={importVisOk}
          onCancel={importVisCancel}
        >
          <div class="flex flex-align-center">
            <div class="flex-child-noshrink flex flex-align-center">
              <span class="red mt-5 mr-5">*</span>
              <span>文件上传：</span>
            </div>
            <uploader buttonName={['选择文件']}
                      multiple={false}
                      fileList={this.importVisFileList}
                      onChange={(fileList, files) => importVisChange(fileList, files)}
                      accept=".xls,.xlsx"
                      showUploadAPP={false}
                      editFileName={false}
                      showUpload={!this.importVisFileList.length}/>
          </div>
          <p style="font-size: 12px;color: #606266;margin-top: 7px;">
            只支持.xls和.xlsx格式文件，文件小于1M且不超过1000行 <span class="blue pointer" onClick={() => toExport(true)}>下载模版</span>
          </p>
        </a-modal>
        <a-modal
          v-model={this.checkVisible}
          maskClosable={false}
          width={600}
          destroyOnClose
          title={checkTitle}
          footer={null}>
          <div class="mb-10">
            共{this.checkTotal || 0}条数据，其中
            <span class="green">{this.checkSuccess || 0}</span>条已校验成功，
            <span class="red">{this.checkFail || 0}</span>条校验失败
          </div>
          <div class="mt-20 flex flex-col flex-justify-center">
            <div class="mb-10">你可以下载校验过的数据表(错误原因在最后一列)，修改后再次导入，也可以在原表格修改后重新导入</div>
            <div class="flex flex-align-center flex-justify-center">
              <a-button style="width: 200px" type="primary" onClick={() => downloadCheckFile()}>下载校验过的数据表</a-button>
            </div>
          </div>
        </a-modal>
        <a-modal
          v-model={this.showEdit}
          maskClosable={false}
          destroyOnClose
          width={680}
          onOk={() => save()}
          title="陈列上限管理">
          <a-form-model
            ref="editRef"
            labelCol={{ span: 3 }}
            wrapperCol={{ span: 21 }}
            {...{ props: { model: editForm } }}
            rules={rules}>
            <a-form-model-item label="商品id" prop="pidStr">
              <a-select disabled={!isAdd} mode="tags" title="关键字" v-model={editForm.pidStr} allowClear
                        placeholder="输入商品id后敲回车确定"></a-select>
            </a-form-model-item>
            <a-form-model-item label="设置维度" prop="settingDimension">
              <a-radio-group disabled={!isAdd} v-model={editForm.settingDimension} options={options.settingDimensionOptions}/>
            </a-form-model-item>
            { editForm.settingDimension === 1 ? <a-form-model-item label="门店级别" prop="areaLevels">
              <a-select
                allowClear
                mode="multiple"
                placeholder="请选择"
                optionFilterProp="children"
                v-model={editForm.areaLevels}
                options={options.storeLevel}
              />
            </a-form-model-item> : null }
            { editForm.settingDimension === 1 ? <a-form-model-item label="陈列上限" prop="dpUpperLimit">
              <a-input-number
              v-model={editForm.dpUpperLimit}
              allowClear
              class="full-width"
              min={0}
              precision={0}
              max={999999999}
              placeholder="请输入"/>
              </a-form-model-item> : null }
            { editForm.settingDimension === 2 ? <div>
              { editForm.limits.map((item, index) => <div class="flex flex-wrap limits-box">
                <a-form-model-item
                  label="门店"
                  ref={'areaIdRef' + index}
                  prop={'areaId' + index}
                  auto-link={false}
                  rules={specialRules('areaId', item)}
                  labelCol={{ span: 7 }}
                  wrapperCol={{ span: 17 }}>
                  <NiAreaSelect
                    allowClear
                    mode={ 2 }
                    multiple={ false }
                    onChange={() => valueChange(1, 'areaIdRef' + index)}
                    onBlur={() => valueChange(2, 'areaIdRef' + index)}
                    class="full-width"
                    showSearch={true}
                    placeholder="请选择门店或者搜索"
                    v-model={item.areaId}/>
                </a-form-model-item>
                <a-form-model-item
                  label="陈列上限"
                  style="margin-left: 32px"
                  labelCol={{ span: 7 }}
                  wrapperCol={{ span: 17 }}
                  auto-link={false}
                  rules={specialRules('limit', item)}
                  ref={'limitRef' + index}
                  prop={'limit' + index}>
                  <a-input-number
                    onChange={() => valueChange(1, 'limitRef' + index)}
                    onBlur={() => valueChange(2, 'limitRef' + index)}
                    v-model={item.limit}
                    allowClear
                    class="full-width"
                    min={0}
                    precision={0}
                    max={999999999}
                    placeholder="请输入"/>
                </a-form-model-item>
                <span onClick={() => removeLimit(index)} class="nowrap delete">删除</span>
              </div>) }
              { editForm.limits?.length < 10 ? <a-button onClick={addLimit} type="primary" icon="plus" style="margin-left: 40px">添加</a-button> : null }
            </div> : null }
          </a-form-model>
        </a-modal>
        <a-modal
          v-model={this.showLog}
          maskClosable={false}
          destroyOnClose
          footer={null}
          title="日志">
          {logs?.length ? <NiLog
            data={logs}
            loading={logLoading}/> : <span class="grey-9">暂无数据</span>}
        </a-modal>
      </div>
    }
  })
</script>

<style scoped lang="scss">
.link {
  color: #1890ff;
  cursor: pointer;
  font-weight: 400;
}

.center {
  text-align: center;
}

.left {
  text-align: left;
}

.mr-26 {
  margin-right: 26px;
}

.fw-600 {
  font-weight: 600;
}
:deep(.limits-box) {
  .ant-form-item {
    width: calc((100% - 78px) / 2);
  }
}
.delete {
  margin-left: 18px;
  margin-top: 8px;
  color: #ff4d4f;
  cursor: pointer;
}
:deep(.ni-area-content) {
  height: 40px;
  box-sizing: border-box;
}
</style>
