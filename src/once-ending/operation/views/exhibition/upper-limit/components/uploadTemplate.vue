<script lang="jsx">
  import { defineComponent, getCurrentInstance, toRefs, reactive } from 'vue'
  import { to } from '~/util/common'

  export default defineComponent({
    props: {
      accept: {
        type: String,
        default: '.xlsx,xls'
      },
      uploadFun: {
        type: Function
      },
      params: {
        type: Object,
        default: () => {
          return {}
        }
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const data = reactive({
        loading: false,
        visible: false,
        total: 0,
        success: 0,
        fail: 0,
        link: '',
      })
      function beforeUpload (file) {
        const isXlsOrXlsx = file.name.includes('.xls') || file.name.includes('.xlsx')
        if (!isXlsOrXlsx) {
          proxy.$message.error('只支持.xls和.xlsx格式文件!')
          return false
        }
        toUpload(file)
        return false
      }

      async function toUpload (file) {
        const formData = new FormData()
        formData.append('file', file)
        data.loading = true
        proxy.$emit('loading', data.loading)
        const [err, res] = await to(props.uploadFun(formData, props.params))
        data.loading = false
        proxy.$emit('loading', data.loading)
        if (err) throw err
        const { code, userMsg } = res
        if (code === 0) {
          proxy.$message.success('数据导入成功')
          proxy.$emit('success')
        } else if (code === 5005) {
          data.visible = true
          Object.assign(data, res.data)
        } else {
          proxy.$message.error(userMsg)
        }
      }

      async function downloadCheckFile () {
        window.location.href = data.link
      }

      return {
        ...toRefs(data),
        beforeUpload,
        downloadCheckFile
      }
    },
    render () {
      const {
        beforeUpload,
        loading,
        downloadCheckFile,
        accept,
        total,
        success,
        fail,
      } = this
      const title = <span>
          <a-icon type="warning" class="red font-24"/>
          <span class="font-16 ml-10 bold">数据校验失败</span>
        </span>
      return <p>
        <a-upload
          name="file"
          accept={accept}
          before-upload={beforeUpload}
          show-upload-list={false}>
          { this.$slots.default || <a-button icon="import" loading={loading} type="primary">导入</a-button> }
        </a-upload>
        <a-modal
          v-model={this.visible}
          maskClosable={false}
          width={600}
          destroyOnClose
          title={title}
          footer={null}>
          <div class="mb-10">
            共{total || 0}条数据，其中
            <span class="green">{ success || 0 }</span>条已校验成功，
            <span class="red">{ fail || 0 }</span>条校验失败
          </div>
          <div class="mt-20">
            <div class="mb-10">你可以下载校验过的数据表(错误原因在最后一列)，修改后再次导入，
              也可以在原表格修改后重新导入
            </div>
            <a-button type="primary" onClick={downloadCheckFile}>下载校验过的数据表</a-button>
          </div>
        </a-modal>
      </p>
    }
  })
</script>
