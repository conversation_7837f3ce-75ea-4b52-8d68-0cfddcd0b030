<script lang="jsx">
  import { defineComponent, getCurrentInstance, ref, toRefs } from 'vue'
  import { NiFilter, NiFilterItem, NiAreaSelect, NiCategory } from '@jiuji/nine-ui'
  import { useState } from '../hooks/useState'
  import { options } from '../constants'
  import { Select } from 'ant-design-vue'
  import { PRODUCT_PID } from '@operation/store/modules/exhibition/action-types'

  export default defineComponent({
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect,
      NiCategory
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const {
        state,
        getData,
        allOptions
      } = useState()

      const productOptions = ref([])
      const keyWord = ref('')
      const loadProduct = ref(false)

      const getProductPid = async function (text) {
        productOptions.value = []
        if (text) {
          keyWord.value = text
          loadProduct.value = true
          const params = {
            world: text,
            limit: 20
          }
          const res = await proxy.$store.dispatch(
            `operation/exhibition/${PRODUCT_PID}`,
            params
          )
          loadProduct.value = false
          if (res) {
            productOptions.value = res.data
          }
        }
      }

      const productPidChange = function () {
        keyWord.value = ''
      }

      return {
        ...toRefs(state),
        getData,
        productOptions,
        getProductPid,
        loadProduct,
        productPidChange,
        allOptions
      }
    },
    render () {
      const {
        searchForm,
        loading,
        getData,
        allOptions
      } = this
      return (
        <ni-filter
          form={searchForm}
          onFilter={() => {
            getData(1)
          }}
          loading={loading}
        >
          <ni-filter-item>
            <a-input-group compact>
              <a-select
                style="width: 100px"
                v-model={searchForm.searchType}
                onChange={() => { searchForm.keyWord = undefined }}
                options={options.searchType}
              />
              {searchForm.searchType === 'productName' ? (
                <a-input
                  style="width: calc(100% - 100px)"
                  allowClear
                  v-model={searchForm.keyWord}
                  placeholder="请输入"
                />
              ) : (
                <a-input-number
                  style="width: calc(100% - 100px)"
                  allowClear
                  v-model={searchForm.keyWord}
                  placeholder="请输入"
                />
              )}
            </a-input-group>
          </ni-filter-item>
          <ni-filter-item label="门店级别">
            <a-select
              allowClear
              class="full-width"
              mode="multiple"
              placeholder="请选择"
              optionFilterProp="children"
              maxTagCount={1}
              v-model={searchForm.areaLevels}
              options={options.storeLevel}
            />
          </ni-filter-item>
          <ni-filter-item label="门店">
            <NiAreaSelect
              multiple
              allowClear
              mode={ 2 }
              placeholder="请选择门店或者搜索"
              maxTagCount={1}
              v-model={searchForm.areaIds}/>
          </ni-filter-item>
        </ni-filter>
      )
    }
  })
</script>

<style lang="scss" scoped>
.form-item {
  display: flex;
  // flex-wrap: wrap;
  align-items: center;
  padding-right: 16px;

  .label {
    width: 60px;
    text-align: right;
    margin-right: 5px;
  }
  .control,
  .ant-select {
    width: calc(100% - 65px);
    height: 32px;
    :deep(.ant-select-selection--multiple) {
      height: 32px;
    }
  }
}
.label {
  text-align: right;
}

:deep(.ant-input) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
:deep(.ant-select-selection__choice__remove) {
  visibility: hidden;
}
:deep(.ant-select-selection__choice) {
  max-width: 45%;
}
</style>
