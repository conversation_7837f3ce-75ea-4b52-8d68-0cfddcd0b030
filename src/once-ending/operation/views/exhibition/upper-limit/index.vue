<script lang="jsx">
  import filterBox from './components/filter-box'
  import tableBox from './components/table-box'
  import { NiListPage } from '@jiuji/nine-ui'
  import { defineComponent } from 'vue'
  import { createState } from './hooks/useState'

  export default defineComponent({
    name: 'index',
    components: { filterBox, tableBox, NiListPage },
    setup () {
      createState()
    },
    render (createElement, context) {
      const { getData } = this
      return <page>
        <ni-list-page pushFilterToLocation={false}>
          <filter-box/>
          <table-box
            class="mt-16"/>
        </ni-list-page>
      </page>
    }
  })
</script>

<style scoped>

</style>
