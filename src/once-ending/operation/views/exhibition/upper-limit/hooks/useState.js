import { reactive, provide, inject, ref, getCurrentInstance } from 'vue'
import { cloneDeep } from 'lodash'
import { originEditForm } from '../constants'
import exhibitionApi from '@operation/api/exhibition'
import { to } from '~/util/common'
import axios from 'axios'
import moment from 'moment/moment'
import { GET_DP_FIX_PHOTO_ENUMS } from '@/operation/store/modules/exhibition/action-types'

const key = Symbol('key')

export function useState () {
  return inject(key)
}

export function createState () {
  const { proxy } = getCurrentInstance()
  const state = reactive({
    searchForm: {
      searchType: 'productName',
      keyWord: undefined,
      areaLevels: undefined,
      areaIds: undefined,
    },
    loading: false,
    dataSource: [],
    pagination: {
      pageSize: 10,
      current: 1,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      pageSizeOptions: ['10', '20', '30', '50']
    },
    showEdit: false,
    showLog: false,
    logs: [],
    logLoading: false,
    editForm: cloneDeep(originEditForm),
    editLoading: false,
    downloadLoading: false,
    exportLoading: false,
    exportSingleLoading: false,
    cacheParams: {},
    isAdd: false,
    importVis: false,
    importVisFileList: [],
    importVisFiles: [],
    checkVisible: false,
    checkTotal: 0,
    checkSuccess: 0,
    checkFail: 0,
    failFileLink: ''
  })

  const formatter = value => value.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  const parser = value => value.replace(/\$\s?|(,*)/g, '')

  async function getData () {
    const { searchForm: { searchType, keyWord, ...other }, pagination: { current, pageSize } } = state
    const params = {
      ...other,
      current,
      size: pageSize,
      // pid: keyWord // 商品名称与商品id都传pid
    }
    params[searchType] = keyWord
    state.cacheParams = params
    state.loading = true
    const [err, res] = await to(exhibitionApi.upperLimit.getList(params))
    state.loading = false
    if (err) throw err
    const { code, userMsg, data } = res
    if (code === 0) {
      const { records, total } = data
      state.dataSource = records
      state.pagination.total = total
    } else {
      proxy.$message.error(userMsg)
    }
  }

  async function toExport (template = false, type) {
    const apiKey = type === 'single' ? 'exportSingleList' : 'exportList'
    const url = exhibitionApi.upperLimit[apiKey]()
    const fileName = type === 'single' ? '单店数据' : ('陈列上限设置' + (template ? '模板' : ''))
    const key = type === 'single' ? 'exportSingleLoading' : template ? 'downloadLoading' : 'exportLoading'
    state[key] = true
    const params = {
      ...state.cacheParams,
      templateFlag: template
    }
    await exportData(url, fileName, !template, params)
    state[key] = false
  }

  function exportData (url, fileName = '', useTime = true, params = {}) {
    return new Promise((resolve, reject) => {
      axios({
        method: 'post',
        url: url,
        responseType: 'blob',
        timeout: 60 * 1000,
        data: params,
        headers: {
          Authorization: proxy.$store.state.token
        }
      })
        .then(res => {
          const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          const reader = new FileReader()
          reader.readAsText(blob, 'utf8')
          reader.addEventListener('loadend', () => {
            try {
              const result = JSON.parse(reader.result)
              this.$message.error(result.userMsg)
              reject(result.userMsg)
            } catch (e) {
              const objectUrl = URL.createObjectURL(blob)
              const link = document.createElement('a')
              link.download = `${fileName}${useTime ? moment().format('YYYYMMDDHHmmss') : ''}.xlsx`
              link.style.display = 'none'
              link.href = objectUrl
              document.body.appendChild(link)
              link.click()
              resolve()
            }
          })
        })
        .catch(e => {
          console.log(e)
          reject(e)
          this.$message.error('下载失败')
        })
    })
  }

  async function handleTableChange (paginationObj) {
    Object.assign(state.pagination, paginationObj)
    getData()
  }

  async function toShowEdit (record) {
    state.isAdd = !record
    state.showEdit = true
    if (record) {
      const res = await exhibitionApi.upperLimit.getDetail({ configId: record.configId }).catch(err => {
        proxy.$message.error(err.message)
      })
      if (res) {
        Object.assign(state.editForm, {
          ...res,
          pidStr: [res.pidStr],
          areaLevels: res.areaLevels?.split(',')?.map(it => it) || []
        })
      }
    } else {
      state.editForm = cloneDeep(originEditForm)
    }
  }

  const editRef = ref()
  function save () {
    editRef.value.validate(async valid => {
      if (valid) {
        const { isAdd, editForm: { areaLevels, pidStr } } = state
        if (isAdd) {
          state.editLoading = true
          const pidStrNew = pidStr.join(',')
          const [err, res] = await to(exhibitionApi.upperLimit.checkPidExists({ pidStr: pidStrNew }))
          state.editLoading = false
          if (err) throw err
          const { code, userMsg } = res
          if (code !== 0) return proxy.$message.error(userMsg)
        }
        const params = {
          ...state.editForm,
          areaLevels: areaLevels ? areaLevels?.join(',') : undefined,
          pidStr: state.editForm.pidStr.join(',')
        }
        state.editLoading = true
        const [err, res] = await to(exhibitionApi.upperLimit.addOrEdit(params))
        state.editLoading = false
        if (err) throw err
        const { code, userMsg } = res
        if (code === 0) {
          state.showEdit = false
          proxy.$message.success(`${isAdd ? '添加' : '编辑'}成功`)
          getData(isAdd ? 'search' : '')
        } else {
          proxy.$message.error(userMsg)
        }
      } else {
        proxy.$message.warning('请先完善数据')
      }
    })
  }

  function deleteItem (configId) {
    proxy.$confirm({
      title: '确认删除该商品吗？',
      onOk: async () => {
        const [err, res] = await to(exhibitionApi.upperLimit.deleteItem({ configId: configId }))
        if (err) throw err
        const { code, userMsg } = res
        if (code === 0) {
          proxy.$message.success(`删除成功`)
          getData()
        } else {
          proxy.$message.error(userMsg)
        }
      }
    })
  }

  async function toShowLog (configId) {
    state.showLog = true
    state.logLoading = true
    const [err, res] = await to(exhibitionApi.upperLimit.getLogList({ configId: configId }))
    state.logLoading = false
    if (err) throw err
    const { code, userMsg, data } = res
    if (code === 0) {
      state.logs = data?.map(it => ({
        ...it,
        time: it.operateTime
      }))
    } else {
      proxy.$message.error(userMsg)
    }
  }
  const openImport = () => {
    state.importVis = true
  }
  const importVisOk = async () => {
    if (!state.importVisFileList.length) return proxy.$message.warning('请先选择文件')
    proxy.$indicator.open()
    const formData = new FormData()
    formData.append('file', state.importVisFiles[0])
    const [err, res] = await to(exhibitionApi.upperLimit.toUpload(formData))
    proxy.$indicator.close()
    if (err) throw err
    const { code, userMsg } = res
    if (code === 0) {
      proxy.$message.success('数据导入成功')
      state.importVis = false
      state.importVisFileList = []
      await getData('search')
    } else if (code === 5005) {
      state.checkVisible = true
      state.checkTotal = res.data.total || 0
      state.checkSuccess = res.data.success || 0
      state.checkFail = res.data.fail || 0
      state.failFileLink = res.data.link || ''
    } else {
      proxy.$message.error(userMsg)
    }
  }
  const importVisCancel = () => {
    state.importVis = false
    state.importVisFileList = []
  }
  const importVisChange = (fileList, files) => {
    if (fileList) {
      state.importVisFileList = fileList
    } else {
      state.importVisFileList = []
    }
    if (files) {
      state.importVisFiles = files
    } else {
      state.importVisFiles = []
    }
  }
  const downloadCheckFile = () => {
    if (state.failFileLink) {
      window.location.href = state.failFileLink
      state.checkVisible = false
      setTimeout(() => {
        state.failFileLink = ''
      }, 5000)
    } else {
      proxy.$message.warning('没有可下载的文件')
    }
  }

  const allOptions = reactive({
    photoLevelOptions: [],
  })
  const getEnums = async function () {
    const res = await proxy.$store.dispatch(
      `operation/exhibition/${GET_DP_FIX_PHOTO_ENUMS}`
    )
    if (res) {
      allOptions.photoLevelOptions = res.data.photoLevel
    }
  }
  getEnums()
  const o = {
    state,
    getData,
    handleTableChange,
    toShowEdit,
    save,
    editRef,
    deleteItem,
    toShowLog,
    toExport,
    formatter,
    parser,
    openImport,
    importVisOk,
    importVisCancel,
    importVisChange,
    downloadCheckFile,
    allOptions
  }
  provide(key, o)
  return o
}
