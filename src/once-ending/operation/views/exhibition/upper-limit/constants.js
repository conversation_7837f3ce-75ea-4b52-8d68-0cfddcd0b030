export const originColumns = [
  {
    title: '商品id',
    dataIndex: 'pid',
    width: 120
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
    width: 300
  },
  {
    title: '门店级别',
    dataIndex: 'areaLevelStr',
    width: 120
  },
  {
    title: '门店',
    dataIndex: 'areaStr',
    width: 200
  },
  {
    title: '陈列上限',
    dataIndex: 'dpUpperLimit',
    width: 120,
    // showKey: 'number'
  },
  {
    title: '可陈列商品总数',
    dataIndex: 'canDisplayCount',
    width: 120,
    // showKey: 'number'
  },
  {
    title: '陈列总数',
    dataIndex: 'hasDisplayCount',
    width: 120,
    // showKey: 'number'
  },
  {
    title: '瑕疵总数',
    dataIndex: 'toBlemishCount',
    width: 120,
    // showKey: 'number'
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 200,
  },
]

export const options = {
  searchType: [
    { label: '商品名称', value: 'productName' },
    { label: '商品id', value: 'pid' },
  ],
  storeLevel: window.tenant?.xtenant < 1000 ? [
    { label: '旗舰门店', value: '1' },
    { label: '主力门店', value: '2' },
    { label: '标准门店', value: '3' },
    { label: '潜力门店', value: '4' },
  ] : [
    { label: '超级', value: '1' },
    { label: '一级', value: '2' },
    { label: '二级', value: '3' },
    { label: '三级', value: '4' },
    { label: '四级', value: '5' },
    { label: '五级', value: '6' },
    { label: '新开', value: '7' }
  ],
  settingDimensionOptions: [
    { label: '门店级别', value: 1 },
    { label: ' 门店', value: 2 },
  ]
}

export const originEditForm = {
  pidStr: undefined,
  dpUpperLimit: undefined,
  areaLevels: undefined,
  settingDimension: 1,
  limits: [
    { areaId: undefined, limit: undefined }
  ]
}

export const rules = {
  pidStr: [{ required: true, trigger: ['change', 'blur'], message: '请输入商品id' }],
  dpUpperLimit: [{ required: true, trigger: ['change', 'blur'], message: '请输入陈列上限' }],
  areaLevels: [{ required: true, trigger: ['change', 'blur'], message: '请选择门店级别' }],
  settingDimension: [{ required: true, trigger: ['change', 'blur'], message: '请选择维度' }],
}

export function formatNum (value) {
  if (isNaN(value)) return value
  let _value = value
  if (!_value) {
    return '0'
  }
  if (typeof parseFloat(_value) !== 'number') {
    return '0'
  }
  if (typeof _value === 'string') {
    // 处理大于2位的小数字符串
    if (_value.match('.') && _value.split('.')[1]?.length > 2) {
      _value = parseFloat(_value).toFixed(2)
    } else {
      // _value = parseFloat(_value).toString()
    }
  } else if (typeof _value === 'number') {
    // 处理大于2位的小数
    if (
      _value.toString().match('.') &&
      _value.toString().split('.')[1]?.length > 2
    ) {
      _value = _value.toFixed(2)
    } else {
      _value = _value.toString()
    }
  }
  const cache = new RegExp(/\B(?=(\d{3})+(?!\d))/g)
  return _value.replace(cache, ',') // 转千分位
}
