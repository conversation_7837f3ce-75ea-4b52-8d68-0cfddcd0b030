import { ref } from 'vue'
import exhibition from '@operation/api/exhibition'
import { to } from '@common/utils/common'
import { message } from 'ant-design-vue'
export const useLabelList = function () {
  const labelList = ref([])
  // 获取陈列柜标签
  const getLabelList = async function () {
    const [err, res] = await to(exhibition.dpCabinetLabelSimpleList())
    if (err) throw err
    const { code, data, userMsg } = res
    if (code === 0) {
      labelList.value = data.map(d => {
        return {
          value: d.id,
          label: d.labelName
        }
      })
    } else {
      message.error(userMsg)
    }
  }
  getLabelList()

  return {
    labelList
  }
}
