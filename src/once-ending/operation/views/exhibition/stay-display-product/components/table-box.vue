<template>
  <div>
    <ni-table
      :rowKey="r => r.id"
      :bordered="true"
      :pagination="pagination"
      :loading="loading"
      :columns="columns"
      :dataSource="dataSource">
    </ni-table>
  </div>
</template>

<script setup lang="jsx">
  import { toRefs, computed } from 'vue'
  import { useState } from '../hooks/useState'
  import { NiTable } from '@jiuji/nine-ui'
  import { originColumns } from '../constants'
  import { formatNum } from '@operation/views/exhibition/upper-limit/constants'

  const { state, fetchData } = useState()
  const { pagination, loading, dataSource } = toRefs(state)

  const customRenderMap = new Map([
    [
      'specialAreaName',
      (text, record) => {
        return {
          children: text && text.length ? <div>
            {text}{record.area ? `（${record.area}）` : null}
          </div> : <span>--</span>,
          attrs: {
            rowSpan: record.show ? record.rowLength : 0,
          }
        }
      }
    ],
    [
      'storeStockAge',
      text => <span>{text || text === 0 ? `${formatNum(text)}天` : '--'}</span>
    ],
    [
      'normal',
      (text) => <p>{ (text || text === 0) ? text : '--' }</p>
    ],
  ])

  const columns = computed(() => {
    return originColumns.map(d => {
      d.align = 'center'
      d.customRender = customRenderMap.get(d.dataIndex) || customRenderMap.get('normal')
      return d
    })
  })

</script>

<style scoped lang="scss">
</style>
