<template>
  <ni-filter
    :form="searchForm"
    :loading="loading"
    :immediate="false"
    @filter="fetchData('search')"
    @reset="reset">
    <ni-filter-item label="地区">
      <ni-area-select
        v-model="searchForm.areaIds"
        :multiple="true"
        :mode="2"
        placeholder="请选择地区"
        :max-tag-count="1"/>
    </ni-filter-item>
    <ni-filter-item label="陈列专区">
      <a-select
        placeholder="请选择"
        mode="multiple"
        :maxTagCount="1"
        allowClear
        option-filter-prop="children"
        :options="specialAreaOptions"
        v-model="searchForm.specialAreaIds"/>
    </ni-filter-item>
    <ni-filter-item>
      <a-input-group compact style="display: flex;min-width: 280px">
        <a-select
          @change="() => searchForm.keyWord = undefined"
          style="width:35%"
          v-model="searchForm.tag"
          :options="searchTypeOptions.slice(0, 4)"/>
        <a-select
          v-if="searchForm.tag === 'productName'"
          ref="product"
          style="width: 65%"
          @search="getProductPid"
          v-model="searchForm.keyWord"
          placeholder="请输入"
          allowClear
          :filter-option="false"
          :default-active-first-option="false"
          :showArrow="false"
          :showSearch="true"
          :dropdownMatchSelectWidth="false"
          :getPopupContainer="triggerNode => triggerNode?.parentNode">
          <a-select-option v-for="item in productOptions" :key="item.productId" :value="item.productId">
            {{ item.name }}
          </a-select-option>
          <template slot="notFoundContent">
            <div class="full-width flex flex-center"
                 v-if="loadProduct"
                 style="height: 60px">
              <a-spin />
          </div>
        </template>
        </a-select>
        <a-input
          v-else
          type="number"
          style="width: 70%"
          allowClear
          v-model="searchForm.keyWord"
          placeholder="请输入"/>
      </a-input-group>
    </ni-filter-item>
    <ni-filter-item label="分类">
      <Category
        :max-tag-count="1"
        v-model="searchForm.cidList"
        @change="() => { searchForm.brandIdList = [] }"
      />
    </ni-filter-item>
    <ni-filter-item label="品牌">
      <Brand
        :cateId="searchForm.cidList"
        v-model="searchForm.brandIdList"/>
    </ni-filter-item>
  </ni-filter>
</template>

<script setup>
  import { toRefs } from 'vue'
  import { useState } from '../hooks/useState'
  import { NiFilter, NiAreaSelect } from '@jiuji/nine-ui'
  import { searchTypeOptions } from '../../goods-display/constants'
  import Category from '@/operation/components/category.vue'
  import Brand from '@/operation/components/brand.vue'

  const { state, fetchData, reset, getProductPid } = useState()
  const { loading, searchForm, specialAreaOptions, productOptions, loadProduct } = toRefs(state)

</script>

<style scoped lang="scss">

</style>
