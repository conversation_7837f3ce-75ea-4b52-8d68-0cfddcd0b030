import { reactive, inject, provide, ref, getCurrentInstance } from 'vue'
import { originSearchForm } from '../constants'
import { cloneDeep, debounce } from 'lodash'
import moment from 'moment/moment'
import exhibitionApi from '~/once-ending/operation/api/exhibition'

const key = Symbol('useState')
export const useState = function () {
  return inject(key)
}
export const createState = function () {
  const { proxy } = getCurrentInstance()

  const state = reactive({
    searchForm: cloneDeep(originSearchForm),
    loading: false,
    specialAreaOptions: [],
    productOptions: [],
    keyWord: undefined,
    loadProduct: false,
    pagination: {
      pageSize: 1,
      current: 1,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      pageSizeOptions: ['1', '3', '5', '10']
    },
    dataSource: [],
    expandedRowKeys: []
  })

  function reset () {
    state.searchForm = cloneDeep(originSearchForm)
  }

  const getForm = function () {
    const { form } = proxy.$route.query
    if (form) {
      const p = JSON.parse(form)
      Object.assign(state.searchForm, p)
    }
    fetchData('search')
  }
  getForm()

  async function fetchData (type) {
    type === 'search' && (state.pagination.current = 1)
    const { pagination: { current, pageSize }, searchForm: { keyWord, tag, ...other } } = state
    const params = {
      current,
      size: pageSize,
      ...other
    }
    if (keyWord) {
      if (tag === 'productName') {
        params.pid = keyWord
      } else {
        params[tag] = keyWord
      }
    }
    state.loading = true
    const res = await exhibitionApi.getStayProduct(params)
    state.loading = false
    state.dataSource = dealData(res?.records || [])
    console.log('state.dataSource', state.dataSource)
    state.pagination.total = res?.total || 0
  }
  function dealData (arr) {
    const cacheData = []
    arr?.forEach(first => {
      if (first.children.length) {
        first.children.forEach((second, secondIndex) => {
          cacheData.push({
            ...second,
            specialAreaName: first.specialAreaName,
            area: first.area,
            show: secondIndex === 0,
            rowLength: secondIndex === 0 ? first.children?.length || 0 : 0
          })
        })
      } else {
        cacheData.push({
          ...first,
          show: true,
          rowLength: 1,
          specialAreaName: first.specialAreaName,
          area: first.area,
        })
      }
    })
    return cacheData
  }
  async function getEnums () {
    const res = await exhibitionApi.getDpFixPhotoEnums()
    if (res?.code === 0) {
      state.specialAreaOptions = res?.data?.specialArea
    }
  }
  getEnums()

  let getProductPid = async function (text) {
    state.productOptions = []
    if (text) {
      state.keyWord = text
      state.loadProduct = true
      let timestamp = new Date().getTime()
      let params = {
        timestamp: timestamp,
        kind: 'all', // 所有商品 不区分业务场景
        isproduct: 1, // 仅搜索商品维度，不包含规格
        limit: 10,
        q: encodeURIComponent(text)
      }
      const res = await proxy.$api.recovery.productAutoData(params)
      state.loadProduct = false
      if (res) {
        state.productOptions = res.data
      }
    }
  }
  getProductPid = debounce(getProductPid, 500)

  const o = {
    state,
    reset,
    fetchData,
    getProductPid
  }
  provide(key, o)
  return o
}
