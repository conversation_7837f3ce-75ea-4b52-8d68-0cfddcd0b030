<template>
  <page>
    <ni-list-page :pushFilterToLocation="false">
      <filter-box />
      <table-box />
    </ni-list-page>
  </page>
</template>

<script setup>
  import { createState } from './hooks/useState'
  import { NiListPage } from '@jiuji/nine-ui'
  import FilterBox from './components/filter-box.vue'
  import TableBox from './components/table-box.vue'

  createState()
</script>

<style scoped lang="scss">

</style>
