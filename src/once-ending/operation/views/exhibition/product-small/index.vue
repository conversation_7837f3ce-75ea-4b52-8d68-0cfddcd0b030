<script lang="jsx">
  import { ref, reactive, getCurrentInstance } from 'vue'
  import { GET_SMALL_LIST } from '@operation/store/modules/exhibition/action-types'
  import { columns } from './constants'
  import { NiTable } from '@jiuji/nine-ui'
  import moment from 'moment'
  import axios from 'axios'
  import exhibition from '@operation/api/exhibition'
  export default {
    name: 'index',
    components: { NiTable },
    setup () {
      const { proxy } = getCurrentInstance()
      const dataSource = ref([])
      const pagination = reactive({
        pageSize: 10,
        current: 1,
        total: 0
      })
      const loading = ref(false)
      const getDatasource = async function (current) {
        current && (pagination.current = current)
        const params = {
          current: pagination.current,
          size: pagination.pageSize,
        }
        const res = await proxy.$store.dispatch(`operation/exhibition/${GET_SMALL_LIST}`, params)
        loading.value = false
        if (res) {
          const { records, total } = res.data
          dataSource.value = records
          pagination.total = total
        }
      }
      getDatasource()

      const changeTable = function (page) {
        Object.assign(pagination, page)
        getDatasource()
      }

      const downloadLoading = ref(false)
      const exportSmallProduct = async function () {
        downloadLoading.value = true
        axios({
          method: 'post',
          url: exhibition.exportSmallExcel(),
          responseType: 'blob',
          data: {},
          timeout: 60000,
          headers: {
            Authorization: proxy.$store.state.token,
            scene: 'small_product'
          }
        }).then(res => {
          if (res) {
            const link = document.createElement('a')
            let blob = new Blob([res.data], { type: 'application/x-excel' })
            link.style.display = 'none'
            link.href = URL.createObjectURL(blob)
            link.download = `小件陈列商品参考清单(${moment().format('YYYY-MM-DD HH：mm')}).xlsx`
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
          }
        }).finally(() => {
          downloadLoading.value = false
        })
      }
      return {
        dataSource,
        pagination,
        loading,
        downloadLoading,
        changeTable,
        exportSmallProduct
      }
    },
    render (createElement, context) {
      const { dataSource, pagination, changeTable, loading, exportSmallProduct, downloadLoading } = this
      return <page>
        <NiTable
          pagination={pagination}
          dataSource={dataSource}
          columns={columns}
          loading={loading}
          bordered
          align={'center'}
          onChange={changeTable}
          rowKey={(r, i) => i}
        >
          <div slot="tool" class="flex flex-justify-end">
            <a-button loading={downloadLoading} onClick={exportSmallProduct}>导出</a-button>
          </div>
        </NiTable>
      </page>
    }
  }
</script>

<style scoped lang="scss">
</style>
