<script lang="jsx">
  import { defineComponent, getCurrentInstance } from 'vue'
  import { NiListPage } from '@jiuji/nine-ui'
  import searchBox from './components/search-box.vue'
  import tableBox from './components/table-box.vue'
  import edit from './components/modals/edit'
  import logs from './components/modals/logs'
  import feedback from './components/modals/feedback'
  import { createState } from './hooks/use-state'
  import BtnRouter from '@operation/components/btn-router'
  export default defineComponent({
    components: { NiListPage, searchBox, tableBox, BtnRouter, edit, logs, feedback },
    setup () {
      const { proxy } = getCurrentInstance()
      createState({ proxy })
    },
    render () {
      return <page>
        <div slot="extra">
          <BtnRouter
            routerName="STORE_ExhibitionSingleManage"
            type="primary"
            text="单品陈列管理"
          />
        </div>
        <ni-list-page pushFilterToLocation={false}>
          <searchBox/>
          <tableBox/>
        </ni-list-page>
        <edit/>
        <logs/>
        <feedback/>
      </page>
    }
  })
</script>
<style scoped>

</style>
