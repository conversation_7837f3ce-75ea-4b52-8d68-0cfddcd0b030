export const options = {
  searchType: [
    { label: '商品id', value: 1 },
    { label: 'ppid', value: 2 },
    { label: '商品名称', value: 3 },
    { label: 'ID', value: 4 },
  ],
  timeType: [
    { label: '反馈截止时间', value: 1 },
    { label: '下发时间', value: 2 },
    { label: '审核时间', value: 3 },
  ],
  status: [
    { label: '待反馈', value: 1 },
    { label: '待审核', value: 3 },
    { label: '已反馈', value: 2 },
  ],
  overTime: [
    { label: '未超时', value: 1 },
    { label: '已超时', value: 2 },
  ],
  productType: [
    { label: '大件', value: 1 },
    { label: '小件', value: 2 }
  ],
  idType: [
    { label: '商品id', value: 1 },
    { label: 'ppid', value: 2 }
  ]
}

export const columns = [
  {
    title: '小区',
    dataIndex: 'smallDepartName',
    width: '100px',
    fixed: 'left'
  },
  {
    title: '门店',
    dataIndex: 'areaCode',
    width: '100px',
    fixed: 'left'
  },
  {
    title: 'ID',
    dataIndex: 'id',
    width: '80px'
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
    width: '280px',
  },
  {
    title: 'ppid',
    dataIndex: 'ppid',
    width: '100px'
  },
  {
    title: '商品id',
    dataIndex: 'productId',
    width: '80px'
  },
  {
    title: '门店陈列标准',
    dataIndex: 'displayStandard',
    width: '140px'
  },
  {
    title: '陈列标准说明',
    dataIndex: 'displayDescription',
    width: '260px'
  },
  {
    title: '门店当前陈列',
    dataIndex: 'displayCurrent',
    width: '140px'
  },
  {
    title: '陈列反馈说明',
    dataIndex: 'feedbackNote',
    width: '180px'
  },
  {
    title: '下发时间',
    dataIndex: 'startTime',
    width: '150px'
  },
  {
    title: '激活时间',
    dataIndex: 'activateTime',
    width: '150px'
  },
  {
    title: '反馈截止时间',
    dataIndex: 'feedbackEndTime',
    width: '150px'
  },
  {
    title: '反馈超时情况',
    dataIndex: 'timeOutText',
    width: '130px'
  },
  {
    title: '状态',
    dataIndex: 'statusText',
    width: '70px'
  },
  {
    title: 'AI匹配度',
    dataIndex: 'matchPercent',
    width: '150px',
  },
  {
    title: 'AI审核',
    dataIndex: 'aiAuditName',
    width: '100px'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '280px',
    fixed: 'right',
  },
]
