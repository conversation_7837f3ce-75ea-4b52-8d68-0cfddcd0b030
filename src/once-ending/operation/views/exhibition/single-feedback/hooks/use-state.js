import { ref, reactive, provide, inject } from 'vue'
import { to } from '@common/utils/common'
import { message } from 'ant-design-vue'
import exhibition from '@operation/api/exhibition'
import axios from 'axios'
import moment from 'moment'

const { singleDisplay } = exhibition
export async function commApi (api, type, params = {}) {
  const [err, res] = await to(api[type](params))
  if (err) throw err
  const { code, userMsg } = res
  if (code !== 0) {
    message.error(userMsg)
    return
  }
  return res
}
const key = Symbol('useState')

export function useState () {
  return inject(key)
}

export function disabledDate (current) {
  const nowDate = moment(new Date()).format('YYYY-MM-DD')
  const currentDate = moment(current).format('YYYY-MM-DD')
  return moment(currentDate).valueOf() < moment(nowDate).valueOf()
}

export function disabledTime (selectTime) {
  function range (start, end) {
    const result = []
    for (let i = start; i < end; i++) {
      result.push(i)
    }
    return result
  }
  const nowDate = new Date()
  let selectAfterTime = false

  let selectHour
  let selectMinute
  if (selectTime) {
    const nowDateTime = moment(nowDate).format('YYYY-MM-DD')
    const selectDateTime = moment(selectTime).format('YYYY-MM-DD')
    const cacheT = new Date(moment(selectTime).format('YYYY-MM-DD HH:mm:ss'))
    selectHour = cacheT.getHours()
    selectMinute = cacheT.getMinutes()
    if (moment(nowDateTime).valueOf() < moment(selectDateTime).valueOf()) {
      selectAfterTime = true
    }
  }
  const hour = nowDate.getHours()
  const minute = nowDate.getMinutes()
  const second = nowDate.getSeconds()
  return { // 根据当前选择时间判断是否禁用时分秒
    disabledHours: () => selectAfterTime ? [] : range(0, hour),
    disabledMinutes: () => (selectAfterTime || selectHour > hour) ? [] : range(0, minute),
    disabledSeconds: () => (selectAfterTime || selectHour > hour || selectMinute > minute) ? [] : range(0, second)
  }
}

export function createState ({ proxy }) {
  const state = reactive({
    searchForm: {
      areaIds: undefined,
      aiAudits: undefined,
      searchKey: 1,
      searchValue: undefined,
      feedbackStatus: undefined,
      timeOut: undefined,
      productType: undefined,
      timeType: 1,
      timeValue: undefined,
      singleId: undefined,
      auditUserId: undefined,
    },
    pagination: {
      pageSize: 10,
      current: 1,
      total: 60,
      showSizeChanger: true,
      showQuickJumper: true,
      pageSizeOptions: ['10', '20', '30', '50']
    },
    loading: false,
    dataSource: [],
    editForm: {
      areaId: undefined,
      productType: undefined,
      pidOrPpid: undefined,
      displayStandard: undefined,
      displayDescription: undefined,
      feedbackEndTime: undefined
    },
    showEdit: false,
    editLoading: false,
    showFeedback: false,
    feedbackForm: {
      feedbackId: undefined,
      reason: undefined,
      feedbackEndTime: undefined
    },
    feedbackLoading: false,
    showLog: false,
    logs: [],
    loadingLogs: false,
    exportLoading: false,
    cacheParams: {}
  })
  const selectedRowKeys = ref([])
  async function fetchData (currentPage) {
    currentPage && (state.pagination.current = currentPage)
    const { productType, timeValue } = state.searchForm
    const { current, pageSize } = state.pagination
    const params = {
      ...state.searchForm,
      large: productType?.includes(1) || undefined,
      small: productType?.includes(2) || undefined,
      startTime: timeValue?.length ? timeValue[0] + ' 00:00:00' : undefined,
      endTime: timeValue?.length ? timeValue[1] + ' 23:59:59' : undefined,
      current,
      size: pageSize
    }
    state.cacheParams = { ...params }
    state.loading = true
    const res = await commApi(singleDisplay, 'getFeedbackList', params)
    state.loading = false
    if (res) {
      const { records, total } = res.data
      state.dataSource = records
      state.pagination.total = total
      selectedRowKeys.value = []
    }
  }
  function handleTableChange (newPage) {
    Object.assign(state.pagination, newPage)
    fetchData(null)
  }
  const { search } = proxy.$route.query
  search && (Object.assign(state.searchForm, { ...JSON.parse(search) }))
  fetchData(1)
  // async function getEnums () {
  //   const res = await commApi(singleDisplay, 'getEnums')
  //   if (res) {}
  // }
  // getEnums()
  function toShowEdit (record) {
    state.editForm = { ...record }
    state.editForm.areaId = record.areaId + ''
    state.showEdit = true
  }
  async function approvalHandle (record, flag) {
    const params = {
      feedbackId: record.id,
      approvalFlag: flag
    }
    state.loading = true
    const res = await commApi(singleDisplay, 'approvalHandle', params)
    console.log(res)
    if (res.code === 0) {
      message.success('审核操作成功')
    } else {
      message.error(res.userMsg)
    }
    await fetchData(null)
    state.loading = false
  }
  async function toSave () {
    console.log('proxy', proxy)
    const { id, areaId, productType, pidOrPpid, displayStandard, displayDescription, feedbackEndTime, activateTime } = state.editForm
    if (!displayStandard) {
      return proxy.$message.warning('请上传陈列标准图片')
    }
    // if (!feedbackEndTime) {
    //   return proxy.$message.warning('请选择反馈截止时间')
    // }
    const params = {
      id,
      areaId,
      productType,
      pidOrPpid,
      displayStandard,
      displayDescription,
      feedbackEndTime,
      activateTime: activateTime ? moment(activateTime).format(`YYYY-MM-DD${proxy.$tnt.xtenant < 1000 ? ' HH:mm:ss' : ''}`) : undefined
    }
    state.editLoading = true
    const res = await commApi(singleDisplay, 'saveFeedback', params)
    state.editLoading = false
    if (res) {
      message.success('保存成功')
      state.showEdit = false
      fetchData(null)
    }
  }

  async function refeedback () {
    const { feedbackEndTime, feedbackId, ...other } = state.feedbackForm
    if (!feedbackEndTime) {
      return proxy.$message.warning('请选择反馈截止时间')
    }
    state.feedbackLoading = true
    const params = { feedbackEndTime, ...other }
    if (feedbackId) {
      params.feedbackId = feedbackId
    } else {
      params.feedbackIdList = selectedRowKeys.value
    }
    const res = await commApi(singleDisplay, feedbackId ? 'refeedback' : 'batchRefeedback', params)
    state.feedbackLoading = false
    if (res) {
      message.success('重新反馈成功')
      state.showFeedback = false
      fetchData(null)
    }
  }

  function toDelete (record) {
    proxy.$confirm({
      title: '确定删除此条数据吗？',
      onOk: async () => {
        const res = await commApi(singleDisplay, 'deleteDisplay', record.id)
        if (res) {
          message.success('删除成功')
          const { pagination: { total, current }, dataSource } = state
          if (total % 10 === 1 && record.id === dataSource[dataSource.length - 1].id && current > 1) {
            state.pagination.current -= 1
          }
          fetchData(null)
        }
      }
    })
  }
  function toReset (obj) {
    Object.keys(obj).forEach(key => {
      obj[key] = undefined
    })
  }

  function toFeedback (record) {
    if (!record && !selectedRowKeys.value.length) return message.warning('请选择需要重新反馈的陈列商品')
    toReset(state.feedbackForm)
    state.feedbackForm.feedbackId = record ? record.id : undefined
    state.showFeedback = true
  }
  const download = function () {
    const cacheParams = state.cacheParams
    state.exportLoading = true
    axios({
      method: 'post',
      url: singleDisplay.exportFeedback(),
      data: cacheParams,
      responseType: 'blob',
      timeout: 60000,
      headers: {
        Authorization: proxy.$store.state.token
      }
    }).then((res) => {
      const link = document.createElement('a')
      let blob = new Blob([res.data], { type: 'application/x-tar' })
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      link.download = `单品陈列反馈(${moment().format('YYYY-MM-DD HH：mm')}).zip`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }).finally(() => {
      state.exportLoading = false
    })
  }

  async function toShowLogs (record) {
    state.showLog = true
    state.loadingLogs = true
    const res = await commApi(singleDisplay, 'getLogs', record.id)
    state.loadingLogs = false
    if (res) {
      state.logs = res.data?.map((it, index) => ({
        ...it,
        id: index + 'index',
        userName: it.createUserName,
        time: it.createTime
      })) || []
    }
  }

  provide(key, {
    state,
    fetchData,
    handleTableChange,
    toShowEdit,
    approvalHandle,
    toSave,
    toFeedback,
    refeedback,
    toDelete,
    download,
    toShowLogs,
    disabledDate,
    disabledTime,
    selectedRowKeys
  })
  return {
    state,
    fetchData,
    handleTableChange,
    toShowEdit,
    approvalHandle,
    toSave,
    toFeedback,
    refeedback,
    toDelete,
    download,
    toShowLogs,
    disabledDate,
    disabledTime,
    selectedRowKeys
  }
}
