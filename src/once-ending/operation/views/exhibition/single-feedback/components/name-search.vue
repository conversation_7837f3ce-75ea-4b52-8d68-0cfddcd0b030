<script lang="jsx">
  import { defineComponent, ref, getCurrentInstance, watch } from 'vue'
  import { debounce } from '~/util/common'
  import exhibition from '@operation/api/exhibition'
  import commApi from '@operation/api/commApi.js'
  const { singleDisplay } = exhibition

  export default defineComponent({
    props: {
      value: {
        type: String,
        default: undefined
      }
    },
    setup (prop) {
      const { proxy } = getCurrentInstance()
      const dataSource = ref([])
      const valueLocal = ref('')
      watch(
        () => prop.value,
        (val) => {
          valueLocal.value = val
        },
        { immediate: true, deep: true }
      )
      const loading = ref(false)
      let toSearch = async (productName) => {
        if (!productName) return
        const res = await commApi(singleDisplay, 'searchName', { productName })
        if (res) {
          dataSource.value = res.data?.slice(0, 100) || []
        }
      }
      toSearch = debounce(toSearch, 400)
      function changeValue (val) {
        valueLocal.value = val
        proxy.$emit('input', valueLocal.value)
      }
      return {
        dataSource,
        toSearch,
        loading,
        valueLocal,
        changeValue
      }
    },
    render () {
      const { dataSource, toSearch, changeValue, valueLocal } = this
      return <a-auto-complete
      allowClear
      value={valueLocal}
      onChange={changeValue}
      dataSource={dataSource}
      placeholder="请输入"
      style="width: 100%"
      onSearch={toSearch} >
      </a-auto-complete>
    }
  })
</script>
