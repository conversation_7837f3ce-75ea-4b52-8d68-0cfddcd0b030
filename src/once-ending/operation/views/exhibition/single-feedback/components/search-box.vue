<script lang="jsx">
  import { defineComponent, ref, computed, inject, getCurrentInstance, toRefs } from 'vue'
  import { NiFilter, NiFilterItem, NiAreaSelect, NiStaffSelect } from '@jiuji/nine-ui'
  import { useState } from '../hooks/use-state'
  import { options } from '../hooks/constants'
  import NameSearch from './name-search.vue'
  import { dictionary } from '@/operation/views/material/common/data'
  export default defineComponent({
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect,
      NameSearch,
      NiStaffSelect
    },
    setup () {
      const { state, fetchData } = useState()
      return {
        ...toRefs(state),
        fetchData
      }
    },
    render () {
      const { searchForm, loading, fetchData } = this
      return <ni-filter
        form={searchForm}
        onFilter={() => {
          fetchData(1)
        }}
        immediate={false}
        loading={loading}
      >
        <ni-filter-item label="地区">
          <NiAreaSelect
            multiple
            allowClear
            mode={1}
            placeholder="请选择地区"
            maxTagCount={1}
            v-model={searchForm.areaIds}
          />

        </ni-filter-item>
        <ni-filter-item label="状态">
          <a-select
            options={options.status}
            v-model={searchForm.feedbackStatus}
            placeholder="请选择"
            allowClear
          />
        </ni-filter-item>
        {
          this.$tnt.xtenant < 1000 ? <ni-filter-item label="AI审核">
            <a-select
              allowClear
              mode="multiple"
              maxTagCount={1}
              placeholder="AI审核"
              options={[
                {
                  value: 1,
                  label: '未审核',
                },
                {
                  value: 2,
                  label: '合格',
                },
                {
                  value: 3,
                  label: '不合格',
                }
              ]}
              v-model={searchForm.aiAudits}
            />
          </ni-filter-item> : null
        }
        <ni-filter-item class="no-label">
          <a-input-group style="display: flex" compact={true}>
            <a-select
              options={options.searchType}
              v-model={searchForm.searchKey}
              class="flex-child-noshrink"
              style="width: 100px"/>
            {searchForm.searchKey === 3 ? <NameSearch v-model={searchForm.searchValue} style="width: 100%"/>
              : <a-input v-model={searchForm.searchValue} allowClear placeholder="请输入"/>}
          </a-input-group>
        </ni-filter-item>
        <ni-filter-item class="no-label">
          <a-input-group style="display: flex" compact={true}>
            <a-select
              options={options.timeType}
              v-model={searchForm.timeType}
              class="flex-child-noshrink"
              style="width: 125px"/>
            <a-range-picker allowClear v-model={searchForm.timeValue}
                            valueFormat="YYYY-MM-DD"
                            show-time={false}/>
          </a-input-group>
        </ni-filter-item>
        <ni-filter-item label="陈列ID">
          <a-input type="Number" min={1} step={1} precision={0} placeholder="请输入" allowClear
                   v-model={searchForm.singleId}/>
        </ni-filter-item>
        <ni-filter-item label="超时情况">
          <a-select
            options={options.overTime}
            v-model={searchForm.timeOut}
            placeholder="请选择"
            allowClear
          />
        </ni-filter-item>
        <ni-filter-item label="审核人">
          <NiStaffSelect v-model={searchForm.auditUserId} placeholder="请输入工号或姓名搜索"/>
        </ni-filter-item>
        <ni-filter-item class="no-label">
          <a-checkbox-group
            v-model={searchForm.productType}
            options={options.productType}/>
        </ni-filter-item>
      </ni-filter>
    }
  })
</script>
<style scoped lang="scss">
.no-label {
  :deep(.label) {
    display: none;
  }
}

:deep(.ant-select-selection__choice__remove) {
  visibility: hidden;
}

:deep(.ant-select-selection__choice) {
  max-width: 45%;
}
</style>
