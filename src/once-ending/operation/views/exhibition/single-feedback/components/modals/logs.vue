<script lang="jsx">
  import Vue, { defineComponent, getCurrentInstance, toRefs } from 'vue'
  import 'viewerjs/dist/viewer.css'
  import Viewer from 'v-viewer'
  import { useState } from '../../hooks/use-state'
  import LogUserDetail from '~/pages/after-service/order/components/log-user-detail.vue'
  Vue.use(Viewer)

  export default defineComponent({
    components: { LogUserDetail },
    setup () {
      const { state } = useState()
      const root = getCurrentInstance().proxy
      const { $viewerApi } = root
      const viewImages = (pictureUrl, index) => {
        $viewerApi({
          images: viewPicturesArr(pictureUrl)
        }).view(index)
      }
      const viewPicturesArr = (pictureUrlStr) => {
        return pictureUrlStr.length ? pictureUrlStr.split(',') : []
      }
      return {
        ...toRefs(state),
        viewImages,
        viewPicturesArr
      }
    },
    render () {
      const { logs, viewImages, viewPicturesArr } = this
      return <div>
        <a-modal
          title="查看日志"
          destroyOnClose
          maskClosable={false}
          width={550}
          footer={false}
          v-model={this.showLog}>
          <div class="log-box">
            {/*
            <NiLog data={logs} loading={loadingLogs}/>
            */}
            {
              logs && logs.length > 0 ? <a-timeline>
                  {
                    logs.map(item => {
                      return <a-timeline-item key={item.id}>
                        <div>
                          <span>{item.content}</span>
                          <span class="logList-wrapper">
                            <LogUserDetail logUserName={item.userName}
                                           showPop={item.userName !== '系统'}></LogUserDetail>
                          </span>
                          <span class="margin-right">{item.createTime}</span>
                          { item.aiAnalysisProcess ? <span class="link" onClick={() => { this.$set(item, 'showAiAnalysis', true) }}>AI分析过程</span> : null }
                          { item.showAiAnalysis ? <div domPropsInnerHTML={item.aiAnalysisProcess?.replaceAll('\n', '<br/>')}></div> : null}
                        </div>
                        <div>
                          {
                            item.displayCurrent && viewPicturesArr(item.displayCurrent) ? viewPicturesArr(item.displayCurrent).map((children, index) => {
                              return <img class="mr-8 mb-8 pointer" style={{ width: '100px', height: '100px' }} src={children} alt="" onClick={() => viewImages(item.displayCurrent, index)} />
                            }) : null
                          }
                        </div>
                      </a-timeline-item>
                    })
                  }
              </a-timeline> : <a-empty/>
            }
          </div>
        </a-modal>
      </div>
    }
  })
</script>
<style lang="scss" scoped>
.log-box {
  padding: 8px;
  min-height: 300px;
  max-height: 600px;
  overflow-y: auto;
}
.link {
  color: #1890ff;
  cursor: pointer;
  white-space: nowrap;
}
</style>
