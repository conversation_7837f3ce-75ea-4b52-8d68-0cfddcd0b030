<script lang="jsx">
  import Vue, { defineComponent, ref, watch, getCurrentInstance, nextTick } from 'vue'
  import { NiImg } from '@jiuji/nine-ui'
  import Uploader from '@operation/views/complaint/detail/components/uploader'
  import Viewer from 'v-viewer'
  import 'viewerjs/dist/viewer.css'
  Vue.use(Viewer)
  export default defineComponent({
    components: { NiImg, Uploader },
    props: {
      value: {
        type: String,
        default: ''
      },
      ttl: { // 图片保存时间
        type: Number,
        default: 0
      }
    },
    setup (prop) {
      const { proxy } = getCurrentInstance()
      const valueLocal = ref('')
      watch(
        () => prop.value,
        (val) => {
          valueLocal.value = val
        },
        { immediate: true, deep: true }
      )
      const viewImage = function (img) {
        nextTick(() => {
          // 找到.images的div挂载到$viewer上
          const viewer = proxy.$el.querySelector('.images').$viewer
          viewer.show()
        })
      }
      const toUpload = () => {
        proxy.$refs.uploader.doUpload()
      }
      const upLoaderChange = (val) => {
        console.log('val', val)
        if (val?.length) {
          valueLocal.value = val[0].fileUrl
        } else {
          valueLocal.value = ''
        }
        proxy.$emit('input', valueLocal.value)
      }
      return {
        valueLocal,
        viewImage,
        upLoaderChange,
        toUpload
      }
    },
    render () {
      const { valueLocal, viewImage, toUpload, upLoaderChange } = this
      return <div>
        <div class="flex flex-align-center">
          <div class="flex flex-center img uplaod" onClick={toUpload}>+</div>
            { valueLocal ? <div class="relative img-box">
              <img class="img margin-left pointer" src={valueLocal} onClick={() => { viewImage() }}/>
              <div class="delete flex-center" onClick={() => { upLoaderChange(null) }}>x</div>
            </div> : null }
          </div>
          <div class="images" v-viewer={{ movable: false }} style="display: none">
            <img src={valueLocal}/>
          </div>
          <Uploader
            ref="uploader"
            ttl={this.ttl}
            {...{ on: { 'update:fileList': upLoaderChange } }}
            style="display: none"
            accept="image/*"
          />
        </div>
    }
  })
</script>
<style lang="scss" scoped>
.uplaod {
  font-size: 50px;
  line-height: 50px;
  border: 1px dashed #d9d9d9;
  box-sizing: border-box;
  cursor: pointer;
}
.img {
  width: 80px;
  height: 60px;
  border-radius: 4px;
}
.img-box:hover {
  .delete {
    display: flex;
  }
}
.delete {
  position: absolute;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  right: -7px;
  top: -7px;
  color: red;
  border: 1px solid #3b3b3b;
  display: none;
  cursor: pointer;
}
</style>
