<script lang="jsx">
  import { defineComponent, toRefs } from 'vue'
  import { useState } from '../../hooks/use-state'
  import { options } from '../../hooks/constants'
  import moment from 'moment'

  export default defineComponent({
    setup () {
      const { state, refeedback, disabledDate, disabledTime } = useState()
      return {
        ...toRefs(state),
        disabledDate,
        refeedback,
        disabledTime
      }
    },
    render () {
      const { feedbackLoading, feedbackForm, disabledDate, disabledTime, refeedback } = this
      return <div>
        <a-modal
          title="重新反馈"
          destroyOnClose
          maskClosable={false}
          width={550}
          onOk={refeedback}
          onCancel={() => { this.showFeedback = false }}
          confirmLoading={feedbackLoading}
          v-model={this.showFeedback}>
          <a-form-model ref="feedbackFormRef" wrapperCol={{ span: 16 }} labelCol={{ span: 6 }}>
            <a-form-model-item label="原因说明">
              <div class="relative input-box">
                <a-textarea placeholder="请输入" maxLength={200} autoSize={{ minRows: 2, maxRows: 6 }} v-model={feedbackForm.reason}/>
                <span class="text">{feedbackForm.reason?.length || 0}/200</span>
              </div>
            </a-form-model-item>
            <a-form-model-item label="反馈截止时间" required>
              <a-date-picker
              allowClear
              v-model={feedbackForm.feedbackEndTime}
              show-time={true}
              valueFormat="YYYY-MM-DD HH:mm:ss"
              disabledDate={disabledDate}
              disabledTime={() => disabledTime(feedbackForm.feedbackEndTime) }
              />
            </a-form-model-item>
          </a-form-model>
        </a-modal>
      </div>
    }
  })
</script>
<style lang="scss" scoped>
:deep(.ant-form-item) {
  display: flex;
}
.input-box {
  .text {
    position: absolute;
    right: 2px;
    bottom: -12px;
    font-size: 12px;
    color: #8b8a8a;
    line-height: 14px;

  }
}
</style>
