<script lang="jsx">
  import Vue, { defineComponent, toRefs, ref, nextTick, getCurrentInstance, computed } from 'vue'
  import { NiTable, NiImg } from '@jiuji/nine-ui'
  import { useState } from '../hooks/use-state'
  import { columns } from '../hooks/constants'
  import Viewer from 'v-viewer'
  import 'viewerjs/dist/viewer.css'
  Vue.use(Viewer)
  export default defineComponent({
    components: { NiTable, NiImg },
    setup () {
      const { proxy } = getCurrentInstance()
      const { state, handleTableChange, toShowEdit, toDelete, toFeedback, download, toShowLogs, selectedRowKeys, approvalHandle } = useState()
      const hashpcl = computed(() => proxy.$store.state.userInfo.Rank.includes('hpcl'))
      const imgUrl = ref('')
      const viewImage = function (img) {
        imgUrl.value = img
        nextTick(() => {
          // 找到.images的div挂载到$viewer上
          const viewer = proxy.$el.querySelector('.images').$viewer
          console.log(viewer)
          viewer.show()
        })
      }

      const onSelectChange = function (selectedKeys) {
        selectedRowKeys.value = selectedKeys
      }
      return {
        ...toRefs(state),
        handleTableChange,
        viewImage,
        imgUrl,
        hashpcl,
        toShowEdit,
        toDelete,
        toFeedback,
        download,
        toShowLogs,
        selectedRowKeys,
        onSelectChange,
        approvalHandle
      }
    },
    data () {
      return {
        customRenderMap: new Map([
          [
            'normal',
            (text, record) => <span>{ text || '--' }</span>
          ],
          [
            'productId',
            (text, record) => <span>{ record.productType === 1 ? record.pidOrPpid : '--' }</span>
          ],
          [
            'texts',
            (text, record) => <div class="flex flex-center">
              {text?.length > 10 ? <a-popover>
                <div slot="content">{ text }</div>
                <div>{ text.substring(0, 10) }...</div>
              </a-popover> : (text || '--')}
            </div>
          ],
          [
            'ppid',
            (text, record) => <span>{ record.productType === 2 ? record.pidOrPpid : '--' }</span>
          ],
          [
            'showImg',
            (text, record) => text ? <NiImg class="image-single-feedback-scoped" src={text} onTap={() => { this.viewImage(text) }}/> : '--'
          ],
          [
            'feedbackNote',
            (text, record) => <div class="flex flex-center">
                { text?.length > 10 ? <a-popover>
                    <div slot="content" style="maxWidth: 500px">{ text }</div>
                    <div>{ text.substring(0, 10) }...</div>
                  </a-popover>
                  : <span> { text || '-' } </span>
                }
            </div>
          ],
          [
            'matchPercent', (text, record) => <div class="flex flex-center">
            {
              text || text === 0 ? <div>{ text }%</div> : <div>--</div>
            }
            </div>
          ],
          [
            'operation',
            (text, record) => <div class="flex flex-center buttons flex-wrap">
              { this.hashpcl ? <a-button type="link" onClick={() => { this.toShowEdit(record) }}>编辑</a-button> : null }
              { this.hashpcl ? <a-button type="link" onClick={() => { this.toDelete(record) }}>删除</a-button> : null }
              { this.hashpcl && record.statusText === '待审核' ? <a-button type="link" onClick={() => { this.approvalHandle(record, '2') }}>审核通过</a-button> : null }
              { this.hashpcl && record.statusText === '待审核' ? <a-button type="link" onClick={() => { this.approvalHandle(record, '1') }}>审核不通过</a-button> : null }
              { this.hashpcl && record.feedbackStatus === 2 ? <a-button type="link" onClick={() => { this.toFeedback(record) }}>重新反馈</a-button> : null }
              <a-button type="link" onClick={() => { this.toShowLogs(record) }}>查看日志</a-button>
            </div>
          ]
        ])
      }
    },
    computed: {
      newColumns () {
        const columnsList = this.$tnt.xtenant < 1000 ? columns : columns.filter(item => !['matchPercent', 'aiAuditName'].includes(item.dataIndex))
        return columnsList.map(it => {
          const cache = { ...it }
          cache.customRender = this.customRenderMap.get('normal')
          const customRenders = this.customRenderMap.get(it.dataIndex)
          customRenders && (cache.customRender = customRenders)
          if (it.dataIndex === 'displayStandard' || it.dataIndex === 'displayCurrent') {
            cache.customRender = this.customRenderMap.get('showImg')
          }
          if (it.dataIndex === 'displayDescription') {
            cache.customRender = this.customRenderMap.get('texts')
          }
          if (it.dataIndex === 'matchPercent') {
            cache.customRender = this.customRenderMap.get('matchPercent')
          }
          return cache
        })
      }
    },
    render () {
      const { loading, pagination, handleTableChange, newColumns, dataSource, imgUrl, exportLoading, download, toFeedback, selectedRowKeys, onSelectChange } = this
      return <div>
        <ni-table
          rowKey="id"
          loading={loading}
          columns={newColumns}
          dataSource={dataSource}
          pagination={pagination}
          row-selection={{
            selectedRowKeys,
            onChange: onSelectChange,
            getCheckboxProps: (record) => {
            return {
              props: {
                disabled: record.feedbackStatus !== 2
              },
            }
          }
          }}
          bordered
          class="single-feedback-table"
          align={'center'}
          onChange={handleTableChange}
        >
          <div style="text-align: left;" slot="tool">
            <a-button disabled={!selectedRowKeys.length} onClick={() => { toFeedback() }} type="primary">批量重新反馈</a-button>
          </div>
          <div slot="action">
              <a-button loading={exportLoading} onClick={download} type="primary">导出</a-button>
          </div>
        </ni-table>
        <div class="images" v-viewer={{ movable: false }} style="display: none">
          <img src={imgUrl}/>
        </div>
      </div>
    }
  })
</script>
<style>
.image-single-feedback-scoped {
  width: 80px;
  height: 60px;
  cursor: pointer;
}
</style>
<style scoped lang="scss">
:deep(.buttons .ant-btn) {
  padding: 0px 8px;
}
:deep(.ant-table-fixed-left .ant-table-body-inner) {
  margin-right: 0;
}
</style>
