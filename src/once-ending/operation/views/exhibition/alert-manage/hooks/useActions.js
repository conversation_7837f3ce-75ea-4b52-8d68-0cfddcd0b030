
import { getCurrentInstance } from 'vue'
import moment from 'moment'
import axios from 'axios'
import { message } from 'ant-design-vue'
import exhibitionApi from '@operation/api/exhibition'
import { to } from '@common/utils/common'
import { useState } from './useState.js'

export default function useActions () {
  const { proxy } = getCurrentInstance()
  const { state } = useState()

  const getData = async function (cur) {
    if (cur) state.pagination.current = cur
    const { current, pageSize } = state.pagination
    const {
      searchForm: { areaIds },
      alertType
    } = state
    const params = {
      current,
      size: pageSize,
      areaIds,
      type: alertType
    }
    state.loading = true
    const [err, res] = await to(exhibitionApi.getWarningList(params))
    state.queryParams = {
      areaIds: params.areaIds,
      type: params.type
    }
    state.loading = false
    if (err) throw err
    const { code, userMsg, data } = res
    if (code !== 0) return message.error(userMsg)

    const { records, total } = data
    state.pagination.total = total
    const cacheData = []
    records.forEach((item, index) => {
      cacheData.push({ ...item, level: 1 })
      const { details } = item
      if (details?.length) {
        details.forEach(it => {
          cacheData.push({ ...it, level: 2 })
        })
      }
    })
    state.dataSource = cacheData
  }
  const exportWarningList = async function () {
    const { type } = state.queryParams
    state.downloadLoading = true
    if (!state.queryParams) {
      message.info('请先查询数据')
      return
    }
    const exportName = {
      1: '门店库存预警商品',
      2: '门店销量预警商品',
      3: '门店下架陈列预警商品',
    }
    axios({
      method: 'post',
      url: exhibitionApi.exportWarningExcel(),
      responseType: 'blob',
      data: state.queryParams,
      timeout: 60000,
      headers: {
        Authorization: proxy.$store.state.token,
      }
    }).then(res => {
      if (!res) return message.error('导出出错')
      const link = document.createElement('a')
      let blob = new Blob([res.data], { type: 'application/x-excel' })
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      link.download = `${exportName[type]}(${moment().format('YYYY-MM-DD HH：mm')}).xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }).catch(err => {
      console.log('err', err)
      message.error(err)
    }).finally(() => {
      state.downloadLoading = false
    })
  }

  const handleTableChange = function (paginationObj) {
    Object.assign(state.pagination, paginationObj)
    getData()
  }

  return {
    getData,
    exportWarningList,
    handleTableChange
  }
}
