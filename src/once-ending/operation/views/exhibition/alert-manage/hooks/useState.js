import { reactive, inject, provide } from 'vue'
import { cloneDeep } from 'lodash'
import { PAGINATION } from '../constants'

const key = Symbol('stateSymbol')

export function useState () {
  return inject(key)
}

export function createState () {
  const state = reactive({
    searchForm: {
      areaIds: undefined,
    },
    alertType: '1',
    queryParams: undefined,
    pagination: cloneDeep(PAGINATION),
    dataSource: [],
    loading: false,
    downloadLoading: false
  })

  const stateSymbol = {
    state,
  }
  provide(key, stateSymbol)
  return stateSymbol
}
