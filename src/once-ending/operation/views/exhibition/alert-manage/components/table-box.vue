<script lang="jsx">
  import {
    defineComponent,
    ref,
    getCurrentInstance,
    onMounted
  } from 'vue'
  import { cloneDeep } from 'lodash'

  import useActions from '../hooks/useActions'
  import { useState } from '../hooks/useState.js'
  import { originColumns, areaShow, tabList } from '../constants'

  import { NiTable, NiPrice } from '@jiuji/nine-ui'

  export default defineComponent({
    name: 'table-box',
    components: {
      NiTable,
      NiPrice
    },
    setup () {
      const { proxy } = getCurrentInstance()
      const { state } = useState()
      const {
        getData,
        exportWarningList,
        handleTableChange
      } = useActions()

      const keys = originColumns.map(it => it.key)
      const columnsKeys = ref(keys)
      const columns = ref(cloneDeep(originColumns))
      const length = originColumns.length
      const colSpanLength = ref(length)
      const images = ref([])

      const columnsSetting = function (checkedKeys) {
        columnsKeys.value = checkedKeys
      }
      function getOriginColumns () {
        const listPageConfig = localStorage.getItem('listPageConfig')
        const key = proxy.$route.path
        if (listPageConfig) {
          const config = JSON.parse(listPageConfig)
          const tableColumns = config[key]?.tableColumns
          tableColumns && (columnsKeys.value = columnsKeys.value.filter(key => !tableColumns[key]?.hide))
        }
      }
      const handleTabChange = (value) => {
        state.alertType = value
        getData(1)
      }

      onMounted(() => {
        getOriginColumns()
      })

      return {
        state,

        columnsSetting,
        colSpanLength,
        columnsKeys,
        columns,
        images,
        handleTableChange,
        handleTabChange,
        exportWarningList
      }
    },
    watch: {
      columnsKeys: {
        handler () {
          this.getColumns()
        },
        immediate: true
      }
    },
    render () {
      const {
        state: {
          loading,
          dataSource,
          pagination,
          alertType,
          downloadLoading
        },
        columns,
        columnsSetting,
        handleTableChange,
        handleTabChange,
        exportWarningList
      } = this
      const tabListShow = tabList.map(it => ({
        key: it.key,
        tab: <span>{it.tab}<a-tooltip placement="bottom"><span slot="title">{it.tip}</span><a-icon style="margin: 0 0 0 4px;font-size: 14px;color: rgba(0, 0, 0, 0.65)" type="question-circle" /></a-tooltip></span>
      }))
      return <div>
        <ni-table
          rowKey={ (r, i) => i }
          rowClassName={
            (r, i) => r.level === 1
              ? 'first-level'
              : r.total
                ? 'totals'
                : null
          }
          loading={loading}
          columns={columns.filter(d => alertType === '3' || d.key !== 'sellPrice')}
          dataSource={dataSource}
          pagination={pagination}
          bordered
          align={'center'}
          onChange={handleTableChange}
          onColumnsSetting={columnsSetting}
        >
        <a-card
          slot="tool"
          size='small'
          class='flex'
          bordered={ false }
          tabList={ tabListShow }
          activeTabKey={ alertType }
          onTabChange={ handleTabChange }
        />
        <span slot='action'>
          <a-button loading={ downloadLoading } onClick={ exportWarningList }>
            导出
          </a-button>
        </span>
        </ni-table>
      </div>
    },
    data () {
      return {
        customRenderMap: new Map([
          [
            'firstLevel',
            (item, record, text) => <div class="totals left">
              {(record.fixPhotoId === -1 && this.state.alertType === '1') ? <span><span class="mr-26">需重点备货商品</span>预警商品数量：{ record.productCount }
                <span class="grey-9" style="font-weight: 400">（近7天有销量但当前无库存的陈列商品）</span>
              </span> : areaShow.map(it => <span class="mr-26">{it.label}：{ record[it.key] || '--'} </span>)}
            </div>
          ],
          [
            'sellPrice',
            (item, record, text) => {
              return text ? <NiPrice value={text} thousand={true}/> : <span>'-'</span>
            }
          ]
        ])
      }
    },
    methods: {
      getChildren (item, record, text) {
        let texts = <span>{text || '-'}</span>
        let mapKey = ''
        if (record.level === 1) {
          mapKey = 'firstLevel'
        }
        if (item.key === 'sellPrice') {
          mapKey = 'sellPrice'
        }
        mapKey && (texts = this.customRenderMap.get(mapKey)(item, record, text))
        return texts
      },
      getColumns () {
        this.columns.forEach((item) => {
          item.customRender = (text, record) => {
            let colSpan = 1
            if (record.level === 1) { // 合并父级全部列
              colSpan = item.key === this.columnsKeys[0] ? this.columnsKeys.length : 0
            }
            return {
              children: this.getChildren(item, record, text),
              attrs: {
                // 合并行
                colSpan: colSpan
              }
            }
          }
        })
      }
    },
  })
</script>

<style scoped lang="scss">
:deep(.first-level) {
  background: #c4e3fb;
}
:deep(.ant-table-tbody
  > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)
  > td) {
  background: none;
}
.link {
  color: #1890ff;
  cursor: pointer;
  font-weight: 400;
}
:deep(.totals) {
  font-weight: 600;
}
.center {
  text-align: center;
}
.left {
  text-align: left;
}
.mr-26 {
  margin-right: 26px;
}
.fw-600{
  font-weight: 600;
}
:deep(.nine-table-bar){
  padding: 12px 0;
}
:deep(.nine-table-bar .nine-table-bar-main){
  align-items: center;
}
:deep(.ant-card-small > .ant-card-head){
  padding-left: 0;
}
:deep(.ant-card-head .ant-tabs-bar){
  border: none;
}
:deep(.ant-card-small > .ant-card-head) {
  border: none;
}
:deep(.ant-tabs .ant-tabs-large-bar .ant-tabs-tab) {
  padding: 0 0 10px;
}
</style>
