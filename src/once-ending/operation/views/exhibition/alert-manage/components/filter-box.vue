<script lang="jsx">
  import { defineComponent, onMounted } from 'vue'
  import { NiFilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import { useState } from '../hooks/useState.js'
  import useActions from '../hooks/useActions.js'

  export default defineComponent({
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect
    },
    setup () {
      const { state } = useState()
      const {
        getData
      } = useActions()

      onMounted(() => {
        getData()
      })

      return {
        state,
        getData
      }
    },
    render () {
      const {
        state: {
          searchForm,
          loading
        },
        getData
      } = this
      return (
        <ni-filter
          form={searchForm}
          onFilter={() => {
            getData(1)
          }}
          immediate={false}
          loading={loading}
        >
          <ni-filter-item label="选择门店">
            <NiAreaSelect
              multiple
              allowClear
              showCheckAll
              mode={ 2 }
              placeholder="请选择门店或者搜索"
              maxTagCount={1}
              v-model={searchForm.areaIds}
            />
          </ni-filter-item>
        </ni-filter>
      )
    }
  })
</script>

<style lang="scss" scoped>
:deep(.ant-select-selection__choice__remove) {
  visibility: hidden;
}
:deep(.ant-select-selection__choice) {
  max-width: 65%;
}
.filter-item .content{

}
</style>
