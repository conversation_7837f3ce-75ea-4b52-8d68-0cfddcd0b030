export const originColumns = (function () {
  return [
    {
      title: '商品名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 320
    },
    {
      title: 'ppid',
      dataIndex: 'ppid',
      key: 'ppid',
      width: 90
    },
    {
      title: '商品id',
      dataIndex: 'productId',
      key: 'productId',
      width: 180
    },
    {
      title: '串号',
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      showNumber: true,
      width: 160
    },
    {
      title: '陈列编号',
      dataIndex: 'relationId',
      key: 'relationId',
      showNumber: true,
      width: 160
    },
    {
      title: '建议售价',
      dataIndex: 'sellPrice',
      key: 'sellPrice',
      width: 120,
      show: window.tenant.xtenant < 1000,
    },
  ].filter(d => d.show === undefined || d.show)
})()

export const areaShow = [
  { label: '门店', key: 'area' },
  { label: '柜子名称', key: 'fixPhotoName' },
  { label: '预警商品数量', key: 'productCount' },
]
export const statisticsOptions = [
  { tableKey: 'countSalesSeven', statisticsKey: 'countSalesSevenTotal' },
  { tableKey: 'grossProfitSeven', statisticsKey: 'grossProfitSevenTotal' },
  { tableKey: 'countSalesThirty', statisticsKey: 'countSalesThirtyTotal' },
  { tableKey: 'grossProfitThirty', statisticsKey: 'grossProfitThirtyTotal' },
  { tableKey: 'countSalesAll', statisticsKey: 'countSalesAllTotal' },
  { tableKey: 'grossProfitAll', statisticsKey: 'grossProfitAllTotal' },
  { tableKey: 'prototypeHits', statisticsKey: 'prototypeHitsTotal' },
  { tableKey: 'productCost', statisticsKey: 'productCostTotal' },
  { tableKey: 'shopStock', statisticsKey: 'shopStockTotal' },
  { tableKey: 'allStock', statisticsKey: 'allStockTotal' },
]
export const tabList = [
  {
    key: '1',
    tab: '库存预警',
    tip: '门店当前陈列商品，门店无现货库存预警，请门店及时备货'
  },
  {
    key: '2',
    tab: '销量预警',
    tip: '门店当前陈列商品，30天销量为0，请门店及时评估调整陈列'
  },
  {
    key: '3',
    tab: '下架陈列预警',
    tip: '门店当前待处理陈列商品，需门店及时撤除展陈并跟进库存清理'
  }
]
export const PAGINATION = {
  pageSize: 1,
  current: 1,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ['1', '3', '5', '10']
}
