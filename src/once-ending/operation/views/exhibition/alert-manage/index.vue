<script lang="jsx">
  import { defineComponent } from 'vue'
  import filterBox from './components/filter-box'
  import tableBox from './components/table-box'
  import { NiListPage } from '@jiuji/nine-ui'
  import { createState } from './hooks/useState.js'

  export default defineComponent({
    name: 'index',
    components: { filterBox, tableBox, NiListPage },
    setup () {
      createState()
    },
    render () {
      return <page>
        <ni-list-page pushFilterToLocation={false}>
          <filter-box class='mb-8'/>
          <table-box/>
        </ni-list-page>
      </page>
    }
  })
</script>
