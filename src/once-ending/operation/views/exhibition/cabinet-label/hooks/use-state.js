import { inject, provide, reactive, ref } from 'vue'
const key = Symbol('useState')

export const useState = function () {
  return inject(key)
}

export const createState = function (api) {
  const {
    dpCabinetLabelList
  } = api

  const loading = ref(false)

  const isFeatch = ref(false)

  const dataSource = ref([])

  const visible = ref(false)

  const pagination = ref({
    current: 1,
    pageSize: 20,
    total: 0,
    pageSizeOptions: ['20', '50', '100', '200'],
    showQuickJumper: true,
    showTotal: total => `共计${total}条`
  })

  const destroyedModal = ref(false)

  const addState = reactive({
    id: '',
    title: '',
    labelName: ''
  })

  const fetchData = async function (cur) {
    if (cur) pagination.value.current = cur
    const { current, pageSize: size } = pagination.value
    const params = {
      current,
      size
    }
    loading.value = true
    const res = await dpCabinetLabelList(params)
    loading.value = false
    if (res) {
      const { data: { records, total } } = res
      dataSource.value = records
      pagination.value.total = total
    }
  }
  fetchData()

  const o = {
    dataSource,
    addState,
    loading,
    isFeatch,
    fetchData,
    visible,
    destroyedModal,
    pagination
  }

  provide(key, o)
  return o
}
