<script lang="jsx">
  import { defineComponent } from 'vue'
  import { useApi } from '../hooks/use-api'
  import { useState } from '../hooks/use-state'
  import { Button, message } from 'ant-design-vue'
  import NoData from '~/components/no-data'
  import { columns } from '../constants'
  import { NiTable } from '@jiuji/nine-ui'
  import AddModal from './add-modal'

  export default defineComponent({
    components: {
      NiTable,
      NoData,
      AddModal,
    },
    setup () {
      const { dpCabinetLabelDel } =
        useApi()
      const { loading, dataSource, addState, isFeatch, destroyedModal, visible, fetchData, pagination } =
        useState()

      const add = function (record) {
        addState.title = record ? '编辑标签' : '新增标签'
        addState.labelName = record ? record.labelName : undefined
        addState.id = record ? record.id : ''
        visible.value = true
      }

      const del = async function (record) {
        const res = await dpCabinetLabelDel(record.id)
        if (res) {
          message.success('删除成功')
          fetchData()
        }
      }

      const handleTableChange = function (paginationObj) {
        pagination.value = {
          ...paginationObj
        }
        fetchData()
      }

      return {
        loading,
        isFeatch,
        add,
        dataSource,
        destroyedModal,
        del,
        pagination,
        handleTableChange
      }
    },
    render () {
      const {
        dataSource,
        loading,
        isFeatch,
        destroyedModal,
        add,
        del,
        pagination,
        handleTableChange
      } = this
      return (
      <div>
        <NiTable
          class="rights-config-table mt-10"
          locale={{ emptyText: <NoData is-featch={isFeatch} /> }}
          dataSource={dataSource}
          columns={columns}
          loading={loading}
          pagination={pagination}
          onChange={ handleTableChange }
          scopedSlots={{
            action: (text, record) => (
              <div>
                <Button
                  type="link"
                  onClick={() => {
                    add(record)
                  }}
                >
                  编辑
                </Button>
                  <Button onClick={() => { del(record) }} type="link">删除</Button>
              </div>
            ),
            noData: (text) => <span>{text || '--'}</span>,
          }}
        >
          <div slot="tool">
            <Button
              type="primary"
              onClick={() => {
                add()
              }}
            >
              添加标签
            </Button>
          </div>
        </NiTable>
        {!destroyedModal ? <AddModal /> : null}
      </div>
      )
    },
  })
</script>
