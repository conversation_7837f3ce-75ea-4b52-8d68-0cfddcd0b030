<script lang="jsx">
  import { defineComponent, nextTick, ref } from 'vue'
  import {
    Modal,
    Input,
    FormModel,
    message
  } from 'ant-design-vue'
  import { useState } from '../hooks/use-state'
  import { useApi } from '../hooks/use-api'
  export default defineComponent({
    setup () {
      const { dpCabinetLabelUpdate, dpCabinetLabelSave } = useApi()
      const { visible, addState, destroyedModal, fetchData } = useState()

      const formRule = ref(null)

      const loading = ref(false)

      const ok = async function () {
        const { title, ...other } = addState
        const params = {
          ...other
        }
        loading.value = true
        const res = addState.id ? await dpCabinetLabelUpdate(params) : await dpCabinetLabelSave(params)
        loading.value = false
        if (res) {
          message.success(`${addState.id ? '更新' : '保存'}成功`)
          visible.value = false
          fetchData()
        }
      }

      const afterClose = function () {
        destroyedModal.value = true
        nextTick(() => {
          destroyedModal.value = false
        })
      }

      return {
        visible,
        afterClose,
        ok,
        addState,
        formRule,
        loading
      }
    },
    render () {
      const { visible, ok, afterClose, addState, loading } =
        this
      return (
      <Modal
        title={addState.title}
        visible={visible}
        mask-closable={false}
        width="600px"
        onOk={ok}
        onCancel={() => {
          this.visible = false
        }}
        after-close={afterClose}
        confirmLoading={loading}
      >
        <FormModel
          {...{ props: { model: addState } }}
          ref="formRule"
          label-col={{ span: 6 }}
          wrapper-col={{ span: 18 }}
        >
          <FormModel.Item
            label="陈列柜标签名称"
            prop="labelName"
            rules={[
              {
                required: true,
                message: '请输入标签名称',
                trigger: 'blur',
              },
            ]}
          >
            <Input
              v-model={addState.labelName}
              allow-clear
              placeholder="请输入标签名称"
              maxLength={30}
            />
          </FormModel.Item>
        </FormModel>
      </Modal>
      )
    },
  })
</script>
