<script lang="jsx">
  import { defineComponent } from 'vue'
  import TableBox from './components/table-box'
  import { createApi } from './hooks/use-api'
  import { createState } from './hooks/use-state'
  import { NiListPage } from '@jiuji/nine-ui'

  export default defineComponent({
    components: {
      TableBox,
      NiListPage
    },
    setup () {
      const api = createApi()
      createState(api)
    },
    render () {
      return <page class="cabinet-label">
      <ni-list-page push-filter-to-location={false}>
        <TableBox/>
      </ni-list-page>

      </page>
    }
  })
</script>
