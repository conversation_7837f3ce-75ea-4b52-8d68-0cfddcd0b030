<script lang="jsx">
  import { defineComponent, inject } from 'vue'
  import { tabsOptions } from '../constants'
  export default defineComponent({
    name: 'index',
    setup () {
      const productType = inject('productType')
      const cabinetName = inject('cabinetName')
      const showCount = inject('showCount')
      const cabinetLabelStr = inject('cabinetLabelStr')
      return {
        productType,
        cabinetName,
        showCount,
        cabinetLabelStr
      }
    },
    render (createElement, context) {
      const { cabinetName, showCount, cabinetLabelStr } = this
      return <div class="relative">
        <a-tabs v-model={this.productType}>
          { tabsOptions.map(it => <a-tab-pane key={it.value} tab={it.label}></a-tab-pane>) }
        </a-tabs>
        <div class="areas">
        <span>柜子名称：{cabinetName}{showCount}</span>
        {
          cabinetLabelStr ? <span class="ml-8">陈列柜标签：{cabinetLabelStr}</span> : null
        }
        </div>
      </div>
    }
  })
</script>

<style scoped lang="scss">
.areas {
  position: absolute;
  right: 0;
  top: 12px;
  font-weight: 600;
}
</style>
