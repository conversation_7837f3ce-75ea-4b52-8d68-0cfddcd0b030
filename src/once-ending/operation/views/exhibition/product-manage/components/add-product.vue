<script lang="jsx">
  import { defineComponent, provide, reactive, ref, inject, watch, getCurrentInstance } from 'vue'
  import searchBox from './search-box'
  import { bigAddColumns, smallAddColumns, smallSearchType, bigSearchType } from '../constants'
  import {
    GET_SMALL_STOCK,
    GET_BIG_STOCK,
    ADD_CABINET_PRODUCT
  } from '@operation/store/modules/exhibition/action-types'
  export default defineComponent({
    name: 'display-product',
    components: { searchBox },
    setup () {
      const { proxy } = getCurrentInstance()
      const fixPhotoId = inject('fixPhotoId')
      const areaId = inject('areaId')
      const searchForm = reactive({
        tag: 'productName',
        keyWord: undefined,
        cateIds: undefined
      })
      provide('searchForm', searchForm)
      const isAdd = ref(true)
      provide('isAdd', isAdd)
      const productType = inject('productType')
      const needUpdateFiles = inject('needUpdateFiles')
      const dataSource = ref([])
      const pagination = reactive({
        pageSize: 10,
        current: 1,
        total: 0
      })

      const loading = ref(false)
      const getDatasource = async function (current) {
        const { tag, keyWord, cateIds } = searchForm
        current && (pagination.current = current)
        const type = productType.value
        const params = {
          areaId: areaId.value,
          current: pagination.current,
          size: pagination.pageSize,
        }
        keyWord && (params[tag] = keyWord)
        type === 2 && (params.cateIds = cateIds)
        loading.value = true
        const apiKey = type === 1 ? GET_SMALL_STOCK : GET_BIG_STOCK
        const res = await proxy.$store.dispatch(`operation/exhibition/${apiKey}`, params)
        loading.value = false
        if (res) {
          const { records, total } = res.data
          dataSource.value = records.map(it => ({
            ...it,
            addDisplayCount: 1
          }))
          pagination.total = total
        }
      }
      provide('getDatasource', getDatasource)
      watch(() => productType.value, (val) => {
        searchForm.tag = val === 1 ? 'ppid' : 'mkcId'
        searchForm.keyWord = undefined
        searchForm.cateIds = undefined
        pagination.current = 1
        pagination.total = 0
        dataSource.value = []
        val === 2 && (getDatasource(1))
      }, { immediate: false })

      const changeNumber = function (val, record) {
        record.addDisplayCount = val
      }

      const changeTable = function (page) {
        Object.assign(pagination, page)
        getDatasource()
      }

      const addLoading = inject('addLoading')
      const addDisplay = async function (record) {
        const params = {
          areaId: areaId.value,
          fixPhotoId: fixPhotoId.value,
          stockList: [{ ...record, type: productType.value }]
        }
        productType.value === 2 && (params.stockList[0].relationId = record.mkcId)
        addLoading.value = true
        needUpdateFiles.value = false
        const res = await proxy.$store.dispatch(`operation/exhibition/${ADD_CABINET_PRODUCT}`, params)
        addLoading.value = false
        if (res) {
          needUpdateFiles.value = true
          proxy.$message.success('添加陈列成功，请重新上传陈列图片')
          getDatasource()
        }
      }

      const downloadFiles = async function () {

      }
      return {
        pagination,
        dataSource,
        productType,
        changeNumber,
        changeTable,
        addDisplay,
        loading,
        downloadFiles
      }
    },
    computed: {
      columns () {
        const newColumns = this.productType === 1 ? smallAddColumns : bigAddColumns
        newColumns.forEach(it => {
          it.align = 'center'
          it.dataIndex === 'operation' && (it.width = '16%')
          if (this.customRenderMap.get(it.dataIndex)) {
            it.customRender = this.customRenderMap.get(it.dataIndex)
          }
        })
        return newColumns
      },
      emptyText () {
        const options = this.productType === 1 ? smallSearchType.slice(0, 3) : bigSearchType
        const text = `输入${options.map(it => it.label).join('、')}搜索商品信息`
        return <div class="full-width no-data flex flex-center">
          { text }
        </div>
      }
    },
    data () {
      return {
        customRenderMap: new Map([
          [
            'operation',
            (text, record) => <a-button disabled={this.productType === 1 ? record.stockCount <= 0 : false} type="link" onClick={() => this.addDisplay(record)}>添加陈列</a-button>
          ],
          [
            'addDisplayCount',
            (text, record) => <div class="flex flex-center number">
            <a-input-number
              onChange={(val) => this.changeNumber(val, record)}
              min={1}
              max={record.stockCount > 0 ? record.stockCount : 1}
              class="input"
              value={text}/>
          </div>
          ],
        ])
      }
    },
    render (createElement, context) {
      const hide = true
      const {
        pagination,
        dataSource,
        columns,
        productType,
        changeTable,
        loading,
        emptyText,
        downloadFiles
      } = this
      const isSmall = productType === 1
      return <div class="display-box">
        <searchBox/>
        {
          hide ? null : <div class="mt-16">
          {isSmall ? <div class="flex mt-16 flex-justify-end">
            <router-link to="/store/exhibition/product-small">
              <a-button type="link">小件陈列商品参考清单</a-button>
            </router-link>
          </div> : '未陈列样机'}
        </div>
        }
        <a-table
          size="small"
          pagination={pagination}
          dataSource={dataSource}
          columns={columns}
          loading={loading}
          bordered
          locale={{ emptyText: emptyText }}
          onChange={changeTable}
          rowKey={(r, i) => i}
          class="mt-8">
        </a-table>
      </div>
    }
  })
</script>

<style scoped lang="scss">
.display-box {
  width: 50%;
  box-sizing: border-box;
  padding: 0 16px 16px 16px;
}
.buttons {
  :deep(.ant-btn) {
    padding: 0 8px;
  }
}
.number {
  .input {
    width: 90px;
  }
  :deep(.ant-input-number-input) {
    text-align: center;
  }
}
.no-data {
  height: 60px;
  color: rgba(0, 0, 0, 0.45);
}
</style>
