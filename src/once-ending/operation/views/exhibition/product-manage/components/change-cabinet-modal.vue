<script lang="jsx">
  import { ref, inject, defineComponent, getCurrentInstance, watch } from 'vue'
  import { GET_CABINET_NAME } from '@operation/store/modules/exhibition/action-types'
  export default defineComponent({
    name: 'change-cabinet-modal',
    setup () {
      const { proxy } = getCurrentInstance()
      const singleChangeForm = inject('singleChangeForm')
      const batchLoading = inject('batchLoading')
      const startChange = inject('startChange')
      const areaId = inject('areaId')
      const fixPhotoId = inject('fixPhotoId')
      const cabinetOptions = inject('cabinetOptions')
      const nameOptions = ref([])
      const loading = ref(false)
      const getNameOptions = async function () {
        const params = {
          areaId: areaId.value,
          dpFixPhotoType: singleChangeForm.dpFixPhotoType
        }
        nameOptions.value = []
        loading.value = true
        const res = await proxy.$store.dispatch(`operation/exhibition/${GET_CABINET_NAME}`, params)
        loading.value = false
        if (res) {
          nameOptions.value = res.data.map(it => ({
            label: it.name,
            value: it.id
          }))
        }
      }
      watch(() => singleChangeForm.dpFixPhotoType, (val) => {
        val && (getNameOptions())
      }, { immediate: true })

      function sureChange () {
        const { dpFixPhotoType, newFixPhotoId } = singleChangeForm
        if (!dpFixPhotoType) {
          return proxy.$message.warning('请选择柜子类型')
        }
        if (!newFixPhotoId) {
          return proxy.$message.warning('请选择柜子柜子名称')
        }
        if (fixPhotoId.value === newFixPhotoId) {
          return proxy.$message.warning('更换的柜子不能和当前柜子一样！')
        }
        startChange()
      }

      const cancel = function () {
        singleChangeForm.dpFixPhotoType = undefined
        singleChangeForm.newFixPhotoId = undefined
      }
      return {
        singleChangeForm,
        batchLoading,
        cabinetOptions,
        nameOptions,
        sureChange,
        getNameOptions,
        loading,
        cancel
      }
    },
    render (createElement, context) {
      const {
        singleChangeForm,
        batchLoading,
        sureChange,
        getNameOptions,
        nameOptions,
        loading,
        cancel,
        cabinetOptions
      } = this
      return <a-modal
        width={400}
        title="更换陈列柜"
        destroyOnClose={true}
        centered
        onOk={sureChange}
        onCancel={cancel}
        confirmLoading={batchLoading}
        v-model={singleChangeForm.show}
      >
        <a-form label-col={{ span: 6 }} wrapper-col={{ span: 16 }}>
          <a-form-item label="柜子类型" required>
            <a-select
              placeholder="请选择"
              options={cabinetOptions}
              v-model={singleChangeForm.dpFixPhotoType}
              class="full-width"/>
          </a-form-item>
          <a-form-item label="柜子名称" required>
            <a-select
              v-model={singleChangeForm.newFixPhotoId}
              placeholder="请选择"
              options={nameOptions}
              class="full-width">
              { (loading && !nameOptions.length) ? <div class="not-content flex flex-center" slot="notFoundContent">
                <a-spin/>
              </div> : null }
            </a-select>
          </a-form-item>
        </a-form>
      </a-modal>
    }
  })
</script>

<style scoped lang="scss">
.not-content {
  height: 100px;
}
</style>
