<script lang="jsx">
  import { defineComponent, provide, reactive, ref, inject, computed, getCurrentInstance, watch } from 'vue'
  import Uploader from '~/components/custom-uploader'
  import searchBox from './search-box'
  import editModal from './edit-modal'
  import changeCabinetModal from './change-cabinet-modal'
  import { smallDisplayColumns, bigDisplayColumns, smallSearchType, bigSearchType } from '../constants'
  import { Icon, message } from 'ant-design-vue'
  import {
    GET_DISPLAY_PRODUCT,
    CANCEL_DISPLAY,
    TURN_FLAWS,
    CHANGE_CABINET_TYPE,
    GET_CABINET_PHOTO,
    SAVE_CABINET_PHOTO,
    GET_CABINET_TYPE
  } from '@operation/store/modules/exhibition/action-types'
  import { to } from '~/util/common'
  import exhibition from '@operation/api/exhibition'
  export default defineComponent({
    name: 'display-product',
    components: { searchBox, Uploader, editModal, changeCabinetModal },
    setup () {
      const { proxy } = getCurrentInstance()
      const fixPhotoId = inject('fixPhotoId')
      const cabinetType = inject('cabinetType')
      const needUpdateFiles = inject('needUpdateFiles')
      const areaId = inject('areaId')
      const searchForm = reactive({
        tag: 'productName',
        keyWord: undefined
      })
      provide('searchForm', searchForm)
      const productType = inject('productType')
      const dataSource = ref([])
      const pagination = reactive({
        pageSize: 10,
        current: 1,
        total: 0,
        showTotal: (total) => `共${total}条数据`
      })
      const rowKeys = ref([])
      const rows = ref([])
      const rowSelection = computed(() => {
        return {
          selectedRowKeys: rowKeys.value,
          onChange: (selectedRowKeys, selectedRows) => {
            rowKeys.value = selectedRowKeys
            rows.value = selectedRows
          }
        }
      })

      const cabinetOptions = ref([])
      provide('cabinetOptions', cabinetOptions)
      async function getCabinetOptions () { // 获取柜子类型
        const res = await proxy.$store.dispatch(`operation/exhibition/${GET_CABINET_TYPE}`, { areaId: areaId.value })
        if (res) {
          cabinetOptions.value = res.data.map(it => ({
            label: it.name,
            value: it.id,
          }))
        }
      }
      getCabinetOptions()
      const singleChangeForm = reactive({
        show: false,
        dpFixPhotoType: undefined,
        newFixPhotoId: undefined
      })
      const batchOperationForm = reactive({
        productName: '',
        operationType: 1,
        show: false,
        dpFixPhotoType: undefined,
        newFixPhotoId: undefined
      })
      const batchLoading = ref(false)
      provide('batchLoading', batchLoading)
      provide('singleChangeForm', singleChangeForm)
      provide('batchOperationForm', batchOperationForm)

      const loading = ref(false)
      const getDatasource = async function (current) {
        const { tag, keyWord } = searchForm
        current && (pagination.current = current)
        const params = {
          fixPhotoId: fixPhotoId.value,
          type: productType.value,
          key: keyWord ? tag : undefined,
          value: keyWord,
          current: pagination.current,
          size: pagination.pageSize
        }
        loading.value = true
        const res = await proxy.$store.dispatch(`operation/exhibition/${GET_DISPLAY_PRODUCT}`, params)
        loading.value = false
        if (res) {
          rows.value = []
          rowKeys.value = []
          const { records, total } = res.data
          dataSource.value = records
          pagination.total = total
        }
      }

      const addLoading = inject('addLoading')
      function reset () {
        searchForm.tag = productType.value === 1 ? 'ppid' : 'mkcId'
        searchForm.keyWord = undefined
        getDatasource(1)
      }
      watch(() => addLoading.value, (val) => { // 添加完成刷新列表
        if (!val) {
          reset()
        }
      })
      provide('getDatasource', getDatasource)
      watch(() => productType.value, (val) => { // 切换tab刷新列表
        reset()
      }, { immediate: true })

      const cacheRecord = ref({})
      const cacheType = ref('single')
      function getParams () {
        const array = cacheType.value === 'single' ? [{ ...cacheRecord.value }] : rows.value
        const params = {
          areaId: areaId.value,
          prVo: array.map(it => ({
            ...it,
            type: productType.value,
            fixPhotoId: fixPhotoId.value,
          }))
        }
        return params
      }

      // 取消陈列
      async function startCancel () {
        const batch = cacheType.value === 'batch'
        const params = getParams()
        needUpdateFiles.value = false
        batch && (batchLoading.value = true)
        const res = await proxy.$store.dispatch(`operation/exhibition/${CANCEL_DISPLAY}`, params)
        batch && (batchLoading.value = false)
        if (res) {
          batchOperationForm.show = false
          needUpdateFiles.value = true
          proxy.$message.success(`${batch ? '批量' : ''}取消陈列成功，请重新上传陈列图片`)
          getDatasource(1)
        }
      }
      provide('startCancel', startCancel)
      const cancelDisplay = function (record) {
        cacheRecord.value = { ...record }
        cacheType.value = 'single'
        proxy.$confirm({
          title: `确定将商品${record.productName}取消陈列吗？`,
          onOk: async () => await startCancel()
        })
      }

      // 转瑕疵
      async function startTurn () {
        const batch = cacheType.value === 'batch'
        const params = getParams()
        needUpdateFiles.value = false
        batch && (batchLoading.value = true)
        const res = await proxy.$store.dispatch(`operation/exhibition/${TURN_FLAWS}`, params)
        batch && (batchLoading.value = false)
        if (res) {
          batchOperationForm.show = false
          needUpdateFiles.value = true
          proxy.$message.success(`${batch ? '批量' : ''}转瑕疵成功，请重新上传陈列图片`)
          getDatasource(1)
        }
      }
      provide('startTurn', startTurn)
      const turnFlaws = function (record) {
        cacheRecord.value = { ...record }
        cacheType.value = 'single'
        proxy.$confirm({
          title: `确定将商品${record.productName}转瑕疵吗？`,
          onOk: () => startTurn()
        })
      }

      const startChange = async function () {
        const single = cacheType.value === 'single'
        const form = single ? singleChangeForm : batchOperationForm
        const params = {
          oldFixPhotoId: fixPhotoId.value,
          newFixPhotoId: form.newFixPhotoId,
          ...getParams()
        }
        batchLoading.value = true
        needUpdateFiles.value = false
        const res = await proxy.$store.dispatch(`operation/exhibition/${CHANGE_CABINET_TYPE}`, params)
        batchLoading.value = false
        if (res) {
          form.show = false
          needUpdateFiles.value = true
          proxy.$message.success(`${single ? '' : '批量'}更换陈列柜成功，请重新上传陈列图片`)
          getDatasource(1)
        }
      }
      provide('startChange', startChange)
      const changeCabinet = function (record) {
        cacheRecord.value = { ...record }
        cacheType.value = 'single'
        singleChangeForm.show = true
        singleChangeForm.newFixPhotoId = fixPhotoId.value
        singleChangeForm.dpFixPhotoType = cabinetType.value
      }

      const imgUrls = ref([])
      const orgImgFids = ref([])
      const getImgUrls = async function () {
        const params = { type: '60', linkId: fixPhotoId.value }
        const res = await proxy.$store.dispatch(`operation/exhibition/${GET_CABINET_PHOTO}`, params)
        if (res) {
          const { data } = res
          data.map(d => {
            d.checkId = d.id
          })
          imgUrls.value = data
          orgImgFids.value = data.map(d => d.fid)
        }
      }
      getImgUrls()
      watch(() => needUpdateFiles.value, (val) => {
        val && (getImgUrls())
      })
      const imgUrlsChange = computed(() => {
        const imgFids = imgUrls.value.map(d => d.fid)
        return (
          [
            ...imgFids.filter(x => !orgImgFids.value.includes(x)),
            ...orgImgFids.value.filter(x => !imgFids.includes(x))
          ].length > 0
        )
      })
      const loadingSaveImg = ref(false)
      const saveImgUrls = async function () {
        if (!imgUrlsChange.value) {
          return proxy.$message.warning('图片无变更，无需保存')
        }
        const displayPhotoList = imgUrls.value.map(d => {
          const { fid, fileName, filePath, checkId } = d
          return {
            linkId: fixPhotoId.value,
            fid,
            fileName,
            filePath,
            id: checkId
          }
        })
        const params = {
          areaId: areaId.value,
          fixPhotoId: fixPhotoId.value,
          displayPhotoList
        }
        loadingSaveImg.value = true
        const res = await proxy.$store.dispatch(`operation/exhibition/${SAVE_CABINET_PHOTO}`, params)
        loadingSaveImg.value = false
        if (res) {
          proxy.$message.success('保存图片成功')
        }
      }
      const uploaderOn = {
        'update:fileList': val => {
          if (val.length > 4) {
            message.info('最多上传4张展陈图片')
            imgUrls.value = val.slice(0, 4)
          } else {
            imgUrls.value = val
          }
        }
      }
      const dataSourceLimit = computed(() => imgUrls.value?.length >= 4)
      const batchOperation = function () {
        if (!rows.value.length) {
          return proxy.$message.warning('请选择已陈列商品')
        }
        batchOperationForm.productName = rows.value.map(it => it.productName).join('；')
        batchOperationForm.operationType = 1
        batchOperationForm.newFixPhotoId = fixPhotoId.value
        batchOperationForm.dpFixPhotoType = cabinetType.value
        batchOperationForm.show = true
        cacheType.value = 'batch'
      }

      const changeTable = function (page) {
        Object.assign(pagination, page)
        getDatasource()
      }

      const printLoading = ref(false)
      const print = async function () {
        if (!rowKeys.value.length) return proxy.$message.warning('请选择已陈列商品')
        const ids = rows.value.map(d => d.relationId).join(',')
        const params = productType.value === 1 ? {
          id: ids,
          t: new Date().getTime()
        } : {
          printID: ids,
          type: 21,
          ClientNo: '',
          PrintCount: 1
        }
        printLoading.value = true
        const papiKey = productType.value === 1 ? 'printSmall' : 'print'
        const [err, res] = await to(exhibition[papiKey](params))
        printLoading.value = false
        if (err) throw err
        const { code, userMsg } = res
        if (code === 0) {
          message.success('打印成功')
        } else {
          message.error(userMsg)
        }
      }
      return {
        pagination,
        dataSource,
        productType,
        rowSelection,
        uploaderOn,
        imgUrls,
        dataSourceLimit,
        cancelDisplay,
        turnFlaws,
        loading,
        loadingSaveImg,
        batchOperation,
        changeCabinet,
        changeTable,
        saveImgUrls,
        print,
        printLoading
      }
    },
    computed: {
      columns () {
        const newColumns = this.productType === 1 ? smallDisplayColumns : bigDisplayColumns
        newColumns.forEach(it => {
          it.align = 'center'
          it.dataIndex === 'operation' && (it.customRender = this.operation)
        })
        return newColumns
      }
    },
    data () {
      return {
        operation: (text, record) => <div class="flex buttons">
          <a-button type="link" onClick={() => this.cancelDisplay(record)}>取消陈列</a-button>
          {this.productType === 1 ? <a-button onClick={() => this.turnFlaws(record)} type="link">转瑕疵</a-button> : null}
          <a-button type="link" onClick={() => this.changeCabinet(record)}>更换陈列柜</a-button>
        </div>
      }
    },
    render (createElement, context) {
      const {
        pagination,
        dataSource,
        columns,
        rowSelection,
        uploaderOn,
        imgUrls,
        dataSourceLimit,
        loading,
        batchOperation,
        changeTable,
        saveImgUrls,
        loadingSaveImg,
        print,
        printLoading
      } = this
      return <div class="display-box">
        <searchBox/>
        <div class="flex flex-align-center mt-16 flex-justify-between">
          <span>已陈列商品</span>
          <a-button onClick={batchOperation}>批量操作</a-button>
        </div>
        <div class="relative">
        <a-table
          size="small"
          pagination={pagination}
          dataSource={dataSource}
          columns={columns}
          loading={loading}
          rowSelection={rowSelection}
          bordered
          onChange={changeTable}
          rowKey={(r) => r.id}
          class="mt-8"/>
          {
            this.$tnt.xtenant < 1000
              ? <a-button class="absolute" style="bottom:12px;position:absolute" loading={printLoading}
                          onClick={print} type="primary">{printLoading ? '打印中...' : '标签打印'}</a-button>
              : null
          }
        </div>
        <div class={['upload-box', !dataSource.length ? 'mt-16' : '']}>
          <p>
            {imgUrls && imgUrls.length > 4 ? (
              <span class="font-12 grey-9">
                  <Icon class="yellow font-14" type="exclamation-circle"/>{' '}
                最多上传4张图片
                </span>
            ) : null}
          </p>
          <div class="flex relative">
            <div style="margin-top: 15px;" class="require">
              陈列柜照片反馈
            </div>
            <div id="upload-img-push">
              <Uploader
                qr-tip="上传过程中请勿关闭二维码,关闭二维码会造成上传失败"
                file-list={imgUrls}
                {...{
                  on: uploaderOn,
                  props: { onlyShowPhone: true, showRename: false }
                }}
                accept={'image/*'}
                ref="uploader"
                collection="oa-operate"
                path-key="filePath"
                disabled={dataSourceLimit}
              />
            </div>
            <a-button loading={loadingSaveImg} onClick={saveImgUrls} class="save" type="primary" size="small">保存图片</a-button>
          </div>
        </div>
        <editModal/>
        <changeCabinetModal/>
      </div>
    }
  })
</script>

<style scoped lang="scss">
.display-box {
  width: 50%;
  border-right: 1px solid #d9d9d9;
  box-sizing: border-box;
  padding: 0 16px 16px 16px;
}
.buttons {
  :deep(.ant-btn) {
    padding: 0 4px;
  }
}
.require {
  &::before {
    display: inline-block;
    margin-right: 4px;
    color: #f5222d;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: "*";
  }
}
.upload-box {
  border-top: 1px solid #d9d9d9;
  margin-bottom: 16px;
}
.save {
  position: absolute;
  left: 236px;
  top: 14px
}
</style>
