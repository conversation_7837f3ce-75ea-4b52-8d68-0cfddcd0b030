<script lang="jsx">
  import { defineComponent, inject, computed, getCurrentInstance } from 'vue'
  import { smallSearchType, bigSearchType } from '../constants'
  import { Select, Input, Button } from 'ant-design-vue'
  import { cateIdsOptions } from '../../goods-display/constants'
  export default defineComponent({
    name: 'search-box',
    setup () {
      const { proxy } = getCurrentInstance()
      const searchForm = inject('searchForm')
      const productType = inject('productType')
      const getDatasource = inject('getDatasource')
      const isAdd = inject('isAdd')
      const options = computed(() => {
        return productType.value === 1 ? (isAdd?.value ? smallSearchType.slice(0, 3) : smallSearchType) : bigSearchType
      })
      const search = function () {
        const { tag, keyWord } = searchForm
        if (productType.value === 1 && !keyWord) {
          return proxy.$message.warning(`请输入查询内容`)
        }
        const numberKeys = ['productId', 'smallDispalyId', 'mkcId', 'ppid']
        if (numberKeys.includes(tag) && keyWord && (Number(keyWord) + '' === 'NaN')) {
          const label = options.value.find(it => it.value === tag)?.label
          return proxy.$message.warning(`请输入正确的${label}`)
        }
        keyWord && (searchForm.keyWord = keyWord.replace(/(^[ \t\n\r]+)|([ \t\n\r]+$)/g, ''))
        getDatasource(1)
      }
      return {
        searchForm,
        productType,
        isAdd,
        getDatasource,
        options,
        search
      }
    },
    render (createElement, context) {
      const { searchForm, productType, isAdd, getDatasource, options, search } = this
      return <div class="flex flex-align-center">
        <Input.Group compact style="width: 300px">
          <Select
            style="width:32%"
            v-model={searchForm.tag}
            options={options}
          />
          <Input
            style="width: 68%"
            allowClear
            type="search"
            onSearch={() => search()}
            v-model={searchForm.keyWord}
            placeholder="请输入"
          />
        </Input.Group>
        {
          (productType === 2 && isAdd) ? <Select
            style="width:110px;margin-left:10px"
            v-model={searchForm.cateIds}
            options={cateIdsOptions}
            placeholder="商品分类"
            allow-clear
          ></Select> : null
        }
        <Button type="primary" class="ml-16" onClick={() => search()}>查询</Button>
      </div>
    }
  })
</script>

<style scoped>

</style>
