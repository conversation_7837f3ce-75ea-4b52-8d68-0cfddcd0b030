<script lang="jsx">
  import { ref, inject, defineComponent, getCurrentInstance, watch } from 'vue'
  import { GET_CABINET_NAME } from '@operation/store/modules/exhibition/action-types'
  import { batchOptions } from '../constants'
  export default defineComponent({
    name: 'edit-modal',
    setup () {
      const { proxy } = getCurrentInstance()
      const batchOperationForm = inject('batchOperationForm')
      const batchLoading = inject('batchLoading')
      const areaId = inject('areaId')
      const cabinetOptions = inject('cabinetOptions')
      const productType = inject('productType')
      const startCancel = inject('startCancel')
      const startTurn = inject('startTurn')
      const startChange = inject('startChange')
      const fixPhotoId = inject('fixPhotoId')
      const nameOptions = ref([])
      const loading = ref(false)
      const getNameOptions = async function () {
        const params = {
          areaId: areaId.value,
          dpFixPhotoType: batchOperationForm.dpFixPhotoType
        }
        nameOptions.value = []
        loading.value = true
        const res = await proxy.$store.dispatch(`operation/exhibition/${GET_CABINET_NAME}`, params)
        loading.value = false
        if (res) {
          nameOptions.value = res.data.map(it => ({
            label: it.name,
            value: it.id
          }))
        }
      }
      watch(() => batchOperationForm.dpFixPhotoType, (val) => {
        val && (getNameOptions())
      }, { immediate: true })

      function sureChange () {
        const { operationType } = batchOperationForm
        switch (operationType) {
        case 1: startCancel()
                break
        case 2: startTurn()
                break
        case 3: {
                  const { dpFixPhotoType, newFixPhotoId } = batchOperationForm
                  if (!dpFixPhotoType) {
                    return proxy.$message.warning('请选择柜子类型')
                  }
                  if (!newFixPhotoId) {
                    return proxy.$message.warning('请选择柜子柜子名称')
                  }
                  if (fixPhotoId.value === newFixPhotoId) {
                    return proxy.$message.warning('更换的柜子不能和当前柜子一样！')
                  }
                  startChange()
                }
                break
        }
      }

      const cancel = function () {
        batchOperationForm.operationType = 1
        batchOperationForm.dpFixPhotoType = undefined
        batchOperationForm.newFixPhotoId = undefined
      }
      return {
        batchOperationForm,
        cabinetOptions,
        nameOptions,
        productType,
        batchLoading,
        sureChange,
        cancel
      }
    },
    render (createElement, context) {
      const {
        batchOperationForm,
        batchLoading,
        sureChange,
        cabinetOptions,
        nameOptions,
        productType,
        loading,
        cancel
      } = this
      const cacheOptions = productType === 1 ? batchOptions : batchOptions.filter(it => it.value !== 2)
      return <a-modal
        width={700}
        title="批量操作"
        destroyOnClose={true}
        centered
        onOk={sureChange}
        onCancel={cancel}
        confirmLoading={batchLoading}
        v-model={batchOperationForm.show}
      >
        <a-form>
          <a-form-item class="name" label-col={{ span: 3 }} wrapper-col={{ span: 21 }} label="已选商品">
            <p class="line-24">{batchOperationForm.productName}</p>
          </a-form-item>
          <a-form-item label-col={{ span: 3 }} wrapper-col={{ span: 21 }} label="操作" required>
            <a-radio-group options={cacheOptions} v-model={batchOperationForm.operationType}/>
          </a-form-item>
          <div class="flex types" v-show={batchOperationForm.operationType === 3}>
            <a-form-item label-col={{ span: 6 }} wrapper-col={{ span: 14 }} label="柜子类型" required>
              <a-select
                placeholder="请选择"
                options={cabinetOptions}
                v-model={batchOperationForm.dpFixPhotoType} class="full-width"/>
            </a-form-item>
            <a-form-item label-col={{ span: 6 }} wrapper-col={{ span: 14 }} label="柜子名称" required>
              <a-select
                placeholder="请选择"
                options={nameOptions}
                v-model={batchOperationForm.newFixPhotoId}
                class="full-width">
                {(loading && !nameOptions.length) ? <div class="not-content flex flex-center"
                                                         slot="notFoundContent">
                  <a-spin/>
                </div> : null}
              </a-select>
            </a-form-item>
          </div>
        </a-form>
      </a-modal>
    }
  })
</script>

<style scoped lang="scss">
.types {
  :deep(.ant-form-item) {
    width: 50%;
  }
}
.line-24 {
  line-height: 24px;
}
.name {
  :deep(.ant-form-item-label) {
    line-height: 24px;
  }
}
</style>
