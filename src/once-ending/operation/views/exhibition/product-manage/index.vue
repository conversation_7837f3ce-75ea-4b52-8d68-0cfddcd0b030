<script lang="jsx">
  import { defineComponent, ref, provide, getCurrentInstance } from 'vue'
  import tabs from './components/tabs'
  import displayProduct from './components/display-product'
  import addProduct from './components/add-product'
  export default defineComponent({
    name: 'index',
    components: { tabs, displayProduct, addProduct },
    setup () {
      const { proxy } = getCurrentInstance()
      const productType = ref(1)
      provide('productType', productType)
      const fixPhotoId = ref('')
      const areaId = ref('')
      const cabinetType = ref('')
      const cabinetName = ref('')
      const showCount = ref('')
      const addLoading = ref(false)
      const needUpdateFiles = ref(false)
      const cabinetLabelStr = ref('')
      provide('needUpdateFiles', needUpdateFiles)
      provide('addLoading', addLoading)
      provide('fixPhotoId', fixPhotoId)
      provide('areaId', areaId)
      provide('cabinetType', cabinetType)
      provide('cabinetName', cabinetName)
      provide('showCount', showCount)
      provide('cabinetLabelStr', cabinetLabelStr)
      const getForm = function () {
        const { form } = proxy.$route.query
        if (form) {
          const cacheForm = JSON.parse(form)
          fixPhotoId.value = cacheForm.fixPhotoId
          areaId.value = cacheForm.areaId
          cabinetType.value = cacheForm.cabinetType
          cabinetName.value = cacheForm.cabinetName
          showCount.value = cacheForm.showCount
          cabinetLabelStr.value = cacheForm.cabinetLabelStr
        }
      }
      getForm()

      window.addEventListener('popstate', function (e) {
        console.log('e', e)
      }, false)
    },
    render (createElement, context) {
      return <page>
        <div class="containers">
          <tabs/>
          <div class="flex">
            <displayProduct/>
            <addProduct/>
          </div>
        </div>
      </page>
    }
  })
</script>

<style scoped lang="scss">
.containers {
  padding: 8px;
  background: #FFFFFF;
  min-width: 1360px;
}
:deep(.ant-table-small > .ant-table-content > .ant-table-body) {
  margin: 0px;
}
</style>
