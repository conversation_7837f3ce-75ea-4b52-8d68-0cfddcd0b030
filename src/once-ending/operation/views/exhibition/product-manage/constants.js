export const tabsOptions = [
  { label: '小件商品', value: 1 },
  { label: '大件商品', value: 2 }
]
export const smallSearchType = [
  { label: '商品名称', value: 'productName' },
  { label: '商品ID', value: 'productId' },
  { label: 'ppid', value: 'ppid' },
  { label: '陈列编号', value: 'smallDispalyId' }
]
export const bigSearchType = [
  { label: 'mkc_id', value: 'mkcId' },
  { label: 'ppid', value: 'ppid' },
  { label: '串号', value: 'imei' },
  { label: '商品名称', value: 'productName' },
]
export const batchOptions = [
  { label: '取消陈列', value: 1 },
  { label: '转瑕疵', value: 2 },
  { label: '更换陈列柜', value: 3 },
]
export const smallDisplayColumns = [
  {
    title: '陈列编号',
    dataIndex: 'relationId',
    width: '15%'
  },
  {
    title: 'ppid',
    dataIndex: 'ppid',
    width: '15%'
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '25%'
  },
]
export const bigDisplayColumns = [
  {
    title: 'mkc_id',
    dataIndex: 'relationId',
    width: '16%'
  },
  {
    title: '串号',
    dataIndex: 'imei',
    width: '16%'
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '22%'
  }
]
export const smallAddColumns = [
  {
    title: '商品id',
    dataIndex: 'productId',
    width: '13%'
  },
  {
    title: 'ppid',
    dataIndex: 'ppid',
    width: '13%'
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
  },
  {
    title: '陈列数量',
    dataIndex: 'addDisplayCount',
    width: '15%'
  },
  {
    title: '可陈列数量',
    dataIndex: 'stockCount',
    width: '16%'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '16%'
  }
]
export const bigAddColumns = bigDisplayColumns.map(it => ({
  ...it,
  dataIndex: it.dataIndex === 'relationId' ? 'mkcId' : it.dataIndex
}))
