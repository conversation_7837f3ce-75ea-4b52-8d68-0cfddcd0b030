export const originColumns = [
  {
    title: '专区名称',
    dataIndex: 'name',
    width: 110
  },
  {
    title: '专区类型',
    dataIndex: 'typeName',
    width: 110
  },
  {
    title: '关联勋章',
    dataIndex: 'achievementMedalName',
    width: 220
  },
  {
    title: '陈列规范',
    dataIndex: 'displaySpecify',
    width: 280
  },
  {
    title: '关联驻店',
    dataIndex: 'zhudianRoleName',
    width: 220,
    showType: 'longText'
  },
  {
    title: '商品分类',
    dataIndex: 'productCategoryName',
    width: 240,
    showType: 'longText'
  },
  {
    title: '商品品牌',
    dataIndex: 'productBrandName',
    width: 240,
    showType: 'longText'
  },
  {
    title: '更新人',
    dataIndex: 'updateUserName',
    width: 110
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 110
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 110,
    fixed: 'right'
  },
]

export const originEditForm = {
  id: undefined,
  type: undefined,
  name: undefined,
  achievementMedalIds: undefined,
  displaySpecify: undefined,
  attachmentList: [],
  zhudianList: undefined,
  productCategoryList: undefined,
  productBrandList: undefined,
  productIdList: undefined,
  storeList: [],
  color: ''
}

export const rules = {
  name: [{ required: true, trigger: ['blue', 'change'], message: '请输入专区名称' }],
  type: [{ required: true, trigger: ['blue', 'change'], message: '请选择专区类型' }],
  storeList: [{ required: true, trigger: 'change', message: '请选择适用门店' }],
  color: [{ required: true, trigger: 'change', message: '请选择颜色' }],
}
