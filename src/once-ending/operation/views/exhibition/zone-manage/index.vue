<script lang="jsx">
  import Vue, { defineComponent, toRefs, ref, getCurrentInstance } from 'vue'
  import { createState } from './hooks/use-state'
  import { NiTable, NiImg, NiAreaSelect } from '@jiuji/nine-ui'
  import { originColumns, rules } from './constants'
  import clear from '~/assets/images/clear.png'
  import 'viewerjs/dist/viewer.css'
  import Viewer from 'v-viewer'
  import Category from '@operation/components/category.vue'
  import Brand from '@operation/components/brand.vue'
  import exhibition from '@operation/api/exhibition'
  import { message } from 'ant-design-vue'
  import { ColorPicker } from 'element-ui'
  Vue.use(Viewer).use(ColorPicker)

  const labelCol = { span: 3 }
  const wrapperCol = { span: 21 }

  export default defineComponent({
    components: {
      NiTable,
      NiImg,
      Category,
      Brand,
      NiAreaSelect
    },
    setup () {
      const { proxy } = getCurrentInstance()
      const {
        state,
        toShowEdit,
        toSave,
        doUpload,
        viewImage,
        editRef,
        toDelete,
        handleTableChange
      } = createState()

      const importStoreLoading = ref(false)
      const fileRef = ref(null)
      const formRef = ref(null)
      const importStore = function () {
        if (importStoreLoading.value) return message.warning('正在导入中，请稍后')
        fileRef.value.click()
      }

      const selectFile = async function (e) {
        let file = e.target.files[0]
        if (file && file.length === 0) return
        /* eslint-disable */
        let form = new FormData()
        form.append('file', file)
        proxy.$indicator.open()
        importStoreLoading.value = true
        const res = await exhibition.importArea(form).catch(err => {
          message.error(err.message)
          formRef.value.reset()
        })
        proxy.$indicator.close()
        formRef.value.reset()
        importStoreLoading.value = false
        if(res){
          message.success('导入成功')
          const list = state.editForm.storeList.concat(res.map(d => d.id))
          state.editForm.storeList = Array.from(new Set(list))
        }

      }

      return {
        ...toRefs(state),
        toShowEdit,
        toSave,
        doUpload,
        viewImage,
        editRef,
        toDelete,
        handleTableChange,
        importStore,
        importStoreLoading,
        formRef,
        fileRef,
        selectFile
      }
    },
    data () {
      return {
        customRenderMap: new Map([
          [
            'normal',
            (text, record) => <span>{ text || '--' }</span>
          ],
          [
            'displaySpecify',
            (text, record) => <span>
              { text?.length > 50 ? <a-popover>
                <div slot="content" style="maxWidth: 600px">{ text }</div>
                <span>{ text.substring(0, 50) }...</span>
              </a-popover> : text ? <span>{text}</span> : record.attachmentList?.length ? <span v-viewer="{movable: false}">
                { record.attachmentList.map(file => <ni-img class="single-image" src={file.filePath}/>) }
              </span> : '--' }
            </span>
          ],
          [
            'longText',
            (text) => <span>
              { text?.length > 50 ? <a-popover>
                <div slot="content" style="maxWidth: 600px">{ text }</div>
                <span>{ text.substring(0, 50) }...</span>
              </a-popover> : text || '--' }
            </span>
          ],
          [
            'operation',
            (text, record) => <div class="flex flex-center buttons">
              <a-button type="link" onClick={() => { this.toShowEdit(record) }}>编辑</a-button>
              <a-button type="link" onClick={() => { this.toDelete(record) }}>删除</a-button>
            </div>
          ]
        ])
      }
    },
    computed: {
      columns () {
        return originColumns.map(item => {
          const cache = { ...item, align: 'center' }
          if (item.showType === 'longText') {
            cache.customRender = this.customRenderMap.get('longText')
          } else {
            cache.customRender = this.customRenderMap.get(item.dataIndex) || this.customRenderMap.get('normal')
          }
          return cache
        })
      }
    },
    render (createElement, context) {
      const {
        columns,
        loading,
        dataSource,
        toShowEdit,
        editForm,
        editLoading,
        toSave,
        doUpload,
        viewImage,
        specAreaType,
        zhudianRole,
        listEnableMedalOptions,
        pagination,
        handleTableChange,
        importStore,
        selectFile
      } = this
      return <page>
        <ni-table
          columns={columns}
          loading={loading}
          pagination={false}
          onChange={handleTableChange}
          data-source={dataSource}>
          <div slot="tool">
            <a-button onClick={() => toShowEdit(null)} type="primary">添加专区</a-button>
          </div>
        </ni-table>
        <a-modal
          v-model={this.showEdit }
          title={(editForm.id ? '编辑' : '添加') + '专区'}
          destroyOnClose={true}
          maskClosable={false}
          confirmLoading={editLoading}
          onOk={toSave}
          width={820}>
          <a-form-model
            { ...{ props: { model: editForm } } }
            rules={rules}
            class="form-model"
            ref="editRef"
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 17 }}>
            <a-form-model-item label="专区名称" prop="name">
              <a-input maxLength={10} v-model={editForm.name} placeholder="请输入" allowClear>
                <span slot="suffix" class="grey-9 ml-4 font-12">{editForm.name?.length || 0}/10</span>
              </a-input>
            </a-form-model-item>
            <a-form-model-item label="专区类型" prop="type">
              <a-select v-model={editForm.type} options={specAreaType} placeholder="请选择" allowClear/>
            </a-form-model-item>
            <a-form-model-item label="关联勋章">
              <a-select mode="multiple"
                        v-model={editForm.achievementMedalIds}
                        showSearch
                        placeholder="输入勋章名称搜索"
                        allowClear
                        maxTagCount={1}
                        options={listEnableMedalOptions}
                        optionFilterProp="children"/>
            </a-form-model-item>
            <a-form-model-item label="关联驻店">
              <a-select mode="multiple"
                        v-model={editForm.zhudianList}
                        showSearch
                        placeholder="请选择"
                        allowClear
                        labelInValue={true}
                        maxTagCount={1}
                        options={zhudianRole}
                        optionFilterProp="children"/>
            </a-form-model-item>
            <a-form-model-item label="商品分类">
              <category
                v-model={editForm.productCategoryList}
                multiple={true}
                labelInValue={true}
                treeCheckable={true}
                maxTagCount={1}
                onChange={() => { editForm.productBrandList = undefined }}
                placeholder="请选择"
                allowClear/>
            </a-form-model-item>
            <a-form-model-item label="商品品牌">
              <brand
                cateId={editForm.productCategoryList?.map(it => it.value)}
                v-model={editForm.productBrandList}
                labelInValue={true}
                placeholder="请选择"
                maxTagCount={1}
                allowClear/>
            </a-form-model-item>
            <a-form-model-item
              labelCol={labelCol}
              wrapperCol={wrapperCol}
              style="width: 100%;padding-right: 17px"
              label="商品id">
              <a-select placeholder="输入后按回车回填内容" allowClear={true} v-model={editForm.productIdList} mode="tags"/>
            </a-form-model-item>
            <a-form-model-item
              labelCol={labelCol}
              wrapperCol={wrapperCol}
              prop="storeList"
              style="width: 100%;padding-right: 17px"
              autoLink={false}
              ref="storeList"
              label="适用门店">
              <div class="flex">
                <NiAreaSelect
                  v-model={editForm.storeList}
                  placeholder="请选择"
                  multiple={true}
                  allowClear={true}
                  class="flex-child-average"
                  onChange={() => { this.$refs.storeList.onFieldChange() }}
                  maxTagCount={1}/>
                <div class="ml-8 flex-child-noshrink" style="height: 32px;margin-top: -3px;">
                  <a onClick={() => { importStore() }}>导入</a>
                  <a class="ml-8" href="https://img2.ch999img.com/newstatic/52538/1a4231f26df3bba4.xlsx">下载模板</a>
                  <form ref="formRef">
                    <input style="display: none" ref="fileRef" type="file" onChange={selectFile}></input>
                </form>
                </div>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="陈列规范内容"
              class="relative"
              labelCol={labelCol}
              wrapperCol={wrapperCol}
              style="width: 100%;padding-right: 17px">
              <a-textarea class="mt-4" maxLength={500} autoSize={{ minRows: 1, maxRows: 5 }} v-model={editForm.displaySpecify} placeholder="请选择" allowClear/>
              <div class="flex flex-justify-end grey-9 font-12 tip">{editForm.displaySpecify?.length || 0}/500</div>
            </a-form-model-item>
            <a-form-model-item
              label="陈列规范图片"
              labelCol={labelCol}
              wrapperCol={wrapperCol}
              style="margin-top: -12px;width: 100%;padding-right: 17px">
              <div class="flex flex-wrap">
                { editForm.attachmentList?.length < 9 ? <div onClick={doUpload} class="add-file single-image flex flex-center">+</div> : null }
                { editForm.attachmentList?.map((file, index) => <div class="single-image">
                  { file.loading ? <div class="flex flex-center full-width full-height">
                    <a-icon class="font-16" type="loading" />
                  </div> : <div class="relative full-width full-height">
                    <ni-img onTap={() => viewImage(index)} class="full-width full-height" style="border-radius: 4px" src={file.filePath}/>
                    <ni-img onTap={() => { editForm.attachmentList.splice(index, 1) }} src={clear} class="delete"/>
                  </div> }
                </div>) }
              </div>
            </a-form-model-item>
            {
              this.$tnt.xtenant < 1000 ? <a-form-model-item
                prop="color"
                label="颜色配置"
                labelCol={labelCol}
                wrapperCol={wrapperCol}
                style="margin-top: -12px;width: 100%;padding-right: 17px">
                <el-color-picker
                  class="color-pick"
                  v-model={editForm.color}
                  show-alpha={true}
                ></el-color-picker>
              </a-form-model-item> : null
            }
          </a-form-model>
        </a-modal>
        <div class="images" v-viewer="{movable: false}" style="display: none">
          { editForm.attachmentList?.map((file, index) => <img class={'show-image' + index} key={index} src={file.filePath}/>) }
        </div>
      </page>
    }
  })
</script>
<style lang="scss">
.el-button{
  &.el-color-dropdown__link-btn{
    padding: 7px 15px !important;
  }
}
</style>
<style scoped lang="scss">
:deep(.buttons) {
  .ant-btn {
    height: auto;
  }
}
:deep(.single-image) {
  width: 80px;
  height: 80px;
  margin: 0 12px 10px 0;
  cursor: pointer;
  position: relative;
  border-radius: 4px;
}
.ml-4 {
  margin-left: 4px;
}
.mt-4 {
  margin-top: 4px;
}
.tip {
  line-height: 12px;
  position: absolute;
  right: 0;
  bottom: -14px;
}
.add-file {
  box-sizing: border-box;
  border: 1px dashed #d9d9d9;
  font-size: 24px;
}
.delete {
  width: 14px;
  height: 14px;
  position: absolute;
  right: -6px;
  top: -6px;
}
:deep(.form-model) {
  display: flex;
  flex-wrap: wrap;
  .ant-form-item {
    width: 50%;
  }
}
.color-pick{

}
</style>
