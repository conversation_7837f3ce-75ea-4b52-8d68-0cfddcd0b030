import { reactive, provide, inject, getCurrentInstance, ref } from 'vue'
import exhibition from '@operation/api/exhibition'
import { originEditForm } from '../constants'
import { cloneDeep } from 'lodash'
import { useNineUpload } from '~/once-ending/common/utils/useNineUpload'

const { zoneManage } = exhibition

const key = Symbol('useState')
export function useState () {
  return inject(key)
}
export function createState () {
  const { proxy } = getCurrentInstance()
  const state = reactive({
    loading: false,
    dataSource: [],
    editForm: cloneDeep(originEditForm),
    showEdit: false,
    editLoading: false,
    uploadLoading: false,
    typeOptions: {},
    specAreaType: [],
    zhudianRole: [],
    listEnableMedalOptions: [],
    pagination: {
      pageSize: 10,
      current: 1,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      pageSizeOptions: ['10', '20', '30', '50']
    },
  })
  async function fetchData (type) {
    type === 'search' && (state.pagination.current = 1)
    const { current, pageSize } = state.pagination
    const params = {
      current,
      size: pageSize,
    }
    state.loading = true
    const res = await zoneManage.getList(params).catch(err => {
      proxy.$message.error(err.message)
    })
    state.loading = false
    if (res) {
      // const { total, records } = res
      // state.pagination.total = total
      // state.dataSource = records
      state.dataSource = res
    }
  }
  fetchData('search')

  function handleTableChange (pagination) {
    Object.assign(state.pagination, pagination)
    fetchData()
  }

  async function getListEnableMedal () {
    const res = await zoneManage.getListEnableMedal().catch(err => {
      proxy.$message.error(err.message)
    })
    if (res) {
      state.listEnableMedalOptions = res || []
    }
  }
  getListEnableMedal()

  async function getEnums () {
    const res = await exhibition.getDpFixPhotoEnums()
    if (res?.code === 0) {
      state.specAreaType = res?.data?.specAreaType
      state.zhudianRole = res?.data?.zhudianRole
    }
  }
  getEnums()
  async function toShowEdit (record) {
    state.showEdit = true
    if (record) {
      const res = await zoneManage.getDetail({ id: record.id }).catch(err => {
        proxy.$message.error(err.message)
      })
      if (res) {
        Object.assign(state.editForm, {
          ...res,
          achievementMedalIds: res.achievementMedalIds ? (res.achievementMedalIds + '')?.split(',') : undefined,
          productCategoryList: res.productCategoryList?.map(it => ({ ...it, value: it.key })),
        })
      }
    } else {
      state.editForm = cloneDeep(originEditForm)
    }
  }

  const editRef = ref()
  async function toSave () {
    editRef.value.validate(async valid => {
      if (valid) {
        const { achievementMedalIds, productCategoryList, ...other } = state.editForm
        const params = {
          ...other,
          achievementMedalIds: achievementMedalIds ? achievementMedalIds.join(',') : undefined,
          productCategoryList: productCategoryList?.map(it => ({ ...it, key: it.value }))
        }
        state.editLoading = true
        const res = await zoneManage.updateZone(params).catch(err => {
          proxy.$message.error(err.message)
        })
        state.editLoading = false
        if (res) {
          proxy.$message.success(`${state.editForm.id ? '编辑' : '保存'}成功`)
          state.showEdit = false
          fetchData()
        }
      } else {
        proxy.$message.warning('请先完善数据')
      }
    })
  }

  function toDelete (record) {
    proxy.$confirm({
      title: '确定删除此条数据吗？',
      onOk: async () => {
        const res = await zoneManage.deleteZone({ id: record.id }).catch(err => {
          proxy.$message.error(err.message)
        })
        if (res) {
          proxy.$message.success('删除成功')
          // const { pagination: { current }, dataSource } = state
          // if (dataSource.length === 1 && current > 1) {
          //   state.pagination.current -= 1
          // }
          fetchData()
        }
      }
    })
  }

  const cacheCount = ref(0)
  function onProgress ({ fileCount }) {
    if (cacheCount.value === 0) {
      state.uploadLoading = true
      cacheCount.value = fileCount
      const maxCount = 9 - state.editForm.attachmentList?.length || 0
      const needCount = fileCount <= maxCount ? fileCount : maxCount
      for (let i = 0; i < needCount; i++) {
        state.editForm.attachmentList.push({
          loading: true
        })
      }
    }
  }
  function verifyFile (files) {
    let pass = true
    if (state.editForm.attachmentList?.length + files.length > 9) {
      proxy.$message.warning('最多可上传9个文件')
      pass = false
      return pass
    }
    for (let i = 0; i < files.length; i++) {
      const name = files[i].name
      const regImg = /.(jpg|jpeg|png)$/i
      if (!regImg.test(name)) {
        proxy.$message.warning('只能上传.pdf .jpg .png .jpeg格式文件')
        pass = false
        break
      }
    }
    return pass
  }
  async function doUpload () {
    cacheCount.value = 0
    state.originFiles = cloneDeep(state.editForm.attachmentList)
    useNineUpload({
      multiple: true,
      accept: '.pdf,.jpg,.png,.jpeg',
      onProgress: onProgress
    }, verifyFile).then(res => {
      if (res) {
        state.editForm.attachmentList = state.originFiles.concat(res)
      }
    }).finally(() => {
      state.uploadLoading = false
    })
  }

  function viewImage (index) {
    const viewer = proxy.$el.querySelector('.show-image' + index)
    viewer.click()
  }

  const o = {
    state,
    fetchData,
    toShowEdit,
    toSave,
    toDelete,
    doUpload,
    viewImage,
    editRef,
    handleTableChange
  }
  provide(key, o)
  return o
}
