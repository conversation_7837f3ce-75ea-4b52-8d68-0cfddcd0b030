$gap:                               8px;
$margin-base:                       $gap*3; //24px
$area-select-height:                150px;
.m-0{margin:0}
i, span{
  font-style: normal;
  display: inline-block;
}
.ant-btn,
.form-item{
  margin-right:$gap;
}

.spin-content{
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 40vh;
  z-index: 3;
  background: rgba(100, 100, 100, 0.5);
}

.ant-tabs-tabpane{
  > div > span{
    width: 100%;
  }
}

.ant-card{
  margin-bottom: $margin-base;
}

.label{
  min-width: 3.5em;
  text-align: right;
  flex-shrink: 1;

  &.label-xs{
    min-width: 2.5em;
  }
}
.value{
  min-width: 2em;
}

.feild-must::before{
  content: '*';
  color: red;
}

.config{
  flex: 1 0 400px;
  min-height: $area-select-height;
}

.card-header{
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: $gap * 2;
  margin: -$gap 0 $margin-base;
}

.card-footer{
  .ant-input-number{
    width: 65px;
  }
}

  /*选中样式*/
.chosen {
    border: solid 2px #3089dc;
}
  /*选中样式*/
.ghost {
    background: #3089dc;
}

.mover{
  cursor: move;
}