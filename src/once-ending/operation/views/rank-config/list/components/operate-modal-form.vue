<template>
  <a-modal
    :visible="operateConfigModalVisible"
    class="rule-modal"
    title='运营管控分规则编辑'
    okText='确定'
    :maskClosable="false"
    @cancel="onCancelModal"
    @ok="handleOk"
  >
      <a-form-item label='运营管控分计算逻辑：'>
        <a-textarea v-model="currentEditItem.performanceComponents1" :maxLength='550' placeholder="请输入550字以下绩效分构成描述" :rows="4" />
      </a-form-item>
      <a-form-item label='排序规则：'>
        <a-textarea v-model="currentEditItem.sortRules1" :maxLength='550' placeholder="请输入550字以下排序规则" :rows="4" />
      </a-form-item>
      <a-form-item label='运营管控分排名关联规则：'>
        <a-textarea v-model="currentEditItem.associationRules1" :maxLength='550' placeholder="请输入550字以下绩效排名系数的关联规则" :rows="4" />
      </a-form-item>
  </a-modal>
</template>

<script>
  import { toRefs } from 'vue'
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions'

  export default {
    name: 'rule-modal-form',
    setup () {
      const { state, setState } = useState()
      const { onCancelModal } = useActions()
      const handleOk = () => {
        setState('operateConfigModalVisible', false)
      }
      return {
        ...toRefs(state),
        onCancelModal,
        handleOk
      }
    }
  }
</script>

<style lang="scss" scope>
  .rule-modal{
    .ant-form-item-children{
      width: 100%;
    }
  }
</style>
