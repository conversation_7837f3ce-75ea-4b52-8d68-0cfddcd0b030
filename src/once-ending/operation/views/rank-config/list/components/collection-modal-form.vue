<template>
  <a-modal
    :visible="rankConfigModalVisible"
    :title='modalTitle'
    okText='确定'
    :centered="true"
    @cancel="onCancelModal"
    @ok="handleRankConfigOk"
  >
    <!-- 占比排名 -->
    <div class="form-modal-container" v-if="currentEditItem.fixedOrNormalityType === 1" >
      <div class="form-item header">
        <i class="label">名次(排名从高到低)</i>
        <i>绩效系数</i>
      </div>
      <div class="form-item">
        <div class="flex flex-align-center">
          <a-input-number
          :min="0" :max="100"
          v-model="currentEditItem.top10Point"/>
          <i class="unit">%</i>
        </div>
        <a-input-number
          :min="0" :max="100"
          v-model="currentEditItem.rankOneRatio"/>
      </div>
      <div class="form-item">
        <div class="flex flex-align-center">
          <a-input-number
          :min="0" :max="100"
          v-model="currentEditItem.top25Point"/>
          <i class="unit">%</i>
        </div>
        <a-input-number
          :min="0" :max="100"
          v-model="currentEditItem.rankTwoRatio"/>
      </div>
      <div class="form-item">
        <div class="flex flex-align-center">
          <a-input-number
          :min="0" :max="100"
          v-model="currentEditItem.center30Point"/>
          <i class="unit">%</i>
        </div>
        <a-input-number
          :min="0" :max="100"
          v-model="currentEditItem.rankThreeRatio"/>
      </div>
      <div class="form-item">
        <div class="flex flex-align-center">
          <a-input-number
          :min="0" :max="100"
          v-model="currentEditItem.last25Point"/>
          <i class="unit">%</i>
        </div>
        <a-input-number
          :min="0" :max="100"
          v-model="currentEditItem.rankFourRatio"/>
      </div>
      <div class="form-item">
        <div class="flex flex-align-center">
          <a-input-number
          :min="0" :max="100"
          v-model="currentEditItem.last10Point"/>
          <i class="unit">%</i>
        </div>
        <a-input-number
          :min="0" :max="100"
          v-model="currentEditItem.rankFiveRatio"/>
      </div>
    </div>
    <!-- 固态排名 -->
    <div v-else class="form-modal-container tw">
      <div class="tips" v-if="!selectAreaNum">请先选择区域</div>
      <template v-else>
        <div class="form-item header">
          <i class="label">名次(排名从高到低)</i>
          <i>绩效系数</i>
        </div>
        <div class="form-item"
          v-for="(item, index) in currentEditItem.areaCodesArr"
          :key='index'>
          <i class="label">{{index + 1}}</i>
          <a-input-number
            :min="0" :max="100"
            v-model="currentEditItem.fixedRankConfigArr[index]"
          />
        </div>
      </template>
    </div>
  </a-modal>
</template>

<script>
  import { computed, toRefs } from 'vue'
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions'

  export default {
    name: 'collection-modal-form',
    setup () {
      const { state } = useState()
      const {
        onCancelModal,
        handleRankConfigOk
      } = useActions()

      return {
        ...toRefs(state),
        modalTitle: computed(() => {
          if (!state.enumData.kinds) return null
          let currentValue = state.enumData?.kinds.find(item => (item.value + '') === state.currentKinds)
          let titleKind = currentValue?.label
          let titleFixedOrNormalityType = state.enumData?.fixedOrNormalityTypes[state.currentEditItem.fixedOrNormalityType - 1]?.label
          return `${titleKind} ${state.currentEditItem.teamName} ${titleFixedOrNormalityType} 配置`
        }),
        onCancelModal,
        handleRankConfigOk
      }
    }
  }
</script>

<style lang="scss" scope>
  .form-modal-container{
    .form-item{
      margin:8px auto;
      width: 95%;
      display: flex;
      justify-content: space-around;

      &.header i{
        font-weight: 600;
        margin-bottom: 0.5em;
      }

      .unit{
        display: inline-block;
        margin-left: 5px;
      }
    }
    .tips{
      color:#00a0e9;
    }
  }

</style>
