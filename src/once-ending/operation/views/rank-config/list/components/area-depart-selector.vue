<template>
  <a-tree-select
    :treeData="treeData"
    v-model="valueLocal"
    showSearch
    allowClear
    :maxTagCount="maxTagCount"
    :showCheckedStrategy="showType"
    searchPlaceholder="请输入"
    style="width: 100%"
    v-bind="$attrs"
    :dropdownStyle="_dropdownStyle"
    :treeCheckable="treeCheckable || $attrs.treeCheckable"
    @change="onChange"
  />
</template>

<script>
  import { TreeSelect } from 'ant-design-vue'
  import { treeToArray, treeWalk } from '~/util/treeUtils'

  const SHOW_ALL = TreeSelect.SHOW_ALL
  const SHOW_PARENT = TreeSelect.SHOW_PARENT
  const SHOW_CHILD = TreeSelect.SHOW_CHILD

  export default {
    name: 'AreaDepartSelector',
    model: {
      prop: 'value', // string[], 不管是不是multiple模式
      event: 'change'
    },
    props: {
      value: null,
      // area-区域,department-部门,role=部门角色, departmentAuth=登录为分公司时只能查看该分公司以下的部门
      type: {
        validator: val => ['area', 'department', 'role', 'departmentAuth'].includes(val),
        default: 'area'
      },
      maxTagCount: {
        type: Number,
        default: 1
      },
      /**
       * 传入对树数据操作的函数
       * @param {Array<{ id: string, label: string, children?: Array<any>}>} treeData
       * @returns treeData
       */
      filterNode: {
        type: Function,
        default: treeData => treeData
      },
      // 只能选择末节点
      leafOnly: {
        type: Boolean,
        default: false
      },
      // 回填方式
      showType: {
        type: String,
        default: 'SHOW_PARENT'
      },
      dropdownStyle: null
    },
    computed: {
      _dropdownStyle () {
        return {
          maxHeight: '300px',
          ...this.dropdownStyle
        }
      }
    },
    data () {
      return {
        SHOW_PARENT,
        SHOW_CHILD,
        SHOW_ALL,
        treeData: [],
        treeDataFlatten: [],
        valueLocal: [],
        treeCheckable: false
      }
    },
    created () {
      // 解决初始值不响应页面的问题
      if (!this.value) {
        this.valueLocal = undefined
      } else {
        let tmpValue = this.value
        if (!Array.isArray(tmpValue)) tmpValue = [tmpValue]
        this.valueLocal = tmpValue.map(it => typeof it === 'object' ? String(it.id) : { value: it })
      }
      this.loadTreeData()
    },
    methods: {
      async loadTreeData () {
        let res = null
        res = sessionStorage.getItem('departmentorarea')
        if (!res) {
          res = await this.$api.common.getAreaTree(this.type)
          if (res.code !== 0) return
          sessionStorage.setItem('departmentorarea', JSON.stringify(res))
        } else {
          res = JSON.parse(res)
        }

        let treeData = res.data
        if (this.type === 'area') {
          treeData = res.data.areaOptions
        }
        if (this.type === 'department') {
          treeData = res.data.departOptions
        }

        let treeDataFlatten = treeToArray(treeData)
        treeDataFlatten.forEach(it => {
          it.key = it.id
          it.value = it.id
          it.title = it.label
        })
        treeDataFlatten.sort((a, b) => a.key > b.key ? 1 : -1)

        if (this.filterNode) {
          treeData = this.filterNode(treeData)
        }

        if (this.leafOnly) {
          this.treeCheckable = true
          treeWalk(treeData, node => (node.disableCheckbox = !!node?.children?.length))
        }

        Object.assign(this, { treeData, treeDataFlatten })
      },

      onChange (value, label, extra) {
        if (!Array.isArray(value)) value = [value]
        value = value.filter(item => item)
        this.$emit('change', value, label, extra)
      }

    }
  }
</script>
