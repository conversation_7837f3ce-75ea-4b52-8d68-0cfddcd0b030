<!--
   @PageName: 根据需求二次加工区域选择组件
   @Description: 区域组件在一个页面上出现较多时 && 需要根据大小区对树节点进行相应的处理 时用
                根据大、小区、门店进行相应的计算处理【大】只能选大区、【小】只能选小区、【门店】只能选门店
                为减少区域组件的计算：1.用了缓存组件，2.列表加载时正常返回树，新建与编辑时作相应计算
   @Date: 2021-08-25 10:00:00
   @Date: 重构 2022-07-04
-->
<script lang="jsx">
  import AreaSelector from './area-depart-selector'
  import { treeWalk, treeShake } from '~/util/treeUtils'

  export default {
    components: {
      AreaSelector
    },
    props: {
      // 此组件主要依据此作相应处理
      // [areaStore:门店, areaSmall:小区, arealarge:大区]
      areaType: {
        type: String,
        default: 'areaLarge'
      },
      form: {
        type: Object,
        default: () => {}
      },
      type: {
        type: [String],
        default: 'area' // area-区域,department-部门,role=部门角色
      },
      disabled: {
        default: false
      },
      placeholder: {
        type: String,
        default: '地区'
      },
      maxTagCount: {
        type: Number,
        default: 30
      },
      lazyTime: {
        type: Number,
        default: 300 // 排行页面，一般情况足够，若还出现请求次数过多，则延长时间
      },
    },

    data: () => ({
      showType: 'SHOW_CHILD', // 区域组件回显方式
      predicateFn: null, // 根据区域计算func
      filter: false, // 过滤没有子节点的数据
      componentKey: 0, // 强制更新组件
      initSuccess: false,
      delNode: (treeData) => treeData // 为了提高性能：页面加载时不去计算区域组件树
    }),

    render () {
      return <div class="flex area-selector">
        <i class="label feild-must">区域：</i>
        { this.initSuccess && <area-selector
            treeCheckable
            treeNodeFilterProp='label'
            ref='areaSelector'
            treeCheckStrictly
            type={this.type}
            placeholder={this.placeholder}
            showType={this.showType}
            v-model={this.form.areaCodesArr}
            disabled={this.disabled}
            filterNode={this.delNode}
            maxTagCount={this.maxTagCount}
            key={this.componentKey}
          />
        }
      </div>
    },

    created () {
      // 区域组件虽做了缓存，但是刚加载此页面时，大区有n条数据，区域组件会去请求n次，
      // 因此在此页面上，请求一次区域数据后做缓存，组件做了延迟加载优化，
      this.lazy()
      // 新建:区域组件作大小区规则计算
      if (!this.form.areaCodesArr.length) this.forceRerender()
    },

    watch: {
      areaType (val, old) {
        const showType = {
          store: 'SHOW_CHILD',
          small: 'SHOW_ALL',
          large: 'SHOW_CHILD'
        }
        if (val !== old) {
          // 大、小区过滤掉第一层的叶子节点
          this.filter = val !== 'areaStore'
          // 处理大、小区回显方式
          this.showType = showType[val]
        }
      },
      // 编辑:区域组件作大小区规则计算
      disabled (val) {
        if (!val) this.forceRerender()
      },
    },

    methods: {
      lazy () {
        setTimeout(() => {
          this.initSuccess = true
        }, this.lazyTime)
      },
      forceRerender () {
        this.delNode = this.filterNode
        this.componentKey += 1
      },
      /**
       * @param {treeData}
       * @returns {dealData} 处理后的数据
       */
      filterNode (treeData) {
        const { areaType } = this
        let callBackAreaFn = {
          // 只能选门店
          areaStore (node) {
            // node.disabled = !!node.children
            node.disabled = node.dataType !== 0
          },
          // 只能选小区
          areaSmall (node) {
            node.disabled = node.dataType !== 4
          },
          // 只能选大区
          areaLarge (node) {
            node.disabled = node.dataType !== 3
            if (node.disabled) node.children = null
          }
        }
        // 减少计算量用法（场景：分类与列表）：缓存
        let delData = sessionStorage.getItem(areaType)
        if (!delData) {
          delData = treeWalk(treeData, callBackAreaFn[areaType])
          sessionStorage.setItem(areaType, JSON.stringify(delData))
        } else {
          delData = JSON.parse(delData)
        }
        return treeShake(delData, node => !node.disabled)

        // 不用减少计算用法 return treeWalk(treeData, callBackAreaFn[areaType])
      },
    }
  }
</script>
