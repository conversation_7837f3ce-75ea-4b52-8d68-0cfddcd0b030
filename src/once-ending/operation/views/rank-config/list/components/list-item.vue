<template>
  <a-card>
    <div class="card-header">
      <div class="title flex flex-align-center">
        <i class="label feild-must">组名：</i>
        <a-input
          v-if="data.editable"
          class="input-group-name"
          :maxLength='20'
          style='width: 300px'
          placeholder="请输入20字以下组名"
          v-model="data.teamName"
        />
        <template v-else>
          {{ data.teamName }}
        </template>
        <a-button
          class="ml-16"
          :disabled="!data.editable"
          @click="() => onShowModal('rule')">
          服务分规则
        </a-button>
        <a-button
          class="ml-16"
          v-if="$tnt.xtenant < 1000"
          :disabled="!data.editable"
          @click="() => onShowModal('operate')">
          运营管控分规则
        </a-button>
      </div>

      <div class="extra">
        <a-popconfirm
          v-if="data.id && data.editable"
          title="删除后将完全移除该分组数据，可能会影响本月绩效排行，请问是否继续?"
          @confirm="() => onRemove(data.key)"
        >
          <a-button type="danger">删除</a-button>
        </a-popconfirm>
        <span class="mover"><a-icon type="appstore" /></span>
      </div>
    </div>

    <section class="card-body flex">
      <AreaSelector
        :form="data"
        :disabled="!data.editable"
        :areaType="state.areaType"
      />
      <div class="config flex" v-if="cantConfig">
        <!-- 跟、自排-->
        <div class="form-item" >
          <i class="feild-must"/>
          <a-select
            :disabled="!data.editable"
            v-model="data.rankType"
            style="min-width: 88px"
          >
            <a-select-option
              v-for="item in state.enumData.rankTypes"
              :disabled="item.value === 2"
              :key="item.value"
            >
              {{item.label}}
            </a-select-option>
          </a-select>
        </div>
        <!-- 正态、固定排名-->
        <div class="form-item"  v-show="data.rankType === 1">
          <i class="feild-must"/>
          <a-select
            :disabled="!data.editable"
            v-model="data.fixedOrNormalityType"
            style="min-width: 88px">
            <a-select-option
              v-for="item in state.enumData.fixedOrNormalityTypes"
              :key="item.value"
            >
              {{item.label}}
            </a-select-option>
          </a-select>
        </div>

        <div class="form-item" v-show="data.rankType === 1">
          <a-button
            v-show="data.editable"
            :disabled="!data.fixedOrNormalityType"
            @click="() => onShowModal('rank')"
            type="primary">
            排名配置
          </a-button>
        </div>
        <!-- 当前有跟排 !!data.followTeamId 便不可跟排其它 -->
        <div v-show="data.rankType === 2" class="form-item m-0" >
          <i class="label label-xs">跟随</i>
            <a-select
              :disabled="!data.editable || data.isBebefollowed"
              v-model="data.followTeamId"
              style="min-width: 88px">
              <a-select-option
                v-for="(item, index) in state.teamsEnum"
                :value="item.value"
                :key="index">
                  {{item.label}}
              </a-select-option>
            </a-select>
              最相近分值变化
        </div>
      </div>
    </section>

    <!-- 修正系数-->
    <section v-if="$tnt.xtenant !== 0" class="card-footer flex flex-justify-between">
      <div class="correction-wrap">
        <div class="correction flex flex-align-center mb-8">
          板块总分：
          <div class="form-item">
            <i class="label feild-must">评价分</i>
            <a-input-number
              v-if="data.editable"
              :min="0" :max="100"
              v-model="data.serverRatio"
            />
            <span class="value" v-else>{{data.serverRatio}}</span>
          </div>

          <div class="form-item">
            <i class="label feild-must">运营管控</i>
            <a-input-number
              v-if="data.editable"
              :min="0" :max="100"
              v-model="data.operateRatio"
            />
            <span class="value" v-else>{{data.operateRatio}}</span>
          </div>
          <div class="form-item">
            <i class="label feild-must">展陈物料</i>
            <a-input-number
              v-if="data.editable"
              :min="0" :max="100"
              v-model="data.exhibitionRatio"
            />
            <span class="value" v-else>{{data.exhibitionRatio}}</span>
          </div>
          <div class="form-item">
            <i class="label feild-must">培训带培</i>
            <a-input-number
              v-if="data.editable"
              :min="0" :max="100"
              v-model="data.trainRatio"
            />
            <span class="value" v-else>{{data.trainRatio}}</span>
          </div>
          <div class="form-item">
            <i class="label feild-must">库存管理</i>
            <a-input-number
              v-if="data.editable"
              :min="0" :max="100"
              v-model="data.stockRatio"
            />
            <span class="value" v-else>{{data.stockRatio}}</span>
          </div>
        </div>
        <div class="correction flex flex-align-center">
          修正系数：
          <div class="form-item">
            <i class="label feild-must">评价分</i>
            <a-input-number
              v-if="data.editable"
              :min="0" :max="100"
              v-model="data.serverRate"
            />
            <span class="value" v-else>{{data.serverRate}}</span>
          </div>

          <div class="form-item">
            <i class="label feild-must">运营管控</i>
            <a-input-number
              v-if="data.editable"
              :min="0" :max="100"
              v-model="data.operateRate"
            />
            <span class="value" v-else>{{data.operateRate}}</span>
          </div>
          <div class="form-item">
            <i class="label feild-must">展陈物料</i>
            <a-input-number
              v-if="data.editable"
              :min="0" :max="100"
              v-model="data.exhibitionRate"
            />
            <span class="value" v-else>{{data.exhibitionRate}}</span>
          </div>
          <div class="form-item">
            <i class="label feild-must">培训带培</i>
            <a-input-number
              v-if="data.editable"
              :min="0" :max="100"
              v-model="data.trainRate"
            />
            <span class="value" v-else>{{data.trainRate}}</span>
          </div>
          <div class="form-item">
            <i class="label feild-must">库存管理</i>
            <a-input-number
              v-if="data.editable"
              :min="0" :max="100"
              v-model="data.stockRate"
            />
            <span class="value" v-else>{{data.stockRate}}</span>
          </div>
        </div>
      </div>
    </section>

    <section class="card-footer flex flex-justify-end">
      <div v-if='data.editable'>
        <a-button type="primary" class="mr-5" @click="onSave(data.key)">保存</a-button>
        <a-popconfirm title="确定取消?" @confirm="onCancel(data.key)">
          <a-button>取消</a-button>
        </a-popconfirm>
      </div>
      <a-button v-else type="primary" @click="onEdit(data.key)">编辑</a-button>
    </section>
  </a-card>
</template>

<script>
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions'

  import AreaSelector from './area-select'
  import { computed, getCurrentInstance } from 'vue'

  export default {
    components: {
      AreaSelector,
    },
    props: {
      data: { // 当前项数据
        type: Object,
        default: () => ({})
      },
    },
    setup () {
      const { proxy: { $tnt } } = getCurrentInstance()
      const { state } = useState()
      const {
        onShowModal,
        onRemove,
        onSave,
        onCancel,
        onEdit
      } = useActions()

      return {
        state,
        cantConfig: computed(() => {
          const isCant = $tnt.xtenant === 0 ? (['3', '6'].includes(state.currentKinds)) : true
          return isCant
        }),
        onShowModal,
        onRemove,
        onSave,
        onCancel,
        onEdit
      }
    }
  }
</script>
<style lang="scss" scoped>
  @import '../../style.scss';
    :deep(.area-selector){
      margin-bottom: $margin-base;
    width: calc(100% - 400px - #{$margin-base});
    height: $area-select-height;
    margin-right: $margin-base;
    position: relative;
    z-index: 2;

    .ant-select-selection{
      min-height: $area-select-height
    }
    .area{ width: 100% }
    }
</style>
