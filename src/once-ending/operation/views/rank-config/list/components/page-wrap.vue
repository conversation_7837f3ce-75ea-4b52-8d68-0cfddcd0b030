<script lang="jsx">
  import { onMounted, watch, getCurrentInstance } from 'vue'
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions'

  import draggable from 'vuedraggable'
  import ListItem from './list-item.vue'
  import RuleModalForm from './rule-modal-form'
  import OperateModalForm from './operate-modal-form'
  import CollectionModalForm from './collection-modal-form'

  export default {
    components: {
      draggable,
      ListItem,
      RuleModalForm,
      OperateModalForm,
      CollectionModalForm
    },
    setup () {
      const { proxy } = getCurrentInstance()
      const { state, setState } = useState()
      const { changeTabs, onSort, onDddGroup, getData, pageInit } = useActions()

      onMounted(() => {
        pageInit()
      })
      watch(
        () => state.currentKinds,
        (val, old) => {
          const AREA_MAP = new Map([ // 九机
            ['1', 'areaStore'], // [门店:1]
            ['2', 'areaSmall'], // [小区:2]
            ['3', 'areaLarge'], // [大区:3]
            ['5', 'areaSmall'], // [小区售后:5]
            ['6', 'areaLarge'], // [大区售后:6]
            ['0', 'areaStore'], // [人员:0]
            ['default', 'areaLarge'], // 大区兜底
          ])
          let areaType
          if (proxy.$tnt.xtenant === 0) {
            areaType = AREA_MAP.get(val) || AREA_MAP.get('default')
          } else {
            areaType = val === '1' ? 'areaStore' : val === '2' ? 'areaSmall' : 'areaLarge'
          }
          setState('areaType', areaType)
          if (val !== old) {
            getData(val)
            setState('teamsEnum', [])
          }
        }
      )
      watch(
        () => state.currentEditItem.areaCodesArr, // 所选区域数量
        val => { setState('selectAreaNum', val.length) },
        { deep: true }
      )
      watch(
        () => state.editingKey, // 当前编辑的组key
        val => {
          if (!val) state.dataSource.forEach(item => { item.editable = false })
          // 跟排数据，筛选掉自身
          const teamsEnum = state.teamsEnum.filter(item => item.value !== state.currentEditItem.id)
          setState('teamsEnum', teamsEnum)
        }
      )
      return {
        state,
        changeTabs,
        onSort,
        onDddGroup
      }
    },
    render () {
      const {
        state: {
          currentKinds,
          enumData,
          ruleConfigModalVisible,
          operateConfigModalVisible,
          rankConfigModalVisible,
        },
        changeTabs,
        onSort,
        onDddGroup
      } = this
      if (!enumData?.kinds) return
      const tabList = enumData.kinds.map(item => ({ key: item.value + '', tab: item.label }))
      return (
        <page>
          <a-card
            tabList={ tabList }
            activeTabKey={ currentKinds + '' }
            onTabChange={ changeTabs }
          >
            <p class="blue text-right mb-16">
              <a-icon type="info-circle"/> 每月4号前修改的存到上月，4号开始修改的存到本月
            </p>
            <draggable
              chosenClass="chosen"
              ghostClass="ghost"
              handle='.mover'
              force-fallback={ true }
              animation={ 1000 }
              v-model={ this.state.dataSource }
              onSort={ onSort }>
            <transition-group>
              {
                this.state.dataSource.map(item => (
                  <ListItem
                    key={ item.key }
                    data={ item }
                  />
                ))
              }
            </transition-group>
            </draggable>
            <div class="add-button-wrap padding">
              <a-button block
                type="dashed"
                onClick={ onDddGroup }
              > + 添加新组
              </a-button>
            </div>
          </a-card>
          { ruleConfigModalVisible && <RuleModalForm/> }
          { operateConfigModalVisible && <OperateModalForm/> }
          { rankConfigModalVisible && <CollectionModalForm/> }
        </page>
      )
    }
  }
</script>

<style lang="scss" scoped>
  .add-button-wrap{
    width:40%;
    margin:16px auto;
    margin-bottom: 100px;
  }
  .spin-content{
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
  :deep(.ant-tabs){
    overflow: visible;
  }
  :deep(.ant-modal-mask){
    z-index: 99
  }
  :deep(.ant-modal-wrap){
    z-index: 100
  }
</style>
