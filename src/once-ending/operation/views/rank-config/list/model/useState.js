import { reactive, inject, provide } from 'vue'

const key = Symbol('rankConfig')

export function useState () {
  return inject(key)
}

export function createState () {
  const state = reactive({
    editable: false,
    time: '',
    loading: false,
    areaType: 'areaLarge',
    editType: '', // 编辑类型： [add, modifiy]
    enumData: {}, // 页面枚举值
    currentKinds: '1', // 当前区域类型
    editingKey: '', // 当前编辑对象key
    currentEditItem: {}, // 当前编辑对象
    cacheData: {}, // 缓存编辑修改前的数据
    dataSource: [],
    filter: false, // 区域是否过滤无子节点数据
    feildArry: [], // 弹框字段

    drag: false,
    teamsEnum: [],
    // 当前编辑组
    selectAreaNum: 0,
    selectedItems: [],
    rankConfigModalVisible: false,
    ruleConfigModalVisible: false,
    operateConfigModalVisible: false,
  })
  const setState = (key, val) => { state[key] = val }
  const rankConfig = {
    state,
    setState,
  }
  provide(key, rankConfig)
  return rankConfig
}
