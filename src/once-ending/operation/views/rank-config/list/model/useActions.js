
import moment from 'moment'
import UUID from 'uuid'
import { pick, cloneDeep } from 'lodash'
import { message } from 'ant-design-vue'
import { getCurrentInstance } from 'vue'
import { to } from '~/util/common'
import api from '~/api'
import { useState } from './useState.js'

export default function useActions () {
  const instance = getCurrentInstance().proxy
  const {
    state,
    setState
  } = useState()
  const POINT_ARR = ['top10Point', 'top25Point', 'center30Point', 'last25Point', 'last10Point']
  const RATIO_ARR = ['rankOneRatio', 'rankTwoRatio', 'rankThreeRatio', 'rankFourRatio', 'rankFiveRatio']

  // [性能优化]解决的问题: 页面初次加载时，会以列表数的次数去请求区域数据。
  // 1.在区域组件加载前进行区域数据的请求，缓存. 2.组件做延时处理
  const getAreaData = async () => {
    const isStorage = sessionStorage.getItem('departmentorarea')
    if (!isStorage) {
      const [err, res] = await to(api.common.getAreaTree())
      if (err) throw err
      if (res.code !== 0) return
      sessionStorage.setItem('departmentorarea', JSON.stringify(res))
    }
  }
  const getEnum = async () => {
    const res = await instance.$store.dispatch('operation/rankConfig/getConfigEnums')
    if (!res) return
    const { code, data } = res
    if (code !== 0) return
    // fixedOrNormalityTypes:自排枚举 kinds:区域 rankTypes:自排、跟排
    setState('enumData', data)
    // 更新当前tab选值。默认获取区域（tabs）枚举值的第一个枚举值的数据
    if (data.kinds?.length) {
      const currentKinds = data.kinds[0].value + '' // 页面初始是大区的tab.作为tabkey统一用String
      setState('currentKinds', currentKinds)
      getData(currentKinds)
    }
  }
  const getData = async (currentKinds) => {
    const params = { kinds: currentKinds }
    instance.$indicator.open()
    const res = await instance.$store.dispatch(
      'operation/rankConfig/getList',
      params
    )
    instance.$indicator.close()
    if (!res) return
    const { code, data } = res
    if (code !== 0) return
    if (!data.length) {
      setState('dataSource', [])
    } else {
      const dataSource = data.map(item => ({
        ...item,
        key: UUID(),
      }))
      setState('dataSource', dataSource)
      // 跟随组的枚举值
      const enums = state.dataSource.map((item) => ({
        value: item.id,
        label: item.teamName
      }))
      setState('teamsEnum', enums)
    }
  }
  /**
   * 根据所选tab【门店类型】请求相应数据[watch(currentKinds)]）
   * @param currentKinds{Number}
   */
  const changeTabs = (currentKinds) => {
    const isEdit = state.dataSource.some(item => item.editable === true)
    if (isEdit) {
      message.warning('请先保存正在编辑的数据')
      return
    }
    setState('currentKinds', currentKinds)
  }
  const onCancelModal = () => { // 回归修改前的字段
    state.feildArry.forEach(item => {
      state.currentEditItem[item] = state.cacheData[item]
    })
    setState('rankConfigModalVisible', false)
    setState('ruleConfigModalVisible', false)
    setState('operateConfigModalVisible', false)
  }
  // 【排名配置模态框】确定时验证
  const handleRankConfigOk = () => {
    const isPass = _checkedRankType1()
    if (!isPass) return
    setState('rankConfigModalVisible', false)
  }
  const onSort = () => {
    const newData = cloneDeep(state.dataSource)
    const list = newData.map((item, index) => {
      return {
        id: item.id,
        sort: index + 1,
        areaLevelType: index + 1
      }
    })
    instance.$store.dispatch(
      'operation/rankConfig/sortEvaluateConfig',
      list
    )
  }
  const onShowModal = (modalType) => {
    // type 本组规则：'rule' 排名配置:'rank'
    let feildArry = []
    if (modalType === 'rule') {
      setState('ruleConfigModalVisible', true)
      feildArry = ['performanceComponents', 'sortRules', 'associationRules']
    } else if (modalType === 'operate') {
      setState('operateConfigModalVisible', true)
      feildArry = ['performanceComponents1', 'sortRules1', 'associationRules1']
    } else {
      setState('rankConfigModalVisible', true)
      const { fixedOrNormalityType } = state.currentEditItem
      feildArry = fixedOrNormalityType === 1
        ? [
          'top10Point',
          'top25Point',
          'center30Point',
          'last25Point',
          'last10Point',
          'rankOneRatio',
          'rankTwoRatio',
          'rankThreeRatio',
          'rankFourRatio',
          'rankFiveRatio',
        ]
        : ['fixedRankConfigArr']
    }
    const cacheData = pick(state.currentEditItem, ...feildArry)
    setState('feildArry', feildArry)
    setState('cacheData', cacheData)
  }
  const onRemove = async (key) => { // 删除组
    const newData = cloneDeep(state.dataSource)
    const target = newData.find(item => item.key === key)
    instance.$indicator.open()
    const res = await instance.$store.dispatch(
      'operation/rankConfig/removeEvaluateConfig',
      { id: target.id }
    )
    instance.$indicator.close()
    if (!res) return
    if (res.code !== 0) return
    message.success('删除成功')
    onSort() // 重新排序
    getData(state.currentKinds)
  }
  const onSave = async (key) => {
    const newData = cloneDeep(state.dataSource)
    const target = newData.find(item => key === item.key)
    const {
      teamName,
      areaCodesArr,
      rankType,
      followTeamId,
      fixedOrNormalityType
    } = target

    if (!teamName) {
      message.error('分组名不能为空')
      return
    }
    if (!areaCodesArr.length) {
      message.error('请选择区域')
      return
    }
    const isCant = instance.$tnt.xtenant === 0 ? (['3', '6'].includes(state.currentKinds)) : true
    if (isCant) {
      if (!rankType) {
        message.error('请选择排名规则')
        return
      }
      if (rankType === 2 && !followTeamId) { // 跟排--> 需选择跟随组
        message.error('选择跟排的组')
        return
      }
      if (rankType === 1) { // 自排--> 需选择占比/固定排名
        if (!fixedOrNormalityType) {
          message.error('选择自排规则')
          return
        }
        target.followTeamId = 0 // 置空跟随组
        const isPass = _checkedRankType1()
        if (!isPass) return
        if (fixedOrNormalityType === 1) { // [占比排名] --> 置空[固态排名]配置
          target.fixedRankConfigArr.length = 0
          target.fixedRankConfig = ''
        } else if (fixedOrNormalityType === 2) { // [固态排名] --> 置空[占比排名]配置
          const normalityArr = [...POINT_ARR, ...RATIO_ARR]
          normalityArr.forEach(item => { target[item] = null })
        }
      }
    }
    if (instance.$tnt.xtenant !== 0) {
      const isPass = _checkNull(target)
      if (!isPass) return
    }
    // 新增的排名设为最后
    if (state.editType === 'add') {
      target.sort = newData.length
      target.areaLevelType = newData.length
    }
    // it => typeof it === 'object' ? it.value : it 区域选择框双向绑定数据更新后为一个数组对象,如果用户不更新区域,默认数据为一个数组,兼容用户不更新地区数据就去保存
    target.areaCodes = target.areaCodesArr = target.areaCodesArr.map(it => typeof it === 'object' ? it.value : it)
    instance.$indicator.open()
    const res = await instance.$store.dispatch(
      'operation/rankConfig/addEvaluateConfig',
      target
    )
    instance.$indicator.close()
    if (!res) return
    if (res.code !== 0) return
    message.success('保存成功')
    // 修改时，需要重新排序
    if (state.editType === 'modifiy') onSort()
    getData(state.currentKinds)
  }
  // 检测【板块总分】与【修正系数】
  const _checkNull = (targetObj) => {
    const checkNullArr = {
      serverRatio: '板块总分--评价分不能为空',
      operateRatio: '板块总分--运营管控不能为空',
      trainRatio: '板块总分--培训代培不能为空',
      exhibitionRatio: '板块总分--展陈物料不能为空 ',
      stockRatio: '板块总分--库存管理不能为空',

      serverRate: '修正系数--评价分不能为空',
      operateRate: '修正系数--运营管控不能为空',
      trainRate: '修正系数--培训代培分不能为空',
      exhibitionRate: '修正系数--展陈物料不能为空',
      stockRate: '修正系数--库存分不能为空',
    }
    try {
      Object.keys(checkNullArr).forEach(item => {
        if (targetObj[item] == null) {
          message.error(checkNullArr[item])
          throw new Error('【板块总分】与【修正系数】必填较验未通过')
        }
      })
    } catch (e) {
      if (e.message === '【板块总分】与【修正系数】必填较验未通过') throw e
      return false
    }
    return true
  }
  // 检测自排的【占比排名】与【固态排名】
  const _checkedRankType1 = () => {
    const {
      top10Point,
      top25Point,
      center30Point,
      last25Point,
      last10Point,
      areaCodesArr,
      fixedOrNormalityType,
      fixedRankConfigArr,
    } = state.currentEditItem

    if (fixedOrNormalityType === 1) { // 占比排名（需验证5档相当等于100%） 2：固态排名
      if (POINT_ARR.some(item => state.currentEditItem[item] == null)) {
        message.error('排名名次配置不能为空')
        return false
      } else {
        const isZero = 100 - (top10Point + top25Point + center30Point + last25Point + last10Point)
        if (isZero) {
          message.error('5档排名占比相加需为100%，请重新调整名次百分比')
          return false
        }
      }
      if (RATIO_ARR.some(item => state.currentEditItem[item] == null)) {
        message.error('各项绩效系数不能为空')
        return false
      }
      return true
    }
    if (fixedOrNormalityType === 2) { // [固态排名]
      if (fixedRankConfigArr.length > areaCodesArr.length) {
        // 修改时多的处理
        fixedRankConfigArr.splice(areaCodesArr.length)
      } else if (fixedRankConfigArr.length === areaCodesArr.length) {
        // 修改时少的处理
        const tempArr = fixedRankConfigArr.filter(item => item !== null)
        if (tempArr.length < areaCodesArr.length) {
          message.error('请完善固态排名配置')
          return false
        }
      } else {
        message.error('请完善固态排名配置')
        return false
      }
    }
    return true
  }
  // 取消整体保存
  const onCancel = () => {
    getData(state.currentKinds)
  }
  const onEdit = (key) => {
    let newData = cloneDeep(state.dataSource)
    setState('editType', 'modifiy')
    // 1.判断有无其它组处理编辑状态
    // 1.1.有，提示先保存当前处理编辑状态的组
    const isEdit = newData.some(item => item.editable === true)
    if (isEdit) {
      message.warning('请先保存正在编辑的数据')
      return
    }
    setState('editable', true)
    setState('editingKey', key)
    // 1.2.无，把当前设为编辑状态
    const target = newData.find(item => key === item.key)
    // 当前是否有被跟随
    target.isBebefollowed = newData.some(item => target.id === item.followTeamId)
    if (target) {
      target.editable = true
      setState('dataSource', newData)
      setState('currentEditItem', target)
    }
  }
  // 增加空新配置组
  const onDddGroup = () => {
    const newData = cloneDeep(state.dataSource)
    setState('editType', 'add')
    if (newData.length > 0) {
      // 1.判断有无其它组处理编辑状态
      // 1.1.有，提示先保存当前处理编辑状态的组
      const isEdit = newData.some(item => item.editable === true)
      if (isEdit) {
        message.warning('请先保存正在编辑的数据')
        return
      }
    }
    const form9ji = {
      key: UUID(),
      editable: true,

      kinds: state.currentKinds, // * 地区类型
      teamName: '', // * 分组名
      areaCodes: '', // 地区code 大小区departCode，门店areaCode
      areaCodesArr: [], // 地区code
      areaLevelType: null, // 各区分组对应排序

      rankType: null, // * 1-自排  2-跟排
      fixedOrNormalityType: null, // * 1-正太排名 2-固态排名
      fixedRankConfigArr: [], // 固定排名配置，与地区数量对应
      fixedRankConfig: '', // 固定排名配置，与地区数量对应
      followTeamId: null, // 跟随的组id

      performanceComponents: '', // 绩效分构成
      sortRules: '', // 排序规则
      sort: undefined,
      associationRules: '', // 绩效排名的关联规则

      top10Point: null, // 排名前10%系数
      top25Point: null, // 排名前10%~35%系数
      center30Point: null, // 排名前35%~65%系数
      last25Point: null, // 排名前65%~90%系数
      last10Point: null, // 排名后10%系数
    }
    const formRatio = {
      serverRatio: null, // * 评价分
      operateRatio: null, // * 运营管控
      exhibitionRatio: null, // * 展陈物料
      trainRatio: null, // * 培训代培分
      stockRatio: null, // * 库存分

      serverRate: null, // * 评价分
      operateRate: null, // * 运营管控
      exhibitionRate: null, // * 展陈物料
      trainRate: null, // * 培训代培分
      stockRate: null, // * 库存分
    }
    const formOut = {
      ...form9ji,
      ...formRatio
    }
    const newEvaluateConfig = instance.$tnt.xtenant === 0 ? form9ji : formOut
    newData.push(newEvaluateConfig)
    setState('dataSource', newData)
    setState('editingKey', newEvaluateConfig.key)
    setState('currentEditItem', newEvaluateConfig)
  }
  const pageInit = () => {
    setState('time', moment(new Date()))
    getEnum()
    getAreaData()
  }

  return {
    getAreaData,
    getEnum,
    getData,
    changeTabs,
    onCancelModal,
    handleRankConfigOk,
    onSort,
    onShowModal,
    onRemove,
    onSave,
    onCancel,
    onEdit,
    onDddGroup,
    pageInit
  }
}
