<template>
  <page>
    <a-card>
      <div class="flex flex-align-center">
        <a-month-picker :allowClear="false" placeholder="请选择年月份" format="YYYY-MM" v-model="time"/>
        <div class="flex">
          <input ref="importFile" accept=".xlsx, .xls" type="file" style="display:none" @change="importFiles" />
          <a-button class="ml-20" type="primary" icon="upload" @click="importBtn">上传该月数据</a-button>
        </div>
        <a-button @click="exportLevel" class="ml-20" type="primary">导出</a-button>
      </div>
      <div style="margin-top: 24px;width: 70%">
        <div v-if="loadIng" class="full-width flex flex-center border" style="height: 200px">
          <a-spin/>
        </div>
        <div v-else>
          <div v-if="dataList && dataList.length">
            <a-table :columns="constants.columns"
                     :dataSource="dataList"
                     rowKey="id"
                     :pagination="pagination"
                     @change="handleTableChange" bordered>
                     <template slot="area" slot-scope="text">
                       <span>{{text || '-'}}</span>
                     </template>
                     </a-table>
          </div>
          <div v-else class="full-width flex flex-center border" style="height: 200px">
            该月份暂无数据
          </div>
        </div>
      </div>
    </a-card>
    <a-modal title="确认上传" v-model="showUpload" :width="400">
      <div class="flex flex-center flex-col">
        <span>即将覆盖的{{timeString}}定级数据</span>
        <span>是否继续上传？</span>
      </div>
      <template slot="footer">
        <a-button @click="showUpload = false" type="primary">取消</a-button>
        <a-button @click="sureUpload">上传</a-button>
      </template>
    </a-modal>
  </page>
</template>

<script type="text/jsx">
  import { onMounted, watch, toRefs, getCurrentInstance, reactive } from 'vue'
  import moment from 'moment'
  import ApiStaff from '~/api/staff'
  import { message } from 'ant-design-vue'
  import * as constants from '../constants'
  export default {
    name: 'user',
    setup () {
      const { proxy } = getCurrentInstance()
      const state = reactive({
        constants,
        time: '',
        timeString: '',
        loadIng: false,
        showUpload: false,
        dataList: [],
        pagination: {
          current: 1,
          pageSize: 100,
          total: 0,
        },
      })
      onMounted(() => {
        state.time = moment(new Date())
      })
      const fetchData = (cu) => {
        if (!state.time) return message.warning('请选择年月份')
        if (cu) state.pagination.current = cu
        state.loadIng = true
        const { current, pageSize } = state.pagination
        const params = {
          month: state.timeString.replace('-', ''),
          size: pageSize,
          current: current
        }
        ApiStaff.getLevelInfo(params).then(res => {
          if (res.code === 0) {
            state.dataList = res.data.records
            state.pagination.total = res.data.total
          } else {
            message.error(res.userMsg)
          }
        }).finally(() => {
          state.loadIng = false
        })
      }
      const importBtn = () => {
        proxy.$refs.importFile.value = ''
        proxy.$refs.importFile.click()
      }
      const handleTableChange = (page) => {
        state.pagination = page
        fetchData(null)
      }
      const exportLevel = () => { // 导出数据
        window.location.href = ApiStaff.exportLevelInfo({ month: state.timeString.replace('-', ``) })
      }
      const sureUpload = () => {
        proxy.$indicator.open()
        let file = state.file
        let param = new FormData()
        param.append('file', file)
        param.append('month', state.timeString.replace('-', '')) // 增加参数
        console.log(file)
        ApiStaff.upLoadUser(param).then(res => {
          if (res.code === 0) {
            fetchData(null)
            message.success('上传成功')
          } else {
            message.error(res.userMsg)
          }
        }).finally(() => {
          state.showUpload = false
          indicator.close()
        })
      }
      const importFiles = (obj) => { // 上传文件
        if (obj.target.files && obj.target.files.length) {
          state.file = obj.target.files[0]
          state.showUpload = true
        }
      }
      watch(
        () => state.time,
        () => {
          state.timeString = moment(state.time).format('YYYY-MM')
          fetchData(1)
        },
        { immediate: true }
      )
      return {
        ...toRefs(state),
        importBtn,
        handleTableChange,
        exportLevel,
        sureUpload,
        importFiles
      }
    }
  }
</script>
