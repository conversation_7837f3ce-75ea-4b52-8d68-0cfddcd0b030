<script lang="jsx">
  import { defineComponent, ref, nextTick } from 'vue'
  import { Layout, Menu, Icon, message, Spin, PageHeader } from 'ant-design-vue'
  import { to } from '~/util/common'
  import metabase from '@operation/api/metabase'
  import { NiImg } from '@jiuji/nine-ui'
  import { debounce } from 'lodash'

  export default defineComponent({
    setup () {
      const collapsed = ref(false)

      const menu = ref([])

      const selectedKeys = ref([])

      const menuInit = ref(false)

      const currentFrameLink = ref('')

      const showFrame = ref(true)

      const frameLoading = ref(false)

      const frameHeight = ref(0)

      const title = ref('')

      const iframe = ref(null)

      const getAllMenu = async function () {
        const [err, res] = await to(metabase.getAllMenu())
        if (err) throw err
        const { code, data, userMsg } = res
        if (code === 0) {
          menu.value = data
          menuInit.value = true
        } else {
          message.error(userMsg)
        }
      }
      getAllMenu()

      const myObserver = ref(null)

      const menuClick = function (item) {
        if (item.type === 0) { return message.warning('请选择报表菜单') }
        if (!item.link) return message.warning('该菜单暂无报表')
        if (myObserver.value) myObserver.value.disconnect()
        myObserver.value = null
        frameLoading.value = true
        selectedKeys.value = [item.id]
        showFrame.value = false
        title.value = item.name
        currentFrameLink.value = item.link
        nextTick(() => {
          showFrame.value = true
          nextTick(() => {
            if (iframe.value) {
              iframe.value.onload = function () {
                frameLoading.value = false
                const height = iframe.value.getBoundingClientRect().height
                frameHeight.value = height
                myObserver.value = new ResizeObserver(
                  debounce((entries) => {
                    entries?.forEach((entrie) => {
                      const { height } = entrie?.contentRect || {}
                      if (height) {
                        frameHeight.value = height
                      }
                    })
                  }),
                  200
                )
                myObserver.value.observe(iframe.value)
              }
            }
          })
        })
      }

      return {
        collapsed,
        menu,
        selectedKeys,
        menuClick,
        menuInit,
        currentFrameLink,
        title,
        showFrame,
        iframe,
        frameLoading,
        frameHeight,
      }
    },
    render () {
      const {
        collapsed,
        menu,
        selectedKeys,
        menuClick,
        menuInit,
        currentFrameLink,
        title,
        showFrame,
        frameLoading,
        frameHeight,
      } = this
      return (
      <Spin spinning={!menuInit}>
        <Layout class="metabase-layout">
          <Layout.Sider
            v-model={collapsed}
            trigger={null}
            class="metabase-sider"
            width="220"
            theme="light"
            collapsible
          >
            <div
              class={[
                'page-title flex flex-align-center',
                collapsed
                  ? 'flex-justify-center collapsed'
                  : 'flex-justify-between',
              ]}
            >
              <transition name="fade">
                <div v-show={!collapsed}>运营数据报表</div>
              </transition>
              <Icon
                class="trigger"
                style={{ color: collapsed ? '#1890ff' : '' }}
                type={collapsed ? 'menu-unfold' : 'menu-fold'}
                onClick={() => (this.collapsed = !collapsed)}
              />
            </div>
            <Menu
              mode="inline"
              theme="light"
              class="menu"
              selectedKeys={selectedKeys}
              inline-collapsed={collapsed}
            >
              {menu.map((d) =>
                d.children && d.children.length ? (
                  <Menu.SubMenu key={d.id}>
                    <div
                      slot="title"
                      class="flex flex-align-center full-height"
                    >
                      <NiImg
                        class="item-icon"
                        src={collapsed ? d.hiddenIcon : d.showIcon}
                      />
                      <transition name="fade">
                        <span v-show={!collapsed} class="ml-10">
                          {d.name}
                        </span>
                      </transition>
                    </div>
                    {d.children.map((k) =>
                      k.children && k.children.length ? (
                        <Menu.SubMenu key={k.id} title={k.name}>
                          {k.children.map((j) => (
                            <Menu.Item
                              key={j.id}
                              onClick={() => {
                                menuClick(j)
                              }}
                            >
                              {j.name}
                            </Menu.Item>
                          ))}
                        </Menu.SubMenu>
                      ) : (
                        <Menu.Item
                          key={k.id}
                          onClick={() => {
                            menuClick(k)
                          }}
                        >
                          {k.name}
                        </Menu.Item>
                      )
                    )}
                  </Menu.SubMenu>
                ) : (
                  <Menu.Item
                    key={d.id}
                    title={d.name}
                    onClick={() => {
                      menuClick(d)
                    }}
                  >
                    <div class="flex flex-align-center full-height">
                      <NiImg
                        class="item-icon"
                        src={collapsed ? d.hiddenIcon : d.showIcon}
                      />
                      <transition name="fade">
                        <span v-show={!collapsed} class="ml-10">
                          {d.name}
                        </span>
                      </transition>
                    </div>
                  </Menu.Item>
                )
              )}
            </Menu>
          </Layout.Sider>
          <Layout.Content>
            {currentFrameLink ? (
              <Spin spinning={frameLoading}>
                <div class="content">
                  <PageHeader title={title} />
                  <div
                    class="frame-box"
                    style={{ height: frameLoading ? '' : `${frameHeight}px` }}
                  >
                    {showFrame ? (
                      <iframe
                        class="full-width full-height iframe"
                        src={currentFrameLink}
                        frameborder="0"
                        ref="iframe"
                      ></iframe>
                    ) : null}
                  </div>
                </div>
              </Spin>
            ) : (
              <div class="no-data flex flex-align-center flex-justify-center">
                请选择报表
              </div>
            )}
          </Layout.Content>
        </Layout>
      </Spin>
      )
    },
  })
</script>
<style lang="scss" scoped>
.metabase-layout {
  margin: 0 -24px;
  min-height: 100%;
  .fade-enter-active {
    animation: bounce-in 0.5s;
  }

  @keyframes bounce-in {
    0% {
      opacity: 0;
    }
    20% {
      opacity: 0;
    }
    30% {
      opacity: 0;
    }
    50% {
      opacity: 0.7;
    }
    100% {
      opacity: 1;
    }
  }
  .metabase-sider {
    position: sticky;
    height: calc(100vh - 68px);
    top: 0;
    .page-title {
      height: 48px;
      padding: 0 16px 0 24px;
      font-weight: 600;
      background: #fff;
      border-bottom: 1px solid #e8e8e8;
      &.collapsed {
        padding: 0;
      }
    }
    .trigger {
      &:hover {
        color: #1890ff;
      }
    }
    .menu {
      &::-webkit-scrollbar {
        width: 2px;
      }
      height: calc(100% - 68px);
      overflow-y: auto;
      overflow-x: hidden;
    }
    .item-icon {
      width: 14px;
      height: 14px;
    }
  }
  .content {
    min-height: 100%;
    padding-bottom: 10px;
    .frame-box {
      margin: 0 10px;
      background: #fff;
      height: calc(100vh - 142px);
      .iframe {
        vertical-align: bottom;
      }
    }
  }
  .no-data {
    height: 100%;
    font-size: 18px;
  }

  :deep(.ant-menu-inline) {
    border-right: 1px solid transparent;
  }
  :deep(.ant-menu-vertical) {
    border-right: 1px solid transparent;
  }
  :deep(.ant-menu-vertical-left) {
    border-right: 1px solid transparent;
  }
}
</style>
