const isJiuJi = window.tenant.xtenant < 1000
const validatePass = async (_rule, value) => {
  if (!value && value !== 0) {
    return Promise.reject(new Error('请输入奖励积分'))
  } else if (value <= 0 || (value > 1000 && isJiuJi)) {
    return Promise.reject(new Error(`奖励积分仅支持${isJiuJi ? '小于等于1000的' : ''}正整数`))
  } else {
    return Promise.resolve()
  }
}
const validatePassPPID = function (name) {
  return async (_rule, value) => {
    if (value) {
      const ppidArray = value.replace(/，/g, ',').split(',')
      const reg = /^\d+$/
      if (ppidArray.filter(d => reg.test(d)).length !== ppidArray.length) {
        return Promise.reject(new Error(`${name}须为数字`))
      }
      let cacheArray = []
      for (let item of ppidArray) {
        (!cacheArray.find(it => it === item)) && (cacheArray.push(item))
      }
      if (ppidArray.length !== cacheArray.length) {
        return Promise.reject(new Error(`存在重复${name}`))
      } else {
        return Promise.resolve()
      }
    } else {
      return Promise.resolve()
    }
  }
}
const inspireTargetJiuJi = '激活目标是指发放激励的数量/数额门槛。</br>' +
  '激励方式为达量时，销量在激励有效期内达到激活目标中填写的值则发放一次激励。</br>' +
  '激励方式为叠加时，销量在激励有效期内每次达到激活目标中填写的值都会发放一次激励并扣减对应数值，超量部分会保留。</br>' +
  '激励方式为达量叠加时，销量在激励有效期内，达到激活目标中填写的达成量，之后每次达到激活目标中的叠加量都会发放一次激励并扣减对应数值，超量部分会保留。'
const inspireTargetOut = '激活目标是指发放激励的数量/数额门槛。</br>' +
  '激励方式为达量时，销量或销售额、利润额等在激励有效期内达到激活目标中填写的值则发放一次激励。</br>' +
  '激励方式为叠加时，销量或销售额、利润额等在激励有效期内每次达到激活目标中填写的值都会发放一次激励并扣减对应数值，超量部分会保留。</br>' +
  '激励方式为达量叠加时，销量或销售额、利润额等在激励有效期内，达到激活目标中填写的达成量，之后每次达到激活目标中的叠加量都会发放一次激励并扣减对应数值，超量部分会保留。'
export const allOptions = {
  searchType: [
    { label: '标题', value: 'title' },
    { label: 'ppid', value: 'ppId' },
    { label: '商品ID', value: 'productIds' },
  ],
  voidRule: (function () {
    const o = {
      title: [{ required: true, message: '请输入标题', trigger: ['change', 'blur'] }],
      businessType: [{ required: true, message: '请选择业务类别', trigger: ['change', 'blur'] }],
      inspireKind: [{ required: true, message: '请选择激励类别', trigger: ['change', 'blur'] }],
      inspireType: [{ required: true, message: '请选择激励方式', trigger: ['change', 'blur'] }],
      inspireWay: [{ required: true, message: '请选择激励类型', trigger: ['change', 'blur'] }],
      inspireTarget: [{ required: true, message: '请输入激活目标', trigger: ['change', 'blur'] }],
      targetType: [{ required: true, message: '请选择目标类型', trigger: ['change', 'blur'] }],
      inspirePrice: [{ required: true, validator: validatePass, trigger: ['change', 'blur'] }],
      orderTypes: [{ required: true, message: '请选择订单类型', trigger: ['change', 'blur'] }],
      // containDistributionOrder: [{ required: true, message: '请选择是否仅包含销售分销订单', trigger: ['change', 'blur'] }],
      timeRange: [{ required: true, message: '请选择激励时间', trigger: ['change', 'blur'] }]
    }
    if (window.tenant.xtenant < 1000) {
      o.manufacturerSubsidyFlag = [{ required: true, message: '请选择是否厂家补贴', trigger: ['change'] }]
    }
    return o
  }()),
  describes: {
    inspireTarget: isJiuJi ? inspireTargetJiuJi : inspireTargetOut,
    roles: '选中角色后，参与激励发放的员工仅为 主要角色 与选中角色对应的员工，其余员工不发放激励。不选则不做限制。'
  },
  containOptions: [
    { label: '是', value: 1 },
    { label: '否', value: 2 }
  ]
}

export const ppIdRoules = function (voidForm) {
  const required = { required: true, message: '请至少填写一项,多个用英文逗号隔开', trigger: ['change', 'blur'] }
  const custom = { validator: validatePassPPID('PPID'), trigger: ['change', 'blur'] }
  return !voidForm.productIds ? [required, custom] : voidForm.productIds && voidForm.ppId ? [custom] : []
}

export const productIdsRoules = function (voidForm) {
  const required = { required: true, message: '请至少填写一项,多个用英文逗号隔开', trigger: ['change', 'blur'] }
  const custom = { validator: validatePassPPID('商品ID'), trigger: ['change', 'blur'] }
  return !voidForm.ppId ? [required, custom] : voidForm.ppId && voidForm.productIds ? [custom] : []
}
