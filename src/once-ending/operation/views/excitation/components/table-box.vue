<template>
  <div>
    <ni-table :loading="loading"
              :data-source="tableList"
              :columns="columns.filter(d => d.show === undefined || d.show)"
              :pagination="pagination"
              @change="tableChange"
              rowKey="id"
              :row-selection="rowSelection"
              size="small"
              :bordered="true"
              :ignoreAffix="false"
    >
      <template slot="tool">
        <a-button :loading="voidLoading" @click="batchVoid">批量作废</a-button>
      </template>
      <div slot="action">
        <a-button icon="plus" class="mr-8" type="primary" @click="showExcitation">新增激励</a-button>
        <a-button @click="goDetail(1)" class="mr-8">全部发放明细</a-button>
        <a-button @click="goDetail(2)" class="mr-8">悬赏统计</a-button>
        <a-popover placement="bottom">
          <template slot="content">
            <div class="flex flex-col">
              <a-upload
                name="file"
                @change="(info) => handleChange(info, errorTip)"
                :action="uploadUrl"
                :headers="headers"
                accept=".xls,.xlsx"
                :showUploadList="false"
              >
                <a-button>数据导入</a-button>
              </a-upload>
              <a
                class="mt-8 btn-default"
                :href="$tnt.xtenant < 1000 ? 'https://img2.ch999img.com/newstatic/38299/1a1d5f508c301646.xlsx' : 'https://img2.ch999img.com/newstatic/61030/176180b6fbcbc5ed.xlsx'">
                模板下载
              </a>
            </div>
          </template>
          <a-button :loading="importLoading">批量导入</a-button>
        </a-popover>
        <export-excel class="export-data" icon="export" style-type="default" @click="exportExcitation(columns.filter(d => d.show === undefined || d.show))" title="导出" ref="exportData"></export-excel>
      </div>
    </ni-table>
    <a-modal :zIndex="100" class="void" centered :maskClosable="false" :footer="false" :destroyOnClose="true" v-model="showVoid" :width="748" title="添加激励">
      <div style="padding: 24px 24px 0 24px" class="add-excitation">
        <a-form-model ref="voidFormRef" :rules="allOptions.voidRule" :model="voidForm" class="flex flex-wrap" layout="vertical">
          <a-form-model-item label="标题" prop="title">
            <a-input class="titleInput" placeholder="请输入" v-model="voidForm.title" :maxLength="150" allowClear>
              <template slot="suffix" class="font-11">{{ voidForm.title && voidForm.title.length ? voidForm.title.length : 0 }}/150</template>
            </a-input>
          </a-form-model-item>
          <a-form-model-item label="业务类别" prop="businessType">
            <a-select placeholder="请选择"
                      style="width: 100%"
                      :options="enums.businessTypes"
                      v-model="voidForm.businessType"
                      @change="businessTypeChange"
            />
          </a-form-model-item>

          <a-form-model-item label="激励类别" prop="inspireKind">
            <a-input v-if="isJiuJi" value="销量" :disabled="true"/>
            <a-select placeholder="请选择"
                      style="width: 100%"
                      v-else
                      :disabled="$tnt.xtenant < 1000 && voidForm.businessType === 12"
                      :options="enums.inspireKinds"
                      v-model="voidForm.inspireKind"/>
          </a-form-model-item>

          <a-form-model-item label="激励方式" prop="inspireType">
            <a-select placeholder="请选择"
                      style="width: 100%"
                      @change="changeInspireType"
                      :options="enums.inspireTypes"
                      v-model="voidForm.inspireType"/>
          </a-form-model-item>

          <a-form-model-item label="激励类型" prop="inspireWay">
            <a-select placeholder="请选择"
                      style="width: 100%"
                      :disabled="disabled"
                      :options="enums.inspireWays"
                      v-model="voidForm.inspireWay"/>
          </a-form-model-item>

          <a-form-model-item required>
            <div class="full-width relative">
              <template v-if="voidForm.inspireType !== 3">
                <a-input-number @focus="changeFocus('inspireTarget', 'enter', '激活目标')"
                                @blur="changeFocus('inspireTarget', 'leave', '激活目标')"
                                :class="{'border-red': errorTypeMessage}"
                                ref="inspireTarget"
                                placeholder="仅支持正整数输入"
                                v-model="voidForm.inspireTarget"
                                style="width: 100%"/>
              </template>
              <template v-else>
                <div class="flex flex-align-center">
                  <span>达成量</span>
                  <a-input-number
                    v-model="voidForm.achieveTarget"
                    @focus="changeFocus('achieveTarget', 'enter', '达成量')"
                    @blur="changeFocus('achieveTarget', 'leave', '达成量')"
                    ref="finishNumber"
                    style="width: 65px;
                    margin-left: 2px"/>
                  <span class="ml-8">叠加量</span>
                  <a-input-number
                    v-model="voidForm.inspireTarget"
                    @focus="changeFocus('inspireTarget', 'enter', '叠加量')"
                    @blur="changeFocus('inspireTarget', 'leave', '叠加量')"
                    ref="addNumber"
                    style="width: 65px;
                    margin-left: 2px"/>
                </div>
              </template>
              <div class="errors" v-if="errorTypeMessage">
                {{ errorTypeMessage }}
              </div>
            </div>
            <div class="flex flex-align-center item-label" slot="label">
              <span>激活目标</span>
              <a-tooltip :overlayStyle="{ minWidth: '300px' }">
                <template slot="title">
                  <div v-html="allOptions.describes.inspireTarget"></div>
                </template>
                <a-icon class="font-14" style="margin-left: 5px" type="question-circle"/>
              </a-tooltip>
            </div>
          </a-form-model-item>

          <a-form-model-item label="目标类型" prop="targetType">
            <a-select placeholder="请选择"
                      style="width: 100%"
                      :disabled="disabledTarget || ($tnt.xtenant < 1000 && voidForm.businessType === 12)"
                      :options="enums.targetTypes"
                      v-model="voidForm.targetType"/>
          </a-form-model-item>

          <a-form-model-item>
            <div class="flex flex-align-center">
              <a-select placeholder="请选择"
                        style="width: 100%"
                        :maxTagCount="1"
                        mode="multiple"
                        class="area-selector"
                        allowClear
                        optionFilterProp="children"
                        :options="enums.roles"
                        v-model="voidForm.roles"/>
            </div>
            <div class="flex flex-align-center" slot="label">
              <span>主要角色</span>
              <a-tooltip :overlayStyle="{ minWidth: '300px' }">
                <template slot="title">
                  <div v-html="allOptions.describes.roles"></div>
                </template>
                <a-icon class="font-14" style="margin-left: 5px" type="question-circle"/>
              </a-tooltip>
              <a-checkbox class="ml-16" v-model="selectRoleAll" @change="changeRole">全选</a-checkbox>
            </div>
          </a-form-model-item>

          <a-form-model-item label="地区">
            <ni-area-select
              v-model="voidForm.areaIds"
              :allow-clear="true"
              multiple
              show-search
              :author="$tnt.xtenant >= 1000"
              placeholder="请选择"
              class="area-selector"
              :max-tag-count="1"
            />
          </a-form-model-item>

          <a-form-model-item label="门店类别">
            <a-select placeholder="请选择门店类别"
                      style="width: 100%"
                      mode="multiple"
                      allowClear
                      :maxTagCount="1"
                      :options="enums.areaTypes"
                      v-model="voidForm.areaKinds"/>
          </a-form-model-item>

          <a-form-model-item class="order-types" prop="orderTypes" v-if="$tnt.xtenant < 1000">
            <template slot="label">
              <div class="flex flex-align-center">
                <div class="custom-label flex-child-average">订单类型</div>
                <a-checkbox class="flex-child-noshrink" v-model="selectOrderTypesAll" @change="changeOrderTypes">全选</a-checkbox>
              </div>
            </template>
              <a-select placeholder="请选择订单类型"
                      style="width: 100%"
                      mode="multiple"
                      optionFilterProp="children"
                      :maxTagCount="1"
                      allowClear
                      :options="enums.orderTypes"
                      @change="orderTypesChange"
                      v-model="voidForm.orderTypes"/>
          </a-form-model-item>

          <!-- <a-form-model-item prop="containDistributionOrder" label="仅包含销售分销订单" v-if="showContain">
            <a-radio-group :options="allOptions.containOptions" v-model="voidForm.containDistributionOrder"/>
          </a-form-model-item> -->

          <a-form-model-item label="门店级别" v-if="$tnt.xtenant >= 1000">
            <a-select placeholder="请选择"
                      style="width: 100%"
                      mode="multiple"
                      :maxTagCount="1"
                      allowClear
                      :options="enums.areaLevel"
                      v-model="voidForm.areaLevels"/>
          </a-form-model-item>

          <a-form-model-item label="奖励积分" prop="inspirePrice">
            <a-input-number
              ref="inspirePrice"
              @change="changeInspirePrice"
              class="full-width"
              :min="1"
              :precision="0"
              :max="isJiuJi ? 1000 : 999999999"
              :placeholder="isJiuJi ? '仅支持输入小于等于1000的正整数' : '仅支持正整数输入'" v-model="voidForm.inspirePrice"/>
          </a-form-model-item>

          <a-form-model-item label="PPID" prop="ppId" ref="ppId" :autoLink="false" v-if="voidForm.businessType !== 12" :rules="voidForm.businessType !== 12 ? constants.ppIdRoules(voidForm) : null">
            <a-input placeholder="PPID和商品ID至少填写一项" style="width: 100%" v-model="voidForm.ppId" @blur="blur('ppId')" @change="change('ppId')"/>
          </a-form-model-item>

          <a-form-model-item label="商品ID" prop="productIds" ref="productIds" :autoLink="false" v-if="voidForm.businessType !== 12" :rules="voidForm.businessType !== 12 ? constants.productIdsRoules(voidForm) : null">
            <a-input placeholder="PPID和商品ID至少填写一项" style="width: 100%" v-model="voidForm.productIds" @blur="blur('productIds')" @change="change('productIds')"/>
          </a-form-model-item>
          <a-form-model-item>
            <div class="flex flex-align-center">
              <a-select style="width: 70px" v-model="preferentialType" :options="preferentialOptions"/>
              <a-input-number :disabled="voidForm.isOriPrice && preferentialType === '1'" style="flex: 1;margin-right: 4px" placeholder="请输入正整数" @blur="preferentialRestrictionsBlur" v-model="voidForm.preferentialRestrictions"/>
              <span>{{ preferentialType === '1' ? '元' : '%' }}</span>
            </div>
            <div class="errors" v-if="preferentialError">不可填写0或负数</div>
            <template slot="label">
              <div class="flex flex-justify-between relative">
                <div><span>优惠限制(小于)</span><a-tooltip :overlayStyle="{ minWidth: '300px' }">
                <template slot="title">
                  <p>金额：用于限制该激励内商品的改价金额，若订单内该商品改价金额大于所设置金额则不发放激励。小于等于所设置的金额才会发送激励。</p>
                  <p>比例：用于限制优惠比例小于配置数值，若优惠比例大于等于数值则不发放激励。优惠比例=（会员价-实际售价）/会员价*100% </p>
                  <p>注：仅识别直接修改商品价格的情况，订单使用优惠码进行优惠的，不影响激励发放</p>
                </template>
                <a-icon class="font-14" style="margin-left: 5px" type="question-circle"/>
              </a-tooltip></div>

                <a-checkbox v-model="voidForm.isOriPrice">原价</a-checkbox>
              </div>
            </template>
          </a-form-model-item>
          <a-form-model-item v-if="$tnt.xtenant >= 1000" label="优惠限制包含优惠码" prop="limitCouponFlag">
            <a-radio-group v-model="voidForm.limitCouponFlag">
              <a-radio :value="1" @click.native="(e) => radioClick(e)">
                是
              </a-radio>
              <a-radio :value="0" @click.native="(e) => radioClick(e)">
                否
              </a-radio>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="激励时间" prop="timeRange" v-if="$tnt.xtenant >= 1000">
            <a-range-picker
              v-model="voidForm.timeRange"
              format="YYYY-MM-DD"
              allow-clear
            />
          </a-form-model-item>
           <a-form-model-item v-if="$tnt.xtenant < 1000" label="是否厂家补贴" prop="manufacturerSubsidyFlag">
            <a-radio-group v-model="voidForm.manufacturerSubsidyFlag">
              <a-radio :value="true">
                是
              </a-radio>
              <a-radio :value="false">
                否
              </a-radio>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="激励时间" prop="timeRange" v-if="$tnt.xtenant < 1000">
            <a-range-picker
              v-model="voidForm.timeRange"
              :showTime="{valueFormat:'HH:mm:ss',format:'HH:mm:ss',defaultValue:[moment('00:00:00', 'HH:mm:ss'),moment('23:59:59', 'HH:mm:ss')]}"
              format="YYYY-MM-DD HH:mm:ss"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              allow-clear
              style="width: 100%"
            />
          </a-form-model-item>
          <a-form-model-item label="渠道" v-if="$tnt.xtenant >= 1000 && voidForm.businessType === 1">
            <a-select placeholder="请输入关键字搜索"
                      style="width: 100%"
                      mode="multiple"
                      :maxTagCount="1"
                      :default-active-first-option="false"
                      :not-found-content="fetchingChannel ? undefined : fetched ? '暂无数据' : null"
                      allowClear
                      :showSearch="true"
                      :filter-option="false"
                      :get-popup-container="(trigger) => trigger.parentNode"
                      @search="fetchChannel"
                      :options="channelOptions"
                      v-model="voidForm.channelIdList">
              <a-spin v-if="fetchingChannel" slot="notFoundContent" size="small" />
            </a-select>
          </a-form-model-item>
        </a-form-model>
      </div>
      <div class="flex flex-justify-end button-group">
        <a-button :loading="addLoading" type="primary" class="ml-8" style="width: 88px" @click="addExcitation(addTip)">添加</a-button>
      </div>
    </a-modal>
  </div>
</template>
<script type="text/jsx" lang="jsx">
  import { NiTable, NiAreaSelect } from '@jiuji/nine-ui'
  import moment from 'moment'
  import exportExcel from '~/components/exportExcel'
  import excitation from '@operation/api/excitation'
  import * as constants from '../constants.js'
  import { EXCITATION_ADD_ENUMS, EXCITATION_BATCH_VOID, EXCITATION_ADD } from '@operation/store/modules/excitation/action-types'
  import { ref, reactive, watch, computed, getCurrentInstance, nextTick } from 'vue'
  import { debounce } from 'lodash'
  const isJiuJi = window.tenant.xtenant < 1000
  export default {
    name: 'check-table',
    props: {
      tableList: {
        type: Array,
        default: () => {
          return []
        }
      },
      loading: {
        type: Boolean,
        default: false
      },
      pagination: {
        type: Object,
        default: () => {}
      },
      cachePrams: {
        type: Object,
        default: () => {}
      }
    },
    components: {
      NiTable,
      exportExcel,
      NiAreaSelect
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const voidForm = reactive({
        title: undefined,
        businessType: undefined,
        inspireKind: undefined,
        inspireType: undefined,
        inspireWay: undefined,
        inspireTarget: undefined,
        targetType: undefined,
        roles: undefined,
        areaIds: undefined,
        areaKinds: undefined,
        orderTypes: undefined,
        areaLevels: undefined,
        ppId: undefined,
        productIds: undefined,
        // sDate: undefined,
        // eDate: undefined,
        preferentialRestrictions: undefined,
        inspirePrice: undefined,
        achieveTarget: undefined,
        isOriPrice: false,
        timeRange: undefined,
        manufacturerSubsidyFlag: undefined,
        limitCouponFlag: undefined,
        // containDistributionOrder: undefined
        channelIdList: undefined
      })
      const fetchingChannel = ref(false)
      const fetched = ref(false)
      const channelOptions = ref([])
      let fetchChannel = (value) => {
        let params = {
          kinds: 3,
          q: value,
          limit: 100,
          timestamp: new Date().getTime()
        }
        channelOptions.value = []
        fetchingChannel.value = true
        proxy.$api.channel.getInsourceChannelList(params).then(res => {
          if (res.code === 0) {
            fetched.value = true
            channelOptions.value = res?.data?.map(item => ({
              label: item.companyJc,
              value: item.id
            }))
          }
        }).finally(() => {
          fetchingChannel.value = false
        })
      }
      fetchChannel = debounce(fetchChannel, 200)
      let enums = ref({})
      const selectedRowKeys = ref([])
      const selectedRows = ref([])
      const showVoid = ref(false)
      const disabled = ref(false)
      const disabledTarget = ref(false)
      const voidLoading = ref(false)
      const addLoading = ref(false)
      const selectRoleAll = ref(false)
      const selectOrderTypesAll = ref(false)
      const importLoading = ref(false)
      const focusInput = ref('')
      const errorName = ref('')
      const errorTypeMessage = ref('')
      const preferentialOptions = ref([
        { label: '金额', value: '1' },
        { label: '比例', value: '2' },
      ])
      const preferentialType = ref('1')
      function changeFocus (key, type, name) {
        errorName.value = name
        focusInput.value = key
        type === 'leave' && (setErrorType(voidForm[key], key))
      }
      const inputValue = computed(() => {
        return focusInput.value ? voidForm[focusInput.value] : undefined
      })
      watch(() => inputValue.value, (val) => {
        val !== undefined && (setErrorType(val))
      })
      function setErrorType (val, key) {
        if (key && key !== focusInput.value) return
        if (!val && val !== 0) {
          errorTypeMessage.value = '请输入' + errorName.value
        } else if (val <= 0) {
          errorTypeMessage.value = errorName.value + '不可填写0或负数'
        } else {
          if (!Number.isInteger(val)) {
            voidForm[focusInput.value] = parseInt(val)
          } else {
            errorTypeMessage.value = ''
          }
        }
      }
      function changeInspirePrice (val) {
        if (val && val > 0 && !Number.isInteger(val)) {
          voidForm.inspirePrice = parseInt(val)
        }
      }
      function clearRowKeys () {
        selectedRowKeys.value = []
        selectedRows.value = []
      }
      const getEnums = async function () {
        const res = await proxy.$store.dispatch(`operation/excitation/${EXCITATION_ADD_ENUMS}`)
        if (res) {
          enums.value = res.data
        }
      }
      getEnums()
      // 表格分页事件
      function tableChange (pagination) {
        for (let key in props.pagination) {
          props.pagination[key] = pagination[key]
        }
        proxy.$emit('fetchData')
      }
      function changeRole (e) {
        if (e.target.checked) {
          voidForm.roles = enums.value.roles.map(it => it.value)
        } else {
          voidForm.roles = []
        }
      }
      const changeOrderTypes = function (e) {
        if (e.target.checked) {
          voidForm.orderTypes = enums.value.orderTypes.map(it => it.value)
        } else {
          voidForm.orderTypes = []
        }
        nextTick(() => {
          proxy.$refs.voidFormRef.validateField('orderTypes')
        })
      }
      const orderTypesChange = function (val) {
        selectOrderTypesAll.value = val.length === enums.value.orderTypes.length
      }
      function batchVoid () { // 批量作废
        if (!selectedRowKeys.value.length) {
          return proxy.$message.warning('请选择要批量作废的数据')
        }
        proxy.$confirm({
          title: '是否确认作废，作废后无法恢复？',
          onOk: async () => {
            voidLoading.value = true
            const res = await proxy.$store.dispatch(`operation/excitation/${EXCITATION_BATCH_VOID}`, selectedRowKeys.value)
            voidLoading.value = false
            if (res) {
              proxy.$message.success('批量作废成功')
              proxy.$emit('fetchData')
            }
          }
        })
      }
      function showExcitation () { // 添加
        fetched.value = false
        channelOptions.value = []
        for (let key in voidForm) {
          voidForm[key] = undefined
        }
        voidForm.inspireKind = isJiuJi ? 1 : undefined
        showVoid.value = true
        voidForm.isOriPrice = false
        selectRoleAll.value = false
        selectOrderTypesAll.value = false
        errorTypeMessage.value = ''
        errorName.value = ''
      }
      const addExcitation = async (addTip) => { // 添加
        const h = proxy.$createElement
        let passSpecial = true
        if (voidForm.inspireType === 3) {
          if (!voidForm.inspireTarget) {
            errorTypeMessage.value = voidForm.inspireTarget !== 0 ? '请输入叠加量' : '叠加量不可填写0或负数'
            passSpecial = false
          }
          if (!voidForm.achieveTarget) {
            errorTypeMessage.value = voidForm.achieveTarget !== 0 ? '请输入达成量' : '达成量不可填写0或负数'
            passSpecial = false
          }
        } else if (!voidForm.inspireTarget) {
          errorTypeMessage.value = voidForm.inspireTarget !== 0 ? '请输入激活目标' : '激活目标不可填写0或负数'
          passSpecial = false
        }
        if (preferentialError.value) {
          passSpecial = false
        }
        proxy.$refs.voidFormRef.validate((valid) => {
          if (!valid || !passSpecial) {
            proxy.$message.warning('请先完善数据')
            return
          }
          if (proxy.$tnt.xtenant >= 1000) {
            if (voidForm.isOriPrice || voidForm.preferentialRestrictions) {
              if (!voidForm.limitCouponFlag && voidForm.limitCouponFlag !== 0) {
                return proxy.$message.warning('配置了‘优惠限制’，则必须选择优惠限制是否包含优惠码')
              }
            }
            if (voidForm.limitCouponFlag || voidForm.limitCouponFlag === 0) {
              if (!voidForm.isOriPrice && !voidForm.preferentialRestrictions) {
                return proxy.$message.warning('选择优惠限制是否包含优惠码，则必须配置了‘优惠限制’')
              }
            }
          }
          proxy.$confirm({
            title: '确认创建？',
            content: addTip(),
            onOk: async () => {
              addLoading.value = true
              const format = proxy.$tnt.xtenant < 1000 ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'
              const params = {
                ...voidForm,
                sDate: moment(voidForm.timeRange[0]).format(format),
                eDate: moment(voidForm.timeRange[1]).format(format),
                isOriPrice: voidForm.isOriPrice ? 1 : 0,
                roles: voidForm.roles ? voidForm.roles.join(',') : undefined,
                areaIds: voidForm.areaIds ? voidForm.areaIds.join(',') : undefined
              }
              if (proxy.$tnt.xtenant >= 1000) {
                delete params.manufacturerSubsidyFlag
              }
              if (proxy.$tnt.xtenant < 1000) {
                delete params.limitCouponFlag
              }
              delete params.preferentialRestrictions
              const key = preferentialType.value === '1' ? 'preferentialAmount' : 'preferentialRestrictions'
              params[key] = voidForm.preferentialRestrictions
              // 是否校验存在相同商品的激励
              params.checkSameProductFlag = true
              delete params.timeRange
              // const res = await proxy.$store.dispatch(`operation/excitation/${EXCITATION_ADD}`, params)
              // addLoading.value = false
              // if (res) {
              //   proxy.$message.success('添加成功')
              //   showVoid.value = false
              //   proxy.$emit('fetchData')
              // }
              excitation.excitationAdd(params).then(res => {
                if (res.code === 0) {
                  addLoading.value = false
                  proxy.$message.success('添加成功')
                  showVoid.value = false
                  proxy.$emit('fetchData')
                } else {
                  if (res.code === 3001) {
                    proxy.$confirm({
                      title: '提示',
                      content: res.userMsg || res.msg,
                      okText: '添加',
                      cancelText: '取消',
                      onOk: async () => {
                        params.checkSameProductFlag = false
                        excitation.excitationAdd(params).then(res => {
                          if (res.code === 0) {
                            addLoading.value = false
                            proxy.$message.success('添加成功')
                            showVoid.value = false
                            proxy.$emit('fetchData')
                          } else {
                            addLoading.value = false
                            proxy.$message.warn(res.userMsg || res.msg)
                          }
                        })
                      },
                      onCancel: () => {
                        addLoading.value = false
                      }
                    })
                  } else {
                    addLoading.value = false
                    proxy.$message.error(res.userMsg || res.msg)
                  }
                }
              })
            }
          })
        })
      }
      function handleChange (info, errorTip) {
        importLoading.value = true
        if (info.file.response) {
          const importResponse = info.file.response
          importLoading.value = false
          if (importResponse && importResponse.code === 0) {
            proxy.$message.success('数据导入成功')
            showVoid.value = false
            proxy.$emit('fetchData')
          } else {
            const error = importResponse.userMsg || importResponse.msg
            errorTip(error)
          }
        }
      }
      function exportExcitation (columns) { // 导出数据
        const params = excitation.excitationExport()
        let body = {
          ...props.cachePrams,
          ppIdDifferent: 1
        }
        body.inspireItems = columns.map(it => it.dataIndex)
        const pageKey = '/operation/excitation/list'
        const items = localStorage.getItem('listPageConfig')
        if (items) {
          const tableColumns = JSON.parse(items)[pageKey]?.tableColumns || undefined
          if (tableColumns) {
            const exportColumns = columns.filter(it => !(tableColumns.hasOwnProperty(it.dataIndex) && tableColumns[it.dataIndex].hide))
            body.inspireItems = exportColumns.map(it => it.dataIndex)
          }
        }
        params.body = body
        const name = '销售激励' + moment().format('YYYY.MM.DD HH时mm分ss秒') + '.xlsx'
        proxy.$refs.exportData.exportExcel(params, name)
      }
      function goDetail (type) {
        let address = '/Ch999User/BussinessInspireStatistics'
        if (type === 2) {
          address = '/Ch999User/InspireNewStatistics'
        }
        window.location.href = proxy.$tnt.oaHost + address
      }
      function changeInspireType () {
        errorTypeMessage.value = ''
        focusInput.value = ''
        voidForm.inspireTarget = undefined
        voidForm.achieveTarget = undefined
        const inspireTypeOne = voidForm.inspireType === 1
        const inspireTypeThree = voidForm.inspireType === 3
        voidForm.inspireWay = (inspireTypeOne || inspireTypeThree) ? 0 : undefined
        voidForm.targetType = voidForm.businessType === 12 ? 1 : inspireTypeThree ? 1 : undefined
        disabled.value = (inspireTypeOne || inspireTypeThree)
        disabledTarget.value = inspireTypeThree
      }

      const preferentialRestrictionsBlur = function (e) {
        const val = e.target.value
        const valList = val.split('.')
        if (valList.length > 0) {
          // voidForm.preferentialRestrictions = `${valList[0]}.${valList[1].slice(0, 2)}`
          voidForm.preferentialRestrictions = `${valList[0]}`
        }
      }
      const preferentialError = computed(() => {
        if (voidForm.isOriPrice && preferentialType.value === '1') {
          voidForm.preferentialRestrictions = undefined
          return false
        } else {
          const val = voidForm.preferentialRestrictions
          return !!(val <= 0 && val !== '')
        }
      })
      const getData = function (data) {
        const format = proxy.$tnt.xtenant < 1000 ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'
        return data ? moment(data).add(1, 'days').format(format) : data
      }

      const blur = function (type) {
        nextTick(() => {
          if (!voidForm.ppId && voidForm.productIds) {
            proxy.$refs.voidFormRef.clearValidate('ppId')
          } else if (!voidForm.productIds && voidForm.ppId) {
            proxy.$refs.voidFormRef.clearValidate('productIds')
          }
          proxy.$refs.ppId.onFieldBlur()
          proxy.$refs.productIds.onFieldBlur()
        })
      }

      const change = function (type) {
        nextTick(() => {
          if (!voidForm.ppId && voidForm.productIds) {
            proxy.$refs.voidFormRef.clearValidate('ppId')
          } else if (!voidForm.productIds && voidForm.ppId) {
            proxy.$refs.voidFormRef.clearValidate('productIds')
          }
          proxy.$refs.ppId.onFieldChange()
          proxy.$refs.productIds.onFieldChange()
        })
      }
      return {
        moment,
        getData,
        selectedRowKeys,
        selectedRows,
        showVoid,
        voidForm,
        enums,
        voidLoading,
        addLoading,
        importLoading,
        disabled,
        disabledTarget,
        selectRoleAll,
        selectOrderTypesAll,
        focusInput,
        errorTypeMessage,
        inputValue,
        preferentialOptions,
        preferentialType,
        preferentialError,
        changeFocus,
        changeInspirePrice,
        changeRole,
        changeOrderTypes,
        orderTypesChange,
        clearRowKeys,
        tableChange,
        batchVoid,
        showExcitation,
        addExcitation,
        handleChange,
        exportExcitation,
        goDetail,
        changeInspireType,
        preferentialRestrictionsBlur,
        blur,
        change,
        fetchingChannel,
        fetched,
        fetchChannel,
        channelOptions
      }
    },
    data () {
      return {
        constants,
        isJiuJi,
        allOptions: constants.allOptions,
        uploadUrl: `/cloudapi_nc/ncSegments/excel/import/v1?xservicename=oa-ncSegments`,
        checks: {
          remark: undefined,
          status: ''
        },
        showVisible: false,
        frameUrl: '',
        title: '',
        processStatusName: '',
        columns: [
          {
            title: 'ID',
            dataIndex: 'id',
            fixed: 'left',
            width: '80px',
            customRender: (text) =>
              <span> { text || '-' } </span>
          },
          {
            title: '状态',
            dataIndex: 'statusName',
            width: '100px',
            customRender: (text, record) =>
              <span style={{ color: record.statusColor || '' }}> { text || '-' } </span>
          },
          {
            title: '标题',
            width: '130px',
            dataIndex: 'title',
            customRender: (text) =>
              <span> { text || '-' } </span>
          },
          {
            title: '奖励积分',
            width: '100px',
            dataIndex: 'inspirePrice',
            customRender: (text) =>
              <span style={{ color: '#F5222D ' }}> { text || '-' } </span>
          },
          {
            title: '达量前提',
            width: '100px',
            dataIndex: 'achieveTarget',
            customRender: (text) =>
              <span> { text || '-' } </span>
          },
          {
            title: '激活目标',
            width: '100px',
            dataIndex: 'inspireTarget',
            customRender: (text) =>
              <span> { text || '-' } </span>
          },
          {
            title: '优惠限制',
            width: '100px',
            dataIndex: 'limitPriceName',
            customRender: (text) =>
              <span> { text || '-' } </span>
          },
          {
            title: '优惠限制包含优惠码',
            width: '150px',
            dataIndex: 'limitCouponFlagStr',
            show: this.$tnt.xtenant >= 1000,
            customRender: (text) =>
              <span> { text || '-' } </span>
          },
          {
            title: '开始日期',
            width: '100px',
            dataIndex: 'sDate',
            customRender: (text) =>
              <span> { text || '-' } </span>
          },
          {
            title: '结束日期',
            width: '100px',
            dataIndex: 'eDate',
            customRender: (text) =>
              <span> { text || '-' } </span>
          },
          {
            title: '业务类别',
            width: '100px',
            dataIndex: 'businessTypeName',
            customRender: (text) =>
              <span> { text || '-' } </span>
          },
          {
            title: '激励类别',
            width: '100px',
            dataIndex: 'inspireKindName',
            customRender: (text) =>
              <span> { text || '-' } </span>
          },
          {
            title: '激励方式',
            width: '100px',
            dataIndex: 'inspireTypeName',
            customRender: (text) =>
              <span> { text || '-' } </span>
          },
          {
            title: '激励类型',
            width: '100px',
            dataIndex: 'inspireWayName',
            customRender: (text) =>
              <span> { text || '-' } </span>
          },
          {
            title: '是否厂家补贴',
            width: '110px',
            dataIndex: 'manufacturerSubsidyText',
            show: this.$tnt.xtenant < 1000,
            customRender: (text) =>
              <span> { text || '-' } </span>
          },
          {
            title: '订单类型',
            width: '110px',
            dataIndex: 'subType',
            customRender: (text) =>
              text ? <a-popover>
              <div style={{ maxWidth: '464px' }} slot="content">{ text }</div>
              <span class="lines-2"> { text } </span>
            </a-popover> : '-',
            show: this.$tnt.xtenant < 1000
          },
          // {
          //   title: '仅包含销售分销订单',
          //   width: '150px',
          //   dataIndex: 'containDistributionOrderStr',
          //   customRender: (text) =>
          //     <span> { text || '-' } </span>,
          //   show: this.$tnt.xtenant < 1000
          // },
          {
            title: '目标类型',
            width: '110px',
            dataIndex: 'targetTypeName',
            customRender: (text) =>
              <span> { text || '-' } </span>
          },
          {
            title: '主要角色',
            width: '100px',
            dataIndex: 'roles',
            customRender: (text) => text ? <a-popover>
              <div style={{ maxWidth: '464px' }} slot="content">{ text }</div>
              <span class="lines-1"> { text } </span>
            </a-popover> : '-'
          },
          {
            title: '地区',
            width: '140px',
            dataIndex: 'area',
            customRender: (text) => text ? <a-popover>
              <div style={{ maxWidth: '464px' }} slot="content">{ text }</div>
              <span class="lines-1"> { text } </span>
            </a-popover> : '-'
          },
          {
            title: 'ppriceid',
            width: '100px',
            dataIndex: 'ppId',
            customRender: (text, record) => {
              const string = record.products.length ? record.products.map(it => it.productName + '(' + it.productColor + ')' + '(' + it.ppId + ')').join(',') : ''
              return string ? <a-popover>
                <div style={{ maxWidth: '464px' }} slot="content">{string}</div>
                <span class="lines-1"> {string} </span>
              </a-popover> : '-'
            }
          },
          {
            title: '商品ID',
            width: '100px',
            dataIndex: 'productList',
            customRender: (text, record) => {
              const string = record.productList.length ? record.productList.map(it => it.productName + '(' + it.productColor + ')' + '(' + it.pid + ')').join(',') : ''
              return string ? <a-popover>
                <div style={{ maxWidth: '464px' }} slot="content">{string}</div>
                <span class="lines-1"> {string} </span>
              </a-popover> : '-'
            }
          },
          {
            title: '渠道',
            width: '120px',
            dataIndex: 'channelNames',
            forceHide: this.$tnt.xtenant < 1000,
            customRender: (text) => text ? <a-popover>
              <div style={{ maxWidth: '464px' }} slot="content">{text}</div>
              <span class="lines-1"> {text} </span>
            </a-popover> : '-'
          },
          {
            title: '添加人',
            width: '140px',
            dataIndex: 'inUser',
            customRender: (text) =>
              <span> { text || '-' } </span>
          },
          {
            title: '累计发放',
            width: '100px',
            fixed: 'right',
            dataIndex: 'totalPoints',
            customRender: (text, record) => {
              return <a rel="noopener noreferrer" href={`/Ch999User/BussinessInspireStatistics?id=${record.id}&Start=${this.getData(record.sDate) + (this.$tnt.xtenant < 1000 ? '' : ' 00:00:00')}&End=${this.getData(record.eDate) + (this.$tnt.xtenant < 1000 ? '' : ' 23:59:59')}`}>{ text }
              </a>
            }

          }
        ]
      }
    },
    methods: {
      radioClick (e) {
        if (this.voidForm.limitCouponFlag === Number(e.target.value)) {
          this.voidForm.limitCouponFlag = undefined
        } else {
          this.voidForm.limitCouponFlag = e.target.value
        }
      },
      errorTip (error) {
        this.$message.error({
          icon: () => <span></span>,
          content: () => <div class="error-content">
            <div class="flex">
              <a-icon theme="filled" two-tone-color="#eb2f96" type="close-circle" class="icon"/>
              <span class="ml-16">错误提示</span>
            </div>
            <div class="error-text" domPropsInnerHTML={error}></div>
          </div>
        })
      },
      addTip () {
        const h = this.$createElement
        const { inspireKind, inspireType, achieveTarget, inspireTarget, inspirePrice } = this.voidForm
        const inspireKindName = this.enums.inspireKinds.find(it => it.value === inspireKind)?.label || ''
        const spanInspireKindName = <span class="red">{ inspireKindName }</span>
        const achieveName = inspireType === 3 ? <span>达成量：<span class="red">{achieveTarget}</span>
          叠加量：<span class="red">{inspireTarget}</span></span> : <span class="red">{ inspireTarget }</span>
        const xtenant = this.$tnt.xtenant
        const area = this.$store.state.userInfo.Area
        const content = <p>{xtenant === 0 ? <span>当前创建地区为：<span class="red">{area}</span>；</span> : ''}
          {xtenant === 0 ? '' : '您提交的'}激励类别为：{spanInspireKindName}；
          激活目标为：{achieveName}；奖励积分为<span class="red">{inspirePrice}</span>。由于涉及到积分发放，请
          <span class="red">慎重确定</span>是否填写正确
        </p>
        return content
      },
      businessTypeChange (value) {
        if (value === 12) {
          this.voidForm.inspireKind = 1
          this.voidForm.targetType = 1
        }
      }
    },
    computed: {
      // 批量选择处理
      rowSelection () {
        return {
          onChange: (selectedRowKeys, selectedRows) => {
            this.selectedRowKeys = selectedRowKeys
            this.selectedRows = selectedRows
          },
          selectedRowKeys: this.selectedRowKeys,
          getCheckboxProps: record => ({
            props: {
              disabled: !record.selected
            }
          })
        }
      },
      headers () {
        return {
          Authorization: this.$store.state.token,
          scene: 'inspire'
        }
      },
      // showContain () {
      //   return Boolean(this.$tnt.xtenant < 1000 && [2, 5, 6, 11].find(it => this.voidForm.orderTypes?.includes(it)))
      // }
    }
  }
</script>
<style scoped lang="scss">
.void {
  .ant-form-item {
    width: calc((100% - 24px) / 2);
    margin-bottom: 18px;
    &:nth-child(2n) {
      margin-left: 24px;
    }
  }
  .ant-form-item-with-help {
    margin-bottom: -2px;
  }
  .ant-form-vertical .ant-form-explain {
    position: relative;
  }
}
:deep(.ant-select) {
  height: 32px;
}
:deep(.ant-modal-body) {
  padding: 0px;
}
:deep(.area-selector) {
  width: 100%;
  .ant-select-selection--multiple{
    max-height: 32px;
    .ant-select-selection__choice{
      max-width: calc(100% - 90px) !important;
    }
  }
}
.btn-default{
    line-height: 32px;
    position: relative;
    display: inline-block;
    font-weight: 400;
    white-space: nowrap;
    text-align: center;
    border: 1px solid transparent;
    height: 32px;
    padding: 0 15px;
    font-size: 14px;
    border-radius: 4px;
    color: rgba(0, 0, 0, 0.65);
    background-color: #fff;
    border-color: #d9d9d9;
}
.color-b {
  color: #1890ff;
}
.show-length {
  position: absolute;
  right: 12px;
  bottom: -2px;
  color: #bfbfbf;
}
.button-group {
  border-top: 0.5px solid rgba(0, 0, 0, 0.06);
  padding: 10px;
}
.mr-8 {
  margin-right: 8px;
}
.item-label {
  margin: -20px 0 0 12px;
}
.add-excitation {
  height: 70vh;
  overflow-y: auto;
}
.error-content {
  margin: 6px 12px;
  max-width: 400px;
  .icon {
    font-size: 18px;
    margin-right: 0px;
    padding-bottom: 1px
  }
  .error-text {
    margin: 8px 4px 4px 34px;
    text-align: left;
    line-height: 26px
  }
}
.border-red {
  border-color: #f5222d;
}
.errors {
  color: #f5222d;
  position: absolute;
  top: 34px;
  left: 0;
}
.red {
  color: #f5222d
}
.order-types{
  :deep(.ant-form-item-required){
    &::before{
      display: none;
    }
  }
}
.custom-label{
  &::before{
    display: inline-block;
    margin-right: 4px;
    color: #f5222d;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }
}
.titleInput {
  :deep(.ant-input) {
    padding-right: 78px !important;
  }
}
</style>
