<template>
  <ni-filter :loading="loading" :form="form" @filter="fetchData" :labelWidth="100">
    <ni-filter-item label="搜索">
      <a-input v-model="form.keyWord" placeholder="请输入">
        <a-select :options="allOptions.searchType" v-model="form.searchType" slot="addonBefore" style="width: 80px"/>
      </a-input>
    </ni-filter-item>

    <ni-filter-item label="业务类别">
      <a-select placeholder="请选择业务类别"
                allowClear
                :options="enums.businessTypes"
                v-model="form.businessType"/>
    </ni-filter-item>

    <ni-filter-item label="激励类别">
      <a-select placeholder="请选择激励类别"
                allowClear
                :options="enums.inspireKinds"
                v-model="form.inspireKind"/>
    </ni-filter-item>

    <ni-filter-item label="激励方式">
      <a-select placeholder="请选择激励方式"
                allowClear
                :options="enums.inspireTypes"
                v-model="form.inspireType"/>
    </ni-filter-item>

    <ni-filter-item label="激励类型">
      <a-select placeholder="请选择激励类型"
                allowClear
                :options="enums.inspireWays"
                v-model="form.inspireWay"/>
    </ni-filter-item>

    <ni-filter-item label="地区">
      <ni-area-select
        v-model="form.areaIds"
        :allow-clear="true"
        multiple
        show-search
        placeholder="请选择地区"
        class="area-selector"
        :max-tag-count="1"
      />
    </ni-filter-item>

    <ni-filter-item label="激励状态">
      <a-select placeholder="请选择激励状态"
                allowClear
                mode="multiple"
                :maxTagCount="1"
                :options="enums.status"
                v-model="form.status"/>
    </ni-filter-item>

    <ni-filter-item class="time" label="时间筛选">
      <a-range-picker
        v-model="form.timeRange"
        allow-clear
      ></a-range-picker>
    </ni-filter-item>
    <ni-filter-item v-if="$tnt.xtenant < 1000" label="是否厂家补贴">
      <a-select placeholder="请选择是否厂家补贴"
                allowClear
                :options="enums.inspireManufacturerSubsidys"
                v-model="form.manufacturerSubsidyFlag"/>
    </ni-filter-item>
  </ni-filter>
</template>
<script>
  import { NiFilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import { reactive, toRefs, getCurrentInstance } from 'vue'
  import { EXCITATION_ENUMS } from '@operation/store/modules/excitation/action-types'
  import * as constants from '../constants.js'
  export default {
    name: 'check-form',
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect
    },
    props: {
      typeInfo: {
        type: Object
      },
      loading: {
        type: Boolean,
        default: false
      },
      form: {
        type: Object,
        default: () => {
          return {
            areaIds: [],
            searchType: 'title',
            keyWord: '',
            businessType: '',
            inspireKind: '',
            inspireType: '',
            inspireWay: '',
            timeRange: [],
            status: '',
            manufacturerSubsidyFlag: undefined
          }
        }
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const pageData = reactive({
        enums: []
      })
      const getEnums = async function () {
        const res = await proxy.$store.dispatch(`operation/excitation/${EXCITATION_ENUMS}`)
        if (res) {
          let cacheEnums = {}
          if (res.data) {
            for (let key in res.data) {
              cacheEnums[key] = res.data[key].map(it => ({ ...it, value: it.value + '' }))
            }
          }
          pageData.enums = { ...cacheEnums }
        }
      }
      getEnums()
      function getOption (array) {
        return array ? array.map(it => ({
          label: it.name,
          value: it.code
        })) : []
      }
      function fetchData () {
        proxy.$emit('fetchData')
      }
      return {
        ...toRefs(pageData),
        getOption,
        fetchData
      }
    },
    data () {
      return {
        allOptions: constants.allOptions,
      }
    }
  }
</script>
<style lang="scss" scoped>
:deep(.area-selector) {
  max-width: 100%;
  .ant-select-selection--multiple{
    max-height: 32px;
    .ant-select-selection__choice{
      max-width: calc(100% - 90px) !important;
    }
  }
}
.check-form{
  .time{
    :deep(.ant-select-selection) {
      border-radius: 4px 0 0 4px;
      border-right: none;
    }
    :deep(.ant-calendar-picker-input) {
      border-radius: 0 4px 4px 0;
    }
  }
  .button-group{
    .export-data{
      margin-right: 30px;
      margin-left: 20px;
    }
  }
}
</style>
