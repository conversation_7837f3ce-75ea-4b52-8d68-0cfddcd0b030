<script type="text/jsx" lang="jsx">
  import { NiListPage } from '@jiuji/nine-ui'
  import { defineComponent, ref, reactive, getCurrentInstance } from 'vue'
  import searchBox from './components/search-box.vue'
  import { EXCITATION_LIST } from '@operation/store/modules/excitation/action-types'
  import tableBox from './components/table-box.vue'
  import moment from 'moment'
  export default defineComponent({
    name: 'index',
    components: { searchBox, tableBox, NiListPage },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const loading = ref(false)
      const exportLoading = ref(false)
      const form = reactive({
        areaIds: [],
        searchType: 'title',
        keyWord: undefined,
        businessType: undefined,
        inspireKind: undefined,
        inspireType: undefined,
        inspireWay: undefined,
        timeRange: [],
        status: undefined,
        manufacturerSubsidyFlag: undefined
      })
      const tableList = ref([])
      const pagination = reactive({
        pageSize: 20,
        current: 1,
        total: 0,
        showTotal: (total) => `共${total}条数据`,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ['10', '20', '35', '50']
      })
      const pageData = reactive({
        cachePrams: {}
      })
      const fetchData = async function (current) {
        if (current === 1) pagination.current = 1
        let params = {
          ...form,
          current: pagination.current,
          size: pagination.pageSize,
          ppIdDifferent: 2
        }
        if (proxy.$tnt.xtenant >= 1000) {
          delete params.manufacturerSubsidyFlag
        }
        if (form.keyWord) {
          params[form.searchType] = form.keyWord
          if ((params.searchType === 'ppId' || params.searchType === 'productIds') && isNaN(form.keyWord)) {
            return proxy.$message.warning(`请输入正确的${params.searchType === 'ppId' ? 'ppid' : '商品ID'}`)
          }
        }
        if (proxy.$refs.refTableBox) {
          proxy.$refs.refTableBox.clearRowKeys()
        }
        if (form.timeRange && form.timeRange.length) {
          params.sDate = moment(form.timeRange[0]).format('YYYY-MM-DD')
          params.eDate = moment(form.timeRange[1]).format('YYYY-MM-DD')
        }
        delete params.keyWord
        delete params.searchType
        delete params.timeRange
        loading.value = true
        pageData.cachePrams = { ...params }
        const res = await proxy.$store.dispatch(`operation/excitation/${EXCITATION_LIST}`, params)
        loading.value = false
        if (res) {
          tableList.value = res.data.records
          pagination.total = res.data.total
        }
      }
      return {
        loading,
        fetchData,
        exportLoading,
        form,
        tableList,
        pagination,
        pageData
      }
    },
    render () {
      const { form, tableList, pagination, fetchData, exportLoading, loading, pageData } = this
      return <page>
        <ni-list-page pushFilterToLocation={false}>
          <search-box export-loading={exportLoading}
                      form={form}
                      loading={loading}
                      onFetchData={() => fetchData(1)}>
          </search-box>
          <table-box ref='refTableBox'
                     loading={loading}
                     class="mt-16"
                     cachePrams={pageData.cachePrams}
                     onFetchData={() => fetchData()}
                     pagination={pagination}
                     table-list={tableList}></table-box>
        </ni-list-page>
      </page>
    }
  })
</script>

<style scoped>
.check-table-box{
  margin-top: 20px;
}
</style>
