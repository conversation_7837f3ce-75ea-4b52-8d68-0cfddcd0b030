<template>
  <div class="open-classes">
    <div class="content mt-8">
      <a-form-model class="rule-form" v-for="(item,index) in formData" :key="index" :model="item" :label-col="{span: 3}" :wrapper-col="{span: 21}">
        <a-form-model-item label="课程名称">
          <text-input v-model="item.title" placeholder="请输入课程名称" maxLength="20"></text-input>
        </a-form-model-item>
        <a-form-model-item label="课程简介">
          <text-input type="textarea" v-model="item.content" placeholder="请输入课程简介" maxLength="200"></text-input>
        </a-form-model-item>
        <a-form-model-item label="视频">
          <uploader @change="res => videoChange(res,item)" :showUploadAPP="false" accept=".mp4,.avi,.wmv,.mov,.wav,.flv" :fileList="item.fileBO" :disabled="item.fileBO.length === 1" :buttonName="['上传视频']" :multiple="false" :moreAmount="false" :editFileName="false" pathKey="filePath"/>
        </a-form-model-item>
      </a-form-model>
    </div>
  </div>
</template>

<script setup>
  import { rules, openClassesObj } from '../constants'
  import { defineExpose, getCurrentInstance, ref } from 'vue'
  import TextInput from '~/components/form/text-input'
  import Uploader from '~/components/uploader'
  import { cloneDeep } from 'lodash'
  import { to } from '~/util/common'
  import dashboard from '@/operation/api/dashboard'
  import { message } from 'ant-design-vue'

  const { proxy } = getCurrentInstance()

  const formData = ref(Array.from(new Array(8)).map(d => cloneDeep(openClassesObj)))

  const videoChange = function (res, item) {
    item.fileBO = res
  }

  const getPublicClass = async function () {
    const [err, res] = await to(dashboard.getPublicClass())
    if (err) throw err
    const { code, data: { itemList }, userMsg } = res
    if (code === 0) {
      if (itemList) {
        itemList.map((d, i) => {
          const { fileBO, ...other } = d
          const o = {
            ...d,
            fileBO: fileBO ? [fileBO] : []
          }
          formData.value.splice(i, 1, o)
        })
      }
    } else {
      message.error(userMsg)
    }
  }
  getPublicClass()

  const save = async function () {
    const params = {
      itemList: formData.value.map(d => {
        const { fileBO, ...other } = d
        return {
          ...other,
          fileBO: fileBO.length ? fileBO[0] : null
        }
      })
    }
    const [err, res] = await to(dashboard.savePublicClass(params))
    if (err) throw err
    const { code, userMsg } = res
    if (code === 0) {
      message.success('保存成功')
      getPublicClass()
    } else {
      message.error(userMsg)
    }
  }

  defineExpose({
    save
  })
</script>

<style scoped lang="scss">
.open-classes{
  .title{
    font-size: 14px;
  }
  .content{
    border: 1px solid #e8e8e8;
    border-radius: 2px;
    padding: 10px 16px;
    .rule-form{
      margin-bottom: 16px;
      border-bottom: 1px dashed #e8e8e8;
      &:last-child{
        margin-bottom:0;
        border-bottom:none;
      }
    }
  }
}
</style>
