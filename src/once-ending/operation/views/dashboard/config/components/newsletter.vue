<template>
  <div class="newsletter">
  <div class="title bold">主版块内容</div>
    <div class="content mt-8">
      <a-form-model :model="mainData" :label-col="{span: 3}" :wrapper-col="{span: 21}">
        <a-form-model-item label="标题">
          <text-input v-model="mainData.title" placeholder="请输入标题" maxLength="20"></text-input>
        </a-form-model-item>
        <a-form-model-item label="本期精彩议程">
          <text-input type="textarea" v-model="mainData.content" placeholder="请输入本期精彩议程" maxLength="200"></text-input>
        </a-form-model-item>
        <a-form-model-item label="视频">
          <uploader @change="mainVideoChange" :showUploadAPP="false" accept=".mp4,.avi,.wmv,.mov,.wav,.flv" :fileList="mainData.fileBO" :disabled="mainData.fileBO.length === 1" :buttonName="['上传视频']" :multiple="false" :moreAmount="false" :editFileName="false" pathKey="filePath"/>
        </a-form-model-item>
      </a-form-model>
    </div>
    <div class="title bold mt-16">右侧栏目</div>
    <div class="content mt-8">
      <a-form-model class="right-form" v-for="(item,index) in rightData" :key="index" :model="item" :label-col="{span: 3}" :wrapper-col="{span: 21}">
        <a-form-model-item label="标题">
          <text-input v-model="item.title" placeholder="请输入标题" maxLength="20"></text-input>
        </a-form-model-item>
        <a-form-model-item label="本期精彩议程">
          <text-input type="textarea" v-model="item.content" placeholder="请输入本期精彩议程" maxLength="200"></text-input>
        </a-form-model-item>
      </a-form-model>
    </div>
  </div>
</template>

<script setup>
  import { rules, newsletterDefaultObj } from '../constants'
  import { ref, defineExpose, getCurrentInstance } from 'vue'
  import TextInput from '~/components/form/text-input'
  import Uploader from '~/components/uploader'
  import dashboard from '@/operation/api/dashboard'
  import { to } from '~/util/common'
  import { cloneDeep } from 'lodash'
  import { message } from 'ant-design-vue'
  const { proxy } = getCurrentInstance()
  const mainData = ref({
    title: undefined,
    content: undefined,
    fileBO: []
  })

  const rightData = ref(Array.from(new Array(5)).map(d => cloneDeep(newsletterDefaultObj)))

  const mainVideoChange = function (res) {
    mainData.value.fileBO = res
  }

  const getMainConfig = async function () {
    const [err, res] = await to(dashboard.getMainConfig())
    if (err) throw err
    const { code, data: { mainItem, itemList }, userMsg } = res
    if (code === 0) {
      if (mainItem) {
        const { fileBO, ...other } = mainItem
        mainData.value = {
          ...other,
          fileBO: fileBO ? [fileBO] : []
        }
      }
      if (itemList) {
        itemList.map((d, i) => {
          rightData.value.splice(i, 1, d)
        })
      }
    } else {
      message.error(userMsg)
    }
  }
  getMainConfig()

  const save = async function () {
    const { fileBO, ...other } = mainData.value
    const params = {
      mainItem: {
        ...other,
        fileBO: fileBO.length ? fileBO[0] : null
      },
      itemList: rightData.value
    }
    const [err, res] = await to(dashboard.saveMainConfig(params))
    if (err) throw err
    const { code, userMsg } = res
    if (code === 0) {
      message.success('保存成功')
      getMainConfig()
    } else {
      message.error(userMsg)
    }
  }

  defineExpose({
    save
  })

</script>

<style scoped lang="scss">
.newsletter{
  .title{
    font-size: 14px;
  }
  .content{
    border: 1px solid #e8e8e8;
    border-radius: 2px;
    padding: 10px 16px;
    .right-form{
      margin-bottom: 16px;
      border-bottom: 1px dashed #e8e8e8;
      &:last-child{
        margin-bottom:0;
        border-bottom:none;
      }
    }
  }
}
</style>
