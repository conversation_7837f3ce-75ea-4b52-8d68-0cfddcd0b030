<template>
  <page class="config">
    <div class="main">
      <a-tabs v-model="active">
        <a-tab-pane v-for="tab in tabs" :key="tab.value" :tab="tab.label">
          <newsletter ref="newsletter" v-if="tab.value === 1"></newsletter>
          <open-classes ref="openClasses" v-if="tab.value === 2"></open-classes>
        </a-tab-pane>
      </a-tabs>
    </div>
    <div class="footer fixed flex flex-align-center">
      <a-button type="primary" @click="save">
        保存
      </a-button>
    </div>
  </page>
</template>

<script setup>
  import { ref, getCurrentInstance } from 'vue'
  import { tabs } from './constants'
  import Newsletter from './components/newsletter'
  import OpenClasses from './components/open-classes'

  const active = ref(1)

  const { proxy } = getCurrentInstance()

  const save = function () {
    if (active.value === 1) {
      proxy.$refs.newsletter[0].save()
    } else {
      proxy.$refs.openClasses[0].save()
    }
  }
</script>

<style scoped lang="scss">
.config{
  .main{
    background: #fff;
    border-radius: 8px;
    padding: 10px 16px;
    margin-bottom: 74px;
  }
  .footer {
    height: 64px;
    padding: 0 20px;
    bottom: 0;
    background: #fff;
    left: 0;
    right: 0;
    z-index: 9;
  }
}
</style>
