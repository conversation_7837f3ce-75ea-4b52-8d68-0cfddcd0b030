export const tabs = [{
  label: '商学院-周度时讯会',
  value: 1
}, {
  label: '商学院-公开课',
  value: 2
}]

export const rules = function (type) {
  return {
    title: [{ required: true, message: `请输入${type === 'newsletter' ? '标题' : '课程名称'}`, trigger: ['blur', 'change'] }],
    content: [{ required: true, message: `请输入${type === 'newsletter' ? '本期精彩议程' : '课程简介'}`, trigger: ['blur', 'change'] }],
    fileBO: [{ required: true, message: '请上传视频', trigger: 'change' }]
  }
}

const baseObj = {
  title: undefined,
  content: undefined
}

export const newsletterDefaultObj = {
  ...baseObj
}

export const openClassesObj = {
  ...baseObj,
  fileBO: []
}
