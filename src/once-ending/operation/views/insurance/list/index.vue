<script lang="jsx">
  import { defineComponent } from 'vue'
  import { createState } from './hooks/useState'
  import { options } from './constants'
  import Item from './components/item'

  export default defineComponent({
    name: 'index',
    setup () {
      const { currentTab } = createState()
      return {
        currentTab
      }
    },
    render () {
      return <page>
        <a-tabs v-model={this.currentTab}>
          { options.tabs.map(item => <a-tab-pane key={item.value} tab={item.label} >
            <Item type={item.value}/>
          </a-tab-pane>) }
        </a-tabs>
      </page>
    }
  })
</script>

<style scoped lang="scss">
:deep(.ant-tabs-bar) {
  padding-top: 8px;
  margin: 0;
  background: #fff;
  .ant-tabs-nav {
    margin-left: 24px;
    font-size: 16px;
  }
}
</style>
