import { provide, inject, ref, getCurrentInstance } from 'vue'
import insuranceApi from '@/operation/api/insurance'
import { to } from '~/util/common'

const key = Symbol('key')

export function useState () {
  return inject(key)
}

export function createState () {
  const { proxy } = getCurrentInstance()

  const currentTab = ref(1)

  const { type } = proxy.$route.query
  type && (currentTab.value = +type)

  const companyTaxList = ref([])
  async function getCompanyTaxList () {
    const [err, res] = await to(insuranceApi.getCompanyTaxList())
    if (err) throw err
    if (res?.code === 0) {
      companyTaxList.value = res?.data?.map(it => ({
        ...it,
        label: it.name,
        value: it.id,
      })) || []
    }
  }
  getCompanyTaxList()

  const o = {
    currentTab,
    companyTaxList
  }
  provide(key, o)
  return o
}
