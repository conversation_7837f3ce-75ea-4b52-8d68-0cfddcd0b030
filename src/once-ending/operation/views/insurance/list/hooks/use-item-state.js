import { reactive, provide, inject, ref, getCurrentInstance, computed } from 'vue'
import { cloneDeep } from 'lodash'
import { originEditForm, originSearchForm, options, defaultpagination, toggleFoldDelParamsKeysMap } from '../constants'
import insuranceApi from '@/operation/api/insurance'
import { to } from '~/util/common'
import axios from 'axios'
import moment from 'moment'

const key = Symbol('key')

export function useState () {
  return inject(key)
}

export function createState (currentTab) {
  const { proxy } = getCurrentInstance()
  const state = reactive({
    searchForm: cloneDeep(originSearchForm),
    loading: false,
    dataSource: [],
    selectedRowKeys: [],
    pagination: cloneDeep(defaultpagination),
    productNameOption: [],
    showEdit: false,
    showLog: false,
    logs: [],
    editForm: cloneDeep(originEditForm),
    editLoading: false,
    uploadLoading: false,
    exportLoading: false,
    cacheParams: {}
  })

  const rowSelection = computed(() => {
    return {
      onChange: (selectedRowKeys) => {
        state.selectedRowKeys = selectedRowKeys
      },
      selectedRowKeys: state.selectedRowKeys,
    }
  })

  const toggleFold = ref(true)

  const onToggleFold = function (val) {
    toggleFold.value = val
  }

  const getBaseParams = function () {
    const { searchForm: { searchType, time, keyWord, ...other }, pagination: { current, pageSize } } = state
    const params = cloneDeep({
      ...other,
      type: currentTab,
      startEndTime: time?.length ? time[0] : undefined,
      toEndTime: time?.length ? time[1] : undefined,
      current,
      size: pageSize
    })
    params[searchType] = keyWord
    if (toggleFold.value) {
      const keys = toggleFoldDelParamsKeysMap.get(currentTab)
      keys.map(d => {
        delete params[d]
      })
    }
    return params
  }

  async function getData (type) {
    if (type === 'search') {
      state.pagination.current = 1
    }
    const params = getBaseParams()
    state.cacheParams = params
    console.log('params', params)
    state.loading = true
    const [err, res] = await to(insuranceApi.getList(params))
    state.loading = false
    if (err) throw err
    if (res?.code === 0) {
      state.selectedRowKeys = []
      const { records, total } = res.data
      state.dataSource = records
      state.pagination.total = total
    } else {
      proxy.$message.error(res.userMsg)
    }
  }

  async function handleTableChange (paginationObj) {
    Object.assign(state.pagination, paginationObj)
    getData()
  }

  function getLink (id, type) {
    let link = `/detail/${id}`
    switch (type) {
      case 'edit':
        link = `/edit/${id}?type=${currentTab}`
        break
      case 'add':
        link = `/edit/0?type=${currentTab}`
        break
    }
    return '/operation/insurance' + link
  }

  const editRef = ref()
  async function save () {
    editRef.value.validate()
  }

  function deleteItem (id) {
    const { selectedRowKeys } = state
    const ids = id ? [id] : selectedRowKeys
    if (!ids?.length) {
      return proxy.$message.warning('请先选择数据')
    }
    proxy.$confirm({
      title: '确认删除吗？',
      onOk: async () => {
        const [err, res] = await to(insuranceApi.deleteItem(ids.join(',')))
        if (err) throw err
        if (res?.code === 0) {
          proxy.$message.success('删除成功')
          getData()
        } else {
          proxy.$message.error(res.userMsg)
        }
      }
    })
  }

  async function toShowLog (id) {
    state.showLog = true
  }

  function reset () {
    state.searchForm = cloneDeep(originSearchForm)
  }

  function downloadTemplate () {
    const url = insuranceApi.downloadTemplate(currentTab)
    const fileName = options.tabs.find(it => it.value === currentTab).label + '保险导入模板'
    exportData(url, fileName, false)
  }

  async function exportList () {
    const url = insuranceApi.exportList()
    const fileName = options.tabs.find(it => it.value === currentTab).label + '保险管理'
    state.exportLoading = true
    await exportData(url, fileName, true, state.cacheParams)
    state.exportLoading = false
  }

  function exportData (url, fileName = '', useTime = true, params = {}) {
    return new Promise((resolve, reject) => {
      axios({
        method: 'post',
        url: url,
        responseType: 'blob',
        timeout: 60 * 1000,
        data: params,
        headers: {
          Authorization: proxy.$store.state.token
        }
      })
        .then(res => {
          const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          const reader = new FileReader()
          reader.readAsText(blob, 'utf8')
          reader.addEventListener('loadend', () => {
            try {
              const result = JSON.parse(reader.result)
              this.$message.error(result.userMsg)
              reject(result.userMsg)
            } catch (e) {
              const objectUrl = URL.createObjectURL(blob)
              const link = document.createElement('a')
              link.download = `${fileName}${useTime ? moment().format('YYYYMMDDHHmmss') : ''}.xlsx`
              link.style.display = 'none'
              link.href = objectUrl
              document.body.appendChild(link)
              link.click()
              resolve()
            }
          })
        })
        .catch(e => {
          console.log(e)
          reject(e)
          this.$message.error('下载失败')
        })
    })
  }

  function changeTab () {
    reset()
    getData('search')
  }
  const o = {
    currentTab,
    state,
    getData,
    handleTableChange,
    downloadTemplate,
    getLink,
    save,
    editRef,
    deleteItem,
    toShowLog,
    rowSelection,
    reset,
    changeTab,
    exportList,
    toggleFold,
    onToggleFold
  }
  provide(key, o)
  return o
}
