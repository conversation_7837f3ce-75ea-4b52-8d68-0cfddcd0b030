
export const originColumns = [
  {
    title: '地区',
    dataIndex: 'area',
    width: 150,
    showType: [1, 2]
  },
  {
    title: '车牌号',
    dataIndex: 'plateNum',
    width: 150,
    showType: [3]
  },
  {
    title: '车辆型号',
    dataIndex: 'carModel',
    width: 150,
    showType: [3]
  },
  {
    title: '车架号',
    dataIndex: 'frameNum',
    width: 150,
    showType: [3]
  },
  {
    title: '发动机号',
    dataIndex: 'engineNum',
    width: 150,
    showType: [3]
  },
  {
    title: '车辆类型',
    dataIndex: 'carTypeStr',
    width: 150,
    showType: [3]
  },
  {
    title: '险种',
    dataIndex: 'insuranceTypeStr',
    width: 150
  },
  {
    title: '保险单号/合同批签号',
    dataIndex: 'insurancePolicyNo',
    width: 180
  },
  {
    title: '保险状态',
    dataIndex: 'insuranceStatusStr',
    width: 140,
  },
  {
    title: '被保人',
    dataIndex: 'insuredName',
    width: 140,
    showType: [1, 2]
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
    width: 140,
    showType: [2]
  },
  {
    title: '员工在职状态',
    dataIndex: 'iszaizhi',
    width: 150,
    showType: [2]
  },
  {
    title: '保险标的地址',
    dataIndex: 'insuranceAddress',
    width: 150,
    showType: [1, 2]
  },
  {
    title: '投保人',
    dataIndex: 'applicantName',
    width: 140,
  },
  {
    title: '保险开始时间',
    dataIndex: 'startDate',
    width: 140,
  },
  {
    title: '保险结束时间',
    dataIndex: 'endDate',
    width: 140,
  },
  {
    title: '保费',
    dataIndex: 'insurancePrice',
    showKey: 'price',
    width: 140,
  },
  {
    title: '投保渠道',
    dataIndex: 'insuranceChannel',
    width: 140,
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 200,
    fixed: 'right'
  },
]

export const options = {
  searchType: [
    { label: '车牌号', value: 'plateNum' },
    { label: '车辆型号', value: 'carModel' },
    { label: '车架号', value: 'frameNum' },
    { label: '发动机号', value: 'engineNum' },
    { label: '车辆类型', value: 'carType' },
  ],
  tabs: [
    { label: '公司', value: 1, },
    { label: '个人', value: 2 },
    { label: '车辆', value: 3 },
  ],
  insuranceType: {
    1: [
      { label: '财产基本险', value: 1 },
      { label: '公众责任险', value: 2 },
      { label: '安防联网系统', value: 3 },
    ],
    2: [
      { label: '雇主责任险', value: 4 },
      { label: '团体意外险', value: 5 },
      { label: '非机动车三者责任保险', value: 6 },
    ],
    3: [
      { label: '机动车交通事故强制责任险', value: 7 },
      { label: '车辆损失险', value: 8 },
      { label: '第三者责任险', value: 9 },
      { label: '司机乘客意外伤害险', value: 10 },
    ],
  },
  status: [
    { label: '未生效', value: 0 },
    { label: '保障中', value: 1 },
    { label: '已退保', value: 2 },
  ],
  jobStatus: [
    { label: '在职', value: 1 },
    { label: '离职', value: 0 },
  ],
  carType: [
    { label: '个人', value: 1 },
    { label: '公车', value: 2 },
    { label: '物流车', value: 3 },
  ],
  insuredType: [
    { label: '内部', value: 1 },
    { label: '外部', value: 2 },
  ]
}
export const originSearchForm = {
  areaIds: undefined,
  insuranceType: undefined,
  searchType: 'plateNum',
  keyWord: undefined,
  time: undefined,
  applicantName: undefined,
  applicantId: undefined,
  insuredName: undefined,
  insuranceStatus: undefined,
  insurancePolicyNo: undefined,
  iszaizhi: undefined,
}
export const originEditForm = {
  productId: undefined,
  max: undefined,
  storeLevel: undefined,
}

export const rules = {
  productId: [{ required: true, trigger: ['change', 'blur'], message: '请输入商品id' }],
  max: [{ required: true, trigger: ['change', 'blur'], message: '请输入陈列上限' }],
  storeLevel: [{ required: true, trigger: ['change', 'blur'], message: '请选择门店级别' }],
}

export function numberToChinese (num) {
  const map = { 0: '零', 1: '一', 2: '二', 3: '三', 4: '四', 5: '五', 6: '六', 7: '七', 8: '八', 9: '九' }
  const units = ['', '十', '百', '千', '万']

  let result = ''
  let level = 0

  while (num > 0) {
    const digit = num % 10
    const unit = level > 0 ? units[level % 4] : ''

    result = map[digit] + unit + result
    num = Math.floor(num / 10)
    level++
  }

  return result
}

export const defaultpagination = {
  pageSize: 10,
  current: 1,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ['10', '20', '30', '50']
}

const baseToggleFoldDelParamsKeys = ['insurancePolicyNo', 'applicantId', 'applicantName']
export const toggleFoldDelParamsKeysMap = new Map([
  [1, [...baseToggleFoldDelParamsKeys, 'insuredName']],
  [2, [...baseToggleFoldDelParamsKeys, 'insuredName', 'iszaizhi']],
  [3, baseToggleFoldDelParamsKeys]
])
