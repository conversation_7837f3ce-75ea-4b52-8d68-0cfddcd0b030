<template>
  <div class="inline-block">
    <a-button :type="importBtnType" @click="onClickImportBtn">
      {{ importBtnText }}
    </a-button>

    <a-modal v-model="visible" :maskClosable="false" id="importExcelNewModal" :width="width" :title="title"
             destroyOnClose @cancel="cancel" :footer="null">
      <a-spin :spinning="uploading" size="large" class="spin" tip="正在导入, 请勿刷新或者关闭页面">
        <template v-if="uploadStatus === 1">
          <slot name="otherFormModel"></slot>
          <a-form-model-item label="文件上传" required>
            <a-upload
              name="file"
              accept=".xls,.xlsx"
              :file-list="fileList"
              :before-upload="beforeUpload"
              @change="handleChange"
            >
              <a-button>
                <a-icon type="upload"/>
                {{ chooseBtnText }}
              </a-button>
            </a-upload>
            <div>只支持.xls和.xlsx格式文件，文件小于{{ size }}M
              <a class="blue margin-left pointer" @click="downloadTemplate" :href="temUrl ? temUrl : 'javascript:;'">下载模版</a>
            </div>
          </a-form-model-item>
          <a-form-model-item class="flex flex-justify-center">
            <a-button class="mr-20" @click="cancel">
              取消
            </a-button>
            <a-button type="primary" :loading="uploading" @click="upLoad">
              确认导入
            </a-button>
          </a-form-model-item>
        </template>
        <template v-else-if="uploadStatus === 2">
          <div class="flex flex-col flex-align-center">
            <a-icon type="check-circle" class="font-30 green"/>
            <span class="bold font-16 mt-10 mb-10">导入成功</span>
            <span>共导入{{checkResult[replaceFields.count]}}条数据</span>
            <a-button type="primary" class="mt-16" @click="okImport">好的</a-button>
          </div>
        </template>
        <template v-else-if="uploadStatus === 3">
          <div class="flex flex-align-center bold mb-10">
            <a-icon type="warning" class="red font-24"/>
            <span class="font-16 ml-10">数据校验失败</span>
          </div>
          <div class="mb-10">
            共{{
              checkResult[replaceFields.count] || (checkResult[replaceFields.successCount] || 0 + checkResult[replaceFields.failCount] || 0)
            }}条数据，其中
            <span class="green">{{ checkResult[replaceFields.successCount] }}</span>条已校验成功，
            <span class="red">{{ checkResult[replaceFields.failCount] }}</span>条校验失败<span v-if="showDataSource">，失败数据如下</span>
          </div>
          <a-table
            v-if="showDataSource"
            :columns="tableColumns"
            :rowKey="(record, i) => i"
            :data-source="dataSource"
            :pagination="pagination"
            size="small"
            :scroll="{x: 800 ,y: 400}"
            @change="handleTableChange"
          >
            <template slot="checkReason" slot-scope="text">
              <a-tooltip
                placement="top"
                :getPopupContainer="getPopupContainer"
                :overlayStyle="{width: '400px'}"
              >
                <template slot="title">
                  <div>{{ text }}</div>
                </template>
                <div class="pointer ellipsis">{{ text }}</div>
              </a-tooltip>
            </template>
          </a-table>
          <div :class="[ { 'mt-20': dataSource.length === 0 }]">
            <div class="mb-10">你可以下载校验过的数据表(错误原因在最后一列)，修改后再次导入，
                               也可以在原表格修改后重新导入
            </div>
            <a-button type="primary" @click="downloadCheckFile">下载校验过的数据表</a-button>
<!--            <a-button class="ml-10" @click="reimport">重新导入</a-button>-->
          </div>
        </template>
      </a-spin>
    </a-modal>
  </div>
</template>
<script>

  import { message } from 'ant-design-vue'

  export default {
    name: 'importRule',
    props: {
      // 卡片标题
      title: {
        type: String,
        default: '导入'
      },
      uploadFun: {
        type: Function
      },
      // 触发卡片显示的按钮的文本
      importBtnText: {
        type: String,
        default: '导入'
      },
      importBtnType: {
        type: String,
        default: 'primary'
      },
      // 选择上传文件按钮的文本
      chooseBtnText: {
        type: String,
        default: '选择文件'
      },
      // 文件大小限制
      size: {
        type: Number,
        default: 50
      },
      // 表格的行数
      rows: {
        type: Number,
        default: 1000
      },
      // 表格的列
      columns: {
        type: Array,
        default: () => []
      },
      // 校验表格的API eg: smallCheck.checkExcl
      checkApi: {
        type: String,
        default: ''
      },
      // 模版url
      temUrl: {
        type: String,
        default: ''
      },
      // 失败原因表格列宽
      checkReasonWidth: {
        type: String,
        default: '200px'
      },
      replaceFields: { // 替换 checkResult 字段
        type: Object,
        default: () => {
          return {
            count: 'count',
            successCount: 'successCount',
            successList: 'successList',
            failList: 'failList',
            failCount: 'failCount',
            checkReason: 'checkReason',
            rowNum: 'rowNum'
          }
        }
      },
      checkExtraParams: { // 校验接口的额外参数
        type: Object,
        default: () => {}
      },
      showDataSource: {
        type: Boolean
      }
    },
    data () {
      return {
        visible: false,
        width: 590,
        uploadStatus: 1, // 1-上传 2-成功 3-失败
        isUpload: false,
        uploading: false,
        fileList: [],
        tableColumns: [],
        checkResult: {
          count: 0,
          successCount: 0,
          successList: [],
          failList: [],
          failCount: 0,
          fileUrl: undefined
        },
        dataSource: [],
        pagination: {
          total: 0,
          showTotal: () => `共 ${this.pagination.total} 条`,
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['5', '10', '20', '50', '100'],
          showSizeChanger: true,
          showQuickJumper: true
        },
        successVisible: false
      }
    },
    watch: {
      columns: {
        handler (newValue) {
          this.tableColumns = newValue
        },
        deep: true,
        immediate: true
      },
    },
    methods: {
      downloadTemplate () {
        this.$emit('downloadTemplate')
      },
      getPopupContainer () {
        return document.querySelector('#importExcelNewModal')
      },
      onClickImportBtn () {
        this.$emit('clickImportBtn')
        this.visible = true
        this.uploadStatus = 1
      },
      downloadCheckFile () {
        let a = document.createElement('a')
        a.href = this.checkResult.fileUrl
        a.click()
      },
      // 上传前的前端校验
      beforeUpload (file) {
        const isXlsOrXlsx = file.name.includes('.xls') || file.name.includes('.xlsx')
        if (!isXlsOrXlsx) {
          this.$message.error('只支持.xls和.xlsx格式文件!')
          this.$nextTick(() => {
            this.fileList = []
          })
        }
        const isLt2M = file.size / 1024 / 1024 < this.size
        if (!isLt2M) {
          this.$message.error(`文件必须小于${this.size}M!`)
          this.$nextTick(() => {
            this.fileList = []
          })
        }
        return false
      },
      // 文件列表变化时触发
      handleChange (files) {
        this.fileList = files?.fileList?.length ? [files.file] : []
      },
      // 点击取消按钮
      cancel () {
        this.uploadStatus = 1
        this.fileList = []
        this.isUpload = false
        this.visible = false
        this.successVisible = false
      },
      okImport () {
        this.$emit('okImport')
        this.cancel()
      },
      reimport () {
        this.fileList = []
        this.isUpload = false
      },
      // 上传规则文件
      upLoad () {
        const { fileList } = this
        if (fileList.length === 0) {
          return this.$message.error('请先选择需导入的文件！')
        }
        const formData = new FormData()
        fileList.forEach(file => {
          formData.append('file', file)
        })
        this.formData = formData
        this.uploading = true
        this.uploadFun(formData, this.checkExtraParams).then((res) => {
          if ([0, 5000].includes(res.code)) {
            Object.assign(this.checkResult, res.data)
            if (res.code === 0) {
              this.$emit('okImport')
              this.uploadStatus = 2
            } else if (res.code === 5000) {
              this.uploadStatus = 3
              this.isUpload = true
              this.checkResult.fileUrl = res.data.link
              if (this.showDataSource) {
                this.dataSource = res.data[this.replaceFields.failList]
                this.pagination.total = res.data[this.replaceFields.failCount]
                this.isUpload = true
                this.width = 1068
                this.handleColums()
              }
            }
          } else {
            this.$message.error('校验失败, ' + res.userMsg)
          }
          this.uploading = false
        }).catch((err) => {
          console.error('校验规则模板：', err)
          this.uploading = false
          this.$message.error('校验模板出错')
        })
      },
      handleColums () {
        let page = {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['5', '10', '20', '50', '100'],
          showSizeChanger: true,
          showQuickJumper: true
        }
        if (this.tableColumns[this.tableColumns.length - 1].dataIndex !== this.replaceFields.checkReason) {
          this.tableColumns.push({
            width: this.checkReasonWidth,
            title: '校验不通过原因',
            dataIndex: this.replaceFields.checkReason,
            ellipsis: true,
            fixed: 'right',
            scopedSlots: { customRender: 'checkReason' }
          })
        }
        if (this.tableColumns[0].dataIndex !== this.replaceFields.rowNum) {
          this.tableColumns.unshift({
            width: 100,
            title: '行号',
            dataIndex: this.replaceFields.rowNum,
          })
        }
        this.pagination = {
          ...page,
          total: this.checkResult[this.replaceFields.failCount] || 0,
          showTotal: () => `共 ${this.checkResult[this.replaceFields.failCount] || 0} 条`
        }
      },
      // 用户改变分页展示条数
      handleTableChange (page) {
        this.pagination = page
      },
    },
  }
</script>
<style lang="less" scoped>
  :deep(.ant-tooltip-placement-top) {
    white-space: normal;
  }
</style>
