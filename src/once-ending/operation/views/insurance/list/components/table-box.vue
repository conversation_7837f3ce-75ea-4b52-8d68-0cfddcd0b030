<script lang="jsx">
  import { defineComponent, toRefs } from 'vue'
  import { NiTable, NiLog, NiPrice } from '@jiuji/nine-ui'
  import { options, originColumns, rules } from '../constants'
  import { useState } from '../hooks/use-item-state'
  import insuranceApi from '@/operation/api/insurance'
  import ImportExcelNew from './import-excel-new.vue'
  export default defineComponent({
    name: 'table-box',
    components: {
      NiTable,
      NiLog,
      NiPrice,
      ImportExcelNew
    },
    setup () {
      const {
        state,
        getData,
        handleTableChange,
        downloadTemplate,
        getLink,
        save,
        editRef,
        deleteItem,
        toShowLog,
        rowSelection,
        exportList,
        currentTab
      } = useState()
      return {
        ...toRefs(state),
        getData,
        handleTableChange,
        downloadTemplate,
        getLink,
        save,
        editRef,
        deleteItem,
        toShowLog,
        rowSelection,
        exportList,
        currentTab
      }
    },
    data () {
      return {
        customRenderMap: new Map([
          [
            'text',
            (text) => <span>{ text || text === 0 ? text : '--' }</span>
          ],
          [
            'price',
            text => text ? <NiPrice
              color="rgba(0, 0, 0, 0.65)"
              decimalColor="rgba(0, 0, 0, 0.65)"
              prefixColor="rgba(0, 0, 0, 0.65)"
              value={text}/> : '--'
          ],
          [
            'action',
            (text, record) => <span>
              <router-link to={this.getLink(record.id, 'watch')} target='_blank'>
                查看
              </router-link>
               <router-link to={this.getLink(record.id, 'edit')} target='_blank' class="ml-16">
                 编辑
               </router-link>
              <a-button type="link" onClick={() => this.deleteItem(record.id)}>删除</a-button>
            </span>
          ]
        ])
      }
    },
    computed: {
      columns () {
        return originColumns.filter(item => {
          return item.showType ? item.showType.includes(this.currentTab) : true
        }).map(item => {
          const cacheItem = { ...item }
          cacheItem.align = 'center'
          cacheItem.customRender = this.customRenderMap.get(item.showKey) || this.customRenderMap.get(item.dataIndex) || this.customRenderMap.get('text')
          return cacheItem
        })
      }
    },
    render (createElement, context) {
      const {
        loading,
        dataSource,
        pagination,
        columns,
        handleTableChange,
        downloadTemplate,
        rowSelection,
        deleteItem,
        getLink,
        uploadLoading,
        currentTab,
        exportLoading,
        exportList,
        getData
      } = this
      return <div>
        <ni-table
          rowKey={ (r, i) => r.id }
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={pagination}
          bordered
          rowSelection={rowSelection}
          align={'center'}
          onChange={handleTableChange}>
          <div slot="tool" class="flex flex-justify-start">
            <a-button type="primary" onClick={() => deleteItem(null)}>批量删除</a-button>
          </div>
          <span slot="action">
            <router-link to={getLink(null, 'add')} target='_blank'>
              <a-button type="primary">新增保险记录</a-button>
            </router-link>
            <ImportExcelNew
              onDownloadTemplate={downloadTemplate}
              importBtnText="批量导入"
              replaceFields={{
                count: 'total',
                successCount: 'success',
                failCount: 'fail'
              }}
              onOkImport={() => getData('search')}
              checkExtraParams={{ type: currentTab }}
              uploadFun={insuranceApi.toUpload}
              class="ml-16"/>
            <a-button onClick={() => exportList()} icon="export" loading={exportLoading} class="ml-16" >导出</a-button>
          </span>
        </ni-table>
      </div>
    }
  })
</script>

<style scoped lang="scss">
:deep(.ant-table-fixed-left .ant-table-body-inner) {
  margin-right: 0;
}
.menus-content {
  p {
    color: #1890ff;
    padding: 2px 0;
    text-align: center;
    cursor: pointer;
    width: 100%;
  }
}
</style>
