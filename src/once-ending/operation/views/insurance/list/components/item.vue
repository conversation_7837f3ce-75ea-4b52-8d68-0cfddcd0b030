<template>
  <ni-list-page :pushFilterToLocation="false" class="mt-16">
    <filter-box></filter-box>
    <table-box class="mt-16"></table-box>
  </ni-list-page>
</template>

<script setup>
  import { defineProps } from 'vue'
  import FilterBox from './filter-box.vue'
  import TableBox from './table-box.vue'
  import { NiListPage } from '@jiuji/nine-ui'
  import { createState } from '../hooks/use-item-state'

  const props = defineProps({
    type: {
      type: [String, Number],
      default: ''
    }
  })
  createState(props.type)

</script>

<style scoped lang="scss">

</style>
