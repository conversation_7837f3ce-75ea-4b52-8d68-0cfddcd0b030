<script lang="jsx">
  import { defineComponent, toRefs } from 'vue'
  import { NiFilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import { options } from '../constants'
  import { useState } from '../hooks/useState'
  import { useState as useItemState } from '../hooks/use-item-state'

  export default defineComponent({
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect
    },
    setup (props) {
      const { companyTaxList } = useState()
      const {
        state,
        getData,
        reset,
        currentTab,
        onToggleFold
      } = useItemState()
      return {
        ...toRefs(state),
        getData,
        reset,
        currentTab,
        companyTaxList,
        onToggleFold
      }
    },
    render () {
      const {
        searchForm,
        loading,
        getData,
        reset,
        currentTab,
        companyTaxList,
        onToggleFold
      } = this
      return (
        <ni-filter
          form={searchForm}
          onReset={reset}
          immediate={true}
          onFilter={() => getData('search')}
          label-width={100}
          onToggleFold={onToggleFold}
          loading={loading}>
          { [1, 2].includes(currentTab) ? <ni-filter-item label="地区">
            <NiAreaSelect
              placeholder="请选择"
              multiple={true}
              maxTagCount={1}
              allowClear
              class="area-select"
              v-model={searchForm.areaIds}
            />
          </ni-filter-item> : null }
          { [3].includes(currentTab) ? <ni-filter-item>
            <a-input-group compact style="display: flex">
              <a-select
                options={options.searchType}
                onChange={() => { searchForm.keyWord = undefined }}
                v-model={searchForm.searchType}
                class="flex flex-child-noshrink"
                style="width: 100px"
              />
              { searchForm.searchType === 'carType' ? <a-select
                allowClear
                v-model={searchForm.keyWord}
                placeholder="请选择"
                class="flex flex-child-noshrink"
                style="width: calc(100% - 100px);min-width: 198px"
                options={options.carType}
                /> : <a-input
                allowClear
                placeholder="请输入"
                v-model={searchForm.keyWord}
                maxLength={500}
                class="flex flex-child-noshrink"
                style="width: calc(100% - 100px)"
              />
              }
            </a-input-group>
          </ni-filter-item> : null }
          <ni-filter-item label="险种">
            <a-select
              allowClear
              placeholder="请选择"
              v-model={searchForm.insuranceType}
              options={options.insuranceType[currentTab]}
            />
          </ni-filter-item>
          <ni-filter-item label="保险状态">
            <a-select
              allowClear
              placeholder="请选择"
              v-model={searchForm.insuranceStatus}
              options={options.status}
            />
          </ni-filter-item>
          <ni-filter-item label="保险结束时间">
            <a-range-picker
              allowClear
              placeholder={['开始时间', '结束时间']}
              valueFormat="YYYY-MM-DD"
              v-model={searchForm.time}
            />
          </ni-filter-item>
          <ni-filter-item label="保单/批签号">
            <a-input
              allowClear
              maxLength={50}
              placeholder="请输入"
              v-model={searchForm.insurancePolicyNo}
            />
          </ni-filter-item>
          { [1, 2].includes(currentTab) ? <ni-filter-item label="被保人">
            <a-input
              allowClear
              maxLength={50}
              placeholder="请输入"
              v-model={searchForm.insuredName}
            />
          </ni-filter-item> : null }
          <ni-filter-item label="投保人">
            { [2, 3].includes(currentTab) ? <a-select
              v-model={searchForm.applicantId}
              options={companyTaxList}
              allowClear
              placeholder="请选择"
              class="full-width"/> : <a-input
              allowClear
              maxLength={50}
              placeholder="请输入"
              v-model={searchForm.applicantName}
            />}
          </ni-filter-item>
          { [2].includes(currentTab) ? <ni-filter-item label="员工在职状态">
            <a-select
              allowClear
              placeholder="请选择"
              v-model={searchForm.iszaizhi}
              options={options.jobStatus}
            />
          </ni-filter-item> : null }
        </ni-filter>
      )
    }
  })
</script>

<style lang="scss" scoped>
:deep(.area-select) {
  .ant-select-selection__choice {
    .ant-select-selection__choice__content {
      max-width: 100px;
    }
  }
}
</style>
