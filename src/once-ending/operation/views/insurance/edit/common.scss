.content-box {
  background: #fff;
  padding: 16px 24px 0 24px;
  box-sizing: border-box;
  border-radius: 8px;
  margin-bottom: 24px;
  .title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 16px;
    position: relative;
    &:before {
      position: absolute;
      left: 0;
      bottom: -4px;
      content: "";
      display: inline-block;
      width: 100%;
      height: 6px;
      background: linear-gradient(90deg, #1890FF 0%, rgba(24, 144, 255, 0) 100%);
    }
  }
}
:deep(.ant-form-item) {
  display: flex;
  width: calc((100% - 180px) / 3);
  margin-right: 60px;
  .ant-form-item-control-wrapper {
    flex-grow: 1;
  }
}
.mt-24 {
  margin-top: 24px;
}
.pb-24 {
  padding-bottom: 24px;
}
.overflow {
  overflow: hidden;
}
