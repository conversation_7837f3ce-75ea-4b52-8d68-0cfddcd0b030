import { reactive, provide, inject, ref, getCurrentInstance, computed } from 'vue'
import { cloneDeep } from 'lodash'
import { originForm, insuranceInfoForm, userForm, listNeedKeys, userInfoNeedKeys } from '../constants'
import insuranceApi from '@/operation/api/insurance'
import { to } from '~/util/common'

const key = Symbol('key')

export function useState () {
  return inject(key)
}

export function createState () {
  const { proxy } = getCurrentInstance()
  const state = reactive({
    form: cloneDeep(originForm),
    isAdd: false,
    companyTaxList: [],
    saveLoading: false
  })

  const { query: { type }, params: { id } } = proxy.$route
  type && (state.form.type = +type)
  if (id === '0') {
    state.isAdd = true
  } else {
    state.form.id = id
    getDetail()
  }
  type === '3' && state.isAdd && (state.form.insuranceAddress = '云南省昆明市五华区学府路690号金鼎科技园三号标准厂房五楼502号')

  async function getDetail () {
    proxy.$indicator.open()
    const [err, res] = await to(insuranceApi.getDetail(state.form.id))
    proxy.$indicator.close()
    if (err) throw err
    if (res?.code === 0) {
      const { list, ...other } = originForm
      Object.keys({ ...other }).forEach(key => {
        state.form[key] = res.data[key]
      })
      Object.keys(insuranceInfoForm).forEach(key => {
        state.form.list[0][key] = res.data[key]
      })
      const { insuredId, insuredName, type, applicantId } = res.data
      insuredId && (state.form.insuredId = +insuredId)
      applicantId && (state.form.applicantId = +applicantId)
      if (type === 3) {
        state.form.insuredInfo = {
          key: insuredId,
          label: insuredName
        }
      }
    } else {
      proxy.$message.error(res.userMsg)
    }
  }

  const formRef = ref()

  async function toShowLog (id) {
    state.showLog = true
  }

  function changeArray (type, key, index) {
    const needForm = cloneDeep(key === 'list' ? insuranceInfoForm : userForm)
    if (type === 'add') {
      state.form[key].push(needForm)
    } else {
      state.form[key].splice(index, 1)
    }
  }

  async function getCompanyTaxList () {
    const [err, res] = await to(insuranceApi.getCompanyTaxList())
    if (err) throw err
    if (res?.code === 0) {
      state.companyTaxList = res?.data?.map(it => ({
        ...it,
        label: it.name,
        value: it.id,
      })) || []
    }
  }
  getCompanyTaxList()

  async function changeArea (id, name, node) {
    if (!node) {
      state.form.insuranceAddress = undefined
      return
    }
    const [err, res] = await to(insuranceApi.getAreaAddress(node?.area))
    if (err) throw err
    if (res?.code === 0) {
      state.form.insuranceAddress = res.data.address
      formRef.value.clearValidate('insuranceAddress')
    }
  }

  function goRef (id) {
    proxy.$message.warning('请先完善数据')
    const box = document.getElementById(id)
    box && (box.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    }))
  }

  function checkNoPass (formItem, keys) {
    return keys.find(key => Array.isArray(formItem[key]) ? !formItem[key].length || formItem[key]?.find(item => {
      return checkNoPass(item, Object.keys(item))
    }) : (!formItem[key] && formItem[key] !== 0))
  }

  function getName (array) {
    const { companyTaxList } = state
    return Array.isArray(array) ? array.map(item => ({
      ...item,
      name: (item.type === 1 ? companyTaxList?.find(it => it.value === item.id)?.label : item.name) || undefined
    })) : []
  }

  const InsuranceInfoRef = ref()

  function save () {
    const { form, isAdd } = state
    const { insuredInfo, type, list, id } = form
    formRef.value.validate(async valid => {
      if (valid) {
        const index = InsuranceInfoRef.value.checkFrameNum()
        if (index > -1) {
          goRef('listId' + index)
          return
        }
        const params = {
          ...form
        }
        if (type === 3) {
          params.insuredId = insuredInfo?.key
          params.insuredName = insuredInfo?.label
        }
        if (!isAdd) {
          Object.assign(params, list[0])
          delete params.list
        }
        if (type === 1) {
          params.insuredList = getName(params.insuredList)
          params.applicantList = getName(params.applicantList)
        }
        state.saveLoading = true
        const [err, res] = await to(insuranceApi.save(params, isAdd))
        state.saveLoading = false
        if (err) throw err
        if (res?.code === 0) {
          proxy.$message.success('保存成功')
          if (isAdd) {
            goList()
          } else {
            proxy.$router.push(`/operation/insurance/detail/${id}`)
          }
        } else {
          proxy.$message.error(res.userMsg)
        }
      } else {
        let checkId = 'userInfoId'
        const { type } = form
        const index = list.findIndex(it => {
          return checkNoPass(it, listNeedKeys(type, it.insuranceType))
        })
        if (checkNoPass(form, userInfoNeedKeys(type))) {
          goRef(checkId)
        } else if (index > -1) {
          checkId = 'listId' + index
          goRef(checkId)
        }
      }
    })
  }

  function goList () {
    const { query } = proxy.$route
    const url = `/operation/insurance/list`
    proxy.$router.push({
      path: url,
      query: { ...query }
    })
  }

  function changeApplicantId (value) {
    const item = state.companyTaxList.find(it => it.value === value) || {}
    state.form.applicantName = item.label
    state.form.insuranceAddress = item.address || undefined
    item.address && (formRef.value.clearValidate('insuranceAddress'))
  }

  const formatter = value => value.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  const parser = value => value.replace(/\$\s?|(,*)/g, '')
  const o = {
    state,
    formRef,
    changeArray,
    changeArea,
    formatter,
    parser,
    changeApplicantId,
    save,
    goList,
    InsuranceInfoRef
  }
  provide(key, o)
  return o
}
