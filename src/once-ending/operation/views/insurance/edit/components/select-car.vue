<script lang="jsx">
  import { defineComponent, ref, watch, getCurrentInstance, reactive } from 'vue'
  import { debounce } from 'lodash'
  import { to } from '~/util/common'
  import vehicleBorrowApi from '@office/api/vehicle-borrow'

  export default defineComponent({
    props: {
      value: {
        type: [Number, String, Object]
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const valueLocal = ref(undefined)
      const fetching = ref(false)
      const options = ref([])
      const keyWord = ref('')
      const pagination = reactive({
        current: 1,
        size: 10,
        total: 0
      })

      watch(() => props.value, (val) => {
        valueLocal.value = val
      }, { immediate: true })

      function handleChange (val) {
        valueLocal.value = val
        proxy.$emit('input', valueLocal.value)
        proxy.$emit('change', valueLocal.value)
      }

      let fetchData = async (val, type) => {
        if (type === 'more') {
          pagination.current += 1
        } else {
          options.value = []
          pagination.current = 1
          keyWord.value = val || ''
        }
        const { current, size } = pagination
        const params = {
          current,
          size,
          plateNum: keyWord.value,
          startTime: '',
          endTime: ''
        }
        fetching.value = true
        const [err, res] = await to(vehicleBorrowApi.companyCarList(params))
        fetching.value = false
        if (err) throw err
        const { code, data, userMsg } = res
        if (code === 0) {
          const { total, records } = data
          pagination.total = total || 0
          options.value = options.value.concat(records?.map(it => ({
            label: it.plateNum,
            value: it.id
          })))
        } else {
          proxy.$message.error(userMsg)
        }
      }
      fetchData = debounce(fetchData, 200)

      // 每次下拉框滚动条滚到底部,加载缓存数据
      const selectPopupScroll = (e) => {
        if (!options.value.length || fetching.value || options.value.length >= pagination.total) return
        const { target } = e
        const scrollHeight = target.scrollHeight - target.scrollTop
        const clientHeight = target.clientHeight
        if (scrollHeight < clientHeight + 5) {
          fetchData(null, 'more')
        }
      }

      return {
        valueLocal,
        fetching,
        handleChange,
        fetchData,
        options,
        selectPopupScroll
      }
    },
    render () {
      const {
        valueLocal,
        fetching,
        fetchData,
        handleChange,
        options,
        selectPopupScroll
      } = this
      return <a-select
        label-in-value
        value={valueLocal}
        placeholder="输入车牌号搜索车辆"
        style="width: 100%"
        filter-option="false"
        showSearch
        allowClear
        {...{
          props: this.$attrs,
          on: this.$listeners
        }}
        options={options}
        not-found-content={fetching ? undefined : null}
        onSearch={fetchData}
        onPopupScroll={selectPopupScroll}
        onChange={handleChange}>
        <a-spin v-if="fetching" slot="notFoundContent" size="small" />
      </a-select>
    }
  })
</script>

<style scoped lang="scss">

</style>
