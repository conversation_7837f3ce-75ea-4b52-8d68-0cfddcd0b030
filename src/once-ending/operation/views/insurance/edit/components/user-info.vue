<script lang="jsx">
  import { defineComponent, toRefs, reactive } from 'vue'
  import { options } from '../../list/constants'
  import { useState } from '../hooks/useState'
  import SelectCar from './select-car.vue'
  import { NiAreaSelect, NiStaffSelect } from '@jiuji/nine-ui'
  export default defineComponent({
    name: 'UserInfo',
    components: { SelectCar, NiAreaSelect, NiStaffSelect },
    props: {
      title: {
        type: String,
      }
    },
    setup (props) {
      const { state, changeArray, changeArea, changeApplicantId } = useState()
      const errorIndex = reactive({
        applicantList: undefined,
        insuredList: undefined,
      })
      const specialRules = (key) => {
        return [{
          required: true,
          trigger: ['blur', 'change'],
          validator: (rule, value, callback) => {
            const label = (key === 'applicantList' ? '投保人' : '被保人')
            const index = state.form[key]?.findIndex(item => {
              if (!item.type || (item.type === 1 && !item.id && item.id !== 0) || (item.type === 2 && !item.name)) {
                return true
              }
            })
            if (index > -1) {
              errorIndex[key] = index + 1
              return callback(new Error(`请完善${label}第${index + 1}条信息`))
            }
            errorIndex[key] = undefined
            return callback()
          }
        }]
      }

      const carTypeChange = function (val) {
        // 车辆类型变更且变更前后不是公车或者物流车就清空投保人
        if (state.form.carType !== val && (![2, 3].includes(state.form.carType) || ![2, 3].includes(val))) {
          state.form.applicantName = undefined
          state.form.applicantId = undefined
        }
        state.form.carType = val
      }
      return {
        ...toRefs(state),
        changeArray,
        changeArea,
        specialRules,
        errorIndex,
        changeApplicantId,
        carTypeChange
      }
    },
    render () {
      const {
        title,
        form,
        changeArray,
        isAdd,
        changeArea,
        specialRules,
        errorIndex,
        companyTaxList,
        changeApplicantId,
        carTypeChange
      } = this
      const selectPeople = (item, key, index) => <div class="flex flex-align-center nowrap" style={{ marginTop: `${index === 0 ? 0 : 8}px` }}>
        <div class="flex flex-child-noshrink flex-align-center full-width">
          <a-select
            options={options.insuredType}
            v-model={item.type}
            class="no-error flex-child-noshrink"
            onBlur={() => { this.$refs[key + 'Ref'].onFieldBlur() }}
            onChange={() => { this.$refs[key + 'Ref'].onFieldChange() }}
            style={{ width: '80px' }}/>
          <div class="flex-child-noshrink" style={{ width: 'calc(100% - 88px)', marginLeft: '8px' }}>
            { item.type === 1 ? <a-select
              v-model={item.id}
              allowClear
              placeholder={'请选择' + (key === 'insuredList' ? '被保人' : '投保人')}
              options={companyTaxList}
              onBlur={() => { this.$refs[key + 'Ref'].onFieldBlur() }}
              onChange={() => { this.$refs[key + 'Ref'].onFieldChange() }}
              class={['full-width', errorIndex[key] === (index + 1) ? '' : 'no-error']}/> : <a-input
              v-model={item.name}
              allowClear
              placeholder={'请输入' + (key === 'insuredList' ? '被保人' : '投保人')}
              onBlur={() => { this.$refs[key + 'Ref'].onFieldBlur() }}
              onChange={() => { this.$refs[key + 'Ref'].onFieldChange() }}
              class={['full-width', errorIndex[key] === (index + 1) ? '' : 'no-error']}/> }
          </div>
          <span class="ml-16 pointer" style="color: #1890ff" onClick={() => changeArray('add', key)}>添加</span>
          { index !== 0 ? <span class="ml-16 pointer" style="color: #ff4d4f" onClick={() => changeArray('delete', key, index)}>删除</span> : null }
        </div>
      </div>
      return <div class="content-box">
        <span class="title">{ title }</span>
        <div class="flex flex-wrap mt-16">
          <a-form-model-item label="保险对象" prop="type">
            <a-select
              disabled
              v-model={form.type}
              options={options.tabs}
              class="full-width"/>
          </a-form-model-item>
          { form.type === 1 ? <a-form-model-item label="门店代码" prop="areaId">
            <NiAreaSelect
              class="full-width"
              allowClear
              placeholder="请选择门店"
              showCheckAll={false}
              showSearch={true}
              onChange={changeArea}
              v-model={form.areaId}/>
          </a-form-model-item> : null }
          { form.type === 2 ? <a-form-model-item label="被保人" prop="insuredId">
            <NiStaffSelect
              placeholder="输入员工工号或姓名"
              multiple={false}
              onChange={(id, node,) => { form.insuredName = node?.staffName || undefined }}
              v-model={form.insuredId}
              class="full-width"/>
          </a-form-model-item> : null }
          { form.type === 3 ? <a-form-model-item
            label="被保车辆"
            prop="insuredInfo">
            <SelectCar v-model={form.insuredInfo}/>
          </a-form-model-item> : null }
          { form.type === 3 ? <a-form-model-item label="车辆类型" prop="carType">
            <a-select
              allowClear
              options={options.carType}
              placeholder="请选择"
              value={form.carType}
              onChange={carTypeChange}
              class="full-width"/>
          </a-form-model-item> : null }
          { [2, 3].includes(form.type) ? <a-form-model-item label="投保人" prop="applicantId">
            {
              form.type === 3 && form.carType === 1 ? <NiStaffSelect
                placeholder="输入员工工号或姓名"
                multiple={false}
                onChange={(id, node) => { form.applicantName = node?.staffName || undefined }}
                v-model={form.applicantId}
                class="full-width"/> : <a-select
                onChange={changeApplicantId}
                v-model={form.applicantId}
                options={companyTaxList}
                placeholder="请选择"
                class="full-width"/>
            }
          </a-form-model-item> : null }
          <a-form-model-item label="保险标的地址" prop="insuranceAddress">
            <a-input
              allowClear
              v-model={form.insuranceAddress}
              placeholder="请输入地址"
              class="full-width"/>
          </a-form-model-item>
          { form.type === 1 ? <div class="full-width overflow">
            <a-form-model-item
              label="被保人"
              prop="insuredList"
              autoLink={false}
              ref="insuredListRef"
              rules={specialRules('insuredList')}>
              { form.insuredList?.map((item, index) => selectPeople(item, 'insuredList', index)) }
            </a-form-model-item>
            <a-form-model-item
              label="投保人"
              prop="applicantId"
              autoLink={false}
              ref="applicantListRef"
              rules={specialRules('applicantList')}>
              { form.applicantList?.map((item, index) => selectPeople(item, 'applicantList', index)) }
            </a-form-model-item>
          </div> : null }
        </div>
      </div>
    }
  })
</script>

<style scoped lang="scss">
@import "../common";
:deep(.no-error) {
  .ant-select-selection {
    border-color: #d9d9d9;
    .ant-select-arrow {
      color: rgba(0, 0, 0, 0.25)
    }
  }
}
</style>
