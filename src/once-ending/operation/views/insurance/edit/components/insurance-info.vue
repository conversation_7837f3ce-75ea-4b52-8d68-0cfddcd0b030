<script lang="jsx">
  import { defineComponent, toRefs, getCurrentInstance } from 'vue'
  import { options, numberToChinese } from '../../list/constants'
  import { useState } from '../hooks/useState'
  import { rules } from '../constants'
  import Uploader from '@/operation/views/complaint/detail/components/uploader.vue'
  import moment from 'moment'
  export default defineComponent({
    name: 'InsuranceInfo',
    components: { Uploader },
    props: {
      title: {
        type: String,
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const { state, changeArray, formatter, parser, formRef, InsuranceInfoRef } = useState()
      const specialRules = (key, item) => {
        return [{
          required: true,
          trigger: ['blur', 'change'],
          validator: (rule, value, callback) => {
            if ((!item[key] && item[key] !== 0) || (Array.isArray(item[key]) && !item[key]?.length)) {
              return callback(new Error(rules[key][0]?.message))
            }
            if (['startDate', 'endDate'].includes(key)) {
              const { startDate, endDate } = item
              if (startDate > endDate) {
                return callback(new Error(key === 'startDate' ? '保险开始日期不能大于结束日期' : '保险结束日期不能小于于开始日期'))
              }
            }
            return callback()
          }
        }]
      }

      function checkFrameNum () {
        return state.form.list.findIndex((item, index) => {
          proxy.$refs['frameNumRef' + index]?.onFieldBlur()
          if (!item.frameNum && state.form.type === 2 && item.insuranceType === 6) {
            return true
          }
        })
      }

      function blur (ref) {
        proxy.$refs[ref]?.onFieldBlur()
      }
      function change (ref, item, key, index) {
        proxy.$refs[ref]?.onFieldChange()
        if (['startDate', 'endDate'].includes(key)) {
          const { startDate, endDate } = item
          if (startDate && endDate) {
            const otherRef = `${key === 'startDate' ? 'endDate' : 'startDate'}Ref${index}`
            proxy.$refs[otherRef]?.onFieldChange()
            if (endDate >= startDate) {
              const date = moment().format('YYYY-MM-DD')
              item.insuranceStatus = date < startDate ? 0 : date > endDate ? 2 : 1
            }
          }
        } else if (['insuranceType'].includes(key)) {
          const props = [
            'plateNumRef' + index,
            'frameNumRef' + index,
            'brandRef' + index,
            'insuranceProject' + index,
            'assetsAmount' + index,
            'stockAmount' + index,
          ]
          formRef.value?.clearValidate(props)
        }
      }

      function upLoaderChange (files, item, ref) {
        item.attachmentsList = files
        console.log(666, item.attachmentsList)
        change(ref)
      }
      return {
        ...toRefs(state),
        changeArray,
        upLoaderChange,
        formatter,
        parser,
        specialRules,
        blur,
        change,
        checkFrameNum
      }
    },
    render () {
      const {
        title,
        form,
        changeArray,
        isAdd,
        upLoaderChange,
        formatter,
        parser,
        specialRules,
        blur,
        change
      } = this
      const isPeople = Boolean(form.type === 2)
      return <div class="content-box pb-24">
        <span class="title">{ title }</span>
        { form.list?.map((item, index) => <div class="flex flex-wrap mt-16" id={'listId' + index}>
          { form.list?.length > 1 ? <div class="full-width">
            <a-form-model-item label={'保险' + numberToChinese(index + 1)} style="margin-bottom: 4px" class="no-colon"/>
          </div> : null }
          <a-form-model-item
            label="险种"
            prop="insuranceType"
            autoLink={false}
            ref={'insuranceTypeRef' + index}
            rules={specialRules('insuranceType', item)}>
            <a-select
              allowClear
              onBlur={() => blur('insuranceTypeRef' + index)}
              onChange={() => change('insuranceTypeRef' + index, item, 'insuranceType', index)}
              placeholder="请选择"
              v-model={item.insuranceType}
              options={options.insuranceType[form.type]}
            />
          </a-form-model-item>
          <a-form-model-item
            label="保险单号/合同批签号"
            autoLink={false}
            ref={'insurancePolicyNoRef' + index}
            rules={specialRules('insurancePolicyNo', item)}
            prop="insurancePolicyNo">
            <a-input
              min={0}
              placeholder="请输入"
              onBlur={() => blur('insurancePolicyNoRef' + index)}
              onChange={() => change('insurancePolicyNoRef' + index)}
              v-model={item.insurancePolicyNo}
              class="full-width"/>
          </a-form-model-item>
          <a-form-model-item
            autoLink={false}
            ref={'insuranceStatusRef' + index}
            rules={specialRules('insuranceStatus', item)}
            prop="insuranceStatus">
            <span slot="label">
              保险状态
              <a-tooltip style="margin-left: 4px" overlayStyle={{ maxWidth: '400px' }} trigger="click">
                 <span slot="title">
                   根据选择的保险开始/结束日期自动匹配保险状态
                 </span>
                <a-icon type="question-circle" class="blue" />
              </a-tooltip>
            </span>
            <a-select
              allowClear
              disabled={true}
              placeholder="请先选择保险开始/结束日期"
              onBlur={() => blur('insuranceStatusRef' + index)}
              onChange={() => change('insuranceStatusRef' + index)}
              v-model={item.insuranceStatus}
              options={options.status}
              class="full-width"/>
          </a-form-model-item>
          <a-form-model-item
            label="保费"
            autoLink={false}
            ref={'insurancePriceRef' + index}
            rules={specialRules('insurancePrice', item)}
            prop="insurancePrice">
            <a-input-number
              allowClear
              v-model={item.insurancePrice}
              class='full-width'
              onBlur={() => blur('insurancePriceRef' + index)}
              onChange={() => change('insurancePriceRef' + index)}
              placeholder="请输入"
              min={0}
              max={99999999}
              precision={2}
              formatter={formatter}
              parser={parser}/>
          </a-form-model-item>
          <a-form-model-item
            label="保险开始日期"
            autoLink={false}
            ref={'startDateRef' + index}
            rules={specialRules('startDate', item)}
            prop="startDate">
            <a-date-picker
              placeholder="请选择"
              v-model={item.startDate}
              onBlur={() => blur('startDateRef' + index)}
              onChange={() => change('startDateRef' + index, item, 'startDate', index)}
              allowClear
              valueFormat="YYYY-MM-DD"
              class="full-width"/>
          </a-form-model-item>
          <a-form-model-item
            label="保险结束日期"
            autoLink={false}
            ref={'endDateRef' + index}
            rules={specialRules('endDate', item)}
            prop="endDate">
            <a-date-picker
              placeholder="请选择"
              v-model={item.endDate}
              onBlur={() => blur('endDateRef' + index)}
              onChange={() => change('endDateRef' + index, item, 'endDate', index)}
              allowClear
              valueFormat="YYYY-MM-DD"
              class="full-width"/>
          </a-form-model-item>
          { isPeople && item.insuranceType === 6 ? <a-form-model-item
            label="车牌号"
            autoLink={false}
            ref={'plateNumRef' + index}
            prop={'plateNumRef' + index}
            rules={specialRules('plateNum', item)}>
            <a-input
              allowClear
              max={100}
              onBlur={() => blur('plateNumRef' + index)}
              onChange={() => change('plateNumRef' + index)}
              class="full-width"
              placeholder="请输入"
              v-model={item.plateNum}/>
          </a-form-model-item> : null }
          { isPeople && item.insuranceType === 6 ? <a-form-model-item
            label="车架"
            autoLink={false}
            ref={'frameNumRef' + index}
            prop={'frameNumRef' + index}
            rules={specialRules('frameNum', item)}>
            <a-input
              allowClear
              max={100}
              onBlur={() => blur('frameNumRef' + index)}
              onChange={() => change('frameNumRef' + index)}
              class="full-width"
              placeholder="请输入"
              v-model={item.frameNum}/>
          </a-form-model-item> : null }
          { isPeople && item.insuranceType === 6 ? <a-form-model-item
            label="品牌"
            autoLink={false}
            ref={'brandRef' + index}
            prop={'brandRef' + index}
            rules={specialRules('brand', item)}>
            <a-input
              allowClear
              max={100}
              onBlur={() => blur('brandRef' + index)}
              onChange={() => change('brandRef' + index)}
              class="full-width"
              placeholder="请输入"
              v-model={item.brand}/>
          </a-form-model-item> : null }
          <a-form-model-item
            label="投保渠道"
            autoLink={false}
            ref={'insuranceChannelRef' + index}
            rules={specialRules('insuranceChannel', item)}
            prop="insuranceChannel">
            <a-input
              allowClear
              placeholder="请输入"
              onBlur={() => blur('insuranceChannelRef' + index)}
              onChange={() => change('insuranceChannelRef' + index)}
              v-model={item.insuranceChannel}
              class="full-width"/>
          </a-form-model-item>
          { form.type === 1 ? <a-form-model-item
            label="投保项目"
            autoLink={false}
            ref={'insuranceProjectRef' + index}
            rules={item.insuranceType === 2 ? specialRules('insuranceProject', item) : []}
            prop={'insuranceProject' + index}>
              <a-input
                v-model={item.insuranceProject}
                maxLength={100}
                allowClear
                onBlur={() => blur('insuranceProjectRef' + index)}
                onChange={() => change('insuranceProjectRef' + index)}
                placeholder="请输入"
                class="full-width"/>
            </a-form-model-item> : null }
          { form.type === 1 ? <a-form-model-item
            label="库存标的金额"
            autoLink={false}
            ref={'stockAmountRef' + index}
            rules={item.insuranceType === 1 ? specialRules('stockAmount', item) : []}
            prop={'stockAmount' + index}>
            <a-input-number
              allowClear
              v-model={item.stockAmount}
              class='full-width'
              placeholder="请输入"
              min={0}
              onBlur={() => blur('stockAmountRef' + index)}
              onChange={() => change('stockAmountRef' + index)}
              max={99999999}
              precision={2}
              formatter={formatter}
              parser={parser}/>
            </a-form-model-item> : null }
            { form.type === 1 ? <a-form-model-item
              label="资产标的金额"
              autoLink={false}
              ref={'assetsAmountRef' + index}
              rules={item.insuranceType === 1 ? specialRules('assetsAmount', item) : []}
              prop={'assetsAmount' + index}>
              <a-input-number
                allowClear
                v-model={item.assetsAmount}
                class='full-width'
                onBlur={() => blur('assetsAmountRef' + index)}
                onChange={() => change('assetsAmountRef' + index)}
                placeholder="请输入"
                min={0}
                max={99999999}
                precision={2}
                formatter={formatter}
                parser={parser}/>
            </a-form-model-item> : null }
            <a-form-model-item label="备注" class="remark">
              <a-textarea
                v-model={item.remark}
                autoSize={{ minRows: 1, maxRows: 10 }}
                maxLength={500}
              />
           </a-form-model-item>
          <div class="full-width overflow">
            <a-form-model-item
              autoLink={false}
              prop="attachmentsList"
              ref={'attachmentsListRef' + index}
              rules={specialRules('attachmentsList', item)}
              label="附件">
              <div class="flex-child-noshrink" style="width: 200%">
                <Uploader
                  pathKey="filePath"
                  fileList={item.attachmentsList}
                  {...{ on: { 'update:fileList': (files) => upLoaderChange(files, item, 'attachmentsListRef' + index) } }}
                  useExtension={true}
                  accept={`image/*,.doc,.docx,.xls,.xlsx,.pdf,.ppt,.pptx,.mp4,.zip,.rar`}/>
              </div>
            </a-form-model-item>
          </div>
          { isAdd ? <div class="flex">
            <div class="add-button button" onClick={() => changeArray('add', 'list')}>增加保险信息</div>
            {form.list?.length > 1 ? <div class="delete-button button" onClick={() => changeArray('delete', 'list', index)}>删除保险信息</div> : null}
          </div> : null }
        </div>) }
      </div>
    }
  })
</script>

<style scoped lang="scss">
@import "../common";
.button {
  margin-left: 16px;
  padding: 6px 16px;
  cursor: pointer;
  border-radius: 33px;
}
.add-button {
  color: #239DFC;
  background: rgb(230, 243, 255)
}
.delete-button {
  background: #ff4d4f;
  color: #fff
}
:deep(.no-colon) {
  .ant-form-item-label {
    padding-right: 10px;
    label:after {
      display: none;
    }
  }
}
:deep(.file-preview) {
  &:first-child {
    margin-top: 8px;
  }
}
.remark {
  width: calc(66.66% - 60px);
  :deep(.ant-form-item-label) {
    width: 15.8% !important;
  }
}
</style>
