<script lang="jsx">
  import { defineComponent, toRefs } from 'vue'
  import { createState } from './hooks/useState'
  import UserInfo from './components/user-info.vue'
  import InsuranceInfo from './components/insurance-info.vue'
  import { rules } from './constants'

  export default defineComponent({
    name: 'index',
    components: { UserInfo, InsuranceInfo },
    setup () {
      const { state, formRef, save, goList, InsuranceInfoRef } = createState()
      return {
        ...toRefs(state),
        formRef,
        save,
        goList,
        InsuranceInfoRef
      }
    },
    render () {
      const { form, save, saveLoading, isAdd } = this
      return <page class="pb-100" title={`${isAdd ? '添加' : '编辑'}保险记录`}>
        <a-form-model
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          ref="formRef"
          { ...{ props: { model: form } } }
          rules={rules}>
          <UserInfo title="保险人信息" id="userInfoId"/>
          <InsuranceInfo title="保险信息" ref="InsuranceInfoRef"/>
        </a-form-model>
        <div class="footer">
          <a-button loading={saveLoading} onClick={() => save()} type="primary" class="ml-20">保存</a-button>
        </div>
    </page>
    }
  })
</script>

<style scoped lang="scss">
.pb-100 {
  padding-bottom: 80px;
}
.footer {
  position: fixed;
  background: #fff;
  padding: 24px;
  width: 100%;
  left: 0;
  bottom: 0;
  display: flex;
}
</style>
