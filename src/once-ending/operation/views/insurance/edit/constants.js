import { cloneDeep } from 'lodash'

export const insuranceInfoForm = {
  insuranceType: undefined,
  insurancePolicyNo: undefined,
  insuranceStatus: undefined,
  insurancePrice: undefined,
  startDate: undefined,
  endDate: undefined,
  insuranceChannel: undefined,
  insuranceProject: undefined,
  stockAmount: undefined,
  assetsAmount: undefined,
  remark: undefined,
  attachmentsList: [],
  plateNum: undefined,
  frameNum: undefined,
  brand: undefined,
}

export const userForm = { id: undefined, type: 1, name: undefined }

export const originForm = {
  type: undefined,
  areaId: undefined,
  insuranceAddress: undefined,
  applicantId: undefined,
  applicantName: undefined,
  insuredId: undefined,
  insuredName: undefined,
  insuredInfo: undefined,
  carType: undefined,
  applicantList: [cloneDeep(userForm)],
  insuredList: [cloneDeep(userForm)],
  list: [cloneDeep(insuranceInfoForm)],
  fileList: []
}
const commonRule = { required: true, trigger: ['change', 'blur'] }
export const rules = {
  type: [{ ...commonRule, message: '请选择保险对象' }],
  areaId: [{ ...commonRule, message: '请选择门店' }],
  insuranceAddress: [{ ...commonRule, message: '请输入保险标的地址' }],
  applicantId: [{ ...commonRule, message: '请选择投保人' }],
  insuredId: [{ ...commonRule, message: '请选择被保人' }],
  carType: [{ ...commonRule, message: '请选择车辆类型' }],
  insuredInfo: [{ ...commonRule, message: '请选择被保车辆' }],
  insuranceType: [{ ...commonRule, message: '请选择险种' }],
  insuranceProject: [{ ...commonRule, message: '请输入投保项目' }],
  insurancePolicyNo: [{ ...commonRule, message: '请输入保险单号/合同批签号' }],
  insuranceStatus: [{ ...commonRule, message: '请先选择保险开始/结束日期' }],
  plateNum: [{ ...commonRule, message: '请输入车牌号' }],
  frameNum: [{ ...commonRule, message: '请输入车架' }],
  brand: [{ ...commonRule, message: '请输入品牌' }],
  insurancePrice: [{ ...commonRule, message: '请输入保费' }],
  startDate: [{ ...commonRule, message: '请选择保险开始日期' }],
  endDate: [{ ...commonRule, message: '请选择保险结束日期' }],
  insuranceChannel: [{ ...commonRule, message: '请选择投保渠道' }],
  stockAmount: [{ ...commonRule, message: '请输入库存标的金额' }],
  assetsAmount: [{ ...commonRule, message: '请输入资产标的金额' }],
  attachmentsList: [{ ...commonRule, message: '请上传附件' }],
}

export const listNeedKeys = (type, insuranceType) => {
  let cache = [
    'insuranceType',
    'insurancePolicyNo',
    'insuranceStatus',
    'insurancePrice',
    'startDate',
    'endDate',
    'insuranceChannel'
  ]
  type === 2 && insuranceType === 3 && (cache = cache.concat(['plateNum', 'frameNum', 'brand']))
  type === 1 && insuranceType === 1 && (cache = cache.concat(['assetsAmount', 'stockAmount']))
  return cache
}

export const userInfoNeedKeys = (type) => {
  const other = [
    'applicantId',
    'insuredId',
    'insuranceAddress',
  ]
  return type === 1 ? [
    'areaId',
    'insuranceAddress',
    'applicantList',
    'insuredList'
  ] : type === 2 ? other : other.concat([
    'carType',
  ])
}
