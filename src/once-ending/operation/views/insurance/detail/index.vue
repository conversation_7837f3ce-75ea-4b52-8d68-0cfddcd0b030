<script lang="jsx">
  import { defineComponent, toRefs } from 'vue'
  import { createState } from './hooks/useState'
  import Uploader from '@/operation/views/complaint/detail/components/uploader.vue'
  import { NiLog } from '@jiuji/nine-ui'

  export default defineComponent({
    name: 'index',
    components: { Uploader, NiLog },
    setup () {
      const { state } = createState()
      return {
        ...toRefs(state)
      }
    },
    render () {
      const { form, logs } = this
      return <page>
          <div class="content-box">
            <span class="title">保险人信息</span>
            <a-descriptions class="mt-16">
              <a-descriptions-item label="保险对象">
                {form.typeStr}
              </a-descriptions-item>
              { form.type === 1 ? <a-descriptions-item label="门店代码">
                {form.area}
              </a-descriptions-item> : null }
              { [2, 3].includes(form.type) ? <a-descriptions-item label={form.type === 3 ? '车牌号' : '被保人'}>
                {form.insuredName}
              </a-descriptions-item> : null }
              { form.type === 3 ? <a-descriptions-item label="车辆类型">
                { form.carTypeStr }
              </a-descriptions-item> : null }
              { [2, 3].includes(form.type) ? <a-descriptions-item label="投保人">
                {form.applicantName}
              </a-descriptions-item> : null }
              <a-descriptions-item label="保险标的地址">
                {form.insuranceAddress}
              </a-descriptions-item>
              { form.type === 1 ? <a-descriptions-item label="被保人">
                <div class="flex flex-col">
                  { form.insuredList?.map(item => <div>
                    {item.typeStr ? item.typeStr + '-' : ''}
                    {item.name}
                  </div>) }
                </div>
              </a-descriptions-item> : null }
              { form.type === 1 ? <a-descriptions-item label="投保人">
                <div class="flex flex-col">
                  { form.applicantList?.map(item => <div>
                    {item.typeStr ? item.typeStr + '-' : ''}
                    {item.name}
                  </div>) }
                </div>
              </a-descriptions-item> : null }
            </a-descriptions>
          </div>
          <div class="content-box">
            <span class="title">保险信息</span>
            <a-descriptions class="mt-16">
              <a-descriptions-item label="险种">
                {form.insuranceTypeStr}
              </a-descriptions-item>
              <a-descriptions-item label="保险单号/合同批签号">
                {form.insurancePolicyNo}
              </a-descriptions-item>
              <a-descriptions-item label="保险状态">
                {form.insuranceStatusStr}
              </a-descriptions-item>
              <a-descriptions-item label="保费">
                {form.insurancePrice}
              </a-descriptions-item>
              <a-descriptions-item label="保险开始日期">
                {form.startDate}
              </a-descriptions-item>
              <a-descriptions-item label="保险结束日期">
                {form.endDate}
              </a-descriptions-item>
              { form.type === 2 && form.insuranceType === 6 ? <a-descriptions-item label="车牌号">
                {form.plateNum}
              </a-descriptions-item> : null }
              { form.type === 2 && form.insuranceType === 6 ? <a-descriptions-item label="车架">
                {form.frameNum}
              </a-descriptions-item> : null }
              { form.type === 2 && form.insuranceType === 6 ? <a-descriptions-item label="品牌">
                {form.brand}
              </a-descriptions-item> : null }
              <a-descriptions-item label="投保渠道">
                {form.insuranceChannel}
              </a-descriptions-item>
              { form.type === 1 ? <a-descriptions-item label="投保项目">
                {form.insuranceProject}
              </a-descriptions-item> : null }
              { form.type === 1 ? <a-descriptions-item label="库存标的金额">
                {form.stockAmount}
              </a-descriptions-item> : null }
              { form.type === 1 ? <a-descriptions-item label="资产标的金额">
                {form.assetsAmount}
              </a-descriptions-item> : null }
              <a-descriptions-item label="备注" span={2}>
                {form.remark}
              </a-descriptions-item>
              <a-descriptions-item label="附件" span={3}>
                <div class="flex flex-col">
                  <Uploader
                    editFileName={false}
                    deleteItem={false}
                    showUpload={false}
                    pathKey="filePath"
                    fileList={form.attachmentsList}
                    useExtension={true}/>
                </div>
              </a-descriptions-item>
            </a-descriptions>
          </div>
        <div class="content-box">
          <span class="title">日志信息</span>
          { logs?.length ? <NiLog class="mt-16" data={logs}/> : <div class="flex flex-center grey-9" style="height: 100px">
            暂无数据
          </div> }
        </div>
      </page>
    }
  })
</script>

<style scoped lang="scss">
@import "../edit/common";
</style>
