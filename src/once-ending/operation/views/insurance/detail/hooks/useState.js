import { reactive, provide, inject, getCurrentInstance } from 'vue'
import { debounce, cloneDeep } from 'lodash'
import { originForm, insuranceInfoForm } from '../../edit/constants'
import { to } from '~/util/common'
import insuranceApi from '@/operation/api/insurance'

const key = Symbol('key')

export function useState () {
  return inject(key)
}

export function createState () {
  const { proxy } = getCurrentInstance()
  const state = reactive({
    form: {
      ...cloneDeep(originForm),
      ...cloneDeep(insuranceInfoForm),
    },
    logs: []
  })

  const { id } = proxy.$route.params

  async function getDetail () {
    proxy.$indicator.open()
    const [err, res] = await to(insuranceApi.getDetail(id))
    proxy.$indicator.close()
    if (err) throw err
    if (res?.code === 0) {
      Object.assign(state.form, res.data)
    } else {
      proxy.$message.error(res.userMsg)
    }
  }
  getDetail()

  async function getLogs () {
    const [err, res] = await to(insuranceApi.getLogs(id))
    if (err) throw err
    if (res?.code === 0) {
      state.logs = res.data?.map(it => ({
        ...it,
        userName: it.createUserName
      })) || []
    } else {
      proxy.$message.error(res.userMsg)
    }
  }
  getLogs()

  const o = {
    state,
  }
  provide(key, o)
  return o
}
