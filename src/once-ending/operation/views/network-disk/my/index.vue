<template>
  <div class="disk-detail-wrap">
    <Table
      v-if="topState.listOrGrid === '1'"
      :columns="commonColumns"
      :dataSource="dataSource"
      :isRowSelection="topState.isBatch"
      @reNameFileHandle="reNameFileHandle"
      @removeFileHandle="removeFileHandle"
      @batchDeleteFileHandle="(value) => batchDeleteFile(value)"
    ></Table>
    <Grid
      v-if="topState.listOrGrid === '2'"
      :dataSource="dataSource"
      @reNameFileHandle="reNameFileHandle"
      @removeFileHandle="removeFileHandle"
      @batchDeleteFileHandle="(value) => batchDeleteFile(value)"
    ></Grid>
  </div>
</template>

<script setup>
  import { commonColumns } from '../constants'
  import { useNetWorkDiskInject } from '../useNetWorkDisk'
  import Table from '../components/table'
  import Grid from '../components/grid'
  const {
    topState,
    createNewFolderHandle,
    reNameFileHandle,
    removeFileHandle,
    dataSource,
    batchDeleteFile,
  } = useNetWorkDiskInject()
</script>

<style lang="scss" scoped>
@import "../common";
</style>
