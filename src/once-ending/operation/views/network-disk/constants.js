const imageIco = require('../../../../assets/images/files/image-text.png')
const pdfIco = require('../../../../assets/images/files/pdfIco.png')
const wordIco = require('../../../../assets/images/files/wordIco.png')
const pptIco = require('../../../../assets/images/files/pptIco.png')
const videoIco = require('../../../../assets/images/files/video.png')
const audioIco = require('../../../../assets/images/files/audio.png')
const excelIco = require('../../../../assets/images/files/excelIco.png')
const textIco = require('../../../../assets/images/files/txtIco.png')
const folderIco = require('../../../../assets/images/files/folder.png')
const zipIco = require('../../../../assets/images/files/zip.png')
const unknownIco = require('../../../../assets/images/files/unknown.png')

const sortTypeOptions = [
  {
    label: '按文件名称排序',
    value: 1
  },
  {
    label: '按时间排序',
    value: 2
  },
  // {
  //   label: '按文件类型排序',
  //   value: 3
  // },
  {
    label: '按文件大小排序',
    value: 4
  },
]

const folderOrFile = [
  {
    label: '全部',
    value: '5'
  },
  {
    label: '文件夹',
    value: '1'
  },
  {
    label: '文件',
    value: '2'
  },
]

const recentColumns = [
  {
    title: '名称',
    dataIndex: 'name',
    width: 300,
    scopedSlots: { customRender: 'nameSlot' },
  },
  {
    title: '最近浏览时间',
    dataIndex: 'recentView',
    width: 100
  },
  {
    title: '创建人',
    dataIndex: 'createUserName',
    width: 150
  },
  {
    title: '大小',
    dataIndex: 'fileSize',
    width: 150
  },
  {
    title: '操作',
    dataIndex: 'actionSlot',
    width: 150,
    scopedSlots: { customRender: 'actionSlot' },
  }
]

const commonColumns = [
  {
    title: '名称',
    dataIndex: 'name',
    width: 300,
    scopedSlots: { customRender: 'nameSlot' },
  },
  {
    title: '创建时间',
    dataIndex: 'createDate',
    width: 100
  },
  {
    title: '创建人',
    dataIndex: 'createUserName',
    width: 150
  },
  {
    title: '大小',
    dataIndex: 'fileSize',
    width: 150
  },
  {
    title: '操作',
    dataIndex: 'actionSlot',
    width: 150,
    scopedSlots: { customRender: 'actionSlot' },
  }
]

const recycleColumns = [
  {
    title: '名称',
    dataIndex: 'name',
    width: 300,
    scopedSlots: { customRender: 'nameSlot' },
  },
  {
    title: '大小',
    dataIndex: 'fileSize',
    width: 150
  },
  {
    title: '删除人',
    dataIndex: 'createUserName',
    width: 150
  },
  {
    title: '删除时间',
    dataIndex: 'createDate',
    width: 100
  },
  {
    title: '有效时间',
    dataIndex: 'validTime',
    width: 100,
  }
]

const formatIco = (fileName = '') => {
  if (fileName) {
    const regImg = /(jpg|bmp|gif|ico|pcx|jpeg|tif|png|raw|tga|webp|svg)$/i
    const regPDF = /(pdf|pdfx)$/i
    const regWord = /(doc|docx)$/i
    const regPPT = /(ppt|pptx)$/i
    const regExcel = /(xls|xlsx)$/i
    const regVideo = /(avi|rmvb|mpeg|wmf|mov|mkv|mp4)$/i
    const regAudio = /(mp3|wav|m4a)$/i
    const regZip = /(zip)$/i
    const regText = /(text)$/i
    if (regImg.test(fileName)) return imageIco
    if (regPDF.test(fileName)) return pdfIco
    if (regWord.test(fileName)) return wordIco
    if (regPPT.test(fileName)) return pptIco
    if (regVideo.test(fileName)) return videoIco
    if (regAudio.test(fileName)) return audioIco
    if (regZip.test(fileName)) return zipIco
    if (regExcel.test(fileName)) return excelIco
    if (regText.test(fileName)) return textIco
    return unknownIco
  } else {
    return folderIco
  }
}

const previewImg = (fileUrl = '') => {
  if (fileUrl) {
    const regImg = /.(jpg|bmp|gif|ico|pcx|jpeg|tif|png|raw|tga|webp|svg)$/i
    return regImg.test(fileUrl)
  }
}

const accessEnum = [
  {
    label: '禁止访问',
    value: -1
  },
  {
    label: '移除成员',
    value: -2
  },
  {
    label: '可查看',
    value: 1
  },
  {
    label: '可上传',
    value: 2
  },
  {
    label: '可编辑',
    value: 3
  },
  {
    label: '管理员',
    value: 5
  },
  {
    label: '超级管理员',
    value: 6
  },
]

export {
  sortTypeOptions,
  folderOrFile,
  recentColumns,
  formatIco,
  previewImg,
  commonColumns,
  recycleColumns,
  accessEnum
}
