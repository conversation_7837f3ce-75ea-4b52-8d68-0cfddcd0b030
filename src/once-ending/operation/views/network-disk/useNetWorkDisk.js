import { inject, provide, ref, reactive, getCurrentInstance, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router/composables'
import Api from '../../api/network-disk'
import C<PERSON>Deep from 'lodash/cloneDeep'
import { saveAs } from 'file-saver'
import { sortTypeOptions } from './constants'
import uuid from 'uuid'

const key = Symbol('netWorkDisk')

export function useNetWorkDiskInject () {
  return inject(key)
}

export function useNetWorkDisk () {
  const {
    $confirm,
    $message,
    $indicator,
    $store,
    $tnt
  } = getCurrentInstance().proxy
  const route = useRoute()
  const router = useRouter()
  const topState = reactive({
    searchValue: '',
    // 批量操作
    isBatch: false,
    // 批量操作选中的列表
    selectItemList: [],
    //  排序方式
    sortType: sessionStorage.getItem('sortType') ? JSON.parse(sessionStorage.getItem('sortType')) : [2],
    // 列表图宫格切换
    listOrGrid: sessionStorage.getItem('listOrGrid') ? sessionStorage.getItem('listOrGrid') : '1',
    // 文件夹还是文件, 排列方式——按文件类型排序
    // folderOrFile: sessionStorage.getItem('folderOrFile') ? sessionStorage.getItem('folderOrFile') : '5',
  })
  const selectedRowKeys = ref([])
  const selectedRows = ref([])
  const bottomState = reactive({
    delDisabled: true,
  })

  const pagination = ref({
    current: 1,
    pageSize: 10,
    showTotal: total => `共 ${total} 条记录`,
    pageSizeOptions: ['10', '20', '50', '100'],
    showSizeChanger: true,
    total: 0,
  })

  const dataSource = ref([])

  const renameFileRef = ref(null)
  const uploadFileRef = ref(null)
  const uploadFileLoading = ref(false)
  const removeFileRef = ref(null)
  const removeFileSingleInfo = ref(undefined)
  const tabVal = computed(() => {
    return $store.state.operation.networkDisk.tab
  })
  const folderId = ref(undefined)
  const parentPathList = ref([])
  const isShare = ref(false)
  const isCommonFlder = ref(false)
  const forwardInfoVal = computed(() => {
    return $store.state.operation.networkDisk.forwardInfo
  })
  const forwardValMsg = computed(() => {
    return $store.state.operation.networkDisk.forwardVal
  })
  const sortTypeOptionsNew = ref([])
  const resetValue = () => {
    topState.searchValue = ''
    topState.isBatch = false
    topState.selectItemList = []
    // todo
    topState.sortType = [2]
    topState.listOrGrid = '1'
    // topState.folderOrFile = '5'
    bottomState.delDisabled = true
    dataSource.value = []
    pagination.value.total = 0
    pagination.value.current = 1
    pagination.value.pageSize = 10
    flagFormat()
    setTimeout(() => {
      fetchData()
    }, 300)
  }
  const flagFormat = () => {
    if (route.path.includes('recent')) {
      sortTypeOptionsNew.value = sortTypeOptions.filter(item => item.value !== 3)
    } else {
      sortTypeOptionsNew.value = sortTypeOptions
    }
    folderId.value = route.query.pathId ? route.query.pathId : route.params.id
    if (route.name === 'recent') {
      $store.commit('operation/networkDisk/setTab', 3)
      $store.commit('operation/networkDisk/createNewFolderShow', false)
      $store.commit('operation/networkDisk/uploadFileShow', false)
      $store.commit('operation/networkDisk/setIsBatchShow', false)
      if (sessionStorage.getItem('sortType')) {
        if (JSON.parse(sessionStorage.getItem('sortType'))[0] === 3) {
          topState.sortType = [2]
          sessionStorage.setItem('sortType', JSON.stringify(topState.sortType))
        } else {
          topState.sortType = JSON.parse(sessionStorage.getItem('sortType'))
          sessionStorage.setItem('sortType', JSON.stringify(topState.sortType))
        }
      } else {
        topState.sortType = [2]
        sessionStorage.setItem('sortType', JSON.stringify(topState.sortType))
      }
      // topState.folderOrFile = 5
      // sessionStorage.setItem('folderOrFile', 5)
      document.title = '最近'
      route.meta.title = '最近'
    } else if (route.name === 'my') {
      $store.commit('operation/networkDisk/setTab', 1)
      $store.commit('operation/networkDisk/createNewFolderShow', true)
      $store.commit('operation/networkDisk/uploadFileShow', true)
      $store.commit('operation/networkDisk/setIsBatchShow', true)
      document.title = '我的'
      route.meta.title = '我的'
      if (sessionStorage.getItem('sortType')) {
        if (JSON.parse(sessionStorage.getItem('sortType'))[0] === 2) {
          topState.sortType = [2]
          sessionStorage.setItem('sortType', JSON.stringify(topState.sortType))
        } else {
          topState.sortType = JSON.parse(sessionStorage.getItem('sortType'))
          sessionStorage.setItem('sortType', JSON.stringify(topState.sortType))
        }
      } else {
        topState.sortType = [2]
        sessionStorage.setItem('sortType', JSON.stringify(topState.sortType))
      }
      // if (sessionStorage.getItem('folderOrFile')) {
      //   topState.folderOrFile = sessionStorage.getItem('folderOrFile')
      // } else {
      //   topState.folderOrFile = '5'
      //   sessionStorage.setItem('folderOrFile', '5')
      // }
    } else if (route.name === 'share') {
      $store.commit('operation/networkDisk/setTab', 2)
      document.title = '分享'
      route.meta.title = '分享'
      if (sessionStorage.getItem('sortType')) {
        if (JSON.parse(sessionStorage.getItem('sortType'))[0] === 2) {
          topState.sortType = [2]
          sessionStorage.setItem('sortType', JSON.stringify(topState.sortType))
        } else {
          topState.sortType = JSON.parse(sessionStorage.getItem('sortType'))
          sessionStorage.setItem('sortType', JSON.stringify(topState.sortType))
        }
      } else {
        topState.sortType = [2]
        sessionStorage.setItem('sortType', JSON.stringify(topState.sortType))
      }
      // if (sessionStorage.getItem('folderOrFile')) {
      //   topState.folderOrFile = sessionStorage.getItem('folderOrFile')
      // } else {
      //   topState.folderOrFile = '5'
      //   sessionStorage.setItem('folderOrFile', '5')
      // }
    } else if (route.name === 'commonFolder') {
      $store.commit('operation/networkDisk/setTab', 2)
      $store.commit('operation/networkDisk/createNewFolderShow', true)
      $store.commit('operation/networkDisk/uploadFileShow', true)
      $store.commit('operation/networkDisk/setIsBatchShow', true)
      document.title = '公共'
      route.meta.title = '公共'
      if (sessionStorage.getItem('sortType')) {
        if (JSON.parse(sessionStorage.getItem('sortType'))[0] === 2) {
          topState.sortType = [2]
          sessionStorage.setItem('sortType', JSON.stringify(topState.sortType))
        } else {
          topState.sortType = JSON.parse(sessionStorage.getItem('sortType'))
          sessionStorage.setItem('sortType', JSON.stringify(topState.sortType))
        }
      } else {
        topState.sortType = [2]
        sessionStorage.setItem('sortType', JSON.stringify(topState.sortType))
      }
      // if (sessionStorage.getItem('folderOrFile')) {
      //   topState.folderOrFile = sessionStorage.getItem('folderOrFile')
      // } else {
      //   topState.folderOrFile = '5'
      //   sessionStorage.setItem('folderOrFile', '5')
      // }
    } else if (route.name === 'recycle') {
      $store.commit('operation/networkDisk/setTab', 4)
      document.title = '回收站'
      route.meta.title = '回收站'
    } else {
      $store.commit('operation/networkDisk/setTab', undefined)
    }
    // 优化布尔值设置
    isShare.value = route.name === 'share'
    isCommonFlder.value = route.name === 'commonFolder'
    if (sessionStorage.getItem('listOrGrid')) {
      topState.listOrGrid = sessionStorage.getItem('listOrGrid')
    } else {
      topState.listOrGrid = '1'
    }
  }
  flagFormat()
  const getPcHomeTab = () => {
    Api.getPcHomeTab().then(res => {
      if (res.code === 0) {
        $store.commit('operation/networkDisk/setRoutes', res.data)
      } else {
        $message.error(res.userMsg)
      }
    })
  }
  getPcHomeTab()
  const getParentId = () => {
    if (route.params.id && !route.query.pathId) {
      return route.params.id === '0' ? undefined : route.params.id
    }
    if (route.params.id && route.query.pathId) {
      return route.query.pathId
    }
  }
  const netDiskFileHome = (isLoadMore = false) => {
    const params = {
      size: pagination.value.pageSize,
      current: pagination.value.current,
      tab: tabVal.value,
      sort: topState.sortType.join(','),
      search: topState.searchValue,
      parentId: getParentId(),
      // dataType: topState.folderOrFile === '5' || topState.folderOrFile === 5 ? undefined : topState.folderOrFile,
      dataType: '',
      sharedTopId: route.query.sharedTopId,
      sourceIM: isShare.value ? 1 : undefined,
      t: Date.now() + uuid(),
    }
    $indicator.open()
    Api.netDiskFileHome(params).then(res => {
      if (res.code === 0) {
        if (isLoadMore) {
          dataSource.value = [...dataSource.value, ...res.data.records]
        } else {
          dataSource.value = res.data.records
        }
        parentPathList.value = res.data.parentPathList
        pagination.value.total = res.data.total
        $store.commit('operation/networkDisk/setBatchPermission', res.data.parentAccess)
      } else {
        $message.error(res.userMsg)
      }
    }).finally(() => {
      $indicator.close()
    })
  }
  const netDiskRecycleBin = () => {
    const params = {
      size: pagination.value.pageSize,
      current: pagination.value.current,
    }
    $indicator.open()
    Api.netDiskRecycleBin(params).then(res => {
      if (res.code === 0) {
        dataSource.value = res.data.records
        pagination.value.total = res.data.total
      } else {
        $message.error(res.userMsg)
      }
    }).finally(() => {
      $indicator.close()
    })
  }
  const fetchData = () => {
    if (!tabVal.value) {
      flagFormat()
    }
    if ([1, 2, 3].includes(tabVal.value)) {
      netDiskFileHome()
    } else {
      netDiskRecycleBin()
    }
  }

  const loadMoreData = () => {
    if (pagination.value.current * pagination.value.pageSize < pagination.value.total) {
      pagination.value.current += 1
      if ([1, 2, 3].includes(tabVal.value)) {
        netDiskFileHome(true)
      } else {
        // 暂不支持回收站加载更多
        return false
      }
      return true
    }
    return false
  }
  fetchData()
  const handleTableChange = async (pageInfo) => {
    pagination.value = CloneDeep(pageInfo)
    await fetchData()
  }
  const batchDeleteFile = (params) => {
    $confirm({
      title: '提示',
      content: '确定删除吗？',
      onOk: () => {
        $indicator.open()
        Api.batchDeleteFile(params).then(async res => {
          if (res.code === 0) {
            $message.success(res.userMsg)
            selectedRowKeys.value = []
            if (topState.isBatch) {
              topState.isBatch = false
            }
            await fetchData()
            await getPcHomeTab()
          } else {
            $message.error(res.userMsg)
          }
        }).finally(() => {
          $indicator.close()
        })
      },
      onCancel: () => {
        console.log('取消删除')
      }
    })
  }
  const batchDeleteFileHandle = async () => {
    if (selectedRowKeys.value && selectedRowKeys.value.length) {
      await batchDeleteFile(selectedRowKeys.value)
    }
  }
  const fileRenameHandle = (params) => {
    $indicator.open()
    Api.fileRename(params).then(res => {
      if (res.code === 0) {
        $message.success(res.userMsg)
        fetchData()
      } else {
        $message.error(res.userMsg)
      }
    }).finally(() => {
      $indicator.close()
    })
  }
  const createNewFolderHandle = () => {
    $store.commit('operation/networkDisk/renameFileStateFlag', 1) // 设置为创建文件夹模式
    $store.commit('operation/networkDisk/setRenameFileInfo', {}) // 清空重命名信息
    renameFileRef.value.open()
  }
  const uploadFileHandle = () => {
    uploadFileRef.value.fileHandleChange()
  }
  const uploadSureHandle = (fileList) => {
    uploadFileRef.value.closeUploadList()
  }

  const isBatchHandle = () => {
    topState.isBatch = !topState.isBatch
    if (!topState.isBatch) {
      selectedRowKeys.value = []
    }
  }

  const sortTypeChange = ({ item, key, keyPath }) => {
    // if (key === 3) {
    //   topState.folderOrFile = '5'
    // } else {
    //   topState.folderOrFile = ''
    // }
    topState.sortType = keyPath
    sessionStorage.setItem('sortType', JSON.stringify(keyPath))
    // sessionStorage.setItem('folderOrFile', topState.folderOrFile)
    fetchData()
  }

  const listOrGridHandle = (flag) => {
    topState.listOrGrid = flag
    sessionStorage.setItem('listOrGrid', flag)
    selectedRowKeys.value = []
    topState.isBatch = false
    pagination.value.current = 1
    pagination.value.pageSize = flag === 1 ? 10 : 48
    fetchData()
  }

  const folderOrFileItemHandle = (value) => {
    topState.folderOrFile = value
    sessionStorage.setItem('folderOrFile', value)
    fetchData()
  }
  const reNameFileHandle = (record) => {
    console.log('record', record)
    $store.commit('operation/networkDisk/renameFileStateFlag', 2) // 设置为重命名模式
    $store.commit('operation/networkDisk/setRenameFileInfo', record)
    renameFileRef.value.open()
  }
  const removeFileHandle = (record) => {
    removeFileSingleInfo.value = record
    removeFileRef.value.open()
  }

  const showSelectMemberModal = ref(false)
  const forwardTableHandle = () => {
    forward(selectedRows.value)
  }
  const forward = function (itemInfo) {
    if (itemInfo.length > 15) return $message.error('最多只能选择15条数据')
    $store.commit('operation/networkDisk/setForwardInfo', Array.isArray(itemInfo) ? itemInfo : [itemInfo])
    showSelectMemberModal.value = true
  }
  const copyLinkHandle = async (info) => {
    const params = {
      fileId: info.id,
    }
    const res = await Api.createShareLink(params).catch(err => {
      $message.error(err.message)
    })
    if (res) {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(res)
          .then(() => {
            $message.success('链接已复制到剪贴板')
          })
          .catch(err => {
            console.error('复制失败:', err)
            fallbackCopy(res)
          })
      } else {
        fallbackCopy(res)
      }
    }
  }
  const openPermission = (info) => {
    $store.commit('operation/networkDisk/setPermissionFolderId', info.id)
    $store.commit('operation/networkDisk/setPermissionVisible', true)
  }
  const fallbackCopy = (text) => {
    const textarea = document.createElement('textarea')
    textarea.value = text
    textarea.style.position = 'fixed'
    textarea.style.opacity = '0'
    document.body.appendChild(textarea)
    textarea.select()
    try {
      const successful = document.execCommand('copy')
      if (successful) {
        $message.success('链接已复制到剪贴板')
      } else {
        $message.error('复制失败')
      }
    } catch (err) {
      $message.error('复制失败')
    }
    // 移除临时元素
    document.body.removeChild(textarea)
  }
  const saveFileHandle = (info) => {
    if (info.fileLink) {
      saveAs(info.fileLink, info.name)
    } else {
      console.error('没有下载链接')
    }
  }
  const deleteCompletely = () => {
    $confirm({
      title: '提示',
      content: '确定删除吗？',
      onOk: () => {
        $indicator.open()
        Api.recycleBinBatchDelete(selectedRowKeys.value).then(async res => {
          if (res.code === 0) {
            selectedRowKeys.value = []
            $message.success(res.userMsg)
            await fetchData()
          } else {
            $message.error(res.userMsg)
          }
        }).finally(() => {
          $indicator.close()
        })
      },
      onCancel: () => {
        console.log('取消删除')
      }
    })
  }
  const recycleBinBatchRecover = () => {
    $indicator.open()
    Api.recycleBinBatchRecover(selectedRowKeys.value).then(async res => {
      if (res.code === 0) {
        selectedRowKeys.value = []
        $message.success(res.userMsg)
        await fetchData()
        await getPcHomeTab()
      } else {
        $message.error(res.userMsg)
      }
    }).finally(() => {
      $indicator.close()
    })
  }
  const openNewFolder = (record) => {
    if (route.name === 'recent') {
      router.push({
        path: `/operation/network-disk/recent/${record.id}`,
      })
    } else if (route.name === 'my') {
      router.push({
        path: `/operation/network-disk/my/${record.id}`,
      })
    } else if (route.name === 'commonFolder') {
      router.push({
        path: route.fullPath,
        query: {
          pathId: record.id
        }
      })
    } else if (route.name === 'share') {
      router.push({
        path: `/operation/network-disk/share/${record.id}?sharedTopId=${route.query.sharedTopId ? route.query.sharedTopId : route.params.id}`,
      })
    } else if (route.name === 'recycle') {
      router.push({
        path: `/operation/network-disk/recycle/${record.id}`,
      })
    } else {
      console.log('出错了')
    }
  }
  const changeBatch = (item) => {
    if (selectedRowKeys.value.includes(item.id)) {
      selectedRowKeys.value = selectedRowKeys.value.filter(i => i !== item.id)
    } else {
      selectedRowKeys.value.push(item.id)
    }
    if (selectedRows.value.some(d => d.id === item.id)) {
      selectedRows.value = selectedRows.value.filter(d => d.id !== item.id)
    } else {
      selectedRows.value.push(item)
    }
  }
  watch(() => route.fullPath, (newVal, oldValue) => {
    flagFormat()
  })
  watch(() => selectedRowKeys.value, (newVal, oldValue) => {
    if (newVal && newVal.length) {
      bottomState.delDisabled = false
    } else {
      bottomState.delDisabled = true
    }
  }, {
    immediate: true,
    deep: true
  })

  const initOrganization = async () => {
    await $store.dispatch('operation/networkDisk/getOrganization')
    $store.dispatch('operation/networkDisk/getImRecent')
  }
  initOrganization()
  const forwardUserHandle = (user) => {
    let params = {
      staffIds: user || [],
      groupIds: [],
      message: forwardValMsg.value,
      fileInfo: [],
      folderInfo: []
    }
    if (forwardInfoVal.value && forwardInfoVal.value.length) {
      forwardInfoVal.value.forEach(item => {
        if (item.path) {
          params.folderInfo.push({
            folderId: item.id + '',
            folderName: item.meta.title,
          })
        } else {
          if (item.fileExt) {
            params.fileInfo.push({
              fileName: item.name,
              filePath: item.fileLink,
              fileSize: item.fileSizeValue
            })
          } else {
            params.folderInfo.push({
              folderId: item.id + '',
              folderName: item.name,
            })
          }
        }
      })
    }
    console.log(params)
    $indicator.open()
    Api.imForwardFile(params).then(async res => {
      if (res.code === 0) {
        $message.success('转发成功')
        logReportHandle(selectedRowKeys.value, 3)
        selectedRowKeys.value = []
        selectedRows.value = []
        fetchData()
      } else {
        $message.error(res.userMsg)
      }
    }).finally(() => {
      $indicator.close()
      $store.commit('operation/networkDisk/setForwardVal', '')
      $store.commit('operation/networkDisk/setForwardInfo', [])
    })
  }
  const logReportHandle = (fileIds, operate) => {
    // operate 操作，2 查看，3 发送给同事，4 下载
    Api.logReport({
      fileIds,
      operate
    }).then(res => {
      if (res.code !== 0) {
        $message.error(res.userMsg)
      }
    })
  }
  const o = {
    topState,
    selectedRowKeys,
    selectedRows,
    sortTypeChange,
    listOrGridHandle,
    folderOrFileItemHandle,
    isBatchHandle,
    bottomState,
    folderId,
    renameFileRef,
    uploadFileRef,
    uploadFileLoading,
    removeFileRef,
    createNewFolderHandle,
    uploadFileHandle,
    uploadSureHandle,
    resetValue,
    pagination,
    reNameFileHandle,
    removeFileHandle,
    dataSource,
    fetchData,
    getPcHomeTab,
    batchDeleteFile,
    handleTableChange,
    fileRenameHandle,
    forward,
    showSelectMemberModal,
    copyLinkHandle,
    openPermission,
    saveFileHandle,
    tabVal,
    deleteCompletely,
    recycleBinBatchRecover,
    batchDeleteFileHandle,
    loadMoreData,
    openNewFolder,
    changeBatch,
    removeFileSingleInfo,
    parentPathList,
    forwardUserHandle,
    forwardTableHandle,
    isShare,
    isCommonFlder,
    logReportHandle,
    sortTypeOptionsNew
  }
  provide(key, o)
  return o
}
