.disk-con {
  position: relative;
  height: 100vh;
  overflow-y: auto;
  background: #FFFFFF;
  .breadcrumb {
    padding: 20px;
    border-bottom: 1px solid #F0F0F0;
    span{
      font-size: 14px;
      font-weight: bold;
    }
  }
  .controller-top {
    padding: 10px 20px;
    border-bottom: 1px solid #F0F0F0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .controller-top-left {
      display: flex;
      align-items: center;
      width: 400px;
    }
    .controller-top-right {
      display: flex;
      align-items: center;
      flex: 1;
      justify-content: flex-end;
    }
  }
  .disk-detail-wrap {
    .grid-wrap {
      padding: 20px;
      overflow-y: auto;
      .imgStyle {
        width: 100px;
        height: 100px;
        border-radius: 6px;
      }
      .grid-item {
        width: calc((100% - 140px) / 8);
        margin: 0 20px 20px 0;
        padding: 10px 8px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        .item-title {
          width: 80%;
          line-height: 24px;
          text-align: center;
        }
        &:nth-child(8n) {
          margin-right: 0;
        }
      }
      .lines-2 {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .loading-more, .no-more {
        text-align: center;
        padding: 10px 0;
        color: #999;
        font-size: 14px;
      }
    }
  }
  .folderOrFile-wrap {
    padding: 10px 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #F0F0F0;
    .folderOrFile-item {
      font-size: 14px;
      margin-right: 20px;
      cursor: pointer;
    }
    .active {
      font-weight: bold;
      color: #1890ff;
    }
  }
  .controller-bottom {
    width: 100%;
    padding: 10px 20px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
  }
}
