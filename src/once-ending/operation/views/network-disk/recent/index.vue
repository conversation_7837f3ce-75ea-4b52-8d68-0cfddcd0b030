<template>
  <div class="disk-detail-wrap">
    <Table
      v-if="topState.listOrGrid === '1'"
      :columns="recentColumns"
      :dataSource="dataSource"
      :isRowSelection="topState.isBatch"
      @reNameFileHandle="reNameFileHandle"
      @removeFileHandle="removeFileHandle"
      @batchDeleteFileHandle="(value) => batchDeleteFile(value)"
    ></Table>
    <Grid
      v-if="topState.listOrGrid === '2'"
      :dataSource="dataSource"
    ></Grid>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { recentColumns } from '../constants'
  import { useNetWorkDiskInject } from '../useNetWorkDisk'
  import Table from '../components/table'
  import Grid from '../components/grid'
  const {
    topState,
    createNewFolderHandle,
    reNameFileHandle,
    removeFileHandle,
    dataSource,
    batchDeleteFile
  } = useNetWorkDiskInject()
  console.log(sessionStorage.getItem('listOrGrid'))
</script>

<style lang="scss" scoped>
@import "../common";
</style>
