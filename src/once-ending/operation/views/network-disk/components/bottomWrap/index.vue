<template>
  <div class="controller-bottom">
    <div v-if="isShare || isCommonFlder">
      <span class="mr-8 grey-9" v-if="selectedRowKeys && selectedRowKeys.length">已选中 {{ selectedRowKeys.length }} 条数据</span>
      <a-button type="primary mr-16" v-if="([2,3,5,6].includes(batchPermission)) && topState.isBatch" :disabled="bottomState.delDisabled" @click="forwardTableHandle">发送给同事</a-button>
      <a-button type="primary mr-16" v-if="([3,5,6].includes(batchPermission)) && topState.isBatch" :disabled="bottomState.delDisabled" @click="removeFileHandle">移动</a-button>
      <a-button type="danger" v-if="([3,5,6].includes(batchPermission)) && topState.isBatch" :disabled="bottomState.delDisabled" @click="batchDeleteFileHandle">删除</a-button>
    </div>
    <div v-else>
      <span class="mr-8 grey-9" v-if="selectedRowKeys && selectedRowKeys.length">已选中 {{ selectedRowKeys.length }} 条数据</span>
      <a-button type="primary mr-16" v-if="[1, 2].includes(tabVal) && topState.isBatch" :disabled="bottomState.delDisabled" @click="forwardTableHandle">发送给同事</a-button>
      <a-button type="primary mr-16" v-if="[1, 2].includes(tabVal) && topState.isBatch" :disabled="bottomState.delDisabled" @click="removeFileHandle">移动</a-button>
      <a-button type="danger" v-if="[1, 2].includes(tabVal) && topState.isBatch" :disabled="bottomState.delDisabled" @click="batchDeleteFileHandle">删除</a-button>
    </div>
    <a-button v-if="[4].includes(tabVal)" :disabled="bottomState.delDisabled" type="primary mr-16" @click="deleteCompletely">彻底删除</a-button>
    <a-button v-if="[4].includes(tabVal)" :disabled="bottomState.delDisabled" type="primary" @click="recycleBinBatchRecover">还原</a-button>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import { useNetWorkDiskInject } from '../../useNetWorkDisk'
  import store from '~/store'

  const {
    topState,
    bottomState,
    tabVal,
    deleteCompletely,
    recycleBinBatchRecover,
    batchDeleteFileHandle,
    removeFileHandle,
    forwardTableHandle,
    selectedRowKeys,
    isShare,
    isCommonFlder,
  } = useNetWorkDiskInject()
  const batchPermission = computed(() => store.state.operation.networkDisk.batchPermission)
</script>

<style lang="scss" scoped>
@import "../../common";
</style>
