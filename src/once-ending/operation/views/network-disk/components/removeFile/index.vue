<template>
  <div>
    <a-modal
      :visible="removeFileVis"
      :closable="false"
      title="移动文件到"
      width="1000px"
      :footer="null"
    >
      <div class="move-file-wrap">
        <div class="content flex">
          <div class="content-left">
            <div class="tree-menu">
              <div v-for="item in removeTreeData" :key="item.id" class="tree-item">
                <div
                  class="tree-node"
                  :class="{
                    'tree-node-selected': selectedLeftKey === item.id && !item.children.length,
                    'tree-node-parent': item.children.length
                  }"
                  @click="handleNodeClick(item)"
                >
                  <span class="tree-icon" v-if="item.children.length">
                    <a-icon :type="item.isOpen ? 'caret-down' : 'caret-right'" />
                  </span>
                  <span class="tree-icon" v-else></span>
                  <span class="tree-title lines-1">{{ item.name }}</span>
                </div>
                <div class="tree-children" v-if="item.children.length && item.isOpen">
                  <div
                    v-for="child in item.children"
                    :key="child.id"
                    class="tree-item"
                  >
                    <div
                      class="tree-node tree-node-child"
                      :class="{'tree-node-selected': selectedLeftKey === child.id}"
                      @click.stop="handleChildClick(child)"
                    >
                      <span class="tree-title lines-1">{{ child.name }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="content-right flex flex-col">
            <div class="bread-crumb">
              <a-breadcrumb separator=">">
                <a-breadcrumb-item v-for="(item, index) in visibleBreadcrumbs.start" :key="item.id">
                  <span
                    class="canClick pointer breadcrumb-text"
                    @click="handleItemClick(item, index)"
                    :title="item.name"
                  >
                  {{ item.name }}
                </span>
                </a-breadcrumb-item>
                <!-- 省略号 -->
                <a-breadcrumb-item v-if="visibleBreadcrumbs.hasEllipsis">
                  <a-dropdown :trigger="['click']">
                    <span class="ellipsis-breadcrumb pointer">...</span>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item
                          v-for="(item, index) in visibleBreadcrumbs.hidden"
                          :key="item.id"
                          @click="handleItemClick(item, visibleBreadcrumbs.start.length + index)"
                        >
                          {{ item.name }}
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-breadcrumb-item>
                <!-- 显示最后2级 -->
                <a-breadcrumb-item v-for="(item, index) in visibleBreadcrumbs.end" :key="item.id">
                  <span
                    :class="[index === visibleBreadcrumbs.end.length - 1 ? '' : 'canClick pointer', 'breadcrumb-text']"
                    @click="index === visibleBreadcrumbs.end.length - 1 ? null : handleItemClick(item, visibleBreadcrumbs.start.length + visibleBreadcrumbs.hidden.length + index)"
                    :title="item.name"
                  >
                  {{ item.name }}
                </span>
                </a-breadcrumb-item>
              </a-breadcrumb>
            </div>
            <div class="list-wrap" v-if="dataList && dataList.length">
              <div
                class="list-item flex flex-justify-between flex-align-center"
                v-for="item in dataList" :key="item.id"
                :class="item.fileExt ? 'grey-9 disabledPointer' : 'pointer'"
                @click="!item.fileExt ? handleItemClick(item) : null"
              >
                <img :src="previewImg(item.fileLink) ? item.fileLink : formatIco(item.fileLink)" width="24px" height="24px" class="mr-5">
                <div class="flex-child-average lines-1 ml-8">{{ item.name }}</div>
              </div>
            </div>
            <div v-else class="list-wrap flex flex-col flex-align-center flex-justify-center">
              <img src="../../../../../../assets/images/empty2.png" width="120" height="120"/>
              <span class="font-14 grey-9 mt-8">暂无数据</span>
            </div>
          </div>
        </div>
        <div class="footer">
          <a-button type="primary" :disabled="createHandleDisabled" class="mr-16" @click="createHandle">新建文件夹</a-button>
          <a-button :disabled="moveDisabled" type="primary" class="mr-16" @click="fileBatchMove">移动到此处</a-button>
          <a-button class="mr-16" @click="cancel">取消</a-button>
        </div>
      </div>
    </a-modal>
    <a-modal
      :visible="createVisible"
      title="创建文件夹"
      :maskClosable="false"
      :dialog-style="{ top: '30%' }"
      @ok="ok"
      @cancel="cancelCreate"
    >
    <div class="flex flex-col createVisibleWrap">
      <p class="required mb-5">请输入名称</p>
      <a-input v-model="inputVal" placeholder="请输入" :maxLength="20" allowClear>
        <template #suffix>{{ inputVal && inputVal.length ? inputVal.length : 0 }} / 20</template>
      </a-input>
    </div>
  </a-modal>
  </div>
</template>

<script setup lang="jsx">
  import { ref, defineExpose, getCurrentInstance, computed, defineEmits } from 'vue'
  import { formatIco, previewImg } from '../../constants'
  import Api from '../../../../api/network-disk'
  import { useNetWorkDiskInject } from '../../useNetWorkDisk'

  const { $message, $store, $indicator } = getCurrentInstance().proxy

  const {
    removeFileSingleInfo,
    selectedRowKeys,
    fetchData
  } = useNetWorkDiskInject()

  const emit = defineEmits(['fetchDataHandle'])

  const removeFileVis = ref(false)
  const removeTreeData = ref([])
  const selectedLeftKey = ref('') // 当前选中的菜单项key
  const selectedLeftNode = ref(undefined) // 当前选中的节点对象
  const selectedRightKey = ref('') // 当前选中的节点
  const selectedRightNode = ref(undefined) // 当前选中的节点对象
  const parentPathList = ref([])
  const dataList = ref([])
  const tabVal = ref(1)
  const createVisible = ref(false)
  const inputVal = ref('')

  const visibleBreadcrumbs = computed(() => {
    const maxVisible = 5 // 最多显示5个层级
    const list = parentPathList.value

    if (list.length <= maxVisible) {
      return {
        start: list.slice(0, -1),
        end: list.slice(-1),
        hidden: [],
        hasEllipsis: false
      }
    }

    const startCount = 2 // 前面显示2个
    const endCount = 2 // 后面显示2个

    return {
      start: list.slice(0, startCount),
      end: list.slice(-endCount),
      hidden: list.slice(startCount, -endCount),
      hasEllipsis: true
    }
  })

  const moveDisabled = computed(() => {
    if (!selectedRightKey.value && !selectedLeftKey.value) return true
    if (!selectedRightKey.value) {
      return selectedLeftNode.value.name !== '我的' && [1, 2].includes(selectedLeftNode.value.access)
    } else {
      return [1, 2].includes(selectedRightNode.value.access)
    }
  })

  const createHandleDisabled = computed(() => {
    if (!selectedRightKey.value && !selectedLeftKey.value) return true
    if (!selectedRightKey.value) {
      return selectedLeftNode.value.name !== '我的' && [1].includes(selectedLeftNode.value.access)
    } else {
      return [1].includes(selectedRightNode.value.access)
    }
  })

  const open = () => {
    const routeList = $store.state.operation.networkDisk.routes.filter(item => ['我的', '公共'].includes(item.meta.title))
    removeTreeData.value = routesFormat(routeList)
    removeFileVis.value = true
    // 重置选中状态
    selectedLeftKey.value = removeTreeData.value[0].id
    selectedLeftNode.value = removeTreeData.value[0]
    getTableVal()
    netDiskFileHome()
  }

  const getTableVal = () => {
    if (selectedLeftNode.value.name === '我的') {
      tabVal.value = 1
    } else {
      tabVal.value = 2
    }
  }

  const routesFormat = (routeList) => {
    return routeList.map(item => {
      const children = item.children && item.children.length ? routesFormat(item.children) : []
      return {
        name: item.meta.title,
        id: item.id,
        access: item.access,
        children,
        isOpen: true
      }
    })
  }

  // 处理一级菜单点击
  const handleNodeClick = (node) => {
    if (node.children && node.children.length) {
      // 有子菜单，切换展开/收起状态
      node.isOpen = !node.isOpen
    } else {
      // 没有子菜单，选中该节点
      selectedLeftKey.value = node.id
      selectedLeftNode.value = node
      selectedRightKey.value = undefined
      selectedRightNode.value = undefined
      getTableVal()
      netDiskFileHome()
    }
  }

  // 处理二级菜单点击
  const handleChildClick = (node) => {
    // 选中该节点
    selectedLeftKey.value = node.id
    selectedLeftNode.value = node
    selectedRightKey.value = undefined
    selectedRightNode.value = undefined
    getTableVal()
    netDiskFileHome()
  }

  // 点击右侧文件夹
  const handleItemClick = (item) => {
    selectedRightKey.value = item.id
    selectedRightNode.value = item
    netDiskFileHome()
  }
  const netDiskFileHome = () => {
    const params = {
      current: 1,
      size: 1000,
      tab: tabVal.value,
      sort: 2,
      search: '',
      parentId: getFolderId(),
    }
    Api.netDiskFileHome(params).then(res => {
      if (res.code === 0) {
        dataList.value = res.data.records
        parentPathList.value = res.data.parentPathList
      } else {
        $message.error(res.userMsg)
      }
    })
  }

  const cancel = () => {
    removeFileVis.value = false
    selectedLeftKey.value = undefined
    selectedLeftNode.value = undefined
    selectedRightKey.value = undefined
    selectedRightNode.value = undefined
  }

  const createHandle = () => {
    createVisible.value = true
    inputVal.value = ''
  }
  const getIds = () => {
    if (selectedRowKeys.value && selectedRowKeys.value.length) {
      return selectedRowKeys.value
    } else {
      return removeFileSingleInfo.value ? [removeFileSingleInfo.value?.id] : []
    }
  }
  const getFolderId = () => {
    // 先判断是否移动到右侧的子目录
    if (selectedRightNode.value?.id) {
      return selectedRightNode.value?.id
    } else if (selectedLeftNode.value?.id && selectedLeftNode.value?.name === '我的') { // 如果移动到我的目录下，不能带着id
      return ''
    } else if (selectedLeftNode.value?.id && selectedLeftNode.value?.id === 2) { // 公共的id是2，但是新建不能带着id
      return ''
    } else {
      return selectedLeftNode.value?.id
    }
  }
  const fileBatchMove = () => {
    if (!selectedRightNode.value?.id && !selectedLeftNode.value?.id) return $message.warning('请选择要移动到的位置')
    const params = {
      // 文件夹/文件 ID
      ids: getIds(),
      // 类型，1 我的，2 公共
      type: tabVal.value,
      // 目标文件夹ID
      folderId: getFolderId(),
    }
    Api.fileBatchMove(params).then(res => {
      if (res.code === 0) {
        $message.success('移动成功')
        cancel()
        fetchData()
        emit('fetchDataHandle')
      } else {
        $message.error(res.userMsg)
      }
    })
  }
  const ok = () => {
    if (!inputVal.value) return $message.warning('请输入名称')
    const params = {
      // 1 我的，2 公共
      type: tabVal.value,
      // 文件夹名称
      name: inputVal.value,
      // 上一层级文件夹id
      folderId: getFolderId(),
    }
    $indicator.open()
    Api.fileCreateFolder(params).then(res => {
      if (res.code === 0) {
        $message.success(res.userMsg)
        netDiskFileHome()
        cancelCreate()
        emit('fetchDataHandle')
        emit('netDiskFileHomeHandle')
      } else {
        $message.error(res.userMsg)
      }
    }).finally(() => {
      $indicator.close()
    })
  }
  const cancelCreate = () => {
    createVisible.value = false
    inputVal.value = ''
  }
  defineExpose({
    open
  })
</script>

<style lang="scss" scoped>
.move-file-wrap {
  height: 500px;
  .content {
    height: calc(100% - 46px);
    .content-left {
      width: 240px;
      border-right: 1px solid #e8e8e8;
      overflow-y: auto;

      .tree-menu {
        .tree-item {
          .tree-node {
            display: flex;
            align-items: center;
            padding: 10px 8px;
            cursor: pointer;

            &:hover {
              background-color: #f5f5f5;
            }

            &.tree-node-selected {
              color: #1890ff;
              background-color: #E7F3FF;
            }

            &.tree-node-parent {
              font-weight: 500;
            }

            &.tree-node-child {
              padding-left: 18px;
              padding-top: 5px;
              padding-bottom: 5px;
            }

            .tree-icon {
              width: 16px;
              display: inline-block;
              margin-right: 8px;
            }

            .tree-title {
              flex: 1;
            }
          }

          .tree-children {
            margin-left: 16px;
          }
        }
      }
    }
    .content-right {
      flex: 1;
      display: flex;
      flex-direction: column;
      .bread-crumb {
        padding: 0 15px;
        height: 40px;
        line-height: 40px;
        border-bottom: 1px solid #e8e8e8;
        :deep(.ant-breadcrumb) {
          line-height: 32px;
        }
      }
      .list-wrap {
        flex: 1;
        overflow-y: auto;
        .list-item {
          padding: 0 15px;
          height: 32px;
          line-height: 32px;
          border-bottom: 1px solid #e8e8e8;
          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
  }
  .footer {
    height: 45px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    border-top: 1px solid #e8e8e8;
  }
}
:deep(.ant-modal-body) {
  padding: 0;
}
.disabledPointer {
  cursor: not-allowed;
}
.createVisibleWrap {
  padding: 15px 20px;
}
.required {
  position: relative;
  &::before {
    content: '*';
    position: absolute;
    left: -10px;
    top: 3px;
    color: red;
  }
}
.canClick {
  &:hover{
    color: #1890ff;
  }
}
.breadcrumb-text {
  display: inline-block;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: top;
  color: #333;
}
.title{
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
