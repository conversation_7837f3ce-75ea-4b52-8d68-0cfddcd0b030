<template>
  <a-modal
    :visible="visible"
    :title="flag === 1 ? '创建文件夹' : '重命名'"
    :maskClosable="false"
    @ok="ok"
    @cancel="cancel"
  >
    <div class="flex flex-col">
      <p class="required mb-5">请输入名称</p>
      <a-input v-model="inputVal" placeholder="请输入" :maxLength="20" allowClear>
        <template #suffix>{{ inputVal && inputVal.length ? inputVal.length : 0 }} / 20</template>
      </a-input>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, defineProps, defineEmits, defineExpose, getCurrentInstance, computed, watch } from 'vue'
  import store from '~/store'
  import Api from '../../../../api/network-disk'

  const emit = defineEmits('fetchDataHandle')

  const { $message, $route, $indicator, $store } = getCurrentInstance().proxy

  const flag = computed(() => store.state.operation.networkDisk.renameFileStateFlag)
  const tabVal = computed(() => store.state.operation.networkDisk.tab)
  const renameFileInfo = computed(() => store.state.operation.networkDisk.renameFileInfo)
  const menuAddFileVal = computed(() => store.state.operation.networkDisk.menuAddFile)
  const folderIdVal = computed(() => store.state.operation.networkDisk.folderId)

  const visible = ref(false)
  const inputVal = ref('')
  const reNameId = ref(undefined)

  watch(renameFileInfo, (newVal) => {
    inputVal.value = newVal?.name || newVal?.meta?.title || ''
    reNameId.value = newVal?.id || undefined
  })

  const open = () => {
    visible.value = true
  }

  const cancel = () => {
    visible.value = false
    inputVal.value = ''
    reNameId.value = undefined
    $store.commit('operation/networkDisk/setRenameFileInfo', {})
    $store.commit('operation/networkDisk/setMenuAddFile', false)
  }

  const ok = () => {
    if (!inputVal.value) return $message.warning('请输入名称')
    if (flag.value === 1) {
      const params = {
        // 1 我的，2 公共
        type: tabVal.value,
        // 文件夹名称
        name: inputVal.value,
        // 上一层级文件夹id
        folderId: folderIdVal.value,
      }
      console.log('文件夹params', params)
      $indicator.open()
      Api.fileCreateFolder(params).then(res => {
        if (res.code === 0) {
          $message.success(res.userMsg)
          emit('fetchDataHandle')
          cancel()
        } else {
          $message.error(res.userMsg)
        }
      }).finally(() => {
        $indicator.close()
      })
    } else {
      const params = {
        id: reNameId.value,
        name: inputVal.value,
      }
      $indicator.open()
      Api.fileRename(params).then(res => {
        if (res.code === 0) {
          $message.success(res.userMsg)
          emit('fetchDataHandle')
          cancel()
        } else {
          $message.error(res.userMsg)
        }
      }).finally(() => {
        $indicator.close()
      })
    }
  }

  defineExpose({
    open
  })
</script>

<style lang="scss" scoped>
.required {
  position: relative;
  &::before {
    content: '*';
    position: absolute;
    left: -10px;
    top: 3px;
    color: red;
  }
}
</style>
