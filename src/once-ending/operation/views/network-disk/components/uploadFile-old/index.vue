<template>
  <div>
    <form
      style="display: none"
      ref="tagFileForm"
      name="tagFileForm">
      <input
        ref="tagFile"
        type="file"
        accept="image/*,.pdf,.pdfx,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.avi,.rmvb,.mpeg,.wmf,.mov,.mkv,.mp4,.mp3,.wav,.m4a,.zip"
        name="tagFile"
        multiple
        @change="selectTagFile"
      />
    </form>
    <div class="uploadListWrap" v-if="showUploadList">
      <div class="top-wrap flex flex-align-center flex-justify-between">
        <span class="font-14 bold">传输列表</span>
        <a-icon type="close" class="font-16 bold pointer" @click="closeUploadList"/>
      </div>
      <div class="uploadListCon">
        <div class="uploadListItem flex flex-align-center" v-for="(item, index) in selectedFileList" :key="index">
          <img :src="formatIco(item.files[0].name)" width="24px" height="24px" class="mr-16">
          <div class="flex-child-average flex flex-col">
            <p class="font-12 bold lines-1">{{ item.files[0].name }}</p>
            <a-progress :percent="item.files[0].progress" size="small" :status="item.files[0].status"/>
            <p class="font-12 grey-6">{{ formatBytes(item.files[0].size) }}</p>
          </div>
          <div class="flex-child-noshrink flex flex-align-center" style="width: 65px;">
            <a-icon type="delete" style="color: #1890ff; margin-left: 16px" v-if="item.files[0].status !== FILE_STATUS.SUCCESS" @click="delUploadItem(index)"/>
            <a-icon type="reload" style="color: #1890ff; margin-left: 16px" v-if="item.files[0].status === FILE_STATUS.EXCEPTION" @click="reloadUploadItem(index)"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, defineProps, defineExpose, getCurrentInstance, computed, watch, onBeforeUnmount } from 'vue'
  import { formatIco } from '../../constants'
  import nineUpload from '@jiuji/nine-upload'
  import Api from '../../../../api/network-disk'
  import uuid from 'uuid'
  import { useNetWorkDiskInject } from '../../useNetWorkDisk'

  const props = defineProps({
    fileMaxNum: {
      type: Number,
      default: 9999999999
    }
  })

  const { fetchData, folderId } = useNetWorkDiskInject()
  const typeVal = computed(() => {
    return $store.state.operation.networkDisk.tab
  })

  const { $message, $api, $set, $route, $store } = getCurrentInstance().proxy
  const FILE_STATUS = {
    SUCCESS: 'success',
    EXCEPTION: 'exception',
    NORMAL: 'normal',
    ACTIVE: 'active',
  }
  const tagFile = ref(null)
  const selectedFileList = ref([])
  const originalFiles = ref([]) // 保存原始文件对象，用于重新上传
  const getFileExtension = (filename) => {
    const match = filename && filename.match(/\.([^.]+)$/)
    return match ? match[1] : ''
  }
  const fileHandleChange = () => {
    // 不清空的话，当用户选择同样的文件时，change 事件不会触发
    tagFile.value.value = null
    // 点击文件选择器，触发文件选择
    // 注意：这里不会清空已有的文件列表，新选择的文件会追加到现有列表中
    tagFile.value.click()
  }

  const selectTagFile = async (event) => {
    $store.commit('operation/networkDisk/uploadFileLoading', true)
    const files = Array.from(event.target.files)
    let selectedFileListOriginal = []
    // 检查选择的文件数量和已有文件数量的总和是否超过最大限制
    const totalFiles = files.length + selectedFileList.value.length
    if (totalFiles > props.fileMaxNum) {
      // 计算还能添加多少个文件
      const remainingSlots = Math.max(0, props.fileMaxNum - selectedFileList.value.length)
      if (remainingSlots > 0) {
        $message.info(`最多能选择${remainingSlots}个文件，已自动保留前${remainingSlots}个新选择的文件！`)
        selectedFileListOriginal = files.slice(0, remainingSlots)
      } else {
        $message.info(`传输列表已达到最大文件数量上传限制(${props.fileMaxNum})，无法添加更多文件！`)
        $store.commit('operation/networkDisk/uploadFileLoading', false)
        return // 如果已经达到最大数量，直接返回
      }
    } else {
      selectedFileListOriginal = files
    }

    // 保存原始文件对象，追加到现有列表而不是替换
    originalFiles.value = [...originalFiles.value, ...selectedFileListOriginal]
    // 创建新选择文件的显示对象
    const newFileItems = selectedFileListOriginal.map(item => {
      return {
        type: typeVal.value,
        folderId: folderId.value,
        files: [
          {
            name: item.name,
            status: FILE_STATUS.NORMAL,
            size: item.size,
            progress: 0,
            fid: '',
            fileName: '',
            filePath: '',
            fileRelativePath: '',
          }
        ],
        isFinished: false,
      }
    })

    // 将新文件添加到现有列表中
    const startIndex = selectedFileList.value.length
    selectedFileList.value = [...selectedFileList.value, ...newFileItems]

    try {
      // 获取上传 token，只需获取一次
      let uploadData = window.nineUploadData
      if (!uploadData) {
        const { code, data, userMsg } = await $api.common.getUploadToken()
        if (code !== 0) {
          $message.error(userMsg)
          return
        }
        uploadData = data
        window.nineUploadData = data
        setTimeout(() => {
          window.nineUploadData = null
        }, 30 * 60 * 1000)
      }

      // 创建上传任务数组，只处理新添加的文件
      const uploadTasks = selectedFileListOriginal.map((file, i) => {
        const index = startIndex + i // 计算在完整列表中的索引
        return nineUpload({
          files: [file],
          onPickFiles: async () => uploadData,
          multiple: true,
          form: { collection: 'oa-net-drive' },
          chunkSize: 2048000,
          onProgress: ({ percent, fileIndex, fileCount }) => {
            $set(selectedFileList.value[index].files[0], 'status', FILE_STATUS.ACTIVE)
            $set(selectedFileList.value[index].files[0], 'progress', percent)
            if (percent >= 99) {
              $set(selectedFileList.value[index].files[0], 'status', FILE_STATUS.SUCCESS)
              $set(selectedFileList.value[index].files[0], 'progress', 100)
            }
          },
        })
      })

      // 并行执行所有上传任务
      const results = await Promise.all(uploadTasks)

      // 处理上传结果，注意索引偏移
      results.forEach(({ err, res }, i) => {
        console.log('err-err-err1', err)
        console.log('res-res-res1', res)
        const index = startIndex + i // 计算在完整列表中的索引
        if (err && err.length) {
          console.log('err', err)
          // 注意：这里不能直接 splice，因为会影响后续索引
          // 标记为失败状态，稍后统一处理
          $set(selectedFileList.value[index].files[0], 'status', FILE_STATUS.EXCEPTION)
          $set(selectedFileList.value[index].files[0], 'error', `${err[0].name}上传失败,${err[0].err.message}`)
        }

        if (res && res.length) {
          $set(selectedFileList.value[index].files[0], 'fid', res[0].fid)
          $set(selectedFileList.value[index].files[0], 'fileName', res[0].fileName || res[0].filename)
          $set(selectedFileList.value[index].files[0], 'filePath', res[0].filePath || res[0].downloadPath)
          $set(selectedFileList.value[index].files[0], 'fileSize', res[0].size)
          $set(selectedFileList.value[index].files[0], 'suffix', getFileExtension(res[0].fileName || res[0].filename))
          $set(selectedFileList.value[index].files[0], 'fileRelativePath', res[0].fileRelativePath)
          // 上传成功后调用fileUploadFile方法将文件信息提交到服务器
          fileUploadFile(index)
        }
      })

      // 处理失败的文件
      const failedFiles = selectedFileList.value.filter(file => file.files[0].status === FILE_STATUS.EXCEPTION)
      if (failedFiles.length > 0) {
        // 显示第一个错误信息
        $message.error(failedFiles[0].files[0].error)
        // 不从列表中移除失败的文件，用户可以点击重新上传
      }
    } catch (error) {
      console.error('上传过程发生错误:', error)
      $message.error('上传过程发生错误，请重试')
    }
  }
  const formatBytes = (bytes, decimals = 2) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i]
  }
  const showUploadList = ref(false)
  const closeUploadList = () => {
    // 关闭上传列表时，完全清空所有文件列表
    // 这样下次上传时会重新开始，而不是追加
    selectedFileList.value = []
    originalFiles.value = [] // 清空原始文件对象
    showUploadList.value = false
    // 重置上传状态
    $store.commit('operation/networkDisk/uploadFileLoading', false)
    if (tagFile.value) {
      tagFile.value.value = null
    }
  }
  const delUploadItem = (index) => {
    // 删除显示列表中的项
    selectedFileList.value.splice(index, 1)

    // 同步删除原始文件对象
    // 注意：originalFiles中的索引与selectedFileList保持一致
    originalFiles.value.splice(index, 1)

    // 如果删除后列表为空，可以考虑重置状态
    if (selectedFileList.value.length === 0) {
      // 列表为空时重置上传状态
      $store.commit('operation/networkDisk/uploadFileLoading', false)
    } else {
      // 检查剩余文件是否都已完成上传
      checkAllFilesFinished()
    }
  }
  const reloadUploadItem = async (index) => {
    // 获取需要重新上传的文件信息
    const fileItem = selectedFileList.value[index]
    if (!fileItem || fileItem.files[0].status !== FILE_STATUS.EXCEPTION) {
      return
    }

    // 重置文件状态
    $set(selectedFileList.value[index].files[0], 'status', FILE_STATUS.NORMAL)
    $set(selectedFileList.value[index].files[0], 'progress', 0)
    $set(selectedFileList.value[index].files[0], 'error', '')

    try {
      // 获取上传 token
      let uploadData = window.nineUploadData
      if (!uploadData) {
        const { code, data, userMsg } = await $api.common.getUploadToken()
        if (code !== 0) {
          $message.error(userMsg)
          return
        }
        uploadData = data
        window.nineUploadData = data
        setTimeout(() => {
          window.nineUploadData = null
        }, 30 * 60 * 1000)
      }

      // 从原始文件列表中找到对应的文件对象
      const originalFile = originalFiles.value[index]
      if (!originalFile) {
        $message.error('找不到原始文件，请重新选择文件上传')
        return fileHandleChange()
      }

      // 使用原始文件对象重新上传
      const { err, res } = await nineUpload({
        files: [originalFile],
        onPickFiles: async () => uploadData,
        multiple: true,
        form: { collection: 'oa-net-drive' },
        onProgress: ({ percent, fileIndex, fileCount }) => {
          console.log(percent, fileIndex, fileCount)
          $set(selectedFileList.value[index].files[0], 'status', FILE_STATUS.ACTIVE)
          $set(selectedFileList.value[index].files[0], 'progress', percent)
          if (percent >= 99) {
            $set(selectedFileList.value[index].files[0], 'status', FILE_STATUS.SUCCESS)
            $set(selectedFileList.value[index].files[0], 'progress', 100)
          }
        },
      })
      console.log('err-err-err2', err)
      console.log('res-res-res2', res)
      if (err && err.length) {
        $set(selectedFileList.value[index].files[0], 'status', FILE_STATUS.EXCEPTION)
        $set(selectedFileList.value[index].files[0], 'error', `${err[0].name}上传失败,${err[0].err.message}`)
        $message.error(`${err[0].name}上传失败,${err[0].err.message}`)
      }
      if (res && res.length) {
        $set(selectedFileList.value[index].files[0], 'fid', res[0].fid)
        $set(selectedFileList.value[index].files[0], 'fileName', res[0].fileName || res[0].filename)
        $set(selectedFileList.value[index].files[0], 'filePath', res[0].filePath || res[0].downloadPath)
        $set(selectedFileList.value[index].files[0], 'fileSize', res[0].size)
        $set(selectedFileList.value[index].files[0], 'suffix', getFileExtension(res[0].fileName || res[0].filename))
        $set(selectedFileList.value[index].files[0], 'fileRelativePath', res[0].fileRelativePath)
        // 重置isFinished状态，因为需要重新上传到服务器
        $set(selectedFileList.value[index], 'isFinished', false)
        // 重新上传成功后调用fileUploadFile方法将文件信息提交到服务器
        fileUploadFile(index)
      }
    } catch (error) {
      console.error('重新上传过程发生错误:', error)
      $message.error('重新上传过程发生错误，请重试')
      $set(selectedFileList.value[index].files[0], 'status', FILE_STATUS.EXCEPTION)
    }
  }
  const fileUploadFile = (index) => {
    console.log('selectedFileList.value', selectedFileList.value)
    const params = {
      files: {
        fid: selectedFileList.value[index].files[0].fid,
        fileName: selectedFileList.value[index].files[0].fileName,
        filePath: selectedFileList.value[index].files[0].filePath,
        fileSize: selectedFileList.value[index].files[0].fileSize || selectedFileList.value[index].files[0].size,
        suffix: selectedFileList.value[index].files[0].suffix,
        fileRelativePath: selectedFileList.value[index].files[0].fileRelativePath,
      },
      type: selectedFileList.value[index].files[0]?.type || typeVal.value,
      folderId: selectedFileList.value[index].files[0]?.folderId || folderId.value,
      t: Date.now() + uuid(),
    }
    console.log('params', params)
    Api.fileUploadFile(params).then(res => {
      if (res.code !== 0) {
        // 如果服务器处理失败，重置状态并显示错误信息
        $set(selectedFileList.value[index].files[0], 'status', FILE_STATUS.EXCEPTION)
        $set(selectedFileList.value[index].files[0], 'progress', 100) // 保持进度为100%，但状态为异常
        $set(selectedFileList.value[index].files[0], 'error', res.userMsg || '文件上传到服务器失败')
        $message.error(res.userMsg || '文件上传到服务器失败')
      } else {
        // 服务器处理成功
        $set(selectedFileList.value[index], 'isFinished', true)
        $set(selectedFileList.value[index].files[0], 'status', FILE_STATUS.SUCCESS)
        $set(selectedFileList.value[index].files[0], 'progress', 100)
        fetchData()
        // 检查是否所有文件都已上传完成
        checkAllFilesFinished()
      }
    }).catch(error => {
      // 处理API调用异常
      console.error('文件上传到服务器出错:', error)
      $set(selectedFileList.value[index].files[0], 'status', FILE_STATUS.EXCEPTION)
      $set(selectedFileList.value[index].files[0], 'error', '文件上传到服务器出错')
      $message.error('文件上传到服务器出错，请重试')
    }).catch(error => {
      // 处理API调用异常
      console.error('文件上传到服务器出错:', error)
      $set(selectedFileList.value[index].files[0], 'status', 'exception')
      $set(selectedFileList.value[index].files[0], 'error', '文件上传到服务器出错')
      $message.error('文件上传到服务器出错，请重试')
    }).finally(() => {
      $store.commit('operation/networkDisk/uploadFileLoading', false)
    })
  }
  // 检查所有文件是否都已上传完成
  const checkAllFilesFinished = () => {
    if (selectedFileList.value.length === 0) {
      $store.commit('operation/networkDisk/uploadFileLoading', false)
      return
    }

    const allFinished = selectedFileList.value.every(item => item.isFinished === true)
    if (allFinished) {
      closeUploadList()
    }
  }

  // 监听selectedFileList的变化，当列表为空时重置uploadLoading
  watch(() => selectedFileList.value.length, (newLength) => {
    if (newLength > 0) {
      showUploadList.value = true
    }
    if (newLength === 0) {
      $store.commit('operation/networkDisk/uploadFileLoading', false)
    }
  })
  onBeforeUnmount(() => {
    closeUploadList()
  })
  defineExpose({
    fileHandleChange,
    closeUploadList,
  })
</script>

<style lang="scss" scoped>
.uploadListWrap {
  position: absolute;
  box-sizing: border-box;
  top: 116px;
  right: 20px;
  z-index: 1000;
  width: 500px;
  max-height: 640px;
  overflow-y: auto;
  background: #FFFFFF;
  border-radius: 6px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  .top-wrap {
    padding: 10px 15px;
    border-bottom: 1px solid #F0F0F0;
    position: sticky;
    top: 0;
    background: #FFFFFF;
    z-index: 999;
  }
  .uploadListCon {
    height: calc(100% - 42px);
    overflow-y: auto;
    .uploadListItem {
      padding: 10px 15px;
      border-bottom: 1px solid #F0F0F0;
    }
  }
}
</style>
