<template>
  <div v-if="disabled" class="access-dropdown">{{getLabel(true,localValue)}}</div>
  <a-dropdown v-else trigger="click">
    <div class="access-dropdown pointer">{{getLabel(false,localValue)}}<a-icon type="down" /></div>
    <a-menu slot="overlay"  @click="handleMenuClick">
      <a-menu-item v-for="item in showAccessOptions" :key="item.value">
        <div class="bold">{{item.label}}</div>
        <div class="tip grey-9">{{item.tip}}</div>
      </a-menu-item>
    </a-menu>
  </a-dropdown>
</template>

<script setup>
  import { defineProps, defineEmits, computed, ref, watch } from 'vue'
  import { accessOptions, getLabel } from './constants'
  const props = defineProps({
    value: {
      type: Number,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    level: {
      type: Number,
      default: 1
    },
    superAdmin: {
      type: Boolean,
      default: false
    },
    isShare: { // 操作权限的角色是否是共享成员
      type: Boolean,
      default: false
    },
    isSpace: {
      type: Boolean,
      default: false
    },
    isBatch: {
      type: Boolean,
      default: false
    }
  })

  const emits = defineEmits(['input'])

  const localValue = ref(undefined)

  watch(() => props.value, val => {
    localValue.value = val
  }, { immediate: true })

  const showAccessOptions = computed(() => accessOptions(props.level, props.superAdmin, props.isShare, props.isSpace, props.isBatch))

  const handleMenuClick = function (item) {
    localValue.value = item.key
    emits('input', localValue.value)
  }

</script>

<style scoped lang="scss">

</style>
