<template>
  <a-modal :visible="visible" title="成员权限管理" @cancel="cancel" :afterClose="afterClose">
    <div class="search-box">
      <a-input placeholder="请输入员工id/组织id或姓名/组织名称搜索" class="full-width" v-model="showSearchKey" allowClear @input="onSearch">
        <template #prefix>
          <a-icon style="color:#9c9c9c" type="search" />
        </template>
      </a-input>
    </div>
    <div class="flex flex-align-center flex-justify-between mt-10">
      <a-checkbox :disabled="checkAllDisabled" :checked="checkAll" @change="checkAllChange">全选</a-checkbox>
      <div class="add pointer" @click="showSelectMemberModal = true"><a-icon class="icon" type="plus-square" />添加成员</div>
    </div>
      <div class="member-box mt-10">
        <div class="label">空间成员：{{noRemoveSpaceMembers.length}}人</div>
        <div v-if="showSpaceMembers.length" class="list-box mt-10">
          <div class="list-item flex flex-align-center flex-justify-between mt-8" v-for="(item,index) in showSpaceMembers" :key="index">
            <div class="flex flex-align-center">
              <a-checkbox class="flex-child-noshrink mr-12" :disabled="!item.editable" v-model="item.checked" />
              <div class="flex flex-align-center">
                <ni-img class="associated-img flex-child-noshrink mr-12" :style="item.associatedType === 1 ? 'border-radius:50%' : ''" :src="item.associatedType === 1 ? item.associatedImg : organization"/>
                <div class="flex-child-average">
                  <div class="name">{{item.associatedName}}<span v-if="item.associatedType === 1">（{{item.area}}，{{item.roleName}}）</span></div>
                </div>
              </div>
            </div>
            <div class="flex-child-noshrink ml-12">
              <access-dropdown :level="level" :super-admin="isSuperAdmin" :is-share="false" :is-space="true" :disabled="!item.editable" :is-batch="false" v-model="item.access"/>
            </div>
          </div>
        </div>
        <a-empty v-else :description="searchKey ? '未筛选到空间成员,请重新筛选' : '暂无空间成员'"/>
      </div>
    <div class="member-box mt-10" v-if="showAddShareMember">
      <div class="label">共享成员：{{noRemoveSharedMembers.length}}人</div>
      <div v-if="showSharedMembers.length" class="list-box mt-10">
        <div class="list-item flex flex-align-center flex-justify-between mt-8" v-for="(item,index) in showSharedMembers" :key="index">
          <div class="flex flex-align-center">
            <a-checkbox class="flex-child-noshrink mr-12" :disabled="!item.editable" v-model="item.checked" />
            <div class="flex flex-align-center">
              <ni-img class="associated-img flex-child-noshrink mr-12" :style="item.associatedType === 1 ? 'border-radius:50%' : ''" :src="item.associatedType === 1 ? item.associatedImg : organization"/>
              <div class="flex-child-average">
                <div class="name">{{item.associatedName}}<span v-if="item.associatedType === 1">（{{item.area}}，{{item.roleName}}）</span></div>
              </div>
            </div>
          </div>
          <div class="flex-child-noshrink ml-12">
            <access-dropdown :level="level" :super-admin="isSuperAdmin" :is-share="true" :is-space="false" :disabled="!item.editable" :is-batch="false" v-model="item.access"/>
          </div>
        </div>
      </div>
      <a-empty v-else :description="searchKey ? '未筛选到共享成员,请重新筛选' : '暂无共享成员'"/>
    </div>
    <template #footer>
      <div class="footer flex flex-align-center flex-justify-between">
        <div style="min-width: 1px">
          <div v-if="showBatchAccess">
            <div class="flex flex-align-center">
              <span>将选中成员权限设置为：</span><access-dropdown :level="level" :super-admin="isSuperAdmin" :is-share="isShare" :is-space="isSpace" :disabled="false" :is-batch="true" v-model="batchAccess" @input="batchAccessChange"/>
            </div>
          </div>
        </div>
        <div class="flex flex-align-center">
          <a-button @click="cancel" class="mr-8">取消</a-button>
          <a-button @click="ok" type="primary">确定</a-button>
        </div>
      </div>
    </template>
    <select-member-modal v-model="showSelectMemberModal" :disabledIds="disabledIds" @finish="onFinish"></select-member-modal>
  </a-modal>
</template>

<script setup>
  import { ref, computed, getCurrentInstance, watch, defineEmits } from 'vue'
  import { debounce } from 'lodash'
  import AccessDropdown from './access-dropdown'
  import { NiImg } from '@jiuji/nine-ui'
  import organization from '~/assets/images/network-disk/organization.png'
  import api from '@operation/api/network-disk'
  import { message } from 'ant-design-vue'
  import SelectMemberModal from './select-member-modal'

  const { proxy } = getCurrentInstance()

  const emits = defineEmits(['success'])

  const visible = computed({
    get: () => proxy.$store.state.operation.networkDisk.permissionVisible,
    set: (val) => {
      proxy.$store.commit('operation/networkDisk/setPermissionVisible', val)
    }
  })

  watch(() => visible.value, val => {
    if (val) {
      getMemberInfo()
    }
  })

  const memberInfo = ref({
    level: 1,
    access: 2,
    admin: false,
    addMemberAccess: 3,
    showAddShareMember: false,
    showAddSpaceMember: false,
    sharedMembers: [],
    spaceMembers: []
  })

  const showSelectMemberModal = ref(false)

  const batchAccess = ref(undefined)

  const showSearchKey = ref(undefined)

  const searchKey = ref('')

  const isSuperAdmin = computed(() => memberInfo.value.access === 6)

  const isAdmin = computed(() => memberInfo.value.admin)

  const level = computed(() => memberInfo.value.level)

  const showAddShareMember = computed(() => memberInfo.value.showAddShareMember)

  const noRemoveSpaceMembers = computed(() => memberInfo.value.spaceMembers.filter(d => d.access !== -2))

  const showSpaceMembers = computed(() => {
    if (!searchKey.value) return noRemoveSpaceMembers.value
    return noRemoveSpaceMembers.value.filter(d => d.associatedName.toLocaleLowerCase().includes(searchKey.value) || `${d.associatedId}`.toLocaleLowerCase().includes(searchKey.value))
  })

  const noRemoveSharedMembers = computed(() => memberInfo.value.sharedMembers.filter(d => d.access !== -2))

  const showSharedMembers = computed(() => {
    if (!searchKey.value) return noRemoveSharedMembers.value
    return noRemoveSharedMembers.value.filter(d => d.associatedName.toLocaleLowerCase().includes(searchKey.value) || `${d.associatedId}`.toLocaleLowerCase().includes(searchKey.value))
  })

  const disabledIds = computed(() => {
    return [...noRemoveSharedMembers.value.map(d => `${d.associatedId}_${d.associatedType === 1 ? 2 : 1}`), ...noRemoveSpaceMembers.value.map(d => `${d.associatedId}_${d.associatedType === 1 ? 2 : 1}`)]
  })

  const checkAll = computed(() => {
    const noDisabledSpaceMembers = noRemoveSpaceMembers.value.filter(d => d.editable)
    const noDisabledSharedMembers = noRemoveSharedMembers.value.filter(d => d.editable)
    return !checkAllDisabled.value && noDisabledSpaceMembers.every(d => d.checked) && noDisabledSharedMembers.every(d => d.checked)
  })

  const checkAllDisabled = computed(() => {
    const noDisabledSpaceMembers = noRemoveSpaceMembers.value.filter(d => d.editable)
    const noDisabledSharedMembers = noRemoveSharedMembers.value.filter(d => d.editable)
    return !noDisabledSpaceMembers.length && !noDisabledSharedMembers.length
  })

  const showBatchAccess = computed(() => {
    return noRemoveSpaceMembers.value.some(d => d.checked) || noRemoveSharedMembers.value.some(d => d.checked)
  })

  const isShare = computed(() => {
    return !noRemoveSpaceMembers.value.some(d => d.checked)
  })

  const isSpace = computed(() => {
    return !noRemoveSharedMembers.value.some(d => d.checked)
  })

  watch(() => showBatchAccess.value, val => {
    if (!val) batchAccess.value = undefined
  })

  const batchAccessChange = function (val) {
    memberInfo.value.spaceMembers.map(d => {
      if (d.checked) d.access = val
    })
    memberInfo.value.sharedMembers.map(d => {
      if (d.checked) d.access = val
    })
  }

  const getMemberInfo = async function () {
    const params = {
      fileId: proxy.$store.state.operation.networkDisk.permissionFolderId
    }
    const res = await api.getMemberInfo(params).catch(err => {
      message.error(err.message)
    })
    if (res) {
      res.spaceMembers.map(d => {
        d.checked = false
        d.olgAccess = d.access
      })
      res.sharedMembers.map(d => {
        d.checked = false
        d.olgAccess = d.access
      })
      memberInfo.value = res
    }
  }

  const setCheckedAll = function (checked) {
    noRemoveSpaceMembers.value.map(d => {
      if (d.editable) d.checked = checked
    })
    noRemoveSharedMembers.value.map(d => {
      if (d.editable) d.checked = checked
    })
  }

  const checkAllChange = function (e) {
    setCheckedAll(e.target.checked)
  }

  const onSearch = debounce(function () {
    searchKey.value = showSearchKey.value ? showSearchKey.value.toLocaleLowerCase() : ''
  }, 400)

  const onFinish = function (ids, selectMember) {
    const key = showAddShareMember.value ? 'sharedMembers' : 'spaceMembers'
    selectMember.map(d => {
      const { id, name, dataType, headImg, area, zhiWu } = d
      const item = memberInfo.value[key].find(d => d.associatedId === id)
      if (!item) {
        memberInfo.value[key].push({
          editable: true,
          access: memberInfo.value.addMemberAccess,
          associatedId: id,
          associatedName: name,
          associatedType: dataType === 2 ? 1 : 2,
          checked: false,
          associatedImg: headImg,
          area,
          roleName: zhiWu
        })
      } else if (item.access === -2) { // 移除的人如果再选择添加,默认把权限改了就行
        item.access = 2
      }
    })
  }

  const ok = async function () {
    const { spaceMembers, sharedMembers } = memberInfo.value
    if (spaceMembers.concat(sharedMembers).filter(d => d.associatedId !== proxy.$store.state.userInfo.UserID && d.access === 6).length > 1) {
      return message.warning('只能设置一个人为超级管理员，请重新选择')
    }
    const params = {
      fileId: proxy.$store.state.operation.networkDisk.permissionFolderId,
      spaceMembers: spaceMembers.filter(d => d.olgAccess !== d.access),
      sharedMembers: sharedMembers.filter(d => d.olgAccess !== d.access)
    }
    const res = await api.saveMemberInfo(params).catch(err => {
      message.error(err.message)
    })
    if (res) {
      message.success('保存成功')
      visible.value = false
      emits('success')
    }
  }

  const cancel = function () {
    visible.value = false
  }

  const afterClose = function () {
    showSearchKey.value = undefined
    searchKey.value = ''
    batchAccess.value = undefined
    showSelectMemberModal.value = false
    memberInfo.value = {
      admin: false,
      sharedMembers: [],
      spaceMembers: []
    }
  }

</script>

<style scoped lang="scss">
:deep(.ant-modal-body){
  max-height: 60vh;
  overflow-y: auto;
  overflow-x: hidden;
}
.add{
  color: #1890ff;
  .icon{
    margin-right: 4px;
  }
}
.associated-img{
  width: 32px;
  height: 32px;
}
</style>
