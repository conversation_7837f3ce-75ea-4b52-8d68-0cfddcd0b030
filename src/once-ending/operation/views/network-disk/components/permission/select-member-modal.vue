<template>
  <a-modal title="选择成员" width="1024px" :body-style="{ padding: '0px'}" v-model="visible" @ok="ok" @cancel="cancel" :afterClose="afterClose">
    <div class="flex content">
      <div class="left-box">
        <div class="search-box">
          <a-input placeholder="请输入员工id/组织id或员工姓名/组织名称搜索" allowClear class="full-width" v-model="showSearchKey" @input="onSearch">
            <template #prefix>
              <a-icon style="color:#9c9c9c" type="search" />
            </template>
          </a-input>
        </div>
        <template v-if="searchKey">
          <div class="filter-list emp-share-emp-list-box">
            <recycle-scroller
              v-if="showFilterList.length > 0"
              v-slot="{ item }"
              class="scroller"
              :items="showFilterList"
              :item-size="60"
              key-field="id"
              item-tag="div"
            >
              <div class="item flex flex-align-center">
                <a-checkbox
                  :checked="checkedKeys.includes(item.id) || item.disableCheckbox"
                  :disabled="item.disableCheckbox"
                  style="width: 100%"
                  @change="(e) => handleCheckChange(e, item)"
                >
                  <div class="user-box flex">
                    <a-avatar v-if="item.dataType === 2" :size="44" :src="item.headImg + '?width=200'" />
                    <div v-else style="width:44px;height: 44px" class="flex flex-align-center flex-justify-center">
                      <img :width="25" :src="departIcon" alt=""/>
                    </div>
                    <div class="user-info" v-if="item.dataType === 2">
                      <div class="user-name">
                        {{ item.name }}
                        <label class="font-12 ml-0.5">({{ item.area }})</label>
                      </div>
                      <div class="user-department">
                        <div class="zhiWu">{{ item.zhiWu }}</div>
                        {{ item.zhiJi }}
                      </div>
                    </div>
                    <div v-else style="color:#333" class="depart-info flex flex-align-center ml-8 bold font-15">{{item.name}}<div class="font-12 ml-0.5">（编制{{ item.bianzhi }}/在职{{ item.onDuty }}）</div></div>
                  </div>
                </a-checkbox>
              </div>
            </recycle-scroller>
            <a-empty v-else description="无数据"/>
          </div>
        </template>
        <template v-else>
          <div class="left-content-box">
            <div class="contactPerson-box">
              <div class="title bold flex flex-align-center" @click="openContactPerson = !openContactPerson">
                <img class="inline-block" :class="{ 'rotate-90': openContactPerson}" :src="arrowDownIcon" alt="常用联系人">
                常用联系人
              </div>
              <div class="emp-share-emp-list-box" :class="openContactPerson ? 'showEmpList' : 'hideEmpList'">
                <recycle-scroller
                  v-if="imRecent.length > 0"
                  v-slot="{ item }"
                  class="scroller"
                  :items="imRecent"
                  :item-size="60"
                  key-field="id"
                  item-tag="div"
                >
                  <div class="item flex flex-align-center">
                    <a-checkbox
                      :checked="checkedKeys.includes(item.id) || item.disableCheckbox"
                      :disabled="item.disableCheckbox"
                      style="width: 100%"
                      @change="(e) => handleCheckChange(e, item)"
                    >
                      <div class="user-box flex">
                        <a-avatar :size="44" :src="item.headImg + '?width=200'" />
                        <div class="user-info">
                          <div class="user-name">
                            {{ item.ch999Name }}
                            <label class="font-12 ml-0.5">({{ item.area }})</label>
                          </div>
                          <div class="user-department" style="margin-top: 0">
                            <div class="zhiWu">{{ item.zhiWu }}</div>
                            {{ item.zhiJi }}
                          </div>
                        </div>
                      </div>
                    </a-checkbox>
                  </div>
                </recycle-scroller>
                <a-empty v-else description="暂无常用联系人"/>
              </div>
            </div>
            <div class="depart-box mt-10">
              <div class="title bold flex flex-align-center">
                组织架构
              </div>
              <div class="tree-box">
                <a-tree
                  ref="treeWrapper"
                  :checkedKeys="showCheckedKeys"
                  checkable
                  :tree-data="organization"
                  block-node
                  :checkStrictly="selectType === 0"
                  :selectable="false"
                  :show-icon="true"
                  :replaceFields="{ children:'children', title:'name', key:'id' }"
                  @check="onBatchCheck"
                >
                  <template #title="{ dataType,headImg,name,area,zhiWu,zhiJi,workKeys,bianzhi,onDuty }">
                    <template v-if="dataType === 2">
                      <div class="flex flex-align-center user-item">
                        <a-avatar class="flex-child-noshrink headImg" v-if="dataType === 2" :size="44" :src="headImg + '?width=200'" />
                        <div class="user-info">
                          <div class="name-box flex flex-align-end"><div class="name">{{name}}</div><div class="area">({{ area }})</div></div>
                          <div class="user-department" style="margin-top: 0">
                            <div class="zhiWu">{{ zhiWu || '未知' }}</div>
                            {{ zhiJi }}
                          </div>
                          <div class="full-width">
                            <div class="full-width zhiNeng lines-1">
                              职能：{{ workKeys || '-' }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>
                    <template v-else>
                      <div class="flex flex-align-center depart-item">
                        <img :width="25" :src="departIcon" alt=""/>
                        <div class="name">{{ name }}</div>
                        <div class="bianzhi">（编制{{ bianzhi }}/在职{{ onDuty }}）</div>
                      </div>
                    </template>
                  </template>
                </a-tree>
              </div>
            </div>
          </div>
        </template>

      </div>
      <div class="line"></div>
      <div class="right-box">
        <div class="select-info-header flex flex-align-center flex-justify-between">
          <div class="select-text">已选中 {{checkedKeys.length}} 个联系人</div>
          <div class="clear-text" @click="clearAll">清空</div>
        </div>
        <div class="filter-list emp-share-emp-list-box select-member flex flex-col">
          <recycle-scroller
            v-slot="{ item }"
            class="scroller flex-child-average"
            :items="showRightData"
            :item-size="60"
            key-field="id"
            item-tag="div"
          >
            <div class="item flex flex-align-center flex-justify-between">
              <div class="user-box flex flex-align-center">
                <a-avatar v-if="item.dataType === 2" :size="44" :src="item.headImg + '?width=200'" />
                <div v-else style="width:44px;height: 44px" class="flex flex-align-center flex-justify-center">
                  <img :width="25" :src="departIcon" alt=""/>
                </div>
                <div class="user-info" v-if="item.dataType === 2">
                  <div class="user-name">
                    {{ item.name }}
                    <label class="font-12 ml-0.5">({{ item.area }})</label>
                  </div>
                  <div class="user-department">
                    <div class="zhiWu">{{ item.zhiWu }}</div>
                    {{ item.zhiJi }}
                  </div>
                </div>
                <div v-else style="color:#333" class="depart-info flex flex-align-center ml-8 bold font-15">{{item.name}}<div class="font-12 ml-0.5">（编制{{ item.bianzhi }}/在职{{ item.onDuty }}）</div></div>
              </div>
              <img width="16" @click="clear(item)" class="mr-12 clear pointer" :src="closeFillIcon" alt="">
            </div>
          </recycle-scroller>
          <div class="forward-wrap" v-if="forwardInfo && forwardInfo.length">
            <div class="name-ico-wrap flex flex-col">
              <div class="name-ico">
                <div class="flex flex-align-center file-info">
                  <div class="file-icon-wrapper">
                    <img :src="previewImg(forwardInfo[0]?.fileLink) ? forwardInfo[0]?.fileLink : formatIco(forwardInfo[0]?.fileLink)" class="file-icon">
                  </div>
                  <div class="flex-child-average lines-1 file-name">{{ forwardInfo[0].name || forwardInfo[0].meta.title }}</div>
                </div>
                <div class="flex flex-align-center flex-justify-between file-meta">
                  <div class="flex flex-align-center creator-info">
                    <span>{{  forwardInfo[0].createUserName }}</span>
                    <span v-if="forwardInfo[0].createUserName" class="creator-label">创建</span>
                  </div>
                  <div class="flex flex-align-center flex-justify-center doc-count">共{{ forwardInfo.length }}个文档</div>
                </div>
              </div>
              <div class="flex flex-col message-input-wrapper">
                <a-textarea v-model="forwardInput" placeholder="请输入留言" :max-length="100" @change="forwardInputChange" allowClear class="message-input"/>
                <div class="flex flex-align-center flex-justify-end char-count">
                  <span>{{ forwardInput && forwardInput.length ? forwardInput.length : 0 }} / 100</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
  import { defineEmits, defineProps, ref, computed, getCurrentInstance } from 'vue'
  import { debounce, cloneDeep } from 'lodash'
  import arrowDownIcon from '~/assets/images/network-disk/arrow-right-fill.png'
  import { RecycleScroller } from 'vue-virtual-scroller'
  import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
  import departIcon from '~/assets/images/network-disk/depart.png'
  import { treeIntoList, findParentIntree } from '~/util/common'
  import closeFillIcon from '~/assets/images/network-disk/close-fill.png'
  import { message } from 'ant-design-vue'
  import { previewImg, formatIco } from '../../constants'
  import JTextarea from '@hr/components/j-textarea.vue'
  const props = defineProps({
    value: {
      type: Boolean,
      default: false
    },
    disabledIds: {
      type: Array,
      default: () => ([])
    },
    selectType: { // 0 全部 1 人
      type: Number,
      default: 0
    }
  })

  const emits = defineEmits(['input', 'finish'])

  const { proxy } = getCurrentInstance()

  const visible = computed({
    get: () => props.value,
    set: val => {
      emits('input', val)
    }
  })

  const openContactPerson = ref(true)

  const selectMember = ref([])

  const checkedKeys = ref([])

  const showSearchKey = ref(undefined)

  const searchKey = ref('')

  const disabledIds = computed(() => props.disabledIds.filter(d => props.selectType === 0 || (props.selectType === 1 && d.split('_')[1] === '2')))
  const forwardInfo = computed(() => proxy.$store.state.operation.networkDisk.forwardInfo)
  const forwardInput = ref('')

  const changeOrganization = function (arr) {
    return arr.map(d => {
      d.orgId = d.id
      d.id = `${d.orgId}_${d.dataType}`
      d.disableCheckbox = disabledIds.value.includes(d.id)
      d.class = d.dataType === 1 ? 'depart-box' : 'user-box'
      d.scopedSlots = {
        icon: 'custom'
      }
      if (d.children && d.children.length) {
        d.children = changeOrganization(d.children)
      }
      return d
    })
  }

  const organization = computed(() => {
    const orgOrganization = cloneDeep(proxy.$store.state.operation.networkDisk.organization)
    return changeOrganization(orgOrganization)
  })

  const imRecent = computed(() => {
    const orgImRecent = cloneDeep(proxy.$store.state.operation.networkDisk.imRecent)
    return orgImRecent.map(d => {
      d.orgId = d.id
      d.id = `${d.orgId}_${d.dataType}`
      d.disableCheckbox = disabledIds.value.includes(d.id)
      return d
    })
  })

  const showRightData = computed(() => selectMember.value.filter(d => props.selectType === 0 || (props.selectType === 1 && d.dataType === 2)))

  const handleCheckChange = function (e, item) {
    const { id } = item
    if (e.target.checked) {
      if (!checkedKeys.value.includes(id)) {
        checkedKeys.value.push(id)
      }
      const selectMemberIds = selectMember.value.map(d => d.id)
      if (!selectMemberIds.includes(id)) {
        selectMember.value.push(item)
      }
    } else {
      const idIndex = checkedKeys.value.findIndex(d => d === id)
      if (idIndex > -1) checkedKeys.value.splice(idIndex, 1)
      const index = selectMember.value.findIndex(d => d.id === id)
      if (index > -1) selectMember.value.splice(index, 1)
    }
  }

  const onBatchCheck = function (_, { checked, node }) {
    if (props.selectType === 0) {
      const data = node.$vnode.data.props.dataRef
      const { id } = data
      if (checked) {
        if (!checkedKeys.value.includes(id)) {
          checkedKeys.value.push(id)
        }
        const selectMemberIds = selectMember.value.map(d => d.id)
        if (!selectMemberIds.includes(id)) {
          selectMember.value.push(data)
        }
      } else {
        const idIndex = checkedKeys.value.findIndex(d => d === id)
        if (idIndex > -1) checkedKeys.value.splice(idIndex, 1)
        const index = selectMember.value.findIndex(d => d.id === id)
        if (index > -1) selectMember.value.splice(index, 1)
      }
    } else {
      checkedKeys.value = _.filter(d => !disabledIds.value.includes(d))
      selectMember.value = checkedKeys.value.map(d => {
        return organizationList.value.find(k => k.id === d)
      }).filter(d => d)
    }
  }

  const showCheckedKeys = computed(() => {
    return [...checkedKeys.value, ...disabledIds.value]
  })

  const clear = function (item) {
    const { id } = item
    const idIndex = checkedKeys.value.findIndex(d => d === item.id)
    if (idIndex > -1) checkedKeys.value.splice(idIndex, 1)
    const index = selectMember.value.findIndex(d => d.id === id)
    if (index > -1) selectMember.value.splice(index, 1)
    if (props.selectType === 1) {
      const parent = findParentIntree(organization.value, id)
      parent.map(d => {
        const idIndex = checkedKeys.value.findIndex(k => k === d.id)
        if (idIndex > -1) checkedKeys.value.splice(idIndex, 1)
        const index = selectMember.value.findIndex(k => k.id === d.id)
        if (index > -1) selectMember.value.splice(index, 1)
      })
    }
  }

  const clearAll = function () {
    checkedKeys.value = []
    selectMember.value = []
  }

  const ok = function () {
    if (!selectMember.value.length) return message.warning('请选择成员')
    const data = cloneDeep(selectMember.value)
    emits('finish', data.map(d => d.orgId), data.map(d => {
      d.id = d.orgId
      return d
    }))
    cancel()
  }

  const cancel = () => {
    visible.value = false
    forwardInput.value = ''
    proxy.$store.commit('operation/networkDisk/setForwardVal', '')
    proxy.$store.commit('operation/networkDisk/setForwardInfo', [])
  }

  const afterClose = function () {
    openContactPerson.value = true
    selectMember.value = []
    checkedKeys.value = []
    showSearchKey.value = undefined
    searchKey.value = ''
  }

  const onSearch = debounce(function () {
    searchKey.value = showSearchKey.value ? showSearchKey.value.toLocaleLowerCase() : ''
  }, 400)

  const organizationList = computed(() => treeIntoList(organization.value))

  const showFilterList = computed(() => organizationList.value.filter(d => props.selectType === 0 || (props.selectType === 1 && d.dataType === 2)).filter(d => d.name.toLocaleLowerCase().includes(searchKey.value) || `${d.orgId}`.toLocaleLowerCase().includes(searchKey.value)))

  const forwardInputChange = (e) => {
    proxy.$store.commit('operation/networkDisk/setForwardVal', e.target.value)
  }
</script>

<style lang="scss" scoped>
.content{
  .left-box,.right-box{
    width: calc((100% - 1px) /2);
    flex-shrink: 0;
    height: 500px;
    padding: 12px;
  }
  .line{
    width: 1px;
    background: #DFDFDF;
  }
  .emp-share-emp-list-box {
    :deep(.ant-checkbox-wrapper) {
      display: flex;
      align-items: center;
      span {
        display: block;
      }
    }
    .item{
      height: 64px;
      align-items: center;
      &:hover {
        background: #f5f5f5;
      }
    }
    .user-box {
      position: relative;
      padding-top: 4px;
      width: 100%;
      .user-info {
        margin-left: 8px;
        .user-name {
          font-weight: 600;
          font-size: 14px;
          color: #333333;
        }
        .user-department {
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          margin-top: 4px;
          .zhiWu {
            display: inline-block;
            border-radius: 10px;
            border: 1px solid #1890FF;
            padding: 0 6px;
            text-align: center;
            color: #1890FF;
            font-weight: 400;
            font-size: 12px;
            height: 18px;
            line-height: 16px;
            min-width: 30px;
          }
        }

      }
      .close {
        position: absolute;
        right: 20px;
        top: 16px;
        cursor: pointer;
      }
    }
    .master {
      width: 32px;
      height: 16px;
      line-height: 16px;
      background: rgba(250, 100, 0, 0.1);
      border-radius: 4px;
      font-weight: 400;
      font-size: 12px;
      color: #FA6400;
      display: inline-block;
      text-align: center;
    }
    .admin {
      min-width: 32px;
      height: 16px;
      line-height: 16px;
      background: rgba(24, 144, 255, 0.1);
      border-radius: 4px;
      font-weight: 400;
      font-size: 12px;
      color: #1890FF;
      display: inline-block;
      text-align: center;
      padding: 0 4px;
    }
    .empty {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .forward-wrap {
      margin-top: 12px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

      .name-ico-wrap {
        .name-ico {
          padding: 12px;
          background: #f5f7fa;
          border-radius: 8px 8px 0 0;
          border-bottom: 1px solid #eaedf1;

          .file-info {
            .file-icon-wrapper {
              width: 36px;
              height: 36px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 10px;
              background: #fff;
              border-radius: 4px;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
              overflow: hidden;

              .file-icon {
                width: 24px;
                height: 24px;
                object-fit: contain;
              }
            }

            .file-name {
              font-weight: 500;
              font-size: 14px;
              color: #333;
            }
          }

          .file-meta {
            margin-top: 8px;
            font-size: 12px;
            color: #666;

            .creator-info {
              .creator-label {
                margin-left: 8px;
                color: #999;
              }
            }

            .doc-count {
              background: rgba(24, 144, 255, 0.1);
              color: #1890ff;
              padding: 2px 8px;
              border-radius: 10px;
              font-size: 12px;
            }
          }
        }

        .message-input-wrapper {
          padding: 12px;
          background: #fff;
          border-radius: 0 0 8px 8px;

          .message-input {
            border-radius: 4px;
            border-color: #d9d9d9;
            transition: all 0.3s;

            &:hover, &:focus {
              border-color: #1890ff;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
            }
          }

          .char-count {
            margin-top: 4px;
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }
  .filter-list{
    height: 430px;
    margin-top: 10px;
  }
  .left-box{
    .left-content-box{
      height: calc(100% - 32px);
      overflow-y: auto;
      overflow-x: hidden;
    }
    .title {
      font-size: 15px;
      color: #333333;
      cursor: pointer;
      padding-left: 6px;
      img {
        width: 8px;
        margin-right: 8px;
        transition: all .3s;
        &.rotate-90 {
          transform: rotate(90deg);
        }
      }
      &:hover {
        background: #f5f5f5;
      }
    }

    .contactPerson-box {
      margin-top: 10px;

      .scroller {
        padding-left: 12px;
      }
      .showEmpList {
        height: 100%;
        overflow: inherit;
        padding-left: 6px;
      }
      .hideEmpList {
        height: 0;
        overflow: hidden;
      }
    }
    .depart-box{
      .title{
        margin-left: 4px;
      }
      :deep(.user-box){
        display: flex;
        align-items: center;
        height: 84px;
        .ant-tree-node-content-wrapper{
          height: 100%;
          display: flex;
          align-items: center;
          .ant-tree-title{
            width: 100%;
          }
        }
      }
      :deep(.depart-box){
        padding: 4px 0 10px 16px;
      }
      .user-item{
        .user-info{
          width: calc(100% - 66px);
          margin-left: 22px;
          .name-box{
            font-size: 15px;
            font-weight: 600;
            .area{
              font-weight: 400;
              font-size: 13px;
              color: #333333;
            }
          }
          .user-department {
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            margin-top: 4px;
            .zhiWu {
              display: inline-block;
              border-radius: 10px;
              border: 1px solid #1890FF;
              padding: 0 6px;
              text-align: center;
              color: #1890FF;
              font-weight: 400;
              font-size: 12px;
              height: 18px;
              line-height: 16px;
              min-width: 30px;
            }
          }
          .zhiNeng {
            font-weight: 400;
            font-size: 12px;
            color: #666666;
            &.active {
              color: #ffffff;
            }
          }
        }
      }
      .depart-item{
        font-weight: 400;
        font-size: 15px;
        .name{
          margin-left: 22px;
        }
        .bianzhi{
          font-weight: 400;
          font-size: 13px;
          color: #5e5e5e;
        }
      }
    }
  }
  .right-box{
    .select-info-header{
      font-weight: 400;
      font-size: 16px;
      color: #999999;
      height: 34px;
      line-height: 34px;
      .clear-text{
        font-size: 14px;
        cursor: pointer;
        color: rgb(24, 144, 255);
      }
    }

    .select-member{
      .user-box{
        height: 48px;
      }
    }
  }

}
:deep(.ant-input) {
  resize: none;
}
</style>
