export const accessOptions = function (level, superAdmin, isShare, isSpace, isBatch) {
  return [
    // 顶级目录，当前登录人角色是超管
    {
      label: '转让超级管理员',
      value: 6,
      tip: '转让超级管理员',
      show: level === 1 && superAdmin && !isBatch
    },
    // 所有目录，当前登录人角色是超管
    {
      label: '管理员',
      value: 5,
      tip: '可修改成员权限，新建/上传/查看/删除/移动文件',
      show: superAdmin
    },
    // 所有目录，当前登录人角色是超管、管理员
    {
      label: '可编辑',
      value: 3,
      tip: '可新建/上传/查看/删除/移动文件'
    },
    // 所有目录，当前登录人角色是超管、管理员
    {
      label: '可上传',
      value: 2,
      tip: '仅可上传/新建/查看文件，不可删除'
    },
    // 所有目录，当前登录人角色是超管、管理员
    {
      label: '可查看',
      value: 1,
      tip: '仅可查看文件'
    },
    {
      label: '禁止查看',
      value: -1,
      tip: '禁止查看',
      show: level > 1 && isSpace
    },
    // 非顶层的其他目录，当前登录人角色是超管、管理员，单独或者批量设置共享成员权限
    {
      label: '移除成员',
      value: -2,
      tip: '移除成员',
      show: level === 1 || isShare
    }
  ].filter(d => d.show === undefined || d.show)
}

const labelMap = new Map([
  [-1, '禁止查看'],
  [-2, '移除成员'],
  [1, '可查看'],
  [2, '可上传'],
  [3, '可编辑'],
  [5, '管理员'],
  [6, '转让超级管理员']
])

export const getLabel = function (disabled, type) {
  return type === 6 ? `${disabled ? '' : '转让'}超级管理员` : labelMap.get(type) || '请选择权限'
}
