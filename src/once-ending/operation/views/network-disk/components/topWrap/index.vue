<template>
  <div class="flex flex-col mr-16">
    <div class="breadcrumb" v-if="!isRecycle">
      <a-breadcrumb separator=">">
        <a-breadcrumb-item v-for="(item, index) in parentPathList" :key="item.id">
          <span
            :class="index === parentPathList.length - 1 ? 'grey-9' : 'canClick pointer'"
            @click="index === parentPathList.length - 1 ? null : openNewFolder(item, index)"
          >{{  item.name  }}</span>
        </a-breadcrumb-item>
      </a-breadcrumb>
    </div>
    <div class="controller-top" v-if="isRecycle">
      <div class="flex flex-align-center" style="padding: 8px 0">
        <span class="font-16 bold">回收站</span>
        <span>(已删除的文件仅保留7天，超过7天将彻底删除)</span>
      </div>
    </div>
    <div class="controller-top" v-else>
      <div class="controller-top-left">
        <a-input-search style="width: 100%" placeholder="请输入文件名称搜索" v-model="topState.searchValue" allowClear @search="fetchData" />
        <a-button type="primary" @click="fetchData" class="ml-16" icon="search">搜索</a-button>
        <a-button @click="resetValue" class="ml-16">重置</a-button>
      </div>
      <div class="controller-top-right">
        <div v-if="isShare || isCommonFlder" class="flex">
          <a-button type="primary" class="mr-16" v-if="[2,3,5,6].includes(batchPermission)" @click="createNewFolder">新建文件夹</a-button>
<!--          <a-button type="primary" :loading="uploadFileLoading" @click="uploadFile">上传文件</a-button>-->
          <upload-file class="mr-16" v-if="[2,3,5,6].includes(batchPermission)"></upload-file>
          <a-button type="primary" class="mr-16" v-if="[2,3,5,6].includes(batchPermission)" @click="isBatchHandle">{{ !topState.isBatch ? '批量操作' : '取消批量操作' }}</a-button>
        </div>
        <div v-else class="flex">
          <a-button type="primary" class="mr-16" v-if="createNewFolderShow" @click="createNewFolder">新建文件夹</a-button>
<!--          <a-button type="primary" class="mr-16" :loading="uploadFileLoading" v-if="uploadFileShow" @click="uploadFile">上传文件</a-button>-->
          <upload-file class="mr-16" v-if="uploadFileShow"></upload-file>
          <a-button type="primary" class="mr-16" v-if="isBatchShow" @click="isBatchHandle">{{ !topState.isBatch ? '批量操作' : '取消批量操作' }}</a-button>
        </div>
        <a-dropdown class="mr-16">
          <div class="pointer">
            <span>{{sortTypeOptions.find(item => item.value+'' === topState.sortType.toString())?.label}}</span>
            <a-icon type="down" />
          </div>
          <a-menu slot="overlay" v-model="topState.sortType" @click="sortTypeChange">
            <a-menu-item v-for="item in sortTypeOptionsNew" :key="item.value">{{ item.label }}</a-menu-item>
          </a-menu>
        </a-dropdown>
        <a-icon type="unordered-list" class="font-18 bold mr-16 pointer" @click="listOrGridHandle('1')" :class="topState.listOrGrid === '1' ? 'blue' : ''"/>
        <a-icon type="appstore"  class="font-18 bold pointer" @click="listOrGridHandle('2')" :class="topState.listOrGrid === '2' ? 'blue' : ''"/>
      </div>
    </div>
    <!-- <div class="folderOrFile-wrap" v-if="topState.sortType.toString() === '3' && !isRecycle">
      <div class="folderOrFile-item" :class="topState.folderOrFile === item.value ? 'active' : ''"
          v-for="item in folderOrFile"
          :key="item.value"
          @click="folderOrFileItemHandle(item.value)"
      >{{ item.label }}</div>
    </div> -->
  </div>
</template>

<script setup>
  import { computed, defineEmits } from 'vue'
  import store from '~/store'
  import { useRoute, useRouter } from 'vue-router/composables'
  import { useNetWorkDiskInject } from '../../useNetWorkDisk'
  import UploadFile from '../uploadFile'
  import {
    sortTypeOptions,
    folderOrFile
  } from '../../constants'

  const createNewFolderShow = computed(() => store.state.operation.networkDisk.createNewFolderShow)
  const uploadFileShow = computed(() => store.state.operation.networkDisk.uploadFileShow)
  const uploadFileLoading = computed(() => store.state.operation.networkDisk.uploadFileLoading)
  const batchPermission = computed(() => store.state.operation.networkDisk.batchPermission)
  const isBatchShow = computed(() => store.state.operation.networkDisk.isBatchShow)

  const emits = defineEmits([
    'createNewFolderHandle',
    'uploadFileHandle'
  ])
  const route = useRoute()
  const router = useRouter()
  const {
    topState,
    isBatchHandle,
    sortTypeChange,
    listOrGridHandle,
    folderOrFileItemHandle,
    fetchData,
    resetValue,
    parentPathList,
    tabVal,
    isShare,
    isCommonFlder,
    sortTypeOptionsNew
  } = useNetWorkDiskInject()
  const isCommonFolder = computed(() => route.path.includes('commonFolder'))
  const isRecycle = computed(() => route.path.includes('recycle'))
  const createNewFolder = () => {
    store.commit('operation/networkDisk/renameFileStateFlag', 1)
    if (isCommonFolder) {
      if (route.query.pathId) {
        store.commit('operation/networkDisk/setFolderId', route.query.pathId)
      } else {
        store.commit('operation/networkDisk/setFolderId', route.params.id)
      }
    }
    emits('createNewFolderHandle')
  }
  // const uploadFile = () => {
  //   emits('uploadFileHandle')
  // }
  const openNewFolder = (record, index) => {
    if (route.name === 'recent') {
      router.push({
        path: `/operation/network-disk/recent/${record.id}`,
      })
    } else if (route.name === 'my') {
      router.push({
        path: `/operation/network-disk/my/${record.id}`,
      })
    } else if (route.name === 'commonFolder') {
      if (index === 0 && record.name === '公共') return
      router.push({
        path: route.fullPath,
        query: {
          pathId: record.id
        }
      })
    } else if (route.name === 'share') {
      if (index === 0 && record.name === '公共') return
      router.push({
        path: `/operation/network-disk/share/${record.id}?sharedTopId=${route.query.sharedTopId ? route.query.sharedTopId : route.params.id}`,
      })
    } else if (route.name === 'recycle') {
      router.push({
        path: `/operation/network-disk/recycle/${record.id}`,
      })
    } else {
      console.log('出错了')
    }
  }
</script>

<style lang="scss" scoped>
@import "../../common";
.canClick {
  &:hover{
    color: #1890ff;
  }
}
</style>
