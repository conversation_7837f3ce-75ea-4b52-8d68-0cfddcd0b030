<template>
  <div class="disk-con">
    <TopWrap
      :uploadFileLoading="uploadFileLoading"
      @search="searchHandle"
      @createNewFolderHandle="createNewFolderHandle"
      @uploadFileHandle="uploadFileHandle"
    ></TopWrap>
    <div class="disk-detail-wrap">
      <router-view></router-view>
    </div>
    <RenameFile  ref="renameFileRef" @fetchDataHandle="fetchData"/>
<!--    <UploadFile ref="uploadFileRef"/>-->
    <RemoveFile ref="removeFileRef" @fetchDataHandle="fetchData();getPcHomeTab()" />
    <Permission />
  </div>
</template>

<script setup>
  import { useRouter } from 'vue-router/composables'
  import TopWrap from '../topWrap/index.vue'
  // import UploadFile from '../uploadFile/index.vue'
  import RemoveFile from '../removeFile/index.vue'
  import RenameFile from '../renameFile/index.vue'
  import { useNetWorkDiskInject } from '../../useNetWorkDisk'
  import Permission from '../permission'

  const router = useRouter()
  const {
    uploadFileLoading,
    uploadFileHandle,
    uploadSureHandle,
    createNewFolderHandle,
    searchHandle,
    renameFileRef,
    removeFileRef,
    uploadFileRef,
    resetValue,
    fetchData,
    tab,
    getPcHomeTab
  } = useNetWorkDiskInject()

  // 使用全局导航守卫监听路由变化
  router.beforeEach((to, from, next) => {
    resetValue()
    next()
  })
</script>

<style lang="scss" scoped>
@import "../../common";
</style>
