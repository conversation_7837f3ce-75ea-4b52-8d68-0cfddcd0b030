<template>
  <ni-list-page>
    <ni-table
      :columns="columns"
      :dataSource="dataSource"
      :pagination="pagination"
      :setting="false"
      :rowKey="(record) => record.id"
      :rowSelection="rowSelectionConfig"
      @change="handleTableChange"
    >
      <template slot="nameSlot" slot-scope="text, record, index">
        <div class="flex flex-wrap flex-align-center">
          <img :src="previewImg(record.fileLink) ? record.fileLink : formatIco(record.fileLink)" width="24px" height="24px" class="mr-5">
          <p class="pointer lines-1" style="line-height: 24px" @click="preview(record)">{{text}}</p>
          <span v-if="isCommonFolder" class="flag font-12">{{ accessEnum.find(item => item.value === record.access)?.label }}</span>
        </div>
      </template>
      <template slot="actionSlot" slot-scope="text, record">
        <a-dropdown v-if="record.operationList && record.operationList.length">
          <a-icon type="ellipsis" style="font-size: 30px; font-weight: bold;"/>
          <a-menu slot="overlay">
            <a-menu-item v-for="item in record.operationList" :key="item.value" @click="operationListHandle(record, item.value)">{{ item.label }}</a-menu-item>
          </a-menu>
        </a-dropdown>
      </template>
      <template slot="statistics">
        <BottomWrap></BottomWrap>
      </template>
    </ni-table>
  </ni-list-page>
</template>

<script setup>
  import { useNetWorkDiskInject } from '../../useNetWorkDisk'
  import { defineProps, computed, onUnmounted } from 'vue'
  import { useRoute } from 'vue-router/composables'
  import { NiListPage, NiTable, NiPreviewer } from '@jiuji/nine-ui'
  import { formatIco, previewImg, accessEnum } from '../../constants'
  import BottomWrap from '../bottomWrap/index.vue'

  const props = defineProps({
    columns: {
      type: Array,
      default: () => ([])
    },
    isRowSelection: {
      type: Boolean,
      default: false
    }
  })

  const emit = defineEmits([
    'reNameFileHandle',
    'removeFileHandle',
    'batchDeleteFileHandle'
  ])

  const {
    pagination,
    dataSource,
    selectedRowKeys,
    selectedRows,
    handleTableChange,
    copyLinkHandle,
    openPermission,
    saveFileHandle,
    openNewFolder,
    forward,
    logReportHandle
  } = useNetWorkDiskInject()
  const route = useRoute()
  const isCommonFolder = computed(() => route.path.includes('commonFolder'))
  const rowSelectionChange = (keys, rows) => {
    selectedRowKeys.value = keys
    selectedRows.value = rows
    console.log(selectedRows.value)
  }

  const rowSelectionConfig = computed(() =>
    props.isRowSelection
      ? {
        selectedRowKeys: selectedRowKeys.value,
        onChange: rowSelectionChange
      }
      : undefined
  )

  const { open, destroy, replaceData } = NiPreviewer()
  const preview = async (record) => {
    if (record.fileExt) {
      let imgList = [
        {
          src: record.fileLink,
          name: record.name || ''
        }
      ]
      await replaceData(imgList)
      open(0)
      logReportHandle([record.id], 2)
    } else {
      openNewFolder(record)
    }
  }
  const operationListHandle = (record, value) => {
    /* value:
      1: 发送给同事
      2: 复制链接
      3: 成员权限管理
      5: 下载
      6: 重命名
      7: 移动到
      9: 删除
    */
    switch (value) {
    case 1:
      forward(record)
      if (record.fileExt) {
        logReportHandle([record.id], 3)
      }
      break
    case 2:
      copyLinkHandle(record)
      break
    case 3:
      openPermission(record)
      break
    case 5:
      saveFileHandle(record)
      if (record.fileExt) {
        logReportHandle([record.id], 4)
      }
      break
    case 6:
      emit('reNameFileHandle', record)
      break
    case 7:
      emit('removeFileHandle', record)
      break
    case 9:
      emit('batchDeleteFileHandle', [record.id])
      break
    default:
      break
    }
  }
  onUnmounted(() => {
    destroy()
  })
</script>

<style lang="scss" scoped>
@import "../../common";
.flag {
  padding: 2px 4px;
    border-radius: 4px;
    background: rgba(204, 204, 204, 0.3);
    margin-left: 8px;
}
</style>
