<template>
  <div :class="$store.state.operation.networkDisk.collapsed ? 'hiddenDiv' : ''">
    <a-sub-menu :key="menuInfo.path" v-bind="$props" v-on="$listeners">
      <div slot="title" class="flex flex-align-center">
          <div v-if="!$store.state.operation.networkDisk.collapsed">
            <a-icon v-if="menuInfo && menuInfo.icon" :type="menuInfo.icon" />
            <span class="title">{{ menuInfo.meta.title }}</span>
            <a-icon
              type="plus-square" @click.stop.prevent="addMonitor"
              class="ml-8"
              style="color: #1890ff;font-size: 18px; font-weight: 600;"
            />
          </div>
          <div v-else>
            <a-icon v-if="menuInfo && menuInfo.icon" :type="menuInfo.icon" style="padding-left: 18px"/>
          </div>
      </div>
      <template v-for="item in menuChildren">
        <a-menu-item v-if="!item.children || item.children.length < 1" :key="item.path">
          <div class="full-width flex flex-align-center">
            <router-link class="flex-child-average title lines-1" :to="item.path">{{ item.meta.title }}</router-link>
            <a-popover placement="right" v-if="item.operationList && item.operationList.length">
              <template slot="content">
                <div
                  class="controller-item"
                  v-for="operationItem in item.operationList"
                  :key="operationItem.value"
                  @click="operationItemHandle(item, operationItem.value)"
                >{{ operationItem.label }}</div>
              </template>
              <a-icon class="flex-child-noshrink ml-8" type="setting" style="color: #1890ff;font-size: 16px; font-weight: 600;"/>
            </a-popover>
          </div>
        </a-menu-item>
        <sub-menu v-else :key="`submenu-${item.path}`" :menu-info="item" v-on="$listeners"/>
      </template>
    </a-sub-menu>
    <RenameFile ref="reNameFile" @fetchDataHandle="getPcHomeTab"/>
    <select-member-modal v-model="showSelectMemberModal" :select-type="1" @finish="forwardUserHandle"/>
  </div>
</template>

<script setup>
  import { defineProps, ref, getCurrentInstance, computed } from 'vue'
  import { Menu } from 'ant-design-vue'
  import RenameFile from '../../components/renameFile/index.vue'
  import { useNetWorkDiskInject } from '../../useNetWorkDisk.js'
  import SelectMemberModal from '../permission/select-member-modal'

  const props = defineProps({
    ...Menu.SubMenu.props,
    menuInfo: {
      type: Object,
      default: () => ({})
    },
  })
  const {
    getPcHomeTab,
    batchDeleteFile,
    copyLinkHandle,
    openPermission,
    saveFileHandle,
    removeFileHandle,
    forward,
    showSelectMemberModal,
    forwardUserHandle,
  } = useNetWorkDiskInject()
  const { $store, $route } = getCurrentInstance().proxy
  const reNameFile = ref(null)
  const menuChildren = computed(() => props.menuInfo?.children || [])

  const addMonitor = () => {
    $store.commit('operation/networkDisk/setTab', 2)
    $store.commit('operation/networkDisk/renameFileStateFlag', 1)
    $store.commit('operation/networkDisk/setRenameFileInfo', {})
    $store.commit('operation/networkDisk/setMenuAddFile', true)
    $store.commit('operation/networkDisk/setFolderId', '')
    reNameFile.value.open()
  }
  const operationItemHandle = (item, value) => {
    /* value:
        1: 发送给同事
        2: 复制链接
        3: 成员权限管理
        5: 下载
        6: 重命名
        7: 移动到
        9: 删除
    */
    switch (value) {
    case 1:
      forward(item)
      break
    case 2:
      copyLinkHandle(item)
      break
    case 3:
      openPermission(item)
      break
    case 5:
      saveFileHandle(item)
      break
    case 6:
      fileRenameHandleCol(item)
      break
    case 7:
      removeFileHandle(item)
      break
    case 9:
      batchDeleteFileHandle(item.id)
      break
    default:
      break
    }
  }

  const batchDeleteFileHandle = (id) => {
    batchDeleteFile([id])
  }
  const fileRenameHandleCol = (item) => {
    $store.commit('operation/networkDisk/renameFileStateFlag', 2)
    $store.commit('operation/networkDisk/setRenameFileInfo', item)
    reNameFile.value.open()
  }
</script>

<style lang="scss" scoped>
.title {
  font-size: 16px;
  font-weight: 500;
}
a,a:link,a:visited,a:hover,a:active{
  text-decoration: none;
  color: inherit;
}
:deep(.ant-menu-item) {
  padding-right: 0 !important;
}
.controller-item {
  cursor: pointer;
  font-size: 14px;
  color: #666;
  width: 100px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  border-bottom: 1px solid #eee;
  &:hover {
    color: #1890ff;
  }
  &:last-child {
    border-bottom: none;
  }
}
.hiddenDiv {
  :deep(.ant-menu-submenu-arrow) {
    display: none;
  }
}
</style>
