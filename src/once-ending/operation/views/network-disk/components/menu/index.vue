<template>
  <a-menu mode="inline"
          class="sales-left-menu"
          :selectedKeys="selectedKeys"
          :defaultSelectedKeys="selectedKeys"
          :openKeys="openKeysList"
          :key="route.path"
          ref="menuRef"
          @openChange="onOpenChange"
          @click="menuItemHandle">
    <template v-for="item in routesList">
      <a-menu-item v-if="!(item.children && item.children.length) && (item.meta.title !== '公共')" :key="item.path">
        <router-link :to="item.path">
          <div class="flex flex-align-center">
            <a-icon v-if="item && item.icon" style="font-size: 16px" :type="item.icon" class="anticon" />
            <span class="title">{{ item.meta.title }}</span>
          </div>
        </router-link>
      </a-menu-item>
      <SubMenu v-if="(item.children && item.children.length) || (item.meta.title === '公共')" :key="`submenu-${item.path}`" :menuInfo="item"/>
    </template>
  </a-menu>
</template>

<script setup>
  import { ref, defineProps, computed, watch, onMounted, nextTick, getCurrentInstance } from 'vue'
  import SubMenu from './sub-menu.vue'
  import { useRoute } from 'vue-router/composables'
  import { useNetWorkDiskInject } from '../../useNetWorkDisk'

  const props = defineProps({
    routes: {
      type: Array,
      default: []
    },
    collapsed: {
      type: Boolean,
      default: false
    }
  })
  const route = useRoute()
  const instance = getCurrentInstance()
  const openKeysList = ref(JSON.parse(sessionStorage.getItem('openKeys')) || [])
  const { topState } = useNetWorkDiskInject()

  // 初始化 selectedKeys，处理特定路由共享同一个选中菜单项
  const getInitialSelectedKeys = () => {
    const path = route.path
    // 让所有 /operation/network-disk/my/{id} 格式的路由共享同一个选中状态
    if (/^\/operation\/network-disk\/my\/\d+$/.test(path)) {
      return ['/operation/network-disk/my/0']
    } else if (/^\/operation\/network-disk\/recent\/\d+$/.test(path)) { // 让所有 /operation/network-disk/recent/{id} 格式的路由共享同一个选中状态
      return ['/operation/network-disk/recent/0']
    } else if (/^\/operation\/network-disk\/recycle\/\d+$/.test(path)) { // 让所有 /operation/network-disk/recycle/{id} 格式的路由共享同一个选中状态
      return ['/operation/network-disk/recycle/0']
    }
    return [path]
  }

  // 使用计算出的初始值初始化 selectedKeys
  const selectedKeys = ref(getInitialSelectedKeys())
  const routesList = computed(() => props.routes || [])

  // 强制更新组件
  const forceUpdate = () => {
    if (instance && instance.proxy && instance.proxy.$forceUpdate) {
      instance.proxy.$forceUpdate()
    }
  }

  // 监听路由变化，更新菜单选中状态
  watch(() => route.path, (newPath) => {
    // 使用 setTimeout 延迟设置选中状态，确保在 DOM 更新后设置
    setTimeout(() => {
      // 处理 my/{id} 路由
      if (/^\/operation\/network-disk\/my\/\d+$/.test(newPath)) {
        selectedKeys.value = ['/operation/network-disk/my/0']
        applySelectedStyle('/operation/network-disk/my/0')
      } else if (/^\/operation\/network-disk\/recent\/\d+$/.test(newPath)) { // 处理 recent/{id} 路由
        selectedKeys.value = ['/operation/network-disk/recent/0']
        applySelectedStyle('/operation/network-disk/recent/0')
      } else if (/^\/operation\/network-disk\/recycle\/\d+$/.test(newPath)) { // 处理 recycle/{id} 路由
        selectedKeys.value = ['/operation/network-disk/recycle/0']
        applySelectedStyle('/operation/network-disk/recycle/0')
      } else {
        selectedKeys.value = [newPath]
      }
      sessionStorage.setItem('selectedKeys', JSON.stringify(selectedKeys.value))
      // 强制更新组件
      nextTick(() => {
        forceUpdate()
      })
    }, 100) // 延迟 100 毫秒
  }, { immediate: true })

  // 应用选中样式的辅助函数
  const applySelectedStyle = (targetKey) => {
    nextTick(() => {
      // 使用更准确的选择器
      const menuItems = document.querySelectorAll('.ant-menu-item')
      let targetMenuItem = null
      // 遍历所有菜单项，找到匹配的菜单项
      menuItems.forEach(item => {
        if (item.getAttribute('key') === targetKey) {
          targetMenuItem = item
        }
      })
      if (targetMenuItem) {
        // 先移除所有菜单项的选中样式
        menuItems.forEach(item => {
          item.classList.remove('ant-menu-item-selected')
        })
        // 为目标菜单项添加选中样式
        targetMenuItem.classList.add('ant-menu-item-selected')
      }
      // else {
      //   console.log('未找到菜单项元素:', targetKey)
      // }
    })
  }

  // 组件挂载后，确保菜单选中状态正确
  onMounted(() => {
    // 在 DOM 更新后设置菜单选中状态
    nextTick(() => {
      // 处理 my/{id} 路由
      if (/^\/operation\/network-disk\/my\/\d+$/.test(route.path)) {
        selectedKeys.value = ['/operation/network-disk/my/0']
        sessionStorage.setItem('selectedKeys', JSON.stringify(selectedKeys.value))
      } else if (/^\/operation\/network-disk\/recent\/\d+$/.test(route.path)) { // 处理 recent/{id} 路由
        selectedKeys.value = ['/operation/network-disk/recent/0']
        sessionStorage.setItem('selectedKeys', JSON.stringify(selectedKeys.value))
      } else if (/^\/operation\/network-disk\/recycle\/\d+$/.test(route.path)) { // 处理 recycle/{id} 路由
        selectedKeys.value = ['/operation/network-disk/recycle/0']
        sessionStorage.setItem('selectedKeys', JSON.stringify(selectedKeys.value))
      }
    })

    // 使用 setTimeout 延迟操作 DOM，确保菜单项已渲染
    setTimeout(() => {
      // 处理 my/{id} 路由
      if (/^\/operation\/network-disk\/my\/\d+$/.test(route.path)) {
        applySelectedStyle('/operation/network-disk/my/0')
      } else if (/^\/operation\/network-disk\/recent\/\d+$/.test(route.path)) { // 处理 recent/{id} 路由
        applySelectedStyle('/operation/network-disk/recent/0')
      } else if (/^\/operation\/network-disk\/recycle\/\d+$/.test(route.path)) { // 处理 recycle/{id} 路由
        applySelectedStyle('/operation/network-disk/recycle/0')
      }
    }, 500) // 延迟 500 毫秒
  })
  const onOpenChange = (openKeys) => {
    openKeysList.value = openKeys
    sessionStorage.setItem('openKeys', JSON.stringify(openKeysList.value))
  }
  const menuItemHandle = ({ item, key, keyPath }) => {
    // 特殊处理所有 /operation/network-disk/my/{id} 格式的路由
    if (/^\/operation\/network-disk\/my\/\d+$/.test(key)) {
      // 直接设置为 /operation/network-disk/my/0
      selectedKeys.value = ['/operation/network-disk/my/0']
      // 强制更新 sessionStorage
      sessionStorage.setItem('selectedKeys', JSON.stringify(selectedKeys.value))
    }
    // 其他情况由 v-model 自动处理
  }
  // const selectHandle = (item) => {
  //   console.log('item', item)
  //   if (item.meta && item.meta.title && item.meta.title === '最近') {
  //     topState.sortType = [2]
  //     topState.folderOrFile = '5'
  //     sessionStorage.setItem('sortType', JSON.stringify(topState.sortType))
  //     sessionStorage.setItem('folderOrFile', topState.folderOrFile)
  //   }
  //   if (item.meta && item.meta.title && (['我的', '公共']).includes(item.meta.title)) {
  //     topState.sortType = [3]
  //     topState.folderOrFile = '5'
  //     sessionStorage.setItem('sortType', JSON.stringify(topState.sortType))
  //     sessionStorage.setItem('folderOrFile', topState.folderOrFile)
  //   }
  // }
</script>

<style lang="scss" scoped>
.title {
  font-size: 16px;
  font-weight: 500;
}
a:visited{
  text-decoration: none;
  color: inherit;
}
:deep(.router-link-exact-active, .router-link-active) {
  color: #1890ff !important;
}
:deep(.ant-menu-item-selected > a) {
  color: #1890ff !important;
}
:deep(.ant-menu-item:has(.router-link-exact-active)) {
  background-color: #E7F3FF;
  a::before {
    position: absolute;
    left: auto !important;
    top: 0;
    right: 0;
    bottom: 0;
    height: 40px;
    width: 3px;
    background-color: #1890ff !important;
    content: '';
  }
}
</style>
