<template>
  <div>
    <div class="grid-wrap relative" ref="gridWrapRef" v-if="dataSource && dataSource.length" @scroll="handleScroll" :style="{ height: topState.sortType.toString() === '3' ? 'calc(100vh - 160px)' : 'calc(100vh - 120px)'}">
      <div class="flex flex-wrap">
        <div class="grid-item pointer" v-for="(item, index) in dataSource" :key="item.id" @click="preview(item)">
          <div>
            <div class="flex flex-align-center flex-justify-center">
              <img class="imgStyle" :src="previewImg(item.fileLink) ? item.fileLink : formatIco(item.fileLink)" alt="">
            </div>
            <div class="flex mt-5">
              <div class="flex-child-average item-title lines-2 mr-8" style="height: 48px">{{ item.name }}</div>
              <div class="flex" style="margin-top: 3px" v-if="topState.isBatch">
                <a-checkbox :checked="selectedRowKeys.includes(item.id)" @click.stop.prevent="changeBatch(item)"/>
              </div>
            </div>
          </div>
          <div class="flex flex-justify-between flex-align-center mt-5">
            <span class="font-12 grey-9">{{ previewImg(item.fileLink) ? '图片' : item.fileExt ? '文档' : '文件夹' }}</span>
            <a-dropdown>
              <svg t="1752136100745" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6146" width="24" height="24"><path d="M191.94 512.06m-75 0a75 75 0 1 0 150 0 75 75 0 1 0-150 0Z" fill="#565656" p-id="6147"></path><path d="M514.34 512.06m-75 0a75 75 0 1 0 150 0 75 75 0 1 0-150 0Z" fill="#565656" p-id="6148"></path><path d="M832.06 512.06m-75 0a75 75 0 1 0 150 0 75 75 0 1 0-150 0Z" fill="#565656" p-id="6149"></path></svg>
              <a-menu slot="overlay">
                <a-menu-item v-for="childItem in item.operationList" :key="childItem.value" @click="operationListHandle(item, childItem.value)">{{ childItem.label }}</a-menu-item>
              </a-menu>
            </a-dropdown>
          </div>
        </div>
      </div>
      <div v-if="loading" class="loading-more">加载中...</div>
      <div v-if="noMore && dataSource.length" class="no-more" style="padding-bottom: 60px;">没有更多数据了</div>
      <div class="bottom-con">
        <BottomWrap></BottomWrap>
      </div>
    </div>
    <div v-else class="grid-wrap flex flex-col flex-justify-center flex-align-center" :style="{ height: topState.sortType.toString() === '3' ? 'calc(100vh - 160px)' : 'calc(100vh - 120px)'}">
      <img src="../../../../../../assets/images/empty2.png"/>
      <span class="font-14 grey-9 mt-8">暂无数据</span>
    </div>
  </div>
</template>

<script setup>
  import { defineProps, onUnmounted, defineEmits, ref, onMounted, watch, computed } from 'vue'
  import { formatIco, previewImg } from '../../constants'
  import { useNetWorkDiskInject } from '../../useNetWorkDisk'
  import { NiPreviewer } from '@jiuji/nine-ui'
  import BottomWrap from '../bottomWrap/index.vue'

  const props = defineProps({
    dataSource: {
      type: Array,
      default: () => ([])
    }
  })

  const emit = defineEmits([
    'reNameFileHandle',
    'removeFileHandle',
    'batchDeleteFileHandle'
  ])

  const {
    topState,
    copyLinkHandle,
    openPermission,
    saveFileHandle,
    loadMoreData,
    pagination,
    openNewFolder,
    changeBatch,
    selectedRowKeys,
    forward,
    logReportHandle
  } = useNetWorkDiskInject()

  const gridWrapRef = ref(null)
  const loading = ref(false)
  const noMore = ref(false)

  // 节流函数，防止频繁触发加载更多
  let scrollTimer = null
  const handleScroll = () => {
    if (scrollTimer) return
    scrollTimer = setTimeout(() => {
      const el = gridWrapRef.value
      if (!el) return

      // 当滚动到距离底部100px时触发加载更多
      if (el.scrollHeight - el.scrollTop - el.clientHeight < 100 && !loading.value && !noMore.value) {
        loadMore()
      }
      scrollTimer = null
    }, 200)
  }

  const loadMore = async () => {
    if (loading.value || noMore.value) return

    loading.value = true
    const hasMore = await loadMoreData()
    loading.value = false

    if (!hasMore) {
      noMore.value = true
    }
  }

  // 当数据源或分页信息变化时，重置加载状态
  const resetLoadState = () => {
    noMore.value = pagination.value.current * pagination.value.pageSize >= pagination.value.total
  }

  onMounted(() => {
    resetLoadState()
  })

  const { open, destroy, replaceData } = NiPreviewer()
  const preview = async (item) => {
    if (item.fileExt) {
      let imgList = [
        {
          src: item.fileLink,
          name: item.name || ''
        }
      ]
      await replaceData(imgList)
      open(0)
      logReportHandle([item.id], 2)
    } else {
      openNewFolder(item)
    }
  }
  const operationListHandle = (item, value) => {
    /* value:
      1: 发送给同事
      2: 复制链接
      3: 成员权限管理
      5: 下载
      6: 重命名
      7: 移动到
      9: 删除
    */
    switch (value) {
    case 1:
      forward(item)
      if (item.fileExt) {
        logReportHandle([item.id], 3)
      }
      break
    case 2:
      copyLinkHandle(item)
      break
    case 3:
      openPermission(item)
      break
    case 5:
      saveFileHandle(item)
      if (item.fileExt) {
        logReportHandle([item.id], 4)
      }
      break
    case 6:
      emit('reNameFileHandle', item)
      break
    case 7:
      emit('removeFileHandle', item)
      break
    case 9:
      emit('batchDeleteFileHandle', [item.id])
      break
    default:
      break
    }
  }
  onUnmounted(() => {
    destroy()
    if (scrollTimer) {
      clearTimeout(scrollTimer)
      scrollTimer = null
    }
  })

  // 监听dataSource变化，重置加载状态
  watch(() => props.dataSource, () => {
    resetLoadState()
  })
</script>

<style lang="scss" scoped>
@import "../../common";
.bottom-con {
  position: fixed;
  width: calc(100% - 248px);
  bottom: 0px;
  background: #FFFFFF;
}
</style>
