<template>
  <div class="upload-file">
    <a-button type="primary" @click="handleUploadFile">上传文件</a-button>
    <div class="uploadListWrap" v-if="showUploadList">
      <div class="top-wrap flex flex-align-center flex-justify-between">
        <span class="font-14 bold">传输列表</span>
        <a-icon type="close" class="font-16 bold pointer" @click="closeUploadList"/>
      </div>
      <div class="uploadListCon">
        <div class="uploadListItem flex flex-align-center" v-for="(item, index) in selectedFileList" :key="index">
          <img :src="formatIco(item.fileName)" width="24px" height="24px" class="mr-16">
          <div class="flex-child-average flex flex-col">
            <p class="font-12 bold lines-1">{{ item.fileName }}</p>
            <a-progress :percent="item.progress" size="small" :status="item.status"/>
            <p class="font-12 grey-6">{{ formatBytes(item.fileSize) }}</p>
          </div>
          <div class="flex-child-noshrink flex flex-align-center" style="width: 65px;">
            <a-icon type="delete" style="color: #1890ff; margin-left: 16px" v-if="item.status !== FILE_STATUS.SUCCESS" @click="delUploadItem(index)"/>
            <a-icon type="reload" style="color: #1890ff; margin-left: 16px" v-if="item.status === FILE_STATUS.EXCEPTION" @click="reloadUploadItem(item)"/>
          </div>
        </div>
      </div>
    </div>
    <form
      style="display: none"
      ref="tagFileForm"
      name="tagFileForm">
      <input
        ref="tagFile"
        type="file"
        accept="image/*,.pdf,.pdfx,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.avi,.rmvb,.mpeg,.wmf,.mov,.mkv,.mp4,.mp3,.wav,.m4a,.zip"
        name="tagFile"
        multiple
        @change="selectTagFile"
      />
    </form>
  </div>
</template>

<script setup>
  import { ref, getCurrentInstance, computed } from 'vue'
  import { formatIco } from '../../constants'
  import nineUpload from '@jiuji/nine-upload'
  import Api from '../../../../api/network-disk'
  import { useNetWorkDiskInject } from '../../useNetWorkDisk'
  import UploadFileQueue from './upload-file-queue'

  const queue = new UploadFileQueue()

  const { fetchData, folderId } = useNetWorkDiskInject()
  const typeVal = computed(() => {
    return $store.state.operation.networkDisk.tab
  })

  const { $message, $api, $set, $route, $store } = getCurrentInstance().proxy
  const FILE_STATUS = {
    SUCCESS: 'success',
    EXCEPTION: 'exception',
    NORMAL: 'normal',
    ACTIVE: 'active',
  }
  const tagFileForm = ref(null)
  const tagFile = ref(null)
  const selectedFileList = ref([])
  const getFileExtension = (filename) => {
    const match = filename && filename.match(/\.([^.]+)$/)
    return match ? match[1] : ''
  }

  const handleUploadFile = function () {
    // 清空一下file
    tagFileForm.value.reset()
    tagFile.value.click()
  }

  const createFileList = function (files) {
    return files.map(file => {
      return {
        type: typeVal.value,
        folderId: folderId.value,
        rawFile: file,
        fileName: file.name,
        status: FILE_STATUS.NORMAL,
        fileSize: file.size,
        suffix: getFileExtension(file.name),
        progress: 0,
        fid: '',
        filePath: '',
        fileRelativePath: '',
        isFinished: false,
      }
    })
  }

  const getUploadToken = async function () {
    // 获取上传 token，只需获取一次
    if (!window.nineUploadData) {
      const { code, data, userMsg } = await $api.common.getUploadToken()
      if (code !== 0) {
        $message.error(userMsg)
        return
      }
      window.nineUploadData = data
      setTimeout(() => {
        window.nineUploadData = null
      }, 30 * 60 * 1000)
    }
  }

  const uploadFile = async function (file) {
    try {
      nineUpload({
        files: [file.rawFile],
        onPickFiles: async () => window.nineUploadData,
        multiple: false,
        form: { collection: 'oa-net-drive' },
        chunkSize: 2048000,
        onProgress: ({ percent, fileIndex, fileCount }) => {
          // 不清楚为啥，关闭弹框以后再次上传文件有概率不会触发这个回调
          file.status = FILE_STATUS.ACTIVE
          file.progress = percent
          console.log(`${file.fileName}:${percent}`)
          if (percent >= 99) {
            file.status = FILE_STATUS.SUCCESS
            file.progress = 100
          }
        },
      }).then(async ({ res, err }) => {
        if (err) {
          file.status = FILE_STATUS.EXCEPTION
          $message.error(`${err.name}上传失败：${err.err}`)
          return
        }
        if (res) {
          const fileData = res || {}
          // 为防止onProgress不回调，这里把状态再次改一下
          file.status = FILE_STATUS.SUCCESS
          file.progress = 100
          file.fid = fileData.fid
          file.filePath = fileData.filePath
          file.fileRelativePath = fileData.fileRelativePath
          if (showUploadList.value) queue.add(fileUploadFile(file))
        }
      })
    } catch (error) {
      console.error('上传过程发生错误:', error)
      $message.error('上传过程发生错误，请重试')
    }
  }

  const selectTagFile = async (event) => {
    const newSelectedFileList = createFileList(Array.from(event.target.files))
    selectedFileList.value.push(...newSelectedFileList)
    showUploadList.value = true
    await getUploadToken()
    newSelectedFileList.forEach(item => {
      uploadFile(item)
    })
  }
  const formatBytes = (bytes, decimals = 2) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i]
  }

  const showUploadList = ref(false)

  const closeUploadList = () => {
    // 关闭上传列表时，完全清空所有文件列表
    // 这样下次上传时会重新开始，而不是追加
    selectedFileList.value = []
    showUploadList.value = false
    // 清除队列
    if (queue) {
      queue.clearQueue()
    }
    if (tagFileForm.value) {
      tagFileForm.value.reset()
    }
  }
  const delUploadItem = (index) => {
    // 删除显示列表中的项
    selectedFileList.value.splice(index, 1)

    // 如果删除后列表为空，可以考虑重置状态
    if (selectedFileList.value.length === 0) {
      closeUploadList()
    }
  }
  const reloadUploadItem = async (file) => {
    // 文件已经上传成功
    if (file.fid) {
      if (showUploadList.value) queue.add(fileUploadFile(file))
    } else {
      file.status = FILE_STATUS.NORMAL
      file.progress = 0
      await getUploadToken()
      uploadFile(file)
    }
  }
  const fileUploadFile = (file) => {
    return () => new Promise(async (resolve) => {
      const params = {
        files: {
          fid: file.fid,
          fileName: file.fileName,
          filePath: file.filePath,
          fileSize: file.fileSize,
          suffix: file.suffix,
          fileRelativePath: file.fileRelativePath,
        },
        type: file?.type || typeVal.value,
        folderId: file?.folderId || folderId.value
      }
      const res = await Api.fileUploadFile(params).catch(error => {
        // 如果服务器处理失败，重置状态并显示错误信息
        file.status = FILE_STATUS.EXCEPTION
        file.progress = 100
        $message.error(error.message || '文件上传到服务器失败')
      })
      if (res) {
        // 服务器处理成功
        file.isFinished = true
        file.status = FILE_STATUS.SUCCESS
        file.progress = 100
        await fetchData()
        // 检查是否所有文件都已上传完成
        checkAllFilesFinished()
      }
      resolve()
    })
  }
  // 检查所有文件是否都已上传完成
  const checkAllFilesFinished = () => {
    const allFinished = selectedFileList.value.every(item => item.isFinished === true)
    if (allFinished && showUploadList.value) {
      closeUploadList()
    }
  }

</script>

<style lang="scss" scoped>
.uploadListWrap {
  position: absolute;
  box-sizing: border-box;
  top: 116px;
  right: 20px;
  z-index: 1000;
  width: 500px;
  max-height: 640px;
  overflow-y: auto;
  background: #FFFFFF;
  border-radius: 6px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  .top-wrap {
    padding: 10px 15px;
    border-bottom: 1px solid #F0F0F0;
    position: sticky;
    top: 0;
    background: #FFFFFF;
    z-index: 999;
  }
  .uploadListCon {
    height: calc(100% - 42px);
    overflow-y: auto;
    .uploadListItem {
      padding: 10px 15px;
      border-bottom: 1px solid #F0F0F0;
    }
  }
}
</style>
