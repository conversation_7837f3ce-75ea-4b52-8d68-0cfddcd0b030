export default class UploadFileQueue {
  constructor () {
    this.queue = []
    this.isRunning = false
  }

  add (uploadFile) {
    return new Promise((resolve, reject) => {
      this.queue.push({ uploadFile, resolve, reject })
      this.run()
    })
  }

  async run () {
    if (this.isRunning || this.queue.length === 0) return

    this.isRunning = true
    const { uploadFile, resolve, reject } = this.queue.shift()

    try {
      await uploadFile()
      resolve()
    } catch (error) {
      reject(error)
    }

    this.isRunning = false
    this.run() // 继续执行下一个请求
  }

  clearQueue () {
    this.queue = []
    console.log(this.queue)
  }
}
