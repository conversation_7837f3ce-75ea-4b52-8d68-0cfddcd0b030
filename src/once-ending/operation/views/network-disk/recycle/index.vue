<template>
  <div class="disk-detail-wrap">
    <Table
      :columns="recycleColumns"
      :dataSource="dataSource"
      :isRowSelection="true"
    ></Table>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { recycleColumns } from '../constants'
  import Table from '../components/table'
  import { useNetWorkDiskInject } from '../useNetWorkDisk'
  const {
    dataSource,
  } = useNetWorkDiskInject()
</script>

<style lang="scss" scoped>
@import "../common";
</style>
