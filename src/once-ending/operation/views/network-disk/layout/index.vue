<template>
  <div class="disk-data">
    <a-spin size="large" :delay="200" :spinning="loading">
      <a-layout>
        <a-layout-sider v-model="collapsed" :trigger="null" collapsible>
          <div class="disk-logo border-bottom " :class="collapsed ? 'flex-justify-center' : 'flex-justify-between'">
            <router-link class="font-18 bold blue" v-if="!collapsed" to="/operation/network-disk">OA网盘</router-link>
            <a-icon
              class="trigger"
              style="font-size: 16px; font-weight: bold;"
              :type="collapsed ? 'menu-unfold' : 'menu-fold'"
              @click="collapsedChange"
            />
          </div>
          <Menu v-if="routes && routes.length" :collapsed="collapsed" :routes="routes"></Menu>
        </a-layout-sider>
        <a-layout class="content-body">
          <a-layout-content>
            <keep-alive>
              <router-view
                v-if="$route.meta.keepAlive && hasPermission"
                :key="$route.path"
              >
              </router-view>
            </keep-alive>
            <router-view
              v-if="!$route.meta.keepAlive && hasPermission"
              :key="$route.path"
            >
            </router-view>
            <page403 :isBack="false" v-if="!hasPermission"></page403>
          </a-layout-content>
        </a-layout>
      </a-layout>
    </a-spin>
  </div>
</template>

<script setup>
  import { reactive, ref, computed, getCurrentInstance, watch } from 'vue'
  import Page403 from '@common/views/abnormal-page/403'
  import Menu from '../components/menu/index.vue'
  import { useNetWorkDisk } from '../useNetWorkDisk'
  useNetWorkDisk()

  const root = getCurrentInstance().proxy
  const { $route, $store } = root
  const state = reactive({
    userInfo: $store.state.userInfo
  })
  const routes = ref([])
  const collapsed = computed(() => {
    return $store.state.operation.networkDisk.collapsed
  })
  const collapsedChange = () => {
    $store.commit('operation/networkDisk/changeCollapsed')
  }
  const hasPermission = computed(() => {
    const hasMatePermission = $route.meta && $route.meta.permission
    if (sessionStorage.getItem('isTest')) {
      return true
    }
    // 只判断当前路由，是否mate中有permission，如果没有，则认为不需要权限，则显示路由页面，由后续处理
    if (!hasMatePermission) {
      return true
    }
    const permission = state.userInfo ? state.userInfo.Rank || [] : []
    return !$route.matched.some(
      r => r.meta.permission && !permission.includes(r.meta.permission)
    )
  })

  const loading = computed(() => {
    return $store.state.loading
  })
  watch(() => $store.state.operation.networkDisk.routes, (newVal, oldVal) => {
    if (newVal && Array.isArray(newVal)) {
      routes.value = [...newVal]
    }
  }, {
    deep: true,
    immediate: true
  })
</script>

<style lang="scss" scoped>
.disk-data{
  height: 100vh;
  .disk-logo {
    display: flex;
    height: 60px;
    align-items: center;
    padding: 0 15px;
    .router-link-active {
      text-decoration: none;
      color: #000000;
    }
  }
  :deep(.ant-layout-sider) {
    background: #FFFFFF;
  }
  :deep(.ant-layout-content) {
    height: 100vh;
  }
  .content-body {
    padding: 0 0 0 8px;
    .layout-breadcrumb{
      box-sizing: border-box;
      padding: 20px 0 0 16px;
    }
  }
}
</style>
