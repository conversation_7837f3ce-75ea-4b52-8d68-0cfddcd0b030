<script lang="jsx">
  import { ref, reactive, provide, defineComponent, getCurrentInstance } from 'vue'
  import { NiListPage } from '@jiuji/nine-ui'
  import searchBox from './components/search-box.vue'
  import tableBox from './components/table-box.vue'
  import moment from 'moment'
  import { GET_RECEPTION_LIST } from '@operation/store/modules/reception/action-types'
  export default defineComponent({
    name: 'index',
    components: {
      NiListPage,
      searchBox,
      tableBox
    },
    setup () {
      const { proxy } = getCurrentInstance()
      const loading = ref(false)
      const isFeatch = ref(false)
      const exportLoading = ref(false)
      const form = reactive({
        areaIds: [],
        timeRange: undefined,
        userIds: undefined,
        age: undefined,
        brand: undefined,
        auditList: undefined
      })
      const tableList = ref([])
      provide('form', form)
      provide('loading', loading)
      provide('isFeatch', isFeatch)
      provide('tableList', tableList)
      const pagination = reactive({
        pageSize: 20,
        current: 1,
        total: 0,
        showTotal: (total) => `共${total}条数据`,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ['10', '20', '35', '50']
      })
      provide('pagination', pagination)
      const fetchData = async function (current) {
        current && (pagination.current = 1)
        let params = {
          ...form,
          current: pagination.current,
          size: pagination.pageSize,
        }
        if (form.timeRange && form.timeRange.length) {
          params.startTime = form.timeRange[0]
          params.endTime = form.timeRange[1]
          if (moment(params.startTime).add(3, 'months') < moment(params.endTime)) {
            proxy.$message.warning('时间间隔不能超过三个月')
            return
          }
        }

        delete params.timeRange
        loading.value = true
        const res = await proxy.$store.dispatch(`operation/reception/${GET_RECEPTION_LIST}`, params)
        loading.value = false
        if (res) {
          tableList.value = res.data.records
          isFeatch.value = true
          pagination.total = res.data.total
        }
      }
      function tableChange (newPagination) {
        Object.assign(pagination, newPagination)
        fetchData()
      }
      return {
        loading,
        fetchData,
        exportLoading,
        form,
        tableList,
        pagination,
        tableChange
      }
    },
    render () {
      const { form, tableList, pagination, fetchData, exportLoading, loading, tableChange } = this
      return <page class="reception-page">
        <ni-list-page pushFilterToLocation={false}>
          <search-box export-loading={exportLoading}
                      form={form}
                      loading={loading}
                      onSearch={() => fetchData(1)}>
          </search-box>
          <table-box ref='refTableBox'
                     loading={loading}
                     class="mt-16"
                     onFetchData={() => fetchData()}
                     pagination={pagination}
                     onTableChange={tableChange}
                     table-list={tableList}></table-box>
        </ni-list-page>
      </page>
    }
  })
</script>

<style scoped lang="scss">
.reception-page {
  position: relative;
}
</style>
