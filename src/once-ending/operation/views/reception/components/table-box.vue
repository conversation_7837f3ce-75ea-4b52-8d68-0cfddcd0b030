<script type="text/jsx" lang="jsx">
  import { NiTable, NiImg } from '@jiuji/nine-ui'
  import Vue, { inject, defineComponent, ref, nextTick, getCurrentInstance } from 'vue'
  import 'viewerjs/dist/viewer.css'
  import Viewer from 'v-viewer'
  import NoData from '@operation/components/no-data'
  Vue.use(Viewer)
  export default defineComponent({
    name: 'table-box',
    components: {
      NiTable,
      NiImg,
      NoData
    },
    setup () {
      const tableList = inject('tableList')
      const loading = inject('loading')
      const isFeatch = inject('isFeatch')
      const pagination = inject('pagination')
      const images = ref([])
      const { proxy } = getCurrentInstance()
      const viewImage = function (urls) {
        images.value = [urls]
        nextTick(() => {
          // 找到.images的div挂载到$viewer上
          const viewer = proxy.$el.querySelector('.images').$viewer
          viewer.show()
        })
      }
      const textCut = (str) => {
        if (str) {
          return str.length > 10 ? str.substring(0, 10) + '...' : str
        } else {
          return '-'
        }
      }
      return {
        tableList,
        loading,
        isFeatch,
        pagination,
        viewImage,
        images,
        textCut
      }
    },
    data () {
      return {
        columns: [
          {
            title: '接待员工',
            dataIndex: 'receiveUserName',
            width: '100px',
            customRender: (text) =>
              <span> { text || '--' } </span>
          },
          {
            title: '图片',
            dataIndex: 'pic',
            width: '120px',
            customRender: (text) => text ? <div class="pointer" onClick={ () => { this.viewImage(text) } }>
                <NiImg class="show-img" src={ text }/>
              </div> : '暂无图片'
          },
          {
            title: '开始接待时间',
            width: '140px',
            dataIndex: 'receiveStartTime',
            customRender: (text) =>
              <span> { text || '--' } </span>
          },
          {
            title: '结束接待时间',
            width: '140px',
            dataIndex: 'receiveEndTime',
            customRender: (text) =>
              <span> { text || '--' } </span>
          },
          {
            title: '审核结果',
            width: '100px',
            forceHide: this.$tnt.xtenant !== 0,
            dataIndex: 'auditStatus',
            customRender: (text) =>
              <span> { text || '--' } </span>
          },
          {
            title: this.$tnt.xtenant === 0 ? '接待门店' : '门店',
            width: '100px',
            dataIndex: 'areaName',
            customRender: (text) =>
              <span> { text || '--' } </span>
          },
          {
            title: '小区',
            width: '100px',
            dataIndex: 'smallArea',
            customRender: (text) =>
              <span> { text || '--' } </span>
          },
          {
            title: '大区',
            width: '100px',
            dataIndex: 'bigArea',
            customRender: (text) =>
              <span> { text || '-' } </span>
          },
          {
            title: '备注',
            width: '100px',
            dataIndex: 'remark',
            forceHide: this.$tnt.xtenant !== 0,
            customRender: (text) => {
              return <div>
                {
                  text && text.length > 10 ? <a-popover>
                    <template slot="content">
                      <p class="remarkText">{text}</p>
                    </template>
                    <span class="pointer">{this.textCut(text)}</span>
                  </a-popover> : <span>{text || '-'}</span>
                }
              </div>
            }
          }
        ]
      }
    },
    render () {
      const { tableList, columns, loading, isFeatch, pagination, images } = this
      return <div>
        <NiTable
        align="center"
        locale={{ emptyText: <NoData is-featch={isFeatch} /> }}
        dataSource={ tableList }
        columns={ columns }
        onChange={(newPagination) => { this.$emit('tableChange', newPagination) }}
        pagination={ pagination }
        loading={ loading }
        footerTotalNum={ 0 }/>
        <div class="images" v-viewer={{ movable: false }} style="display: none">
          { images.map(src => <img src={src} key={src}/>)}
        </div>
      </div>
    }
  })
</script>

<style lang="scss" scoped>
:deep(.remarkText) {
  width: 200px;
}
:deep(.view-img) {
  width: 380px;
  height: 340px;
}
:deep(.show-img) {
  width: 60px;
  height: 50px;
}
</style>
