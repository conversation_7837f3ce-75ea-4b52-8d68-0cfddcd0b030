<script lang="jsx">
  import { NiFilter, NiFilterItem, NiAreaSelect, NiStaffSelect } from '@jiuji/nine-ui'
  import { reactive, toRefs, getCurrentInstance, inject, defineComponent } from 'vue'
  import moment from 'moment'
  import { allOptions } from '../constants'
  export default defineComponent({
    name: 'search-box',
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect,
      NiStaffSelect
    },
    props: {},
    setup () {
      const form = inject('form')
      const loading = inject('loading')
      function getPopupContainer () {
        return document.querySelector('.reception-page')
      }
      return {
        form,
        loading,
        getPopupContainer
      }
    },
    render () {
      const { form, loading, getPopupContainer } = this
      return <ni-filter
        form={ form }
        loading={ loading }
        onFilter={() => this.$emit('search')}>
        <ni-filter-item label="地区" class="areas-select">
          <NiAreaSelect
            placeholder="请选择地区"
            class="area-selector"
            max-tag-count={1}
            multiple
            allowClear
            mode={ 2 }
            treeNodeFilterProp="title"
            v-model={form.areaIds}
            getPopupContainer={getPopupContainer}
            style="min-width: 260px"
          />
        </ni-filter-item>

        <ni-filter-item label="接待时间">
          <a-range-picker
            v-model={ form.timeRange }
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder={ ['开始时间', '结束时间'] }
            show-time={{ defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')] }}
            getPopupContainer={ getPopupContainer }
        />
        </ni-filter-item>

        <ni-filter-item label="员工">
          <NiStaffSelect
            class={{ 'more-select': form.userIds && form.userIds.length && form.userIds.length > 1 }}
            v-model={form.userIds}
            max-tag-count={1}
            multiple={true}
            option-filter-prop="children"
            allow-clear={true}
            show-search={true}
            placeholder="请输入员工姓名/工号"
          ></NiStaffSelect>
        </ni-filter-item>

        {this.$tnt.xtenant === 0 && <ni-filter-item label="审核结果">
          <a-select
          mode="multiple"
          placeholder="请选择审核结果"
          v-model={form.auditList}
          allow-clear={true}
          options={allOptions.auditResultOptions}/>
        </ni-filter-item>}
      </ni-filter>
    }
  })
</script>

<style lang="scss" scoped>
.more-select{
  :deep(.ant-select-selection__choice){
    max-width: 45%;
  }
}
</style>
