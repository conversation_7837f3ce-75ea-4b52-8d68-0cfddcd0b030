<template>
  <div>
    <a-modal
      title="更换流程图"
      :visible="visible"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-upload :action="uploadPath"
                :headers="header"
                listType="picture-card"
                accept="image/*"
                :fileList="fileList"
                :beforeUpload="beforeUpload"
                @preview="handlePreview"
                @change="handlePhotoChange">
          <div class="ant-upload-text" v-if="fileList.length < 1">上传流程图</div>
      </a-upload>
    </a-modal>
  </div>
</template>

<script>
  import { reactive, toRefs, getCurrentInstance, computed } from 'vue'
  import complaintApi from '@operation/api/complaint'
  export default {
    props: {
      visible: Boolean,
      default: false
    },
    setup (props, { emit }) {
      const root = getCurrentInstance()
      const { $api, $store, $message } = root.proxy
      const header = computed(() => {
        return {
          Authorization: $store.state.token
        }
      })
      const state = reactive({
        uploadPath: $api.common.uploadPathNew,
        fileList: []
      })
      const beforeUpload = () => {}
      const handlePreview = (file) => {
        let imgUrl = ''
        if (file.response) {
          imgUrl = file.response.data.filePath
        } else {
          imgUrl = file.url
        }
        window.open(imgUrl)
      }
      const handleOk = () => {
        if (state.fileList.length > 0) {
          let params = {
            image: state.fileList[0].response.data.filePath
          }
          complaintApi.modifyBusinessFlowChart(params).then(res => {
            if (res.code === 0) {
              $message.success('上传成功')
              handleCancel()
            } else {
              $message.error(res.userMsg)
            }
          })
        } else {
          $message.warn('请上传流程图')
        }
      }
      const handleCancel = () => {
        emit('closeFlowChart')
        state.fileList = []
      }
      const handlePhotoChange = ({ file, fileList }) => {
        state.fileList = fileList
      }
      return {
        header,
        ...toRefs(state),
        handleOk,
        handleCancel,
        beforeUpload,
        handlePreview,
        handlePhotoChange
      }
    }
  }
</script>

<style lang="scss" scoped>
:deep(.ant-modal-body) {
  max-height: 450px;
}
:deep(.ant-upload-list-picture-card-container) {
  width: 100%;
  height: 400px;
  span {
    display: inline-block;
    width: 100%;
  }
}
:deep(.ant-upload-list-picture-card .ant-upload-list-item) {
  float: none;
  width: 400px;
  height: 400px;
  text-align: center;
  margin: 0 auto;
}
:deep(.ant-upload.ant-upload-select-picture-card) {
  width: 100%;
  height: 45px;
}
</style>
