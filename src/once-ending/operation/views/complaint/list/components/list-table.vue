<script lang="jsx">
  import { defineComponent, inject, ref, getCurrentInstance } from 'vue'
  import { NiTable } from '@jiuji/nine-ui'
  import { Button } from 'ant-design-vue'
  import {
    xtenantOptions,
    columns,
    catOptions,
    touSuRankOptions,
    searchStateOptions,
    rankColor,
    tagOptions,
    catMap,
    tagMap
  } from '../constants' // 数据层
  import moment from 'moment'
  import complaint from '@operation/api/complaint'
  import { getStatusStyle, statesEnum } from '@/operation/views/complaint/detail/constants'
  import UserLevel from '../../detail/components/user-level.vue'
  export default defineComponent({
    components: {
      NiTable,
      UserLevel
    },
    props: {
      dataSource: {
        type: Array,
        default: () => {
          return []
        }
      },
      loading: {
        type: Boolean,
        default: false
      },
      pagination: {
        type: Object,
        default: () => {
          return {
            current: 1,
            pageSize: 10,
            total: 0,
            showTotal: total => `总共 ${total} 条`
          }
        }
      }
    },
    data () {
      return {
        customRenderMap: new Map([
          [
            'tsId',
            (text, record) => (
            <router-link target="_blank" to={`detail/${text}`}>{{ text }}</router-link>
          )
          ],
          [
            'tag',
            (text, record) => record.tag ? <div class="tag" style={tagMap.get(record.tag).style}>{tagMap.get(record.tag).name}</div> : '--'
          ],
          [
            'catName',
            (text, record) => record.cat ? <div class="cat" style={catMap.get(record.cat)}>{record.catName}</div> : '--'
          ],
          [
            'userId',
            (text, record) => (
            <div>
              <span>{record.userClassName ? text : '已注销'}</span>
              {record.userClassName ? <span>【{record.tcount}】</span> : null}
              {!this.$store.state.userInfo.IsTaxModel &&
              record.userClassName ? (
                <span class="red">
                  {record.xtenantName
                    ? `【${record.xtenantName}】`
                    : ''}
                </span>
              ) : null}
            </div>
          )
          ],
          [
            'userClassName',
            (text, record) => (
            <div>
              {text ? (
               <UserLevel member={record}/>
              ) : (
                <span>-</span>
              )}
            </div>
          )
          ],
          [
            'writer',
            (text, record) => (
            <div>
              {text}
              <span style="color:red;">({record.tcount})</span>
            </div>
          )
          ],
          [
            'addTime',
            (text, record) => (
            <div class={{ red: record.timeOut }}>
              {moment(text).format('YYYY-MM-DD HH:mm')}
            </div>
          )
          ],
          [
            'content',
            (text, record) => (
            <div style="min-width:40px;">
              <p style="zoom:.95;text-overflow:ellipsis;overflow: hidden">
                {text}
              </p>
            </div>
          )
          ],
          [
            'departAreaName',
            (text, record) => (
            <div>
              {record.zenRenAreasAndDeparts.filter(a => a.type === 0).length ? (
                record.zenRenAreasAndDeparts
                  .filter(a => a.type === 0)
                  .map(d => {
                    const cat = this.getTableValue(catOptions, d && d.cat ? d.cat : null)
                    const touSuRank = this.getTableValue(touSuRankOptions, d.touSuRank)
                    const scoreArea = !!(d.scoreArea || d.scoreArea === 0)
                    let cache = []
                    if (d.departAreaName) {
                      const departAreaName = <span>{d.departAreaName}</span>
                      cache.push(departAreaName)
                    }
                    if (scoreArea) {
                      const area = <span class="red">扣分：{d.scoreArea || 0}</span>
                      cache.push(area)
                    }
                    if (cat) {
                      cache.push(cat)
                    }
                    if (touSuRank) {
                      const rank = <span
                        style={{
                          color: rankColor[d.touSuRank]
                        }}
                      >
                        {touSuRank}
                      </span>
                      cache.push(rank)
                    }
                    return <div>
                      <span>{d.areaName}</span>
                      { cache?.length ? <span>（{
                        cache.map((it, index) => <span>{it}{index + 1 !== cache.length && cache.length > 1 ? '，' : null}</span>)
                      }）</span> : null }
                    </div>
                  })
              ) : (
                <span>无</span>
              )}
            </div>
          )
          ],
          [
            'depart',
            (text, record) => (
            <div>
              {record.zenRenAreasAndDeparts.filter(a => a.type === 1).length ? (
                record.zenRenAreasAndDeparts
                  .filter(a => a.type === 1)
                  .map(d => {
                    const cat = this.getTableValue(catOptions, d && d.cat ? d.cat : null)
                    const touSuRank = this.getTableValue(touSuRankOptions, d.touSuRank)
                    const scoreDep = !!(d.scoreDep || d.scoreDep === 0)
                    const showBracket = Boolean(d.departAreaName || d.scoreDep || cat || touSuRank)
                    let cache = []
                    if (d.departmentCentName) {
                      cache.push(<span>{d.departmentCentName}</span>)
                    }
                    if (scoreDep) {
                      cache.push(<span class="red">扣分：{d.scoreDep}</span>)
                    }
                    if (cat) {
                      cache.push(cat)
                    }
                    if (touSuRank) {
                      cache.push(<span
                        style={{
                          color: rankColor[d.touSuRank]
                        }}
                      >
                        {touSuRank}
                      </span>)
                    }
                    return <div>
                      <span>{d.department}</span>
                      { cache?.length ? <span>（{
                        cache.map((it, index) => <span>{it}{index + 1 !== cache.length && cache.length > 1 ? '，' : null}</span>)
                      }）</span> : null }
                    </div>
              })
              ) : (
                <span>无</span>
              )}
            </div>
          )
          ],
          [
            'states',
            (text, record) => (
            <div>
              {text ? (
                <div class="status" style={getStatusStyle(record.states)}>{statesEnum[record.states]}</div>
              ) : (
                <span>暂无</span>
              )}
            </div>
          )
          ]
        ])
      }
    },
    computed: {
      columns () {
        columns.forEach(d => {
          const customRender = this.customRenderMap.get(d.dataIndex)
          if (customRender) {
            d.customRender = customRender
          }
        })
        return columns
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const getParams = inject('getParams')

      const exportLoading = ref(false)

      const tableChange = function (paginations) {
        props.pagination.current = paginations.current
        props.pagination.pageSize = paginations.pageSize
        proxy.$emit('fetchData')
      }

      const getTableValue = function (list, value) {
        if (!list.find(item => item.value === value)) return ''
        return list.find(item => item.value === value).label
      }

      const exportExcel = function () {
        exportLoading.value = true
        const url = complaint.exportComplaintUrl()
        const params = getParams()
        const fileName = `投诉管理-${moment().format(
          'YYYYMMDDHHmmss'
        )}.xlsx`
        complaint
          .exportXlsx({
            url,
            method: 'post',
            params,
            fileName: fileName,
            timeOut: 600 * 1000,
            token: proxy.$store.state.token
          })
          .then(res => {
            const link = document.createElement('a')
            let blob = new Blob([res.data], { type: 'application/x-excel' })
            link.style.display = 'none'
            link.href = URL.createObjectURL(blob)
            link.download = fileName
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
          })
          .finally(() => {
            exportLoading.value = false
          })
      }

      return {
        exportLoading,
        tableChange,
        getTableValue,
        exportExcel
      }
    },
    render () {
      const {
        dataSource,
        columns,
        loading,
        tableChange,
        pagination,
        exportLoading,
        exportExcel
      } = this
      return (
      <NiTable
        dataSource={dataSource}
        columns={columns}
        loading={loading}
        onChange={tableChange}
        pagination={pagination}
      >
        <div slot="tool">
          <Button
            style="float:right"
            type="primary"
            loading={exportLoading}
            onClick={exportExcel}
          >
            {!exportLoading ? '导出客诉' : '导出中'}
          </Button>
        </div>
      </NiTable>
      )
    }
  })
</script>
<style lang="scss" scoped>
:deep(.status) {
  height: 20px;
  border-radius: 2px;
  font-size: 12px;
  line-height: 12px;
  padding: 4px;
  box-sizing: border-box;
  display: inline-block;
}
:deep(.cat) {
  padding: 4px;
  font-size: 12px;
  line-height: 12px;
  display: inline-block;
  border-radius: 2px;
}
:deep(.tag) {
  padding: 4px 6px 4px 6px;
  font-size: 12px;
  color: #FFFFFF;
  line-height: 12px;
  border-radius: 3px;
  height: 20px;
  display: inline-block;
  box-sizing: border-box;
}
</style>
