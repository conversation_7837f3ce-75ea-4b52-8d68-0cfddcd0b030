<script lang="jsx">
  import { defineComponent, ref, getCurrentInstance, computed } from 'vue'
  import { Select, Input, DatePicker, Checkbox } from 'ant-design-vue'
  import { NiFilter, NiFilterItem, NiAreaSelect, NiDepartSelect } from '@jiuji/nine-ui'
  import {
    tagOptions,
    userClassOptions,
    xtenantOptions,
    catOptions,
    showWebOptions,
    kindsOptions,
    touSuRankOptions,
    searchStateOptions,
    visibleOptions,
    // customerOptions,
    dateTypeOptions,
    searchKindOptions,
    plainOptions1,
    plainOptions2,
    typesOptions,
    sourceOptions,
    overFiveFlagOptions
  } from '../constants'
  import AreaSelector from '~/components/staff/area-selector'
  import Reason from '../../components/reason-select' // 投诉原因列表

  export default defineComponent({
    components: {
      NiFilter,
      NiFilterItem,
      AreaSelector,
      Reason,
      NiAreaSelect,
      NiDepartSelect
    },
    props: {
      form: {
        type: Object,
        default: () => ({})
      },
      loading: {
        type: <PERSON><PERSON><PERSON>,
        default: false
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const showArea = ref(true)

      const hasGjkp = computed(() => proxy.$store.state.userInfo.Rank.includes('gjkp') || proxy.$store.state.userInfo.Rank.includes('99'))

      const reset = function () {
        showArea.value = false
        proxy.$nextTick(() => {
          showArea.value = true
        })
      }

      const handleStatusIsIng = ref(false)

      const inProgressChange = function (e) {
        const value = e.target.checked
        handleStatusIsIng.value = value
        if (value) props.form.searchState = undefined
      }

      return {
        showArea,
        reset,
        handleStatusIsIng,
        inProgressChange,
        hasGjkp
      }
    },
    render () {
      const {
        form,
        reset,
        loading,
        showArea,
        handleStatusIsIng,
        inProgressChange,
        hasGjkp
      } = this
      return (
        <NiFilter
          form={form}
          onFilter={() => {
            this.$emit('fetchData', 1)
          }}
          unfold-count={window.innerWidth < 1600 ? 6 : 8}
          immediate={false}
          onReset={reset}
          label-width={120}
          itemMinWidth={400}
          loading={loading}
          save-able={false}
        >
          <ni-filter-item label="性质">
            <Select
              allow-clear
              mode="multiple"
              allowClear
              maxTagCount={1}
              placeholder="请选择"
              v-model={form.tags}
              options={tagOptions}
            />
          </ni-filter-item>
          {!this.$store.state.userInfo.IsTaxModel && this.$tnt.xtenant === 0 ? (
            <ni-filter-item label="投诉主体">
              <Select
                allow-clear
                placeholder="请选择"
                v-model={form.xtenant}
                options={xtenantOptions}
              />
            </ni-filter-item>
          ) : null}
          <ni-filter-item label="定性分类">
            <Select
              allow-clear
              placeholder="请选择"
              v-model={form.cat}
              options={catOptions}
            />
          </ni-filter-item>
          <ni-filter-item label="处理状态">
            <Select
              allow-clear
              disabled={handleStatusIsIng}
              placeholder="请选择"
              mode="multiple"
              maxTagCount={1}
              v-model={form.stateList}
              options={searchStateOptions}
            />
          </ni-filter-item>
          <ni-filter-item label="责任部门">
            <NiDepartSelect
              maxTagCount={1}
              returnedValue="ALL"
              multiple={true}
              value={form.depart} onChange={(val) => {
              form.depart = val
            }}/>
            {/* {showArea && (
            <AreaSelector
              onChange={(list, node, selectedid, all, allSelectedid) => {
                form.depart = allSelectedid
              }}
              style="flex-grow: 1;min-width: 0;"
              type="department"
              placeholder="部门"
            />
          )} */}
          </ni-filter-item>
          <ni-filter-item label="责任地区">
            <NiAreaSelect
              maxTagCount={1}
              allowClear
              multiple={true}
              v-model={form.areaIdM}
              placeholder="请选择地区"/>
            {/* {showArea && (
            <AreaSelector
              onChange={(list, node, selectedId) => {
                form.areaIdM = selectedId
              }}
              style="width:100%;min-width: 0;"
              type="area"
              placeholder="地区"
            />
          )} */}
          </ni-filter-item>
          <ni-filter-item label="会员等级">
            <Select
              allow-clear
              placeholder="请选择"
              v-model={form.userClass}
              options={userClassOptions}
            />
          </ni-filter-item>

          <ni-filter-item label="投诉分类">
            <Select
              allow-clear
              mode="multiple"
              placeholder="请选择"
              maxTagCount={1}
              v-model={form.kindList}
              options={kindsOptions}
            />
          </ni-filter-item>
          <ni-filter-item label="投诉等级">
            <Select
              allow-clear
              placeholder="请选择"
              v-model={form.touSuRank}
              checked={form.touSuRank}
              options={touSuRankOptions}
            />
          </ni-filter-item>

          <ni-filter-item label="网站可见">
            <Select
              allow-clear
              placeholder="请选择"
              v-model={form.showWeb}
              options={showWebOptions}
            />
          </ni-filter-item>
          {/* <ni-filter-item label="客诉状态">
            <Select
              allow-clear
              placeholder="请选择"
              v-model={form.customer}
              options={customerOptions}
            />
          </ni-filter-item> */}
          <ni-filter-item>
            <Input.Group compact>
              <Select
                allow-clear
                placeholder="请选择"
                dropdown-match-select-width={false}
                style="width:30%;"
                v-model={form.dateType}
                options={dateTypeOptions}
              />
              <DatePicker.RangePicker
                v-model={form.times}
                style="width:69%;"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </Input.Group>
          </ni-filter-item>
          <ni-filter-item label="投诉原因">
            <Reason
              placeholder="投诉原因"
              v-model={form.touSuTypeVal}
              onChange={list => {
                form.touSuTypeVal = list
              }}
            />
          </ni-filter-item>
          <ni-filter-item>
            <Input.Group compact>
              <Select
                style="width:30%;"
                placeholder="请选择"
                v-model={form.searchKind}
                options={searchKindOptions}
              />
              <Input
                v-model={form.key}
                style="width: 70%"
                placeholder="输入内容"
              />
            </Input.Group>
          </ni-filter-item>
          {
            this.$tnt.xtenant < 1000 ? <ni-filter-item label="补偿方式">
              <Select
                allow-clear
                placeholder="请选择"
                v-model={form.types}
                options={typesOptions}
                mode="multiple"
              />
            </ni-filter-item> : null
          }

          {
            this.$tnt.xtenant < 1000 ? <ni-filter-item label="投诉来源">
              <Select
                allow-clear
                placeholder="请选择"
                v-model={form.tsType}
                options={sourceOptions(hasGjkp)}
                mode="multiple"
                maxTagCount={1}
              />
            </ni-filter-item> : null
          }
          {
            this.$tnt.xtenant < 1000 ? <ni-filter-item label="是否超五星好评">
              <Select
                allow-clear
                placeholder="请选择"
                v-model={form.overFiveFlag}
                options={overFiveFlagOptions}
              />
            </ni-filter-item> : null
          }
          {
            this.$tnt.xtenant < 1000 ? <ni-filter-item class="no-label">
              <div class="flex flex-align-center" style="margin-left:30px">
                <Checkbox.Group
                  class="flex flex-child-noshrink flex-align-center"
                  v-model={form.value1}
                  options={plainOptions1}
                />
                <Checkbox.Group
                  class="flex flex-child-noshrink flex-align-center"
                  style="margin-left:8px"
                  v-model={form.value2}
                  options={plainOptions2.filter(item => item.value !== 5)}
                />
              </div>
            </ni-filter-item> : null
          }
          {
            this.$tnt.xtenant < 1000 ? <ni-filter-item class="no-label">
              <div class="flex flex-align-center" style="margin-left:30px">
                <Checkbox class="flex flex-child-noshrink flex-align-center" onChange={inProgressChange}
                          v-model={form.inProgress}>
                  进行中
                </Checkbox>
                <Checkbox class="flex flex-child-noshrink flex-align-center" v-model={form.selfFlag}>
                  仅个人相关
                </Checkbox>
                <Checkbox class="flex flex-child-noshrink flex-align-center" v-model={form.followFlag}>
                  我关注的
                </Checkbox>
              </div>
            </ni-filter-item> : null
          }
          {
            this.$tnt.xtenant >= 1000 ? <ni-filter-item class="no-label">
              <Checkbox.Group
                style="margin-left:33px"
                v-model={form.value1}
                options={plainOptions1}
              />
            </ni-filter-item> : null
          }
          {
            this.$tnt.xtenant >= 1000 ? <ni-filter-item class="no-label">
              <div class="flex flex-align-center" style="margin-left:33px">
                <Checkbox.Group
                  v-model={form.value2}
                  options={this.$tnt.xtenant < 1000 ? plainOptions2.filter(item => item.value !== 5) : plainOptions2}
                />
                <Checkbox class="flex flex-child-noshrink flex-align-center" style="margin-left:8px"
                          onChange={inProgressChange} v-model={form.inProgress}>
                  进行中
                </Checkbox>
              </div>
            </ni-filter-item> : null
          }

        </NiFilter>
      )
    }
  })
</script>
<style lang="scss" scoped>
.ant-btn + .ant-btn {
  margin-left: 8px;
}

.ant-col {
  padding: 10px 5px;
  display: flex;
  flex-wrap: wrap;

  span {
    padding-right: 5px;
    white-space: nowrap;
  }
}

#__DEV_INFO {
  display: none;
}

.no-label {
  :deep(.label) {
    opacity: 0;
  }
}
</style>
