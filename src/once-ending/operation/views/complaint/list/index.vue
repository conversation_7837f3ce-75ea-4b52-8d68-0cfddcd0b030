<script type="text/jsx" lang="jsx">
  import { defineComponent, ref, provide, onActivated, reactive, getCurrentInstance } from 'vue'
  import ListForm from './components/list-form.vue'
  import ListTable from './components/list-table.vue'
  import FlowChart from './components/flowChart.vue'
  import { NiListPage } from '@jiuji/nine-ui'
  import { TOU_SU_RES_LIST } from '@operation/store/modules/complaint/action-types'
  import { Button } from 'ant-design-vue'
  import moment from 'moment'
  export default defineComponent({
    components: {
      ListForm,
      ListTable,
      NiListPage,
      FlowChart
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const loading = ref(false)

      const dataSource = ref([])

      const flowChartVisible = ref(false)

      const form = reactive({
        tags: undefined, // 性质
        xtenant: undefined, // 投诉主体
        userClass: undefined, // 会员等级
        cat: undefined, // 定性分类
        kindList: undefined, // 投诉分类
        touSuRank: undefined, // 投诉等级
        depart: [], // 责任部门
        areaIdM: [], // 责任地区
        stateList: undefined, // 处理状态
        inProgress: false, // 进行中
        selfFlag: false, // 仅个人相关
        followFlag: false, // 我关注的
        bonus: false, // 奖金
        fine: false, // 罚款
        overTime: false, // 超时
        // 高级筛选
        // customer: undefined, // 客诉状态
        dateType: 1, // 时间筛选类型
        touSuTypeVal: [], // 投诉原因
        searchKind: 6, // 其他筛选条件
        optimize: false, // 优化点
        typical: false, // 典型
        times: null,
        value1: [],
        value2: [],
        types: [], // 补偿方式
        showWeb: undefined,
        tsType: undefined,
        overFiveFlag: 0
      })

      const pagination = ref({ current: 1, pageSize: 10, total: 0, showTotal: total => `总共 ${total} 条` })

      const getParams = function (cur) {
        if (cur) pagination.value.current = cur
        let { times, dateType, value1, value2, types, selfFlag, overFiveFlag, ...req } = form
        req.overFiveFlag = overFiveFlag === undefined ? 2 : overFiveFlag
        req.bonus = value1.includes(1)
        req.fine = value1.includes(2)
        req.overTime = value1.includes(3)
        req.optimize = value2.includes(4)
        req.typical = proxy.$tnt.xtenant >= 1000 ? value2.includes(5) : undefined
        if (times && times.length && dateType) {
          req.isGaoJi = true
          req.dateType = dateType
          req.startTime = `${times[0]} 00:00:00`
          req.endTime = `${times[1]} 23:59:59`
        }
        const { current, pageSize: size } = pagination.value
        req.current = current
        req.size = size
        if (selfFlag) req.selfFlag = 1
        if (proxy.$tnt.xtenant < 1000) req.types = types
        return req
      }

      provide('getParams', getParams)

      const fetchData = async function (current) {
        const params = getParams(current)
        loading.value = true
        const res = await proxy.$store.dispatch(`operation/complaint/${TOU_SU_RES_LIST}`, params)
        loading.value = false
        if (res) {
          const { data: { records, total } } = res
          dataSource.value = records
          pagination.value.total = total
        }
      }

      onActivated(() => {
        if (proxy.$route.params.areaIdM) {
          form.areaIdM = [proxy.$route.params.areaIdM]
        }
        fetchData()
      })

      const setForm = function () {
        const { history, areaIdM, tags, ...others } = proxy.$route.query
        const query = { ...others }
        const reg = /^(-?\d+)(\.\d+)?$/
        for (let k in query) {
          if (k !== 'endTime' && k !== 'startTime') {
            form[k] = reg.test(query[k]) ? parseInt(query[k]) : query[k]
          }
          const typeList = ['kindList', 'stateList', 'touSuTypeVal', 'areaIdM', 'depart']
          if (typeList.includes(k)) {
            const typeNum = typeList.slice(0, 3)
            const val = query[k] ? query[k]?.split(',') : []
            form[k] = typeNum.includes(k) ? val.map(it => +it) : val
          }
        }
        if (Object.keys(query).includes('endTime') && Object.keys(query).includes('startTime')) {
          query.startTime = moment(new Date(query.startTime)).format('YYYY-MM-DD')
          query.endTime = moment(new Date(query.endTime)).format('YYYY-MM-DD')
          form.times = [query.startTime, query.endTime]
        }
        if (areaIdM) {
          form.areaIdM = [areaIdM]
        }
        if (history === '1') {
          form.overFiveFlag = undefined
        }
        if (tags) {
          form.tags = tags.split(',').map(it => +it)
        }
        console.log('form', form)
      }

      setForm()

      const toComplaintOvertime = function () {
        const routeData = proxy.$router.resolve({
          path: '/operation/statistics-report/complaint-overtime'
        })
        const url = routeData.href
        window.open(url, '_blank')
      }

      const toLink = function () {
        proxy.$router.push({
          path: '/complaint/add'
        })
      }

      const openFlowChart = () => {
        flowChartVisible.value = true
      }

      const closeFlowChart = () => {
        flowChartVisible.value = false
      }

      return {
        form,
        dataSource,
        pagination,
        fetchData,
        loading,
        toComplaintOvertime,
        toLink,
        flowChartVisible,
        openFlowChart,
        closeFlowChart,
        isJiJi: proxy.$tnt.xtenant < 1000
      }
    },
    beforeRouteLeave (to, from, next) {
      localStorage.removeItem('complaintStatisticsParams')
      next()
    },
    render () {
      const { toComplaintOvertime, loading, form, dataSource, pagination, fetchData, toLink, flowChartVisible, openFlowChart, closeFlowChart, isJiJi } = this
      return <page>
  <template slot="extra">
    <Button type="primary" onClick={openFlowChart}>更换流程图</Button>
    { isJiJi ? <Button type="primary"><a rel="noopener noreferrer" href={`${this.$tnt.oaHost}/staticpc/#/complaint/statistics`} target="_blank">投诉统计</a> </Button> : null }
    {/* <Button type="primary"><a rel="noopener noreferrer" href={`${this.$tnt.oaHost}/staticpc/#/complaint/chart`} target="_blank">投诉图表统计</a></Button> */}
    { isJiJi ? null : <Button type="primary"><a rel="noopener noreferrer" href={`${this.$tnt.oaHost}/office/tousu/TousuRankStatistics.aspx`} target="_blank">投诉等级统计</a></Button> }
    <Button type="primary" onClick={toComplaintOvertime}>投诉超时统计</Button>
    <Button type="primary" onClick={toLink}>添加投诉</Button>
  </template>
  <ni-list-page push-filter-to-location={false}>
    <ListForm ref="ListForm" loading={loading} onFetchData={fetchData} form={form} class="mb-16"/>
    <ListTable ref="ListTable" loading={loading} onFetchData={fetchData} data-source={dataSource} pagination={pagination} />
    <FlowChart visible={flowChartVisible} onCloseFlowChart={closeFlowChart}></FlowChart>
  </ni-list-page>
</page>
    }
  })
</script>
