// 性质
export const tagOptions = [
  {
    label: '投诉',
    value: 1
  },
  {
    label: '建议',
    value: 2
  },
  {
    label: '表扬',
    value: 3
  }
]

export const tagMap = new Map([
  [
    1,
    { name: '投诉', style: 'background: #F15643;' }
  ],
  [
    2,
    { name: '建议', style: 'background: #1890FF;' }
  ],
  [
    3,
    { name: '表扬', style: 'background: #0BBE69;' }
  ],
  [
    4,
    { name: '金点子', style: 'background: #FA6400;' }
  ]
])

export const catMap = new Map([
  [
    1,
    { background: 'rgba(241, 86, 67, 0.1)', color: '#F15643' }
  ],
  [
    2,
    { background: 'rgb(156, 156, 156, 0.1)', color: '#9C9C9C' }
  ],
  [
    3,
    { background: 'rgba(250, 100, 0, 0.1)', color: '#FA6400' }
  ]
])
// 投诉主体
export const xtenantOptions = [
  {
    label: '九机',
    value: 0
  },
  {
    label: '丫丫',
    value: 1
  },
  {
    label: '华为',
    value: 2
  },
  {
    label: '苹果',
    value: 3
  },
  {
    label: '九电',
    value: 4
  },
  {
    label: '大疆',
    value: 8
  },
  {
    label: '三星',
    value: 15
  },
  {
    label: 'vivo',
    value: 6
  },
  {
    label: 'oppo',
    value: 7
  },
  {
    label: '小米',
    value: 10
  },
  {
    label: '荣耀',
    value: 11
  },
  {
    label: '联想',
    value: 14
  }
]

// 会员等级
export const userClassOptions = [
  {
    label: '普通会员',
    value: '0'
  },
  {
    label: '青铜会员',
    value: '1'
  },
  {
    label: '白银会员',
    value: '2'
  },
  {
    label: '黄金会员',
    value: '3'
  },
  {
    label: '钻石会员',
    value: '5'
  },
  {
    label: '双钻会员',
    value: '6'
  }
]

// 定性分类
export const catOptions = [
  {
    label: '有效投诉',
    value: 1
  },
  {
    label: '无效投诉',
    value: 2
  },
  {
    label: '警示投诉',
    value: 3
  }
]
// 网站可见
export const showWebOptions = [
  {
    label: '是',
    value: true
  },
  {
    label: '否',
    value: false
  }
]

// 投诉分类
export const kindsOptions = [
  {
    label: '售前',
    value: 1
  },
  {
    label: '售后',
    value: 2
  },
  {
    label: '后端',
    value: 3
  }
]

// 投诉等级
export const touSuRankOptions = [
  {
    label: '轻微',
    value: 1
  },
  {
    label: '一般',
    value: 2
  },
  {
    label: '重要',
    value: 3
  },
  {
    label: '严重',
    value: 4
  },
  {
    label: '重大',
    value: 5
  },
]

// 处理状态
export const searchStateOptions = [
  {
    label: '未处理',
    value: 1
  },
  {
    label: '已受理',
    value: 2
  },
  {
    label: '已完成',
    value: 3
  },
  {
    label: '待整改',
    value: 4
  },
  {
    label: '已处理',
    value: 5
  },
  {
    label: '已整改',
    value: 6
  },
  {
    label: '已还原',
    value: 7
  },
  {
    label: '已界定',
    value: 8
  },
  {
    label: '待复核',
    value: 9
  },
  {
    label: '待仲裁',
    value: 10
  }
]

// 网站可见
export const visibleOptions = [
  {
    label: '是',
    value: 1
  },
  {
    label: '否',
    value: 2
  }
]

// 客诉状态
export const customerOptions = [
  {
    label: '客诉完结',
    value: 1
  },
  {
    label: '进行中',
    value: 2
  }
]

// 时间筛选类型
export const dateTypeOptions = [
  {
    label: '投拆时间',
    value: 1
  },
  {
    label: '完结时间',
    value: 2
  }
]

// 其他筛选条件
export const searchKindOptions = [
  {
    label: '投诉内容',
    value: 5
  },
  {
    label: '投诉ID',
    value: 6
  },
  {
    label: '会员ID',
    value: 7
  },
  {
    label: '进程内容',
    value: 8
  },
  {
    label: '责任人',
    value: 9
  },
  {
    label: '跟进人',
    value: 10
  },
  {
    label: '联系电话',
    value: 11
  },
  {
    label: '会员手机号',
    value: 12
  },
  {
    label: '添加人',
    value: 13
  }
]

// 静态
export const plainOptions1 = (function () {
  const list = [
    {
      label: '有奖励',
      value: 1,
      show: window.tenant.xtenant >= 1000
    },
    {
      label: '有罚款',
      value: 2
    },
    {
      label: '已超时',
      value: 3
    }
  ]
  return list.filter(d => d.show === undefined || d.show)
})()

export const plainOptions2 = [
  {
    label: '含有优化点',
    value: 4
  },
  {
    label: '典型投诉',
    value: 5
  }
]

// 补偿方式
export const typesOptions = [
  {
    label: '代金券',
    value: 1
  },
  {
    label: '微信现金',
    value: 2
  }
]

export const sourceOptions = function (hasGjkp) {
  const list = [
    {
      label: 'PC',
      value: 1
    },
    {
      label: 'OA录入',
      value: 2
    },
    {
      label: 'M版',
      value: 3
    },
    {
      label: '小程序',
      value: 4
    },
    {
      label: 'APP',
      value: 7
    },
    {
      label: '门店二维码',
      value: 6
    },
    {
      label: '工商投诉',
      value: 8,
      show: window.tenant.xtenant < 1000 && hasGjkp
    },
    {
      label: '市场监督局',
      value: 9,
      show: window.tenant.xtenant < 1000 && hasGjkp
    }
  ]
  return list.filter(d => d.show === undefined || d.show)
}

// 列表头
export const columns = [
  {
    title: '投诉ID',
    dataIndex: 'tsId',
    width: '80px',
    key: 'tsId',
    scopedSlots: { customRender: 'tsId' }
  },
  {
    title: '投诉性质',
    dataIndex: 'tag',
    width: '80px',
    key: 'tag',
    scopedSlots: { customRender: 'tag' }
  },
  {
    title: '定性分类',
    dataIndex: 'catName',
    width: '80px',
    key: 'catName',
    scopedSlots: { customRender: 'catName' },
    forceHide: window.tenant.xtenant >= 1000
  },
  {
    title: '会员ID',
    dataIndex: 'userId',
    width: '200px',
    key: 'userId',
    scopedSlots: { customRender: 'userId' }
  },
  {
    title: '会员等级',
    dataIndex: 'userClassName',
    width: '150px',
    key: 'userClassName',
    scopedSlots: { customRender: 'userClassName' }
  },
  {
    title: '投诉时间',
    dataIndex: 'addTime',
    width: '155px',
    key: 'addTime',
    scopedSlots: { customRender: 'addTime' }
  },
  {
    title: '投诉内容',
    dataIndex: 'content',
    key: 'content',
    width: '480px',
    scopedSlots: { customRender: 'content' }
  },
  {
    title: '责任归属区域',
    dataIndex: 'departAreaName',
    key: 'departAreaName',
    width: '200px',
    scopedSlots: { customRender: 'departAreaName' }
  },
  {
    title: '责任归属部门',
    dataIndex: 'depart',
    key: 'depart',
    width: '200px',
    scopedSlots: { customRender: 'depart' }

  },
  {
    title: '处理进程',
    dataIndex: 'states',
    width: '120px',
    key: 'states',
    scopedSlots: { customRender: 'states' }
  },
]

// 投诉等级颜色
export const rankColor = {
  1: '',
  2: '#1E90FF',
  3: '#CDCD00',
  4: '#FFA500',
  5: '#D90005'
}

export const dimissionOptions = [
  { label: '地区', value: 1 },
  { label: '部门', value: 2 }
]

export const overFiveFlagOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 0 }
]
