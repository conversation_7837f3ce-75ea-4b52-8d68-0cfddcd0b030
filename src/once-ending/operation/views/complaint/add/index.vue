<script type="text/jsx" lang="jsx">
  import { defineComponent, ref, getCurrentInstance } from 'vue'
  import {
    Card,
    Row,
    Col,
    Select,
    Input,
    Radio,
    Button,
    message,
  } from 'ant-design-vue'
  import Uploader from '~/components/upload/uploader'
  import {
    FIND_USER_INFO,
    FIND_USER_INFO_V2,
    ADD_NEW_TOU_SU,
  } from '@operation/store/modules/complaint/action-types'
  import { sourceOptions } from '../list/constants'
  import { NiAreaSelect, NiDepartSelect } from '@jiuji/nine-ui'

  export default defineComponent({
    components: {
      Uploader,
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      // 投诉门店还是部门
      const tag = ref(1)

      // 是否查询到会员展示
      const findUser = ref(true)

      const user = ref({
        memberName: '',
        mobile: '',
        userClassName: '',
        userId: '',
        xtenant: '',
      })

      const screen = ref({
        keyWord: '',
        keyWordType: 'phone',
      })

      const msg = ref({
        areaId: undefined,
        content: '',
        departId: undefined,
        type: 2,
        mobile: '',
        memberName: '',
        attachIds: [],
        files: [],
        tag: '',
      })

      const onSearch = async function () {
        if (!screen.value.keyWord) {
          return message.info('请输入会员信息查询!')
        }
        const params = {
          ...screen.value,
        }
        const res = await proxy.$store.dispatch(
          `operation/complaint/${
            proxy.$tnt.xtenant < 1000 ? FIND_USER_INFO_V2 : FIND_USER_INFO
          }`,
          params
        )
        if (res) {
          const { data } = res
          if (data.length) {
            findUser.value = true
            user.value = res.data[0]
          } else {
            findUser.value = false
            message.info('没有查找到会员信息!')
          }
        }
      }

      const ok = async function () {
        if (findUser.value && !user.value.userId) {
          message.info('请搜索会员')
          return
        }
        if (!findUser.value) {
          if (!msg.value.memberName) return message.info('请输入会员名称')
          if (!msg.value.mobile) return message.info('请输入会员联系方式')
        }
        if (tag.value === 1 && !msg.value.areaId) {
          return message.info('请选择门店')
        }
        if (tag.value === 2 && !msg.value.departId) {
          return message.info('请选择部门')
        }
        if (!msg.value.tag) return message.info('请选择性质')
        if (proxy.$tnt.xtenant < 1000 && !msg.value.type) { return message.info('请选择投诉来源') }
        if (!msg.value.content) return message.info('请输入投诉内容')
        const {
          type,
          content,
          files,
          mobile,
          memberName,
          departId,
          areaId,
          tag: msgTag,
        } = msg.value
        let params = { type, content, files: [] }
        files.map((file) => {
          const { fid, fileName } = file
          params.files.push({
            fid,
            filename: fileName,
          })
        })
        if (tag.value === 1) {
          params = { ...params, areaId }
        } else {
          params = { ...params, departId }
        }
        if (findUser.value) {
          params = { ...params, ...user.value }
        } else {
          params = { ...params, mobile, memberName }
        }
        params.tag = msgTag
        const res = await proxy.$store.dispatch(
          `operation/complaint/${ADD_NEW_TOU_SU}`,
          params
        )
        if (res) {
          message.success('添加成功')
          const timer = setTimeout(() => {
            res.data && (proxy.$router.push(`/complaint/detail/${res.data}`))
            clearTimeout(timer)
          }, 1000)
        }
      }

      const uploadedFiles = function (fileList) {
        msg.value.files = fileList
      }

      return {
        tag,
        findUser,
        user,
        screen,
        msg,
        onSearch,
        uploadedFiles,
        ok,
      }
    },
    render () {
      const { screen, onSearch, findUser, user, msg, tag, ok, uploadedFiles } =
        this
      return (
      <page>
        <Card style="width:1000px;margin:auto;">
          <Row>
            <Col class="flex">
              <span class="title import">查询会员</span>
              <Input.Group compact class="flex">
                <Select v-model={screen.keyWordType} style="width: 35%">
                  <Select.Option value="phone">按电话搜索</Select.Option>
                  <Select.Option value="name">按名称搜索</Select.Option>
                  <Select.Option value="userid">按会员ID搜索</Select.Option>
                </Select>
                <Input.Search
                  v-model={screen.keyWord}
                  placeholder="请输入"
                  enter-button="查询"
                  onSearch={onSearch}
                />
              </Input.Group>
            </Col>
          </Row>
          {findUser ? (
            <Row gutter={20}>
              <Col span={8} class="flex">
                <span class="title">会员名称</span>
                <div>
                  {user.memberName || '-'}
                  {user.xtenant ? (
                    <span class="red">【{user.xtenant}】</span>
                  ) : null}
                </div>
              </Col>
              <Col span={8} class="flex">
                <span class="title">会员等级</span>
                {user.userClassName || '-'}
              </Col>
              <Col span={8} class="flex">
                <span class="title">联系方式</span>
                {user.mobile || '-'}
              </Col>
            </Row>
          ) : (
            <Row gutter={20}>
              <Col class="flex">
                <div class="red" style="margin-left:1em;">
                  未找到该会员,请手动输入！
                </div>
              </Col>
              <Col span={8} class="flex">
                <span class="title">会员名称</span>
                <Input allow-clear v-model={msg.memberName} />
              </Col>
              <Col span={8} class="flex">
                <span class="title">联系方式</span>
                <Input allow-clear v-model={msg.mobile} />
              </Col>
            </Row>
          )}
          <Row gutter={20}>
            <Col span={15} class="flex">
              <span class="title import">责任归属</span>
              <div class="flex" style="flex:auto">
                <Radio.Group
                  v-model={this.tag}
                  class="flex"
                  style="align-items: center;margin-right:2em;flex-shrink:0;"
                >
                  <Radio value={1}>门店</Radio>
                  <Radio value={2}>部门</Radio>
                </Radio.Group>
                {tag === 1 ? (
                  <NiAreaSelect
                    style="width:100%"
                    showSearch
                    allowClear
                    dropdown-style={{ maxHeight: '300px' }}
                    v-model={msg.areaId}
                    placeholder="选择门店"
                  />
                )
                : <NiDepartSelect
                    returned-value="ALL"
                    style="width:100%"
                    dropdown-style={{ maxHeight: '300px' }}
                    value={msg.departId}
                    onChange={(val) => {
                      msg.departId = val
                    }}
                    placeholder="选择部门"
                    allow-clear={true}
                  />}

              </div>
            </Col>
            <Col span={24} class="flex">
              <span class="title import">性质</span>
              <Radio.Group
                v-model={msg.tag}
                name="radioGroup"
                class="flex"
                style="align-items:center;"
              >
                <Radio value={1}>投诉</Radio>
                <Radio value={2}>建议</Radio>
                <Radio value={3}>表扬</Radio>
              </Radio.Group>
            </Col>
            {this.$tnt.xtenant < 1000 ? (
              <Col span={24} class="flex">
                <span class="title import">投诉来源</span>
                <Select
                  v-model={msg.type}
                  placeholder="请选择投诉来源"
                  options={sourceOptions(true).filter((d) =>
                    [2, 8, 9].includes(d.value)
                  )}
                  allow-clear
                  style="width: 35%"
                />
              </Col>
            ) : null}
            <Col span={24} class="flex">
              <span class="title import">投诉内容</span>
              <Input.TextArea
                v-model={msg.content}
                placeholder="请详细描述"
                auto-size={{ minRows: 4 }}
              />
            </Col>
            <Col span={8} class="flex">
              <span class="title">上传附件</span>
              <div>
                <Uploader
                  onChange={uploadedFiles}
                  show-edit-list={false}
                  accept=".png,.jpg,.png,"
                />
              </div>
            </Col>
          </Row>
          <div class="flex" style="justify-content: center;">
            <Button
              style="margin-right: 10px;"
              onClick={() => {
                this.$router.push('list')
              }}
            >
              取消
            </Button>
            <Button type="primary" onClick={ok}>
              {' '}
              提交
            </Button>
          </div>
        </Card>
      </page>
      )
    },
  })
</script>

<style lang="scss" scoped>
.ant-col {
  margin-bottom: 1em;
}
.flex {
  display: flex;
  line-height: 32px;
  & > .title {
    line-height: 32px;
    padding: 0 1em;
    position: relative;
    &::before {
      content: ":";
      display: block;
      position: absolute;
      top: 0;
      right: 0.5em;
    }
    flex-shrink: 0;
  }
  & > .import {
    &::after {
      content: "*";
      display: block;
      position: absolute;
      color: #f21c1c;
      line-height: 32px;
      top: 0;
      left: 0.5em;
    }
  }
}
</style>
