<script lang="jsx">
  import { defineComponent, toRefs } from 'vue'
  import { NiFilter, NiFilterItem, NiAreaSelect, NiDepartSelect } from '@jiuji/nine-ui'
  import { useState } from '../hooks/useState.js'
  import Reason from '../../components/reason-select' // 投诉原因列表
  import {
    dimissionOptions,
    kindsOptions,
    searchStateOptions
  } from '../../list/constants.js'
  export default defineComponent({
    name: 'filterBox',
    components: {
      NiFilter,
      NiFilterItem,
      Reason,
      NiAreaSelect,
      NiDepartSelect
    },
    setup () {
      const { state, fetchData, changeDimension } = useState()
      return {
        ...toRefs(state),
        fetchData,
        changeDimension
      }
    },
    render () {
      const {
        form,
        fetchData,
        loading,
        changeDimension
      } = this
      return <NiFilter
        form={form}
        onFilter={() => fetchData(1)}
        unfold-Count={5}
        immediate={false}
        label-width={100}
        loading={loading}
        save-able={false}>
        <ni-filter-item label="统计维度">
          <a-select
            class="full-width"
            onChange={changeDimension}
            options={dimissionOptions}
            v-model={form.dimension}
          />
        </ni-filter-item>
        <ni-filter-item label="投诉原因">
          <Reason
            placeholder="投诉原因"
            v-model={form.categoryList}
            onChange={list => {
              form.categoryList = list
            }}
          />
        </ni-filter-item>
        <ni-filter-item label="投诉分类">
          <a-select
            allow-clear
            maxTagCount={1}
            placeholder="请选择"
            v-model={form.kindList}
            mode="multiple"
            options={kindsOptions}
          />
        </ni-filter-item>
        { form.dimension === 1 ? <ni-filter-item label="责任地区">
          <NiAreaSelect
            maxTagCount={1}
            allowClear
            multiple={true}
            v-model={form.areaIdList}
            placeholder="请选择地区"/>
        </ni-filter-item> : form.dimension === 2 ? <ni-filter-item label="责任部门">
          <NiDepartSelect
            maxTagCount={1}
            returnedValue="ALL"
            multiple={true}
            value={form.departIdList} onChange={(val) => { form.departIdList = val }}/>
        </ni-filter-item> : null}
        <ni-filter-item label="处理状态">
          <a-select
            allow-clear
            maxTagCount={1}
            placeholder="请选择"
            mode="multiple"
            v-model={form.stateList}
            options={searchStateOptions}
          />
        </ni-filter-item>
        <ni-filter-item label="投诉时间">
          <a-range-picker
            v-model={form.timeRange}
            style="width:100%;"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"/>
        </ni-filter-item>
      </NiFilter>
    }
  })
</script>
