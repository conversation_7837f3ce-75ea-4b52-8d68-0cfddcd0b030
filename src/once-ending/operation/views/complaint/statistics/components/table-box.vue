<script lang="jsx">
  import { defineComponent, toRefs } from 'vue'
  import { NiTable } from '@jiuji/nine-ui'
  import { useState } from '../hooks/useState.js'

  export default defineComponent({
    name: 'tableBox',
    components: { NiTable },
    setup () {
      const { state, exportExcel, expandedRowsChange, rowClassName, getLink } = useState()
      return {
        ...toRefs(state),
        exportExcel,
        expandedRowsChange,
        rowClassName,
        getLink
      }
    },
    data () {
      return {
        columns: [
          {
            title: '区域',
            dataIndex: 'nodeName',
            align: 'center',
            width: '100px'
          },
          {
            title: '投诉总量',
            align: 'center',
            dataIndex: 'totalNum',
            width: '100px'
          },
          {
            title: '投诉总扣分',
            align: 'center',
            dataIndex: 'totalLostPoints',
            width: '100px'
          },
          {
            title: '有效投诉量',
            align: 'center',
            dataIndex: 'validityNum',
            width: '100px'
          },
          {
            title: '警示投诉量',
            align: 'center',
            dataIndex: 'warningNum',
            width: '100px'
          },
          {
            title: '无效基础分量',
            align: 'center',
            dataIndex: 'invalidBaseNum',
            width: '100px'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: '100px',
            customRender: (text, record) => <span style="color: #1890FF" class={['pointer', record?.children?.length ? 'margin-left' : '']} onClick={() => this.getLink(record)}>查看详情</span>
          },
        ]
      }
    },
    methods: {
      expandIcon (props) {
        if (!props.record?.children?.length) return null
        return (
          <a onClick={e => { props.onExpand(props.record, e) }}>
            { props.expanded ? '收起' : '展开' }
            <a-icon type={props.expanded ? 'caret-up' : 'caret-down'}/>
          </a>
        )
      }
    },
    render () {
      const {
        dataSource,
        columns,
        loading,
        exportLoading,
        exportExcel,
        expandIcon,
        expandedRowsChange,
        expandedRowKeys,
        rowClassName
      } = this
      return <NiTable
        dataSource={dataSource}
        columns={columns}
        loading={loading}
        expandIcon={expandIcon}
        expandIconColumnIndex={columns.length - 1}
        onExpandedRowsChange={expandedRowsChange}
        expandedRowKeys={expandedRowKeys}
        rowKey={ (r, i) => r.uniqueNodeId }
        pagination={false}
        footerTotalNum={1}
        rowClassName={rowClassName}
        bordered={true}
        align="center"
      >
        <div slot="tool">
          <a-button
            style="float:right"
            type="primary"
            loading={exportLoading}
            onClick={exportExcel}
          >
            {!exportLoading ? '导出' : '导出中'}
          </a-button>
        </div>
      </NiTable>
    }
  })
</script>
<style scoped lang="scss">
:deep(.level-1) {
  background: #bae7ff;
}
:deep(.level-2) {
  background: #e6f7ff;
}
:deep(.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td) {
  background: none;
}
:deep(.indent-level-1) {
  display: none;
}
</style>
