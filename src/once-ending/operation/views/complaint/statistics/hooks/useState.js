import { reactive, provide, inject, getCurrentInstance } from 'vue'
import { getWeekOptions } from '@operation/util/common.js'
import { TOUSU_STATIS } from '@operation/store/modules/complaint/action-types'
import complaintApi from '@operation/api/complaint'
import moment from 'moment'
import axios from 'axios'

const key = Symbol('state')
const rowClassOptions = [
  { class: 'level-1', level: 1 },
  { class: 'level-2', level: 2 },
  { class: 'level-3', level: 3 },
]

export function useState () {
  return inject(key)
}

export default function createState () {
  const { proxy } = getCurrentInstance()
  const state = reactive({
    form: {
      dimension: 1,
      categoryList: undefined, // 投诉原因
      kindList: undefined, // 投诉分类
      areaIdList: undefined, // 责任地区
      departIdList: undefined, // 责任部门
      stateList: undefined, // 处理状态
      timeRange: undefined // 时间
    },
    loading: false,
    exportLoading: false,
    dataSource: [],
    expandedRowKeys: [],
    cacheParams: {}
  })

  function setDefaultTimes () {
    const weekOptions = getWeekOptions(1)
    const { start, end } = weekOptions[0].value
    state.form.timeRange = [start, end]
  }
  let id = 0
  async function fetchData () {
    const { timeRange, ...other } = state.form
    const params = {
      ...other,
      startTime: timeRange[0] || undefined,
      endTime: timeRange[1] || undefined,
    }
    state.cacheParams = { ...params }
    state.expandedRowKeys = []
    state.loading = true
    const res = await proxy.$store.dispatch(`operation/complaint/${TOUSU_STATIS}`, params)
    state.loading = false
    if (res) {
      const { data: { data, total } } = res
      state.dataSource = data
      data?.length && (state.dataSource.push({ ...total, id: '-1', nodeLevel: 1 }))
    }
  }

  function getLink (record) {
    const { dimension, areaIdList, departIdList, ...other } = state.cacheParams
    const keys = [
      { toK: 'depart', currentK: 'departIdList' },
      { toK: 'areaIdM', currentK: 'areaIdList' },
      { toK: 'touSuTypeVal', currentK: 'categoryList' },
    ]
    const isTotal = !!(record.nodeName === '合计')
    const cacheParams = {
      ...other,
      areaIdList: dimension === 1 ? isTotal ? areaIdList : getIds(`${record.nodeType === 1 ? 'a' : ''}` + record.nodeId, dimension) : undefined,
      departIdList: dimension === 2 ? isTotal ? departIdList : getIds('' + record.nodeId, dimension) : undefined
    }
    const query = Object.keys(cacheParams).filter(it => cacheParams[it]).map(k => {
      const val = Array.isArray(cacheParams[k]) ? cacheParams[k].join(',') : cacheParams[k]
      const keyItem = keys.find(it => it.currentK === k)
      const key = keyItem?.toK || k
      return `${key}=${val}`
    })
    const qs = window.tenant.oaHost + '/staticpc/#/complaint/list?' + query.join('&')
    window.open(qs)
    // return qs
  }

  function getIds (id, type) {
    const ids = []
    const areaS = sessionStorage.getItem('ni-area-select11truefalsefalse')
    const departmentS = sessionStorage.getItem('departmentTree')
    const areaTree = areaS ? JSON.parse(areaS) : []
    const departmentTree = departmentS ? JSON.parse(departmentS) : []
    const arr = (type === 1 ? areaTree : departmentTree)
    console.log('arr', arr)
    if (!arr.length) return ids
    let cacheItem
    function findIds (array, needPush) {
      array.forEach(element => {
        element.value === id && (cacheItem = element)
        if (element.children?.length) {
          if (needPush || (!needPush && !cacheItem)) {
            findIds(element.children, needPush)
          }
        } else if (needPush) {
          ids.push(element.value)
        }
      })
    }
    findIds(arr, false)
    if (cacheItem) {
      cacheItem?.children?.length ? findIds(cacheItem.children, true) : ids.push(cacheItem.value)
    }
    return ids
  }

  function init () {
    setDefaultTimes()
    fetchData()
  }
  init()

  function exportExcel () {
    const { cacheParams } = state
    state.exportLoading = true
    axios({
      method: 'post',
      url: complaintApi.exportExcelStatistics(),
      data: cacheParams,
      responseType: 'blob',
      timeout: 60000,
      headers: {
        Authorization: proxy.$store.state.token
      }
    }).then((res) => {
      const link = document.createElement('a')
      let blob = new Blob([res.data], { type: 'application/x-excel' })
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      link.download = `投诉统计(${moment().format('YYYY-MM-DD HHmmss')}).xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }).finally(() => {
      state.exportLoading = false
    })
  }

  function expandedRowsChange (keys) {
    state.expandedRowKeys = keys
  }

  function rowClassName (record) {
    return rowClassOptions.find(it => it.level === record.nodeLevel)?.class || ''
  }

  function changeDimension (val) {
    state.form.departIdList = undefined
    state.form.areaIdList = undefined
  }

  provide(key, {
    state,
    fetchData,
    exportExcel,
    expandedRowsChange,
    rowClassName,
    changeDimension,
    getLink
  })
  return {
    state,
    fetchData,
    getLink
  }
}
