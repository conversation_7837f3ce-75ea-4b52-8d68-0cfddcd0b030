<script lang="jsx">
  import { defineComponent } from 'vue'
  import filterBox from './components/filter-box.vue'
  import tableBox from './components/table-box.vue'
  import { NiListPage } from '@jiuji/nine-ui'
  import createState from './hooks/useState.js'

  export default defineComponent({
    name: 'statistics',
    conponents: {
      filterBox,
      tableBox,
      NiListPage
    },
    setup () {
      const { state } = createState()
    },
    render () {
      return <page>
        <ni-list-page push-filter-to-location={false}>
          <filterBox/>
          <tableBox class="mt-16"/>
        </ni-list-page>
      </page>
    }
  })
</script>
