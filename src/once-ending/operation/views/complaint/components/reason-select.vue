<template>
<span
    style="width:100%;">
  <a-tree-select
  ref="tree"
    v-model="value"
    style="width: 100%;height:32px;overflow:auto;margin:0;"
    :tree-data="treeData"
    tree-checkable
    allowClear
    search-placeholder="Please select"
    :placeholder="placeholder"
    :replaceFields="{children:'children', title:'name', key:'id', value: 'id' }"
    :maxTagCount="0"
    :maxTagPlaceholder="(omittedValues)=>`已选中了${omittedValues.length}条`"
    :dropdownStyle="{height:'320px'}"
    @change="change"
    :filterTreeNode="filterTreeNode"
    :getPopupContainer="e=>e.parentElement"
  ></a-tree-select>
</span>
</template>

<script>
  import { TreeSelect } from 'ant-design-vue'
  import complaint from '@operation/api/complaint'
  const SHOW_PARENT = TreeSelect.SHOW_PARENT
  export default {
    name: 'selectss',
    props: {
      kind: {
        type: Number, // 1-投诉, 2-评价, 3-合作伙伴投诉建议
        default: 1
      },
      placeholder: {
        type: String,
        default: '选择投诉原因'
      }
    },
    data () {
      return {
        treeData: [],
        value: this.$attrs.value,
        SHOW_PARENT
      }
    },
    created () {
      complaint.getTouSuTypes({ kind: this.kind }).then(res => {
        if (res.code === 0) {
          this.treeData = res.data
        }
      })
    },
    computed: {
      selected () {
        return this.$attrs.value
      }
    },
    watch: {
      selected () {
        this.value = this.$attrs.value
      }
    },
    methods: {
      change (ids) {
        this.$emit('change', ids)
        this.$emit('update:value', ids)
      },
      filterTreeNode (inputValue, treeNode) {
        return treeNode.data.props.title.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
      }
    }
  }
</script>
<style lang="scss" scoped>
.ant-select-selection__rendered{
  display: flex;

}
</style>
