<template>
  <a-tree-select
      v-model="value"
      style="width: 100%"
      :tree-data="treeData"
      v-bind="$attrs"
      v-on="$listeners"
      :getPopupContainer="(triggerNode) => triggerNode.parentNode"
      :replaceFields="{children:'children', title:'label', key:'id', value: 'id' }"
  />
</template>
<script>
  import { TreeSelect } from 'ant-design-vue'
  const SHOW_PARENT = TreeSelect.SHOW_PARENT
  export default {
    props: {
      type: {
        type: [String],
        default: 'area' // area-区域,department-部门,role=部门角色
      },
      selectedLastNode: {
        type: <PERSON><PERSON><PERSON>,
        default: false
      },
      selected: String,
      canSelectAllTree: {
        type: <PERSON>olean,
        default: false
      }
    },
    data () {
      return {
        treeData: [],
        value: []
      }
    },
    watch: {
      selected () {
        if (this.selected) this.value = [this.selected]
      }
    },
    created () {
      this.getData()
    },
    methods: {
      filterNode (list) {
        if (this.selectedLastNode) {
          list.map(item => {
            if (item.children?.length) {
              item.disabled = !this.canSelectAllTree
              this.filterNode(item.children)
            }
          })
        }
        return list
      },
      getData () {
        this.$api.common.getAreaTree(this.type).then(res => {
          if (res.code === 0) {
            let data = res.data
            if (this.type === 'area') {
              data = res.data.areaOptions
            }
            if (this.type === 'department') {
              data = res.data.departOptions
            }
            this.filterNode(data)
            this.treeData = data
          } else {
          }
        }).finally(() => {
          if (this.selected) this.value = [this.selected]
        })
      }
    }
  }
</script>
