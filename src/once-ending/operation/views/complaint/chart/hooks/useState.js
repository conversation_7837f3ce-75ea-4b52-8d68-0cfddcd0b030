import { reactive, provide, inject, getCurrentInstance } from 'vue'
import { getWeekOptions } from '@operation/util/common.js'
import { to } from '~/util/common'
import complaintApi from '@operation/api/complaint'
import {
  qualitativeColorData,
  reasonColorData,
  problemColorData
} from '../constants'

const key = Symbol('state')
const rowClassOptions = [
  { class: 'level-1', level: 1 },
  { class: 'level-2', level: 2 },
  { class: 'level-3', level: 3 },
]

export function useState () {
  return inject(key)
}

export default function createState () {
  const { proxy } = getCurrentInstance()
  const state = reactive({
    form: {
      dimission: 1,
      types: undefined, // 投诉原因
      kinds: undefined, // 投诉分类
      areaIds: undefined, // 责任地区
      departIds: undefined, // 责任部门
      searchState: undefined, // 处理状态
      times: undefined, // 时间
      cat: undefined, // 定性分类
    },
    loading: false,
    exportLoading: false,
    expandedRowKeys: [],
    problemData: [],
    qualitativeData: [],
    reasonData: [],
    refresh: false
  })

  function setDefaultTimes () {
    const weekOptions = getWeekOptions(1)
    const { start, end } = weekOptions[0].value
    state.form.times = [start, end]
  }
  async function fetchData () {
    const { areaIds, departIds, times, dimission, ...other } = state.form
    const params = { ...other }
    const cacheKey = dimission === 1 ? 'areaCode_val' : 'depart_val'
    const cacheIds = dimission === 1 ? areaIds : departIds
    params[cacheKey] = cacheIds?.join(',') || ''
    const stringKey = ['types', 'kinds', 'searchState']
    stringKey.forEach(key => {
      params[key] = state.form[key]?.join(',')
    })
    params.date1 = times[0] || ''
    params.date2 = times[1] || ''
    state.loading = true
    const [err, res] = await to(complaintApi.getChartInfo(params))
    state.loading = false
    if (err) throw err
    const { code, data, userMsg } = res
    if (code === 0) {
      const { problemData, qualitativeData, reasonData } = data
      state.problemData = problemData.map((it, i) => ({ ...problemColorData[i], ...it }))
      state.qualitativeData = qualitativeData.map((it, i) => ({ ...qualitativeColorData[i], ...it }))
      // state.qualitativeData = qualitativeColorData
      state.reasonData = reasonData.map((it, i) => ({ ...reasonColorData[i], ...it }))
      state.refresh = true
      setTimeout(() => {
        state.refresh = false
      }, 10)
    } else {
      proxy.$message.error(userMsg)
    }
  }

  function init () {
    setDefaultTimes()
    fetchData()
  }
  init()

  function exportExcel () {}

  function expandedRowsChange (keys) {
    state.expandedRowKeys = keys
  }

  function rowClassName (record) {
    return rowClassOptions.find(it => it.level === record.level)?.class || ''
  }

  provide(key, {
    state,
    fetchData,
    exportExcel,
    expandedRowsChange,
    rowClassName
  })
  return {
    state,
    fetchData
  }
}
