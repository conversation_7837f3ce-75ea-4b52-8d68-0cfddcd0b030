<script lang="jsx">
  import { defineComponent, toRefs } from 'vue'
  import { NiFilter, NiFilterItem, NiAreaSelect, NiDepartSelect } from '@jiuji/nine-ui'
  import { useState } from '../hooks/useState.js'
  import Reason from '../../components/reason-select' // 投诉原因列表
  import {
    dimissionOptions,
    kindsOptions,
    searchStateOptions,
    catOptions
  } from '../../list/constants.js'
  export default defineComponent({
    name: 'filterBox',
    components: {
      NiFilter,
      NiFilterItem,
      Reason,
      NiAreaSelect,
      NiDepartSelect
    },
    setup () {
      const { state, fetchData } = useState()
      return {
        ...toRefs(state),
        fetchData
      }
    },
    render () {
      const {
        form,
        fetchData,
        loading
      } = this
      return <NiFilter
        form={form}
        onFilter={() => fetchData(1)}
        unfold-Count={5}
        immediate={false}
        label-width={100}
        loading={loading}
        save-able={false}>
        <ni-filter-item label="投诉原因">
          <Reason
            placeholder="投诉原因"
            v-model={form.types}
            onChange={list => {
              form.types = list
            }}
          />
        </ni-filter-item>
        <ni-filter-item label="投诉分类">
          <a-select
            allow-clear
            placeholder="请选择"
            v-model={form.kinds}
            mode="multiple"
            options={kindsOptions}
          />
        </ni-filter-item>
        <ni-filter-item label="定性分类">
          <a-select
            allow-clear
            placeholder="请选择"
            v-model={form.cat}
            options={catOptions}
          />
        </ni-filter-item>
        <ni-filter-item class="no-label">
          <div class="flex">
            <a-select
            style="width: 40px"
            options={dimissionOptions}
            v-model={form.dimission}/>
            { form.dimission === 1 ? <NiAreaSelect
            maxTagCount={1}
            allowClear
            multiple={true}
            class="full-width"
            v-model={form.areaIds}
            placeholder="请选择地区"
            /> : <NiDepartSelect
            maxTagCount={1}
            returnedValue="ALL"
            class="full-width"
            multiple={true}
            value={form.departIds} onChange={(val) => { form.depart = val }}/> }
          </div>
        </ni-filter-item>
        <ni-filter-item label="处理状态">
          <a-select
            allow-clear
            placeholder="请选择"
            mode="multiple"
            v-model={form.searchState}
            options={searchStateOptions}
          />
        </ni-filter-item>
        <ni-filter-item label="投诉时间">
          <a-range-picker
            v-model={form.times}
            style="width:100%;"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"/>
        </ni-filter-item>
      </NiFilter>
    }
  })
</script>
<style scoped lang="scss">
.no-label {
  :deep(.label) {
    display: none;
  }
}
:deep(.ant-select-selection__choice) {
  max-width: 45%;
}
</style>
