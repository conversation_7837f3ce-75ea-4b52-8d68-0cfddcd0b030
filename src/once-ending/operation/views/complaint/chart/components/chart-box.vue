<script lang="jsx">
  import { defineComponent, onMounted, watch, nextTick, toRefs, reactive } from 'vue'
  import { Chart, Util } from '@antv/g2'
  import { useState } from '../hooks/useState.js'

  const chartContent = {
    qualitativeData: null,
    reasonData: null,
    problemData: null
  }

  export default defineComponent({
    setup () {
      function getThetaChartRender (data, container, radius, key) { // 饼形图
        chartContent[key] && chartContent[key].destroy()
        chartContent[key] = new Chart({
          container,
          autoFit: true, // 自动适应盒子宽高
          height: 500,
          padding: [64]
        })
        const chart = chartContent[key]
        chart.data(data)
        chart.coordinate('theta', {
          radius // 用于极坐标，配置极坐标半径，0-1 范围的数值
        })
        chart.tooltip({
          showTitle: false, // 提示title
          showMarkers: true, // 提示水滴
        })
        chart
          .interval()
          .adjust('stack')
          .position('value')
          .color('type', data.map(it => it.color)) // 配置颜色
          .label('type', (val) => {
            return {
              offset: 16,
              style: {
                fill: 'black',
                fontSize: 12,
              },
              content: (obj) => {
                return obj.type + '：' + (obj.percent || obj.value)
              },
            }
          })
          .tooltip('type*value', (item, value) => { // 配置提示
            return {
              name: item,
              value: value,
            }
          })

        chart.interaction('element-single-selected')

        chart.render()
      }
      function getRectChartRender (data, container, title, key) { // 柱形图
        chartContent[key] && chartContent[key].destroy()
        chartContent[key] = new Chart({
          container,
          autoFit: true,
          height: 500,
          padding: [40, 300, 3, 60]
        })
        const chart = chartContent[key]
        chart.data(data)
        chart.scale('value', {
          alias: title // 标题
        })
        chart.axis('type', {
          tickLine: {
            alignTick: true,
          },
        })

        chart.axis('value', {
          label: { // y轴坐标线对应数值
            formatter: text => {
              return text.replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
            }
          },
          title: { // 标题
            offset: 40,
            style: {
              fill: 'gray'
            },
          }
        })
        chart.legend({
          position: 'right',
          itemName: {
            formatter: (text, item, index) => {
              const { percent, value } = data[index]
              return `${text}：${value}（${percent}）`
            }
          },
          offsetX: -100,
          maxItemWidth: 300,
        })
        chart.tooltip({
          shared: false,
          showMarkers: false,
          showTitle: false
        })
        chart.interaction('element-active')
        chart
          .interval()
          .adjust('stack')
          .position('type*value')
          .color('type', data.map(it => it.color))

        // 添加文本标注
        // data.forEach((item) => {
        //   chart
        //     .annotation()
        //     .text({
        //       position: [item.type, item.value],
        //       content: item.value,
        //       style: {
        //         textAlign: 'center',
        //       },
        //       offsetY: -20,
        //     })
        // })
        chart.render()
      }
      const { state } = useState()
      watch(
        () => state.refresh,
        (val) => {
          if (val) {
            nextTick(() => {
              toRender()
            })
          }
        },
        { immediate: true })
      function getShow (array) {
        return Boolean(array?.length && array.find(it => it.value))
      }
      function toRender () {
        getShow(state.qualitativeData) && (getThetaChartRender(state.qualitativeData, 'qualitative-proportion', 0.65, 'qualitativeData'))
        getShow(state.reasonData) && (getThetaChartRender(state.reasonData, 'reason-proportion', 0.6, 'reasonData'))
        getShow(state.problemData) && (getRectChartRender(state.problemData, 'problem-label', '百分比（%）', 'problemData'))
      }
      return {
        ...toRefs(state),
        getShow
      }
    },
    render () {
      const {
        qualitativeData,
        reasonData,
        problemData,
        getShow
      } = this
      return <div class="chart-content">
        <div>
          <p>投诉定性占比</p>
          { getShow(qualitativeData) ? <div id="qualitative-proportion"></div> : <div class="flex flex-center h-500">
            <a-empty/>
          </div> }
          <p>各部门投诉处理时效占比(小时)</p>
        </div>
        <div>
          <p>投诉原因占比</p>
          { getShow(reasonData) ? <div id="reason-proportion"></div> : <div class="flex flex-center h-500">
            <a-empty/>
          </div> }
          <p>各部门投诉处理时效</p>
        </div>
        <div>
          <p>问题标签前15</p>
          { getShow(problemData) ? <div id="problem-label"></div> : <div class="flex flex-center h-500">
            <a-empty/>
          </div> }
          <p>投诉总量前15名</p>
        </div>
      </div>
    }
  })
</script>
<style scoped lang="scss">
.chart-content {
  min-height: 75vh;
  padding: 24px;
  background: #fff;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  > div {
    width: 600px;
    &:last-child {
      width: 700px;
    }
    &:first-child {
      width: 450px;
    }
    margin-bottom: 48px;
    p {
      font-size: 16px;
      font-weight: 600;
      width: 100%;
      text-align: center;
      margin: 16px;
    }
  }
}
.h-500 {
  height: 500px;
}
</style>
