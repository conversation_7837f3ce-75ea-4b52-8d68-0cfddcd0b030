<script lang="jsx">
  import { defineComponent } from 'vue'
  import filterBox from './components/filter-box.vue'
  import chartBox from './components/chart-box.vue'
  import { NiListPage } from '@jiuji/nine-ui'
  import createState from './hooks/useState.js'

  export default defineComponent({
    name: 'statistics',
    conponents: {
      filterBox,
      chartBox,
      NiListPage
    },
    setup () {
      const { state } = createState()
    },
    render () {
      return <page>
        <ni-list-page push-filter-to-location={false}>
          <filterBox/>
          <chartBox/>
        </ni-list-page>
      </page>
    }
  })
</script>
