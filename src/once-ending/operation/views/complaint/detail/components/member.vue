<script lang="jsx">
  import { defineComponent, ref, watch, getCurrentInstance } from 'vue'
  import {
    Row,
    Col,
    Popover,
    Icon,
    Input,
    Button,
    message
  } from 'ant-design-vue'
  import moment from 'moment'
  import { TOU_SU_END_AND_INVALID } from '@operation/store/modules/complaint/action-types'
  import UserLevel from './user-level.vue'

  export default defineComponent({
    components: {
      UserLevel
    },
    props: {
      memberInfo: {
        type: Object,
        default: () => ({})
      },
      detail: {
        type: Object,
        default: () => ({})
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const tag = ref('')

      const mobile = ref('')

      const joinTousuId = ref('')

      watch(
        () => props.memberInfo,
        val => {
          mobile.value = val.memberMobile
        },
        {
          deep: true
        }
      )

      const checkTag = function (tagValue, tagName) {
        tag.value = tagValue
        props.memberInfo.tag = tagName
      }

      const callcenter = function () {
        const getTime = moment().format('YYYY-MM-DD HH:MM:SS')
        window.location.href = `/callcenter/ch999/listCDR.php?time1=${
          props.memberInfo.addTime
        }&time2=${getTime}&dst=${props.memberInfo &&
          props.memberInfo.memberMobile}`
      }

      const blurrySearch = function () {
        return `${proxy.$tnt.oaHost}/member/index?actionName=fromid&key=${props.memberInfo.userId}`
      }

      const getMemberInfo = function () {
        const params = {}
        if (mobile.value !== props.memberInfo.memberMobile) {
          params.mobile = mobile.value
        }
        if (tag.value) params.tag = tag.value
        proxy.$emit('getMemberInfo', params)
      }

      const tousuEndAndinvalid = async function () {
        if (!joinTousuId.value) return message.info('请输入相同的投诉单号')
        const params = {
          id: proxy.$route.params.id,
          joinTousuId: joinTousuId.value,
          isSupplier: false // 供应商反馈和这个用的是同一个接口，用来区分是投诉还是供应商反馈
        }
        const res = await proxy.$store.dispatch(
          `operation/complaint/${TOU_SU_END_AND_INVALID}`,
          params
        )
        if (res) {
          proxy.$router.go(0)
        }
      }

      const toList = function () {
        const routeData = proxy.$router.resolve({
          path: `/complaint/list`,
          query: {
            searchKind: 7,
            key: props.memberInfo.userId,
            history: 1,
            tags: '1,2'
          }
        })
        window.open(routeData.href, '_blank')
      }

      return {
        checkTag,
        mobile,
        joinTousuId,
        callcenter,
        blurrySearch,
        getMemberInfo,
        tousuEndAndinvalid,
        toList
      }
    },
    render () {
      const {
        blurrySearch,
        memberInfo,
        checkTag,
        toList,
        tousuEndAndinvalid,
        callcenter,
        getMemberInfo,
        detail
      } = this
      return (
      <div>
        <Row>
          <Col span={8}>
            <div class="flex flex-align-center">
              <span class='user-name'>
              会员名称：
              <a-tooltip>
                <template slot="title">
                  {memberInfo.memberUserName}
                </template>
               <a
                 rel="noopener noreferrer"
                 href={blurrySearch()}
                 target="_blank"
                 class="bold"
               >{memberInfo.memberUserName}</a>
              </a-tooltip>
            </span>
              <UserLevel class="ml-4" member={memberInfo} memberStarLevel={detail.memberStarLevel}/>
            </div>
          </Col>
          <Col span={8} class="flex flex-align-center">
            <span>会员 ID：</span>
            <span class="bold">{memberInfo.userId}</span>
          </Col>
          <Col span={8}>
            <span>联系电话：</span>
            <Input placeholder="请输入" v-model={memberInfo.memberMobile} class="width-174" />
          </Col>
        </Row>
        <Row>
          <Col span={8}>
            <span>历史投诉次数：</span>
            <span class="pointer bold" onClick={toList} style="color:#1890FF">
              {memberInfo.tcount}
            </span>
          </Col>
          <Col span={8} style='line-height: 2.4em'>
            <span>投诉 IP：</span>
            <a-tooltip title={ memberInfo.writeIp?.length > 26 ? memberInfo.writeIp : null }>
              <span class='ellipsis bold'>{memberInfo.writeIp || '--'}</span>
            </a-tooltip>
          </Col>
          <Col span={8}>
            <span>相同投诉：</span>
            <Input
              allowClear
              v-model={this.joinTousuId}
              placeholder="请填写投诉单号"
              class="width-174 flex flex-child-noshrink"
            />
            <Button
              type="primary"
              ghost
              style="margin-left:8px"
              onClick={tousuEndAndinvalid}
            >
              完结
            </Button>
          </Col>
        </Row>
        { this.$tnt.xtenant < 1000 ? <Row>
          <Col span={8}>
              <Button
                type="primary"
                onClick={callcenter}
              >
                查看通话录音
              </Button>
              <router-link to={`/member/sms/send?smsnumber=${this.mobile}&smsChannel=9`} target="_blank">
                <Button
                type="primary"
                style="margin-left: 10px"
              >
                发送短信
              </Button>
              </router-link>
          </Col>
        </Row> : null }
      </div>
      )
    }
  })
</script>

<style lang="scss" scoped>
@import "../common.scss";
.user-name{
  display: inline-flex;
  a {
    max-width: 8em;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
