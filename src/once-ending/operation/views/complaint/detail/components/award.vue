<script lang="jsx">
  import { defineComponent, ref, watch, getCurrentInstance, inject, computed } from 'vue'
  import {
    Radio,
    Checkbox,
    Row,
    Col,
    Input,
    DatePicker,
    TimePicker,
    InputNumber,
    Button,
    message
  } from 'ant-design-vue'
  import Uploader from './uploader'
  import InputPeople from '~/components/staff/staff-input'
  import moment from 'moment'
  import { stateMap, buttonsMap } from '../constants'
  import platform from '~/util/platform'
  import {
    TOU_SU_PROCESS_DEAL,
    SEND_MA
  } from '@operation/store/modules/complaint/action-types'
  import complaint from '@operation/api/complaint'
  import { to } from '@common/utils/common'
  import BigNumber from 'bignumber.js'
  import { cloneDeep } from 'lodash'

  const originForm = {
    notice: false, // 是否通知
    cate: 0, // 类型
    isShow: false, // 展示
    dsc: '', // 备注
    endTime: null, // 截至跟进
    weixin: false, // 微信
    tongzhi: false, // 内部
    duanxin: true, // 短信
    attachFiles: [], // 文件
    noticeUsers: []
  }

  const originMaForm = {
    duanxin: false,
    price: '',
    time: null,
    tousuId: '',
    appMsg: true
  }

  const originWxForm = {
    duanxin: false,
    price: undefined,
    appMsg: true
  }

  export default defineComponent({
    props: {
      info: {
        type: Object,
        default: () => ({ states: 0 })
      },
      memberInfo: {
        type: Object,
        default: () => ({})
      },
      resultInfo: {
        type: Object,
        default: () => ({})
      }
    },
    components: {
      Uploader,
      InputPeople
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const isJiuJi = computed(() => proxy.$tnt.xtenant < 1000)
      const id = ref(proxy.$route.params.id)
      const time = ref(null)

      const showMa = ref(false)

      const wx = ref(false)

      const userId = ref('')

      const mobile = ref('')

      const ranks = computed(() => {
        return proxy.$store.state.userInfo.Rank || []
      })

      const hasKswc = computed(() => {
        return ranks.value.includes('kswc')
      })

      const hasKsxj = computed(() => {
        return ranks.value.includes('ksxj')
      })

      const form = ref(cloneDeep(originForm))

      const maForm = ref(cloneDeep(originMaForm))
      const totalPrice = computed(() => {
        return new BigNumber(maForm.value.price || 0).plus(wxForm.value.price || 0).toNumber()
      })
      const wxForm = ref(cloneDeep(originWxForm))

      const customerEndTime = ref(false)

      const loading = ref(false)

      watch(
        () => props.memberInfo,
        val => {
          userId.value = val.userId
          mobile.value = val.memberMobile
          customerEndTime.value = !!val.customerEndTime
        },
        {
          deep: true
        }
      )

      const disabledDate = function (current) {
        return current && current < moment().subtract(1, 'days')
      }

      const filess = inject('filess')
      const upLoaderChange = function (files) {
        form.value.attachFiles = files
        filess.value = files
      }
      function getPlatForm () {
        const platformArray = [
          { key: 'android', label: 'Android' },
          { key: 'ios', label: 'iOS' },
          { key: 'pc', label: 'PC_P' },
        ]
        const nowFlatform = platformArray.find(it => platform[it.key])?.label
        return nowFlatform + '/' + (platform.osVersion || '1.0.0')
      }

      const sendMa = async function () {
        const { price, time, duanxin, appMsg } = maForm.value
        if (!price) return message.info('输入金额')
        if (!time) return message.info('选择日期')
        if (!(duanxin || appMsg)) {
          return message.info('选择通知方式')
        }
        send(maForm.value, 1)
      }

      const sendWx = async function () {
        const { price, duanxin, appMsg } = wxForm.value
        if (!price) return message.info('输入金额')
        if (price - 500 > 0) return message.info('金额不能大不500')
        if (!(duanxin || appMsg)) {
          return message.info('选择通知方式')
        }
        send(wxForm.value, 2)
      }

      const send = async function (params, type) {
        const res = await proxy.$store.dispatch(
          `operation/complaint/${SEND_MA}`,
          {
            ...params,
            type,
            tousuId: proxy.$route.params.id,
            userId: userId.value,
            mobile: mobile.value
          }
        )
        if (res) {
          message.success('发送成功')
          if (type === 1) {
            maForm.value = cloneDeep(originMaForm)
          } else {
            wxForm.value = cloneDeep(originWxForm)
          }
          const timeId = setTimeout(() => {
            proxy.$emit('sendSuccess')
            clearTimeout(timeId)
          }, 1000)
        }
      }

      const peopleChange = function (people) {
        form.value.noticeUsers = people.map(d => {
          return {
            noticeUser: d.name,
            ch999Id: d.id
          }
        })
      }

      const beforeUpload = function (file, fileList) {
        const reg = /\.(png|jpg|gif|jpeg|webp|mp4|avi|rmvb|mpeg|wmf|mov|mkv|)$/
        if (!reg.test(file.name)) {
          const index = fileList.findIndex(d => d.uid === file.uid)
          fileList.splice(index, 1)
          message.warning('附件只支持上传音视频及图片类型文件')
          return false
        }
      }

      return {
        time,
        showMa,
        form,
        customerEndTime,
        loading,
        maForm,
        disabledDate,
        upLoaderChange,
        sendMa,
        peopleChange,
        beforeUpload,
        hasKswc,
        hasKsxj,
        wxForm,
        wx,
        sendWx,
        id,
        totalPrice,
        isJiuJi
      }
    },
    render () {
      const {
        info,
        form,
        peopleChange,
        disabledDate,
        upLoaderChange,
        maForm,
        sendMa,
        showMa,
        loading,
        customerEndTime,
        hasKswc,
        hasKsxj,
        wxForm,
        sendWx,
        wx,
        id,
        resultInfo,
        totalPrice,
        isJiuJi
      } = this
      const disabledWeiXin = !!(resultInfo.wechatMoney > 0)
      return (
        <div>
          { isJiuJi ? <div class="single-contents mb-10">
            <div class="flex flex-align-center">
              <span class="title">奖励</span>
              <span>（给投诉会员发放代金券和微信现金）</span>
            </div>
            { this.$tnt.xtenant === 0 ? <div class="flex flex-align-center details">
              <div class="type">代金券：</div>
              <span class="ml-10">金额：</span>
              <InputNumber
                v-model={maForm.price}
                style="height:30px;;width:120px"
                min={0.01}
                precision={2}
                placeholder="请输入金额"/>
              <span style="margin-left:5px">元</span>
              <span class="ml-20">过期时间：</span>
              <DatePicker
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                disabled-date={disabledDate}
                v-model={maForm.time}
                style="height:30px;"/>
              <span class="ml-24">通知方式：</span>
              <Checkbox v-model={maForm.duanxin}>短信</Checkbox>
              <Checkbox style="margin-left: 16px" v-model={maForm.appMsg}>APP消息</Checkbox>
              <Button class="ml-24" type="primary" onClick={sendMa}>
                发送代金券
              </Button>
            </div> : null }
            { this.$tnt.xtenant < 1000 && hasKsxj ? <div class="flex flex-align-center details">
              <div class="type">微信现金：</div>
              <span class="ml-10">金额：</span>
              <InputNumber
                v-model={wxForm.price}
                style="height:30px;width:120px"
                placeholder="请输入金额"
                disabled={disabledWeiXin}
                min={0.01}
                precision={2}
              ></InputNumber>
              <span style="margin-left:5px">元</span>
              <span class="ml-20">通知方式：</span>
              <Checkbox disabled={disabledWeiXin} v-model={wxForm.duanxin}>短信</Checkbox>
              <Checkbox disabled={disabledWeiXin} style="margin-left: 16px" v-model={wxForm.appMsg}>APP消息</Checkbox>
              <Button disabled={disabledWeiXin} class="ml-24" type="primary" onClick={sendWx}>
                发送微信现金
              </Button>
              { resultInfo.wechatMoney > 0 ? <span class="ml-20" style="color: #F21C1C">*已发送过微信现金，不能重复发送</span> : null}
            </div> : null }
            <div class="total flex flex-align-center">代金券和微信现金发放总金额：<span class="money">
              { resultInfo.bonusMoney || 0 }
            </span>元，代金券：<span class="money">
              { resultInfo.couponMoney || 0 }
            </span>元，微信现金：<span class="money">
              { resultInfo.wechatMoney || 0 }
            </span>元</div>
          </div> : null }
          <div class="single-contents">
            <span class="title">惩罚</span>
            <div class="flex flex-align-center" style="margin-top: 28px">
              <div class="type">惩罚金额：<span class="bold ml-10">{resultInfo.fineMoney ?? '--'}元</span></div>
              <a style="margin-left: 10px;"
                 href={`/punish/Punish?title=投诉乐捐【投诉ID：${id}】&tousuid=${id}&type_=1`}>
                <Button type="primary">添加乐捐单</Button>
              </a>
            </div>
          </div>
        </div>
      )
    }
  })
</script>

<style lang="scss" scoped>
@import "../common.scss";
.details {
  margin-top: 20px;
  &:first-child {
    margin-top: 28px;
  }
}
.type {
  font-weight: 600;
  font-size: 15px;
  position: relative;
  line-height: 16px;
  padding-left: 7px;
  min-width: 82px;
  &:after {
    position: absolute;
    content: '';
    width: 3px;
    height: 14px;
    background: #1890FF;
    border-radius: 2px;
    left: 0;
    top: 1px
  }
}
.total {
  height: 32px;
  margin-top: 20px;
  background: linear-gradient( 270deg, rgba(24,144,255,0) 0%, rgba(24,144,255,0.1) 100%);
  border-radius: 4px;
  padding: 0 10px;
  font-weight: bold;
  .money {
    color: #F21C1C;
    margin-right: 4px;
  }
}
:deep(.ant-checkbox + span) {
  padding: 0 0 0 3px;
}
:deep(.file-preview) {
  margin-top: 12px;
  margin-bottom: 0;
}
.flex {
  display: flex;
  > span {
    white-space: nowrap;
  }
}

.flex-col-center {
  align-items: center;
}

.flex-row-center {
  justify-content: center;
}
.red {
  color: #f56c6c;
}
</style>
