<script lang="jsx">
  import { inject, defineComponent, reactive, ref, getCurrentInstance } from 'vue'
  import { detailColumns } from '../constants'
  import { GET_NUMBER_DETAIL } from '@operation/store/modules/complaint/action-types'
  export default defineComponent({
    name: 'view-detail-modal',
    setup () {
      const { proxy } = getCurrentInstance()
      const viewDetail = inject('viewDetail')
      const dataSource = ref([])
      const loading = ref(false)
      const pagination = reactive({
        pageSize: 10,
        current: 1,
        total: 0,
        showSizeChanger: true,
        showLessItems: true,
        showQuickJumper: true,
        pageSizeOptions: ['10', '20', '35', '50']
      })
      function tableChange (newPage) {
        Object.assign(pagination, newPage)
        getData(null)
      }
      const getData = async (current) => {
        current && (pagination.current = current)
        const route = proxy.$route
        const params = {
          // url: `/complain/detail/${proxy.$route.query.id}`,
          current: pagination.current,
          size: pagination.pageSize
        }
        if (route) {
          const { statisticsUrl } = route.meta
          const toUrl = statisticsUrl ? typeof statisticsUrl === 'string' ? statisticsUrl : statisticsUrl(route) : route.fullPath
          params.url = toUrl
        }
        loading.value = true
        const res = await proxy.$store.dispatch(`operation/complaint/${GET_NUMBER_DETAIL}`, params)
        loading.value = false
        if (res) {
          const { data } = res
          dataSource.value = data.recordList
          pagination.total = data.totalRecord
        }
      }
      return {
        viewDetail,
        pagination,
        dataSource,
        loading,
        tableChange,
        getData
      }
    },
    render () {
      const { dataSource, pagination, tableChange, loading } = this
      return <a-modal
        destroyOnClose
        title="浏览日志"
        width={ 650 }
        footer={ false }
        v-model={ this.viewDetail }>
        <a-table
          bordered
          scroll={{ y: 600 }}
          rowKey={ (r, i) => i }
          columns={ detailColumns }
          dataSource={ dataSource }
          loading={ loading }
          pagination={ pagination }
          onChange={ tableChange }
        />
      </a-modal>
    }
  })
</script>

<style scoped lang="scss">
:deep(.ant-table-header .ant-table-hide-scrollbar) {
  overflow-y: hidden;
}
</style>
