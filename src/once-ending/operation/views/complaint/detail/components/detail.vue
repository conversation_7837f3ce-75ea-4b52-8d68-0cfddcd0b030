<script lang="jsx">
  import { defineComponent, ref, getCurrentInstance, computed, watch, inject, onMounted, nextTick } from 'vue'
  import {
    Row,
    Col,
    Input,
    Checkbox,
    Button,
    Tag,
    message
  } from 'ant-design-vue'
  import InputPeople from '~/components/staff/staff-input'
  import moment from 'moment'
  import { statesEnum, getStatusStyle } from '../constants'
  import { catMap, tagMap, tagOptions } from '../../list/constants'
  import { SET_ARCHIVE_CATEGORY, COMPLAIN_HIDE } from '@operation/store/modules/complaint/action-types'
  import FilePreview from '~/components/uploader/file-preview'
  import { NiImg } from '@jiuji/nine-ui'
  import processor from './processor.vue'
  import complaintApi from '~/once-ending/operation/api/complaint'
  import SmallQrcodeModal from '~/components/small-qrcode-modal.vue'

  export default defineComponent({
    props: {
      detail: {
        type: Object,
        default: () => {
          return {
            attachments: []
          }
        }
      },
      memberInfo: {
        type: Object,
        default: () => ({})
      },
    },
    components: {
      InputPeople,
      processor,
      SmallQrcodeModal
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const cacheProcessUser = ref('')
      console.log(props.detail)
      console.log(props.detail.dealUsers)
      const dealUserSubmit = ref(undefined)
      let processorDis = ref(false)
      watch(() => props.detail?.dealUsers, (val) => {
        dealUserSubmit.value = val?.split(',') || []
      }, {
        immediate: true,
        deep: true
      })
      function toCheck () {
        let pass = true
        const { processUser } = props.detail
        // if (!cacheProcessUser.value && !processUser) {
        //   message.warning('请选择正确的跟进人')
        //   pass = false
        // }
        return pass
      }
      const getDetail = inject('getDetail')
      const getProcess = inject('getProcess')
      // 保存
      const setArchiveCategory = async function () {
        console.log(cacheProcessUser.value)
        const { processUser } = props.detail
        if (!toCheck()) return
        const { id, archiveCategory, showWeb } = props.detail
        const params = {
          id,
          processUser: cacheProcessUser.value || processUser,
          archiveCategory: Number(archiveCategory),
          dealUser: dealUserSubmit.value && dealUserSubmit.value.length > 0 ? dealUserSubmit.value.join(',') : ''
        }
        if (proxy.$tnt.xtenant < 1000) {
          delete params.archiveCategory
        }
        if (proxy.$tnt.xtenant >= 1000) {
          delete params.dealUser
        }
        // showWeb && (params.showWeb = true)
        const res = await proxy.$store.dispatch(
          `operation/complaint/${SET_ARCHIVE_CATEGORY}`,
          params
        )
        if (res) {
          message.success('保存成功')
          setTimeout(() => {
            getDetail()
            getProcess()
          }, 1000)
        }
      }

      const imgShow = function (d) {
        window.open(d)
      }

      const isGJKP = computed(() => {
        return proxy.$store.state.userInfo.Rank.includes('gjkp')
      })

      const complaintHide = async () => {
        let params = {
          id: props.detail.id
        }
        const res = await proxy.$store.dispatch(
          `operation/complaint/${COMPLAIN_HIDE}`,
          params
        )
        if (res) {
          message.success('保存成功')
          props.detail.deleteFlag = !props.detail.deleteFlag
        } else {
          message.error(res.userMsg)
        }
      }

      const changeShowWeb = async () => {
        const { id, showWeb } = props.detail
        const params = {
          id,
          showWeb: !showWeb
        }
        complaintApi.showWeb(params).then(res => {
          if (res.code === 0) {
            message.success('保存成功')
            props.detail.showWeb = !showWeb
          } else {
            message.error(res.userMsg)
          }
        })
      }

      function format (file) {
        const regImg = /.(jpg|bmp|gif|ico|pcx|jpeg|tif|png|raw|tga|webp|svg)$/i
        const regVideo = /.(avi|rmvb|mpeg|wmf|mov|mkv|mp4)$/i
        if (regImg.test(file.extension)) return 'file-image'
        if (regVideo.test(file.extension)) return 'file-video'
      }

      const showFile = function (index) {
        const ref = `filePreview${index}`
        proxy.$refs[ref]?.open()
      }

      function getFile (file) {
        return {
          filePath: file.framePath || file.filepath,
          fileName: file.filename || '',
          fid: file.fid ? file.fid + file.extension : ''
        }
      }

      const dealUserChildsChange = (value) => {
        dealUserSubmit.value = value
        console.log(dealUserSubmit.value)
      }

      watch(() => props.detail.customerEndTime, (newVal, oldVal) => {
        processorDis.value = !!newVal
        console.log(processorDis.value)
      }, { deep: true })

      const toList = function (d, type) {
        const startTime = (type === 1 ? moment().startOf('month') : moment().subtract(90, 'days')).format('YYYY-MM-DD')
        const endTime = (type === 1 ? moment().endOf('month') : moment()).format('YYYY-MM-DD')
        console.log(1, startTime, endTime)
        const routeData = proxy.$router.resolve({
          path: `/complaint/list`,
          query: {
            areaIdM: d.areaId + '',
            startTime,
            endTime,
            tags: '1,2'
          }
        })
        window.open(routeData.href, '_blank')
      }
      return {
        cacheProcessUser,
        setArchiveCategory,
        imgShow,
        isGJKP,
        complaintHide,
        changeShowWeb,
        format,
        showFile,
        getFile,
        dealUserChildsChange,
        processorDis,
        toCheck,
        toList
      }
    },
    render () {
      const {
        detail,
        setArchiveCategory,
        imgShow,
        isGJKP,
        complaintHide,
        showFile,
        format,
        getFile,
        dealUserChildsChange,
        processorDis,
        memberInfo,
        changeShowWeb,
        toList
      } = this
      return (
      <div>
        <a-row>
          <a-col span={8} class="flex flex-align-center">
            <span>投诉 ID：</span>
            <span class="bold">{detail.id}</span>
            { detail.states ? <div class="status" style={getStatusStyle(detail.states)}>{statesEnum[detail.states]}</div> : null }
            { this.$tnt.xtenant < 1000 ? <small-qrcode-modal class="ml-8" code-value={`${this.$tnt.moaHost}/new/#/complain/detail/${detail.id}`}/> : null }
          </a-col>
          <a-col span={8}>
            <span>投诉时间：</span>
            <div class={[detail.dealTimeout ? 'red' : 'big', 'bold']}>
              {detail.addTime
                ? moment(detail.addTime).format('YYYY-MM-DD HH:mm')
                : '-'}
            </div>
          </a-col>
          {!this.$store.state.userInfo.IsTaxModel &&
          this.$tnt.xtenant < 1000 ? (
              <a-col span={8}>
                <span>投诉主体：</span>
                <span style="color:#F15643">{`【${memberInfo.xtenant}】`}</span>
              </a-col>
          ) : null}
        </a-row>
        <a-row>
          { this.$tnt.xtenant < 1000 ? <a-col span={8}>
            <span>定性分类：</span>
            {detail.cat ? <div class="cat" style={catMap.get(detail.cat)}>{detail.catName}</div> : <span class="bold">--</span>}
          </a-col> : null }
          <a-col span={8}>
            <span>投诉性质：</span>
            <a-select
              class="width-174"
              placeholder="请选择"
              v-model={detail.tag}
              options={tagOptions}/>
          </a-col>
        </a-row>
        <a-row>
          <a-col span={24}>
            <span>投诉内容：</span>
            <div class="full-width contents">
              {detail.content}
              <div class="flex attachments flex-wrap">
                {
                  detail.attachments.map((k, i) => <span>
                            {
                              format(k) === 'file-image'
                                ? <NiImg src={k.filepath} width="60" height="60" onTap={() => imgShow(k.filepath)}></NiImg>
                                : <span class="inline-block relative pointer">
                                      <NiImg src={k.framePath} width="60" height="60"
                                             onTap={() => showFile(i)}>
                                      </NiImg>
                                      <FilePreview file={getFile(k)}
                                                   type={format(k)}
                                                   pathKey="filePath"
                                                   ref={`filePreview${i}`}
                                                   style="display: none"/>
                                      <a-icon type="play-circle" class="playIco" onClick={() => showFile(i)}/>
                                  </span>

                            }
                          </span>)
                }

              </div>
            </div>
          </a-col>
        </a-row>
        <a-row>
          { this.$tnt.xtenant < 1000 ? <a-col span={8}>
            <span>投诉来源：</span>
            <span class="bold">{detail.tsTypeStr}</span>
          </a-col> : null }
          <a-col span={8}>
            <span>录入人：</span>
            <span class="bold">{detail.inuser ? detail.inuser : '系统'}</span>
          </a-col>
          <a-col span={8}>
            <span>网站显示：</span>
            <a-switch onChange={changeShowWeb} checked={detail.showWeb}/>
            { this.$tnt.xtenant < 1000 && isGJKP ? <span class="ml-20">
              <span>隐藏投诉：</span>
              <a-switch checked={detail.deleteFlag} onChange={complaintHide}/>
            </span> : null }
            { this.$tnt.xtenant >= 1000 ? <span class="ml-20">
              <Checkbox v-model={detail.archiveCategory} style="color:#1890ff;">
              是否典型投诉
              </Checkbox>
            </span> : null }
          </a-col>
        </a-row>
        <a-row>
          <a-col span={8}>
            <span>跟进人：</span>
            {detail.processUser ? (
              <Input
                disabled
                v-model={detail.processUser}
                class="width-174"
              ></Input>
            ) : (
              <InputPeople
                format={3}
                class="width-174 grow1"
                onChange={people => {
                  this.cacheProcessUser = people.value
                }}
                placeholder="输入工号或姓名"
                allow-clear
              />
            )}
          </a-col>
          { this.$tnt.xtenant < 1000 ? <a-col span={8}>
            <span>处理人：</span>
            { detail.dealUsers || detail.dealUsers === null ? <processor dealUserChild={detail.dealUsers} processorDis={ processorDis } onDealUserChildsChange={dealUserChildsChange}></processor>
                : null }
          </a-col> : null }
        </a-row>
        <a-row>
          <a-col span={8}>
            <span>还原时间：</span>
            <div class="bold">
              {detail.huanyuantime
                ? moment(detail.huanyuantime).format('YYYY-MM-DD HH:mm')
                : '--'}
            </div>
          </a-col>
          <a-col span={8}>
            <span>还原超时时间：</span>
            <div class="bold">
              {detail.huanyuantimeout
                ? moment(detail.huanyuantimeout).format('YYYY-MM-DD HH:mm')
                : '--'}
            </div>
            { detail.huanyuantime && detail.huanyuantimeout && detail.huanyuantime > detail.huanyuantimeout ? <div class="status" style={getStatusStyle(-1)}>已超时</div> : null }
          </a-col>
        </a-row>
        <a-row>
          <a-col span={24}>
            <span class="mt-6">投诉区域：</span>
            <div>
              { detail.areas?.length ? detail.areas.map((d, i) => (
                  <div class={['area-item', i === 0 ? '' : 'mt-8']} key={i}>
                    { i + 1 }、
                    <span style="min-width: 90px">{d.area}</span>
                    <span class="ml-10" style="min-width: 90px">
                      {d.areaLevel || '门店暂无等级'}
                    </span>
                    <span class="ml-10">本月累计被投诉：
                      <span class="pointer" onClick={() => toList(d, 1)} style="color:rgba(24, 144, 255, 1)">{d.monthTouSuCount || 0}</span> 次
                    </span>
                    <span class="ml-32">90天内累计被投诉次数：
                      <span class="pointer" onClick={() => toList(d, 2)} style="color:rgba(24, 144, 255, 1)">{d.complaintCount || 0} </span> 次
                    </span>
                  </div>
                )) : '-' }
            </div>
          </a-col>
        </a-row>
      </div>
      )
    }
  })
</script>
<style lang="scss" scoped>
@import "../common.scss";
.area-item {
  min-height: 32px;
  background: linear-gradient( 270deg, #FFFFFF 0%, #F7F8FA 100%);
  border-radius: 4px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  font-weight: 600;
}
.img {
  padding: 0.3em;
  width: 7em;
  height: 7em;
  border: 1px solid #f0f0f0;
  border-radius: 4px;

  & + & {
    margin-left: 0.5em;
  }
  img {
    width: 100%;
    height: 100%;
  }
}

.ant-col {
  &-24 {
    align-items: unset;
  }

  display: flex;
  align-items: center;

  > span {
    white-space: nowrap;
  }
}
.big {
  font-weight: 600;
}
.contents {
  min-height: 50px;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #DEDEDE;
}
.tag-em {
  display: inline-block;
  margin-left: 1.5em;
  color: #49bdef;
  padding: 0.2em 1em;
  border-radius: 4px;
  border: 1px solid #49bdef;

  span {
    color: red;
    font-size: 1.1em;
  }
}
.attachments{
  img{
    display: inline-block;
    border-radius: 8px;
    margin: 8px 8px 0 0;
  }
  .playIco{
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -18px;
    margin-top: -15px;
    font-size: 30px;
    color: #FFFFFF;
  }
}
:deep(.cat) {
  padding: 4px;
  font-size: 12px;
  line-height: 12px;
  display: inline-block;
  border-radius: 2px;
}
</style>
