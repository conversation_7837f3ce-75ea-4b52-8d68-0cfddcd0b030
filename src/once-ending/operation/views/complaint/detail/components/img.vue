<script lang="jsx">
  import { defineComponent } from 'vue'
  import lct from '../images/lct.png'
  import lct9ji from '../images/lct-9ji.png'

  export default defineComponent({
    props: {
      imgSrc: {
        type: String,
        default: ''
      }
    },
    render () {
      const { imgSrc } = this
      return (
        <div class="ml-8 imgWrap">
          <img src={ imgSrc || (this.$tnt.xtenant < 1000 ? lct9ji : lct) } />
        </div>
      )
    }
  })
</script>
<style lang="scss" scoped>
.imgWrap{
  width: 750px;
  img{
    max-width: 750px;
  }
}
</style>
