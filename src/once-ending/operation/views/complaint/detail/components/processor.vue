<template>
  <div>
    <ni-staff-select
    v-model="dealUserChilds"
    style="width: 14em;"
    :multiple="true"
    :disabled="processorDis"
    showType="name"
    :maxTagCount="1"
    placeholder="输入工号或姓名"
    @change="onStaffChange"/>
  </div>
</template>

<script>
  import { ref } from 'vue'
  import { NiStaffSelect } from '@jiuji/nine-ui'
  import split from 'lodash/split'
  export default {
    props: {
      dealUserChild: null,
      processorDis: {
        type: Boolean,
        default: false
      }
    },
    components: {
      NiStaffSelect
    },
    setup (props, { emit }) {
      console.log(props.dealUserChild)
      const dealUserChilds = ref(undefined)
      if (props.dealUserChild && props.dealUserChild.includes(',')) {
        let newArrar = split(props.dealUserChild, ',')
        dealUserChilds.value = newArrar.map(Number)
      } else if (props.dealUserChild && !props.dealUserChild.includes(',')) {
        dealUserChilds.value = [Number(props.dealUserChild)]
      } else {
        dealUserChilds.value = undefined
      }
      /**
     *  keys {id,ids} Number | Number[] key值组合
     *  outputObject {...outputObject} Object 已处理配置字段
     *  originObj { object } Object | Object[] 默认查询到的对象字段
     *  option { option:Option } node节点
     */
      const onStaffChange = () => {
        console.log(dealUserChilds.value)
        emit('dealUserChildsChange', dealUserChilds.value)
      }
      return {
        dealUserChilds,
        onStaffChange
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>
