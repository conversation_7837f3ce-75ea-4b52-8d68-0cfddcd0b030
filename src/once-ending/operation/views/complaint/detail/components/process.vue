<script lang="jsx">
  import { defineComponent, ref, computed, getCurrentInstance, inject, nextTick } from 'vue'
  import { Radio, Button, Checkbox, message, Popover } from 'ant-design-vue'
  import filePreview from '~/components/uploader/file-preview'
  import {
    IS_SHOW_PROCESS_INFO
  } from '@operation/store/modules/complaint/action-types'
  import complaint from '@operation/api/complaint'
  import { to } from '@common/utils/common'
  import LogUserDetail from '@hr/views/custom-benefits/components/commponets/log-user-detail.vue'
  export default defineComponent({
    props: {
      processLog: {
        type: Array,
        default: () => ([])
      },
      memberInfo: {
        type: Object,
        default: () => ({})
      }
    },
    components: { filePreview },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const type = inject('type')
      const getProcess = inject('getProcess')

      const list = computed(() => {
        return type.value === null
          ? props.processLog
          : props.processLog.filter(item => item.cate === type.value)
      })

      const checkAll = computed(() => {
        const { processLog } = props
        const isChecked = (key) => processLog.every(item => item[key])
        return {
          showWeb: isChecked('showWeb'),
          isShow: isChecked('isShow'),
          fakeLog: isChecked('fakeLog'),
        }
      })

      function toCheckAll (type, e) {
        props.processLog.forEach(it => {
          if (type) {
            it[type] = e.target.checked
          } else {
            it.showWeb = true
            it.isShow = true
            it.fakeLog = true
          }
        })
      }

      const checked = function (item) {
        const index = props.processLog.findIndex(i => i.id === item.id)
        props.processLog[index].isShow = !item.isShow
      }

      const check = async function () {
        const cacheShowList = props.processLog.filter(it => it.showWeb)
        if (cacheShowList.length > 1) {
          return message.warning('仅允许勾选一条进程为网站可见')
        }
        const tsProcesses = []
        props.processLog.map(item => {
          tsProcesses.push({ ...item, isShow: Boolean(item.isShow) })
        })
        const params = {
          tsProcesses
        }
        const res = await proxy.$store.dispatch(
          `operation/complaint/${IS_SHOW_PROCESS_INFO}`,
          params
        )
        if (res) {
          message.success('保存成功')
          getProcess()
        }
      }
      function getFile (file) {
        return {
          filePath: file.framePath || file.imgUrl,
          fileName: file.name || '',
          fid: file.fid ? file.fid + file.extension : ''
        }
      }
      function format (file) {
        const regImg = /.(jpg|bmp|gif|ico|pcx|jpeg|tif|png|raw|tga|webp|svg)$/i
        const regVideo = /.(avi|rmvb|mpeg|wmf|mov|mkv|mp4)$/i
        const regAudio = /.(mp3|wav|m4a)$/i
        if (regImg.test(file.name)) return 'file-image'
        if (regVideo.test(file.name)) return 'file-video'
        if (regAudio.test(file.fileName)) return 'file-audio'
      }
      const showFile = function (firstIndex, secondIndex) {
        const ref = `filePreview${firstIndex}${secondIndex}`
        proxy.$refs[ref]?.open()
      }

      const getWeChatRecordStatus = async function (d) {
        // checkFlag,标识是否已经发送过补偿
        if (d.checkFlag) return message.warning('已发放过补偿,请勿重复发放')
        const params = {
          recordId: d.processId
        }
        const [err, res] = await to(complaint.getWeChatRecordStatus(params))
        if (err) throw err
        const { code, data, userMsg } = res
        if (code === 0) {
          if (data) {
            message.error('已发放过补偿,请勿重复发放')
          } else {
            send(d)
          }
        } else {
          message.error(userMsg)
        }
      }

      const send = async function (d) {
        const params = {
          recordId: d.processId,
          tsId: proxy.$route.params.id,
          userId: props.memberInfo.userId
        }
        const [err, res] = await to(complaint.complaintPay(params))
        if (err) throw err
        const { code, userMsg } = res
        if (code === 0) {
          message.success('补偿发放成功')
          const timeId = setTimeout(() => {
            proxy.$router.go(0)
            clearTimeout(timeId)
          }, 1000)
        } else {
          message.error(userMsg)
        }
      }

      return {
        type,
        list,
        checked,
        check,
        showFile,
        getFile,
        format,
        getWeChatRecordStatus,
        toCheckAll,
        checkAll
      }
    },
    render () {
      const { list, checked, check, file, getFile, showFile, format, getWeChatRecordStatus, toCheckAll, checkAll } = this
      return (
      <div>
        <div>
          <Radio.Group
            v-model={this.type}>
            <Radio.Button value={null}>全部</Radio.Button>
            <Radio.Button value={8}>投诉还原</Radio.Button>
            <Radio.Button value={9}>投诉处理</Radio.Button>
            <Radio.Button value={10}>反思整改</Radio.Button>
          </Radio.Group>
        </div>
        <div class="box">
          <div class="flex font-12 mb-10">
            <div class="w-24">
              网站可见
            </div>
            <div class="ml-14 w-24">
              虚假跟进
            </div>
            <div class="ml-14 w-24">会员可见</div>
          </div>
          <div class="log-module">
            {list.map((d, firstIndex) => (
              <div
                class="flex-justify-between"
                style="display:flex;"
                key={d.index}
              >
                <div class="flex">
                  <div
                    class="item flex flex-justify-center"
                    style="flex-shrink: 0;margin-top: 1px"
                  >
                    <div class="w-24 flex flex-justify-center">
                      <Checkbox
                        v-model={d.showWeb}
                      ></Checkbox>
                    </div>
                    <div class="w-24 ml-14 flex flex-justify-center">
                      <Checkbox
                        class={d.fakeLog ? 'red' : ''}
                        v-model={d.fakeLog}
                      ></Checkbox>
                    </div>
                    <div class="w-24 ml-14 flex flex-justify-center">
                      <Checkbox
                        v-model={d.isShow}
                        onClick={() => {
                          checked(d)
                        }}
                      ></Checkbox>
                    </div>
                  </div>
                  <div class="flex" style="margin-left:46px;">
                    <div style="height:100%;margin-right: 8px">
                      <div class={['line line1', firstIndex === 0 ? '' : 'grey-bg']}></div>
                      <div class={['circle'] }></div>
                      {firstIndex < list.length - 1 ? <div class={['line line2 grey-bg']}></div> : null }
                    </div>
                    <div class='dsc-content'>
                      <p>
                    <span
                      domPropsInnerHTML={(d.dsc || '').replaceAll('\n', '<br>')}
                      class={[d.fakeLog ? 'red' : '', 'content']}
                    />
                        {
                          d.cate === 6 ? <span><span>，</span><span style="color:blue;cursor: pointer;" onClick={() => { getWeChatRecordStatus(d) }}>补发{d.money}元补偿</span></span> : null
                        }
                        <div class='info font-12' style="line-height: 20px">
                          <LogUserDetail logUserName={d.opUser} showPop={d.opUser !== '系统'}></LogUserDetail>
                          <span class="font-12" style="color: #828282 ">
                          {d.intime}
                        </span>
                        </div>
                      </p>
                      <p>
                    <span class='info'>
                      {d.attachHtml.length ? (
                        <div class="process-log">
                          {d.attachHtml.map((k, secondIndex) => <span>
                            {
                              format(k) === 'file-image'
                                ? <img src={k.imgUrl} alt={k.name} width="66" height="66" onClick={() => showFile(firstIndex, secondIndex)}>
                                  <filePreview file={getFile(k)}
                                               type={format(k)}
                                               showAudioPreview={true}
                                               pathKey="filePath"
                                               ref={`filePreview${firstIndex}${secondIndex}`}
                                               style="display: none"/>
                                </img>
                                : format(k) === 'file-video'
                                  ? <span class="inline-block relative pointer">
                                      <img src={k.framePath} alt={k.name} width="66" height="66"
                                           onClick={() => showFile(firstIndex, secondIndex)}>
                                        <filePreview file={getFile(k)}
                                                     type={format(k)}
                                                     pathKey="filePath"
                                                     ref={`filePreview${firstIndex}${secondIndex}`}
                                                     style="display: none"/>
                                      </img>
                                      <a-icon type="play-circle" class="playIco" onClick={() => showFile(firstIndex, secondIndex)}/>
                                  </span>
                                  : <a
                                    style="margin-right:8px;"
                                    key={k.id}
                                    onClick={() => showFile(firstIndex, secondIndex)}
                                  >
                                    {k.name}
                                    <filePreview file={getFile(k)}
                                                 type={format(k)}
                                                 pathKey="filePath"
                                                 ref={`filePreview${firstIndex}${secondIndex}`}
                                                 style="display: none"/>
                                  </a>
                            }
                          </span>)}
                        </div>
                      ) : null}
                    </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          { list?.length ? <div class="flex flex-align-center">
            <div class="w-24 flex flex-justify-center">
              <Checkbox checked={checkAll.showWeb} onChange={(e) => toCheckAll('showWeb', e)}/>
            </div>
            <div class="w-24 ml-14 flex flex-justify-center">
              <Checkbox checked={checkAll.fakeLog} onChange={(e) => toCheckAll('fakeLog', e)}/>
            </div>
            <div class="w-24 ml-14 flex flex-justify-center">
              <Checkbox checked={checkAll.isShow} onChange={(e) => toCheckAll('isShow', e)}/>
            </div>
            <div style="margin-left:46px;">全选</div>
            <div class="buttons ml-12" onClick={check}>保存修改</div>
          </div> : null}
        </div>
      </div>
      )
    }
  })
</script>

<style lang="scss" scoped>
@import "../common.scss";

.box {
  margin-top: 24px;

  .item {
    padding-bottom: 24px;
 }
}
.grey-bg {
  background: #e8e8e8;
}
.line {
  margin-left: 1px;
  width: 1px;
}
.line1 {
  height: 7px;
}
.line2{
  height: calc(100% - 14px);
}
.circle {
  width: 7px;
  height: 7px;
  border: 2px solid #239DFC;
  border-radius: 50%;
  position: relative;
  box-sizing: border-box;
  z-index: 1;
  margin-left: -2px;
}
.dsc-content{
  line-height: 22px;
  padding-bottom: 24px;
  .content{
    word-wrap: break-word;
    //white-space: pre-line;
  }
}
.process-log{
  img{
    display: inline-block;
    border-radius: 8px;
    margin: 8px 8px 0 0;
  }
  .playIco{
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -18px;
    margin-top: -15px;
    font-size: 30px;
    color: #FFFFFF;
  }
  a {
    display: block;
  }
}
.ml-10 {
  margin-left: 10px;
}
.log-module {
  max-height: 40vh;
  overflow-y: auto;
}
.w-24{
  width: 24px;
}
</style>
