<script lang="jsx">
  import { defineComponent, ref, reactive, toRefs, nextTick, computed } from 'vue'
  import { NiImg } from '@jiuji/nine-ui'
  import down from '../images/down.png'
  import BigNumber from 'bignumber.js'
  import { throttle } from 'lodash'

  export default defineComponent({
    components: { NiImg },
    props: {
      showUpDown: {
        type: Boolean,
        default: true
      }
    },
    setup () {
      const collapseBoxRef = ref()
      const collapseContainerRef = ref()
      const state = reactive({
        showAll: false,
        transitionTime: 0.3
      })

      let changeShowAll = () => {
        let timer
        timer && (clearTimeout(timer))
        state.showAll = !state.showAll
        const contentHeight = collapseBoxRef.value?.offsetHeight || 0
        state.transitionTime = BigNumber(contentHeight).multipliedBy(0.001).toFixed(2)
        const collapseContainerHeight = collapseContainerRef.value.style.height
        const isAuto = collapseContainerHeight === 'auto'
        if (isAuto) {
          collapseContainerRef.value.style.height = contentHeight + 'px'
        }
        if (state.showAll) {
          collapseContainerRef.value.style.height = contentHeight + 'px'
        } else {
          timer = setTimeout(() => {
            collapseContainerRef.value.style.height = 0
            timer && (clearTimeout(timer))
          }, 0)
        }
      }
      changeShowAll = throttle(changeShowAll, 200)

      function transitionend () {
        state.showAll && (collapseContainerRef.value.style.height = 'auto')
      }

      return {
        ...toRefs(state),
        collapseBoxRef,
        collapseContainerRef,
        changeShowAll,
        transitionend
      }
    },
    render () {
      const { showAll, changeShowAll, showUpDown, transitionTime, transitionend } = this
      return <div>
        <div onTransitionend={transitionend} ref="collapseContainerRef" class="collapse-container" style={{ transition: `height ${transitionTime}s ease-in-out` }}>
          <div ref="collapseBoxRef" style="overflow: hidden">{ this.$slots.default }</div>
        </div>
        { showUpDown ? <div class="flex arrow-box" onClick={changeShowAll}>
          { showAll ? '收起' : '查看' }全部回复
          <NiImg src={down} alt="down" class={['down', showAll ? 'arrow-transform' : '']}/>
        </div> : null }
      </div>
    }
  })
</script>

<style scoped lang="scss">
.collapse-container {
  overflow: hidden;
  will-change: height;
  height: 0;
}
.arrow-box {
  margin-top: 20px;
  margin-left: 42px;
  color: #239DFC;
  line-height: 12px;
  font-size: 12px;
  cursor: pointer;
  .down {
    width: 12px;
    height: 12px;
  }
}
.arrow-transform {
  transition: transform 0.3s;
  transform: rotate(-180deg) translateZ(0)
}
</style>
