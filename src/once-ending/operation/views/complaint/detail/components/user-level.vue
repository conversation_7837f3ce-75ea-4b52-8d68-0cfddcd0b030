<template>
  <div class="flex flex-align-center">
    <ni-img class="user-level" :src="levelsIcon.get(member.userClassName || member.userclassName)"/>
    <div class="user-star" v-if="(member.userClassName || member.userclassName) === '双钻会员'">{{memberStarLevel || member.memberStarLevel || 1}}</div>
    <ni-img v-if="member.specialFlag" class="special" :src="special"/>
  </div>
</template>

<script>

  import { defineComponent } from 'vue'
  import { NiImg } from '@jiuji/nine-ui'
  import { levelsIcon } from '../constants'
  import special from '../images/special.png'

  export default defineComponent({
    components: { NiImg },
    props: {
      member: {
        type: Object,
        default: () => {}
      },
      memberStarLevel: {
        type: Number,
        default: 0
      }
    },
    data () {
      return {
        levelsIcon,
        special
      }
    }
  })
</script>

<style scoped lang="scss">
.user-level {
  height: 20px;
}
.user-star {
  height: 20px;
  background: #2D2016;
  border-radius: 0px 10px 10px 0px;
  padding-right: 5px;
  font-weight: 500;
  font-size: 10px;
  color: #F7D19C;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding-top: 1px;
}
.special {
  width: 30px;
  height: 20px;
  margin-left: 4px;
}
</style>
