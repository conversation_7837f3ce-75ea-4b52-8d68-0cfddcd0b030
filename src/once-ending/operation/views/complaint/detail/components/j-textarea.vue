<script lang="jsx">
  import { defineComponent, reactive, getCurrentInstance, watch, ref, nextTick } from 'vue'

  export default defineComponent({
    name: 'j-textarea',
    props: {
      showComment: {
        type: Boolean,
        default: false
      },
      placeholder: {
        type: String,
        default: '添加评论'
      },
      showAnonymous: {
        type: Boolean,
        default: true
      },
      loading: {
        type: Boolean,
        default: false
      },
      maxLength: {
        type: Number,
        default: 0
      },
      minRows: {
        type: Number,
        default: 4
      },
      background: {
        type: String,
        default: '#F7F8FA'
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const textareaRef = ref()

      watch(() => props.showComment, (show) => {
        close()
        show && (nextTick(() => {
          textareaRef.value?.focus()
        }))
      })

      const form = reactive({
        content: undefined,
        anonymousFlag: undefined
      })
      function addComment () {
        if (!form.content) {
          return proxy.$message.warning('请输入内容')
        }
        if (form.content.length > props.maxLength && props.maxLength) {
          return proxy.$message.warning(`内容最多${props.maxLength}字`)
        }
        proxy.$emit('toComment', form)
      }
      function close () {
        form.content = undefined
        form.anonymousFlag = undefined
      }
      return {
        form,
        addComment,
        close,
        textareaRef
      }
    },
    render () {
      const {
        addComment,
        form,
        showComment,
        placeholder,
        showAnonymous,
        loading,
        maxLength,
        minRows,
        background
      } = this
      return showComment ? <div>
        <a-textarea
          ref="textareaRef"
          class="textarea"
          maxLength={500}
          placeholder={placeholder}
          style={{ background }}
          autoSize={{ minRows, maxRows: 10 }}
          v-model={form.content}/>
        <div class="flex flex-align-center flex-justify-between mt-10">
          <span>
            <a-checkbox v-model={form.anonymousFlag} style="margin-right: 4px"/>
            匿名评论</span>
          <a-button loading={loading} type="primary" onClick={addComment}>评论</a-button>
        </div>
    </div> : null
    }
  })

</script>

<style scoped lang="scss">
:deep(.ant-checkbox-inner) {
  border-radius: 50%;
}
:deep(.ant-checkbox-checked::after) {
  border-radius: 50%;
}
.textarea {
  display: block;
  border: none;
  width: 100%;
  box-sizing: border-box;
  border-radius: 4px;
  padding:10px;
}
</style>
