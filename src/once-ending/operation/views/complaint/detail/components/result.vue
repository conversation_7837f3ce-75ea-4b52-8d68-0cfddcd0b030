<script lang="jsx">
  import { defineComponent, ref, nextTick, getCurrentInstance, inject } from 'vue'
  import { Button, Row, Col, Input, message } from 'ant-design-vue'
  import { modelEnum, originAreaColumns, originStaffColumns } from '../constants'
  import DepartModal from './depart-modal'
  import DutyModal from './duty-modal'
  import {
    DELETE_ZE_REN_REN,
    DELETE_TOU_SU_DEPART,
    GET_TOU_SU_END_INFO
  } from '@operation/store/modules/complaint/action-types'

  export default defineComponent({
    components: {
      DepartModal,
      DutyModal
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const id = ref(proxy.$route.params.id)

      const info = inject('resultInfo')

      const modalDepart = ref({
        title: '责任地区划分',
        visible: false,
        destroyed: false,
        type: '0', // includes [add people, add divide, divide, people],
        data: {
          areaId: '',
          id: '',
          departCode: undefined,
          categoryIds: [],
          cat: 0,
          kinds: 0,
          touSuRank: 0,
          score: 0,
          tousuPoint: undefined
        } // 用set处理对象
      })

      const modalDuty = ref({
        visible: false,
        destroyed: false,
        title: '责任人划分',
        data: {
          tousuPoint: 0,
          bonusPoints: undefined,
          tousuRank: 1,
          tousuLosePoint: 0,
          userName: '',
          userId: '',
          id: ''
        }
      })

      const destroyed = ref({
        departModal: true,
        dutyModal: true
      })

      const getLevel = function (tousuRank) {
        return (
          modelEnum.touSuRank.find(ts => ts.value === tousuRank)?.label || ''
        )
      }

      // 修改/添加责任人
      const editPersonLiable = function (item = '') {
        if (item) {
          item.bonusPoints = item.bonusPoint
          modalDuty.value.data = Object.assign({}, item)
        } else {
          modalDuty.value.data = {
            tousuPoint: 0,
            bonusPoints: undefined,
            tousuRank: 1,
            tousuLosePoint: 0,
            userName: '',
            userId: '',
            id: ''
          }
        }
        modalDuty.value.visible = true
      }

      // 删除责任人
      const deletePersonLiable = function (item) {
        proxy.$confirm({
          title: '确认删除吗？',
          onOk: () => {
            deleteZeRenRen(item)
          }
        })
      }

      const deleteZeRenRen = async function (item) {
        const params = {
          ...item,
          tousuId: +id.value
        }
        const res = await proxy.$store.dispatch(
          `operation/complaint/${DELETE_ZE_REN_REN}`,
          params
        )
        if (res) {
          message.success(res.userMsg)
          saveSuccess()
        }
      }

      // 点击添加或修改责任地区划分
      const touSuDepartsAdd = function (title, item) {
        modalDepart.value.visible = true
        // modalDepart.value.title = title || '修改责任地区划分'
        if (title === '添加责任地区划分') {
          modalDepart.value.data = Object.assign(
            {},
            {
              areaId: null,
              departCode: null,
              categoryIds: [],
              cat: 0,
              kinds: 0,
              touSuRank: 0,
              score: 0,
              id: '',
              tousuPoint: undefined
            }
          )
          modalDepart.value.type = 0
          return
        }
        const {
          type,
          cat,
          kinds,
          touSuRank,
          touSuTypes,
          areaId,
          departId,
          scoreArea,
          scoreDep,
          id,
          tousuPoint
        } = item
        modalDepart.value.type = type
        modalDepart.value.data.id = id
        modalDepart.value.data.areaId = type === 0 ? areaId + '' : undefined
        modalDepart.value.data.departCode =
          type === 0 ? undefined : departId + ''
        modalDepart.value.data.categoryIds = touSuTypes
        modalDepart.value.data.cat = cat || 0
        modalDepart.value.data.kinds = kinds || 0
        modalDepart.value.data.touSuRank = touSuRank || 0
        modalDepart.value.data.tousuPoint = tousuPoint
        modalDepart.value.data.score =
          type === 0 ? scoreArea || 0 : scoreDep || 0
      }

      // 删除责任规划
      const delTouSuDepart = function (item) {
        proxy.$confirm({
          title: '确认删除吗？',
          onOk: () => {
            deleteTouSuDepart(item)
          }
        })
      }

      const deleteTouSuDepart = async function (item) {
        const params = {
          ...item,
          tousuId: +id.value
        }
        const res = await proxy.$store.dispatch(
          `operation/complaint/${DELETE_TOU_SU_DEPART}`,
          params
        )
        if (res) {
          message.success(res.userMsg)
          getDetail()
          proxy.$emit('getInfo')
        }
      }

      // 获取详情
      const getDetail = async function () {
        const params = {
          id: id.value
        }
        const res = await proxy.$store.dispatch(
          `operation/complaint/${GET_TOU_SU_END_INFO}`,
          params
        )
        if (res) {
          info.value = res.data
        }
        modalDepart.value.visible = false
      }

      // 查找label
      const fineEnumLabel = function (value, name) {
        return modelEnum[name].find(item => item.value === value)?.label || '无'
      }

      const destroyedModal = function (key) {
        destroyed.value[key] = false
        nextTick(() => {
          destroyed.value[key] = true
        })
      }

      getDetail()

      function saveSuccess () {
        const timer = setTimeout(() => {
          getDetail()
          proxy.$emit('getProcess')
          clearTimeout(timer)
        }, 1000)
      }

      return {
        id,
        info,
        modalDepart,
        modalDuty,
        getLevel,
        editPersonLiable,
        deletePersonLiable,
        touSuDepartsAdd,
        fineEnumLabel,
        delTouSuDepart,
        getDetail,
        destroyedModal,
        destroyed,
        saveSuccess
      }
    },
    data () {
      return {
        customRenderMap: new Map([
          [
            'areaName',
            (text, record) => <span>{ record[record.type === 0 ? 'areaName' : 'department'] || '-' }</span>
          ],
          [
            'departAreaName',
            (text, record) => <span>{ record[record.type === 0 ? 'departAreaName' : 'departmentCentName'] || '-' }</span>
          ],
          [
            'scoreArea',
            (text, record) => <span>{ record[record.type === 0 ? 'scoreArea' : 'scoreDep'] || 0 }</span>
          ],
          [
            'enumLabel',
            dataIndex => (text, record) => <span>{ this.fineEnumLabel(record[dataIndex], dataIndex) }</span>
          ],
          [
            'touSuType',
            (text, record) => <div>
              { record.touSuType?.length ? record.touSuType.map((type, index) => (
                <div key={index}>
                  <span>【{type.name}】</span>
                  {type.value}
                </div>
              )) : '--' }
            </div>
          ],
          [
            'text',
            text => text ?? '--'
          ],
          [
            'tousuLosePoint',
            (text, record) => <span>{this.$tnt.xtenant < 1000 ? record.tousuLosePoint : record.tousuPoint}</span>
          ],
          [
            'tousuPoint',
            (text, record) => <span>{this.$tnt.xtenant < 1000 ? record.tousuPoint : record.tousuRank && this.getLevel(record.tousuRank)}</span>
          ],
          [
            'complaintCount',
            text => text || 0
          ],
          [
            'action1',
            (text, record) => <span>
              <span class="color-blue pointer" onClick={() => {
                this.touSuDepartsAdd('', record)
              }}>编辑</span>
              <span onClick={() => {
                this.delTouSuDepart(record)
              }} class="color-blue pointer ml-20">删除</span>
            </span>
          ],
          [
            'action2',
            (text, record) => <span>
              <span onClick={() => {
                this.editPersonLiable(record)
              }} class="color-blue pointer">编辑</span>
              <span onClick={() => {
                this.deletePersonLiable(record)
              }} class="color-blue pointer ml-20">删除</span>
            </span>
          ]
        ])
      }
    },
    computed: {
      areaColumns () {
        return originAreaColumns.map(it => {
          const cache = { ...it }
          let showType = this.customRenderMap.get(it.showType)
          showType && (showType = showType(it.dataIndex))
          cache.customRender = this.customRenderMap.get(it.dataIndex) || showType || this.customRenderMap.get('text')
          return cache
        })
      },
      staffColumns () {
        return originStaffColumns.map(it => {
          const cache = { ...it }
          cache.customRender = this.customRenderMap.get(it.dataIndex) || this.customRenderMap.get('text')
          return cache
        })
      }
    },
    render () {
      const {
        destroyed,
        modalDepart,
        modalDuty,
        touSuDepartsAdd,
        info,
        fineEnumLabel,
        delTouSuDepart,
        editPersonLiable,
        getLevel,
        deletePersonLiable,
        id,
        getDetail,
        destroyedModal,
        areaColumns,
        staffColumns,
        saveSuccess
      } = this
      return (
        <div class="mb-24">
          <div class="single-contents mt-10">
            <div class="flex flex-align-center flex-justify-between">
              <span class="title">责任地区划分</span>
              <a-button type="primary" onClick={() => {
                touSuDepartsAdd('添加责任地区划分')
              }}>添加责任地区</a-button>
            </div>
            <a-table
              class="mt-20"
              columns={areaColumns}
              dataSource={info.touSuDeparts}
              pagination={false}
              rowClassName={(r, i) => [r.areaType === 1 ? 'level2' : 'level1', i + 1 === info.touSuDeparts?.length ? 'levelLast' : '']}
              rowKey={(r, i) => i}/>
          </div>
          <div class="single-contents mt-10">
            <div class="flex flex-align-center flex-justify-between">
              <span class="title">责任人划分</span>
              <a-button type="primary" onClick={() => editPersonLiable('')}>添加责任人</a-button>
            </div>
            <a-table
              class="mt-20"
              columns={staffColumns}
              dataSource={info.zeRenRenList}
              pagination={false}
              rowClassName={(r, i) => [r.areaType === 1 ? 'level2' : 'level1', i + 1 === info.touSuDeparts?.length ? 'levelLast' : '']}
              rowKey={(r, i) => i}/>
          </div>
          {destroyed.departModal && (
            <DepartModal
              modal-depart={modalDepart}
              show-depart-modal={modalDepart.visible}
              {...{
                on: {
                  'update:showDepartModal': val => {
                    modalDepart.visible = val
                  }
                }
              }}
              onGetDetail={getDetail}
              onGetInfo={() => {
                this.$emit('getInfo')
              }}
              onDestroyedModal={destroyedModal}
            ></DepartModal>
          )}
          {destroyed.dutyModal && (
            <DutyModal
              modal-duty={modalDuty}
              show-duty-modal={modalDuty.visible}
              {...{
                on: {
                  'update:showDutyModal': val => {
                    modalDuty.visible = val
                  }
                }
              }}
              onSaveSuccess={saveSuccess}
              onDestroyedModal={destroyedModal}
            ></DutyModal>
          )}
        </div>
      )
    }
  })
</script>

<style lang="scss" scoped>
@import "../common.scss";
:deep(.ant-table-thead > tr > th) {
  border-bottom: none;
  padding: 18px 10px;
  line-height: 18px;
  background: #FAFAFA;
  border-radius: 4px 4px 0px 0px;
  font-weight: 600;
}
:deep(.ant-table-tbody > tr > td) {
  border-bottom: none;
  padding: 18px 10px;
  line-height: 18px;
}
:deep(.level1) {
  background: rgba(241, 86, 67, 0.1);
}
:deep(.level2) {
  background: rgba(24, 144, 255, 0.1);
}
:deep(.ant-table-placeholder) {
  border-bottom: none;
}
:deep(.ant-empty-normal) {
  margin: 20px 0 0 0;
}
:deep(.levelLast > td) {
  &:first-child {
    border-radius: 0px 0px 0px 4px;
  }
  &:last-child {
    border-radius: 0px 0px 4px 0px;
  }
}
.grow1 {
  flex-grow: 1;
}

.ant-col {
  &-24 {
    align-items: unset;
    > span {
      white-space: nowrap;
    }

    > div {
      flex-grow: 1;
    }
  }

  display: flex;
  align-items: center;
  margin-bottom: 0.5em;

  > span {
    white-space: nowrap;
  }

  .ant-col {
    > span {
      white-space: nowrap;
      display: block;
      width: 5em;
    }
  }

  .divide {
    margin-left: 4.5em;
    flex-grow: 1;

    .ant-col {
      > span {
        white-space: nowrap;
        display: block;
        width: 5em;
      }
    }

    .store {
      padding: 2em;
      background: #eaf5ff;
      border-radius: 0.5em;
    }

    .department {
      padding: 2em;
      border-radius: 0.5em;
      background: #fbe8ea;
    }
  }
}
:deep(.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td) {
  background: none;
}
</style>
