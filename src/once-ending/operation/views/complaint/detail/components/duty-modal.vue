<script lang="jsx">
  import { defineComponent, computed, getCurrentInstance } from 'vue'
  import { Row, Col, Modal, Select, InputNumber, message } from 'ant-design-vue'
  import InputPeople from '~/components/staff/staff-input'
  import { modelEnum } from '../constants'
  import {
    MODIFY_ZE_REN_REN,
    ADD_ZE_REN_REN
  } from '@operation/store/modules/complaint/action-types'

  export default defineComponent({
    props: {
      modalDuty: {
        type: Object,
        default: () => {
          return {
            data: {}
          }
        }
      },
      showDutyModal: {
        type: Boolean,
        default: false
      }
    },
    components: {
      InputPeople
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const visible = computed({
        get: () => props.showDutyModal,
        set: val => proxy.$emit('update:showDutyModal', val)
      })

      const afterClose = function () {
        proxy.$emit('destroyedModal', 'dutyModal')
      }

      // 保存或者修改责任人按钮
      const modalDutyOk = async function () {
        const params = {
          ...props.modalDuty.data,
          tousuId: +proxy.$route.params.id
        }
        if (proxy.$tnt.xtenant < 1000) {
          delete params.tousuRank
        } else {
          delete params.tousuLosePoint
        }
        const res = await proxy.$store.dispatch(
          `operation/complaint/${
            props.modalDuty.data.id ? MODIFY_ZE_REN_REN : ADD_ZE_REN_REN
          }`,
          params
        )
        if (res) {
          message.success('修改成功')
          proxy.$emit('saveSuccess')
          visible.value = false
        }
      }

      return {
        visible,
        afterClose,
        modalDutyOk
      }
    },
    render () {
      const { modalDuty, visible, modalDutyOk, afterClose } = this
      const { title } = modalDuty
      const { touSuRank } = modelEnum
      return (
      <Modal
        title={title}
        visible={visible}
        width="400px"
        onOk={modalDutyOk}
        onCancel={() => {
          this.visible = false
        }}
        after-close={afterClose}
      >
        <div>
          <Row>
            <Col>
              <span class="label">责任人：</span>
              {modalDuty.data.id ? (
                <span>{modalDuty.data.userName}</span>
              ) : (
                <InputPeople
                  style="wdith:100%;"
                  onChange={item => {
                    modalDuty.data.userName = item?.name
                    modalDuty.data.userId = item?.id
                  }}
                  placeholder="请输入姓名或工号"
                  class="grow1"
                  allow-clear
                  v-model={modalDuty.data.userName}
                />
              )}
            </Col>
            <Col>
              <span class="label">{this.$tnt.xtenant < 1000 ? '扣除积分' : '扣分'}：</span>
              <InputNumber
                min={0}
                v-model={modalDuty.data.tousuPoint}
                placeholder="请输入"
                class="grow1"
                style="width: auto"
              ></InputNumber>
            </Col>
            {
              this.$tnt.xtenant < 1000 && this.$store.state.userInfo.Rank?.includes('ksxj') ? <Col>
                <span class="label"> 奖励积分：</span>
                <InputNumber
                  min={1}
                  v-model={modalDuty.data.bonusPoints}
                  placeholder="请输入"
                  class="grow1"
                  style="width: auto"
                ></InputNumber>
              </Col> : null
            }
            {
              this.$tnt.xtenant < 1000 ? <Col>
                <span class="label">扣分：</span>
                <InputNumber
                  min={0}
                  v-model={modalDuty.data.tousuLosePoint}
                  placeholder="请输入"
                  class="grow1"
                  style="width: auto"
                ></InputNumber>
              </Col> : <Col>
                <span class="label">投诉等级：</span>
                <Select
                  v-model={modalDuty.data.tousuRank}
                  class="grow1"
                  allow-clear
                  placeholder="请选择投诉等级"
                  options={touSuRank}
                />
              </Col>
            }

          </Row>
        </div>
      </Modal>
      )
    }
  })
</script>

<style lang="scss" scoped>
:deep(.ant-modal-title) {
  font-weight: 600;
  font-size: 18px;
}
.label {
  white-space: nowrap;
  display: block;
  width: 70px;
  text-align: right;
}

.grow1 {
  flex-grow: 1;
}

.ant-col {
  &-24 {
    align-items: unset;
    > span {
      white-space: nowrap;
    }

    > div {
      flex-grow: 1;
    }
  }

  display: flex;
  align-items: center;
  margin-bottom: 22px;

  > span {
    white-space: nowrap;
  }

  .ant-col {
    > span {
      white-space: nowrap;
      display: block;
      width: 5em;
    }
  }
}
</style>
