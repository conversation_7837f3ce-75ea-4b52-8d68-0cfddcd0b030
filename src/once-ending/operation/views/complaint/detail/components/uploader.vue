<template>
<div>
  <div class="flex flex-col" v-if="showUpload">
    <div class="flex">
      <div class="upload-button">
        <a-button style="background: #E6F3FF" @click="doUpload" :disabled="disabled" :loading="btnLoading">
          <a-icon type="upload"></a-icon>
          {{ buttonName[0] }}
        </a-button>
      </div>
      <div style="color: #239DFC;" v-if="showUploadAPP">
        <a-button style="background: #E6F3FF" @click="showQr()" :disabled="disabled">
          <a-icon type="qrcode"></a-icon>
          {{ buttonName[1] }}
        </a-button>
      </div>
    </div>
    <p class="font-13 mt-5 padding-right" style="color: #828282;line-height: 1.2" v-if="tips">{{tips}}</p>
    <a-modal
      v-model="qrcodeVisible"
      destroyOnClose
      :maskClosable="false"
      @ok="closeQr"
      @cancel="closeQr"
      :footer="null"
    >
      <div class="flex flex-col qr-container">
        <span class="font-16 bold">使用手机上传</span>
        <div class="flex flex-col flex-align-center flex-justify-center">
          <QrCode :value="qrContent" style="width: 160px;margin-top: 44px"></QrCode>
          <span class="mt-8">使用手机OA扫码进入手机上传</span>
        </div>
      </div>
    </a-modal>
  </div>
  <slot name="extra"></slot>
  <div class="preview-container">
    <div v-for="(file, index) in list" :key="index" class="flex flex-align-center file-preview ">
      <a-input v-if="editFileName" v-model="file.name" class="w-400" :disabled="!editName" @change="e=>{onInputChange(e, file)}"></a-input>
      <div v-else class="file-name lines-1" :title="file.fileName">
        <img v-if="showFileIcon" :src="fileIcon[format(file)]" alt="icon" class="file-icon">
        <span :class="showPreview ? '' : 'download-style'" @click="downloadFile(file.filePath)">
          {{ file.fileName }}
        </span>
      </div>
      <file-preview v-if="showPreview" :showAudioPreview="true" :uploadHost="uploadHost" :isdownloadFile="download" :type="format(file)" ref="preview" :file="file" :pathKey="pathKey" />
      <a v-if="deleteItem" class="padding" @click="deleteFile(file,index)">
        <a-icon type="delete"/>
      </a>
      <div v-if="isSign" title="电子签署" class="padding" style="color:rgb(59, 130, 246);cursor:pointer;" @click="signFile(file, index)">
        <a-icon type="safety" />选择签署方
      </div>
    </div>
  </div>

</div>
</template>

<script>
  import { Modal, Upload } from 'ant-design-vue'
  import uuidv4 from 'uuid/v4'
  import api from '~/api'
  import { mapState } from 'vuex'
  import QrCode from '~/components/qrcode'
  import nineUpload from '@jiuji/nine-upload'
  import filePreview from '~/components/uploader/file-preview'
  const Stomp = require('stompjs/lib/stomp.js').Stomp
  const INIT_MAX = 5
  export default {
    name: 'Uploader',
    props: {
      // 是否显示上传按钮
      showUpload: {
        type: Boolean,
        default: true
      },
      tips: {
        type: String,
        default: ''
      },
      // 是否禁用上传按钮
      disabled: {
        type: Boolean,
        default: false
      },
      // 是否展示附件预览
      showPreview: {
        type: Boolean,
        default: true
      },
      // 编辑按钮名字
      buttonName: {
        type: Array,
        default: () => ['添加附件', '手机上传']
      },
      // 是否多选
      multiple: {
        type: Boolean,
        default: true
      },
      // 支持上传多个文件
      moreAmount: {
        type: Boolean,
        default: true
      },
      // 是否展示附件输入框
      editFileName: {
        type: Boolean,
        default: true
      },
      // 附件大小
      addSize: {
        type: Boolean,
        default: false
      },
      // 支持上传多个文件
      moreAmountWarn: {
        type: String,
        default: '最多上传一个文件'
      },
      // 禁用附件名编辑框
      editName: {
        type: Boolean,
        default: true
      },
      // 文件是或否支持下载
      download: {
        type: Boolean,
        default: true
      },
      // 文件是否支持删除
      deleteItem: {
        type: Boolean,
        default: true
      },
      // path的key名
      pathKey: {
        type: String,
        default: 'fileUrl'
      },
      // 上传的列表
      fileList: {
        type: [Array],
        default: () => [],
        validator: (fileList) => {
          let isRight = true
          fileList?.map(item => {
            if (!item.hasOwnProperty('fid') || !item.hasOwnProperty('fileName')) isRight = false
          })
          if (!isRight) console.error('fileList: 参数不正确')
          return isRight
        }
      },
      // 删除的id列表
      delList: {
        type: [Array],
        default: () => []
      },
      // 二维码的地址
      qrCodeUrl: {
        type: String,
        default: ''
      },
      routKey: {
        type: String,
        default: ''
      },
      // 文件上传的类型
      accept: {
        type: String,
        default: 'image/*,.pdf,.pdfx,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt'
      },
      suffixReg: {
        type: String,
        default: ''
      },
      sendMessage: {
        type: Boolean,
        default: false
      },
      // APP 扫码上传
      showUploadAPP: {
        type: Boolean,
        default: true
      },
      // 是否显示 文件图标
      showFileIcon: {
        type: Boolean,
        default: true
      },
      needDuration: {
        type: Boolean,
        default: false
      },
      maxSize: { // nineUpload上传文件大小
        type: Number,
        default: 300 * 1024 * 1024
      },
      uploadHost: { // 强制使用上传host
        type: String,
        default: ''
      },
      isSign: {
        type: Boolean,
        default: false
      },
      collection: {
        type: String,
        default: 'javaweb'
      },
      useExtension: { // 手机上传时fid是否使用后缀
        type: Boolean,
        default: false
      },
      videoUpload: { // 手机上传视频
        type: Boolean,
        default: true
      },
      videoSize: { // 手机上传视频大小，单位M
        type: Number,
        default: 300
      },
      videoDuration: { // 手机上传视频时长，单位S
        type: Number,
        default: 30
      },
      ttl: { // 图片保存时间
        type: Number,
        default: 0
      }
    },
    components: {
      [Upload.name]: Upload,
      [Modal.name]: Modal,
      QrCode,
      filePreview
    },

    data () {
      return {
        list: [],
        qrcodeVisible: false,
        qrContent: '',
        phoneUpload: {
          isInit: false,
          ws: null,
          routKey: '',
          isLog: true,
          client: null,
          appKey: 'oanew',
          appSecret: 'oanew',
          host: 'oa',
          server: this.$tnt.wss,
          upChange: '/exchange/oaupload/',
          initMax: INIT_MAX,
          initTimerId: 0
        },
        // 上传需要的两个变量值
        appId: '',
        token: '',
        btnLoading: false,
        fileIcon: {
          'file-image': require('~/assets/images/uploader-icon/image.png'),
          'file-pdf': require('~/assets/images/uploader-icon/pdf.png'),
          'file-word': require('~/assets/images/uploader-icon/word.png'),
          'file-ppt': require('~/assets/images/uploader-icon/ppt.png'),
          'file-video': require('~/assets/images/uploader-icon/video.png'),
          'file-audio': require('~/assets/images/uploader-icon/audio.png'),
          'file-excel': require('~/assets/images/uploader-icon/excel.png'),
          'file-text': require('~/assets/images/uploader-icon/common.png')
        }
      }
    },
    computed: {
      ...mapState({
        userInfo: state => state.userInfo || {}
      })
    },
    created () {
      this.list = this.fileList.map(item => {
        return {
          ...item,
          name: this.handleSuffix(item.fileName)
        }
      })
      if (this.$tnt.mq && this.$tnt.mq.length) {
        let tagetMQ = this.$tnt.mq.find(m => m.sourceVhost === 'oa')
        if (tagetMQ) {
          this.phoneUpload.host = tagetMQ.vhost
          this.phoneUpload.appKey = tagetMQ.username
          this.phoneUpload.appSecret = tagetMQ.pwd
        }
      }
      const fileMsg = JSON.parse(window.sessionStorage.getItem('fileMsg'))
      if (fileMsg?.appId && fileMsg?.token) {
        this.appId = fileMsg.appId
        this.token = fileMsg.token
      }
    },
    watch: {
      // 监听数组变化和文件修改
      fileList: {
        deep: true,
        handler (newVal) {
          this.list = this.fileList.map(item => {
            return {
              ...item,
              name: this.handleSuffix(item.fileName)
            }
          })
          this.$emit('fileChange', newVal)
        }
      }
    },
    methods: {
      downloadFile (filePath) {
        if (this.showPreview) return
        window.open(`${filePath}?dl=1`)
      },
      handleSuffix (fileName) {
        return fileName?.slice(0, fileName.lastIndexOf('.')) || ''
      },
      onInputChange (e, data) {
        this.$nextTick(() => {
          const value = e.target._value
          const list = data.fileName.split('.')
          data.fileName = `${value}.${list[list.length - 1]}`
          this.$emit('update:fileList', this.list)
        })
      },
      format (file) {
        const regImg = /.(jpg|bmp|gif|ico|pcx|jpeg|tif|png|raw|tga|webp|svg)$/i
        const regPDF = /.(pdf|pdfx)$/i
        const regWord = /.(doc|docx)$/i
        const regPPT = /.(ppt|pptx)$/i
        const regExcel = /.(xls|xlsx)$/i
        const regVideo = /.(avi|rmvb|mpeg|wmf|mov|mkv|mp4)$/i
        const regAudio = /.(mp3|wav|m4a)$/i
        if (regImg.test(file.fileName)) return 'file-image'
        if (regPDF.test(file.fileName)) return 'file-pdf'
        if (regWord.test(file.fileName)) return 'file-word'
        if (regPPT.test(file.fileName)) return 'file-ppt'
        if (regVideo.test(file.fileName)) return 'file-video'
        if (regAudio.test(file.fileName)) return 'file-audio'
        if (regExcel.test(file.fileName)) {
          return 'file-excel'
        } else {
          return 'file-text'
        }
      },
      async getNewFiles (files) { // 获取newFiles
        function getTime (file) { // 获取时间
          const url = URL.createObjectURL(file)
          const audioElement = new Audio(url)
          return new Promise((resolve) => {
            audioElement.addEventListener('loadedmetadata', function () {
              resolve(audioElement.duration || 0)
            })
          })
        }
        try {
          let videoPass = true
          const cacheFileList = []
          for (let i = 0; i < files.length; i++) {
            const file = files[i]
            const fileType = this.format({ fileName: file.name })
            if (fileType === 'file-video') {
              const time = await getTime(file)
              if (parseInt(time) > 30) {
                videoPass = false
              } else {
                cacheFileList.push(file)
              }
            } else {
              cacheFileList.push(file)
            }
          }
          if (!videoPass) {
            this.$message.error('上传视频的时间不能超过30s')
          }
          return cacheFileList
        } catch (err) {
          console.log(err)
        }
      },
      // 上传
      async doUpload () {
        // 只能上传一个文件时
        if (!this.moreAmount && this.list.length) {
          this.$message.warning(this.moreAmountWarn)
          return
        }
        // 上传
        const config = {
          accept: this.accept,
          multiple: this.multiple,
          maxSize: this.maxSize,
          suffixReg: this.suffixReg
        }
        console.log('config', config)
        if (this.uploadHost) config.uploadHost = () => this.uploadHost
        const form = {
          collection: this.collection
        }
        if (this.ttl) form.ttl = this.ttl
        console.log('ttl123', this.ttl)
        nineUpload({
          ...config,
          onPickFiles: async files => { // files是文件对象，如果要对文件进行处理，处理完成之后要在返回对象里加上files对象
            // this.$emit('setLoading', true)
            this.$indicator.open()
            this.files = files
            try {
              const newFiles = await this.getNewFiles(files)
              if (newFiles.length === 0) {
                this.$indicator.close()
                return {
                  appId: '',
                  token: '',
                  files: []
                }
              }
              if (window.nineUploadData) {
                return {
                  ...window.nineUploadData,
                  files: newFiles
                }
              }
              // 获取appId和token。可以把这个appId和token缓存起来，没必要每次都请求
              const {
                code = 0,
                userMsg = '',
                data = {
                  appId: '',
                  token: ''
                }
              } = await this.$api.common.getUploadToken()
              if (code === 0) {
                window.nineUploadData = data
                setTimeout(() => { // appId和token30分钟过期，要清理一下
                  window.nineUploadData = null
                }, 30 * 60 * 1000)
                console.log('data', data)
                return {
                  ...data,
                  files: newFiles
                }
              } else {
                this.$indicator.close()
                // this.$emit('setLoading', false)
                this.$message.error(userMsg)
              }
            } catch (e) {
              this.$indicator.close()
              this.$message.error(e)
            }
          },
          onProgress: ({
            percent,
            fileIndex,
            fileCount
          }) => {
            this.percent = percent
            this.fileIndex = fileIndex
            this.fileCount = fileCount
          },
          form
        }).then(({
          res,
          err
        }) => {
          console.log('res', res, err)
          if (!this.multiple) {
            err = err ? [err] : []
            res = res ? [res] : []
          }
          if (err && err?.length) {
            err?.map(i => {
              this.$message.error(`${i.name}上传失败,${i.err.message}`)
            })
            this.$indicator.close()
            return
          }
          res = res?.map(item => {
            let i = {
              fid: item.fid,
              fileName: item.fileName || item.filename,
              name: item.fileName ? this.handleSuffix(item.fileName) : this.handleSuffix(item.filename),
              fileSize: item.size
            }
            if (this.addSize) {
              i.size = item.size
            }
            if (item.playPath && item.playPath.length) { // 视频
              i[this.pathKey] = item.downloadPath
              i.framePath = item.framePath
              if (this.needDuration) {
                const fileObj = URL.createObjectURL(this.files[0])
                const audio = new Audio(fileObj)
                audio.addEventListener('loadedmetadata', () => {
                  const duration = audio.duration
                  i.duration = duration
                  URL.revokeObjectURL(fileObj)
                })
              }
            } else {
              // 上传成功后的url根据你的pathKey给你返回
              i[this.pathKey] = item.fileUrl
            }
            return i
          }) || []
          if (res.length) {
            let filelist = [...this.list]
            filelist = [...filelist, ...res]
            this.$emit('update:fileList', filelist)
            this.$emit('change', filelist, this.files)
          }
        }).finally(() => {
          this.$indicator.close()
        })
      },
      // 删除
      deleteFile (item, index) {
        // 单项数据流，防止数据混乱和多次触发change 事件
        let newFileList = Object.assign([], this.list)
        newFileList.splice(index, 1)
        this.$emit('update:fileList', newFileList)
        this.$emit('change', newFileList)
        // 有id表示在数据库中存储（需要回传id进行删除），没有表示本地上传，只上传到了文件服务器中，没有上传到你的模块数据库中
        if (item.id) {
          // 有id就让后端调文件服务器的删除接口
          let delList = Object.assign([], this.delList)
          delList.push(item.id)
          delList = [...new Set(delList)]
          this.$emit('update:delList', delList)
          this.$emit('delete', item, index)
        } else {
          // 接文件服务器的删除接口
        }
      },
      signFile (item, index) {
        this.$emit('signfile', index, item)
      },
      initQr () {
        this.phoneUpload.routKey = this.routKey || this.phoneUpload.routKey || uuidv4() // 复用routKey
        this.phoneUpload.isLog = false

        let url = this.qrCodeUrl || this.qrContent || `${this.$tnt.mHost}/up.aspx?id=${this.phoneUpload.routKey}&p=honorApply${this.videoUpload ? `&videoUpload=true&videoSize=${this.videoSize}&videoDuration=${this.videoDuration}` : ''}` // 复用链接
        this.qrContent = url
      },
      showQr () {
        this.init()
      },
      closeQr () {
        this.destroyClient()
        this.qrcodeVisible = false
      },
      pushGateway () {
        let params = {
          appName: 'oa',
          title: '上传图片',
          content: '请打开oa上传图片',
          extra: JSON.stringify({
            type: 8,
            isAuto: true,
            url: this.qrContent
          }),
          isTest: false,
          alias: ['staff_' + this.userInfo.UserID]
        }
        api.common.pushGateway(params).then(() => {
        }).finally(() => {
        })
      },
      init () {
        let {
          appKey,
          appSecret,
          host,
          server,
        } = this.phoneUpload
        // 先销毁
        this.destroyClient()
        this.initQr()
        // 再创建
        this.phoneUpload.ws = new WebSocket(server)
        this.phoneUpload.client = Stomp.over(this.phoneUpload.ws)
        this.phoneUpload.client.connect(appKey, appSecret, this.connectCallback, this.errorCallback, host)
        this.phoneUpload.isInit = true
      },
      destroyClient () {
        clearInterval(this.phoneUpload?.initTimerId)
        if (this.phoneUpload?.client) {
          this.phoneUpload.client.disconnect(() => {
            this.phoneUpload.isInit = false
            this.phoneUpload.client = null
            this.phoneUpload.ws = null
          })
        }
      },
      connectCallback () {
        let {
          upChange,
          routKey,
          isLog
        } = this.phoneUpload
        this.phoneUpload.client.subscribe(upChange + routKey, (d) => {
          if (d.body !== 'Heartbeat') {
            if (isLog) {
            }
            this.msgAction(d.body)
          }
        })
        // 连接成功后再显示二维码
        this.qrcodeVisible = true
        // 连接成功回调后再去推送
        if (this.sendMessage && this.$tnt.xtenant.toString() === '0' && this.phoneUpload.initMax === INIT_MAX) { // 九机需要消息推送的时候推送消息
          this.pushGateway()
        }
        // 连接成功后再监听失败
        this.phoneUpload.initTimerId = setInterval(() => {
          this.listenerDisconnect()
        }, 1000)
      },
      errorCallback (e) {
        console.warn(e)
        this.$message.error('websocket连接失败')
      },
      msgAction (result) {
        let list = JSON.parse(result)
        list = list.map(pic => {
          const { fid, Extension } = pic
          let item = {
            fid: fid?.indexOf('.') > -1 ? fid : fid + Extension,
            fileName: pic.filename,
            name: this.handleSuffix(pic.filename)
          }
          if (pic.playPath && pic.playPath.length) { // 视频
            item[this.pathKey] = pic.downloadPath
            item.framePath = pic.framePath
          } else {
            item[this.pathKey] = pic.filepath
          }
          return Object.assign({}, item)
        })
        this.$emit('update:fileList', [...this.fileList, ...list])
        this.$emit('change', [...this.fileList, ...list])
        this.closeQr()
      },
      listenerDisconnect () {
        if ((this.phoneUpload.client && this.phoneUpload.client.connected) || this.phoneUpload.initMax <= 0) {
          // 正常连接中 或者 重试超过5次
        } else {
          this.phoneUpload.isInit = false
          this.init()
          this.phoneUpload.initMax--
        }
      },
    },
    beforeDestroy () {
      this.destroyClient()
    }
  }
</script>

<style lang="scss" scoped>
.preview-container {
  :deep(.ant-input) {
    position: static;
  }
}
.preview {
  color:#1890ff;
  margin: .5em .5em;
}
.preview-icon {
  color:#1890ff;
  margin:0em .5em;
}
.file-icon {
  width: 20px;
  height: 20px;
  margin-right: .3em
}
.w-400 {
  width: 400px;
}
.qr-container {
  width: 470px;
  height: 317px;
}
.upload-button {
  margin-right:1em;
  color: #239DFC
}

.qr-btn {
  margin-left: -240px;
}

.upload-btn {
  width: 350px;
}

:deep(.ant-btn) {
  border: none;
  color: #239DFC;
}
.file-preview {
  margin-bottom: .5em;
  line-height: 20px;
}
.file-name {
  width: 170px;
  margin-right: .4em;
  color: #333333;

  .download-style {
    color: #1890ff;
    cursor: pointer;
  }
}
</style>
