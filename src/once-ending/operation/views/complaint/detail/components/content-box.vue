<script lang="jsx">
  import { defineComponent } from 'vue'

  export default defineComponent({
    props: {
      title: {
        type: String,
      },
      icon: {}
    },
    render () {
      const { title, icon } = this
      return <div class="content-box">
        <div class="title">
          <ni-img src={icon} class="title-icon"/>
          {
            title ? <div class="title-text">{title}</div> : null
          }
          <div class="title-text">{ this.$slots.title }</div>
        </div>
        <div class="content">
          { this.$slots.default }
        </div>
      </div>
    }
  })
</script>

<style scoped lang="scss">
.content-box {
  margin-top: 24px;
}
.title {
  display: flex;
  align-items: center;
  height: 60px;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 8px;
  padding: 0 20px;
}
.title-icon {
  width: 20px;
  height: 20px;
}
.title-text {
  font-weight: 600;
  font-size: 16px;
  margin-left: 10px;
}
.content {
  padding: 18px 20px 0 20px;
}
</style>
