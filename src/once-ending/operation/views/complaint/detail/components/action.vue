<script lang="jsx">
  import { defineComponent, getCurrentInstance, inject } from 'vue'
  import { NiImg } from '@jiuji/nine-ui'
  import praiseBig from '@/operation/components/images/toLikeBig.png'
  import isPraiseBig from '@/operation/components/images/isLike.png'
  import trampleBig from '@/operation/components/images/toTrampleBig.png'
  import isTrampleBig from '@/operation/components/images/isTrample.png'
  import complaintApi from '@/operation/api/complaint'

  export default defineComponent({
    components: {
      NiImg
    },
    props: {
      touSuInfo: {
        type: Object,
        default: () => ({})
      },
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const commentList = inject('commentList')
      const id = inject('id')

      function goComment () {
        const box = document.getElementById('commentBoxId')
        box && (box.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        }))
      }

      function likeOrDislike (type) {
        const statusKey = type + 'Status'
        const flagKey = type + 'Flag'
        const params = {
          id,
          type,
          refType: 16,
        }
        params[flagKey] = props.touSuInfo[statusKey] ? 0 : 1
        complaintApi.likeOrDislike(params).then((res) => {
          if (res.code === 0) {
            proxy.$emit('getTouSuInfo')
          } else {
            proxy.$message.error(res.userMsg)
          }
        })
      }
      return {
        commentList,
        goComment,
        likeOrDislike
      }
    },
    render () {
      const {
        commentList,
        touSuInfo,
        goComment,
        likeOrDislike
      } = this
      return <div class="action-box">
        <div class="item" onClick={() => likeOrDislike('like')}>
          <NiImg class="icon" src={touSuInfo?.likeStatus ? isPraiseBig : praiseBig}/>
          <span>{ touSuInfo?.likeCount || '赞一个' }</span>
        </div>
        <div class="item" onClick={() => likeOrDislike('dislike')}>
          <NiImg class="icon" src={touSuInfo?.dislikeStatus ? isTrampleBig : trampleBig} onClick={() => likeOrDislike('dislike')}/>
          <span>{ touSuInfo?.dislikeCount || '踩一个' }</span>
        </div>
      </div>
    }
  })
</script>

<style scoped lang="scss">
.action-box {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40px;
  padding-bottom: 16px;
  .icon {
    width: 15px;
    height: 14px;
    margin-right: 2px;
  }
  .item {
    width: 88px;
    height: 32px;
    border-radius: 20px;
    border: 1px solid #CCCCCC;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
    font-size: 12px;
    cursor: pointer;
    &:first-child {
      margin-left: 0;
    }
  }
}
</style>
