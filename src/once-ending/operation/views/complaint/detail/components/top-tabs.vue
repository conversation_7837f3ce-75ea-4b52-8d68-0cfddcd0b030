<template>
  <div :class="['tabs-box']" id="topTabsId">
    <div v-for="(it, index) in tabs"
         :key="index"
         @click="() => changeTab(it)"
         :class="['tab', currentTab === it.value ? 'tab-check' : '']">
      {{it.label}}
    </div>
  </div>
</template>
<script>
  import { defineComponent, onBeforeUnmount, onMounted, ref, nextTick, computed, inject } from 'vue'
  const isJiuJi = window.tenant.xtenant < 1000
  const originTabs = [
    { label: '会员信息', value: 1, domId: 'memberId', top: 68 },
    { label: '投诉信息', value: 2, domId: 'detailId' },
    { label: '投诉跟进', value: 3, domId: 'processId' },
    { label: '投诉界定', value: 4, domId: 'demarcationId', show: isJiuJi },
    { label: '投诉处理结果', value: 5, domId: 'resultId' },
  ]
  export default defineComponent({
    components: {
    },
    created () {
    },
    setup (props) {
      const currentTab = ref(1)
      const fixed = ref(false)
      const showDemarcation = inject('showDemarcation')

      const tabs = computed(() => {
        return originTabs.filter(it => (it.value !== 4 || showDemarcation.value) && (it.hasOwnProperty('show') ? it.show : true))
      })

      const needListener = ref(true)
      function changeTab (tabItem) {
        needListener.value = false
        currentTab.value = tabItem.value
        let top = 96
        if (overWrap.value) {
          for (let i = 0; i < tabs.value.length; i++) {
            if (tabs.value[i].value === tabItem.value) break
            const height = document.getElementById(tabs.value[i].domId).offsetHeight
            top += (height + 23)
          }
          overWrap.value.scrollTo({
            top: top,
            behavior: 'smooth' // 平滑滚动
          })
        }
      }

      let timer
      function scrollEventHandler () {
        clearTimeout(timer)
        if (needListener.value) {
          let activeNav = 1
          tabs.value.forEach(tabItem => {
            if (!tabItem.domId) return
            const dom = document.getElementById(tabItem.domId)
            if (dom && dom.getBoundingClientRect().top <= 176) {
              activeNav = tabItem.value
            }
          })
          currentTab.value = activeNav
        }
        timer = setTimeout(() => {
          needListener.value = true
          timer && (clearTimeout(timer))
        }, 200)
      }

      const hasScrollbar = inject('hasScrollbar')
      function checkScrollbar () {
        if (!overWrap.value) return
        hasScrollbar.value = overWrap.value.scrollWidth > overWrap.value.clientWidth
      }

      const overWrap = ref()
      onMounted(() => {
        const overWrapBox = document.getElementById('overWrap')
        window.addEventListener('resize', checkScrollbar) // 响应窗口大小变化
        if (overWrapBox) {
          overWrap.value = overWrapBox
          overWrap.value.addEventListener('scroll', scrollEventHandler)
          let timer = setTimeout(() => {
            checkScrollbar()
            clearTimeout(timer)
          }, 1000)
        }
      })

      onBeforeUnmount(() => {
        overWrap.value && (overWrap.value.removeEventListener('scroll', scrollEventHandler))
        window.removeEventListener('resize', checkScrollbar) // 清理事件监听器
      })
      return {
        tabs,
        changeTab,
        currentTab,
        fixed
      }
    }
  })
</script>

<style scoped lang="scss">
.tabs-box {
  background: #fff;
  display: flex;
  z-index: 99;
  padding: 0 20px;
  width: 1108px;
  box-sizing: border-box;
  border-radius: 8px 8px 0 0;
  position: sticky !important;
  top: 0
}
.tab {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 50px;
  box-sizing: border-box;
  padding: 0 10px;
  font-size: 15px;
  position: relative;
  height: 60px;
  margin-right: 48px;
  cursor: pointer;
  background: #fff;
  &:after {
    content: '';
    width: 100%;
    max-width: 100px;
    height: 2px;
    background: transparent;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
  }
}
.tab-check {
  font-size: 16px;
  color: #1890FF;
  font-weight: 600;
  padding: 0 18px;
  &:after {
    background: #1890FF;
  }
}
</style>
