<script lang="jsx">
  import { defineComponent, computed, getCurrentInstance } from 'vue'
  import {
    Row,
    Col,
    Modal,
    Radio,
    Select,
    InputNumber,
    message
  } from 'ant-design-vue'
  import AreaSelector from '../../components/area-select'
  import Depart from '~/components/staff/area-depart-selector'
  import Reason from '../../components/reason-select'
  import InputPeople from '~/components/staff/staff-input'
  import { modelEnum } from '../constants'
  import {
    MODIFY_TOU_SU_DEPART,
    ADD_TOU_SU_DEPART
  } from '@operation/store/modules/complaint/action-types'

  export default defineComponent({
    props: {
      modalDepart: {
        type: Object,
        default: () => ({})
      },
      showDepartModal: {
        type: Boolean,
        default: false
      }
    },
    components: {
      AreaSelector,
      Depart,
      Reason,
      InputPeople
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const visible = computed({
        get: () => props.showDepartModal,
        set: val => proxy.$emit('update:showDepartModal', val)
      })

      const afterClose = function () {
        proxy.$emit('destroyedModal', 'departModal')
      }

      // 保存责任规划按钮
      const modalDepartOk = async function () {
        const {
          areaId,
          departArea,
          departCode,
          score,
          kinds,
          cat,
          touSuRank,
          ...other
        } = props.modalDepart.data
        const params = {
          tousuId: +proxy.$route.params.id,
          type: props.modalDepart.type,
          ...other
        }
        if (params.type === 0) {
          params.areaId = areaId
          params.scoreArea = score
          if (!areaId) return message.info('选择投诉门店')
        } else if (params.type === 1) {
          params.departId = departCode
          params.scoreDep = score
          if (!departCode) return message.info('选择投诉部门')
        } else {
          return message.info('请选择门店或部门')
        }
        // --- 默认为无 ---
        params.kinds = kinds || null
        params.cat = cat || null
        params.touSuRank = touSuRank || null
        // --- / 默认为无 ---
        const res = await proxy.$store.dispatch(
          `operation/complaint/${
            params.id ? MODIFY_TOU_SU_DEPART : ADD_TOU_SU_DEPART
          }`,
          params
        )
        if (res) {
          visible.value = false
          message.success(res.userMsg)
          proxy.$emit('getDetail')
          proxy.$emit('getInfo')
        }
      }

      return {
        visible,
        afterClose,
        modalDepartOk
      }
    },
    render () {
      const { modalDepart, visible, modalDepartOk, afterClose } = this
      const { title } = modalDepart
      const { kinds, cat, touSuRank } = modelEnum
      return (
      <Modal
        title={title}
        visible={visible}
        mask-closable={false}
        width="760px"
        onOk={modalDepartOk}
        onCancel={() => {
          this.visible = false
        }}
        after-close={afterClose}
      >
        <div class="model">
          <Row gutter={[24, 16]}>
            <Col span={24}>
              <span class="label">投诉：</span>
              <Radio.Group
                v-model={modalDepart.type}
                name="radioGroup"
                default-value={1}
              >
                <Radio value={0} class="ml-10">
                  <span>门店</span>
                </Radio>
                <Radio value={1} class="ml-4">
                  <span>部门</span>
                </Radio>
                <AreaSelector
                  selected-last-node
                  ref="area"
                  class="ml-4"
                  v-show={modalDepart.type === 0}
                  style="width:488px;"
                  allow-clear
                  dropdown-style={{ maxHeight: '488px' }}
                  onChange={code => {
                    modalDepart.data.areaId = code
                  }}
                  selected={modalDepart.data.areaId}
                  type="area"
                  placeholder="请选择地区"
                />
                <AreaSelector
                  selected-last-node
                  ref="depart"
                  class="ml-4"
                  can-select-all-tree={true}
                  v-show={modalDepart.type === 1}
                  style="width:488px;"
                  allow-clear
                  dropdown-style={{ height: '488px' }}
                  onChange={code => {
                    modalDepart.data.departCode = code
                  }}
                  selected={modalDepart.data.departCode}
                  type="department"
                  placeholder="请选择部门"
                />
              </Radio.Group>
            </Col>
            <Col span={12}>
              <span class="label">扣分：</span>
              <div class="flex flex-align-center grow1 relative">
                <InputNumber
                  min={0}
                  v-model={modalDepart.data.score}
                  placeholder="请输入分数"
                  class="grow1"
                  style="width: auto"
                ></InputNumber>
                <span class="unit">分</span>
              </div>
            </Col>
            <Col span={12}>
              <span class="label">投诉分类：</span>
              <Select
                v-model={modalDepart.data.kinds}
                class="grow1"
                allow-clear
                placeholder="请选择投诉分类"
                options={kinds}
              />
            </Col>
            <Col span={12}>
              <span class="label">定性分类：</span>
              <Select
                v-model={modalDepart.data.cat}
                class="grow1"
                allow-clear
                placeholder="请选择定性分类"
                options={cat}
              />
            </Col>
            <Col span={12}>
              <span class="label">投诉等级：</span>
              <Select
                v-model={modalDepart.data.touSuRank}
                class="grow1"
                allow-clear
                placeholder="请选择投诉等级"
                options={touSuRank}
              />
            </Col>
            {
              this.$tnt.xtenant < 1000 ? <Col span={12}>
                <span class="label">扣除积分：</span>
                <InputNumber
                  min={1}
                  step={1}
                  precision={0}
                  v-model={modalDepart.data.tousuPoint}
                  placeholder="请输入扣除积分"
                  class="grow1"
                  style="width: auto"
                ></InputNumber>
              </Col> : null
            }
            <Col span="24">
              <span class="label">投诉原因：</span>
              <Reason
                onChange={list => {
                  modalDepart.data.categoryIds = list
                }}
                v-model={modalDepart.data.categoryIds}
              />
            </Col>
          </Row>
        </div>
      </Modal>
      )
    }
  })
</script>

<style lang="scss" scoped>
:deep(.ant-modal-title) {
  font-weight: 600;
  font-size: 18px;
}
.label {
  white-space: nowrap;
  display: flex;
  width: 70px;
  text-align: right;
  align-items: center;
  justify-content: end;
  max-height: 32px;
}
.ml-4 {
  margin-left: 4px;
}
.grow1 {
  flex-grow: 1;
}
:deep(.ant-input-number-input) {
  padding: 0 40px 0 11px;
}
.unit {
  position: absolute;
  right: 24px;
}
.ant-col {
  &-24 {
    align-items: unset;
    > span {
      white-space: nowrap;
    }

    > div {
      flex-grow: 1;
    }
  }

  display: flex;
  align-items: center;
  margin-bottom: 0.5em;

  > span {
    white-space: nowrap;
  }

  .ant-col {
    > span {
      white-space: nowrap;
      display: block;
      width: 5em;
    }
  }
}
</style>
