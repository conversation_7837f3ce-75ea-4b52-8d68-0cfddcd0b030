<script lang="jsx">
  import { defineComponent, ref, watch, getCurrentInstance, inject, computed } from 'vue'
  import {
    Radio,
    Checkbox,
    Row,
    Col,
    Input,
    DatePicker,
    TimePicker,
    InputNumber,
    Button,
    message
  } from 'ant-design-vue'
  import Uploader from './uploader'
  import InputPeople from '~/components/staff/staff-input'
  import moment from 'moment'
  import { stateMap, buttonsMap } from '../constants'
  import platform from '~/util/platform'
  import {
    TOU_SU_PROCESS_DEAL,
    SEND_MA
  } from '@operation/store/modules/complaint/action-types'
  import complaint from '@operation/api/complaint'
  import { to } from '@common/utils/common'
  import cloneDeep from 'lodash/cloneDeep'
  import { NiStaffSelect } from '@jiuji/nine-ui'

  export default defineComponent({
    props: {
      info: {
        type: Object,
        default: () => ({ states: 0 })
      },
      memberInfo: {
        type: Object,
        default: () => ({})
      }
    },
    components: {
      Uploader,
      InputPeople
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const getMemberInfo = inject('getMemberInfo')
      const time = ref(null)

      const showMa = ref(false)

      const wx = ref(false)

      const userId = ref('')

      const mobile = ref('')

      const ranks = computed(() => {
        return proxy.$store.state.userInfo.Rank || []
      })

      const hasKswc = computed(() => {
        return ranks.value.includes('kswc')
      })

      const hasKsxj = computed(() => {
        return ranks.value.includes('ksxj')
      })

      const originForm = ref({
        notice: false, // 是否通知
        // cate: 0, // 类型
        isShow: false, // 展示
        dsc: '', // 备注
        endTime: null, // 截至跟进
        weixin: false, // 微信
        tongzhi: false, // 内部
        duanxin: true, // 短信
        attachFiles: [], // 文件
        noticeUsers: []
      })
      const form = ref(cloneDeep(originForm.value))

      const maForm = ref({
        duanxin: false,
        price: '',
        time: null,
        tousuId: '',
        appMsg: true
      })

      const wxForm = ref({
        duanxin: false,
        price: undefined,
        appMsg: true
      })

      const customerEndTime = ref(false)

      const loading = ref(false)

      watch(
        () => props.memberInfo,
        val => {
          userId.value = val.userId
          mobile.value = val.memberMobile
          customerEndTime.value = !!val.customerEndTime
        },
        {
          deep: true
        }
      )

      // watch(
      //   () => props.info.states,
      //   val => {
      //     if (val === 3) {
      //       form.value.cate = 3
      //     }
      //   },
      //   {
      //     deep: true
      //   }
      // )

      const disabledDate = function (current) {
        return current && current < moment().subtract(1, 'days')
      }

      const filess = inject('filess')
      const upLoaderChange = function (files) {
        form.value.attachFiles = files
        filess.value = files
      }
      function getPlatForm () {
        const platformArray = [
          { key: 'android', label: 'Android' },
          { key: 'ios', label: 'iOS' },
          { key: 'pc', label: 'PC_P' },
        ]
        const nowFlatform = platformArray.find(it => platform[it.key])?.label
        return nowFlatform + '/' + (platform.osVersion || '1.0.0')
      }
      const type = inject('type')
      const tousuProcessDeal = async function (states, buttonName) {
        let cate = type.value
        if (proxy.$tnt.xtenant < 1000) {
          // 在【已还原、已处理、已界定、待整改、已整改、待复核、待仲裁、已完成】状态下，添加处理进程时，无论添加时切换到哪个tab下，添加的所有内容都是归属到【投诉处理】tab下；（除了特殊的整改完毕、事件还原）
          if ([7, 5, 8, 4, 6, 9, 10, 3].includes(props.info.states) && ![6, 7].includes(states)) {
            cate = 9
          } else if (states === 7) { // 添加进程内容时，若点击的是【事件还原】，无论是切换到哪个tab下填写的，事件还原的进程内容都是归属到【投诉还原】tab下；
            cate = 8
            // 在【已还原】状态下，处理进程这里增加一个【事件还原】按钮，添加进程时若点击的是这个按钮，进程内容归属到【投诉还原】tab下；
            // 在【已处理】状态下，处理进程这里增加【事件还原、已处理】按钮，添加进程时若点击的是【事件还原】，进程内容归属到【投诉还原】tab下；
          } else if ([5, 7].includes(props.info.states) && states === 7) {
            cate = 8
            // 添加进程内容时，若点击的是【整改完毕】，无论是切换到哪个tab下填写的，整改完毕的进程内容都是归属到【反思整改】tab下；
          } else if (states === 6) {
            cate = 10
          } else {
            cate = type.value
          }
        }
        const params = {
          ...form.value,
          oldStates: props.info.states,
          states,
          tsId: proxy.$route.params.id,
          opUser: proxy.$store.state.userInfo.UserName,
          buttonName,
          cate
        }
        if (!params.dsc) return message.info('输入备注')
        console.log(props.info.tag)
        if (proxy.$tnt.xtenant < 1000 && props.info.tag === 1 && buttonName.includes('000') && !props.info.dealUsers) return message.warn('请先填写投诉处理人保存后再完结客诉')
        if (params.notice) {
          if (!params.noticeUsers.length) return message.info('请选择接收人')
        } else {
          params.endTime = null
        }
        const files = params.attachFiles.map(d => {
          const fidIndex = d.fid.lastIndexOf('.')
          const cache = {
            fid: fidIndex > -1 ? d.fid.substring(0, fidIndex) : d.fid,
            filename: d.fileName,
            filePath: d.fileUrl
          }
          d.framePath && (cache.framePath = d.framePath)
          return cache
        })
        delete params.attachFiles
        params.files = files.length ? files : ''
        params.platform = getPlatForm()
        loading.value = true
        const res = await proxy.$store.dispatch(
          `operation/complaint/${TOU_SU_PROCESS_DEAL}`,
          params
        )
        if (res) {
          message.success('处理成功')
          form.value = cloneDeep(originForm.value)
          loading.value = false
          const timeId = setTimeout(() => {
            proxy.$emit('doRefresh')
            states === 11 && (getMemberInfo())
            clearTimeout(timeId)
          }, 1000)
        } else {
          loading.value = false
        }
      }

      const sendMa = async function () {
        const { price, time, duanxin, appMsg } = maForm.value
        if (!price) return message.info('输入金额')
        if (!time) return message.info('选择日期')
        if (!(duanxin || appMsg)) {
          return message.info('选择通知方式')
        }
        send(maForm.value, 1)
      }

      const sendWx = async function () {
        const { price, duanxin, appMsg } = wxForm.value
        if (!price) return message.info('输入金额')
        if (price - 500 > 0) return message.info('金额不能大不500')
        if (!(duanxin || appMsg)) {
          return message.info('选择通知方式')
        }
        send(wxForm.value, 2)
      }

      const send = async function (params, type) {
        const res = await proxy.$store.dispatch(
          `operation/complaint/${SEND_MA}`,
          {
            ...params,
            type,
            tousuId: proxy.$route.params.id,
            userId: userId.value,
            mobile: mobile.value
          }
        )
        if (res) {
          message.success('发送成功')
          const timeId = setTimeout(() => {
            proxy.$router.go(0)
            clearTimeout(timeId)
          }, 1000)
        }
      }

      const peopleChange = function (value, outputObject) {
        form.value.noticeUsers = outputObject.map(d => {
          return {
            noticeUser: d.staffName,
            ch999Id: d.staffId
          }
        })
      }

      const beforeUpload = function (file, fileList) {
        const reg = /\.(png|jpg|gif|jpeg|webp|mp4|avi|rmvb|mpeg|wmf|mov|mkv|)$/
        if (!reg.test(file.name)) {
          const index = fileList.findIndex(d => d.uid === file.uid)
          fileList.splice(index, 1)
          message.warning('附件只支持上传音视频及图片类型文件')
          return false
        }
      }

      return {
        time,
        showMa,
        form,
        customerEndTime,
        loading,
        maForm,
        disabledDate,
        upLoaderChange,
        tousuProcessDeal,
        sendMa,
        peopleChange,
        beforeUpload,
        hasKswc,
        hasKsxj,
        wxForm,
        wx,
        sendWx
      }
    },
    render () {
      const {
        info,
        form,
        peopleChange,
        disabledDate,
        upLoaderChange,
        maForm,
        sendMa,
        showMa,
        loading,
        tousuProcessDeal,
        customerEndTime,
        hasKswc,
        hasKsxj,
        wxForm,
        sendWx,
        wx
      } = this

      const renderButtons = () => {
        const states = info.states && stateMap.get(info.states) ? JSON.parse(JSON.stringify(stateMap.get(info.states))) : []

        if (customerEndTime) {
          const index = states.findIndex(
            d => d === 2
          )
          states.splice(index, 1)
        }
        if (!hasKswc && this.$tnt.xtenant === 0) {
          const index = states.findIndex(d => d === 3)
          if (index !== -1)states.splice(index, 1)
        }
        const buttons = states.map(d => {
          const button = buttonsMap.get(d)
          return (
          <Button
            style="margin-right: 10px;"
            loading={loading}
            type="primary"
            onClick={() => {
              tousuProcessDeal(button.toState, button.toButtonName)
            }}
          >
            {button.buttonName}
          </Button>
          )
        })
        if (info.states !== 1) {
          buttons.push(
          <Button
            style="margin-right: 10px;"
            loading={loading}
            type="primary"
            onClick={() => {
              tousuProcessDeal(null, '添加进程')
            }}
          >
            添加进程
          </Button>
          )
        }

        return buttons
      }
      return (
      <div class="mt-24">
        <span class="bold">添加处理进程：</span>
        <div>
          <Row class="mb-15">
            <Col span={24} class="mt-8">
              <Input.TextArea
                style="min-height:94px"
                v-model={form.dsc}
                placeholder="请输入..."
              />
            </Col>
          </Row>
          <Row style="margin-bottom: 14px">
            <Col style="align-items: start">
              <div class="bold mt-8">添加附件：</div>
              <Uploader
                editFileName={false}
                showFileIcon={false}
                fileList={form.attachFiles}
                {...{ on: { 'update:fileList': upLoaderChange } }}
                useExtension={true}
                accept={`image/*,.avi,.rmvb,.mpeg,.wmf,.mov,.mkv,.mp4${this.$tnt.xtenant < 1000 ? ',.mp3,.wav,.m4a' : ''}`}
              />
            </Col>
          </Row>
          <Row>
            <Col span={24} class="flex flex-align-center flex-wrap">
              <div class="mt-10">{renderButtons().map(d => d)}</div>
              <Checkbox v-model={form.isShow} class="mt-10">
                会员可见
              </Checkbox>
              <span class="ml-16 mt-10">
                <Checkbox v-model={form.notice} class="red">
                  通知
                </Checkbox>
              </span>
              {form.notice ? (
                <div class="mt-10" style="flex: 1;display: flex;">
                  <div class="flex flex-col-center ml-16">
                    <span>接收人：</span>
                    <NiStaffSelect maxTagCount={1} value={form.noticeUsers.map(d => d.ch999Id)} onChange={peopleChange} multiple style="min-width:174px;" placeholder="输入工号或姓名" class="grow1" allow-clear/>
                  </div>
                  <div class="flex flex-col-center" style="margin-left:16px;">
                    <span>跟进截止时间：</span>
                    <DatePicker
                      format="YYYY-MM-DD HH:mm"
                      value-format="YYYY-MM-DD HH:mm"
                      show-time={{ format: 'HH:mm' }}
                      disabled-date={disabledDate}
                      v-model={form.endTime}
                      style="height:30px;"
                    />
                  </div>
                </div>
              ) : null}
            </Col>
          </Row>
        </div>
      </div>
      )
    }
  })
</script>

<style lang="scss" scoped>
@import "../common.scss";
:deep(.ant-checkbox + span) {
  padding: 0 0 0 3px;
}
:deep(.file-preview) {
  margin-top: 12px;
  margin-bottom: 0;
}
.flex {
  display: flex;
  > span {
    white-space: nowrap;
  }
}

.flex-col-center {
  align-items: center;
}

.flex-row-center {
  justify-content: center;
}
.red {
  color: #f56c6c;
}
</style>
