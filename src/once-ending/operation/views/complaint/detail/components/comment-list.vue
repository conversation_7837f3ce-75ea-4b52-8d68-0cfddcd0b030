<script lang="jsx">
  import { defineComponent, ref, getCurrentInstance } from 'vue'
  import JTextarea from './j-textarea.vue'
  import CollapseBox from './collapse-box.vue'
  import del from '../images/del.png'
  import showIcon from '../images/show.png'
  import hideIcon from '../images/hide.png'
  import editIcon from '../images/edit.png'
  import isPraise from '../images/is-praise.png'
  import praise from '../images/praise.png'
  import isTrample from '../images/is-trample.png'
  import trample from '../images/trample.png'
  import comment from '../images/comment.png'
  import filePreview from '~/components/uploader/file-preview'
  import { cloneDeep } from 'lodash'
  export default defineComponent({
    components: { JTextarea, CollapseBox, filePreview },
    props: {
      showTextarea: {
        type: Boolean,
        default: true
      },
      data: {
        type: Array,
        default: () => []
      },
      refType: {
        type: Number,
        default: 18
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const placeholder = ref('添加评论')
      const showEdit = ref(false)
      const commentContent = ref('')
      const currentItem = ref(undefined)
      const JTextareaRef = ref()

      function changeShowComment (item) {
        const val = !item.showComment
        placeholder.value = '回复' + item.user?.userName
        currentItem.value = item
        function close (arr) {
          if (Array.isArray(arr)) {
            arr.forEach(it => {
              it.showComment = false
              it.children?.length && (close(it.children))
            })
          }
        }
        close(props.data)
        item.showComment = val
        console.log('item.showComment', item)
      }

      function close () {
        const keyRef = 'JTextareaRef' + currentItem.value?.id
        proxy.$refs.JTextareaRef?.close()
        proxy.$refs[keyRef]?.close()
        showEdit.value = false
      }

      function toComment (params, commentType) {
        proxy.$emit('toComment', {
          ...params,
          replyId: commentType === 'out' ? undefined : currentItem.value?.id || undefined,
          refType: props.refType,
        })
      }
      function editComment (row) {
        currentItem.value = row
        commentContent.value = row.content || ''
        showEdit.value = true
      }
      function editCommentSubmit () {
        console.log('editCommentSubmit', commentContent.value)
        const params = {
          content: commentContent.value,
          id: currentItem.value.id,
          refType: 17
        }
        proxy.$emit('editComment', params)
      }
      function handleCancel () {
        showEdit.value = false
      }
      function likeOrDislike (item, type) {
        const { id } = item
        const statusKey = type + 'Status'
        const flagKey = type + 'Flag'
        const params = {
          id,
          type,
          refType: props.refType,
        }
        params[flagKey] = item[statusKey] ? 0 : 1
        proxy.$emit('likeOrDislike', params, item)
      }

      function format (file) {
        const regImg = /.(jpg|bmp|gif|ico|pcx|jpeg|tif|png|raw|tga|webp|svg)$/i
        const regVideo = /.(avi|rmvb|mpeg|wmf|mov|mkv|mp4)$/i
        const regAudio = /.(mp3|wav|m4a)$/i
        if (regImg.test(file.fileName)) return 'file-image'
        if (regVideo.test(file.fileName)) return 'file-video'
        if (regAudio.test(file.fileName)) return 'file-audio'
      }

      const showFile = function (id, fileIndex) {
        const ref = `filePreview${id}${fileIndex}`
        proxy.$refs[ref]?.open()
      }

      function getFile (file) {
        const { fileName, fid } = file
        let cache = fid
        if (fileName && fid) {
          const index = fileName.lastIndexOf('.')
          cache = fid + fileName.substring(index, fileName.length)
        }
        return {
          filePath: file.framePath || file.filePath,
          fileName: file.fileName || '',
          fid: cache || ''
        }
      }

      return {
        changeShowComment,
        close,
        toComment,
        likeOrDislike,
        placeholder,
        format,
        showFile,
        getFile,
        editComment,
        showEdit,
        currentItem,
        commentContent,
        editCommentSubmit,
        handleCancel
      }
    },
    render () {
      const {
        changeShowComment,
        data,
        showTextarea,
        toComment,
        likeOrDislike,
        placeholder,
        format,
        showFile,
        getFile,
        showEdit,
        editComment,
        currentItem,
        editCommentSubmit,
        handleCancel
      } = this
      const singleUser = (item, level) => <div class="flex">
        <ni-img src={item.user?.userAvatar} alt="avatar" class={['avatar', 'avatar' + level]}/>
        <div class="flex-child-grow">
          <div class="name" style={{ marginTop: `${level === 1 ? 6 : 5}px` }}>
            {item.user?.userName}
            {item.targetUser?.userName ? <span>
              <span class="grey-9">回复</span>
              {item.targetUser?.userName}
            </span> : null}
          </div>
          <div class="contents">{item.content}</div>
          {item.attachmentsList?.length ? <div class="file-list">
            {item.attachmentsList.map((k, fileIndex) => <span>
              {
                format(k) === 'file-image'
                    ? <img src={k.filePath} alt={k.fileName} width="66" height="66"
                           onClick={() => showFile(item.id, fileIndex)}>
                      <filePreview file={getFile(k)}
                                   type={format(k)}
                                   showAudioPreview={true}
                                   pathKey="filePath"
                                   ref={`filePreview${item.id}${fileIndex}`}
                                   style="display: none"/>
                    </img>
                    : format(k) === 'file-video'
                        ? <span class="inline-block relative pointer">
                      <img src={k.filePath} alt={k.fileName} width="66" height="66"
                           onClick={() => showFile(item.id, fileIndex)}>
                        <filePreview file={getFile(k)}
                                     type={format(k)}
                                     pathKey="filePath"
                                     ref={`filePreview${item.id}${fileIndex}`}
                                     style="display: none"/>
                      </img>
                      <a-icon type="play-circle" class="playIco" onClick={() => showFile(item.id, fileIndex)}/>
                    </span>
                        : <a
                            style="margin-right: 8px"
                            key={k.id}
                            onClick={() => showFile(item.id, fileIndex)}
                        >
                          {k.fileName}
                          <filePreview file={getFile(k)}
                                       type={format(k)}
                                       pathKey="filePath"
                                       ref={`filePreview${item.id}${fileIndex}`}
                                       style="display: none"/>
                        </a>
              }
                          </span>)}
          </div> : null}
          <div class="flex flex-align-center mt-10 line-12 font-12">
            <span class="time">{item.createTime}</span>
            {item.showDelTsComment ? <ni-img onTap={() => {
              this.$emit('delComment', item.id, this.refType)
            }} src={del} class="small-icon ml-8"/> : null}
            {item.showTsHide ? <ni-img onTap={() => {
              this.$emit('toggleHide', item.id, item.hiddenFlag ? 0 : 1)
            }} src={!item.hiddenFlag ? showIcon : hideIcon} class="small-icon ml-8"/> : null}
            {item.showTsEdit ? <ni-img onTap={() => {
              editComment(item)
            }} src={editIcon} class="small-icon ml-8"/> : null}
            <ni-img onTap={() => changeShowComment(item)} src={comment} class="small-icon ml-40"/>
            <span class="pointer ml-20" onClick={() => likeOrDislike(item, 'like')}>
              <ni-img src={item.likeStatus ? isPraise : praise} class="big-icon"/>
              {item.likeCount ? <span class="number">{item.likeCount}</span> : null}
            </span>
            <span class="pointer ml-20" onClick={() => likeOrDislike(item, 'dislike')}>
              <ni-img src={item.dislikeStatus ? isTrample : trample} class="big-icon"/>
              {item.dislikeCount ? <span class="number">{item.dislikeCount}</span> : null}
            </span>
          </div>
        <JTextarea
            placeholder={placeholder}
            ref={'JTextareaRef' + item.id}
            class="mt-10"
            minRows={2}
            showComment={item.showComment}
            onToComment={toComment}
            background={level === 1 ? '#F7F8FA' : '#fff'}/>
      </div>
    </div>
      return <div>
        {showTextarea ? <JTextarea
            placeholder="添加评论"
            ref="JTextareaRef"
            style="margin-bottom: 30px"
            onToComment={(params) => toComment(params, 'out')}
            showComment={true}/> : null}
        <div>
          {data.map((first, index) => <div class={[index === 0 ? '' : 'mt-30']}>
            {singleUser(first, 1)}
            {first.children?.length ? <div class="second-comment">
              <div class="mt-20">{singleUser(first.children[0], 2)}</div>
              {first.children?.length > 1 ? <CollapseBox showUpDown={first.children?.length > 1}>
                {first.children.slice(1, first.children.length).map(second => <div class="mt-20">
                  {singleUser(second, 2)}
                </div>)}
              </CollapseBox> : null}
            </div> : null}
          </div>)}
        </div>
        <a-modal
            title="投诉界定"
            visible={showEdit}
            onOk={editCommentSubmit}
            onCancel={handleCancel}
            width={760}
        >
          <div class="relative">
            <a-textarea style="padding-bottom: 25px" max-length={500} v-model={this.commentContent} placeholder="请输入投诉界定" autoSize={{ minRows: 5, maxRows: 10 }}/>
            <span class="absolute complaintDemarcationContent-count">{this.commentContent?.length || 0}/500</span>
          </div>
        </a-modal>
      </div>
    }
  })
</script>

<style scoped lang="scss">
@import "../common";

.second-comment {
  background: #FAFAFA;
  border-radius: 4px;
  padding: 0 20px 20px 20px;
  overflow: hidden;
  margin-left: 58px;
  margin-top: 10px;
}

.number {
  color: #828282;
  margin-left: 1px;
}
.small-icon {
  width: 12px;
  height: 12px;
  cursor: pointer;
}
.big-icon {
  width: 13px;
  height: 12px;
  cursor: pointer;
}
.avatar {
  border-radius: 50%;
  margin-right: 10px;
}
.avatar1 {
  width: 48px;
  height: 48px;
}
.avatar2 {
  width: 32px;
  height: 32px;
}
.name {
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
}
.contents {
  line-height: 21px;
  margin-top: 10px;
  white-space: break-spaces;
  word-break: break-all;
}
.time {
  color: #9C9C9C;
}
.line-12 {
  line-height: 12px;
}
.mt-30 {
  margin-top: 30px;
}
.mt-20 {
  margin-top: 20px;
}
.ml-40 {
  margin-left: 40px;
}
.ml-20 {
  margin-left: 20px;
}
.file-list {
  img{
    display: inline-block;
    border-radius: 8px;
    margin: 8px 8px 0 0;
  }
  .playIco{
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -18px;
    margin-top: -15px;
    font-size: 30px;
    color: #FFFFFF;
  }
  a {
    display: block;
  }
}
.complaintDemarcationContent-count {
  bottom: 0px;
  right: 10px;
}
</style>
