<script lang="jsx">
  import Vue, { defineComponent, ref, getCurrentInstance, provide, computed } from 'vue'
  import Member from './components/member.vue'
  import Detail from './components/detail.vue'
  import Result from './components/result.vue'
  import Handle from './components/handle.vue'
  import Award from './components/award.vue'
  import TopTabs from './components/top-tabs.vue'
  import Process from './components/process'
  import CommentList from './components/comment-list.vue'
  import Imglct from './components/img'
  import browse from '~/components/browse'
  import { catMap } from '../list/constants'
  import {
    GET_SU_RES_INFO,
    GET_MEMBER_BASIC_INFO,
    SHOW_PROCESS_INFO
  } from '@operation/store/modules/complaint/action-types'
  import title1 from './images/title1.png'
  import title2 from './images/title2.png'
  import title3 from './images/title3.png'
  import title4 from './images/title4.png'
  import title5 from './images/title5.png'
  import title6 from './images/title6.png'
  import ContentBox from './components/content-box.vue'
  import Action from './components/action.vue'
  import complaintApi from '@/operation/api/complaint'
  import { Checkbox, DatePicker } from 'ant-design-vue'
  import InputPeople from '~/components/staff/staff-input'
  import moment from 'moment/moment'
  import cloneDeep from 'lodash/cloneDeep'
  import platform from '~/util/platform'
  import { NiStaffSelect } from '@jiuji/nine-ui'
  import gzIco from './images/gz.png'
  import gzhIco from './images/gz_h.png'

  export default defineComponent({
    components: {
      Member,
      Detail,
      Result,
      Handle,
      Process,
      Imglct,
      browse,
      ContentBox,
      Award,
      TopTabs,
      CommentList,
      Action,
      InputPeople
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const filess = ref([])
      const type = ref(null)
      provide('filess', filess)
      provide('type', type)
      const resultInfo = ref({
        bonusMoney: '',
        fineMoney: '',
        touSuDeparts: [],
        zeRenRenList: []
      })
      const followFlag = ref(false)
      provide('resultInfo', resultInfo)
      const detail = ref({
        areas: [],
        id: '',
        inuser: '', // 录入人
        content: '',
        isxinsheng: false, // 是否添加至新声
        attachments: [],
        huanyuantime: '',
        addTime: '',
        huanyuantimeout: '',
        states: '',
        processUser: '', // 跟进人
        archiveCategory: false, // 是否典型投诉
        timeOut: '',
        dealUsers: undefined, // 处理人
        customerEndTime: '',
        tag: undefined
      })

      const memberInfo = ref({
        memberMobile: null,
        memberRealName: null,
        memberUserName: null,
        tag: '',
        tcount: 0,
        xtenant: null
      })
      const isJiuJi = computed(() => proxy.$tnt.xtenant < 1000)
      const id = proxy.$route.params.id
      const demarcationList = ref([])
      const commentList = ref([])
      provide('id', id)
      provide('demarcationList', demarcationList)
      provide('commentList', commentList)
      provide('isJiuJi', isJiuJi)

      function getDemarcationList () {
        getAllReply(17, demarcationList)
      }
      if (isJiuJi.value) {
        getDemarcationList()
        getAllReply(18, commentList)
      }

      function dealTree (arr) {
        if (Array.isArray(arr)) {
          arr.forEach(it => {
            it.showComment = false
            it.children?.length && (dealTree(it.children))
          })
        }
      }
      async function getAllReply (refType, valKey) {
        complaintApi.getComment({ refId: id, refType }).then((res) => {
          if (res.code === 0) {
            const record = res.data?.record || []
            dealTree(record)
            valKey.value = record
          } else {
            proxy.$message.error(res.userMsg)
          }
        })
      }

      async function toComment (par) {
        const { fileList, ...other } = par
        const params = {
          ...other,
          refId: id
        }
        complaintApi.addComment(params).then((res) => {
          if (res.code === 0) {
            proxy.$message.success('评论成功')
            getAllReply(par.refType, par.refType === 17 ? demarcationList : commentList)
            const ref = par.refType === 17 ? 'demarcationRef' : 'commentRef'
            proxy.$refs[ref]?.close()
          } else {
            proxy.$message.error(res.userMsg)
          }
        })
      }

      function likeOrDislike (params, item) {
        complaintApi.likeOrDislike(params).then((res) => {
          if (res.code === 0) {
            getAllReply(params.refType, params.refType === 17 ? demarcationList : commentList)
          } else {
            proxy.$message.error(res.userMsg)
          }
        })
      }
      function editComment (params) {
        complaintApi.editComment(params).then((res) => {
          if (res.code === 0) {
            getAllReply(17, demarcationList)
            proxy.$refs.demarcationRef?.close()
          } else {
            proxy.$message.error(res.userMsg)
          }
        })
      }
      function delComment (id, refType) {
        proxy.$confirm({
          title: '确定删除投诉界定吗？',
          onOk: () => {
            complaintApi.delComment(id).then((res) => {
              if (res.code === 0) {
                getAllReply(refType, refType === 17 ? demarcationList : commentList)
              } else {
                proxy.$message.error(res.userMsg)
              }
            })
          }
        })
      }
      function toggleHideComment (id, hiddenFlag) {
        complaintApi.hiddenComment(id, hiddenFlag).then((res) => {
          if (res.code === 0) {
            getAllReply(17, demarcationList)
          } else {
            proxy.$message.error(res.userMsg)
          }
        })
      }
      const concernHandle = () => {
        const params = {
          sub_id: proxy.$route.params.id,
          type: followFlag.value ? 1 : 0,
          kind: 4
        }
        proxy.$indicator.open()
        complaintApi.subCollectDeal(params).then((res) => {
          if (res.code === 0) {
            followFlag.value = !followFlag.value
            proxy.$message.success(res.userMsg)
          } else {
            proxy.$message.error(res.userMsg)
          }
        }).finally(() => {
          proxy.$indicator.close()
        })
      }
      const processLog = ref([])
      const showDemarcation = computed(() => {
        return !!(isJiuJi.value && (demarcationList?.value?.length > 0 || proxy.$store.state.userInfo.Rank.includes('gjkp')))
      })
      provide('showDemarcation', showDemarcation)

      const uv = ref(0)

      const title = ref(`投诉详情【${proxy.$route.params.id}】`)
      document.title = title.value

      const getDetail = async function () {
        const params = {
          id: proxy.$route.params.id
        }
        const res = await proxy.$store.dispatch(
          `operation/complaint/${GET_SU_RES_INFO}`,
          params
        )
        if (res) {
          const { data, code } = res
          if (code === 0) {
            detail.value = data
            detail.value.archiveCategory = !!detail.value.archiveCategory
            followFlag.value = detail.value.followFlag
          } else if (code === 50004) {
            proxy.$router.push('/err/403')
          }
        }
      }

      const getMemberInfo = async function (data = {}) {
        const params = { id: proxy.$route.params.id, ...data }
        const res = await proxy.$store.dispatch(
          `operation/complaint/${GET_MEMBER_BASIC_INFO}`,
          params
        )
        if (res) {
          const { data } = res
          memberInfo.value = data
        }
      }
      provide('getMemberInfo', getMemberInfo)
      provide('getDetail', getDetail)

      const getProcess = async function () {
        const params = {
          id: proxy.$route.params.id
        }
        const res = await proxy.$store.dispatch(
          `operation/complaint/${SHOW_PROCESS_INFO}`,
          params
        )
        if (res) {
          const { data } = res
          processLog.value = data
          processLog.value.map((item, index) => {
            item.index = index
          })
        }
      }
      provide('getProcess', getProcess)

      const getStatistic = async function () {
        const params = {
          saasTenant: proxy.$tnt.tenantId || 10000,
          xtenant: proxy.$tnt.xtenant,
          url: proxy.$route.fullPath
        }
        uv.value =
          (await proxy.$store.dispatch('statistic/getStatistic', { params, route: proxy.$route })) || 0
      }

      const getInfo = function () {
        getDetail()
        getMemberInfo()
      }

      getInfo()
      getProcess()
      // getStatistic()
      const viewDetail = ref(false)
      provide('viewDetail', viewDetail)
      function showViewDetail () {
        viewDetail.value = true
        proxy.$refs.detailModal.getData(1)
      }

      function getPlatForm () {
        const platformArray = [
          { key: 'android', label: 'Android' },
          { key: 'ios', label: 'iOS' },
          { key: 'pc', label: 'PC_P' },
        ]
        const nowFlatform = platformArray.find(it => platform[it.key])?.label
        return nowFlatform + '/' + (platform.osVersion || '1.0.0')
      }

      const saveTsDemarcation = async function () {
        const { noticeUsers, endTime, notice } = form.value
        const params = {
          tsId: id,
          dsc: detail.value.complaintDemarcationContent,
          notice
        }
        if (notice) {
          params.noticeUsers = noticeUsers
          params.endTime = endTime
          params.platform = getPlatForm()
        }
        complaintApi.saveTsDemarcation(params).then((res) => {
          if (res.code === 0) {
            form.value = {
              notice: false,
              noticeUsers: [],
              endTime: undefined
            }
          } else {
            proxy.$message.error(res.userMsg)
          }
        })
      }

      function save () {
        const { notice, noticeUsers } = form.value
        const { complaintDemarcationContent } = detail.value
        if (complaintDemarcationContent && notice) {
          if (!noticeUsers.length) {
            proxy.$message.error('请选择投诉界定通知人员')
            return
          }
        }
        const { memberMobile } = memberInfo.value
        const { tag } = detail.value
        const { toCheck, setArchiveCategory } = proxy.$refs.detailInfoRef
        if (!toCheck()) return
        proxy.$indicator.open()
        Promise.all([getMemberInfo({ mobile: memberMobile, tag }), setArchiveCategory()]).finally(async () => {
          if (complaintDemarcationContent) await saveTsDemarcation()
          setTimeout(() => {
            getAllReply(17, demarcationList)
            getProcess()
          }, 100)
          proxy.$indicator.close()
        })
      }
      function doRefresh () {
        getProcess()
        getDetail()
        type.value === 7 && (getDemarcationList())
      }

      const hasScrollbar = ref(false)
      const scrollbarHeight = ref(0)
      provide('hasScrollbar', hasScrollbar)
      provide('scrollbarHeight', scrollbarHeight)

      function sendSuccess () {
        getProcess()
        proxy.$refs.resultRef?.getDetail()
      }

      const form = ref({
        notice: false,
        noticeUsers: [],
        endTime: undefined
      })

      const peopleChange = function (value, outputObject) {
        form.value.noticeUsers = outputObject.map(d => {
          return {
            noticeUser: d.staffName,
            ch999Id: d.staffId
          }
        })
      }

      const disabledDate = function (current) {
        return current && current < moment().subtract(1, 'days')
      }

      return {
        detail,
        memberInfo,
        uv,
        getDetail,
        getMemberInfo,
        getInfo,
        processLog,
        getProcess,
        viewDetail,
        showViewDetail,
        title,
        resultInfo,
        save,
        showDemarcation,
        isJiuJi,
        doRefresh,
        demarcationList,
        commentList,
        toComment,
        likeOrDislike,
        delComment,
        editComment,
        hasScrollbar,
        sendSuccess,
        toggleHideComment,
        peopleChange,
        disabledDate,
        form,
        concernHandle,
        followFlag
      }
    },
    render () {
      const {
        uv,
        memberInfo,
        getMemberInfo,
        detail,
        getDetail,
        getInfo,
        processLog,
        getProcess,
        showViewDetail,
        title,
        resultInfo,
        save,
        showDemarcation,
        isJiuJi,
        doRefresh,
        demarcationList,
        commentList,
        toComment,
        likeOrDislike,
        delComment,
        editComment,
        hasScrollbar,
        toggleHideComment,
        sendSuccess,
        peopleChange,
        disabledDate,
        form,
        concernHandle,
        followFlag
      } = this
      return (
      <page style="background-color: inherit;" class="page" title={title}>
        {isJiuJi && (
          <template slot="extra">
            <browse pageType={ 1 } id={ this.$route.params.id }/>
          </template>
        )}
        <div class="flex">
          <div class="containers-info">
            <div class="flex flex-align-center flex-justify-between">
              <TopTabs/>
              <div class="flex-child-noshrink mr-16 pointer" onClick={() => concernHandle()}>
                <span class="mr-5">{ followFlag ? '已关注' : '关注' }</span>
                <img src={followFlag ? gzhIco : gzIco } width="20" height="20" alt=""/>
              </div>
            </div>
            <div style="padding: 0 20px">
              <ContentBox icon={title1} title="会员信息" id="memberId">
                <Member
                  member-info={memberInfo}
                  detail={detail}
                  onGetMemberInfo={getMemberInfo}
                  add-time={detail.addTime}
                />
              </ContentBox>
              <ContentBox icon={title2} title="投诉信息" id="detailId">
                <Detail ref="detailInfoRef" member-info={memberInfo} detail={detail} onGetDetail={getDetail} />
              </ContentBox>
              <ContentBox icon={title3} title="投诉跟进" id="processId">
                <Process process-log={processLog} onGetProcess={getProcess} member-info={memberInfo} />
                <Handle onDoRefresh={doRefresh} info={detail} member-info={memberInfo} />
              </ContentBox>
              { showDemarcation ? <ContentBox class="no-padding" icon={title4} title="" id="demarcationId">
                {
                  <div slot="title" class="flex flex-justify-between">
                    <span>投诉界定</span>
                    {
                      detail.cat ? <span class="cat" style={catMap.get(detail.cat)}>{detail.catName}</span> : null
                    }
                  </div>
                }
                {
                  demarcationList && demarcationList.length > 0 ? <CommentList
                      data={demarcationList}
                      refType={17}
                      ref="demarcationRef"
                      onToComment={toComment}
                      onDelComment={delComment}
                      onEditComment={editComment}
                      onToggleHide={toggleHideComment}
                      onLikeOrDislike={likeOrDislike}
                      showTextarea={false}/> : <div>
                    <div class="relative">
                      <a-textarea
                        ref="textareaRef"
                        class="textarea"
                        maxLength={1000}
                        placeholder={'请输入投诉界定内容'}
                        autoSize={{
                          minRows: 5,
                          maxRows: 10
                        }}
                        v-model={detail.complaintDemarcationContent}>
                      </a-textarea>
                      <span
                        class="absolute complaintDemarcationContent-count">{detail.complaintDemarcationContent?.length || 0}/1000</span>
                    </div>
                    {
                      this.$tnt.xtenant < 1000
                        ? <div style="height:40px" class="padding-top flex flex-align-center">
                          <Checkbox v-model={form.notice} class="red">
                            通知
                          </Checkbox>
                          {form.notice ? (
                            <div class="ml-8 flex-align-center" style="flex: 1;display: flex;">
                              <div class="flex flex-align-center">
                                <span class="flex-child-noshrink">接收人：</span>
                                <NiStaffSelect maxTagCount={1} value={form.noticeUsers.map(d => d.ch999Id)} onChange={peopleChange} multiple style="min-width:174px;" placeholder="输入工号或姓名" class="grow1" allow-clear/>
                              </div>
                              <div class="flex flex-align-center" style="margin-left:16px;">
                                <span>跟进截止时间：</span>
                                <DatePicker
                                  format="YYYY-MM-DD HH:mm"
                                  value-format="YYYY-MM-DD HH:mm"
                                  show-time={{ format: 'HH:mm' }}
                                  disabled-date={disabledDate}
                                  v-model={form.endTime}
                                  style="height:30px;"
                                />
                              </div>
                            </div>
                          ) : null}
                        </div> : null
                    }
                  </div>
                }
              </ContentBox> : null}
              <div id="resultId">
                <ContentBox icon={title5} title="投诉处理结果" class="no-padding1">
                  <Award onSendSuccess={sendSuccess} resultInfo={resultInfo} info={detail} member-info={memberInfo}/>
                  <Result ref="resultRef" onGetInfo={getInfo} onGetProcess={getProcess}/>
                </ContentBox>
              </div>
            </div>
          </div>
          <Imglct imgSrc={detail.businessFlowChart}/>
          <a
            class="fixed-link"
            target="_blank"
            href="https://oa.9ji.com/staticpc/#/office/library/article/7238?auditStatus=1&verify=0">
            投诉界定规则
          </a>
        </div>
        <div class="buttons" style={{ bottom: hasScrollbar ? '9px' : 0 }}>
          <a-button onClick={save} type="primary">保存</a-button>
        </div>
      </page>
      )
    }
  })
</script>

<style lang="scss" scoped>
:deep(.upvote) {
  .big-button {
    margin-left: 20px !important;

    &:first-child {
      margin-left: 0 !important;
    }
  }
}

:deep(.no-padding) {
  .content {
    padding: 24px 0 0 0
  }
}

:deep(.no-padding1) {
  .content {
    padding: 10px 0 0 0
  }
}

:deep(.no-padding2) {
  .content {
    padding: 24px 0 20px 0
  }
}

.page {
  padding-bottom: 70px;
}

.buttons {
  position: fixed;
  left: 0;
  padding: 12px 20px;
  background: #fff;
  z-index: 1;
  width: 100%;
}

.containers-info {
  width: 1108px;
  flex-shrink: 0;
  background: #fff;
  border-radius: 8px;
  padding: 0;
  box-sizing: border-box;
}

.fixed-link {
  position: fixed;
  z-index: 9;
  right: 30px;
  bottom: 20px;
  width: 60px;
  height: 60px;
  font-size: 13px;
  padding: 8px;
  border-radius: 50px;
  text-align: center;
  display: flex;
  align-items: center;
  color: #fff;
  background: #1890FF;
}

.card {
  background: #fff;

  &-item {
    .title {
      font-size: 18px;
      font-weight: 700;
      color: #909399;
    }

    > div {
      margin-bottom: 2em;
      border-radius: 4px;
      border: 1px solid #d7d7d7;
      padding: 1em;
    }
  }
}

.textarea {
  display: block;
  border: none;
  width: 100%;
  box-sizing: border-box;
  border-radius: 4px;
  padding:10px;
  background: #F7F8FA;
}
.complaintDemarcationContent-count {
  bottom: 0px;
  right: 10px;
}
:deep(.cat) {
  padding: 4px;
  font-size: 12px;
  //line-height: 12px;
  display: inline-block;
  border-radius: 2px;
}
:deep(.title-text) {
  width: 100%;
}
</style>
