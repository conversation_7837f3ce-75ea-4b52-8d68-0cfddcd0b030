import level1 from './images/level1.png'
import level2 from './images/level2.png'
import level3 from './images/level3.png'
import level4 from './images/level4.png'
import level5 from './images/level5.png'
import level6 from './images/level6.png'
const isJiuJi = window.tenant?.xtenant < 1000

export const levelsIcon = new Map([
  ['普通会员', level1],
  ['青铜会员', level2],
  ['白银会员', level3],
  ['黄金会员', level4],
  ['钻石会员', level5],
  ['双钻会员', level6],
])

export const statesEnum = {
  1: '未受理',
  2: '已受理',
  7: '已还原',
  5: '已处理',
  8: '已界定',
  9: '待复核',
  10: '待仲裁',
  4: '待整改',
  6: '已整改',
  3: '已完成',
}

export function getStatusStyle (status) {
  let style = { background: 'rgba(241, 86, 67, 0.1)', color: 'rgba(241, 86, 67, 1)' }
  if ([2, 5, 6, 7, 8].includes(status)) {
    style = { background: 'rgba(24, 144, 255, 0.1)', color: 'rgba(24, 144, 255, 1)' }
  } else if ([3].includes(status)) {
    style = { background: 'rgba(11, 190, 105, 0.1)', color: 'rgba(11, 190, 105, 1)' }
  } else if ([4, 9, 10].includes(status)) {
    style = { background: 'rgba(250, 100, 0, 0.1)', color: 'rgba(250, 100, 0, 1)' }
  }
  return style
}

export const stateMap = new Map([
  [1, [12]],
  [2, [9]],
  [4, [10, 2]],
  [5, window.tenant.xtenant < 1000 ? [9, 11, 1, 2, 3] : [1, 2, 3]],
  [6, [2, 3]],
  [7, window.tenant.xtenant < 1000 ? [9, 11] : [11]],
  [8, [2, 4]],
  [9, [2, 5, 8]],
  [10, [2, 5, 6, 7]]
])

export const buttonsMap = new Map([
  [1, {
    toState: 8,
    toButtonName: '界定',
    buttonName: '界定'
  }],
  [2, {
    toState: 11,
    toButtonName: '000000000000000',
    buttonName: '客诉完结'
  }],
  [3, {
    toState: 3,
    toButtonName: '完成',
    buttonName: '完成'
  }],
  [4, {
    toState: 9,
    toButtonName: '申请复核',
    buttonName: '申请复核'
  }],
  [5, {
    toState: 4,
    toButtonName: '复核通过',
    buttonName: '复核通过'
  }],
  [6, {
    toState: 10,
    toButtonName: 'COO仲裁',
    buttonName: 'COO仲裁'
  }],
  [7, {
    toState: 4,
    toButtonName: '仲裁处理',
    buttonName: '仲裁处理'
  }],
  [8, {
    toState: 10,
    toButtonName: '运营仲裁',
    buttonName: '运营仲裁'
  }],
  [9, {
    toState: 7,
    toButtonName: '事件还原',
    buttonName: '事件还原'
  }],
  [10, {
    toState: 6,
    toButtonName: '整改完毕',
    buttonName: '整改完毕'
  }],
  [11, {
    toState: 5,
    toButtonName: '已处理',
    buttonName: '已处理'
  }],
  [12, {
    toState: 2,
    toButtonName: '受理',
    buttonName: '受理'
  }]
])

export const modelEnum = {
  kinds: [
    {
      label: '无',
      value: 0
    },
    {
      label: '售前',
      value: 1
    },
    {
      label: '售后',
      value: 2
    },
    {
      label: '后端',
      value: 3
    },
  ],
  cat: [
    {
      label: '无',
      value: 0
    },
    {
      label: '有效投诉',
      value: 1
    },
    {
      label: '无效投诉',
      value: 2
    },
    {
      label: '警示投诉',
      value: 3
    },
  ],
  touSuRank: [
    {
      label: '无',
      value: 0
    },
    {
      label: '轻微',
      value: 1
    },
    {
      label: '一般',
      value: 2
    },
    {
      label: '重要',
      value: 3
    },
    {
      label: '严重',
      value: 4
    },
    {
      label: '重大',
      value: 5
    }
  ]
}
export const detailColumns = [
  {
    title: '工号',
    dataIndex: 'userId',
    width: '25%'
  },
  {
    title: '姓名',
    dataIndex: 'userName',
    width: '25%'
  },
  {
    title: '时间',
    dataIndex: 'accessTime',
    width: '50%'
  }
]

export const originAreaColumns = (function () {
  return [
    {
      title: '责任门店/部门',
      dataIndex: 'areaName',
      width: '14%'
    },
    {
      title: '所属区域/中心',
      dataIndex: 'departAreaName',
      width: window.tenant.xtenant < 1000 ? '11%' : '14%'
    },
    {
      title: '扣分',
      dataIndex: 'scoreArea',
      width: window.tenant.xtenant < 1000 ? '5%' : '10%'
    },
    {
      title: '定性分类',
      dataIndex: 'cat',
      width: '10%',
      showType: 'enumLabel'
    },
    {
      title: '投诉分类',
      dataIndex: 'kinds',
      width: '10%',
      showType: 'enumLabel'
    },
    {
      title: '投诉等级',
      dataIndex: 'touSuRank',
      width: '10%',
      showType: 'enumLabel'
    },
    {
      title: '扣除积分',
      dataIndex: 'tousuPoint',
      width: '8%',
      show: window.tenant.xtenant < 1000
    },
    {
      title: '投诉原因',
      dataIndex: 'touSuType',
      width: '20%'
    },
    {
      title: '操作',
      dataIndex: 'action1',
      width: '14%'
    }
  ].filter(d => d.show === undefined || d.show)
})()

export const originStaffColumns = (function () {
  return [
    {
      title: '责任人',
      dataIndex: 'userName',
      width: '10%'
    },
    {
      title: '扣分',
      dataIndex: 'tousuLosePoint',
      width: '10%'
    },
    {
      title: isJiuJi ? '扣除积分' : '严重程度',
      dataIndex: 'tousuPoint',
      width: '10%'
    },
    {
      title: '奖励积分',
      dataIndex: 'bonusPoint',
      width: '10%',
      show: isJiuJi
    },
    {
      title: '90天内被投诉次数',
      dataIndex: 'complaintCount',
      width: '10%'
    },
    {
      title: '操作',
      dataIndex: 'action2',
      width: '10%'
    }
  ].filter(d => d.show === undefined || d.show)
})()
