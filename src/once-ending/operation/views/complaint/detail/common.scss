.ant-row {
  display: flex;
  align-items: center;
  line-height: 20px;
  min-height: 32px;
  margin-bottom: 12px;
}
.ant-col {
  display: flex;
  align-items: center;
  > span {
    white-space: nowrap;
  }
}
.width-174 {
  width: 174px;
}
.ml-20 {
  margin-left: 20px;
}
.mt-6 {
  margin-top: 6px;
}
.ml-32 {
  margin-left: 32px;
}
.ml-12 {
  margin-left: 12px;
}
.mt-10 {
  margin-top: 10px;
}
.ml-24 {
  margin-left: 24px;
}
.mt-24 {
  margin-top: 24px;
}
.mt-14 {
  margin-top: 14px;
}
.mb-24 {
  margin-bottom: 24px;
}
.ml-14 {margin-left: 14px}
.status {
  height: 20px;
  border-radius: 2px;
  font-size: 12px;
  line-height: 12px;
  padding: 4px;
  box-sizing: border-box;
  margin-left: 4px;
  display: inline-block;
}
.buttons {
  height: 32px;
  background: #E7F3FF;
  border-radius: 4px;
  color: #1890FF;
  padding: 0 8px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.mb-15 {
  margin-bottom: 15px;
}
.single-contents {
  border-radius: 8px;
  border: 1px solid #EBEBEB;
  padding: 20px;
  .title {
    display: inline-block;
    font-weight: 600;
    font-size: 16px;
    line-height: 16px;
    position: relative;
    &:after {
      content: '';
      position: absolute;
      left: 0;
      bottom: -4px;
      width: 100%;
      height: 6px;
      background: linear-gradient( 90deg, #1890FF 0%, rgba(24,144,255,0) 100%);
    }
  }
}
:deep(.color-blue) {
  color: #1890FF;
}
.ml-4 {
  margin-left: 4px;
}
