<script type='text/jsx' lang="jsx">
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions'
  import AreaDepartSelector from '~/components/staff/area-depart-selector-cache'
  export default {
    name: 'modal-addSubordinate',
    components: {
      AreaDepartSelector,
    },
    props: {
      options: {
        type: Array,
        default: () => ([])
      },
    },
    setup () {
      const {
        state,
        setSubordinateIsShow,
        setAddSubordinateForm,
        reSetAddSubordinateForm
      } = useState()
      const { submitSubordinate } = useActions()

      const closeModal = () => {
        setSubordinateIsShow(false)
        reSetAddSubordinateForm()
      }

      return {
        state,
        setAddSubordinateForm,
        submitSubordinate,
        closeModal,
      }
    },

    render () {
      const {
        state,
        submitSubordinate,
        setAddSubordinateForm,
        closeModal
      } = this
      const {
        currentRecord,
        subordinateIsShow,
        addSubordinateForm
      } = state

      return (
        <a-modal
          title={`【${currentRecord.name}】添加下级`}
          visible={ subordinateIsShow }
          onOk={ submitSubordinate }
          onCancel={ closeModal }
        >
        <a-form label-col={{ span: 6 }} wrapper-col={{ span: 17 }}>
          <a-form-item label='分类名称' required>
            <a-input
              value={ addSubordinateForm.name }
              onChange={ (e) => {
                setAddSubordinateForm(e.target.value, 'name')
              } }
            />
          </a-form-item>
          {
            [2, 3].includes(currentRecord.levels) &&
            <a-form-item label='归属部门' required>
              <AreaDepartSelector
                placeholder="请选择地区"
                type="department"
                treeNodeFilterProp="label"
                onChange={ (ids) => {
                  setAddSubordinateForm(ids[0], 'departId')
                } }
                value={ addSubordinateForm.departId }/>
            </a-form-item>
          }
        </a-form>
      </a-modal>
      )
    }
  }
</script>
