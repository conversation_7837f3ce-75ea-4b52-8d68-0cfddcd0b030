<script lang="jsx">
  import { defineComponent, ref, watch, nextTick } from 'vue'
  import { Select, message } from 'ant-design-vue'
  import api from '@operation/api/work-order'
  import { debounce, cloneDeep } from 'lodash'

  const urlMap = new Map([
    [1, { url: '/api/daily-reading/info/searchDailyReadingList', searchKey: 'keyword' }],
    [2, { url: '/api/studycontent/searchExamList', searchKey: 'keyword' }],
  ])

  export default defineComponent({
    props: {
      value: {
        type: [Array, String, Number],
        default: undefined,
      },
      type: { // 1 诵读 2考试
        type: Number,
        default: 1
      },
      multiple: {
        type: Boolean,
        default: true,
      },
      placeholder: {
        type: String,
        default: '请选择'
      },
      allowClear: {
        type: Boolean,
        default: true
      },
      defaultSearch: {
        type: Boolean,
        default: true,
      },
      defaultOptions: { // 默认枚举，如果页面重复使用该组件，枚举一样，可外面传进来，避免重复调接口
        type: Array,
        default: () => []
      },
      disabled: {
        type: Boolean,
        default: false
      },
      maxTagCount: {
        type: Number,
        default: 1
      },
      popupContainerClass: {
        type: String,
        default: ''
      },
      defaultChange: {
        type: Boolean,
        default: false
      }
    },
    setup (props, { emit }) {
      const localValue = ref(props.multiple ? [] : undefined)
      const orgOptions = ref([])
      watch(
        () => props.value,
        (val) => {
          localValue.value = val
          if (orgOptions.value.length) selectFocusOrBlur() // 避免数据不在首页无法渲染文字
        },
        {
          immediate: true
        }
      )

      const fetching = ref(false)

      // 显示的options
      const selectOptions = ref([])

      // 缓存的options
      const cacheOptions = ref([])
      const selectSearchValue = ref('')

      // 每次下拉框获取焦点或者失去焦点,初始化下拉框数据
      const selectFocusOrBlur = function () {
        const options = cloneDeep(orgOptions.value)
        const initOptions = options.splice(0, 50)
        if (props.multiple) {
          if (localValue.value?.length) {
            localValue.value.forEach((item) => {
              const index = options.findIndex((d) => d.value === item)
              if (index !== -1) {
                initOptions.unshift(options.splice(index, 1)[0])
              }
            })
          }
        } else {
          if (localValue.value) {
            const index = options.findIndex((d) => d.value === localValue.value)
            if (index !== -1) {
              initOptions.unshift(options.splice(index, 1)[0])
            }
          }
        }
        selectOptions.value = initOptions
        cacheOptions.value = options
      }

      watch(() => props.defaultOptions, (val) => {
        if (val?.length && !props.defaultSearch) {
          orgOptions.value = cloneDeep(val)
          selectFocusOrBlur()
          if (props.defaultChange && props.value) {
            const option = props.multiple ? props.value.map(d => orgOptions.value.find(k => k.value === d)) : orgOptions.value.find(d => d.value === props.value)
            emit('change', props.value, option, 'default')
          }
        }
      }, { deep: true, immediate: true })

      // 每次用户输入,匹配所有数据,将数据筛选出来
      const selectSearch = debounce(async function (val) {
        if (!val) {
          selectFocusOrBlur()
          return
        }
        await fetchData(val)
        nextTick(() => {
          selectSearchValue.value = val
          const options = cloneDeep(orgOptions.value)
          selectOptions.value = options.filter((d) =>
            d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase())
          )
          cacheOptions.value = options.filter(
            (d) => !d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase())
          )
        })
      }, 400)

      const handleSelect = function () {
        selectFocusOrBlur()
        selectSearchValue.value = ''
      }

      // 每次下拉框滚动条滚到底部,加载缓存数据
      const selectPopupScroll = function (e) {
        if (!cacheOptions.value?.length) {
          return
        }
        const { target } = e
        const scrollHeight = target.scrollHeight - target.scrollTop
        const clientHeight = target.clientHeight
        if (scrollHeight < clientHeight + 5) {
          const options = cacheOptions.value.splice(0, 50)
          selectOptions.value = selectOptions.value.concat(options)
        }
      }

      // 查询数据
      const fetchData = async function (keyWord) {
        const searchInfo = urlMap.get(props.type)
        const params = {}
        params[searchInfo.searchKey] = keyWord
        fetching.value = true
        const res = await api.searchInName(searchInfo.url, params).catch(err => {
          message.error(err.message)
        })
        fetching.value = false
        if (res) {
          const data = res.map(d => ({ value: props.type === 1 ? d.readId : d.examId, label: props.type === 1 ? d.readName : d.examName }))
          if (!keyWord) {
            orgOptions.value = data
          } else {
            data.map(d => {
              const item = orgOptions.value.find(k => k.value === d.value)
              if (!item) {
                orgOptions.value.push(d)
              } else {
                const index = orgOptions.value.findIndex(k => k.value === d.value)
                orgOptions.value.splice(index, 1, d)
              }
            })
          }
        }
      }

      const change = function (val) {
        emit('input', val)
        const option = props.multiple ? val.map(d => orgOptions.value.find(k => k.value === d)) : orgOptions.value.find(d => d.value === val)
        emit('change', val, option)
      }

      const init = async function () {
        if (!props.defaultSearch) return
        await fetchData()
        nextTick(() => {
          selectFocusOrBlur()
        })
      }
      init()

      return {
        localValue,
        selectOptions,
        selectFocusOrBlur,
        selectSearch,
        handleSelect,
        selectPopupScroll,
        fetching,
        change,
        fetchData
      }
    },
    render () {
      const {
        multiple,
        selectOptions,
        selectFocusOrBlur,
        handleSelect,
        selectSearch,
        selectPopupScroll,
        placeholder,
        fetching,
        allowClear,
        change,
        disabled,
        maxTagCount,
        popupContainerClass
      } = this
      return (
        <Select
          placeholder={placeholder}
          get-popup-container={(triggerNode) => popupContainerClass ? document.querySelector(popupContainerClass) : triggerNode.parentNode}
          show-arrow
          showSearch
          disabled={disabled}
          max-tag-count={maxTagCount}
          mode={multiple ? 'multiple' : 'default'}
          v-model={this.localValue}
          option-filter-prop="children"
          onFocus={selectFocusOrBlur}
          onBlur={selectFocusOrBlur}
          onSelect={handleSelect}
          onSearch={selectSearch}
          onPopupScroll={selectPopupScroll}
          allow-clear={allowClear}
          options={selectOptions}
          onChange={change}
        >
          {
            fetching ? <a-spin slot="notFoundContent" size="small"/> : null
          }

        </Select>
      )
    },
  })
</script>
