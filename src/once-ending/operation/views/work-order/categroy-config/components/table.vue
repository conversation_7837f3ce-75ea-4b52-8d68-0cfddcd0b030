<script type='text/jsx' lang="jsx">
  import { onMounted } from 'vue'
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions'

  import AreaDepartSelector from '~/components/staff/area-depart-selector-cache'
  import AssociationModal from './association-modal'

  export default {
    name: 'config-table',
    components: {
      AreaDepartSelector,
    },
    data () {
      return {
        columns: [
          {
            title: '标识',
            dataIndex: 'identify',
            key: 'identify',
            width: '6%',
            align: 'center'
          },
          {
            title: '板块',
            dataIndex: 'name',
            key: 'name',
            width: '12%',
            align: 'center',
            customRender: (text, record) => <span>{ record.levels === 1 ? { text } : null }</span>
          },
          {
            title: '分类',
            dataIndex: 'categroy',
            key: 'categroy',
            width: '12%',
            align: 'center',
            customRender: (text, record) => <span>{ record.levels !== 1 ? <i>{ record.name }</i> : null }</span>
          },
          {
            title: '',
            dataIndex: 'categroyThree',
            key: 'categroyThree',
            width: '6%',
            align: 'center'
          },
          {
            title: '归属部门',
            dataIndex: 'departId',
            key: 'departId',
            align: 'center',
            width: 320,
            customRender: (text, record) => (
              <span>
              {
                [3, 4].includes(record.levels)
                ? <AreaDepartSelector
                    placeholder="请选择地区"
                    type="department"
                    treeNodeFilterProp="label"
                    onChange={(ids) => this.updateKinds(record, ids)}
                    value={text}/>
                : null
              }</span>)
          },
          {
            title: '关联诵读',
            dataIndex: 'readName',
            key: 'readName',
            width: '8%',
            align: 'center',
            show: this.$tnt.xtenant < 1000
          },
          {
            title: '关联考试',
            dataIndex: 'examName',
            key: 'examName',
            width: '8%',
            align: 'center',
            show: this.$tnt.xtenant < 1000
          },
          {
            title: '状态',
            dataIndex: 'enabled',
            key: 'enabled',
            width: '6%',
            align: 'center',
            customRender: (text, record) => (
              <span>
              {
                record.levels !== 1
                ? <a-switch
                    checked-children="启用"
                    un-checked-children="停用"
                    loading={record.statusAsyncLoading}
                    onChange={() => this.onChangeStatus(record, !text)}
                    checked={text}
                  />
                : null
              }
              </span>
            )
          },
          {
            title: '操作',
            align: 'center',
            customRender: (text, record) => (
              <span>
              {
                record.levels !== 4
                ? <a-button
                    icon="plus-circle"
                    type='link'
                    class="mr-8 pointer"
                    onClick={() => this.addSubordinate(record)}>
                    添加下级
                  </a-button>
                : null
              }
                {
                  this.$tnt.xtenant < 1000 ? <a-button
                    type='link'
                    class="pointer"
                    onClick={() => this.association(record)}>
                    关联诵读/考试
                  </a-button> : null
                }
              </span>
            )
          }
        ].filter(d => d.show === undefined || d.show),
      }
    },
    setup () {
      const {
        state,
        setSubordinateIsShow,
        selectItem,
        showAssociationModal,
        destroyedModal
      } = useState()
      const {
        fetchData,
        updateKinds,
        onChangeStatus,
        addSubordinate,
      } = useActions()

      const association = function (record) {
        selectItem.value = record
        showAssociationModal.value = true
      }

      onMounted(() => {
        fetchData()
      })
      return {
        state,
        setSubordinateIsShow,
        addSubordinate,
        updateKinds,
        onChangeStatus,
        destroyedModal,
        showAssociationModal,
        association
      }
    },
    render () {
      const {
        state,
        destroyedModal,
        columns
      } = this
      const {
        orderCatagroy,
      } = state

      const expandIcon = (props) => {
        if (!props.record.children?.length) return
        return (
          <a-icon
            type={ props.expanded ? 'caret-down' : 'caret-right'}
            class='font-20 blue'
            onClick={e => { props.onExpand(props.record, e) }}/>
        )
      }
      const rowClassName = (record) => {
        const className = record.levels === 1
          ? 'Grade-one'
          : record.levels === 2
            ? 'Grade-two' : 'Grade-three'
        return className
      }

      return (
        <a-card>
          <a-table
            rowKey="id"
            defaultExpandAllRows
            pagination={false}
            expandIconColumnIndex={ 3 }
            indentSize={ 50 }
            dataSource={ orderCatagroy }
            columns={ columns }
            expandIcon={ expandIcon }
            rowClassName={ rowClassName }
          />
          {
            !destroyedModal ? <AssociationModal/> : null
          }
        </a-card>
      )
    },
  }
</script>

<style lang='scss'>
  .Grade-one,
  .Grade-two{
    font-weight: 600;
  }
  .Grade-one{
    color: #1890FF;
  }
  .Grade-three{
    background: #efefef;
  }
</style>
