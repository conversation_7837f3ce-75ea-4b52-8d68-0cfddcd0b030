<template>
  <a-modal
    title="关联诵读/培训"
    v-model="showAssociationModal"
    @ok="ok"
    @cancel="showAssociationModal = false"
    :confirmLoading="loading"
    :afterClose="afterClose"
  >
    <a-form :label-col="{span: 4}" :wrapper-col="{ span: 20 }">
      <a-form-item label='关联诵读'>
        <name-search-lazy-select :multiple="false" placeholder="请选择诵读文章" v-model="formData.readId" @change="(val,option) => handleChange(val,option,'read')"/>
      </a-form-item>
      <a-form-item label='关联考试'>
        <name-search-lazy-select :multiple="false" placeholder="请选择考试" :type="2" v-model="formData.examId" @change="(val,option) => handleChange(val,option,'exam')"/>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup>
  import { ref, nextTick, watch } from 'vue'
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions'
  import NameSearchLazySelect from './name-search-lazy-select'
  import api from '@operation/api/work-order'
  import { message } from 'ant-design-vue'

  const {
    showAssociationModal,
    destroyedModal,
    selectItem
  } = useState()
  const { fetchData } = useActions()

  watch(() => showAssociationModal.value, val => {
    if (val) {
      formData.value.readId = selectItem.value.readId || undefined
      formData.value.readName = selectItem.value.readName || undefined
      formData.value.examId = selectItem.value.examId || undefined
      formData.value.examName = selectItem.value.examName || undefined
    }
  })

  const formData = ref({
    readId: undefined,
    readName: '',
    examId: undefined,
    examName: ''
  })

  const loading = ref(false)

  const handleChange = function (val, option, type) {
    formData.value[type + 'Name'] = option?.label || ''
  }

  const afterClose = function () {
    destroyedModal.value = true
    nextTick(() => {
      destroyedModal.value = false
    })
  }

  const ok = async function () {
    const { readId, examId } = formData.value
    if (!readId && !examId) {
      showAssociationModal.value = false
      return
    }
    const params = {
      ...formData.value,
      id: selectItem.value.id
    }
    const res = await api.saveAssociation(params).catch(err => {
      message.error(err.message)
    })
    if (res) {
      message.success('保存成功')
      showAssociationModal.value = false
      fetchData()
    }
  }
</script>
