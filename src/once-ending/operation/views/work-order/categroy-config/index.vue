<script type='text/jsx' lang="jsx">
  import { provide } from 'vue'
  import { createState } from './model/useState.js'

  import ModalAddSubordinate from './components/modal-addSubordinate'
  import ConfigTable from './components/table'

  export default {
    name: 'categroy-config',
    components: {
      ModalAddSubordinate,
      ConfigTable
    },
    setup () {
      const { state } = createState(provide)
      return {
        state
      }
    },
    render () {
      return (
        <page>
          <ConfigTable/>
          { this.state.subordinateIsShow && <modal-addSubordinate/> }
        </page>
      )
    },
  }
</script>
