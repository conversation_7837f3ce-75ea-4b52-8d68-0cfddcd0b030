
import { getCurrentInstance } from 'vue'
import { message } from 'ant-design-vue'
import {
  FETCH_WORK_ORDER_KINDS,
  UPDATE_WORK_ORDER_KINDS,
  ADD_WORK_ORDER_KINDS
} from '@operation/store/modules/work-order/action-types'

import { useState } from './useState.js'

export default function useActions () {
  const instance = getCurrentInstance().proxy
  const {
    state,
    setCurrentRecord,
    setSubordinateIsShow,
    setOrderCatagroy,
    setAddSubordinateForm,
    reSetAddSubordinateForm
  } = useState()

  const fetchData = async () => {
    instance.$indicator.open()
    const res = await instance.$store.dispatch(`operation/workOrder/${FETCH_WORK_ORDER_KINDS}`)
    instance.$indicator.close()
    const { code, data } = res
    if (code === 0) {
      setOrderCatagroy(data)
    }
  }
  const submitSubordinate = async () => {
    const { parentId, name, departId } = state.addSubordinateForm
    if (!name) {
      message.error('请填写名称')
      return
    }
    if (state.currentRecord.levels !== 1) {
      if (!departId) {
        message.error('请填写归属部门')
        return
      }
    }
    const params = {
      name,
      parentId
    }
    if (departId) params.departId = departId

    instance.$indicator.open()
    const res = await instance.$store.dispatch(`operation/workOrder/${ADD_WORK_ORDER_KINDS}`, params)
    instance.$indicator.close()
    if (res?.code === 0) {
      message.success('保存成功')
      setSubordinateIsShow(false)
      reSetAddSubordinateForm()
      fetchData()
    }
  }
  const updateKinds = async (record, ids) => {
    if (ids?.length) {
      record.departId = ids[0]
    }
    instance.$set(record, 'statusAsyncLoading', true)
    const res = await instance.$store.dispatch(`operation/workOrder/${UPDATE_WORK_ORDER_KINDS}`, record)
    record.statusAsyncLoading = false
    if (res.code === 0) {
      fetchData()
      return true
    }
    return false
  }
  const addSubordinate = (record) => {
    setSubordinateIsShow(true)
    setCurrentRecord(record)
    setAddSubordinateForm(record.id, 'parentId')
  }
  /**
   * 【停用】二级分类 && 二级分类下若有子分类）=> 弹出提示框[确定] => 分类与其子分类状态全置为停用状态
   * 【启用】三级分类 => 其父分类置为启用状态
   */
  // 检查子级中是否有开启状态的三级分类
  const onChangeStatus = (record, enabled) => {
    const h = instance.$createElement
    const hasDeactivate = record.children?.length && record.children.find(item => item.enabled === true)
    if (record.levels === 2 && hasDeactivate && !enabled) {
      instance.$dialog({
        title: '提示',
        content: <span>停用后，其下面的子分类也会被停用，确定停用</span>,
        actions: [
          { label: '取消' },
          {
            label: '确定',
            type: 'primary',
            click: (dialog) => {
              record.enabled = enabled
              const res = updateKinds(record)
              if (res) {
                record.children.map(item => { item.enabled = enabled })
                dialog.close()
              }
            }
          }
        ]
      })
    } else {
      record.enabled = enabled
      updateKinds(record)
    }
  }

  return {
    fetchData,
    submitSubordinate,
    updateKinds,
    addSubordinate,
    onChangeStatus,
  }
}
