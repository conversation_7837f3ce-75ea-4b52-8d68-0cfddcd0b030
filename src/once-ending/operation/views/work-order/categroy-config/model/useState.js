import { reactive, inject, ref } from 'vue'

const key = Symbol('categroyConfig')

export function useState () {
  return inject(key)
}

export function createState (provide) {
  const state = reactive({
    loading: false,
    orderCatagroy: [],
    options: {},
    expandedRowKeys: [],
    currentRecord: {},
    subordinateIsShow: false,
    addSubordinateForm: {
      name: '',
      parentId: 1,
      departId: ''
    },
  })

  const showAssociationModal = ref(false)

  const destroyedModal = ref(false)

  const selectItem = ref({})

  const setLoading = val => { state.loading = val }
  const setOrderCatagroy = val => { state.orderCatagroy = val }
  const setOptions = val => { state.options = val || [] }
  const setExpandedRowKeyse = val => { state.expandedRowKeys = val }
  const setCurrentRecord = val => { state.currentRecord = val }
  const setSubordinateIsShow = val => { state.subordinateIsShow = val }

  const setAddSubordinateForm = (val, key) => {
    if (key && typeof key === 'string') {
      state.addSubordinateForm[key] = val
    } else {
      state.addSubordinateForm = val
    }
  }
  const reSetAddSubordinateForm = () => {
    state.addSubordinateForm = {
      name: '',
      parentId: 1,
      departId: ''
    }
  }

  const categroyConfig = {
    state,
    setLoading,
    setOrderCatagroy,
    setOptions,
    setExpandedRowKeyse,
    setCurrentRecord,
    setSubordinateIsShow,
    setAddSubordinateForm,
    reSetAddSubordinateForm,
    showAssociationModal,
    destroyedModal,
    selectItem
  }
  provide(key, categroyConfig)
  return categroyConfig
}
