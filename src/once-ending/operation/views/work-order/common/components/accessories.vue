<template>
  <custom-uploader
    :fileList.sync="dataSource.accessoriesList"
    showThumbnail
    :isDetail="true"
    :showRename="false"
    :editFileName="false"
    :createUser="createUser"
    collection="oa-operate"
    :disabled="submitDisabled"
    />
</template>
<script>
  import customUploader from './custom-uploader'
  export default {
    name: 'accessories',
    components: {
      customUploader,
    },
    props: {
      dataSource: {
        type: Object,
        default: () => {}
      }
    },
    computed: {
      createUser () { // 是否为创建人
        return this.dataSource.userId === this.$store.state.userInfo.UserID
      },
      // 只有创建人或者责任人可以上传附件
      submitDisabled () {
        let UserID = this.$store.state.userInfo.UserID
        return this.dataSource.userId !== UserID && this.dataSource.dutyUserId !== UserID
      },
    }
  }
</script>
