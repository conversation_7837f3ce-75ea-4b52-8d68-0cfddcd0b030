<template>
 <div>
    <a-form-item label="责任主体" v-if="form.type !== 5" required>
      <a-radio-group
        class="radio-group"
        name="legalDutyGroup"
        v-model="form.legalDuty"
        :disabled="disabled">
        <a-radio v-if="form.type !== 4" :value="0">门店</a-radio>
        <a-radio v-else :value="2">部门</a-radio>
        <a-radio :value="1">责任人</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item label='责任门店' v-if="form.type !== 4 && form.legalDuty === 0" required>
      <area-depart-selector
        :treeCheckable='!disabled'
        placeholder="请选择地区"
        treeNodeFilterProp='label'
        @change="onStoreChange"
        showType="SHOW_CHILD"
        v-model="form.areaIds"/>
    </a-form-item>
    <a-form-item label='责任部门' v-if="form.type === 4 && form.legalDuty === 2" required>
      <area-depart-selector
        :disabled="disabled"
        :treeCheckable='!disabled'
        type="department"
        placeholder="请选择部门"
        :leafOnly='disabled'
        treeNodeFilterProp='label'
        @change="onDepartChange"
        showType="SHOW_CHILD"
        :value="!disabled ? form.departIds : form.departId"
      />
    </a-form-item>
    <a-form-item
      :label="[1, 2, 3].includes(form.type) && $tnt.xtenant < 1000 ? '主要责任人':'责任人'"
      v-if="form.legalDuty === 1"
      required
      class="create-order-people">
      <ni-staff-select
              :disabled="disabled && form.type === 5"
              :value="!disabled ? form.peoplesList.map(d => d.id) : form.people.id"
              :multiple="!disabled ? true : false"
              placeholder="请选择责任人"
              allow-clear
              @change="(value, outputObject) => { inputPeopleChange(value, outputObject) }"
            />
    </a-form-item>
   <a-form-item
     label='其他责任人'
     v-if="form.legalDuty === 1 && $tnt.xtenant < 1000 && [1, 2, 3].includes(form.type)"
     class="create-order-people">
     <ni-staff-select
       :disabled="disabled && form.type === 5"
       v-model="form.otherResponsibilityPersons"
       multiple
       :maxTagCount="1"
       placeholder="其他责任人"
       allow-clear
     />
   </a-form-item>
  </div>
</template>

<script type='text/jsx'>
  import AreaDepartSelector from '~/components/staff/area-depart-selector-cache'
  import { mapState } from 'vuex'
  import { NiStaffSelect } from '@jiuji/nine-ui'

  export default {
    name: 'index-createOrder',
    components: {
      AreaDepartSelector,
      NiStaffSelect
    },
    props: {
      form: {
        type: Object,
        default: () => {}
      },
      editType: {
        type: String,
        default: ''
      },
      disabled: { // true为编辑模式
        type: Boolean,
        default: false
      },
    },
    computed: {
      ...mapState({
        userInfo: state => state.userInfo || {}
      }),
    },
    watch: {
      'form.legalDuty': {
        handler (val) {
          if (val === 2 && this.editType === 'create') {
            this.getCreatOrderStorage()
          }
        },
        deep: true,
        immediate: true
      }
    },
    methods: {
      // 创建工单时，读取缓存，默认设置个人最后一次选择的跨部门-门店
      getCreatOrderStorage () {
        // 仅有跨部门对接权限，默认为跨部门对接 4
        const currentUserId = this.userInfo.UserID
        const creatOrderDepartStorage = window.localStorage.getItem(`CODStorage${currentUserId}`)
        if (creatOrderDepartStorage) {
          const CODS = JSON.parse(creatOrderDepartStorage)
          this.form.departIds = CODS.departIds
        }
      },
      onStoreChange (ids) {
        if (ids.length > 50) {
          this.$message.error('责任门店已超最多50家的限制, 请减至50家以下')
        }
      },
      onDepartChange (ids) {
        if (!this.disabled) {
          this.form.departIds = ids
        } else {
          this.form.departId = ids
        }
      },
      inputPeopleChange (value, outputObject) {
        if (!this.disabled) {
          const output = outputObject.map(d => ({ id: d.staffId, name: d.staffName }))
          const all = this.form.peoplesList ? this.form.peoplesList.concat(output) : output
          const val = all.filter(d => value.includes(d.id))
          const keys = []
          const arr = []
          val.map(d => {
            if (!keys.includes(d.id)) {
              keys.push(d.id)
              arr.push(d)
            }
          })
          this.form.peoplesList = arr
        } else {
          this.form.people = outputObject ? { id: outputObject.staffId, name: outputObject.staffName } : {}
        }
      }
    },
  }
</script>
<style lang='scss' scoped>
  :deep(.ant-radio-group){
    margin-left: 12px;
  }
</style>
