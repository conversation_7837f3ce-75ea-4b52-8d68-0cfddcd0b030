# 上传附件



```用的是最行的九机附件http://192.168.254.8:4873/#/detail/@jiuji/nine-upload对组件进行了二次封装```
用法： [https://code.9ji.com/Front-end/oa-pc/src/branch/master/src/components/custom-uploader/%e6%9c%80%e6%96%b0%e4%b8%8a%e4%bc%a0%e9%99%84%e4%bb%b6%e7%9a%84%e7%bb%84%e4%bb%b6.md](https://code.9ji.com/Front-end/oa-pc/src/branch/master/src/components/custom-uploader/%e6%9c%80%e6%96%b0%e4%b8%8a%e4%bc%a0%e9%99%84%e4%bb%b6%e7%9a%84%e7%bb%84%e4%bb%b6.md)
附件上传就是获取上传附件的列表，如果进行修改的话就是获取删除附件的id让后端删除

#### 前提

删除：只有已经被存储后的附件才能被传会`item`,没有id的表示本地上传未上传至需要的模块中 （删除的功能我们最好用上，服务器也不是无限大的，现在还没有让做删除，我们把要删除的fid传给后端，他们处理了就删除，不处理就废）
展示：会对`fileList`里的数据进行校验，为了展示和修改的功能，`fileList`项必须包含 `fid`, `fileName`, `fileUrl`属性



### 方法一 (使用v-bind 数据绑定) 

这个实际上和v-model差不多 
(语法糖)v-model <===> v-bind:value + v-on:input 
:fileList.async  <====> v-bind:fileList + v-on:update:fileList 

子组件修改父组件的方式时可能会默认触发updata:attrName吧，这个我还没有深究！！
~~~js
<template>
    <custon-uploader 
      v-bind:fileList.sync="fileList" 
      v-bind:delList.sync="delList"
    />
    -----or-----
    <custon-uploader 
      :fileList.sync="fileList" 
      :delList.sync="delList"
    />
</template>
<script lang="jsx">
  import custonUploader from '~/components/custom-uploader'
  export default {
    components: {
      custonUploader
    },
    data () {
      return {
        fileList: [],
        delList: []
      }
    }
  }
</script>
~~~

### 方法二(@event监听函数)


~~~js
<template>
    <custon-uploader 
      @change="change"
      @delete="delete"
    />
</template>
<script>
  import custonUploader from '~/components/custom-uploader'
  export default {
    components: {
      custonUploader
    },
    data () {
      return {
        fileList: [],
        delList: []
      }
    },
    methods: {
      change (list) {
        this.fileList = list
      },
      delete (item) {
        this.delList.push(item.id)
      }
    }
  }
</script>
~~~


## 参数

| 参数名     | 描述                                 | 类型      | 默认值                                                       |
| ---------- | ------------------------------------ | --------- | ------------------------------------------------------------ |
| fileList   | 上传的列表展示列表和修改数据列表内容 | `Arraty`  | []                                                           |
| accept     | 文件类型                             | `String`  | `image/*`,`.pdf`,`.pdfx`,`.doc`,`.docx`,`.ppt`,`.pptx`,`.xls`,`.xlsx`,`.txt` |
| multiple   | 是或否多选                           | `Boolean` | `true`                                                       |
| download   | 是否显示下载                         | `Boolean` | `false`                                                      |
| deleteItem | 是否支持删除操作                     | `Boolean` | `true`                                                       |
| showQrBtn  | 是否支持手机上传                     | `Boolean` | `true`                                                       |
| disabled   | 是否禁止上传                         | `Boolean` | `false`                                                      |
| pathKey   | 显示url的key(可能后端给你的返回的链接不是fileUrl)      | `String` | `fileUrl`             |
---

##  事件
| 事件名称 | 描述                       | 回调参数         |
| -------- | -------------------------- | ---------------- |
| change   | 上传或删除列表下项时会触发 | `function(list)` |
| delete   | 删除时触发函数             | `function(list)` |




(实现想法：所有的数据的修改都是在子组件中实现的，但是子组件中的数据的变化不会修改父组件处理的属性，只会触发change,父组件进行监听，然后父组件重新赋值。
这个应该是叫单项数据流
1.数据通过属性传递，子组件监听变化，赋值展示。
2.子组件内部发生变化，触发change事件，把变化后的数据传递给父组件
3.父组件监听change，保存子组件传递的数据
4.vue内部隐式更改父组件传递给子组件的属性，并且让子组件知道，通过watch监听变化
                /
          父组件--------  event
          |        4     /|
        1 |              3| 
          |/              |
          子组件-------  change
                  2   /



个人感觉这样可以知道数据的每一步走向
以上的想法希望可以帮你理解我写的这个组件，有想的不对的地方请大佬们及时指正()

)


## ++

有不同需求的可自行修改， 之后请更新使用文档！！！！更新使用文档！！！！！！



# 预览附件

## 参数

文件预览已经放在附件上传的组件里了使用到的第三方工具有XDOC[XDOC](https://view.xdocin.com/)和[office](https://view.officeapps.live.com/op/view.aspx)这两种文件预览方式，office不支持打开.txt文件,所以用了XDOC来预览。


| 参数名   | 描述               | 类型      | 默认值                         |
| -------- | ------------------ | --------- | ------------------------------ |
| file     | 需要展示的file项的 | `Object`  | {fileName： xxx, fileUrl: xxx} |
| download | 是否显示下载       | `Boolean` | `true`                         |

