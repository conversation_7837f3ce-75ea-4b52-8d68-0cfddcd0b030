<template>
  <div class="img-wrap">
    <template v-if="list.length" >
      <div v-for="(file, index) in list" :key="index" class="flex flex-wrap flex-align-center relative">
        <!-- 图片 -->
        <template v-if="isImg(file)">
          <lazy-img
            v-if="showThumbnail"
            class="img-style"
            width="100"
            height="100"
            :src="file.fileUrl || file.filePath"/>
          <div class="action-warp">
            <file-preview
              v-if="showPreview"
              :showName="edit"
              :file="file"
              :pathKey="pathKey"
              showFileName="false"
              :download="!download" />
            <a v-if="download" class="padding" href="#" @click="downloadFile(file)"><a-icon type="cloud-download" /></a>
            <a v-if="deleteItem" class="padding" @click="deleteFile(file,index)"><a-icon type="delete"/></a>
            <a-button v-if="file.id && showRename" type="primary" size="small" @click="renameFile(file)">重命名</a-button>
          </div>
        </template>
        <!-- 其它类型 -->
        <template v-else>
          <div class="noImg-warp">
            <a-input v-if="editFileName" v-model="file.fileName" class="file-name" :disabled="!editName"/>
            <div v-else>
              <a-tooltip class="file-name">
                <template slot="title">
                  {{ file.fileName }}
                </template>
                {{ file.fileName }}
              </a-tooltip>
            </div>
            <div class="action-warp">
              <file-preview
                v-if="showPreview"
                :showName="edit"
                :file="file"
                :pathKey="pathKey"
                showFileName="false"
                :download="!download" />
              <a v-if="download" class="padding" href="#" @click="downloadFile(file)"><a-icon type="cloud-download" /></a>
              <a v-if="deleteItem" class="padding" @click="deleteFile(file,index)"><a-icon type="delete"/></a>
              <a-button v-if="file.id && showRename" type="primary" size="small" @click="renameFile(file)">重命名</a-button>
            </div>
          </div>
        </template>
      </div>
    </template>
    <a-button class="upload-btn" type="link" @click="doUpload" v-if="showUpload">
      <a-icon type="plus"></a-icon>
    </a-button>
  </div>
</template>

<script>
  import { Modal, Upload } from 'ant-design-vue'
  import { mapState } from 'vuex'
  import { saveAs } from 'file-saver'
  import nineUpload from '@jiuji/nine-upload'
  import filePreview from './file-preview'
  import LazyImg from '~/components/lazy-img'

  export default {
    name: 'Uploader',
    props: {
      // 图片展示缩略图,其余照旧展示
      showThumbnail: {
        type: Boolean,
        default: false
      },
      // 是否显示重命名按钮
      showRename: {
        type: Boolean,
        default: true
      },
      // 是否禁止上传
      showUpload: {
        type: Boolean,
        default: true
      },
      disabled: {
        type: Boolean,
        default: false
      },
      showPreview: {
        type: Boolean,
        default: true
      },
      // 是否多选
      multiple: {
        type: Boolean,
        default: true
      },
      // 支持上传多个文件
      moreAmount: {
        type: Boolean,
        default: true
      },
      editFileName: {
        type: Boolean,
        default: true
      },
      addSize: {
        type: Boolean,
        default: false
      },
      // 支持上传多个文件
      moreAmountWarn: {
        type: String,
        default: '最多上传一个文件'
      },
      editName: {
        type: Boolean,
        default: true
      },
      // 文件是或否支持下载
      download: {
        type: Boolean,
        default: false
      },
      // 文件是否支持删除
      deleteItem: {
        type: Boolean,
        default: true
      },
      edit: Boolean,
      // path的key名
      pathKey: {
        type: String,
        default: 'fileUrl'
      },
      // 上传的列表
      fileList: {
        type: [Array],
        default: () => [],
        validator: (fileList) => {
          let isRight = true
          fileList?.map(item => {
            if (!item.hasOwnProperty('fid') || !item.hasOwnProperty('fileName')) isRight = false
          })
          if (!isRight) console.error('fileList: 参数不正确')
          return isRight
        }
      },
      // 删除的id列表
      delList: {
        type: [Array],
        default: () => []
      },
      routKey: {
        type: String,
        default: ''
      },
      // 文件上传的类型
      accept: {
        type: String,
        default: 'image/*,.pdf,.pdfx,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt'
      },
      collection: {
        type: String,
        default: 'javaweb'
      }
    },
    components: {
      LazyImg,
      [Upload.name]: Upload,
      [Modal.name]: Modal,
      filePreview
    },

    data () {
      return {
        list: [],
        // 上传需要的两个变量值
        appId: '',
        token: ''
      }
    },
    computed: {
      ...mapState({
        userInfo: state => state.userInfo || {}
      }),
    },
    created () {
      this.list = this.fileList
    },
    watch: {
      // 监听数组变化和文件修改
      fileList: {
        deep: true,
        handler (newVal) {
          this.list = this.fileList
          this.$emit('fileChange', newVal)
        }
      }
    },
    methods: {
      isImg (file) {
        const reg = /\.(png|PNG|jpg|JPG|gif|GIF|jpeg|JPEG|webp|WEBP)$/
        return reg.test(file.fileName)
      },
      // fixme 组件升级已弃用该方法 获取上传需要的token
      getUploadToken () {
        return new Promise(async (resolve, reject) => {
          if (this.appId && this.token) return resolve(true)
          const fileMsg = JSON.parse(window.sessionStorage.getItem('fileMsg'))
          if (fileMsg?.appId && fileMsg?.token) {
            this.appId = fileMsg.appId
            this.token = fileMsg.token
            resolve(true)
          } else {
            const res = await this.$api.common.getUploadToken()
            if (res.code === 0) {
              this.appId = res.data.appId
              this.token = res.data.token
              window.sessionStorage.setItem('fileMsg', JSON.stringify({
                appId: this.appId,
                token: this.token
              }))
              resolve(true)
            } else {
              this.disabled = true
              this.$message.error('获取appID失败禁止上传！')
              resolve(false)
            }
          }
        })
      },
      // 上传
      async doUpload () {
        // 只能上传一个文件时
        if (!this.moreAmount && this.list.length) {
          this.$message.warning(this.moreAmountWarn)
          return
        }
        // 上传
        nineUpload({
          accept: this.accept,
          multiple: this.multiple,
          // appId,
          // token,
          onPickFiles: async files => { // files是文件对象，如果要对文件进行处理，处理完成之后要在返回对象里加上files对象
            if (window.nineUploadData) {
              return window.nineUploadData
            }
            try {
              // 获取appId和token。可以把这个appId和token缓存起来，没必要每次都请求
              const { code = 0, userMsg = '', data = { appId: '', token: '' } } = await this.$api.common.getUploadToken()
              if (code === 0) {
                window.nineUploadData = data
                setTimeout(() => { // appId和token30分钟过期，要清理一下
                  window.nineUploadData = null
                }, 30 * 60 * 1000)
                return data
              } else {
                this.$message.error(userMsg)
              }
            } catch (e) {
              this.$message.error(e)
            }
          },
          onProgress: ({ percent, fileIndex, fileCount }) => {
            this.percent = percent
            this.fileIndex = fileIndex
            this.fileCount = fileCount
          },
          form: {
            collection: this.collection
          }
        }).then(({ res, err }) => {
          if (!this.multiple) {
            res = [res]
          }
          res = res.map(item => {
            let i = { fid: item.fid, fileName: item.fileName, type: 1 }
            if (this.addSize) {
              i.size = item.size
            }
            // 上传成功后的url根据你的pathKey给你返回
            i[this.pathKey] = item.fileUrl
            return i
          })
          if (res.length) {
            let filelist = [...this.list]
            filelist = [...filelist, ...res]
            this.$emit('update:fileList', filelist)
            this.$emit('change', filelist)
          }
          err?.map(i => {
            this.$message.info(`${i.name}上传失败,${i.err.message}`)
          })
        })
      },
      // 删除
      deleteFile (item, index) {
        // 单项数据流，防止数据混乱和多次触发change 事件
        let newFileList = Object.assign([], this.list)
        newFileList = newFileList.filter(it => it.fid !== item.fid)
        this.$emit('update:fileList', newFileList)
        // 有id表示在数据库中存储（需要回传id进行删除），没有表示本地上传，只上传到了文件服务器中，没有上传到你的模块数据库中
        if (item.id) {
          // 有id就让后端调文件服务器的删除接口
          let delList = Object.assign([], this.delList)
          delList.push(item.id)
          delList = [...new Set(delList)]
          this.$emit('update:delList', delList)
          this.$emit('delete', item, index)
        } else {
        // 接文件服务器的删除接口
        }
      },
      // 下载文件
      downloadFile (file) {
        saveAs(file[this.pathKey], file.fileName)
      },
      renameFile (file) {
        // todo 需要有异常兜底机制, 防止后端没有返回附件id的情况
        // if (!file.id) {
        //   this.$message.warn('无法修改附件名称,请截屏联系开发处理')
        //   new Error()
        //   return
        // }
        this.$indicator.open()
        this.$api.common.renameFile({
          id: file.id,
          filename: file.fileName
        }).then((res) => {
          if (res.code === 0) {
            this.$message.success(res.userMsg || '修改成功')
          } else {
            this.$message.error(res.userMsg || '修改失败,请重试')
          }
        }).finally(() => {
          this.$indicator.close()
        })
      }
    },
  }
</script>

<style lang="scss" scoped>
  $preview-item-width: 100px;

.noImg-warp{
  width: $preview-item-width;
  height: $preview-item-width;
  border-radius: 4px;
  padding:10px;
  background: rgba(200,200,200,0.3);
  margin-right: 12px;
  margin-bottom: 8px;
}

.upload-btn {
  width: $preview-item-width;
  height: $preview-item-width;
  margin: 0 12px 8px 0;
  border-radius: 4px;
  font-size: 30px;
  border: 1px dashed rgba(0, 0, 0, 0.2)
}

.img-style {
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  padding: 6px;
  margin-right: 20px
}
.img-wrap{
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  min-height: 30px;
  .img-style{
    margin: 0 12px 8px 0;
  }
  margin-top: 4px;
}

.action-warp{
  position: absolute;
  width: $preview-item-width;
  bottom: 10px;
  left: 7px;
  display: flex;
  align-items: center;
  > div,
  > a{
    flex: 1;
  }
  a{
    text-align: center;
  }
}
.edit-file {
  display: flex;
  align-items: center;
}
.edit-file {
  .file-name {
    width: 235px;
  }
}
</style>
