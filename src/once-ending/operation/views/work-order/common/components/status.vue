<script lang="jsx">
  import { defineComponent } from 'vue'
  import { options } from '../model/constants.js'

  export default defineComponent({
    props: {
      statusNum: {
        type: Number,
        default: 0
      }
    },
    render () {
      const { statusNum } = this
      const { workStatusOptions } = options
      const statusObj = workStatusOptions.find(item => item.value === statusNum)
      return (
      <span>
        <i class={['status', `level${statusNum + 1}`]}/>
        <i >{ statusObj?.label || '-'}</i>
      </span>
    )
    }
  })
</script>

<style lang="scss" scoped>
  $statu-icon-size: 8px;
  // 工单状态颜色
  $status-colors:
  (
    1: #239DFC,
    2: #239DFC,
    3: #239DFC,
    4: #0BBE69,
    5: #F15643,
    6: #F15643,
    7: #239DFC,
    8: #239DFC,
    9: #F15643,
    10: #239DFC,
  );
  .status{
    display: inline-block;
    width: $statu-icon-size;
    height: $statu-icon-size;
    border-radius: $statu-icon-size;
    margin-right: 5px;

    @each $statu, $color in $status-colors{
      &.level#{$statu}{
        background: $color;
      };
    }
  }
</style>
