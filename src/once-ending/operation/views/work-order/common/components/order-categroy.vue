<template>
  <a-tree-select
    :treeData="treeData"
    v-model="valueLocal"
    showSearch
    allowClear
    showType="SHOW_CHILD"
    treeNodeFilterProp='label'
    :maxTagCount="maxTagCount"
    searchPlaceholder="请输入"
    style="width: 100%"
    v-bind="$attrs"
    :dropdownStyle="_dropdownStyle"
    :treeCheckable="treeCheckable"
    @change="onChange"
  />
</template>

<script>
  import { treeToArray, treeWalk } from '~/util/treeUtils'

  export default {
    name: 'OrderCategroy',
    model: {
      prop: 'value',
      event: 'change'
    },
    props: {
      value: null,
      maxTagCount: {
        type: Number,
        default: 0
      },
      /**
       * 传入对树数据操作的函数
       * @param {Array<{ id: string, label: string, children?: Array<any>}>} treeData
       * @returns treeData
       */
      filterNode: {
        type: Function,
        default: treeData => treeData
      },
      treeCheckable: {
        type: <PERSON>olean,
        default: false
      },
      // 只能选择末节点
      leafOnly: {
        type: Boolean,
        default: false
      },
      dropdownStyle: null
    },
    computed: {
      _dropdownStyle () {
        return {
          maxHeight: '300px',
          ...this.dropdownStyle
        }
      }
    },
    watch: {
      value: {
        deep: true,
        immediate: true,
        handler (value) {
          if (!value) {
            this.valueLocal = undefined
          } else {
            let tmpValue = this.value
            if (!Array.isArray(tmpValue)) tmpValue = [tmpValue]
            this.valueLocal = tmpValue.map(it => typeof it === 'object' ? String(it.id) : String(it))
          }
        }
      }
    },
    data () {
      return {
        treeData: [],
        valueLocal: []
      }
    },
    created () {
      this.loadTreeData()
    },
    methods: {
      async loadTreeData () {
        let treeData = null
        try {
          const { code, data, userMsg } = await this.$api.workOrder.fetchWorkOrderKinds({ enable: true })
          if (code === 0) {
            treeData = data
            sessionStorage.setItem('orderCategroy', JSON.stringify(data))
          }
        } catch (e) {
          const orderCategroy = sessionStorage.getItem('orderCategroy')
          treeData = orderCategroy ? JSON.parse(orderCategroy) : []
        }
        let treeDataFlatten = treeToArray(treeData)
        treeDataFlatten.forEach(it => {
          it.value = it.id
          it.label = it.name
        })
        treeDataFlatten.sort((a, b) => a.key > b.key ? 1 : -1)
        if (this.filterNode) {
          treeData = this.filterNode(treeData)
        }

        if (this.leafOnly) {
          // this.treeCheckable = true
          treeWalk(treeData, node => (node.disabled = !!node?.children?.length))
        }

        Object.assign(this, { treeData, treeDataFlatten })
      },

      onChange (value, label, extra) {
        value = Array.isArray(value) ? value.filter(item => item) : value
        this.$emit('change', value, label, extra)
      }
    }
  }
</script>
