<script lang="jsx">
  import { defineComponent } from 'vue'
  import { options } from '../model/constants.js'
  export default defineComponent({
    props: {
      typeNum: {
        type: Number,
        default: 0
      }
    },
    render () {
      const { typeNum } = this
      const typeEumns = options.legalDutyOptions.map(item => item.label)
      return <span>{ typeEumns[typeNum] }</span>
    }
  })
</script>
