
import { productCountLabel } from './useRender.jsx'

export default ({ props }) => {
  const { changeFeild } = props
  return (
    <a-form-item label={productCountLabel}>
      <a-input-number
        value={props.form.productCount}
        min={0}
        max={99999}
        disabled={props.stateDisabled}
        onChange={
          e => { changeFeild(e, 'productCount') }
        }
      />
      <p class="tips">输入0~99999之间的整数</p>
    </a-form-item>
  )
}
