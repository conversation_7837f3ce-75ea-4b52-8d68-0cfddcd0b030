<script type='text/jsx' lang="jsx">
  import { computed, onMounted, watch, getCurrentInstance } from 'vue'
  import { message } from 'ant-design-vue'
  import { useCreateState } from './use-state.js'
  import useDetailInfo from '../../model/useDetailInfo.js'

  import { options } from '../../model/constants.js'
  import OrderCategroy from '../order-categroy'
  import DutySelector from '../order-component/duty-selector'
  import customUploader from '../custom-uploader'
  import KindScores from './type-order/kindScores.vue'
  import TypeOrderBase from './type-order/typeBase.jsx'
  import TypeOrder4 from './type-order/type4.jsx'
  import TypeOrder5 from './type-order/type5.jsx'
  import { NiStaffSelect } from '@jiuji/nine-ui'
  import WorkOrder from '@operation/api/work-order'

  export default {
    name: 'createOrder',
    components: {
      DutySelector,
      OrderCategroy,
      KindScores,
      customUploader,
      TypeOrderBase,
      TypeOrder4,
      TypeOrder5,
      NiStaffSelect
    },
    setup (_) {
      const { proxy } = getCurrentInstance()
      const {
        state,
        setCurrentOrderForm,
        setCreatOrderIsShow,
        setFrontEndUser,
        handerSubmit
      } = useCreateState()
      const { editDetailInfo, featchDetail } = useDetailInfo()
      const getDetail = async () => {
        await featchDetail(state.currentWorkOrderId)
        if (editDetailInfo) setCurrentOrderForm(editDetailInfo)
      }
      const judgeUserBackstage = () => {
        WorkOrder.judgeUserBackstage().then(res => {
          if (res.code === 0 && res.data) {
            setFrontEndUser(true)
          } else {
            setFrontEndUser(false)
          }
        })
      }
      const MAX_WORDS = 1000
      const userInfo = computed(() => proxy.$store.state.userInfo).value
      const isEdit = computed(() => state.editType === 'edit').value
      const isData = computed(() => state.editType === 'edit' || state.editType === 'fastCreate')
      const creatOrderset = () => {
        // 仅有跨部门对接权限，默认为跨部门对接 4
        const isOnlyCrossRank = !userInfo.Rank.includes('xjgd')
        if (isOnlyCrossRank) setCurrentOrderForm(4, 'type')
      }

      const handleDescription = e => {
        if (e.target.value?.length > MAX_WORDS) {
          message.info('字数不能太多，请适当删减！')
          const description = state.currentOrderForm.description.substring(0, MAX_WORDS)
          setCurrentOrderForm(description, 'description')
        }
      }
      const updateFileList = v => {
        setCurrentOrderForm(v, 'accessoriesList')
      }

      watch(
        () => state.currentOrderForm?.type,
        val => {
          // 质量流程类的检查，责任主要设置成责任人
          if (val === 5) setCurrentOrderForm(1, 'legalDuty')
        }
      )

      onMounted(() => {
        creatOrderset()
        if (isData.value) getDetail()
        if (proxy.$tnt.xtenant === 0) judgeUserBackstage()
      })

      const sendUsersChange = function (value, outputObject) {
        const output = outputObject.map(d => ({ id: d.staffId, name: d.staffName }))
        const all = state.currentOrderForm.sendUsers ? state.currentOrderForm.sendUsers.concat(output) : output
        const val = all.filter(d => value.includes(d.id))
        const keys = []
        const arr = []
        val.map(d => {
          if (!keys.includes(d.id)) {
            keys.push(d.id)
            arr.push(d)
          }
        })

        setCurrentOrderForm(arr, 'sendUsers')
      }

      return {
        state,
        userInfo,
        isEdit,
        isData,
        setCreatOrderIsShow,
        setFrontEndUser,
        setCurrentOrderForm,
        updateFileList,
        handleDescription,
        handerSubmit,
        sendUsersChange
      }
    },

    render () {
      const {
        state,
        userInfo,
        // edit不可编辑
        isEdit,

        setCreatOrderIsShow,
        setCurrentOrderForm,
        handleDescription,
        updateFileList,
        handerSubmit,
        sendUsersChange
      } = this
      const {
        creatOrderIsShow,
        currentOrderForm,
        currentOrderForm: {
          status
        },
        saveOrderFormLoading,
        editType
      } = state

      if (!currentOrderForm) return
      const outputOptions = options.orderTypeOptions.filter(item => item.isOutput)
      const frontEndUserOptions = state.frontEndUser ? options.orderTypeOptions : options.orderTypeOptions.filter(item => item.value !== 1)
      const orderTypeOptions = this.$tnt.xtenant === 0 ? frontEndUserOptions : outputOptions

      const title = isEdit ? '编辑工单' : '创建工单'
      // 是否为创建人
      const createUser = currentOrderForm?.userId === userInfo.UserID
      // 按状态 [0待接收、1待整改、2待验收、4申诉中、6整改超]
      // 可编辑字段：1.责任人、2.责任门店、3.工单分类、4.分值、5.工单内容、6.整改时间
      // 不可改1.工单抄送人、2.覆盖量、3.分值生效时间、4.分值关联对象、 ?5.添加附件
      const stateDisabled = isEdit && (!createUser || (![0, 1, 2, 4, 6].includes(status)))
      // 只有【发出人】在工单【未完成:3】状态下可编辑；-->(附件、工单提交)
      const submitDisabled = isEdit && (!createUser || state.currentOrderForm?.status === 3)

      return (
        <a-modal
        title={ title}
        width={ 580 }
        visible={ creatOrderIsShow }
        footer={ false }
        onCancel={ () => { setCreatOrderIsShow(false) } }
      >
        <a-form
          form={ currentOrderForm }
          label-col={{ span: 5 }}
          wrapper-col={{ span: 18 }}>
          <div class="order-form">
            <a-form-item label="工单类型" required>
              <a-select
                v-model={ currentOrderForm.type }
                disabled={ isEdit }
                getPopupContainer={triggerNode => triggerNode.parentNode}
              >
                {
                  orderTypeOptions.map(item => (
                    <a-select-option
                      value={item.value}
                      key={item.value}
                      disabled={item.rank && !userInfo.Rank.includes(item.rank)}
                      >
                      {item.label}
                    </a-select-option>
                  ))
                }
              </a-select>
            </a-form-item>
            {
              // 责任人主体
              <DutySelector editType={editType} form={ currentOrderForm } disabled={ isEdit }/>
            }
            <a-form-item label='工单抄送人'>
            <NiStaffSelect
              value={ currentOrderForm.sendUsers ? currentOrderForm.sendUsers.map(d => d.id) : [] }
              multiple
              placeholder="选择抄送人"
              disabled={ isEdit }
              onChange={ (value, outputObject) => { sendUsersChange(value, outputObject) } }
            />
            </a-form-item>
            { // 总部检查、分公司检查、城市经理检查
              [1, 2, 3].includes(currentOrderForm.type) && <TypeOrderBase
                form={ currentOrderForm }
                stateDisabled={ stateDisabled }
                editDisabled={ isEdit }
                changeFeild={ setCurrentOrderForm }
              />
            }
            { // 跨部门对接
              currentOrderForm.type === 4 && <TypeOrder4
                form={ currentOrderForm }
                stateDisabled={ stateDisabled }
                editDisabled={ isEdit }
                changeFeild={ setCurrentOrderForm }
              />
            }
            { // 跨部门对接
              currentOrderForm.type === 4 && this.$tnt.xtenant < 1000 && <a-form-item label="大件采购单号">
                <a-input-number
                  v-model={ currentOrderForm.largePurchaseId}
                  disabled={stateDisabled}
                  min={0}
                  precision={0}
                />
              </a-form-item>
            }
            { // 质量流程检查
              currentOrderForm.type === 5 && <TypeOrder5
                form={ currentOrderForm }
                stateDisabled={ stateDisabled }
                disabled={ isEdit }
                changeFeild={ setCurrentOrderForm }
              />
            }

            <a-form-item label='添加附件'>
              <custom-uploader
                class="reset-mt"
                disabled={submitDisabled}
                createUser={createUser}
                showThumbnail={true}
                isDetail={true}
                collection="oa-operate"
                showRename={false}
                fileList={currentOrderForm.accessoriesList}
                {...{ on: { 'update:fileList': updateFileList } }}
              />
            </a-form-item>
            <a-form-item label='工单内容' required>
              <div class="area-wrap">
                <a-textarea
                  disabled={stateDisabled}
                  auto-size={{ minRows: 3, maxRows: 5 }}
                  v-model={ currentOrderForm.description }
                  onChange={ handleDescription }
                />
                <i class='text-tips'>{
                  currentOrderForm.description?.length ?? 0
                }/1000</i>
              </div>
            </a-form-item>
          </div>
          <a-row class="mt-8">
            <a-col span={4} push={19}>
              <a-button
                class="margin-right"
                type="primary"
                loading={saveOrderFormLoading}
                disabled={submitDisabled}
                onClick={handerSubmit}
                >提交工单</a-button>
            </a-col>
          </a-row>
        </a-form>
      </a-modal>
      )
    }
  }
</script>

<style lang='scss' scoped>
  .order-form{
    max-height: 68vh;
    overflow-y: auto;
    overflow-x: hidden
  }
.tips{
  position: absolute;
  font-size: 0.8em;
  margin-top: -10px;
  color: #999;
}
.reset-mt{
  margin-top: -14px;
}
  .area-wrap{
    position: relative;
    .text-tips {
      position: absolute;
      bottom: -1.2em;
      right: 0;
      font-size: 12px;
      line-height: 1.45;
      color: red;
    }
  }
:deep(.ant-form-item-children){
  width: 100%;
  .ant-input-number,
  .ant-calendar-picker{
    width: 100%;
  }
}
.create-order-people{
  :deep(.ant-form-item-control) {
    line-height: 32px !important;
  }
}
</style>
