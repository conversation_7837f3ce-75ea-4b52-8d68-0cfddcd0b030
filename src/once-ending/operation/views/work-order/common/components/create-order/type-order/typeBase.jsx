import { getCurrentInstance } from 'vue'
import { options } from '../../../model/constants.js'
import { numberLabel } from './useRender.jsx'

export default ({ props }) => {
  const { changeFeild } = props
  const { $tnt } = getCurrentInstance().proxy
  return (
    <div>
      <KindScores
        value={ props.form.kindScores }
        disabled={ props.stateDisabled }
        onChange={
          (val, remove) => {
            changeFeild(val, 'kindScores')
            changeFeild(remove, 'removeKindIds')
          }
        }
      />
      <a-form-item label={ numberLabel } required>
        <a-input-number
          value={props.form.checkCount}
          onChange={ v => {
            changeFeild(v, 'checkCount')
          }}
          min={0}
          disabled={props.editDisabled}
        />
      </a-form-item>
      <a-form-item label='分值生效时间' required>
        <a-date-picker
          show-time
          disabled={props.editDisabled}
          value={props.form.scoreEffectiveTime}
          value-format="YYYY-MM-DD HH:mm:ss"
          onChange={(moment, str) => { changeFeild(str, 'scoreEffectiveTime') }}
        />
      </a-form-item>
      <a-form-item label='整改时间' required>
        <a-date-picker
          show-time
          disabled={props.stateDisabled}
          value={props.form.predictTime}
          value-format="YYYY-MM-DD HH:mm:ss"
          onChange={(moment, str) => { changeFeild(str, 'predictTime') }}
        />
      </a-form-item>
      <a-form-item label='分值关联对象' required>
        <a-checkbox-group
          value={props.form.scoreChains}
          onChange={ v => {
            changeFeild(v, 'scoreChains')
          }}
          disabled={ props.editDisabled}
          options={ $tnt.xtenant < 1000 ? options.scoreChainOptionsJiuJi : options.scoreChainOptions }
        />
      </a-form-item>
    </div>
  )
}
