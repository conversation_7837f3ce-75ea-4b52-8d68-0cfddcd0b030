<script type='text/jsx' lang="jsx">
  import { ref, defineComponent, watch } from 'vue'
  import OrderCategroy from '../../order-categroy.vue'
  import { scoreLabel } from './useRender.jsx'

  export default defineComponent({
    props: {
      value: {
        type: Array,
        default: () => ([])
      },
      removeKindIds: {
        type: Array,
        default: () => ([])
      },
      disabled: {
        type: Boolean,
        default: 'false'
      }
    },
    setup (props, { emit }) {
      const kindScores = ref([])
      const removeKindIds = ref([])

      const addKinds = (idx) => {
        if (idx === 0) {
          let kindItem = {
            kindId: undefined,
            score: undefined,
            disabled: false,
          }
          kindScores.value.push(kindItem)
        } else {
          const item = kindScores.value[idx]
          if (item.id && !removeKindIds.value.includes(item.id)) {
            removeKindIds.value.push(item.id)
          }
          kindScores.value.splice(idx, 1)
        }
        emit('change', kindScores.value, removeKindIds.value)
      }
      watch(
        () => props.value,
        val => {
          kindScores.value = val?.length ? val : [
            {
              kindId: undefined,
              score: undefined,
              disabled: false,
            }
          ]
          emit('change', val, removeKindIds.value)
        },
        {
          deep: true,
          immediate: true
        }
      )

      watch(() => props.removeKindIds,
            val => {
              removeKindIds.value = val
              emit('change', kindScores.value, val)
            },
            {
              deep: true,
              immediate: true
            })

      return {
        kindScores,
        addKinds
      }
    },

    render () {
      const {
        disabled,
        kindScores = [],
        addKinds
      } = this
      return (
        <a-form
          label-col={{ span: 10 }}
          wrapper-col={{ span: 14 }}>
          {
            kindScores.map((item, idx) => (
              <ARow gutter={12}>
                <ACol span={12}>
                  <a-form-item label="工单分类" required>
                    <OrderCategroy
                      leafOnly
                      placeholder="请选择分类"
                      disabled={ disabled }
                      v-model={item.kindId}
                      getPopupContainer={triggerNode => triggerNode.parentNode}
                    />
                  </a-form-item>
                </ACol>
                <ACol span={9}>
                  <a-form-item label={ scoreLabel } required>
                    <a-input-number
                      v-model={item.score}
                      disabled={ disabled }
                      onChange={ v => {
                        item.score = v
                      }}
                    />
                  </a-form-item>
                </ACol>
                <ACol span={2}>
                  <a-button
                    disabled={ disabled || (idx === 0 && kindScores.length > 20) }
                    class="margin-top"
                    type={idx === 0 ? 'primary' : 'danger'}
                    size="small"
                    onClick={ () => { addKinds(idx) }}
                  >
                    { idx === 0 ? '＋' : '－' }
                  </a-button>
                </ACol>
              </ARow>
            ))
          }
        </a-form>
      )
    }
  })
</script>
