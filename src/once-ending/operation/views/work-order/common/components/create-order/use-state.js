import { reactive, inject, provide, getCurrentInstance } from 'vue'
import { cloneDeep, pick } from 'lodash'
import {
  CREATE_WORK_ORDER,
  UPDATE_WORK_ORDER,
} from '@operation/store/modules/work-order/action-types'
import moment from 'moment'
import { message } from 'ant-design-vue'

const key = Symbol('createState')

export function useCreateState () {
  return inject(key)
}

// 工单字段
const PROP_KEEP = { // 新建保留字段：8个
  type: undefined, // 工单类型
  checkCount: undefined, // 覆盖量
  scoreEffectiveTime: undefined, // 分值生效时间
  predictTime: undefined, // 整改时间
  scoreChains: undefined, // 分值关联对象
  description: undefined, // 工单内容
  legalDuty: undefined, // 责任主体（0 门店 1 责任人, 2 责任部门）
  kindsIds: undefined, // 工单分类(无分值)
  kindScores: [{ // 工单分类、分值
    kindId: undefined,
    score: undefined,
    disabled: false,
  }]
}
const PROP_RESET = { // 11个字段
  areaIds: undefined,
  departIds: undefined,
  departId: undefined,
  score: undefined, // 分类
  productCount: undefined, // 数量
  accessoriesList: undefined, // 附件
  peoplesList: [], // 责任人
  people: {}, // 责任人
  schemeTime: undefined, // 方案出具时间
  hasSecret: false, // 是否密送
  sendUsers: undefined, // 抄送人
  removeKindIds: [], // 删除的工单分类id
  largePurchaseId: undefined, // 大件采购单号id
}
export function createCreateState (fetchData) {
  const { proxy } = getCurrentInstance()
  const state = reactive({
    creatOrderIsShow: false,
    frontEndUser: false,
    editType: '', // create, edit,fastCreate
    currentWorkOrderId: null,
    currentOrderForm: { // 新建工单(表单)
      ...cloneDeep(PROP_KEEP),
      ...cloneDeep(PROP_RESET)
    },
    saveOrderFormLoading: false,
  })

  const setObj = objKey => (val, key) => {
    if (key && typeof key === 'string') {
      state[objKey][key] = val
    } else {
      state[objKey] = val
    }
  }
  const setCurrentOrderForm = setObj('currentOrderForm')
  // reset部分 工单类型、责任主体、工单分类、分值、覆盖量、分值生效时间、整改时间、分值关联对象、工单内容
  // @ isAll {Boolean} 是否是全部重置：不传为部分重置
  const resetCurrentOrderForm = (isAll) => {
    let keepProp = {} // 保留
    if (!isAll) {
      const keepKey = Object.keys(PROP_KEEP)
      keepProp = pick(state.currentOrderForm, keepKey)
    }
    const newForm = {
      ...cloneDeep(PROP_KEEP),
      ...cloneDeep(PROP_RESET),
      ...keepProp,
    }
    setCurrentOrderForm(newForm)
  }

  const setSaveOrderFormLoading = val => { state.saveOrderFormLoading = val }
  const setCreatOrderIsShow = val => { state.creatOrderIsShow = val }
  const setFrontEndUser = val => { state.frontEndUser = val }
  const setEditType = val => { state.editType = val }
  const setCurrentWorkOrderId = val => { state.currentWorkOrderId = val }

  /**
   * 打开新建与编辑【工单表单】
   * @param type{String}: 新建:'create' 编辑:'edit'
   */
  const editOrder = (type, workOrderId) => {
    setEditType(type)
    if (workOrderId) {
      setCurrentWorkOrderId(workOrderId)
    } else {
      resetCurrentOrderForm(true) // 清空表单
    }
    setCreatOrderIsShow(true)
  }
  const handerSubmit = () => {
    const { kindScores } = state.currentOrderForm
    const isPass = _checkParams() // 校验参数
    if (!isPass) return
    const params = _getParams()
    // 1,2,3类别工单
    if ([1, 2, 3].includes(params.type)) {
      // 分数大于0需要二次确认
      const scores = kindScores.map((it) => it.score).every((it) => it > 0)
      if (scores) {
        proxy.$confirm({
          title: '确认创建？',
          content: '正分分值代表加分，提交后工单直接转已完成，请确认是否加分',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            submitCurrentOrder(params)
          },
        })
      } else {
        // 若整改时间的设置距离当前时间小于3小时则进行提示
        const curTimeAdd3 = moment().add(3, 'H').format('YYYY-MM-DD HH:mm:ss')
        const isLessThan3H = params.predictTime < curTimeAdd3
        if (isLessThan3H) {
          proxy.$confirm({
            title: '确认创建？',
            okText: '确定',
            cancelText: '取消',
            content:
              '您所创建的工单整改时间距离当前时间小于3小时，整改人员若超期未整改会扣除10积分，请确认时间是否设置正确！',
            onOk: () => {
              submitCurrentOrder(params)
            },
          })
          return
        }
        submitCurrentOrder(params)
      }
    } else {
      submitCurrentOrder(params)
    }
  }
  // 提交工单
  const submitCurrentOrder = async (params) => {
    const { editType } = state
    // 设置下次默认值缓存
    _setCreatOrderStorage(params)
    const submitType =
      editType === 'create' || editType === 'fastCreate' ? CREATE_WORK_ORDER : UPDATE_WORK_ORDER
    setSaveOrderFormLoading(true)
    const res = await proxy.$store.dispatch(
      `operation/workOrder/${submitType}`,
      params
    )
    setSaveOrderFormLoading(false)
    if (!res) return
    const { code, data, userMsg } = res
    if (code === 0) {
      message.success('提交成功')
      if (editType === 'edit' || editType === 'fastCreate') {
        setCreatOrderIsShow(false)
      } else if (editType === 'create') {
        resetCurrentOrderForm() // 保留部分字段
      }
      if (editType === 'fastCreate') {
        proxy.$router.push(`/work-order/detail/${data}`)
      } else {
        if (fetchData)fetchData()
      }
    } else {
      message.error(userMsg)
    }
  }
  // 参数校验
  const _checkParams = () => {
    const isCreate = state.editType === 'create' || state.editType === 'fastCreate'
    const {
      type,
      description,
      kindsIds,
      kindScores,
      areaIds,
      checkCount,
      scoreEffectiveTime,
      predictTime,
      scoreChains,
      legalDuty,
      peoplesList,
      people,
      departIds,
      schemeTime,
    } = state.currentOrderForm
    if (!type) {
      message.error('请选择工单类型')
      return false
    }

    // 责任人与责任主体检查逻辑
    if (type !== 5 && isCreate) {
      if (legalDuty === undefined) {
        message.error('请选择责任主体')
        return false
      }
      if (legalDuty === 0 && !areaIds?.length) {
        message.error('请添加责任门店')
        return false
      }
      if (legalDuty === 0 && areaIds?.length > 50) {
        message.error('责任门店已超最多50家的限制, 请减至50家以下')
        return false
      }
      if (legalDuty === 2 && !departIds?.length) {
        message.error('请添加责任部门')
        return
      }
    }
    if (
      legalDuty === 1 &&
      ((isCreate && !peoplesList?.length) || (!isCreate && !people.id))
    ) {
      [1, 2, 3].includes(type) && proxy.$tnt.xtenant < 1000 ? message.error('请添加主要责任人') : message.error('请添加责任人')
      return false
    }
    if (type === 5) {
      if (!kindsIds) {
        message.error('请选择工单分类')
        return false
      } else {
        const kindScores = [
          {
            kindId: parseInt(kindsIds),
            score: null,
          },
        ]
        setCurrentOrderForm(kindScores, 'kindScores')
      }
    }
    // 类型为[1,2,3]必检项
    if ([1, 2, 3].includes(type)) {
      const kindIds = kindScores.map((it) => it.kindId)
      const isRepeat = _isRepeat(kindIds)
      if (!kindScores?.length) {
        message.error('请选择工单分类和填写相应分值')
        return false
      } else if (isRepeat) {
        message.error('有重复分类，请重新填写分类与分值')
        return false
      } else {
        const isPass = _forCheek(kindScores)
        if (!isPass) return false
      }
      if (checkCount == null) {
        message.error('请输入覆盖量')
        return false
      }
      if (!scoreEffectiveTime) {
        message.error('请选择分值生效时间')
        return false
      }
      if (!predictTime) {
        message.error('整改时间不能为空')
        return false
      }
      if (!scoreChains?.length) {
        message.error('请选择分值关联对象')
        return false
      } else {
        const scoreChain =
          scoreChains?.length > 1 ? scoreChains.join(',') : scoreChains[0] + ''
        setCurrentOrderForm(scoreChain, 'scoreChain')
      }
    }
    // 类型为[5]必检项
    if (type === 5 && !schemeTime) {
      message.error('方案出具时间不能为空')
      return false
    }

    if (!description) {
      message.error('工单内容不能为空')
      return false
    }
    return true
  }
  const _isRepeat = (arr) => {
    const hash = {}
    for (let i in arr) {
      if (hash[arr[i]]) {
        return true
      }
      hash[arr[i]] = true
    }
    return false
  }
  // 循环校验
  const _forCheek = (arr) => {
    let flag = false
    for (let i = 0; i < arr.length; i++) {
      if (!arr[i].kindId || arr[i].score == null) {
        flag = true
        break
      }
      if (flag) break
    }
    if (!flag) return true

    message.error('有工单分类或分值未填写')
    return false
  }
  // 参数处理
  const _getParams = () => {
    const {
      legalDuty,
      peoplesList,
      people,
      accessoriesList,
      areaIds,
      departId,
      sendUsers,
      type,
    } = state.currentOrderForm
    const params = cloneDeep(state.currentOrderForm)
    // 创建关联工单
    if (state.editType === 'fastCreate') {
      // 当前工单id作为关联工单id
      params.relationId = params.id
      delete params.id
      // 创建关联工单,部门可多选,跟后台交互传departIds,删除departId
      delete params.departId
    }
    // 责任主体处理
    if (legalDuty !== 1) {
      // 主体为门店 / 部门
      delete params.peoplesList
      delete params.people
      if (state.editType !== 'create' && state.editType !== 'fastCreate') {
        // 编辑
        if (legalDuty === 0) {
          // 门店
          delete params.departId
          delete params.departIds
          params.areaId = areaIds[0]
        } else if (legalDuty === 2) {
          // 部门
          params.departId = departId[0]
        }
      }
    } else {
      // 主体为人
      if (state.editType === 'create' || state.editType === 'fastCreate') {
        // 新建
        params.peoplesList = peoplesList.map((d) => {
          return {
            [d.id]: d.name,
          }
        })
        delete params.people
      } else {
        // 编辑
        delete params.departId
        delete params.departIds
        params.people = {
          [people.id]: people.name,
        }
        delete params.peoplesList
      }
    }
    // 跨部门对接没有工单分类
    if (type === 4) {
      params.kindScores = []
      params.kindsIds = undefined
    }

    if (sendUsers?.length) {
      params.sendUsers = sendUsers.map((d) => ({ id: d.id, name: d.name }))
    }
    // 附件参数转换
    if (accessoriesList?.length) {
      params.accessoriesList = accessoriesList.map((item) => {
        const fileNames = item.fileName.split('.')
        const fileFids = item.fid.split('.')
        return {
          accessoryUrl: item.fileUrl,
          accessoryID: fileFids?.length ? fileFids[0] : item.fid,
          postfix: fileNames[fileNames?.length - 1],
          name: item.fileName,
          type: item.type,
        }
      })
    }
    return params
  }
  // 设置个人最后一次选择的跨部门-门店选择 缓存， 做为下次建工单时的默认值
  const _setCreatOrderStorage = (params) => {
    if (params.type === 4 && params.legalDuty === 2) {
      const creatOrderStorage = {
        departIds: params.departIds,
      }
      window.localStorage.setItem(
        `CODStorage${proxy.$store.state.userInfo.UserID}`,
        JSON.stringify(creatOrderStorage)
      )
    }
  }

  const o = {
    state,
    setCreatOrderIsShow,
    setFrontEndUser,
    setEditType,
    setCurrentWorkOrderId,
    setCurrentOrderForm,
    setSaveOrderFormLoading,
    resetCurrentOrderForm,
    editOrder,
    handerSubmit,
    submitCurrentOrder
  }
  provide(key, o)
  return o
}
