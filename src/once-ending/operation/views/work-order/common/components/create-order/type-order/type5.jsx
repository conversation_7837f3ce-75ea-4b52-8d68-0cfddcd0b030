export default ({ props }) => {
  const { changeFeild } = props
  return (
    <div>
      <a-form-item label="工单分类" required>
        <OrderCategroy
          leafOnly
          disabled={ props.stateDisabled }
          placeholder="请选择分类"
          value={ props.form.kindsIds }
          onChange={ v => {
            changeFeild(v, 'kindsIds')
          }}
          getPopupContainer={triggerNode => triggerNode.parentNode}
        />
      </a-form-item>
      <a-form-item label='方案出具时间' required>
        <a-date-picker
          show-time
          value={ props.form.schemeTime }
          value-format="YYYY-MM-DD HH:mm:ss"
          onChange={(moment, str) => { changeFeild(str, 'schemeTime') }}
        />
      </a-form-item>
      <a-form-item label='整改时间'>
        <a-date-picker
          show-time
          disabled={ props.stateDisabled }
          value={ props.form.predictTime }
          value-format="YYYY-MM-DD HH:mm:ss"
          onChange={(moment, str) => { changeFeild(str, 'predictTime') }}
        />
      </a-form-item>
      <a-form-item label='是否密送' >
        <a-checkbox
          disabled={ props.disabled }
          checked={ props.form.hasSecret }
          onChange={ e => {
            changeFeild(e.target.checked, 'hasSecret')
          }}
        />
      </a-form-item>
    </div>
  )
}
