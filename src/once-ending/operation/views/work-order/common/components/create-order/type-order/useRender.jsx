
const numberLabel = h => (
  <a-tooltip>
    <template slot="title">即本次抽查的门店总数，用于之后计算抽查不合格率</template>
    覆盖量 <a-icon type="question-circle" class='blue'/>
  </a-tooltip>
)

const productCountLabel = h => (
  <a-tooltip>
    <template slot="title">若为采销对接，需填上到货总数量</template>
    数量 <a-icon type="question-circle" class='blue'/>
  </a-tooltip>
)

const scoreLabel = h => (
  <a-tooltip>
    <template slot="title">正数代表加分，0代表警告，负数代表扣分</template>
    分值 <a-icon type="question-circle" class='blue'/>
  </a-tooltip>
)
export {
  numberLabel,
  productCountLabel,
  scoreLabel
}
