<script type="text/jsx" lang="jsx">
  import { options } from '../model/constants.js'

  export default {
    name: 'scoreChain',
    props: {
      scoreChainNum: {
        type: [Number, String],
        default: ''
      }
    },
    render () {
      // 两个分总，处理历史脏数据
      const { scoreChainNum, $tnt } = this
      const scoreChainEumns = options.scoreChainOptions.find(item => item.value === parseInt(scoreChainNum))
      const scoreChainEumnsJiuJi = options.scoreChainOptionsJiuJi.find(item => item.value === parseInt(scoreChainNum))
      return <div>
        {
          $tnt.xtenant < 1000
            ? <span class={['tag', `level${scoreChainEumnsJiuJi.value}`]}>
                {scoreChainEumnsJiuJi.label}
              </span>
            : <span class={['tag', `level${scoreChainEumns.value}`]}>
                {scoreChainEumns.label}
              </span>
        }
      </div>
    }
  }
</script>

<style lang="scss" scoped>
  @import '../style.scss';

  .tag {
    display: inline-block;
    border: 1px solid;
    border-radius: 4px;
    padding: 2px 5px;
    font-size: 12px;
    margin: 0 3px 3px 0;
    white-space: nowrap;

    @each $level, $color in $tag-colors{
      &.level#{$level}{
        color: $color;
        border-color: lighten($color, 25%);
        background: lighten($color, 40%);
      };
    }
  }
</style>
