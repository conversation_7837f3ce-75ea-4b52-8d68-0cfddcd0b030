
// var
$gap: 8px;
$detail-content-min-height: calc(100vh - 100px);

$primary-color: #239DFC;
$success-color: #52C487;
$danger-color: #F04134;
$warning-color: #FFBF00;
$info-color: #13C2C2;
$gray-color: #BFBFBF;

$tag-colors:
  (
    1: $danger-color,
    2: $success-color,
    3: $info-color,
    4: $primary-color,
    5: $primary-color,
    6: $success-color,
    7: $info-color,
    8: $primary-color,
    9: $primary-color,
  );
.center-y{
  display: flex;
  align-items: center;
}
.mt-24{
  margin-top: 24px;
}

.detail-content{
  min-height: $detail-content-min-height;
}

.work-order-uls{
  display: flex;
  flex-wrap: wrap;

  li {
    flex: 0 0 30%;
    display: flex;
    margin: 0 24px 24px 0;
    color: $primary-color;

    >i{
      flex:0 0 90px;
      text-align: right;
      margin-right: $gap;
      color: #aaa;

      &.col-2{
        flex:0 0 160px;
      }

      &::after{
          content: ":";
        }
    }
  }
}

