
import { getCurrentInstance, ref } from 'vue'
import {
  FETCH_WORK_ORDER_DETAIL
} from '@operation/store/modules/work-order/action-types'

const detailInfo = ref(undefined) // 详情页所需数据
const editDetailInfo = ref(undefined) // 列表页编辑所需数据

export default function useDetailInfo () {
  const instance = getCurrentInstance().proxy
  const featchDetail = async (workOrderId) => {
    instance.$indicator.open()
    const res = await instance.$store.dispatch(
      `operation/workOrder/${FETCH_WORK_ORDER_DETAIL}`,
      { workOrderId }
    )
    if (!res) return
    instance.$indicator.close()
    const { code, data } = res
    if (code === 0) {
      detailInfo.value = {
        ...data,
        orderKindsOpts: data.kindScores?.length ? data.kindScores.map(it => ({
          label: it.kindName,
          value: it.kindId
        })) : []
      }
      editDetailInfo.value = {
        ...data,
        areaIds: [data.areaId + ''],
        departId: [data.departId],
        departIds: data.legalDuty === 2 ? [data.departId] : [],
        kindsIds: data.kindsId,
        scoreChains: data.scoreChain?.split(',').map(item => parseInt(item)),
        people: {
          id: data.dutyUserId,
          name: data.dutyUser
        },
        peoplesList: data.legalDuty === 1 ? [{
          id: data.dutyUserId,
          name: data.dutyUser
        }] : [],
        sendUsers: data.sendUsers?.length ? data.sendUsers.map((it) => (
          {
            id: it.id,
            name: it.name
          }
        )) : []
      }
    }
  }

  return {
    detailInfo,
    editDetailInfo,
    featchDetail
  }
}
