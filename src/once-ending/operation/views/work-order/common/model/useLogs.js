import { reactive, toRefs, getCurrentInstance } from 'vue'
import { to } from '@common/utils/common'
import { message } from 'ant-design-vue'

export const useDetail = () => {
  const instance = getCurrentInstance().proxy
  const state = reactive({
    pageData: null,
  })
  const featchData = async (workOrderId) => {
    const [err, res] = await to(instance.$store.dispatch('operation/workOrder/fetchWorkOrderDetail', { workOrderId }))
    if (err) throw err
    const { code, data, userMsg } = res
    if (code === 0) {
      state.pageData = data
    } else {
      message.error(userMsg)
    }
  }
  return {
    ...toRefs(state),
    featchData
  }
}

export const useLogs = () => {
  const instance = getCurrentInstance().proxy
  const state = reactive({
    workOrderLogs: null,
    pagination: {
      current: 1,
      size: 50,
    },
  })
  const featchLogs = async (workOrderId) => {
    let params = {
      workOrderId,
      ...state.pagination
    }
    const [err, res] = await to(instance.$store.dispatch('operation/workOrder/fetchWorkOrderLogs', params))
    if (err) throw err
    const { code, data, userMsg } = res
    if (code === 0) {
      state.workOrderLogs = data.records.sort((a, b) => a.createTime < b.createTime ? 1 : -1)
    } else {
      message.error(userMsg)
    }
  }
  return {
    ...toRefs(state),
    featchLogs
  }
}

export const useAddLogs = () => {
  const instance = getCurrentInstance().proxy
  const state = reactive({
    logs: {
      remark: '',
      loading: false
    },
    scheme: {
      remark: '',
      loading: false
    }
  })
  const type = {
    10: 'logs',
    12: 'scheme'
  }
  const changeRemark = (value, operationType) => {
    state[type[operationType]].remark = value
  }
  const addLogs = async (workOrderId, operationType) => {
    if (!state[type[operationType]].remark) {
      message.error('内容不能为空')
      return
    }
    const params = new FormData()
    params.append('workOrderId', workOrderId)
    params.append('operationDescription', state[type[operationType]].remark)
    params.append('operationType', operationType)
    state[type[operationType]].loading = true
    const [err, res] = await to(instance.$store.dispatch('operation/workOrder/addWorkOrderLogs', params))
    if (err) {
      throw err
    }
    const { code, userMsg } = res
    if (code === 0) {
      message.success(userMsg)
      state[type[operationType]].remark = ''
      state[type[operationType]].loading = false
      return true
    } else {
      message.error(userMsg)
      state[type[operationType]].loading = false
      return false
    }
  }
  return {
    ...toRefs(state),
    changeRemark,
    addLogs
  }
}
