const tabList = [
  {
    key: '1',
    tab: '全部',
  },
  {
    key: '2',
    tab: '我发出的',
  },
  {
    key: '3',
    tab: '我接收的',
  },
  {
    key: '4',
    tab: '抄送我的',
  },
]
const options = {
  numTypeOptions: [
    {
      value: 1,
      label: 'ID'
    },
    // {
    //   value: 2,
    //   label: '批次名称'
    // },
  ],
  userTypeOptions: [
    {
      value: 1,
      label: '接收人'
    },
    {
      value: 2,
      label: '发送人'
    },
  ],
  workStatusOptions: [
    {
      value: 0,
      label: '待接收',
      text: '待接收',
      isOutput: true,
    },
    {
      value: 7,
      label: '待出具方案',
      text: '待出具方案',
      isOutput: false,
    },
    {
      value: 1,
      label: '待整改',
      text: '待整改',
      isOutput: true,
    },
    {
      value: 2,
      label: '待验收',
      text: '待验收',
      isOutput: true,
    },
    {
      value: 3,
      label: '已完成',
      text: '已完成',
      isOutput: true,
    },
    {
      value: 4,
      label: '申诉中',
      text: '申诉中',
      isOutput: true,
    },
    {
      value: 9,
      label: '加分审核中',
      text: '加分审核中',
      isOutput: false,
    },
    {
      value: 8,
      label: '出具方案超时',
      text: '出具方案超时',
      isOutput: false,
    },
    {
      value: 6,
      label: '整改超时',
      text: '整改超时',
      isOutput: true,
    }
  ],
  timeTypeOptions: [
    {
      value: 1,
      label: '开单时间'
    },
    {
      value: 2,
      label: '扣分时间'
    },
    {
      value: 3,
      label: '整改时间'
    },
  ],
  kindsIdOptions: [
    {
      value: 1,
      label: '一级板块'
    },
    {
      value: 2,
      label: '二级分类'
    },
    {
      value: 3,
      label: '三级绩效规则'
    }
  ],
  fractionOptions: [
    {
      value: 'scoreGtZero',
      label: '>0',
      text: '>0'
    },
    {
      value: 'scoreEqZero',
      label: '=0',
      text: '=0'
    },
    {
      value: 'scoreLtZero',
      label: '<0',
      text: '<0'
    }
  ],
  scoreChainOptions: [
    { label: '门店', value: 1 },
    { label: '售后主管', value: 5 },
    { label: '售后经理', value: 2 },
    { label: '城市经理', value: 3 },
    { label: '分总', value: 4 },
  ],
  scoreChainOptionsJiuJi: [
    { label: '门店', value: 1 },
    { label: '售后主管', value: 5 },
    { label: '售后经理', value: 2 },
    { label: '城市经理', value: 3 },
    { label: '分总', value: 4 },
    { label: '营销经理', value: 6 },
    { label: '人事经理', value: 7 },
    { label: '物流负责人', value: 8 },
    { label: '运营经理', value: 9 },
  ],
  orderTypeOptions: [
    {
      value: 1,
      label: '总部检查',
      rank: 'xjgd',
      isOutput: true,
    },
    {
      value: 2,
      label: '分公司检查',
      rank: 'xjgd',
      isOutput: true,
    },
    {
      value: 3,
      label: '城市经理检查',
      rank: 'xjgd',
      isOutput: true,
    },
    {
      value: 4,
      label: '跨部门对接',
      isOutput: true,
    },
    {
      value: 5,
      label: '质量流程检查',
      rank: 'xjgd',
      isOutput: false,
    },
  ],
  legalDutyOptions: [
    {
      value: 0,
      label: '门店'
    },
    {
      value: 1,
      label: '责任人'
    },
    {
      value: 2,
      label: '部门'
    },
  ]
}

export {
  tabList,
  options,
}
