import { dateFormat } from '~/util/common'
// tabs data
export const tabs = [
  {
    id: 1,
    name: '分公司维度'
  },
  {
    id: 2,
    name: '总部维度'
  }
]

// 筛选类型options
export const userTypeOptions = [
  {
    value: 1,
    label: '开单人'
  },
  {
    value: 2,
    label: '责任人'
  }
]

// 查看维度options
export const dimensionOptions = [
  {
    value: 1,
    label: '总工单量'
  },
  {
    value: 2,
    label: '扣分工单'
  },
  {
    value: 3,
    label: '警告工单量'
  },
  {
    value: 4,
    label: '加分工单量'
  },
  {
    value: 5,
    label: '扣分'
  },
  {
    value: 6,
    label: '加分'
  }
]

export const childrenColumns = [
  {
    title: '总工单量',
    dataIndex: 'allCount',
    key: 'allCount',
    align: 'center',
    width: '80px',
    filterValue: 1
  },
  {
    title: '扣分工单量',
    dataIndex: 'kouFenCount',
    key: 'kouFenCount',
    align: 'center',
    width: '90px',
    filterValue: 2
  },
  {
    title: '警告工单量',
    dataIndex: 'warnCount',
    key: 'warnCount',
    align: 'center',
    width: '90px',
    filterValue: 3
  },
  {
    title: '加分工单量',
    dataIndex: 'addScoreCount',
    key: 'addScoreCount',
    align: 'center',
    width: '90px',
    filterValue: 4
  },
  {
    title: '扣分',
    dataIndex: 'kouFen',
    key: 'kouFen',
    align: 'center',
    width: '50px',
    filterValue: 5
  },
  {
    title: '加分',
    dataIndex: 'jiaFen',
    key: 'jiaFen',
    align: 'center',
    width: '50px',
    filterValue: 6
  }
]

export const getChildrenColumns = function (type, arr) {
  const children = JSON.parse(JSON.stringify(arr))
  const length = children.length
  return children.map((d, i) => {
    d.dataIndex = `${type}_${d.dataIndex}`
    d.key = `${type}_${d.key}`
    if (i === children.length - 1) {
      d.customCell = function (record, rowIndex) {
        return {
          class: 'last-custom-cell-class'
        }
      }
      d.customHeaderCell = function (column) {
        return {
          class: 'last-custom-header-class'
        }
      }
    } else {
      d.customCell = function (record, rowIndex) {
        return {
          class: 'custom-cell-class'
        }
      }
      d.customHeaderCell = function (column) {
        return {
          class: 'custom-header-class'
        }
      }
    }
    return d
  })
}

// 分公司表头
export const branchColumns = [
  {
    title: '地区',
    dataIndex: 'areaName',
    key: 'areaName',
    className: 'area-name',
    width: 120,
    align: 'center',
    fixed: 'left'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 120,
    align: 'center',
    scopedSlots: {
      customRender: 'operation'
    }
  }
]

export const form = {
  areaIds: [],
  time: [dateFormat(new Date(Date.now(dateFormat(Date.now(), 'yyyy-MM-dd')).valueOf() - 5184000000), 'yyyy-MM-dd'), dateFormat(Date.now(), 'yyyy-MM-dd')],
  orgTime: [dateFormat(new Date(Date.now(dateFormat(Date.now(), 'yyyy-MM-dd')).valueOf() - 5184000000), 'yyyy-MM-dd'), dateFormat(Date.now(), 'yyyy-MM-dd')],
  kindsId: [],
  dimension: [1, 2],
  orgDimension: [1, 2],
  largeArea: false,
  userName: undefined,
  userType: 1,
  timeType: 1,
  legalDuty: undefined,
  scoreChainList: undefined,
}

export const values = {
  addScoreCount: 0,
  allCount: 0,
  jiaFen: 0,
  kouFen: 0,
  kouFenCount: 0,
  warnCount: 0
}

export const hqColumns = [
  {
    title: '工单分类',
    dataIndex: 'kindName',
    key: 'kindName',
    align: 'center',
    width: '120px',
    fixed: 'left'
  },
  {
    title: '总工单量',
    dataIndex: 'allCount',
    key: 'allCount',
    align: 'center'
  },
  {
    title: '扣分工单量',
    dataIndex: 'kouFenCount',
    key: 'kouFenCount',
    align: 'center'
  },
  {
    title: '警告工单量',
    dataIndex: 'warnCount',
    key: 'warnCount',
    align: 'center'
  },
  {
    title: '加分工单量',
    dataIndex: 'addScoreCount',
    key: 'addScoreCount',
    align: 'center'
  },
  {
    title: '扣分',
    dataIndex: 'kouFen',
    key: 'kouFen',
    align: 'center'
  },
  {
    title: '加分',
    dataIndex: 'jiaFen',
    key: 'jiaFen',
    align: 'center'
  },
  {
    title: '覆盖量',
    dataIndex: 'fuGaiCount',
    key: 'fuGaiCount',
    align: 'center'
  },
  {
    dataIndex: 'fuGaiRate',
    key: 'fuGaiRate',
    align: 'center',
    slots: { title: 'fuGaiRateTitle' },
  },
  {
    dataIndex: 'notQualifiedRate',
    key: 'notQualifiedRate',
    align: 'center',
    slots: { title: 'notQualifiedRateTitle' },
  },
  {
    dataIndex: 'appealRate',
    key: 'appealRate',
    align: 'center',
    slots: { title: 'appealRateTitle' },
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: '120px',
    align: 'center',
    scopedSlots: {
      customRender: 'operation'
    }
  }
]

// 设置行class
export const rowClassName = function (record, index, length) {
  if (record.typeBig === 1) {
    return 'big-area'
  } else if (record.typeBig === 0) {
    return 'small-area'
  } else if (index === length - 1) {
    return 'total-row'
  } else {
    return ''
  }
}

// 去工单管理
export const toWorkOrder = function (proxy, record, form, type, kindsIds) {
  const query = {
    startTime: form.time.length ? form.time[0] : '',
    endTime: form.time.length ? form.time[1] : '',
    timeType: form.timeType
  }
  if (type === 1) {
    query.areaIds = record.areaId ? Array.from(new Set(record.areaId.split(','))).join(',') : ''
    query.kindsId = form.kindsId.length ? form.kindsId.join(',') : kindsIds.join(',')
  }
  if (type === 2) {
    query.areaIds = form.areaIds.join(',') || ''
    query.kindsId = record.childId ? Array.from(new Set(record.childId.split(','))).join(',') : ''
    if (form.userName) {
      query.userType = form.userType === 1 ? 2 : 1
      query.userTypeValue = form.userName
    }
  }
  return {
    path: '/work-order',
    query: query
  }
}

export const customHeaderRow = function (column, index) {
  if (index > 1) {
    column.forEach(item => {
      item.className = 'hearder-class'
    })
  }
}
