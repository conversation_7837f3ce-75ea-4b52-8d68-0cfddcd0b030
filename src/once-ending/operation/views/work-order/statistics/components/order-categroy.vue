<template>
  <a-tree-select
    :treeData="treeData"
    v-model="valueLocal"
    showSearch
    allowClear
    show-checked-strategy="SHOW_ALL"
    treeNodeFilterProp='label'
    :maxTagCount="maxTagCount"
    searchPlaceholder="请输入"
    style="width: 100%"
    v-bind="$attrs"
    :dropdownStyle="_dropdownStyle"
    treeCheckable
    @change="onChange"
  />
</template>

<script>
  import { treeToArray, treeWalk } from '~/util/treeUtils'

  export default {
    name: 'OrderCategroy',
    model: {
      prop: 'value',
      event: 'change'
    },
    props: {
      value: null,
      maxTagCount: {
        type: Number,
        default: 1
      },
      /**
       * 传入对树数据操作的函数
       * @param {Array<{ id: string, label: string, children?: Array<any>}>} treeData
       * @returns treeData
       */
      filterNode: {
        type: Function,
        default: treeData => treeData
      },
      // 只能选择末节点
      leafOnly: {
        type: <PERSON>olean,
        default: false
      },
      dropdownStyle: null
    },
    computed: {
      _dropdownStyle () {
        return {
          maxHeight: '300px',
          ...this.dropdownStyle
        }
      }
    },
    watch: {
      value (value) {
        if (!value.length) {
          this.valueLocal = undefined
        } else {
          let tmpValue = this.value
          if (!Array.isArray(tmpValue)) tmpValue = [tmpValue]
          this.valueLocal = tmpValue.map(it => typeof it === 'object' ? String(it.id) : String(it))
        }
      }
    },
    data () {
      return {
        treeData: [],
        valueLocal: []
      }
    },
    created () {
      this.loadTreeData()
    },
    methods: {
      async loadTreeData () {
        let treeData = null
        const { code, data, userMsg } = await this.$api.workOrder.fetchWorkOrderKinds({ enable: true })
        if (code === 0) {
          treeData = data
        }
        let treeDataFlatten = treeToArray(treeData)
        treeDataFlatten.forEach(it => {
          it.value = it.id
          it.label = it.name
        })
        treeDataFlatten.sort((a, b) => a.key > b.key ? 1 : -1)
        if (this.filterNode) {
          treeData = this.filterNode(treeData)
        }
        Object.assign(this, { treeData, treeDataFlatten })
        this.setKindTree()
      },
      setKindTree () {
        const treeData = JSON.parse(JSON.stringify(this.treeData))
        this.$emit('setKindTree', treeData, 'init')
      },
      onChange (value, label, extra) {
        value = Array.isArray(value) ? value.filter(item => item) : value
        this.$emit('change', value, label, extra)
      }
    }
  }
</script>
