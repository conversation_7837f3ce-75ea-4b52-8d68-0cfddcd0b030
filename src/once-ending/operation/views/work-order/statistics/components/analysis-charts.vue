<script type="text/jsx" lang="jsx">
  import { defineComponent, ref, watch, nextTick, reactive, computed, getCurrentInstance } from 'vue'
  import * as echarts from 'echarts5'
  import { Modal, Spin } from 'ant-design-vue'
  import { WORK_ORDER_ANALYSIS } from '@operation/store/modules/work-order/action-types'

  export default defineComponent({
    name: 'analysis-charts',
    props: {
      analysisChartsShow: {
        type: Boolean,
        default: false
      },
      form: {
        type: Object,
        default: () => {
          return {}
        }
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const analysisCharts = ref(null)
      const spinning = ref(false)
      const visible = computed({
        get: () => props.analysisChartsShow,
        set: val => {
          proxy.$emit('update:analysisChartsShow', val)
        }
      })
      const chartData = reactive({
        xAxisData: [],
        seriesData: []
      })

      watch(() => visible.value, () => {
        if (visible.value) {
          nextTick(() => {
            initCharts()
            fetchData()
          })
        }
      }, { immediate: true })

      // 初始化图表
      const initCharts = function () {
        analysisCharts.value = echarts.init(document.getElementById('analysis-charts'))
        setOption()
      }

      // 设置图表
      const setOption = function () {
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '1%',
            containLabel: true
          },
          xAxis:
            {
              type: 'category',
              data: chartData.xAxisData,
              axisTick: {
                show: false,
                alignWithLabel: true
              },
              axisLine: {
                lineStyle: {
                  color: '#E8E8E8'
                }
              },
              axisLabel: {
                color: '#333333',
                interval: 0,
                formatter: function (value) {
                  const length = value.length
                  const provideNum = 2
                  const rowNun = Math.ceil(length / provideNum)
                  let tempStr = ''
                  for (let i = 0; i < rowNun; i++) {
                    const start = i * provideNum
                    const end = start + provideNum
                    const str = i === rowNun - 1 ? value.substring(start, length) : `${value.substring(start, end)}\n`
                    tempStr += str
                  }
                  return tempStr
                }
              }
            },
          yAxis: {
            type: 'value',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              margin: 19,
              color: '#999999'
            },
            splitLine: {
              lineStyle: {
                color: '#E8E8E8',
                type: 'dashed'
              }
            }
          },
          series: {
            type: 'bar',
            barWidth: 40,
            barGap: '30%',
            itemStyle: {
              color: '#239DFC'
            },
            data: chartData.seriesData
          }

        }
        analysisCharts.value.setOption(option)
      }

      // 初始化数据
      const fetchData = async function () {
        const {
          kindsId,
          areaIds,
          time
        } = props.form
        const params = {
          type: 1,
          kindWorks: kindsId,
          areaId: areaIds,
          startTime: time && time.length ? `${time[0]} 00:00:00` : '',
          endTime: time && time.length ? `${time[1]} 23:59:59` : ''
        }
        const res = await proxy.$store.dispatch(`operation/workOrder/${WORK_ORDER_ANALYSIS}`, params)
        if (res) {
          const { data } = res
          chartData.xAxisData = []
          chartData.seriesData = []
          data.forEach(item => {
            chartData.xAxisData.push(item.name)
            chartData.seriesData.push(item.count)
          })
          setOption()
        }
      }

      // 弹窗完全关闭回调
      const afterClose = function () {
        proxy.$emit('destroyModal')
      }

      // 关闭弹框
      const cancel = function () {
        visible.value = false
      }

      return {
        afterClose,
        visible,
        cancel,
        spinning
      }
    },
    render () {
      const { afterClose, visible, cancel, spinning } = this
      return <Modal title="工单扣分量分析" width="70%" visible={visible} footer={null} after-close={afterClose} onCancel={cancel} dialog-class="analysis-charts">
      <Spin spinning={spinning}>
      {
        visible && <div id="analysis-charts"></div>
      }
      </Spin>
      </Modal>
    }
  })
</script>
<style lang="scss" scoped>
.analysis-charts{
  #analysis-charts{
    width: 100%;
    height: 600px;
  }
}
</style>
