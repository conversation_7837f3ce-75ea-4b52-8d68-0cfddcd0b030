<script type="text/jsx" lang="jsx">
  import { defineComponent, getCurrentInstance } from 'vue'
  import { Table, Button, Tooltip, Icon } from 'ant-design-vue'
  import * as constants from '../constants'
  import uuid from 'uuid/v4'

  export default defineComponent({
    name: 'hq-table',
    props: {
      dataSource: {
        type: Array,
        default: () => {
          return []
        }
      },
      form: {
        type: Object,
        default: () => {
          return {
            dimension: []
          }
        }
      },
      loading: {
        type: Boolean,
        default: false
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      // 查看详情
      const check = function (record) {
        return constants.toWorkOrder(proxy, record, props.form, 2)
      }

      return {
        check
      }
    },
    render () {
      const { dataSource, check, loading, $tnt } = this
      const scopedSlots = {
        operation: (text, record, index) => {
          return index !== dataSource.length - 1 ? <router-link target="_blank" to={check(record)}>查看详情</router-link> : null
        },
        fuGaiRateTitle: (text, record, index) => {
          return index !== dataSource.length - 1 ? <router-link target="_blank" to={check(record)}>查看详情</router-link> : null
        }
      }
      return <Table loading={loading} {...{ scopedSlots }} scroll={{ y: '60vh' }} size="small" row-key={() => uuid()} pagination={false} class="work-order-statistics-table" row-class-name={(record, index) => constants.rowClassName(record, index, dataSource.length)} data-source={dataSource} bordered={true} columns={constants.hqColumns}>
      <template slot="fuGaiRateTitle">
        <span style="margin-right:3px">覆盖率</span><Tooltip>
          <template slot="title">
            {`覆盖率=覆盖量/${$tnt.xtenant === 0 ? '九机' : ''}直营店数量`}
          </template>
          <Icon theme="twoTone" tow-tone-color="#1890ff" type="question-circle" />
        </Tooltip>
      </template>
      <template slot="notQualifiedRateTitle">
        <span style="margin-right:3px">不合格率</span><Tooltip>
          <template slot="title">
            不合格率=该周期范围内扣分工单量/覆盖量
          </template>
          <Icon theme="twoTone" tow-tone-color="#1890ff" type="question-circle" />
        </Tooltip>
      </template>
      <template slot="appealRateTitle">
        <span style="margin-right:3px">申诉率</span><Tooltip>
          <template slot="title">
          申诉率=申诉成功的工单量/该分类下总的开单量
          </template>
          <Icon theme="twoTone" tow-tone-color="#1890ff" type="question-circle" />
        </Tooltip>
      </template>
</Table>
    }
  })
</script>
<style lang="scss" scoped>
:deep(.ant-table-scroll) {
  .total-row {
    td {
      height: 60px !important;
    }
  }
}
</style>
