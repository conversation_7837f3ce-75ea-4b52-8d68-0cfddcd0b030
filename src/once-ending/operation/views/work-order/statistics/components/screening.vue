<script type="text/jsx" lang="jsx">
  import { computed, defineComponent, watch, getCurrentInstance } from 'vue'
  import { Form, Select, DatePicker, Checkbox, Button, message, Input } from 'ant-design-vue'
  import * as constants from '../constants'
  import OrderCategroy from './order-categroy'
  import InputPeople from '~/components/staff/staff-input'
  import { treeToArray } from '~/util/treeUtils'
  import { options } from '../../common/model/constants.js'
  import { NiAreaSelect } from '@jiuji/nine-ui'

  export default defineComponent({
    name: 'screening',
    components: {
      NiAreaSelect,
      OrderCategroy,
      InputPeople
    },
    props: {
      active: {
        type: Number,
        default: 1
      },
      form: {
        type: Object,
        default: () => {
          return {}
        }
      },
      fetched: {
        type: Boolean,
        default: false
      },
      fetchData: {
        type: Function
      },
      analysis: {
        type: Function
      },
      reset: {
        type: Function
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      // 工单分类change
      const kindsIdChange = function (value, label, extra) {
        if (!value.length) {
          props.form.dimension = [1, 2]
          props.form.orgDimension = [1, 2]
          proxy.$refs.orderCategroyRef.setKindTree()
        } else {
          const treeData = JSON.parse(JSON.stringify(proxy.$refs.orderCategroyRef.treeData))
          // 过滤树,留下需要显示的树
          const data = getTreeData(treeData, value)
          setKindTree(data)
        }
      }

      // 过滤树,留下需要显示的树
      const getTreeData = function (treeData, value) {
        return treeData.filter(d => {
          if (d.children) {
            if (checkChildren(d, value)) {
              d.children = getTreeData(d.children, value)
              return true
            } else {
              return false
            }
          } else {
            return value.includes(d.id)
          }
        })
      }

      const checkChildren = function (d, value) {
        const tree = treeToArray(d.children)
        return !!tree.find(d => value.includes(d.id))
      }

      const setKindTree = function (data, type) {
        proxy.$emit('setKindTree', data, type)
      }

      const dimensionChange = function (val) {
        if (val.length < 1) {
          message.info('查看维度至少选择一个')
          props.form.dimension = props.form.orgDimension
          return
        }
        props.form.orgDimension = JSON.parse(JSON.stringify(val))
      }

      const timeChange = function (val) {
        if (val.length && new Date(val[1]).valueOf() - new Date(val[0]).valueOf() > 5184000000) {
          message.info('统计时差不能超过60天')
          props.form.time = props.form.orgTime
          return
        }
        props.form.orgTime = val
      }

      const peopleChange = function (val) {
        if (!val) {
          props.form.userName = undefined
          return
        }
        props.form.userName = val.name
      }

      const showDimension = computed(() => props.active === 1 && !!props.form.kindsId.length)

      return {
        kindsIdChange,
        setKindTree,
        dimensionChange,
        timeChange,
        peopleChange,
        showDimension
      }
    },
    render () {
      const { form, active, showDimension, fetched, fetchData, analysis, reset, kindsIdChange, setKindTree, dimensionChange, timeChange, peopleChange } = this
      return <div class="screening">
      <Form layout="inline">
      {
        active === 2 && <Form.Item>
        <Input.Group compact>
      <Select v-model={form.userType} style="width:30%" options={constants.userTypeOptions} allow-clear={false}/>
      <InputPeople ref="inputPeople" style="width:70%" value={form.userName} onChange={peopleChange} placeholder="请选择开单人" allow-clear></InputPeople>
    </Input.Group>
      </Form.Item>
      }
      <Form.Item label="地区" class="area-depart">
        <ni-area-select v-model={form.areaIds} multiple allowClear maxTagCount={1} placeholder="选择地区" style="width: 300px;"/>
      </Form.Item>
      <Form.Item>
          <a-input-group compact>
          <a-select
            v-model={form.timeType}
            options={options.timeTypeOptions.slice(0, 2)}
            style="width: 110px"
          />
          <a-range-picker
            style="width: 310px"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            v-model={form.time}
          />
        </a-input-group>
        </Form.Item>
        {active === 2 ? <Form.Item label="责任主体">
          <a-select
          v-model={form.legalDuty}
          maxTagCount={1}
          style="width: 200px"
          mode="multiple"
          options={options.legalDutyOptions.filter(it => it.value !== 2)}
          placeholder="选择责任主体"
          allowClear/>
        </Form.Item> : null}
        <Form.Item label="分值关联对象">
          <a-select
          v-model={form.scoreChainList}
          maxTagCount={1}
          mode="multiple"
          style="width: 200px"
          options={this.$tnt.xtenant < 1000 ? options.scoreChainOptionsJiuJi : options.scoreChainOptions }
          placeholder="选择分值关联对象"
          allowClear/>
        </Form.Item>
      <Form.Item label="工单分类">
        <OrderCategroy
        ref="orderCategroyRef"
         style="width:240px"
        placeholder="请选择分类"
        v-model={form.kindsId}
        onSetKindTree={setKindTree}
        onChange={kindsIdChange} />
      </Form.Item>
      {
        showDimension && <Form.Item label="查看维度">
          <Select style="width:210px" v-model={form.dimension} onChange={dimensionChange} mode="multiple" max-tag-count={1} placeholder="请选择查看维度" options={constants.dimensionOptions} />
      </Form.Item>
      }
      {
        active === 1 && <Form.Item>
          <Checkbox v-model={form.largeArea}>仅大区</Checkbox>
      </Form.Item>
      }
      <Form.Item>
          <Button style="margin-right:10px" type="primary" onClick={() => fetchData(1)}>查询</Button>
          {
          active === 1 && <Button style="margin-right:10px" disabled={!fetched} type="primary" onClick={analysis}>分类分析</Button>
          }
          <Button style="margin-right:10px" onClick={reset}>重置</Button>
      </Form.Item>
      </Form>
    </div>
    }
  })
</script>
<style lang="scss" scoped>
.screening {
  padding: 4px 24px 20px 24px;
}
 :deep(.ant-form-item-control) {line-height: normal;}
</style>
