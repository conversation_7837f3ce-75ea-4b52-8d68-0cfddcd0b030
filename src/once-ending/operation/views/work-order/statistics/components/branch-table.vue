<script type="text/jsx" lang="jsx">
  import { defineComponent, ref, getCurrentInstance } from 'vue'
  import { Table, Button } from 'ant-design-vue'
  import * as constants from '../constants'
  import uuid from 'uuid/v4'

  export default defineComponent({
    name: 'branch-table',
    props: {
      dataSource: {
        type: Array,
        default: () => {
          return []
        }
      },
      kindsTree: {
        type: Array,
        default: () => {
          return []
        }
      },
      kindsIds: {
        type: Array,
        default: () => {
          return []
        }
      },
      form: {
        type: Object,
        default: () => {
          return {
            dimension: []
          }
        }
      },
      loading: {
        type: <PERSON>olean,
        default: false
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const parentNum = ref(0)
      const branchColumns = ref([])
      const setColumns = function (treeData) {
        return treeData.map(d => {
          const o = {
            title: d.name,
            dataIndex: d.id,
            key: d.id,
            align: 'center'
          }
          if (d.children) {
            o.children = setColumns(d.children)
          } else {
            parentNum.value++
            const childrenColumns = constants.childrenColumns.filter(d => props.form.dimension.includes(d.filterValue))
            o.children = [...constants.getChildrenColumns(d.id, childrenColumns)]
          }
          return o
        })
      }

      // 查看详情
      const check = function (record) {
        return constants.toWorkOrder(proxy, record, props.form, 1, props.kindsIds)
      }

      // 初始组装表头
      const setBranchColumns = function () {
        parentNum.value = 0
        const asyncColumns = setColumns(JSON.parse(JSON.stringify(props.kindsTree)))
        asyncColumns.unshift(1, 0)
        const branchColumns = JSON.parse(JSON.stringify(constants.branchColumns))
        Array.prototype.splice.apply(branchColumns, asyncColumns)
        return branchColumns
      }

      return {
        branchColumns,
        check,
        setBranchColumns
      }
    },
    render () {
      const { dataSource, branchColumns, check, loading } = this
      const scopedSlots = {
        operation: (text, record, index) => {
          return index !== dataSource.length - 1 ? <router-link target="_blank" to={check(record)}>查看详情</router-link> : null
        }
      }
      return <Table loading={loading} scroll={{ x: true, y: '60vh' }} {...{ scopedSlots }} size="small" row-key={() => uuid()} pagination={false} class="work-order-statistics-table" row-class-name={(record, index) => constants.rowClassName(record, index, dataSource.length)} data-source={dataSource} bordered={true} columns={branchColumns} custom-header-row={constants.customHeaderRow}>
</Table>
    }
  })
</script>
