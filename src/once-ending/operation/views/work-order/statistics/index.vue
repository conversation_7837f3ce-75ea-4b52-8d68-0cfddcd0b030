<script type="text/jsx" lang="jsx">
  import { defineComponent, ref, reactive, nextTick, toRefs, getCurrentInstance } from 'vue'
  import { Tabs, message } from 'ant-design-vue'
  import * as constants from './constants'
  import Screening from './components/screening'
  import BranchTable from './components/branch-table'
  import HqTable from './components/hq-table'
  import AnalysisCharts from './components/analysis-charts'
  import { WORK_ORDER_STATISTICS } from '@operation/store/modules/work-order/action-types'

  export default defineComponent({
    name: 'work-order-statistics',
    components: {
      Screening,
      BranchTable,
      HqTable,
      AnalysisCharts
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const active = ref(1)
      const loading = ref(false)
      const destroyModal = ref(true)
      const analysisChartsShow = ref(false)
      const fetched = ref(false)

      const state = reactive({
        form: JSON.parse(JSON.stringify(constants.form)),
        kindsTree: []
      })

      const dataSource = ref([])

      // 查询
      const fetchData = async function () {
        if (active.value === 1 && checkTree() > 40) {
          message.info('选择的分类量乘以查看维度量需小于等于四十，请重新选择后查询')
          return
        }
        const {
          kindsId,
          areaIds,
          dimension,
          time,
          largeArea,
          userType,
          userName,
          timeType,
          legalDuty,
          scoreChainList
        } = state.form
        const params = {
          type: active.value,
          kindWorks: kindsId,
          areaId: areaIds,
          startTime: time && time.length ? `${time[0]} 00:00:00` : '',
          endTime: time && time.length ? `${time[1]} 23:59:59` : '',
          timeType: timeType,
          legalDuty,
          scoreChainList
        }
        if (active.value === 1) {
          params.views = dimension
          params.bigAreaFlag = largeArea
        } else {
          params.userType = userType
          params.userName = userName
        }
        loading.value = true
        const res = await proxy.$store.dispatch(`operation/workOrder/${WORK_ORDER_STATISTICS}`, params)
        loading.value = false
        if (res) {
          fetched.value = true
          dataSource.value = []
          const { data } = res
          const { dataList, dataSum } = data
          if (active.value === 1) {
            const branchTable = proxy.$refs.branchTable
            branchTable.branchColumns = branchTable.setBranchColumns()
            nextTick(() => {
              setTableFixedWidth()
            })
          }
          if (!dataList.length) {
            return
          }
          dataSum.areaName = '合计'
          dataList.push(dataSum)
          dataSource.value = active.value === 1 ? getBranchData(dataList) : getHqData(dataList)
        }
      }

      // 获取数据
      const getBranchData = function (dataList) {
        dataList.forEach((item, index) => {
          const { list } = item
          const countList = []
          const orderKindIds = []
          list.forEach(d => {
            const { values, orderKindId } = d
            orderKindIds.push(orderKindId)
            const valueList = Object.keys(values).map(v => `${orderKindId}_${v}$${values[v]}`)
            countList.push(...valueList)
          })
          parentIds.value.forEach(k => {
            if (!orderKindIds.includes(Number(k))) {
              const valueList = Object.keys(constants.values).map(v => `${k}_${v}$${constants.values[v]}`)
              countList.push(...valueList)
            }
          })
          delete item.list
          countList.forEach(k => {
            item[k.split('$')[0]] = k.split('$')[1]
          })
        })
        return dataList
      }

      // 获取总部数据
      const getHqData = function (dataList) {
        const list = []
        dataList.forEach(d => {
          const children = d.children || []
          delete d.children
          list.push({ ...d, typeBig: 1 })
          children.forEach(k => {
            const subChildren = k.children || []
            delete k.children
            list.push({ ...k, typeBig: 0 })
            subChildren.forEach(i => {
              const lastChildren = i.children || []
              i.children && delete i.children
              list.push({ ...i })
              lastChildren.forEach(l => {
                l.children && delete l.children
                list.push({ ...l })
              })
            })
          })
        })
        list[list.length - 1].typeBig = null
        return list
      }

      // 校验树总数是否大于40
      const parentNum = ref(0)
      const parentIds = ref([])
      const kindsIds = ref([])
      const checkTree = function () {
        parentNum.value = 0
        parentIds.value = []
        kindsIds.value = []
        setParentNum(state.kindsTree)
        return parentNum.value * state.form.dimension.length
      }

      const setParentNum = function (treeData) {
        treeData.forEach(d => {
          if (d.children) {
            setParentNum(d.children)
          } else {
            parentNum.value++
            parentIds.value.push(d.id)
          }
          kindsIds.value.push(d.id)
        })
      }

      // 分类分析
      const analysis = async function () {
        analysisChartsShow.value = true
      }

      // 重置
      const reset = function () {
        resetForm()
      }

      // 重置表单
      const resetForm = function () {
        state.form = { ...JSON.parse(JSON.stringify(constants.form)) }
        if (proxy.$refs.screening && proxy.$refs.screening.$refs.inputPeople) proxy.$refs.screening.$refs.inputPeople.text = undefined
      }

      const handleDestroyModal = function () {
        destroyModal.value = false
        nextTick(() => {
          destroyModal.value = true
        })
      }

      // tab change事件
      const tabChange = function () {
        resetForm()
        dataSource.value = []
      }

      // 获取kindsTree的值
      const setKindTree = function (data, type) {
        state.kindsTree = data
        if (type) {
          nextTick(() => {
            const branchTable = proxy.$refs.branchTable
            if (branchTable) {
              branchTable.branchColumns = branchTable.setBranchColumns()
              nextTick(() => {
                setTableFixedWidth()
              })
            }
          })
        }
      }

      // 解决antd固定列当列数过少,列自适应导致固定列宽度跟实际宽度不一致,有间隙bug
      const setTableFixedWidth = function () {
        const areaNameWidth = document.querySelector('.area-name').offsetWidth
        document.querySelectorAll('.ant-table-fixed-left .ant-table-fixed').forEach(item => {
          item.style.width = `${areaNameWidth}px`
        })
      }

      return {
        active,
        ...toRefs(state),
        fetchData,
        analysis,
        reset,
        dataSource,
        handleDestroyModal,
        destroyModal,
        analysisChartsShow,
        tabChange,
        setKindTree,
        loading,
        kindsIds,
        fetched
      }
    },
    render () {
      const { form, fetched, fetchData, analysis, reset, dataSource, handleDestroyModal, destroyModal, analysisChartsShow, tabChange, kindsTree, setKindTree, loading, kindsIds } = this
      const listeners = {
        'update:analysisChartsShow': val => {
          this.analysisChartsShow = val
        }
      }

      return <page class="work-order-statistics">
<Tabs v-model={this.active} onChange={tabChange}>
  {
    constants.tabs.map(d => <Tabs.TabPane key={d.id} tab={d.name}></Tabs.TabPane>)
  }
  </Tabs>
  <Screening ref="screening" active={this.active} form={form} onSetKindTree={setKindTree} fetchData={fetchData} fetched={fetched} analysis={analysis} reset={reset}/>
  <div class="main">
    {
   this.active === 1 && <BranchTable loading={loading} ref="branchTable" data-source={dataSource} kinds-tree={kindsTree} form={form} kindsIds={kindsIds}/>
  }
  {
   this.active === 2 && <HqTable loading={loading} data-source={dataSource} form={form}/>
  }
  </div>
  {
    destroyModal && <AnalysisCharts form={form} analysis-charts-show={analysisChartsShow} {...{ on: { 'update:analysisChartsShow': listeners['update:analysisChartsShow'] } }} onDestroyModal={handleDestroyModal}/>
  }
</page>
    }
  })
</script>
<style lang="scss" scoped>
.main {
  background: #fff;
  margin-top: 24px;
}
</style>
<style lang="scss">
.work-order-statistics {
  .area-depart{
    .ant-select-selection--multiple .ant-select-selection__choice{
      max-width: 180px !important;
    }
  }
  .ant-page-header,
  .ant-tabs,
  .screening {
    background: #fff;
  }
  .ant-tabs {
    margin: 0 -24px;
    padding: 0 24px;
  }
  .screening {
    margin: 0 -24px;
  }

  .work-order-statistics-table {
    .total-row {
      position: sticky !important;
      bottom: 0;
      background: #fff;
    }
    .ant-table-tbody
      > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)
      > td {
      background: none;
    }
    .ant-table-tbody {
      tr {
        height: 50px !important;
      }
    }
    .big-area {
      &:hover {
        td {
          background: #1890ff !important;
          color: #ffffff;
        }
        td:last-child {
          background: #fff !important;
          color: #000000a6 !important;
        }
      }
    }
    .small-area {
      &:hover {
        td {
          background: lighten(#1890ff, 30%) !important;
          color: #000000a6;
        }
        td:last-child {
          background: #fff !important;
          color: #000000a6 !important;
        }
      }
    }

    .total-row {
      &:hover {
        td {
          background: #fff !important;
          color: #000000a6;
        }
      }
      td {
        border-right-color: transparent !important;
      }
      > td:first-child {
        border-right: 1px solid #e8e8e8 !important;
      }
    }
    th {
      color: #333 !important;
      font-size: 14px !important;
      font-weight: 600 !important;
      background: #fafafa !important;
    }
    .hearder-class {
      font-weight: 500 !important;
    }
    .custom-header-class,
    .custom-cell-class {
      border-right: none !important;
    }
    .last-custom-header-class,
    .last-custom-cell-class {
      border-right: 1px solid #e8e8e8 !important;
    }
    .big-area {
      td {
        background: #1890ff;
        color: #ffffff;
      }
      td:last-child {
        background: #fff;
        color: #000000a6;
      }
    }
    .small-area {
      td {
        background: lighten(#1890ff, 30%);
      }
      td:last-child {
        background: #fff !important;
        color: #000000a6 !important;
      }
    }
    .ant-table-fixed-left {
      .big-area {
        td {
          background: #1890ff !important;
          color: #ffffff !important;
        }
        &:hover {
          td {
            background: #1890ff !important;
            color: #ffffff !important;
          }
        }
      }
      .small-area {
        td {
          background: lighten(#1890ff, 30%) !important;
        }
        &:hover {
          td {
            background: lighten(#1890ff, 30%) !important;
            color: #000000a6 !important;
          }
        }
      }
    }
  }
}
</style>
