<script type='text/jsx' lang="jsx">
  import { useState } from '../model/useState.js'
  import Accessories from '../../common/components/accessories'
  import Status from '../../common/components/status'
  import moment from 'moment'
  import { getCurrentInstance } from 'vue'

  export default {
    name: 'orderContent',
    components: {
      Accessories,
      Status,
    },

    setup () {
      const { proxy } = getCurrentInstance()
      const { detailInfo } = useState()
      // 跳转列表需要带的参数
      // 1.开单时间段
      // 2.责任主体:
      //   是门店需要地区
      // 是责任人需要接收人
      // 3.工单分类
      // 4.分值大于0
      // 5.工单类型
      // timeType 1月度 2 季度
      const getCountHref = function (item, timeType) {
        const { legalDuty, areaId, dutyUser, createTime } = detailInfo.value
        const { kindId } = item
        const startTime = timeType === 1 ? moment(createTime).startOf('month').format('YYYY-MM-DD') : moment(createTime).startOf('quarter').format('YYYY-MM-DD')
        const endTime = timeType === 1 ? moment(createTime).endOf('month').format('YYYY-MM-DD') : moment(createTime).endOf('quarter').format('YYYY-MM-DD')
        const query = {
          timeType: 1, // 开单时间
          startTime, // 开始时间
          endTime, // 结束时间
          legalDuty: legalDuty, // 责任主体
          kindsId: kindId, // 工单分类
          fraction: 'scoreEqZero,scoreLtZero', // 分值 需要小于等于0
          type: 1, // 工单类型,只统计总部检查

        }
        if (legalDuty === 0) { // 责任主体为门店,需要带上门店id
          query.areaIds = areaId
        } else if (legalDuty === 1) { // 责任主体为责任人,需要带上责任人
          query.userType = 1
          query.userTypeValue = dutyUser
        }
        return proxy.$router.resolve({
          path: '/work-order',
          query
        }).href
      }
      return {
        detailInfo,
        getCountHref
      }
    },

    render () {
      const { detailInfo, getCountHref } = this
      if (!detailInfo) return
      const title = () => <Status statusNum={ detailInfo.status }/>
      return (
        <a-card title={title}>
          {
            detailInfo.type !== 4 ? <div class="bonus-points">
              {
                detailInfo.kindScores.map(it => <div class="bonus-points-item flex flex-wrap mt-10">
                <div class="bonus-points-item-info">
                  <div class="label">工单分类：</div>
                  <div class="value">{it.kindName || '-'}</div>
                </div>
                  {
                    [1, 2, 3].includes(detailInfo.type) ? <div class="bonus-points-item-info">
                      <div class="label">分值：</div>
                      <div class="value">{it.score || '-'}</div>
                    </div> : null
                  }
                  {
                    [1, 2, 3].includes(detailInfo.type) ? <div class="bonus-points-item-info">
                      <div class="label">归属部门：</div>
                      <div class="value">{it.kindDepartName || '-'}</div>
                    </div> : null
                  }
                  {
                    [1, 2, 3].includes(detailInfo.type) && this.$tnt.xtenant < 1000 ? <div class="bonus-points-item-info">
                      <div class="label">月度开具次数：</div>
                      <div class="value">{it.monthIssueCount ? <a target='_blank' href={getCountHref(it, 1)}>{it.monthIssueCount}</a> : 0}次</div>
                    </div> : null
                  }
                  {
                    [1, 2, 3].includes(detailInfo.type) && this.$tnt.xtenant < 1000 ? <div class="bonus-points-item-info">
                      <div class="label">季度开具次数：</div>
                      <div class="value">{it.quarterIssueCount ? <a target='_blank' href={getCountHref(it, 2)}>{it.quarterIssueCount}</a> : 0}次</div>
                    </div> : null
                  }
                </div>)
              }
            </div> : null
          }
          <a-row class="mt-20">
            <a-col span={24}>
              <h3 class="block-title">内容描述:</h3>
              <span>{ detailInfo.description }</span>
            </a-col>
            <a-col span={24} class='mt-20'>
              <h3 class="block-title">附件:</h3>
              <Accessories dataSource={ detailInfo } class='accessories'/>
            </a-col>
          </a-row>
        </a-card>
      )
    },
  }
</script>

<style lang="scss">
  @import '../../common/style.scss';
  .ant-card-actions{
    position: absolute;
    width: 100%;
    bottom: 0;
  }
  .ant-form-item-children{
    width: 100%;
  }

  .hander-btn{
    width: 100%;
    padding: 10px 0;
    margin: -10px 0;

    &.primary{
      color: #fff;
      background: $primary-color
    }
    &.danger{
      color: $danger-color;
    }
  }
  .block-title{
    font-weight: 600;
    margin: 0 0 8px;
  }
  .addLogs{
    position: absolute;
    bottom: 60px;
    right: 15px;
    display: flex;
    align-items: center;
    width: 500px;
  }
  .bonus-points{
    .bonus-points-item{
      background: #f7f8fa;
      border-radius: 8px;
      padding: 4px 10px;
      .bonus-points-item-info{
        width: calc(100% / 3);
        display: flex;
        align-items: center;
        height: 36px;
        .label{
          flex-shrink: 0;
        }
        .value{
          flex: 1;
        }
      }
    }
  }
</style>
