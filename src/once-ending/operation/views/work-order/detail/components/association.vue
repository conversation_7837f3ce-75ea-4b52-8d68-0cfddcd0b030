<script type='text/jsx' lang="jsx">
  import { useState } from '../model/useState.js'
  import Accessories from '../../common/components/accessories'
  import Status from '../../common/components/status'

  const colorMap = new Map([
    [1, ['#0BBE69', 'rgba(11,190,105,0.1)']],
    [2, ['#1890FF', 'rgba(24,144,255,0.1)']],
    [3, ['#FA6400', 'rgba(250,100,0,0.1)']],
  ])

  export default {
    name: 'orderContent',
    components: {
      Accessories,
      Status,
    },

    setup () {
      const { detailInfo } = useState()

      const getColorMapKey = function (status, type) {
        return type === 'read' ? status === '未诵读' ? 2 : status === '诵读合格' ? 1 : 3 : status === '未考试' ? 2 : status === '考试合格' ? 1 : 3
      }
      return {
        detailInfo,
        getColorMapKey
      }
    },

    render () {
      const { detailInfo, getColorMapKey } = this
      if (!detailInfo) return
      return (
        <a-card title="加分申请">
          <div class="association">
            {
              detailInfo.addKindScores.map(it => <div class="association-item mt-10">
                <div class="association-item-title">
                  【{it.kindName}】关联诵读/考试
                </div>
                <div class="association-item-content flex flex-align-center mt-8">
                  <div class="association-item-content-item flex">
                    <div>关联诵读：</div>
                    {
                      it.readName ? <div class="flex flex-align-center">
                        <router-link target="_blank"
                                     to={`/hr/train/readCreate/edit?editId=${it.readId}&title=内容查看&verifyStatus=2&showModify=false&showReview=false`}>{it.readName}</router-link>
                        <div class="flex">
                          <div class="status-tag flex flex-align-center" style={{
                            color: colorMap.get(getColorMapKey(it.readStatus, 'read')) ? colorMap.get(getColorMapKey(it.readStatus, 'read'))[0] : '',
                            background: colorMap.get(getColorMapKey(it.readStatus, 'read')) ? colorMap.get(getColorMapKey(it.readStatus, 'read'))[1] : ''
                          }}>{it.readStatus}</div>
                        </div>
                      </div> : <div>-</div>
                    }
                  </div>
                  <div class="association-item-content-item flex">
                    <div>关联考试：</div>
                    {
                      it.examName ? <div class="flex flex-align-center">
                        <router-link target="_blank" to={`/hr/train/exam/list?id=${it.examId}`}>{it.examName}</router-link>
                        <div class="flex">
                          <div class="status-tag flex flex-align-center" style={{
                            color: colorMap.get(getColorMapKey(it.examStatus, 'exam')) ? colorMap.get(getColorMapKey(it.examStatus, 'exam'))[0] : '',
                            background: colorMap.get(getColorMapKey(it.examStatus, 'exam')) ? colorMap.get(getColorMapKey(it.examStatus, 'exam'))[1] : ''
                          }}>{it.examStatus}</div>
                        </div>
                      </div> : <div>-</div>
                    }
                  </div>
                </div>
              </div>)
            }
          </div>
        </a-card>
      )
    },
  }
</script>

<style lang="scss" scoped>

.association-item-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.association-item-item {
  padding: 4px 10px;
}

.association-item-content-item {
  width: 50%;
  padding-left: 10px;

  .status-tag {
    margin-left: 8px;
    height: 20px;
    border-radius: 4px;
    padding: 0 4px;
    font-weight: 400;
    font-size: 12px;
    line-height: 12px;
  }
}
</style>
