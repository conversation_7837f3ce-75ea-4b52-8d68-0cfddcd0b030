
<script lang="jsx">
  import { defineComponent, computed, watch, getCurrentInstance } from 'vue'
  import { useHandleState } from './use-handle.js'
  import { useState } from '../../model/useState'
  import { NiStaffSelect } from '@jiuji/nine-ui'
  const isJiuJi = window.tenant.xtenant < 1000

  export default defineComponent({
    components: {
      NiStaffSelect
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const { detailInfo } = useState()

      const userId = computed(() => proxy.$store.state.userInfo.UserID)

      const {
        orderHandleState,
        setHandleForm,
        acceptAction
      } = useHandleState()

      watch(() => detailInfo.value.sendUsers, () => {
        setHandleForm('sendUsers', detailInfo.value.sendUsers)
      }, {
        immediate: true
      })
      const duty = computed(() => detailInfo.value.dutyUserId === userId.value) // 责任人
      const initiator = computed(() => detailInfo.value.userId === userId.value) // 发起人

      const sendUsersChange = function (value, outputObject) {
        const output = outputObject.map(d => ({ id: d.staffId, name: d.staffName }))
        const all = orderHandleState.handleForm.sendUsers ? orderHandleState.handleForm.sendUsers.concat(output) : output
        const val = all.filter(d => value.includes(d.id))
        const keys = []
        const arr = []
        val.map(d => {
          if (!keys.includes(d.id)) {
            keys.push(d.id)
            arr.push(d)
          }
        })
        setHandleForm('sendUsers', arr)
      }

      return {
        setHandleForm,
        acceptAction,
        orderHandleState,
        duty,
        initiator,
        sendUsersChange,
        detailInfo
      }
    },
    // 处理工单：1发起工单 3 已完成 6 整改超时 9修改工单 10添加备注
    render () {
      const {
        duty, initiator, detailInfo: {
          type,
          status,
          departReceive,
          showPlusScoreApplyV2,
        },
        sendUsersChange,
        orderHandleState,
        acceptAction
      } = this
      // 待整改，待接收状态下增加加分申请，九机用showPlusScoreApplyV2字段判断
      // 工单类型type: 1[总部检查]、 2[分公司检查]、3[城市经理检查]、4[跨部门对接]、 5[质量流程检查]
      // [待接收 0] --> 责任人可见 跨部门：谁接收，谁完成
      // 工单类型为‘总部检查、分公司检查、城市经理检查’的工单，在【待接收】状态下，工单的主要责任人可以看到【加分申请】按钮，申请加分；
      const waitReceiving = () => {
        return <div>
          {
            !duty && !departReceive ? null : renderSend()
          }
          {
            showPlusScoreApplyV2 ? <a-button class='mr-8' onClick={ () => acceptAction(14) }>加分申请</a-button> : null
          }
          {
            !duty && !departReceive ? null : <span>
              { [1, 2, 3].includes(type) && (
                <a-button class='danger mr-8' onClick={ () => acceptAction(3) }>申 诉</a-button>
              ) }
              <a-button type='primary' onClick={ () => acceptAction(2) }>接 收</a-button>
            </span>
          }
        </div>
      }
      // [待整改 1，6，7] --> 责任人可见
      const waitRectified = () => {
        if (!duty) return
        return (
      <div>
        { renderSend() }
        <a-button type='primary' onClick={ () => acceptAction(6) }>
          { type === 4 ? '处理完成' : '整改完成'}
        </a-button>
        { showPlusScoreApplyV2 ? <a-button class='ml-8' onClick={ () => acceptAction(14) }>加分申请</a-button> : null }
      </div>
    )
      }
      // [待验收 2] --> 发起人可见
      const waitAcceptance = () => {
        if (!initiator) return
        return (
      <div>
        { renderSend() }
        <div >
          <a-button class='mr-8' onClick={ () => acceptAction(8) } >验收不通过</a-button>
          <a-button onClick={ () => acceptAction(7) } type='primary'>验收通过</a-button>
        </div>
      </div>
    )
      }
      // [申诉中 4] --> 发起人可见
      const Appealing = () => {
        if (!initiator) return
        return (
      <div>
        { renderSend() }
        <a-button class='mr-8' onClick={ () => acceptAction(5) } >申诉不通过</a-button>
        <a-button onClick={ () => acceptAction(4) } type='primary'>申诉通过</a-button>
      </div>
    )
      }

      // [加分审核 9] --> 发起人可见
      const bonusReview = () => {
        if (!initiator) return null
        return (
          <div>
            { renderSend() }
            <a-button class='mr-8' onClick={ () => acceptAction(16) } >拒绝</a-button>
            <a-button onClick={ () => acceptAction(15) } type='primary'>同意</a-button>
          </div>
        )
      }

      // 抄送 4[跨部门对接]、 5[质量流程检查] 可用
      const renderSend = () => {
        return (
      <a-form label-col={{ span: 2 }} wrapper-col={{ span: 18 }}>
        <a-form-item
          label='消息抄送人'
          class="center-y">
          <NiStaffSelect
            value={ orderHandleState.handleForm.sendUsers ? orderHandleState.handleForm.sendUsers.map(d => d.id) : [] }
            multiple
            placeholder="选择抄送人"
            onChange={ (value, outputObject) => { sendUsersChange(value, outputObject) } }
          />
        </a-form-item>
      </a-form>
    )
      }
      // 工单状态 对应 相应操作
      const types = {
        0: waitReceiving, // 0 待接收 =>【2接收工单 3申诉】      ->责任人可见
        1: waitRectified, // 1 待整改 =>【6处理完成/整改完成】           ->责任人可见
        2: waitAcceptance, // 2 待验收 =>【7验收通过 8验收不通过】->发起人可见
        4: Appealing, // 4 申诉中 =>【4申诉通过 5申诉不通过】     ->发起人可见
        6: waitRectified, // 6 整改超时（同待整改）
        7: waitRectified, // 7 待出具方案(按钮渲染同待整改)
        9: bonusReview // 9 加分审核
      }
      const statusRender = types[status]
      const RenderHtml = typeof statusRender === 'function' ? statusRender() : <statusRender/>
      return (!duty && !initiator) || !statusRender ? null : RenderHtml
    }
  })
</script>
