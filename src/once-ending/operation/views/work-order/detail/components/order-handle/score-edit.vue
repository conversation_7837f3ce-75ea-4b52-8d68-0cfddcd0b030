<template>
  <a-modal title="分值修改"
           v-model="showScoreEdit"
           :confirm-loading="loading"
           @ok="ok"
           width="550px"
           @cancel="showScoreEdit = false">
    <div class="main">
      <div class="score-item" v-for="(item,index) in formData.applyKinds" :key="index">
        <div class="title">工单分类：{{item.label}}</div>
        <div class="score-box flex flex-align-center flex-justify-between mt-8">
          <div class="score-title">分值：</div>
          <el-input-number size="small" v-model="item.score" step-strictly :precision="0"></el-input-number>
        </div>
      </div>
      <div class="mt-8">
        <div>同意加分原因：</div>
        <div class="area-wrap mt-8">
          <a-textarea
            allowClear
            :rows="4"
            :maxLength="100"
            v-model="formData.reason"
            placeholder="请填写同意加分原因"
            />
            <i class='text-tips'>{{ formData.reason?.length || 0}}/100</i>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
  import Vue, { ref, watch, getCurrentInstance, defineEmits } from 'vue'
  import { useHandleState } from './use-handle'
  import { InputNumber } from 'element-ui'
  import { HANDLE_WORK_ORDER } from '@/operation/store/modules/work-order/action-types'
  import { message } from 'ant-design-vue'
  import { useState } from '../../model/useState'
  import { cloneDeep } from 'lodash'
  Vue.use(InputNumber)

  const { proxy } = getCurrentInstance()

  const emits = defineEmits(['featchLogs', 'featchDetail'])

  const { detailInfo } = useState()

  const { showScoreEdit, orderHandleState, setHandleForm, getPlusScoreApplyKindList, plusScoreApplyKindList } = useHandleState()

  watch(() => showScoreEdit.value, async val => {
    await getPlusScoreApplyKindList(1)
    formData.value.applyKinds = cloneDeep(plusScoreApplyKindList.value)
  })

  const loading = ref(false)

  const formData = ref({
    applyKinds: [],
    reason: undefined
  })

  const ok = async function () {
    const { id, areaId, dutyUserId, legalDuty } = detailInfo.value
    const { reason, applyKinds } = formData.value
    const {
      handleForm: {
        sendUsers
      }
    } = orderHandleState
    const params = {
      id,
      type: 15,
      reason,
      applyKinds: applyKinds.map(d => ({
        kindId: d.value,
        score: d.score
      })),
      legalDuty,
      applyDutyUserId: dutyUserId,
      applyDutyAreaId: areaId
    }

    if (sendUsers?.length) params.sendUsers = sendUsers.map(item => ({ id: item.id, name: item.name }))
    loading.value = true
    const res = await proxy.$store.dispatch(
      `operation/workOrder/${HANDLE_WORK_ORDER}`,
      params
    )
    loading.value = false
    if (!res) return
    const { code, userMsg } = res
    if (code === 0) {
      message.success('提交成功')
      emits('featchLogs')
      emits('featchDetail', id)
      // 重新获取设置抄送人
      const { sendUsers } = detailInfo.value
      setHandleForm('sendUsers', sendUsers)
      showScoreEdit.value = false
      formData.value.applyKinds = []
      formData.value.reason = undefined
    } else {
      message.error(userMsg)
    }
  }

</script>

<style scoped lang="scss">
.main{
  .score-item{
    background: #F7F8FA;
    border-radius: 8px;
    padding: 10px;
    .score-box{
      .score-title{
      &:before{
        display: inline-block;
        margin-right: 4px;
        color: #f5222d;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: '*';
      }
      }
    }
  }
  .area-wrap{
    position: relative;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding-bottom: 0.3em;

    textarea{
      border: none;
    }
    .text-tips {
      position: absolute;
      font-size: 12px;
      bottom: 0;
      right: 5px;
      line-height: 1.45;
    }
  }
}
</style>
