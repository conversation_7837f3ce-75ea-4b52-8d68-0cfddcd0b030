<template>
  <custom-uploader
    :fileList.sync="dataSource.accessoriesList2"
    showThumbnail
    :isDetail="false"
    :showRename="false"
    :editFileName="false"
    :createUser="false"
    collection="oa-operate"
  />
</template>
<script>
  import customUploader from '../../../common/components/custom-uploader'
  export default {
    name: 'accessories',
    components: {
      customUploader,
    },
    props: {
      dataSource: {
        type: Object,
        default: () => {}
      }
    }
  }
</script>
