<script type='text/jsx' lang="jsx">
  import { toRefs, watch, computed } from 'vue'
  import { useState } from '../../model/useState'
  import { useHandleState } from './use-handle.js'
  import RenderAction from './renderAction'
  import CustomUploader from '../../../common/components/custom-uploader/uploader'
  import ScoreEdit from './score-edit'

  const actionTypeArray = [
    '发起工单',
    '接收工单',
    '申诉',
    '申诉通过',
    '申诉不通过',
    '整改完成',
    '验收通过',
    '验收不通过',
    '修改工单',
    '添加备注',
    '添加方案',
    '',
    '',
    '加分申请',
    '',
    '加分申请不通过'
  ]

  export default {
    name: 'order-handle',
    components: {
      RenderAction,
      // UploaderImg,
      CustomUploader,
    },

    setup () {
      const { detailInfo } = useState()
      const {
        orderHandleState,
        setResonModalVisible,
        setHandleForm,
        acceptAction,
        onOk,
        resestHandleForm,
        plusScoreApplyKindList,
        featchDetail,
        featchLogs
      } = useHandleState()

      const updateFileList = v => {
        setHandleForm(orderHandleState.currentActionType === 14 ? 'accessoriesList3' : 'accessoriesList2', v)
      }

      const handleClose = () => {
        resestHandleForm()
        setResonModalVisible(false)
      }

      const appealKindOptions = computed(() => orderHandleState.currentActionType === 14 ? plusScoreApplyKindList.value : detailInfo.value.orderKindsOpts)

      watch(
        () => appealKindOptions.value,
        val => {
          if (val.length && val.length === 1) {
            orderHandleState.handleForm.appealKinds = Array.of(appealKindOptions.value[0].value)
          }
        },
        {
          immediate: true
        }
      )

      return {
        detailInfo,
        ...toRefs(orderHandleState),
        setResonModalVisible,
        setHandleForm,
        acceptAction,
        onOk,
        handleClose,
        updateFileList,
        plusScoreApplyKindList,
        featchDetail,
        featchLogs
      }
    },
    render () {
      const {
        detailInfo: {
          orderKindsOpts = []
        },
        currentActionType,
        actionTypeIndex,
        resonModalVisible,
        handleForm,

        handleClose,
        setHandleForm,
        updateFileList,
        plusScoreApplyKindList,
        featchDetail,
        featchLogs
      } = this
      // <UploaderImg
      //   maxLen={ 9 }
      //   class="upload-img"
      //   collection="oa-operate"
      //   onChangeImg={ files => {
      //     handleForm.accessoriesList2 = files
      //   }}
      //   files={ handleForm.accessoriesList2 }
      // />

      return (
        <div class='order-handle'>
          <RenderAction
          />
          <a-modal
            destroyOnClose
            width={600}
            title={actionTypeArray[actionTypeIndex]}
            visible={resonModalVisible}
            onOk={this.onOk}
            onCancel={ handleClose }>
            <a-form label-col={{ span: 6 }} wrapper-col={{ span: 17 }}>
              {
                currentActionType === 3 || currentActionType === 14
                ? <a-form-item label={currentActionType === 3 ? '申诉的工单分类' : '申请加分工单分类'} required>
                  <a-select
                    allowClear
                    showArrow
                    mode="multiple"
                    maxTagCount={1}
                    placeholder="请选择分类"
                    v-model={ handleForm.appealKinds }
                    options={currentActionType === 14 ? plusScoreApplyKindList : orderKindsOpts }
                  />
                </a-form-item> : null
              }
              <a-form-item label={currentActionType === 14 ? '加分理由' : '请填写理由'} required={currentActionType !== 14}>
                <div class="area-wrap">
                  <a-textarea
                    allowClear
                    rows={4}
                    maxLength={ 1000 }
                    v-model={ handleForm.reason }
                    placeholder={ `填写${actionTypeArray[actionTypeIndex]}理由` }
                    onChange = { e => { setHandleForm('reason', e.target.value) } }
                  />
                  <i class='text-tips'>{ handleForm.reason?.length || 0}/1000</i>
                </div>
              </a-form-item>
              {
                currentActionType === 3 || currentActionType === 14 ? <a-form-item label='附件'>
                  <CustomUploader
                    showThumbnail
                    showRename={false}
                    showUpload={handleForm[currentActionType === 14 ? 'accessoriesList3' : 'accessoriesList2'].length < 9}
                    collection="oa-operate"
                    fileList={handleForm[currentActionType === 14 ? 'accessoriesList3' : 'accessoriesList2']}
                    {...{ on: { 'update:fileList': updateFileList } }}
                  />
                </a-form-item> : null

              }
            </a-form>
          </a-modal>
          <ScoreEdit onFeatchLogs={featchLogs} onFeatchDetail={featchDetail}/>
        </div>
      )
    },
  }
</script>

<style lang="scss">
  @import '../../../common/style.scss';
  .order-handle{
    border-top: 1px solid #F0F2F5;
    background: #fff;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 99;
    padding: 16px;

    >div{
      width: 1200px;
      margin: 0 auto;
    }
    .ant-form-item-children{
      width: 100%;
    }

    .hander-btn{
      &.primary{
        color: #fff;
        background: $primary-color
      }
      &.danger{
        color: $danger-color;
      }
    }
  }
  .area-wrap{
    position: relative;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding-bottom: 0.3em;

    textarea{
      border: none;
    }
    .text-tips {
      position: absolute;
      font-size: 12px;
      bottom: 0;
      right: 5px;
      line-height: 1.45;
    }
  }
</style>
<style scoped>
  .upload-img{
    margin-top: -16px;
  }
</style>
