
import { getCurrentInstance, reactive, inject, provide, ref } from 'vue'
import { message } from 'ant-design-vue'
import {
  HANDLE_WORK_ORDER
} from '@operation/store/modules/work-order/action-types'
import { to } from '~/util/common'
import workOrder from '@operation/api/work-order'

const key = Symbol('useHandle')

export function useHandleState () {
  return inject(key)
}

export function createHandleState (detailInfo, editDetailInfo, featchDetail, featchLogs) {
  const { proxy } = getCurrentInstance()
  const orderHandleState = reactive({
    currentActionType: null,
    actionTypeIndex: null,
    resonModalVisible: false, // 拒绝理由modal
    handleForm: {
      appealKinds: [], // 工单类型
      accessoriesList2: [], // 附件
      accessoriesList3: [], // 申请加分的附件
      reason: '', // 理由
      sendUsers: [],
    }
  })

  const showScoreEdit = ref(false)

  const plusScoreApplyKindList = ref([])

  const getPlusScoreApplyKindList = async function (check) {
    const params = {
      workOrderId: proxy.$route.params.id,
      check
    }
    const [err, res] = await to(workOrder.plusScoreApplyKindList(params))
    if (err) throw err
    const { code, data, userMsg } = res
    if (code === 0) {
      plusScoreApplyKindList.value = data.map(d => ({
        label: d.kindName,
        value: d.kindId,
        score: d.score
      }))
    } else {
      message.error(userMsg)
    }
  }

  const setCurrentActionType = (value) => { orderHandleState.currentActionType = value }
  const setActionTypeIndex = (value) => { orderHandleState.actionTypeIndex = value }
  const setResonModalVisible = (value) => { orderHandleState.resonModalVisible = value }
  const setHandleForm = (key, value) => {
    orderHandleState.handleForm[key] = value
  }
  const resestHandleForm = () => {
    orderHandleState.handleForm = {
      ...orderHandleState.handleForm,
      appealKinds: [],
      accessoriesList2: [],
      accessoriesList3: [], // 附件
      reason: '',
    }
  }
  // 处理附件格式
  const _handleFile = (list) => {
    if (!list?.length) return
    return list.map(item => ({
      accessoryUrl: item.fileUrl,
      accessoryID: item.fid.split('.')[0],
      postfix: item.fileName.split('.')[1],
      name: item.fileName,
      type: item.type
    }))
  }

  // 工单处理 2:接收 3:申诉 4:申诉通过 5:申诉不通过 6:处理/整改完成 7:验收通过 8:验收不通过 14:加分申请 15:加分申请通过 16 加分申请不通过
  const acceptAction = actionType => {
    setCurrentActionType(actionType)
    // 处理[3、 5、 8] 三个状态的弹窗
    if ([3, 5, 8, 14, 16].includes(actionType)) {
      setActionTypeIndex(actionType - 1)
      // 加分申请时查询可选择的工单分类
      if (actionType === 14) getPlusScoreApplyKindList()
      setResonModalVisible(true)
    } else if (actionType === 2 && detailInfo.value.type !== 4 && detailInfo.value.type !== 5) { // 工单接收
      proxy.$confirm({
        title: '提示',
        content: '工单接收后不可以申诉，确认接收吗？',
        onOk: () => { handleWorkOrder() }
      })
    } else if (actionType === 15) {
      showScoreEdit.value = true
    } else {
      handleWorkOrder()
    }
  }
  // [3, 5, 8] 理由modal[ok]
  const onOk = async () => {
    const {
      currentActionType,
      handleForm: {
        reason,
        appealKinds,
      }
    } = orderHandleState
    if ((currentActionType === 3 || currentActionType === 14) && !appealKinds?.length) {
      message.error(`请选择${currentActionType === 14 ? '加分申请' : '申诉'}的工单分类`)
      return
    }
    if (!reason && currentActionType !== 14) {
      message.error('理由内容不能为空')
      return
    }
    const isTrue = await handleWorkOrder(true)
    if (!isTrue) return
    resestHandleForm()
    setResonModalVisible(false)
  }
  /**
   * 工单处理 2:接收 3:申诉 4:申诉通过 5:申诉不通过 6:处理/整改完成 7:验收通过 8:验收不通过
   * @param isModalOk{Boolean} 弹窗提交
   */
  const handleWorkOrder = async (isModalOk) => {
    const { id, accessoriesList = [], appealKinds: detailAppealKinds, areaId, dutyUserId, legalDuty } = detailInfo.value
    const {
      currentActionType,
      handleForm: {
        sendUsers,
        appealKinds,
        accessoriesList2,
        accessoriesList3,
        reason,
      }
    } = orderHandleState

    const params = {
      id,
      type: currentActionType,
      accessoriesList: _handleFile(accessoriesList),
      reason,
      accessoriesList2: _handleFile(accessoriesList2), // 申诉附件
      accessoriesList3: _handleFile(accessoriesList3), // 申请加分的附件
    }
    if (currentActionType === 3 || currentActionType === 14) {
      params.appealKinds = appealKinds
    } else {
      params.appealKinds = detailAppealKinds
    }
    params.appealKinds = typeof params.appealKinds === 'string' ? params.appealKinds ? params.appealKinds.split(',') : [] : params.appealKinds
    if (sendUsers?.length) params.sendUsers = sendUsers.map(item => ({ id: item.id, name: item.name }))
    if (currentActionType === 14 || currentActionType === 16) {
      params.legalDuty = legalDuty
      params.applyDutyUserId = dutyUserId
      params.applyDutyAreaId = areaId
    }
    if (currentActionType === 16) delete params.appealKinds
    proxy.$indicator.open()
    const res = await proxy.$store.dispatch(
      `operation/workOrder/${HANDLE_WORK_ORDER}`,
      params
    )
    proxy.$indicator.close()
    if (!res) return
    const { code, userMsg } = res
    if (code === 0) {
      message.success('提交成功')
      featchLogs()
      featchDetail(id)
      // 重新获取设置抄送人
      const { sendUsers } = editDetailInfo.value
      setHandleForm('sendUsers', sendUsers)
      if (isModalOk) return true
    } else {
      message.error(userMsg)
      if (isModalOk) return false
    }
  }

  const o = {
    orderHandleState,
    setCurrentActionType,
    setActionTypeIndex,
    setResonModalVisible,
    setHandleForm,
    acceptAction,
    onOk,
    handleWorkOrder,
    resestHandleForm,
    showScoreEdit,
    plusScoreApplyKindList,
    getPlusScoreApplyKindList,
    featchDetail,
    featchLogs
  }
  provide(key, o)

  return o
}
