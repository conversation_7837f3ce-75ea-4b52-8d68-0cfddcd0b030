import { reactive, inject, provide, toRefs, getCurrentInstance } from 'vue'
import { message } from 'ant-design-vue'
import {
  FETCH_WORK_ORDER_LOGS,
  ADD_WORK_ORDER_LOGS,
} from '@operation/store/modules/work-order/action-types'

const format = function (fileName) {
  const regImg =
    /.(jpg|JPG|bmp|gif|ico|pcx|jpeg|JPEG|tif|png|raw|tga|webp|svg)$/i
  const regPDF = /.(pdf|pdfx)$/i
  const regWord = /.(doc|docx)$/i
  const regPPT = /.(ppt|pptx)$/i
  const regExcel = /.(xls|xlsx)$/i
  const regVideo =
    /.(avi|AVI|rmvb|RMVB|mpeg|MPEG|wmf|WMF|mov|MOV|mkv|MKV|mp4|MP4)$/i
  const regAudio = /.(mp3)$/i
  if (regImg.test(fileName)) return 'file-image'
  if (regPDF.test(fileName)) return 'file-pdf'
  if (regWord.test(fileName)) return 'file-word'
  if (regPPT.test(fileName)) return 'file-ppt'
  if (regVideo.test(fileName)) return 'file-video'
  if (regAudio.test(fileName)) return 'file-audio'
  if (regExcel.test(fileName)) {
    return 'file-excel'
  } else {
    return 'file-text'
  }
}

const key = Symbol('useLogs')

export function useLogsState () {
  return inject(key)
}

export function createLogsState () {
  const { proxy } = getCurrentInstance()

  const state = reactive({
    workOrderLogs: null,
    pagination: {
      current: 1,
      size: 50,
    },
    logs: {
      remark: '',
      loading: false,
    },
    scheme: {
      remark: '',
      loading: false,
    },
  })

  const featchLogs = async () => {
    let params = {
      workOrderId: proxy.$route.params.id,
      ...state.pagination,
    }
    const res = await proxy.$store.dispatch(
      `operation/workOrder/${FETCH_WORK_ORDER_LOGS}`,
      params
    )
    const { code, data, userMsg } = res
    if (code === 0) {
      state.workOrderLogs = data.records.map((it) => ({
        ...it,
        accessoriesListImgs: it.accessoriesList?.length
          ? it.accessoriesList.filter(
            (el) => format(el.fileName) === 'file-image'
          )
          : [],
        accessoriesListFiles: it.accessoriesList?.length
          ? it.accessoriesList.filter(
            (el) => format(el.fileName) !== 'file-image'
          )
          : [],
      }))
    } else {
      message.error(userMsg)
    }
  }

  const type = {
    10: 'logs',
    12: 'scheme',
  }
  const changeRemark = (value, operationType) => {
    state[type[operationType]].remark = value
  }
  const addLogs = async (operationType) => {
    if (!state[type[operationType]].remark) {
      message.error('内容不能为空')
      return
    }
    const params = {
      workOrderId: proxy.$route.params.id,
      operationDescription: state[type[operationType]].remark,
      operationType,
    }
    state[type[operationType]].loading = true
    const res = await proxy.$store.dispatch(
      `operation/workOrder/${ADD_WORK_ORDER_LOGS}`,
      params
    )
    const { code, userMsg } = res
    if (code === 0) {
      message.success(userMsg)
      state[type[operationType]].remark = ''
      state[type[operationType]].loading = false
      return true
    } else {
      message.error(userMsg)
      state[type[operationType]].loading = false
      return false
    }
  }
  provide(key, {
    ...toRefs(state),
    featchLogs,
    changeRemark,
    addLogs,
  })
  return {
    ...toRefs(state),
    featchLogs,
    changeRemark,
    addLogs,
  }
}
