<script type='text/jsx' lang="jsx">
  import Vue, { ref, nextTick, onMounted } from 'vue'
  import 'viewerjs/dist/viewer.css'
  import Viewer from 'v-viewer'
  import { useState } from '../../model/useState'
  import { useLogsState } from './useLogs.js'
  import { NiImg } from '@jiuji/nine-ui'
  import FilePreview from './file-preview/index'
  Vue.use(Viewer)

  export default {
    name: 'logs',
    components: {
      FilePreview
    },
    setup (props) {
      const { workOrderLogs, featchLogs, logs, scheme, changeRemark, addLogs } = useLogsState()
      const { detailInfo } = useState()

      const previewRef = ref(null)
      const selectFile = ref({
        filePath: '',
        fileName: '',
        fid: ''
      })
      const preview = function (file) {
        selectFile.value = {
          filePath: file.url,
          fileName: file.fileName || file.fid,
          fid: file.fid || ''
        }
        nextTick(() => {
          previewRef.value.open()
        })
      }

      const viewerInstance = ref(null)
      const handleInited = function (viewer) {
        viewerInstance.value = viewer
      }
      const handleShow = function (i) {
        viewerInstance.value.view(i)
      }

      const handleAddLogs = async (operationType) => {
        const isTrue = await addLogs(operationType)
        if (isTrue) featchLogs()
      }

      onMounted(() => {
        featchLogs()
      })

      return {
        detailInfo,
        workOrderLogs,
        featchLogs,
        logs,
        scheme,
        changeRemark,
        addLogs,
        handleAddLogs,

        preview,
        selectFile,
        previewRef,
        handleInited,
        handleShow
      }
    },

    render () {
      const {
        workOrderLogs,
        logs,
        scheme,
        detailInfo: { type, status, dutyUserId },

        selectFile,
        preview,
        handleInited,
        handleShow
      } = this
      const duty = dutyUserId === this.$store.state.userInfo.UserID // 责任人
      if (!workOrderLogs) return

      // 7: 'waitSchemes', // 7 待出具方案 =>【11添加方案】   ->责任人可见
      return (
        <a-card title="操作日志">
          <ul class="logs-warp">
            {
              workOrderLogs.map(item => (
                <li key={item.index}>
                  <div class="time">{item.createTime}</div>
                  <div class='content'>
                    <h3 class="title">{item.userName}</h3>
                    <div class="description">
                      <span class="blue">{item.typeName}：</span>
                      <span>{item.description}</span>
                    </div>
                    {
                      item.accessoriesList?.length > 0 &&
                        <div class="attachments">
                          <div class='imgs-wrap'>
                            <viewer images={item.accessoriesListImgs} onInited={handleInited} class='image-box'>
                            {
                              item.accessoriesListImgs.map((img, i) => (
                                <img
                                  onClick={() => { handleShow(i) }}
                                  width="70"
                                  height="70"
                                  src={NiImg.getUrl(img.fileUrl)}
                                />
                              ))
                            }
                            </viewer>
                          </div>
                          <div class='files-wrap'>
                          {
                            item.accessoriesListFiles.map(it => (
                              <div class="full-width my-5">
                                <a rel="noopener noreferrer" href="javascript:;"
                                  class='file-name'
                                  onClick={() => { preview(it) }}
                                >
                                  {it.fileName}
                                </a>
                              </div>
                            ))
                          }
                          </div>
                        </div>
                    }
                  </div>
                </li>
              ))
            }
          </ul>
          <FilePreview
            style="display:none"
            ref="previewRef"
            file={ selectFile }
            pathKey="filePath"
          />
          <a-row class='mt-16'>
            <a-col span={12}>
              <a-input
                placeholder="添加操作日志备注"
                maxLength={ 500 }
                value={ logs.remark }
                onChange={e => { this.changeRemark(e.target.value, 10) }}
              />
            </a-col>
            <a-col span={2}>
              <a-button
                class="margin-left"
                onClick={ () => { this.handleAddLogs(10) }}
                loading={ logs.loading }>
                添加进程
              </a-button>
            </a-col>
          </a-row>
          { // type-5: 质量流程检查 && 责任人 && 三种状态
            type === 5 && duty && [1, 7, 8].includes(status) &&
            <a-row class="mt-16">
              <a-col span={12}>
                <a-textarea
                  placeholder="请输入500字以内方案内容"
                  maxLength={ 500 }
                  value={ scheme.remark }
                  onChange={e => { this.changeRemark(e.target.value, 12) }}
                />
              </a-col>
              <a-col span={2}>
                <a-button
                  class="margin-left"
                  onClick={ () => { this.handleAddLogs(12) }}
                  loading={ scheme.loading }>
                  添加方案
                </a-button>
              </a-col>
            </a-row>
          }
        </a-card>
      )
    },
  }
</script>
<style lang='scss' scoped>
  @import '../../../../../assets/style/logs.scss';
  .my-5{
    margin-top: 5px;
    margin-bottom: 5px;
  }
  .title{
    font-weight: 600;
    margin-bottom: 4px;
  }
  .add-logs{
    width: 60%;
  }
  .imgs-wrap{
    margin-top: 5px;
    img{
      margin-right: 5px;
      margin-bottom: 5px;
      object-fit: cover;
    }
  }
  .file-name{
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
</style>
