/**
把预览和下载单独封成一个组件
文件预览功能不支持文件夹压缩bao
*/
<template>
<div class="flex flex-align-center">
  <img :src="file[pathKey]" width="200" style="visibility: hidden;width: 1px;height: 1px;"/>
  <template v-if="type !== 'file-audio'">
    <a-icon @click="open" title="附件预览" class="pointer" style="color:#1890ff;margin:0em .5em;" type="eye"/>
  </template>
  <span v-else style="width: 29px;"></span>
  <a v-if="isdownloadFile" class="margin-left download margin-right flex flex-align-center" title="附件下载" @click="downloadFile">
    <a-icon :type="dloading ? 'loading' : 'cloud-download'"/>
  </a>
  <!-- 图片查看 -->
  <div class="images" v-viewer="{}" style="display: none">
    <img class="img" :src="file[pathKey]" />
  </div>
  <video-player ref="player"/>
</div>
</template>
<script>
  import 'viewerjs/dist/viewer.css'
  import Viewer from 'v-viewer'
  import Vue from 'vue'
  import VideoPlayer from './video-player'
  import { saveAsFile } from '~/util/export'
  import { saveAs } from 'file-saver'
  Vue.use(Viewer)
  export default {
    components: { VideoPlayer },
    props: {
      file: {
        type: Object,
        default: () => ({ fileName: '' }),
        validator: (fileList) => {
          let isRight = true
          if (!fileList.hasOwnProperty('fileName')) isRight = false
          if (!isRight) console.error('file: 参数不正确fileName')
          return isRight
        }
      },
      pathKey: {
        type: String,
        default: 'fileUrl'
      },
      type: {
        type: String,
        default: ''
      },
      isdownloadFile: {
        type: Boolean,
        default: false
      },
      uploadHost: { // 强制使用上传host
        type: String,
        default: ''
      }
    },
    data () {
      return {
        dloading: false
      }
    },
    methods: {
      // 单独做一个组件时，用到
      format (fileName) {
        const regImg = /.(jpg|JPG|bmp|gif|ico|pcx|jpeg|JPEG|tif|png|PNG|raw|tga|webp|svg)$/i
        const regPDF = /.(pdf|pdfx)$/i
        const regWord = /.(doc|docx)$/i
        const regPPT = /.(ppt|pptx)$/i
        const regExcel = /.(xls|xlsx)$/i
        const regVideo = /.(avi|AVI|rmvb|RMVB|mpeg|MPEG|wmf|WMF|mov|MOV|mkv|MKV|mp4|MP4)$/i
        const regAudio = /.(mp3)$/i
        if (regImg.test(fileName)) return 'file-image'
        if (regPDF.test(fileName)) return 'file-pdf'
        if (regWord.test(fileName)) return 'file-word'
        if (regPPT.test(fileName)) return 'file-ppt'
        if (regVideo.test(fileName)) return 'file-video'
        if (regAudio.test(fileName)) return 'file-audio'
        if (regExcel.test(fileName)) {
          return 'file-excel'
        } else {
          return 'file-text'
        }
      },
      downloadFile () {
        console.log('file', this.file)
        let type = this.type
        if (!type) {
          const fileName = this.file.fileName || this.file.filename
          if (!fileName) {
            return
          }
          type = this.format(fileName)
        }
        const fid = this.file.fid || ''
        const file = this.file
        if (type === 'file-video' && fid && ((file[this.pathKey] && file[this.pathKey].includes('vodserver/download')) || !file[this.pathKey])) { // 附件视频下载
          if (file[this.pathKey] && file[this.pathKey].includes('vodserver/download')) {
            saveAsFile(`${file[this.pathKey]}?dl=1`)
            return
          }
          let requestUrl = []
          if (this.uploadHost) { // 主要用于工单上传附件的特殊业务场景
            requestUrl = [this.$api.common.getVideoInfoByFidV2({ fid }, this.uploadHost)]
          } else {
            requestUrl = [this.$api.common.getVideoInfoByFidV2({ fid }, 'https://upload.9xun.com')]
            if ([4000, 3000, 2000].includes(window.tenant.xtenant)) {
              requestUrl.push(this.$api.common.getVideoInfoByFidV2({ fid }, 'https://upload.iteng.com'))
            }
            if (window.tenant.xtenant === 1000) {
              requestUrl.push(this.$api.common.getVideoInfoByFidV2({ fid }, 'https://upload.zlf.co'))
            }
          }
          this.dloading = true
          Promise.all(requestUrl).then(([res, res1]) => {
            if (res && res.code === 0 && res?.data?.playPath?.length) {
              res.data.downloadPath && saveAsFile(`${res.data.downloadPath}?dl=1`)
            } else if (res1 && res1.code === 0 && res1?.data?.playPath?.length) {
              res.data.downloadPath && saveAsFile(`${res1.data.downloadPath}?dl=1`)
            } else {
              this.$message.error(res?.userMsg || res1?.userMsg || '下载视频附件失败')
            }
          }).catch((err) => {
            this.$message.error('下载视频附件失败：' + err.message)
          }).finally(() => {
            this.dloading = false
          })
        } else {
          saveAs(file[this.pathKey], file.fileName)
        }
      },
      // 预览
      open () {
        console.log('file', this.file)
        let type = this.type
        if (!type) {
          const fileName = this.file.fileName || this.file.filename
          if (!fileName) {
            return
          }
          type = this.format(fileName)
        }
        const file = this.file
        if (type === 'file-image') {
          let viewer = this.$el.querySelector('.images').$viewer
          viewer.show()
        } else if (type === 'file-pdf' || file.fileName.includes('.txt')) {
          window.open(file[this.pathKey])
        } else if (type === 'file-video') {
          this.$refs.player && this.$refs.player.show(file, this.pathKey, this.uploadHost)
        } else {
          window.open(`https://view.officeapps.live.com/op/view.aspx?src=${file[this.pathKey]}`)
        }
      }
    }
  }
</script>
<style lang='scss' scoped>
.download {
  line-height: 1;
}
</style>
