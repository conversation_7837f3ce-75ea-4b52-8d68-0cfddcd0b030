<template>
  <a-modal v-model="visible" :footer="null" @cancel="close">
    <a-icon slot="closeIcon" style="font-size: 30px;" type="close-circle" />
    <div class="mt-20" :id="playId" style="max-height: 80vh;max-width: 80vw"></div>
  </a-modal>
</template>

<script>
  import DPlayer from 'dplayer'
  import uuidv4 from 'uuid/v4'

  window.Hls = require('hls.js')

  export default {
    name: 'video-player',
    data () {
      return {
        visible: false,
        loading: false,
        error: null,
        player: null,
        playId: `playId-${uuidv4()}`,
      }
    },
    methods: {
      close () {
        this.player && this.player.destroy()
        this.visible = false
      },
      show (file, pathKey, host) {
        console.log(file)
        this.loading = true
        let params = {
          fid: file.fid
        }
        let requestUrl = []
        if (host) { // 主要用于工单上传附件的特殊业务场景
          requestUrl = [this.$api.common.getVideoInfoByFidV2(params, host)]
        } else {
          // 兼容老数据
          requestUrl = [this.$api.common.getVideoInfoByFidV2(params, 'https://upload.9xun.com')]
          if ([4000, 3000, 2000].includes(window.tenant.xtenant)) {
            requestUrl.push(this.$api.common.getVideoInfoByFidV2(params, 'https://upload.iteng.com'))
          }
          if (window.tenant.xtenant === 1000) {
            requestUrl.push(this.$api.common.getVideoInfoByFidV2(params, 'https://upload.zlf.co'))
          }
        }
        this.visible = true
        Promise.all(requestUrl).then(([res, res1]) => {
          const playAudio = (playPath) => {
            this.$nextTick(() => {
              console.log(this.playId)
              this.player = new DPlayer({
                container: document.getElementById(this.playId), // 播放器容器
                lang: 'zh-cn', // 语言，'en', 'zh-cn', 'zh-tw'
                video: {
                  defaultQuality: 2,
                  quality: playPath.map(p => ({
                    name: p.resolution,
                    url: p.url,
                    type: 'hls'
                  }))
                }
              })
            })
          }
          if (res && res.code === 0 && res?.data?.playPath?.length) {
            playAudio(res.data.playPath)
          } else if (res1 && res1.code === 0 && res1?.data?.playPath?.length) {
            playAudio(res1.data.playPath)
          } else {
            this.$message.error('获取视频失败')
          }
        }).catch((err) => {
          this.$message.error('获取视频失败：' + err.message)
        }).finally(() => {
          this.loading = false
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
:deep(.ant-modal-content) {
  background: rgba(0, 0, 0, 0.0);
  box-shadow: none;
}
:deep(.ant-modal-close-x) {
  margin: -15px -15px 0px 0px;
  // color: #fff;
}
</style>
