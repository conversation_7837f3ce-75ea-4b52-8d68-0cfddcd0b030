<script type='text/jsx' lang="jsx">
  import { getCurrentInstance, computed } from 'vue'
  import { useState } from '../model/useState.js'

  import ScoreChain from '../../common/components/scoreChain'
  import Status from '../../common/components/status'
  import RenderOrderType from '../../common/components/renderOrderType'
  import RenderLegalDuty from '../../common/components/renderLegalDuty'

  import CreateOrder from '../../common/components/create-order'
  import { createCreateState } from '../../common/components/create-order/use-state'
  import SmallQrcodeModal from '~/components/small-qrcode-modal.vue'

  export default {
    name: 'baseInfo-base',
    components: {
      RenderOrderType,
      ScoreChain,
      Status,
      RenderLegalDuty,
      CreateOrder,
      SmallQrcodeModal
    },

    setup () {
      const { proxy } = getCurrentInstance()
      const { state, detailInfo } = useState()

      const { state: createState, editOrder } = createCreateState()

      const hasGlgd = computed(() => proxy.$store.state.userInfo.Rank?.includes('glgd'))
      const goToList = (id) => {
        proxy.$router.push({
          path: '/logistics/productMkc/mkcCaiGouDetail',
          query: {
            sub_id: id
          }
        })
      }
      const otherUserShow = (userInfo) => {
        if (userInfo && userInfo.length) {
          return userInfo.map(item => item.name).join('，')
        } else {
          return '-'
        }
      }
      return {
        state,
        detailInfo,
        createState,
        editOrder,
        hasGlgd,
        goToList,
        otherUserShow
      }
    },
    render () {
      const { detailInfo, createState, hasGlgd, goToList, otherUserShow } = this

      const { creatOrderIsShow } = createState

      if (!detailInfo) return

      const renderRelation = () => {
        return this.$tnt.xtenant < 1000 && detailInfo.relationId ? <li><i>关联工单的id</i><router-link class="ml-5 blue" to={`/work-order/detail/${detailInfo.relationId}`} target="_blank">{detailInfo.relationId}</router-link></li> : null
      }

      const renderBase = () => {
        return (
          <ul class="work-order-uls work-order-li">
            <li>
              <i>ID</i><span>{ detailInfo.id }</span>
              { this.$tnt.xtenant < 1000 ? <small-qrcode-modal class="ml-8" code-value={`${this.$tnt.moaHost}/new/#/work-order/detail/${detailInfo.id}?workOrderId=${detailInfo.id}`}/> : null }
            </li>
            <li><i>工单类型</i><RenderOrderType typeNum={ detailInfo.type }/></li>
            <li><i>发起人</i><span>{ detailInfo.userName || '-'}</span></li>
            <li><i>责任主体</i><RenderLegalDuty typeNum={ detailInfo.legalDuty }/></li>
            {
              this.$tnt.xtenant < 1000 ? <li><i>{ [1, 2, 3].includes(detailInfo.type) ? '主要责任人' : '责任人' }</i><span>{detailInfo.dutyUser || '-'}</span></li>
                : <li><i>责任人</i><span>{detailInfo.dutyUser || '-'}</span></li>
            }
            {
              this.$tnt.xtenant < 1000 && [1, 2, 3].includes(detailInfo.type) ? <li><i>其他责任人</i><span>{otherUserShow(detailInfo.otherResponsibilityPersonList)}</span></li> : null
            }
            {detailInfo.legalDuty === 0 &&
              <li><i>责任门店</i><span>{ detailInfo.area || '-' }</span></li>
            }
            {this.$tnt.xtenant >= 1000 ? detailInfo.type === 5
              ? <li><i>工单分类</i><span>{ detailInfo.kindScores[0].kindName || '-'}</span></li>
              : detailInfo.kindScores.map(it => (
                <li>
                  <i class='col-2'>工单分类/分值/归属部门</i>
                  <p>
                    <span>{ it.kindName || '-' }</span>/
                    <span>{ it.score || '-' }</span>/
                    <span>{ it.kindDepartName || '-' }</span>
                  </p>
                </li>
              )) : null
            }
            <li><i>开单时间</i><span>{ detailInfo.createTime || '-' }</span></li>
            <li><i>分值生效时间</i><span>{ detailInfo.scoreEffectiveTime || '-' }</span></li>
            {
              detailInfo.type === 5
                ? <li><i>方案出具时间</i><span>{ detailInfo.schemeTime || '-' }</span></li>
                : null
            }
            <li><i>整改时间</i><span>{ detailInfo.predictTime || '-' }</span></li>
            <li><i>覆盖量</i><span>{ detailInfo.checkCount || '-' }</span></li>
            <li><i>分值关联对象</i>
              <span>
                {
                  detailInfo.scoreChain?.length
                    ? detailInfo.scoreChain.split(',').map(item => (
                        <ScoreChain scoreChainNum={ item }/>
                      ))
                    : '-'
                }
              </span>
            </li>
            <li><i>状态</i><Status statusNum={ detailInfo.status }/></li>
            {
              detailInfo.type === 5
                ? <li><i>是否密送</i>{ detailInfo.hasSecret === true ? '是' : '否' }</li>
                : null
            }
            {renderRelation()}
          </ul>
        )
      }
      const renderCross = () => {
        return (
          <ul class="work-order-uls">
            <li><i>ID</i><span>{ detailInfo.id }</span></li>
            <li><i>工单类型</i><RenderOrderType typeNum={ detailInfo.type }/></li>
            <li><i>发起人</i><span>{ detailInfo.userName || '-'}</span></li>
            <li><i>责任主体</i><RenderLegalDuty typeNum={ detailInfo.legalDuty || '-'}/></li>
            { detailInfo.legalDuty === 2 && <li><i>责任部门</i>{ detailInfo.departName || '-'}</li> }
            <li><i>责任人</i><span>{ detailInfo.dutyUser || '-'}</span></li>
            <li><i>开单时间</i><span>{ detailInfo.createTime || '-'}</span></li>
            <li><i>状态</i><Status statusNum={ detailInfo.status }/></li>
            <li><i>数量</i>{ detailInfo.productCount || '-'}</li>
            {
              detailInfo.largePurchaseId
                ? <li><i>大件采购单号</i><span class="pointer" onClick={() => goToList(detailInfo.largePurchaseId)}>{ detailInfo.largePurchaseId }</span></li>
                : <li><i>大件采购单号</i>-</li>
            }
            {renderRelation()}
          </ul>
        )
      }

      return (
        <a-card title='基础信息'>
        {
          this.$tnt.xtenant < 1000 && hasGlgd ? <template slot="extra"><a-button type="primary" onClick={ () => this.editOrder('fastCreate', detailInfo.id) }>创建关联工单</a-button></template> : null
        }
          { detailInfo.type === 4 ? renderCross() : renderBase() }
          { creatOrderIsShow && <CreateOrder /> }
        </a-card>
      )
    },
  }
</script>

<style lang='scss'>
  @import '../../common/style.scss';
  .work-order-li{
    li {
      color: #000 !important;
    }
  }
</style>
