<script type='text/jsx' lang="jsx">
  import { onMounted, getCurrentInstance } from 'vue'
  import { createState } from './model/useState.js'
  import { createLogsState } from './components/logs/useLogs'
  import { createHandleState } from './components/order-handle/use-handle'

  import Browse from '~/components/browse'
  import BaseInfo from './components/baseInfo'
  import OrderContent from './components/orderContent'
  import Logs from './components/logs/index'
  import OrderHandle from './components/order-handle/index.vue'
  import Association from './components/association'

  export default {
    name: 'detail',
    components: {
      Browse,
      BaseInfo,
      OrderContent,
      Logs,
      OrderHandle
    },
    setup () {
      const { proxy } = getCurrentInstance()
      const {
        state,
        setWorkOrderId,
        editDetailInfo,
        detailInfo,
        featchDetail
      } = createState()
      const { featchLogs } = createLogsState()
      createHandleState(editDetailInfo, detailInfo, featchDetail, featchLogs)

      onMounted(() => {
        const id = proxy.$route.params.id
        document.title = `工单详情【${id}】`
        setWorkOrderId(id)
        featchDetail(id)
      })

      return {
        state,
        detailInfo
      }
    },

    render () {
      const { workOrderId } = this.state
      if (!this.detailInfo) return

      return (
        <page title={`工单详情【${workOrderId}】`}>
          {
            this.$tnt.xtenant === 0 &&
            <Browse slot="extra" pageType={ 2 } id={ workOrderId }/>

          }
          <div style={{ width: '1200px', margin: '0 auto', paddingBottom: '140px' }}>
            <BaseInfo class="mb-20"/>
            <orderContent class="mb-20"/>
            {
              this.$tnt.xtenant < 1000 && this.detailInfo.status === 9 && this.detailInfo.addKindScores && this.detailInfo.addKindScores.length ? <Association class="mb-20"/> : null
            }
            <Logs/>
            <OrderHandle/>
          </div>
          { this.$tnt.xtenant === 0 &&
            <a class='router-link'
              target="_blank"
              href="https://oa.9ji.com/staticpc/#/office/library/article/6214?auditStatus=1">
              规则
            </a>
          }
        </page>
      )
    },
  }
  </script>
  <style lang='scss' scoped>
  .router-link{
    position: fixed;
    z-index: 9;
    right: 30px;
    bottom: 50px;
    margin-left: 16px;
    width: 40px;
    height: 40px;
    font-size: 13px;
    padding: 4px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    background: #1890FF;

    &:hover{
      background: #1a74ba
    }
    &:visited{
      color: #fff;
      background: #3f9bf1;
    }
  }
  </style>
