import { reactive, inject, provide } from 'vue'
import useDetailInfo from '../../common/model/useDetailInfo'

const key = Symbol('workOrderDetail')

export function useState () {
  return inject(key)
}

export function createState () {
  const { editDetailInfo, detailInfo, featchDetail } = useDetailInfo()
  const state = reactive({
    loading: true,
    workOrderId: undefined,
    // 日志
    workOrderLogs: [],
    logsPagination: {
      current: 1,
      size: 50,
    },
    // 添加日志
    logs: {
      remark: '',
      loading: false
    },
    scheme: {
      remark: '',
      loading: false
    }
  })

  const setLoading = val => { state.loading = val }
  const setWorkOrderId = val => { state.workOrderId = val }

  const setWorkOrderLogs = val => { state.workOrderLogs = val }
  const changeObj = objKye => (val, key) => {
    if (key && typeof key === 'string') {
      state[objKye][key] = val
    } else {
      state[objKye] = val
    }
  }
  const setLogsPagination = changeObj('logsPagination')
  const setLogs = changeObj('logs')
  const setScheme = changeObj('scheme')

  const workOrderDetail = {
    state,
    setLoading,
    setWorkOrderId,
    setWorkOrderLogs,
    setLogsPagination,
    setLogs,
    setScheme,
    editDetailInfo,
    detailInfo,
    featchDetail
  }
  provide(key, workOrderDetail)
  return workOrderDetail
}
