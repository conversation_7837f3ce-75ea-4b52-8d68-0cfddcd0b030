<template>
  <ni-filter
    class="relative"
    :form="state.screening"
    @filter="doSearch"
    :label-width="100"
    :immediate="false"
    >
    <!-- :loading="loading" -->
      <ni-filter-item class="no-label">
        <a-input v-model="state.screening.numTypeValue" allowClear>
          <a-select
            slot="addonBefore"
            v-model="state.screening.numType"
            :options="options.numTypeOptions"
            style="width: 100px"
          />
        </a-input>
      </ni-filter-item>
      <ni-filter-item label='状态'>
        <a-select
          v-model="state.screening.workStatus"
          class="select-width"
          mode="multiple"
          :maxTagCount='1'
          :options="workStatusOptions"
          placeholder="状态"
          allowClear/>
      </ni-filter-item>
      <ni-filter-item label='地区'>
        <NiAreaSelect
          multiple
          allow-clear
          :max-tag-count="1"
          class="area-depart-selector"
          :userAreaOnly="!isFrontPeoplel"
          treeNodeFilterProp='label'
          placeholder="请选择地区"
          showType="SHOW_CHILD"
          v-model="state.screening.areaIds"
          style="width: 300px"/>
      </ni-filter-item>
      <ni-filter-item label='工单类型'>
        <a-select
          class="select-width"
          v-model="state.screening.type"
          mode="multiple"
          :maxTagCount="1"
          :options="orderTypeOptions"
          allowClear
        />
      </ni-filter-item>
      <ni-filter-item class="no-label">
        <a-input-group compact>
          <a-select
            v-model="state.screening.timeType"
            :options="options.timeTypeOptions"
            style="width: 30%"
          />
          <a-range-picker
            style="width: 70%"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            v-model="state.screening.timeSection"
          />
        </a-input-group>
      </ni-filter-item>
      <ni-filter-item v-show="showUserItem" class="no-label">
        <a-input v-model="state.screening.userTypeValue" style="width: 230px;">
          <a-select
            slot="addonBefore"
            v-model="state.screening.userType"
            :options="options.userTypeOptions"
            style="width: 100px"
          />
        </a-input>
      </ni-filter-item>
      <ni-filter-item label='工单分类' class="select-group relative">
        <OrderCategroy
          class="select-width"
          style="width: 200px"
          :maxTagCount="1"
          placeholder="请选择分类"
          :tree-checkable="true"
          v-model="state.screening.kindsId"/>
      </ni-filter-item>
      <ni-filter-item label='分值'>
        <a-select
          v-model="state.screening.fraction"
          mode="multiple"
          :maxTagCount='1'
          style="width: 166px"
          :options="options.fractionOptions"
          placeholder="选择分值"
          allowClear/>
      </ni-filter-item>
      <ni-filter-item label='责任主体' class="select-group">
        <a-select
          v-model="state.screening.legalDuty"
          :maxTagCount='1'
          style="width: 200px"
          mode="multiple"
          :options="options.legalDutyOptions"
          placeholder="选择责任主体"
          allowClear/>
      </ni-filter-item>
      <ni-filter-item label='分值关联对象'>
        <a-select
          v-model="state.screening.scoreChainList"
          :maxTagCount='1'
          mode="multiple"
          :options="$tnt.xtenant < 1000 ? options.scoreChainOptionsJiuJi : options.scoreChainOptions"
          placeholder="选择分值关联对象"
          allowClear/>
      </ni-filter-item>
      <ni-filter-item class="no-label" v-if="$tnt.xtenant < 1000">
        <a-checkbox
              v-model="state.screening.onlyRelation"
              >关联工单</a-checkbox
            >
      </ni-filter-item>
  </ni-filter>
</template>

<script type='text/jsx'>
  import { computed, onMounted, getCurrentInstance } from 'vue'
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions'
  import { options } from '../../common/model/constants.js'

  import { NiFilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import AreaDepartSelector from '~/components/staff/area-depart-selector'
  import OrderCategroy from '../../common/components/order-categroy'

  export default {
    name: 'index-screening',
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect,
      AreaDepartSelector,
      OrderCategroy,
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const stateObj = useState()
      const { state } = stateObj
      const {
        fetchData,
        fetchIsFrontPeople,
        pageInit,
      } = useActions()

      const doSearch = () => {
        fetchData(1)
      }

      onMounted(() => {
        pageInit()
        fetchIsFrontPeople()
      })

      return {
        state,
        isFrontPeoplel: computed(() => state.isFrontPeoplel), // 是否是前端人员
        showUserItem: computed(() => ['1', '4'].includes(state.screening.workType)),
        orderTypeOptions: computed(() => {
          const outputOptions = options.orderTypeOptions.filter(item => item.isOutput)
          return proxy.$tnt.xtenant === 0 ? options.orderTypeOptions : outputOptions
        }),
        workStatusOptions: computed(() => {
          const outputOptions = options.workStatusOptions.filter(item => item.isOutput)
          return proxy.$tnt.xtenant === 0 ? options.workStatusOptions : outputOptions
        }),
        userInfo: computed(() => proxy.$store.state.userInfo),
        options,
        doSearch
      }
    }
  }
</script>

<style lang='scss' scoped>
:deep(.ant-select-selection__choice){
  max-width: 60%;
}
:deep(.filter-item.no-label){
  .label{
          display: none !important;
        }
}
  .config-link {
    position: absolute;
    line-height: 32px;
  }
</style>
