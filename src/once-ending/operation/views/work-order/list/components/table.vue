<script type='text/jsx' lang="jsx">
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions'
  import { tabList } from '../../common/model/constants.js'

  import { NiTable } from '@jiuji/nine-ui'
  import Status from '../../common/components/status'
  import ScoreChain from '../../common/components/scoreChain'
  import ExcelAction from '~/pages/operation/components/excel-action'
  import CreateOrder from '../../common/components/create-order'
  import { createCreateState } from '../../common/components/create-order/use-state'
  import { cloneDeep } from 'lodash'
  import { getExportItems } from '@operation/util/common'
  import { getCurrentInstance } from 'vue'

  export default {
    name: 'index-table',
    components: {
      Status,
      ScoreChain,
      NiTable,
      ExcelAction,
      CreateOrder,
    },
    data () {
      return {
        columnsDepart: [
          {
            dataIndex: 'dutyDepartName',
            key: 'dutyDepartName',
            title: '责任部门',
            width: '6%'
          },
        ],
        columnsNotDepart: [
          {
            dataIndex: 'dutyUser',
            key: 'dutyUser',
            title: '责任人',
            width: '5%'
          },
          {
            dataIndex: 'otherResponsibilityPersonsStr',
            key: 'otherResponsibilityPersonsStr',
            title: '其他责任人',
            width: '5%'
          },
        ],
        columnsBase: [
          {
            dataIndex: 'id',
            key: 'id',
            title: 'ID',
            width: '5%',
            customRender: text => <router-link class="ml-5 blue" to={`/work-order/detail/${text}`} target="_blank">{text}</router-link>
          },
          {
            dataIndex: 'departName',
            key: 'departName',
            title: '地区',
            width: '5%'
          },
          {
            dataIndex: 'area',
            key: 'area',
            title: '责任门店',
            width: '6%',
            customRender: text => <div>{ text || '--' }</div>
          },
          {
            dataIndex: 'workKindsName',
            key: 'workKindsName',
            title: '工单分类',
            width: '14%',
            customRender: (text, record) => {
              if (!text) return '--'
              return <span>
                {
                  text.length > 16
                    ? <a-tooltip>
                        <template slot="title">{text}</template>
                        <p class='ellipsis'>{ text }</p>
                      </a-tooltip>
                    : <p class='ellipsis'>{ text }</p>
                }
              </span>
            }
          },
          {
            dataIndex: 'description',
            key: 'description',
            title: '工单内容',
            width: '12%',
            customRender: (text, record) => {
              return <div>
                { text?.length > 15 ? <a-popover>
                <div slot="content" style="maxWidth: 500px">{ text }</div>
                <div>{ text.substring(0, 15) }...</div>
              </a-popover>
              : <p>
                {text}
              </p>
            }
              </div>
            }
          },
          // {
          //   dataIndex: 'batchNumber',
          //   key: 'batchNumber',
          //   title: () => (
          //     <a-tooltip>
          //       <template slot="title">批次号生成规则：工单的一二三级分类标识-开单年月日</template>
          //       批次号 <a-icon type="question-circle" class='blue'/>
          //     </a-tooltip>
          //   ),
          //   width: '8%',
          //   customRender: text => <div>{ text || '--' }</div>
          // },
          {
            dataIndex: 'score',
            key: 'score',
            title: '分值',
            width: '4%',
            align: 'center',
            customRender: text => <div>{ text ?? '--' }</div>
          },
          {
            dataIndex: 'scoreChain',
            key: 'scoreChain',
            title: '分值关联对象',
            width: '12%',
            customRender: text => <div class="flex flex-wrap">{ text?.map(item => <ScoreChain scoreChainNum={ item }/>) || '--' }</div>
          },
          {
            dataIndex: 'createName',
            key: 'createName',
            title: '发放人',
            width: '5%'
          },
          {
            dataIndex: 'createTime',
            key: 'createTime',
            title: '开单时间',
            width: '10%',
            sorter: (a, b) => a.orderTime < b.orderTime ? 1 : -1
          },
          {
            dataIndex: 'scoreEffectiveTime',
            key: 'scoreEffectiveTime',
            title: '分值生效时间',
            width: '10%',
            sorter: (a, b) => a.scoreTime < b.scoreTime ? 1 : -1,
            customRender: text => <div>{ text || '--' }</div>
          },
          {
            dataIndex: 'relationId',
            key: 'relationId',
            title: '关联的工单id',
            hide: true,
            width: '8%',
            filterMultiple: false,
            show: this.$tnt.xtenant < 1000,
            customRender: text => this.$tnt.xtenant < 1000 && text ? <router-link class="ml-5 blue" to={`/work-order/detail/${text}`} target="_blank">{text}</router-link> : <span>-</span>
          },
          {
            dataIndex: 'status',
            key: 'status',
            title: '状态',
            width: '5%',
            filterMultiple: false,
            customRender: text => <Status statusNum={ text }/>
          },
          {
            dataIndex: 'actions',
            key: 'actions',
            title: '操作',
            width: '4%',
            customRender: (_, record) => (
              <a rel="noopener noreferrer" onClick={ () => this.editOrder('edit', record.id) }>编辑</a>
            )
          },
        ],
      }
    },
    setup (_, ctx) {
      const { proxy } = getCurrentInstance()
      const { state } = useState()
      const { showAllText, handleTabChange, handleTableChange, fetchData } = useActions()
      const { state: createState, editOrder } = createCreateState(fetchData)
      function toExport () {
        const exportItems = getExportItems(proxy.$route.path + '/', 'actions', state.cacheColumns)
        if (!exportItems?.length) {
          return false
        }
        exportItems.forEach((element, index) => {
          element === 'scoreChain' && (exportItems[index] = 'scoreChainStr')
          element === 'status' && (exportItems[index] = 'statusStr')
        })
        state.queryParam && (state.queryParam.exportItems = exportItems)
        proxy.$refs.exportExcelAction.exportData()
      }
      function containsAll () {
        // 总部检查、分公司检查、城市经理检查  1 2 3
        return [1, 2, 3].some(item => state.screening.type.includes(item))
      }
      return {
        state,
        editOrder,
        showAllText,
        handleTabChange,
        handleTableChange,
        createState,
        toExport,
        containsAll
      }
    },

    render () {
      const {
        state,
        columnsDepart,
        columnsNotDepart,
        columnsBase,
        createState,
        toExport
      } = this

      const { creatOrderIsShow } = createState

      const {
        tableData,
        pagination,
        loading,
        queryParam,
      } = state

      // 选择责任主体为部门时，展示部门
      let columns = columnsBase.filter(d => !Object.keys(d).includes('show') || d.show)
      let isDepartIn = false
      let columnsNotDepartNew = []
      if (queryParam?.legalDuty?.length) isDepartIn = queryParam.legalDuty.includes(2)
      if (queryParam?.type?.length) {
        if (this.$tnt.xtenant < 1000) {
          if (this.containsAll()) {
            columnsNotDepartNew = columnsNotDepart
            columnsNotDepartNew.forEach(item => {
              if (item.key === 'dutyUser') {
                item.title = '主要责任人'
              }
            })
          } else {
            columnsNotDepartNew = columnsNotDepart.filter(item => item.key !== 'otherResponsibilityPersonsStr')
            columnsNotDepartNew.forEach(item => {
              if (item.key === 'dutyUser') {
                item.title = '责任人'
              }
            })
          }
        } else {
          columnsNotDepartNew = columnsNotDepart.filter(item => item.key !== 'otherResponsibilityPersonsStr')
        }
      }
      const obs = isDepartIn ? columnsDepart : columnsNotDepartNew
      columns.splice(9, 0, ...obs)
      state.cacheColumns = cloneDeep(columns)
      return <div>
        <NiTable
          rowKey={ (record, index) => index }
          dataSource={tableData}
          columns={columns}
          scroll={{ y: '52vh' }}
          pagination={pagination}
          loading={loading}
          onChange={ (pagin) => { this.handleTableChange(pagin) } }
          bordered
        >
        <div slot="tool">
          <a-card
            size='small'
            bordered={ false }
            tabList={ tabList }
            activeTabKey={ state.screening.workType }
            onTabChange={ this.handleTabChange }
          />
        </div>
        <div slot="action" class='flex'>
          <a-button
            ghost
            type="primary"
            class='mr-8'
            onClick={ () => { this.editOrder('create') } }
          >
              新建工单
          </a-button>
          <a-button onClick={toExport}>
            <a-icon type="export" />
            导出数据
          </a-button>
          <ExcelAction style="display: none" ref="exportExcelAction" params={ queryParam } scene="workOrder" exportTableName="工单列表"/>
        </div>
        </NiTable>
        { creatOrderIsShow && <CreateOrder /> }
      </div>
    }
  }
</script>

<style lang='scss' scoped>
  .filterDropdown-checkbox{
    padding: 8px 0;
    width: 130px
  }
  .ant-radio-wrapper{
    padding:8px 16px;
  }
  .ant-modal-confirm{
    .ant-modal{
      top:35vh;
      &-mask{
        z-index: 1031;
      }
    }
  }
  :deep(.nine-table-bar){
    padding: 12px 0;
  }
  :deep(.nine-table-bar .nine-table-bar-main){
    align-items: center;
  }
  :deep(.ant-card-small > .ant-card-head){
    padding-left: 0;
  }
  :deep(.ant-card-head .ant-tabs-bar){
    border: none;
  }
  :deep(.ant-card-small > .ant-card-head) {
    border: none;
  }
  :deep(.ant-tabs .ant-tabs-large-bar .ant-tabs-tab) {
    padding: 0 0 10px;
  }
  :deep(.ant-table-thead > tr > th .ant-table-header-column){
    font-weight: 600;
  }
  :deep(.ellipsis){
    max-width: 15em;
  }
  :deep(.text-wrap){
    display: flex;
    overflow: hidden;
    .text{
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: justify;
      display: -webkit-box;
      -webkit-line-clamp: 9;
      -webkit-box-orient: vertical;
      position: relative;

      &::before{
        content: '';
        float: right;
        width: 0;
        height: 100%;
        margin-bottom: -21px;
      }

      .btn-showAll{
        float: right;
        clear: both;
        line-height: 21px;
        cursor: pointer;
        border:0;
        color: #31a6ff;
        background: none;
      }
    }
  }
</style>
