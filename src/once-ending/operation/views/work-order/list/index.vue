<script type='text/jsx' lang="jsx">
  import { getCurrentInstance, computed } from 'vue'
  import { createState } from './model/useState.js'
  import { NiListPage } from '@jiuji/nine-ui'

  import Screening from './components/screening'
  import Table from './components/table'

  export default {
    name: 'workOrder',
    components: {
      Screening,
      Table,
      NiListPage
    },
    setup (_) {
      const { proxy } = getCurrentInstance()
      const userInfo = computed(() => proxy.$store.state.userInfo)
      createState()
      return {
        userInfo
      }
    },
    render () {
      const { userInfo } = this
      return (
        <page>
          <template slot="extra">
            {
              userInfo.Rank.includes('gdpz') ? <router-link
                to={`/work-order/categroy-config`}
                target="_blank"
                class="blue config-link"
              >工单配置</router-link> : null
            }
          </template>
          <NiListPage push-filter-to-location={ false }>
            <Screening class='mb-8'/>
            <Table/>
          </NiListPage>
        </page>
      )
    },
  }
</script>
