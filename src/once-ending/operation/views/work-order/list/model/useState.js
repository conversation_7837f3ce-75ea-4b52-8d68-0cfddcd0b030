import { reactive, inject, provide } from 'vue'
import { cloneDeep } from 'lodash'
import moment from 'moment'

const key = Symbol('workOrder')

export function useState () {
  return inject(key)
}
const SCREENING = {
  workType: '1',
  numType: 1,
  numTypeValue: '',
  userType: 1,
  userTypeValue: '',
  workStatus: [],
  areaIds: [],
  timeType: 1,
  timeSection: window.tenant.xtenant < 1000 ? [moment().subtract(1, 'month').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')] : [],
  type: [1, 2, 3],
  kindsId: [],
  fraction: [],
  legalDuty: [],
  onlyRelation: false,
  scoreChainList: undefined
}
const PAGINATION = {
  current: 1,
  pageSize: 10,
  total: 0,
  pageSizeOptions: ['10', '20', '50', '100'],
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: total => `共计${total}条`
}

export function createState () {
  const state = reactive({
    loading: false,
    isFrontPeoplel: false,
    tableData: [],
    queryParam: undefined,
    screening: cloneDeep(SCREENING),
    screeningReset: undefined,
    pagination: cloneDeep(PAGINATION),
    cacheColumns: []
  })

  const setObj = objKey => (val, key) => {
    if (key && typeof key === 'string') {
      state[objKey][key] = val
    } else {
      state[objKey] = val
    }
  }
  const setScreening = setObj('screening')
  const resetScreening = () => { setScreening(cloneDeep(SCREENING)) }
  const setPagination = setObj('pagination')

  const setQueryParam = val => { state.queryParam = val }

  const setLoading = val => { state.loading = val }
  const setTableData = val => { state.tableData = val || [] }
  const setIsFrontPeoplel = val => { state.isFrontPeoplel = val }
  const setCreatOrderIsShow = val => { state.creatOrderIsShow = val }

  const workOrder = {
    state,
    setLoading,
    setIsFrontPeoplel,
    setCreatOrderIsShow,
    setTableData,
    setQueryParam,
    setScreening,
    resetScreening,

    setPagination,
  }
  provide(key, workOrder)
  return workOrder
}
