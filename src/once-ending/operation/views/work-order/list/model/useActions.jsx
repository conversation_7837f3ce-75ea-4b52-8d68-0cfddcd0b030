
import { getCurrentInstance } from 'vue'
import { message } from 'ant-design-vue'
import moment from 'moment'
import {
  FETCH_WORK_ORDER_LIST,
  FETCH_IS_FRONT_PEOPLE,
} from '@operation/store/modules/work-order/action-types'
import { useState } from './useState'
import { debounce } from 'lodash'

export default function useActions () {
  const instance = getCurrentInstance().proxy
  const {
    state,
    setScreening,
    setQueryParam,

    setIsFrontPeoplel,
    setPagination,
    setTableData,
  } = useState()

  const fetchData = async (current) => {
    if (current) setPagination(current, 'current')
    const params = _getParams()
    if (params.workType) params.workType = parseInt(params.workType)
    if (params.fraction?.length) {
      params.fraction.forEach(item => {
        params[item] = true
      })
      delete params.fraction
    }
    if (params.timeSection?.length) {
      params.startTime = moment(params.timeSection[0]).startOf('d').format('YYYY-MM-DD HH:mm:ss')
      params.endTime = moment(params.timeSection[1]).endOf('d').format('YYYY-MM-DD HH:mm:ss')
    }
    setQueryParam(params)
    instance.$indicator.open()
    const res = await instance.$store.dispatch(`operation/workOrder/${FETCH_WORK_ORDER_LIST}`, params)
    instance.$indicator.close()
    if (!res) return
    const { code, data } = res
    if (code === 0) {
      setTableData(data.records)
      setPagination(data.total, 'total')
    }
  }
  const fetchIsFrontPeople = async () => {
    const res = await instance.$store.dispatch(`operation/workOrder/${FETCH_IS_FRONT_PEOPLE}`)
    if (!res) return
    const { code, data } = res
    if (code === 0) {
      if (data) setIsFrontPeoplel(data)
    }
  }
  const handleTableChange = pagination => {
    setPagination({ ...pagination })
    fetchData()
  }
  const showAllText = (text) => {
    const h = instance.$createElement
    instance.$info({
      content: <div>{text}</div>,
      maskClosable: true,
      width: 720,
      icon: () => null,
    })
  }
  // 筛选参数过滤空选项
  const _getParams = function () {
    const params = { ...state.screening }
    if (instance.$tnt.xtenant >= 1000) {
      delete params.onlyRelation
    }
    for (let key in params) {
      if (
        (!params[key] && params[key] !== 0) ||
        (params[key] && Array.isArray(params[key]) && !params[key].length)
      ) {
        delete params[key]
      }
    }
    params.current = state.pagination.current
    params.size = state.pagination.pageSize
    return params
  }

  // tab
  const handleTabChange = key => {
    setScreening(key, 'workType')
    // 【我发出的 2】【我接收 3】的不显示接收人
    const userType = ['2', '3'].includes(key) ? null : 1
    setScreening(userType, 'userType')
    setScreening('', 'userTypeValue')
    fetchData(1)
  }
  const pageInit = () => {
    // 处理路由所携带的参数
    const {
      areaIds = '',
      startTime = '',
      endTime = '',
      kindsId = '',
      userTypeValue = '',
      userType = '',
      timeType = 1,
      legalDuty,
      fraction,
      type
    } = instance.$route.query
    if (kindsId) setScreening(kindsId.split(','), 'kindsId')
    if (areaIds) setScreening(areaIds.split(',').map(item => item + ''), 'areaIds')
    if (startTime || endTime) {
      const timeRanges = [moment(startTime), moment(endTime)]
      setScreening(timeRanges, 'timeSection')
      state.screening.timeType = +timeType
    }
    if (legalDuty) state.screening.legalDuty = [Number(legalDuty)]
    if (fraction) state.screening.fraction = fraction.split(',')
    if (type) state.screening.type = [Number(type)]

    if (userTypeValue) {
      setScreening(userTypeValue, 'userTypeValue')
      setScreening(Number(userType), 'userType')
    }

    fetchData()
  }

  return {
    fetchData,
    fetchIsFrontPeople,
    showAllText,
    handleTableChange,
    handleTabChange,
    pageInit
  }
}
