<script lang="jsx">
  import { defineComponent, ref, nextTick, getCurrentInstance } from 'vue'
  import { dateFormat } from '~/util/common'
  import { Button, Form, Input, Icon, Switch, message } from 'ant-design-vue'
  import { NiAreaSelect, NiListPage, NiTable } from '@jiuji/nine-ui'
  import nineUpload from '@jiuji/nine-upload'
  import { columns } from './constants'
  import NoData from '@operation/components/no-data'
  import Log from './components/log'
  import hotPhone from './components/hot-phone'
  import StoreTvConfig from './components/store-tv-config'
  import {
    GET_THREE_CONFIG,
    DEL_THREE_CONFIG,
    GET_FOUR_CONFIG,
    SAVE_THREE_CONFIG,
    SAVE_FOUR_CONFIG
  } from '@operation/store/modules/large-data-screen/action-types'
  export default defineComponent({
    components: {
      NiAreaSelect,
      NiListPage,
      NiTable,
      NoData,
      Log,
      hotPhone,
      StoreTvConfig
    },
    data () {
      return {
        locationCustomRender: (text, record) => {
          return <div style="background: rgba(0, 0, 0, 0.04);padding: 5px 0;border-radius: 2px;border: 1px solid rgba(0, 0, 0, 0.15);">
          {text}
          </div>
        },
        openedCustomRender: (text, record) => {
          return <div>
          <Switch checked={record.opened} checked-children="开" un-checked-children="关" onChange={checked => this.checkedChange(checked, record)} />
          </div>
        }
      }
    },
    computed: {
      columns () {
        columns[0].children.forEach(item => {
          if (this[`${item.dataIndex}CustomRender`]) item.customRender = this[`${item.dataIndex}CustomRender`]
        })
        return columns
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const lastUpdateDate = ref(dateFormat(new Date(), 'YYYY-MM-DD hh:mm:ss'))
      const showDrawer = ref(false)
      const file = ref({
        fileName: ''
      })
      const area = ref('')
      const dataSource = ref([])
      const loading = ref(false)
      const isFeatch = ref(false)
      const destroyedDrawer = ref(true)
      const showLog = function () {
        showDrawer.value = true
      }
      const areaChange = function (val) {
        area.value = val
        dataSource.value = []
      }
      const getThreeConfig = async function () {
        const res = await proxy.$store.dispatch(
          `operation/largeDataScreen/${GET_THREE_CONFIG}`)
        if (res) {
          lastUpdateDate.value = dateFormat(new Date(), 'YYYY-MM-DD hh:mm:ss')
          const { data, data: { fileName } } = res
          if (fileName) {
            data.name = fileName
            file.value = data
          } else {
            file.value = {
              fileName: ''
            }
          }
        }
      }

      const getFourConfig = async function () {
        if (!area.value) return message.warning('请选择门店后再查询')
        const params = {
          areaId: area.value,
          screenNumber: '4'
        }
        loading.value = true
        const res = await proxy.$store.dispatch(
          `operation/largeDataScreen/${GET_FOUR_CONFIG}`,
          params
        )
        loading.value = false
        if (res) {
          lastUpdateDate.value = dateFormat(new Date(), 'YYYY-MM-DD hh:mm:ss')
          isFeatch.value = true
          const { data } = res
          dataSource.value = data
        }
      }

      const delFile = async function () {
        const { id, name } = file.value
        const o = { fileName: '' }
        if (id) {
          o.id = id
          o.name = name
        }
        file.value = o
      }

      const saveThreeConfig = async function () {
        const { id, fileName, name } = file.value
        if (!id && !fileName) return
        if (fileName) {
          const params = { ...file.value }
          const res = await proxy.$store.dispatch(
            `operation/largeDataScreen/${SAVE_THREE_CONFIG}`, params)
          if (res) {
            threeConfigCallback()
          }
        } else {
          const params = {
            id,
            fileName: name
          }
          const res = await proxy.$store.dispatch(
            `operation/largeDataScreen/${DEL_THREE_CONFIG}`, params)
          if (res) {
            threeConfigCallback()
          }
        }
      }

      const threeConfigCallback = function () {
        getThreeConfig()
        message.success('配置成功')
      }

      const saveFourConfig = async function (record, checked) {
        const params = {
          flag: checked,
          areaId: area.value,
          id: record.id,
          screenNumber: '4'
        }
        const res = await proxy.$store.dispatch(
          `operation/largeDataScreen/${SAVE_FOUR_CONFIG}`, params)
        if (res) {
          record.opened = checked
          fourConfigCallback()
        }
      }

      const fourConfigCallback = function () {
        lastUpdateDate.value = dateFormat(new Date(), 'YYYY-MM-DD hh:mm:ss')
        message.success('配置成功')
      }

      const upload = async function () {
        let pickFile = {}
        const { err, res } = await nineUpload({
          accept: '.mp4',
          multiple: false,
          form: {
            collection: 'oa'
          },
          maxSize: 50000000,
          onPickFiles: async files => { // files是文件对象，如果要对文件进行处理，处理完成之后要在返回对象里加上files对象
            pickFile = files[0]
            if (window.nineUploadData) {
              return window.nineUploadData
            }
            const { code, data, userMsg } = await proxy.$api.common.getUploadToken()
            if (code === 0) {
              window.nineUploadData = data
              setTimeout(() => {
                window.nineUploadData = null
              }, 30 * 60 * 1000)
              return data
            } else {
              this.$message.error(userMsg)
            }
          }
        })
        if (err) {
          message.error(`${err.name}上传失败：${err.err}`)
          return
        }
        if (res) {
          const { fid } = res
          const lastIndex = fid.lastIndexOf('.')
          const suffix = fid.slice(lastIndex)
          if (suffix !== '.mp4') return message.error('请上传mp4格式的文件')
          const filePath = res.playPath.find(d => d.resolution === '720p').url
          const { name: fileName, size: fileSize } = pickFile
          file.value = {
            filePath,
            fileName,
            fileSize,
            fid
          }
        } else {
          message.error('上传失败')
        }
      }

      const checkedChange = function (checked, record) {
        // record.opened = checked
        saveFourConfig(record, checked)
        dataSource.value.forEach(item => {
          if (item.id !== record.id) {
            item.opened = false
          }
        })
      }

      const updateShowDrawer = function (val) {
        showDrawer.value = val
      }

      const onDestroyedDrawer = function () {
        destroyedDrawer.value = false
        nextTick(() => {
          destroyedDrawer.value = true
        })
      }

      getThreeConfig()
      return {
        lastUpdateDate,
        showLog,
        file,
        area,
        areaChange,
        dataSource,
        loading,
        isFeatch,
        getFourConfig,
        delFile,
        saveThreeConfig,
        upload,
        checkedChange,
        showDrawer,
        destroyedDrawer,
        updateShowDrawer,
        onDestroyedDrawer
      }
    },
    render () {
      const { lastUpdateDate, showDrawer, destroyedDrawer, onDestroyedDrawer, updateShowDrawer, showLog, file, area, areaChange, columns, dataSource, loading, isFeatch, getFourConfig, delFile, saveThreeConfig, upload } = this
      return (
    <page class="large-data-screen">
      <div slot="extra">
        <div class="header-box">
          <span>数据更新于{lastUpdateDate}</span>
          <Button
            onClick={showLog}
            type="primary"
            style="display:flex;align-items:center"
          >
            <img
              style="width:16px;height:16px;margin-right:8px"
              src={require('../../../../assets/images/log.png')}
            />
            操作日志
          </Button>
        </div>
      </div>
      <div class="box one-box">
        <div class="box-header">
          <div class="box-title">热销机型排行配置</div>
          <div class="box-tip">
            对全区域热销机型排行板块进行数据显示配置
          </div>
        </div>
        <hotPhone/>
      </div>
      <div class="box three-box mb-16" v-show={ false }>
        <div class="box-header">
          <div class="box-title">备用视频配置内容</div>
          <div class="box-tip">
            替换所有门店屏幕无数据后显示的文件信息，上传后需点击保存。备用功能，暂时停用。
          </div>
        </div>
        <div class="file-box">
          <Form layout="inline">
            <Form.Item label="文件">
              <Input value={file.fileName} disabled style="width:266px" />
            </Form.Item>
            <Form.Item>
              <Button onClick={delFile} style="margin-right:24px">
                删除
              </Button>
              <Button onClick={saveThreeConfig} type="primary">
                保存
              </Button>
            </Form.Item>
          </Form>
          <div class="upload-box">
            <div class="upload-trigger" onClick={upload}>
              <Icon type="upload" style="margin-right:5px;font-size:13px" />
              <span>上传文件</span>
            </div>
            <div class="upload-tip">
              支持扩展名：MP4：大小限制：50MB之内
            </div>
          </div>
        </div>
      </div>
      <div class="box four-box">
      <div class="box-header">
          <div class="box-title">实时监控配置内容</div>
          <div class="box-tip">
            选择实时监控配置内容，只可显示一个监控画面。
          </div>
        </div>
        <div class="form-box">
        <Form layout="inline">
            <Form.Item label="地区">
              <NiAreaSelect style="width:266px" allow-clear={true} show-search={true} value={area} onChange={areaChange} />
            </Form.Item>
            <Form.Item>
              <Button onClick={getFourConfig} type="primary">
                查询
              </Button>
            </Form.Item>
          </Form>
        </div>
        <div class="table-box">
          <NiListPage>
            <NiTable
            locale={{ emptyText: <NoData is-featch={isFeatch} /> }}
            columns={columns}
            dataSource={dataSource}
            loading={loading}
            bordered={true}
            footer={null}
            pagination={false}>
            </NiTable>
          </NiListPage>
        </div>
      </div>
      <div class="box mt-16">
      <div class="box-header">
          <div class="box-title">门店电视画面配置</div>
          <div class="flex flex-align-center">
            <div class="box-tip">
            对全区域门店电视播放画面进行配置
          </div>
          <Button onClick={() => { this.$refs.StoreTvConfig.addItem() }} style="margin-left: 432px" type="primary">添加</Button>
          </div>
        </div>
        <StoreTvConfig ref="StoreTvConfig"/>
      </div>
      {
        destroyedDrawer && <Log show-drawer={showDrawer} {...{ on: { 'update:showDrawer': val => { updateShowDrawer(val) } } }} onDestroyedDrawer={onDestroyedDrawer}></Log>
      }
    </page>
    )
    }
  })
</script>
<style lang="scss">
.large-data-screen {
  position: relative;
  .ant-page-header-heading-extra {
    float: none;
    line-height: 40px;
    font-size: 12px;
    color: #9c9c9c;
    .header-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>
<style lang="scss" scoped>
.large-data-screen {
  .box {
    padding: 16px 24px;
    background: #fff;
    .box-header {
      .box-title {
        font-size: 18px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 18px;
      }
      .box-tip {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.4);
        line-height: 14px;
        margin: 16px 0;
      }
      &::after {
        content: "";
        width: 100%;
        height: 1px;
        display: block;
        background: #dfdfdf;
      }
    }
  }
  .one-box {
    margin-bottom: 16px;
  }
  .three-box{
    .file-box {
      margin-top: 30px;
    }
    .upload-box {
      margin-left: 40px;
      .upload-trigger {
        width: 266px;
        display: flex;
        justify-content: center;
        margin-top: 30px;
        border-radius: 2px;
        border: 1px dashed #dfdfdf;
        cursor: pointer;
        align-items: center;
        height: 30px;
      }
      .upload-tip {
        margin-top: 10px;
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.4);
        line-height: 14px;
      }
    }
  }
  .four-box {
    margin-top: 16px;
    margin-bottom: 16px;
    .form-box {
      margin-top: 30px;
    }
    .table-box {
      width: 50%;
    }
  }
}
</style>
