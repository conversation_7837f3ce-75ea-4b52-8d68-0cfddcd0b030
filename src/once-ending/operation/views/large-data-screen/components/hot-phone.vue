<script lang="jsx">
  import { ref, defineComponent, getCurrentInstance } from 'vue'
  import * as constants from '../constants'
  import { debounce } from '@common/utils/common'
  import {
    GET_HOT_PHONE_CONFIG,
    SAVE_HOT_PHONE_CONFIG,
    SEARCH_PRODUCT
  } from '@operation/store/modules/large-data-screen/action-types'
  export default defineComponent({
    name: 'hot-phone',
    setup (props) {
      const { proxy } = getCurrentInstance()
      const areaZone = ref('0')
      const fetching = ref(false)
      const dataSource = ref([])
      const options = ref([])
      const loading = ref(false)
      const saveLoading = ref(false)
      const getHotPhone = async () => {
        loading.value = true
        const res = await proxy.$store.dispatch(`operation/largeDataScreen/${GET_HOT_PHONE_CONFIG}`, { areaZone: areaZone.value })
        loading.value = false
        if (res) {
          dataSource.value = []
          const records = res.data.records.map(item => ({
            ...item,
            select: item.productId ? { label: item.productName, key: item.productId } : undefined
          }))
          for (let i = 0; i < 10; i++) {
            dataSource.value.push(records[i] || { select: undefined, productSort: i + 1, areaZone: areaZone.value })
          }
        }
      }
      getHotPhone()
      const saveHotPhone = async () => {
        saveLoading.value = true
        const params = dataSource.value.map(item => ({
          productSort: item.productSort,
          areaZone: item.areaZone,
          id: item.id || undefined,
          productId: item.productId || undefined,
          productName: item.productName || undefined
        }))
        console.log('params', params)
        const res = await proxy.$store.dispatch(`operation/largeDataScreen/${SAVE_HOT_PHONE_CONFIG}`, params)
        saveLoading.value = false
        if (res) {
          proxy.$message.success('保存成功')
        }
      }
      const search = debounce(async (val, index) => {
        if (!val) return
        options.value = []
        fetching.value = true
        const params = {
          world: val,
          pid: '',
          limit: 20,
          areaZone: areaZone.value
        }
        const res = await proxy.$store.dispatch(`operation/largeDataScreen/${SEARCH_PRODUCT}`, params)
        fetching.value = false
        if (res) {
          res.data && (options.value = res.data.map(item => ({
            label: item.productName,
            key: item.pid
          })))
        }
      }, 300)

      function change (val, ops, record) {
        console.log('val', val)
        record.select = val ? { ...val } : undefined
        record.productName = val?.label || undefined
        record.productId = val?.key || undefined
      }
      function getPopupContainer () {
        return document.querySelector('.large-data-screen')
      }
      return {
        dataSource,
        options,
        search,
        fetching,
        loading,
        saveLoading,
        areaZone,
        change,
        saveHotPhone,
        getPopupContainer,
        getHotPhone
      }
    },
    render () {
      const { dataSource, columns, loading, saveLoading, saveHotPhone, getHotPhone } = this
      return <div class="table-box">
        <a-tabs onChange={ getHotPhone } v-model={ this.areaZone }>
          { constants.hotType.map(item => <a-tab-pane tab={ item.label } key={ item.value }/>) }
        </a-tabs>
        <a-table
          rowKey={ (r, i) => i }
          pagination={ false }
          columns={ columns }
          loading={ loading }
          bordered={ true }
          dataSource={ dataSource }/>
        <div class="flex flex-justify-end mt-16">
          <a-button loading={ saveLoading } type="primary" onClick={ saveHotPhone }>保存</a-button>
        </div>
      </div>
    },
    data () {
      return {
        columns: [
          {
            title: '排名',
            dataIndex: 'id',
            width: '250px',
            align: 'center',
            customRender: (text, record, index) => <span>第{ constants.sort[index + 1] }名</span>
          },
          {
            title: '商品',
            dataIndex: 'select',
            width: '500px',
            align: 'center',
            customRender: (text, record, index) => <a-select
              placeholder="请输入商品名称"
              value={ text }
              show-search
              labelInValue
              filterOption={ false }
              onSearch={ (val) => { this.search(val, index) } }
              onChange={ (val, ops) => { this.change(val, ops, record) } }
              options={ this.options }
              allowClear
              get-popup-container={ this.getPopupContainer }
              style="width: 400px">
              <div slot="notFoundContent" class="full-width flex flex-center" style="height: 60px">
                { this.fetching ? <a-spin size="small"/> : '暂无数据' }
              </div>
            </a-select>
          }
        ]
      }
    }
  })
</script>

<style scoped lang="scss">
.table-box {
  width: 800px;
  margin-top: 8px
}
</style>
