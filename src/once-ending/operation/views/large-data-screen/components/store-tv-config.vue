<script lang="jsx">
  import { defineComponent, ref, getCurrentInstance } from 'vue'
  import { Tabs, Switch, InputNumber, Button, Icon, message } from 'ant-design-vue'
  import { hotType, storeTvConfigDefaultFormData } from '../constants'
  import Uploader from '~/components/uploader'
  import { NiImg, NiAreaSelect } from '@jiuji/nine-ui'
  import VideoPlayer from '~/components/uploader/video-player'
  import { GET_AREA_TV_CONFIG, SAVE_OR_UPDATE_AREA_TV_CONFIG } from '@operation/store/modules/large-data-screen/action-types'
  import areaSelect from './area-select.vue'
  export default defineComponent({
    components: {
      NiImg,
      VideoPlayer,
      NiAreaSelect,
      areaSelect
    },
    setup () {
      const { proxy } = getCurrentInstance()

      const active = ref('0')

      const formData = ref({
        ...storeTvConfigDefaultFormData,
      })

      const imgList = ref([])

      const videoList = ref([])

      const removeImg = function (e, i, single) {
        e.stopPropagation()
        single.pictures.splice(i, 1)
      }

      const previewImg = function (d) {
        window.open(d.filePath)
      }

      const player = ref(null)
      const previewVideo = function (d) {
        player.value.show(d, 'filePath')
      }

      const removeVideo = function (i, single) {
        single.videos.splice(i, 1)
      }

      const rest = function () {
        formData.value = {
          ...storeTvConfigDefaultFormData,
        }
      }

      const getStoreTvConfig = async function () {
        const params = {
          type: active.value,
        }
        const res = await proxy.$store.dispatch(
          `operation/largeDataScreen/${GET_AREA_TV_CONFIG}`,
          params
        )
        function getFile (fileList) {
          return fileList?.length ? fileList.map(d => {
            const { id, name: fileName, url: filePath, fid } = d
            return {
              id,
              fileName,
              filePath,
              fid,
            }
          }) : []
        }
        if (res) {
          if (res.data) {
            const {
              data: { configList, opened, id, intervals, screenIntervals },
            } = res
            const cacheList = configList.map((item) => {
              const { pictures, videos } = item
              return {
                ...item,
                pictures: getFile(pictures),
                videos: getFile(videos)
              }
            })
            formData.value = {
              opened,
              id,
              intervals,
              screenIntervals,
              configList: cacheList,
              removeIds: []
            }
          } else {
            rest()
          }
        } else {
          rest()
        }
      }
      getStoreTvConfig()

      const save = async function () {
        if (formData.value.intervals < 1) {
          return message.warning('图片轮播间隔时间大于0')
        }
        if (formData.value.screenIntervals < 1) {
          return message.warning('数据大屏轮播间隔时间大于0')
        }
        if (formData.value.configList.find(it => !it?.areaIds?.length)) {
          return message.warning('请选择适用门店')
        }
        const params = {
          type: active.value,
          ...formData.value
        }
        const res = await proxy.$store.dispatch(
          `operation/largeDataScreen/${SAVE_OR_UPDATE_AREA_TV_CONFIG}`,
          params
        )
        if (res) {
          message.success('保存成功')
          getStoreTvConfig()
        }
      }
      function deleteItem (single, index) {
        formData.value.configList.splice(index, 1)
        if (single.configId) {
          formData.value.removeIds.push(single.configId)
        }
      }
      function addItem () {
        formData.value.configList.push({
          pictures: [],
          videos: [],
          areaIds: []
        })
      }
      return {
        active,
        formData,
        imgList,
        getStoreTvConfig,
        removeImg,
        videoList,
        previewVideo,
        removeVideo,
        previewImg,
        player,
        save,
        deleteItem,
        addItem
      }
    },
    render () {
      const {
        getStoreTvConfig,
        formData,
        removeImg,
        previewImg,
        previewVideo,
        removeVideo,
        save,
        deleteItem,
      } = this
      return (
      <div>
        <Tabs onChange={getStoreTvConfig} v-model={this.active}>
          {hotType.map((item) => (
            <Tabs.TabPane tab={item.label} key={item.value} />
          ))}
        </Tabs>
        <div class="content">
          <div class="item">
            <div class="title">是否播放数据大屏</div>
            <Switch v-model={formData.opened} />
          </div>
          <div class="item">
            <div class="title">
              轮播间隔时间配置
              <span class="grey-d">（数据大屏、图片轮播时间）</span>
            </div>
          </div>
          <div class="flex items-center">
            <div class="item">
            <div class="required">图片轮播间隔时间：</div>
            <InputNumber
              style="width:180px"
              v-model={formData.intervals}
              precision={0}
              placeholder="请输入"
            ></InputNumber>
            <span style="margin-left:8px">S</span>
          </div>
          <div class="item ml-40">
            <div class="required">数据大屏轮播间隔时间：</div>
            <InputNumber
              style="width:180px"
              v-model={formData.screenIntervals}
              precision={0}
              placeholder="请输入"
            ></InputNumber>
            <span style="margin-left:8px">S</span>
          </div>
          </div>
          <div class="margin-top">
            { formData?.configList?.map((single, index) => <div class="single-box">
              <div class="item">
                <div class="required">适用门店：</div>
                { this.active === '0' ? <NiAreaSelect placeholder="请选择适用门店" maxTagCount={1} multiple v-model={single.areaIds} style="width: 360px"/>
                : <areaSelect placeholder="请选择适用门店" maxTagCount={1} multiple v-model={single.areaIds} style="width: 360px"/> }
                <Button onClick={() => deleteItem(single, index)} type="danger" style="margin-left: 200px">删除</Button>
              </div>
              <div class="item">
            <div class="title">图片</div>
            <Uploader
              accept="image/*"
              file-list={single.pictures}
              {...{
                on: {
                  'update:fileList': (val) => {
                    single.pictures = val
                  },
                },
              }}
              show-file-list={false}
              button-name={['上传图片']}
              path-key="filePath"
              showUploadAPP={false}
            />
          </div>
          {single?.pictures?.length ? (
            <div class="img-list mb-16">
              {single.pictures.map((d, i) => (
                <div
                  class="img-box relative"
                  onClick={() => {
                    previewImg(d)
                  }}
                >
                  <NiImg class="img" src={d.filePath} />
                  <Icon
                    onClick={(e) => {
                      removeImg(e, i, single)
                    }}
                    class="absolute"
                    style="right:-7px;top :-5px;cursor: pointer;"
                    type="close-circle"
                  />
                </div>
              ))}
            </div>
          ) : null}
          <div class="item">
            <div class="title">视频</div>
            <Uploader
              file-list={single.videos}
              {...{
                on: {
                  'update:fileList': (val) => {
                    single.videos = val
                  },
                },
              }}
              show-file-list={false}
              button-name={['上传视频']}
              path-key="filePath"
              accept=".mp4,.avi,.wmv,.mov,.wav,.flv"
              showUploadAPP={false}
            />
          </div>
          {single?.videos?.length ? (
            <div class="video-list mb-16">
              {single.videos.map((d, i) => (
                <div class="video-box flex flex-align-center">
                  <div class="name">{d.fileName}</div>
                  <Button
                    class="ml-5"
                    onClick={() => {
                      previewVideo(d)
                    }}
                    type="link"
                    size="small"
                  >
                    查看
                  </Button>
                  <Button
                    onClick={() => {
                      removeVideo(i, single)
                    }}
                    type="link"
                    size="small"
                  >
                    删除
                  </Button>
                </div>
              ))}
            </div>
          ) : null}
            </div>) }
          </div>
        </div>
        <VideoPlayer ref="player" />
        <Button onClick={save} type="primary">
          保存
        </Button>
      </div>
      )
    },
  })
</script>
<style lang="scss" scoped>
.content {
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    .title {
      margin-right: 20px;
      font-weight: 700;
    }
  }
  .img-list {
    display: flex;
    .img-box {
      width: 100px;
      height: 90px;
      margin-right: 20px;
      .img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .required {
    &::before {
      display: inline-block;
      margin-right: 4px;
      color: #f5222d;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: "*";
    }
  }
}
.ml-40 {
  margin-left: 40px;
}
.single-box {
  padding: 16px;
  border: 1px solid #dfdfdf;
  margin-bottom: 24px;
  border-radius: 8px;
}
</style>
