<template>
  <TreeSelect
    :tree-data="treeData"
    :value="valueLocal"
    show-search
    allow-clear
    :placeholder="placeholder"
    :max-tag-count="maxTagCount"
    :show-checked-strategy="showType"
    search-placeholder="请输入"
    style="width: 100%"
    v-bind="$attrs"
    :filter-tree-node="filterNode"
    :tree-checkable="multiple"
    :dropdown-style="dropdownStyle"
    @change="onChange"
  />
</template>
<script>
  import { defineComponent, watch, ref, getCurrentInstance } from 'vue'
  import { TreeSelect } from 'ant-design-vue'
  import { treeWalk } from '~/util/treeUtils'
  const SHOW_ALL = TreeSelect.SHOW_ALL
  const SHOW_PARENT = TreeSelect.SHOW_PARENT
  const SHOW_CHILD = TreeSelect.SHOW_CHILD
  export default defineComponent({
    name: 'AreaSelect',
    components: { TreeSelect },
    props: {
      value: {
        type: [String, Array],
        default: undefined
      },
      dropdownStyle: {
        type: Object,
        default: () => {
          return { maxHeight: '400px', overflow: 'auto' }
        }
      },
      placeholder: {
        type: String,
        default: '请选择地区'
      },
      multiple: {
        type: Boolean,
        default: true
      },
      maxTagCount: {
        type: Number,
        default: 1
      },
      // 只能选择末节点
      leafOnly: {
        type: Boolean,
        default: false
      },
      // 回填方式
      showType: {
        type: String,
        default: 'SHOW_CHILD'
      },
      zone: {
        type: String,
        default: '1'
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const valueLocal = ref(undefined)
      watch(
        () => props.value,
        val => {
          valueLocal.value = val
        },
        { immediate: true, deep: true }
      )
      const treeData = ref([])
      function filterNode (value, node) {
        if (!value) return true
        return node.label.toLowerCase().indexOf(value.toLowerCase()) !== -1
      }
      const loadTreeData = async () => {
        let res = null

        const key = 'sessionAreaDataScoped' + props.zone
        res = sessionStorage.getItem(key)
        if (!res) {
          const response = await proxy.$api.operation.getAreaTree({ zone: props.zone, type: '2' })
          if (!response) return
          res = response.data?.areaOptions || []
          sessionStorage.setItem(key, JSON.stringify(res))
        } else {
          res = JSON.parse(res)
        }
        treeData.value = res

        if (props.leafOnly) {
          treeWalk(
            treeData.value,
            node => (node.disableCheckbox = !!node?.children?.length)
          )
        }
      }
      loadTreeData()
      function onChange (value, label, extra) {
        proxy.$emit('input', value, label, extra)
      }
      return {
        valueLocal,
        treeData,
        onChange,
        filterNode
      }
    },
    data () {
      return {
        SHOW_ALL,
        SHOW_PARENT,
        SHOW_CHILD
      }
    }
  })
</script>

<style scoped></style>
