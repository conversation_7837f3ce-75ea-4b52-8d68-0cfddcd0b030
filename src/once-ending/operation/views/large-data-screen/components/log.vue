<script lang="jsx">
  import { defineComponent, ref, computed, watch, getCurrentInstance } from 'vue'
  import { Drawer, Steps } from 'ant-design-vue'
  import {
    GET_POERATION_LOG
  } from '@operation/store/modules/large-data-screen/action-types'
  export default defineComponent({
    props: {
      showDrawer: {
        type: Boolean,
        default: false
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const visible = computed({
        set: val => {
          proxy.$emit('update:showDrawer', val)
        },
        get: () => props.showDrawer
      })
      const operationLog = ref([])
      const flagDisplay = ref(false)
      const current = ref(1)
      watch(() => visible.value, val => {
        val && getOperationLog()
      })
      const getOperationLog = async function () {
        const params = {
          current: current.value,
          size: 10,
          screenNumbers: '4,5,13,14,15'
        }
        const res = await proxy.$store.dispatch(
          `operation/largeDataScreen/${GET_POERATION_LOG}`, params)
        if (res) {
          const { data: { records, total } } = res
          operationLog.value = operationLog.value.concat(records)
          flagDisplay.value = operationLog.value.length < total
        }
      }
      const mutShowDrawer = function () {
        current.value++
        getOperationLog()
      }

      const afterVisibleChange = function (visible) {
        if (!visible) {
          proxy.$emit('destroyedDrawer')
        }
      }
      return {
        visible,
        operationLog,
        flagDisplay,
        mutShowDrawer,
        afterVisibleChange
      }
    },
    render () {
      const { visible, operationLog, flagDisplay, mutShowDrawer, afterVisibleChange } = this
      return (
      <Drawer title="操作日志" placement="right" visible={visible} width={480} mask-closable={true} onClose={() => { this.visible = false }} after-visible-change={afterVisibleChange}>
        <Steps direction="vertical" progress-dot current={0}>
          {operationLog.map(d => (
            <Steps.Step>
              <template slot="title">
                <span domPropsInnerHTML={ d.type === 5 ? '热销机型排行配置-常规门店' : d.type === 13 ? '热销机型排行配置-MBA门店' : d.type === 4 ? '实时监控配置' : d.type === 14 ? '门店电视画面轮播配置-常规门店' : d.type === 15 ? '门店电视画面轮播配置-MBA门店' : `备用视频配置`}></span>
              </template>
              <template slot="description">
              <span domPropsInnerHTML={d.content}></span>
              <br />
                <span
                  domPropsInnerHTML={d.user}
                  class="blue"
                ></span>
                <span domPropsInnerHTML={d.creatTime}></span>
                <br />
              </template>
            </Steps.Step>
          ))}
        </Steps>
        <div style="right: 0;bottom: 0;width: 100%;border-top: 1px solid #e9e9e9;padding: 20px 16px 10px 16px;background: #fff;text-align: center;z-index: 1;">
          {flagDisplay ? (
            <a-button type="primary" onClick={mutShowDrawer}>
              查看更多
            </a-button>
          ) : (
            <p style="color:#bababa">没有更多数据</p>
          )}
        </div>
      </Drawer>
      )
    }
  })
</script>
<style lang="scss" scoped>
:deep(.ant-steps-item-content) {
  overflow: visible;
}
:deep(.ant-steps-item-title) {
  width: 400px;
  height: auto;
  margin: -20px 0 0 20px;
}
:deep(.ant-steps-item-description) {
  width: 400px;
  margin: 0 0 0 20px;
  font-size: 15px;
}
</style>
