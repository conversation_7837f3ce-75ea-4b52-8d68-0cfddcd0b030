<script lang="jsx">
  import { defineComponent, ref, onActivated, getCurrentInstance } from 'vue'
  import {
    sourceOptions,
    tagOptions,
    catOptions,
    searchStateOptions,
    newPlainOptions,
    dateTypeOptions,
    searchKindOptions,
    columns,
    complaintRankOptions
  } from '../../constants'
  import moment from 'moment'
  import { Select, Input, Checkbox, DatePicker } from 'ant-design-vue'
  import NoData from '@operation/components/no-data'
  import { NiListPage, NiFilter, NiFilterItem, NiTable, NiDepartSelect } from '@jiuji/nine-ui'
  import {
    SAASINFO,
    NEO_SAASINFO,
    FEEDBACK_LIST
  } from '@operation/store/modules/feedback/action-types'
  import Reason from '../../../complaint/components/reason-select'

  export default defineComponent({
    components: {
      NoData,
      NiListPage,
      NiFilter,
      NiFilterItem,
      NiTable,
      Reason,
      NiDepartSelect
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const form = ref({
        source: undefined,
        xtenant: undefined, // 合作伙伴
        tags: undefined, // 性质
        cat: undefined, // 定性分类
        touSuRank: undefined, // 投诉等级
        searchState: undefined, // 处理状态
        bonus: false, // 奖金
        fine: false, // 罚款
        overTime: false, // 超时
        // 高级筛选
        dateType: 1, // 时间筛选类型
        times: [], // 时间范围
        searchKind: undefined, // 其他筛选条件
        key: undefined,
        plain: [],
        touSuTypeVal: undefined,
        departIds: []
      })

      form.value.times = [
        moment(new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000)).format(
          'YYYY-MM-DD'
        ),
        moment(new Date()).format('YYYY-MM-DD')
      ]

      if (proxy.$route.query.searchKind && proxy.$route.query.key) {
        form.value.searchKind = Number(proxy.$route.query.searchKind)
        form.value.key = proxy.$route.query.key
        form.value.times = null
      }

      const xtenantOptions = ref([])

      const dataSource = ref([])

      const pagination = ref({
        current: 1,
        pageSize: 50,
        total: 0,
        pageSizeOptions: ['20', '50', '100', '200'],
        showQuickJumper: true,
        showTotal: total => `共计${total}条投诉`
      })

      const saasinfo = async function (type) {
        const res = await proxy.$store.dispatch(
          `operation/feedback/${type === 1 ? SAASINFO : NEO_SAASINFO}`
        )
        if (res) {
          const { data } = res
          xtenantOptions.value = data
        }
      }
      saasinfo()

      const loading = ref(false)
      const isFeatch = ref(false)
      const feedbackList = async function (cur) {
        if (cur) pagination.value.current = cur
        const params = JSON.parse(JSON.stringify(form.value))
        params.source = params.source ? params.source : null
        const { times, dateType, plain } = params
        if (times && dateType) {
          params.startTime = `${times[0]} 00:00:00`
          params.endTime = `${times[1]} 23:59:59`
        }
        params.bonus = plain.includes(1)
        params.fine = plain.includes(2)
        params.overTime = plain.includes(3)
        params.ckdIng = plain.includes(4)
        params.current = pagination.value.current
        params.size = pagination.value.pageSize
        delete params.times
        delete params.plain
        if (!params.searchKind || !params.key) {
          delete params.searchKind
          delete params.key
        }
        loading.value = true
        const res = await proxy.$store.dispatch(
          `operation/feedback/${FEEDBACK_LIST}`,
          params
        )
        loading.value = false
        isFeatch.value = true
        if (res) {
          const {
            data: { records, total }
          } = res
          dataSource.value = records
          pagination.value.total = total
        }
      }
      feedbackList()

      onActivated(() => {
        feedbackList()
      })

      const tableChange = function (paginations) {
        pagination.value = { ...paginations }
        feedbackList()
      }

      const getAddClass = function (item) {
        if (!item.addTime) return ''
        const newTime = new Date().getTime()
        const addTime = new Date(item.addTime).getTime()
        return newTime - addTime > 48 * 60 * 60 * 1000 ? 'red' : ''
      }

      const sourceChange = function (val) {
        form.value.xtenant = undefined
        saasinfo(val)
      }

      const filterOption = function (input, option) {
        return (
          option.componentOptions.children[0].text
            .toLowerCase()
            .indexOf(input.toLowerCase()) >= 0
        )
      }

      const toComplaintOvertime = function () {
        const routeData = proxy.$router.resolve({
          path: '/operation/statistics-report/feedback-complaint-overtime'
        })
        const url = routeData.href
        window.open(url, '_blank')
      }

      return {
        isFeatch,
        form,
        xtenantOptions,
        dataSource,
        pagination,
        feedbackList,
        tableChange,
        getAddClass,
        sourceChange,
        filterOption,
        loading,
        toComplaintOvertime
      }
    },
    render () {
      const {
        form,
        loading,
        feedbackList,
        xtenantOptions,
        sourceChange,
        filterOption,
        isFeatch,
        dataSource,
        getAddClass,
        pagination,
        tableChange,
        toComplaintOvertime
      } = this
      return (
      <page>
        <div slot="extra">
          <a-button type="primary" onClick={toComplaintOvertime}>投诉超时统计</a-button>
        </div>
        <ni-list-page push-filter-to-location={false}>
          <ni-filter
            form={form}
            loading={loading}
            onFilter={() => {
              feedbackList(1)
            }}
            label-width={100}
            immediate={false}
          >
            <ni-filter-item label="合作伙伴类型">
              <Select
                allowClear
                placeholder="请选择"
                v-model={form.source}
                onChange={sourceChange}
                options={sourceOptions}
              />
            </ni-filter-item>
            <ni-filter-item label="合作伙伴">
              <Select
                allowClear
                placeholder="请选择"
                v-model={form.xtenant}
                options={xtenantOptions}
                show-search
                option-filter-prop="children"
                filter-option={filterOption}
              />
            </ni-filter-item>
            <ni-filter-item label="性质">
              <Select
                allowClear
                placeholder="请选择"
                v-model={form.tags}
                options={tagOptions}
                maxTagCount={1}
                mode="multiple"
              />
            </ni-filter-item>
            <ni-filter-item label="定性分类">
              <Select
                allowClear
                placeholder="请选择"
                v-model={form.cat}
                options={catOptions}
              />
            </ni-filter-item>
            <ni-filter-item label="投诉等级">
              <Select
                allowClear
                placeholder="请选择"
                v-model={form.touSuRank}
                checked={form.touSuRank}
                options={complaintRankOptions}
              />
            </ni-filter-item>
            <ni-filter-item label="处理状态">
              <Select
                allowClear
                placeholder="请选择"
                v-model={form.searchState}
                options={searchStateOptions}
              />
            </ni-filter-item>
            <ni-filter-item class="no-label-none">
              <Input.Group compact>
                <Select
                  allowClear
                  style="width:30%;"
                  placeholder="请选择"
                  v-model={form.dateType}
                  options={dateTypeOptions}
                />
                <DatePicker.RangePicker
                  allowClear={false}
                  style="width: 70%"
                  v-model={form.times}
                  format="YYYY-MM-DD"
                  valueFormat="YYYY-MM-DD"
                />
              </Input.Group>
            </ni-filter-item>
            <ni-filter-item class="no-label-none">
              <Input.Group compact>
                <Select
                  allowClear
                  style="width:30%;"
                  placeholder="请选择"
                  v-model={form.searchKind}
                  options={searchKindOptions.filter(d => d.show.includes(0))}
                />
                <Input
                  v-model={form.key}
                  style="width: 70%"
                  placeholder="输入内容"
                />
              </Input.Group>
            </ni-filter-item>
            <ni-filter-item label="投诉原因">
              <Reason
                onChange={list => {
                  form.touSuTypeVal = list
                }}
                v-model={form.touSuTypeVal}
                kind={3}
              />
            </ni-filter-item>
            <ni-filter-item label="责任部门">
              <NiDepartSelect
                v-model={form.departIds}
                multiple
                max-tag-count={1}
                returnedValue="ALL"
                onChange={(ids) => { form.departIds = ids }}
              />
            </ni-filter-item>
            <ni-filter-item class="no-label">
              <Checkbox.Group
                v-model={form.plain}
                name="checkboxgroup"
                options={newPlainOptions}
              />
            </ni-filter-item>
          </ni-filter>
          <ni-table
            style="margin-top: 10px;"
            class="feedback-table"
            rowKey="tsId"
            locale={{ emptyText: <NoData is-featch={isFeatch} /> }}
            dataSource={dataSource}
            columns={columns.filter(d => d.show.includes(0))}
            loading={loading}
            onChange={tableChange}
            pagination={pagination}
            scopedSlots={{
              tag: (text, item) => (
                <div>
                  {tagOptions.find(it => it.value === text)?.label || '--'}
                </div>
              ),
              addTime: (text, item) => (
                <div class={getAddClass(item)}>
                  {moment(item.addTime).format('YYYY-MM-DD HH:mm')}
                </div>
              ),
              toDetail: (text, item) => (
                <span>
                  <RouterLink to={`jiujiDetail/${item.tsId}`} target="_blank">
                    {text}
                  </RouterLink>
                </span>
              ),
              action: (text, item) => (
                <RouterLink to={`jiujiDetail/${item.tsId}`} target="_blank">
                  查看详情
                </RouterLink>
              ),
              departList: (text, item) => <div>
                { text?.map(it => <p>{it.departName}，<span class="red">扣分：{it.deductScore}</span></p>) || null }
              </div>
            }}
          ></ni-table>
        </ni-list-page>
      </page>
      )
    }
  })
</script>
<style lang="scss" scoped>
$success-color: #13cc1a;
$success-hover-color: darken($success-color, 5%);
.red {
  color: red;
}
.btn-success {
  margin-right: 20px;
  background-color: $success-color;
  color: white;
  border: none;

  &:focus {
    color: white;
    background: $success-hover-color;
  }

  &:hover {
    color: white;
    background: $success-hover-color;
  }
}

.ant-btn + .ant-btn {
  margin-left: 8px;
}

.ant-col {
  padding: 10px 5px;
  display: flex;
  flex-wrap: wrap;

  span {
    padding-right: 5px;
    white-space: nowrap;
  }
}
:deep(.no-label){
  .label{
    opacity: 0;
  }
}
:deep(.no-label-none){
  .label{
    display: none;
  }
}
</style>
