<script lang="jsx">
  import { defineComponent, computed, ref, getCurrentInstance } from 'vue'
  import { Radio, Button, Checkbox, message } from 'ant-design-vue'
  import {
    IS_SHOW_PROCESS_INFO
  } from '@operation/store/modules/feedback/action-types'

  export default defineComponent({
    props: {
      processLog: {
        type: Array,
        default: () => {
          return []
        }
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const radioType = ref(null)

      const list = computed(() => radioType.value === null ? props.processLog : props.processLog.filter(item => item.cate === radioType.value))

      const checked = function (item) {
        if (item.isShow) {
          const index = props.processLog.findIndex(i => i.id === item.id)
          props.processLog[index].isShow = false
        } else {
          const index = props.processLog.findIndex(i => i.id === item.id)
          props.processLog[index].isShow = true
        }
      }

      const check = async function () {
        const tsProcesses = []
        props.processLog.map(item => {
          tsProcesses.push({ ...item, isShow: Boolean(item.isShow) })
        })
        const params = {
          tsProcesses
        }
        const res = await proxy.$store.dispatch(
          `operation/feedback/${IS_SHOW_PROCESS_INFO}`,
          params
        )
        if (res) {
          message.success('保存成功')
          proxy.$router.go(0)
        }
      }

      return {
        list,
        checked,
        check,
        radioType
      }
    },
    render () {
      const { list, check, checked } = this
      return <div>
    <div>
      <Radio.Group v-model={this.radioType} button-style="solid" size="small">
        <Radio.Button value={null}>全部</Radio.Button>
        <Radio.Button value={0}>客诉处理进程</Radio.Button>
        <Radio.Button value={1}>客户追问</Radio.Button>
        <Radio.Button value={2}>落实反思进程</Radio.Button>
        <Radio.Button value={3}>优化点</Radio.Button>
      </Radio.Group>
      <Button onClick={() => { this.$emit('getProcess') }} size='small'> 刷新</Button>
    </div>
    <div class="box">
      <div class="flex flex-justify-between">
        <div class="flex flex-center">
          合作伙伴可见
        </div>
        <div class="flex flex-center">
          虚假跟进
        </div>
      </div>
      {
        list.length ? <div>{
          list.map(item => <div class="item flex-justify-between" style="display:flex;" key={item.index}>
        <div class="flex">
          <div class="flex flex-justify-center" style="width:56px;margin-right:.5em;flex-shrink: 0;">
            <Checkbox v-model={item.isShow} onClick={() => { checked(item) }} ></Checkbox>
          </div>
          <div class='dsc-content'>
            <span domPropsInnerHTML={item.dsc} class={item.fakeLog ? 'red' : ''}></span>
            <span>{`【${item.opUser}】`}</span>
            <span style="margin-left: 1em"> 附件:</span>
            {
              item.attachHtml.length ? <span>
              {
                item.attachHtml.map(img => <a style="margin-right:1em;" href={img.imgUrl} key={img.id} target="view_window" >{img.name}</a>)
              }
          </span> : <span> 无</span>
            }
            <span style="margin-left:1em;color:#828080;font-size:12px;">{item.intime}</span>
          </div>
        </div>
        <div class="flex flex-justify-center flex-child-noshrink margin-left" style="width: 56px">
          <Checkbox class={item.fakeLog ? 'red' : ''} style="margin:0;" v-model={item.fakeLog}></Checkbox>
        </div>
      </div>)
        }</div> : <div style="text-align: center;">暂无客诉处理进程</div>
      }
    </div>
    <Button type="primary" onClick={check}>保存修改</Button>
  </div>
    }
  })
</script>

<style lang="scss" scoped>
.box{
  margin: 1em .5em;
  .item + .item{
    margin-top: 1.1em;
  }
}
</style>
