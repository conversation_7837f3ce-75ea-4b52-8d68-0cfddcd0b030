<script lang="jsx">
  import { defineComponent, computed, getCurrentInstance } from 'vue'
  import {
    Row,
    Col,
    Modal,
    Select,
    message,
    InputNumber
  } from 'ant-design-vue'
  import { modelEnum } from '../../../constants'
  import {
    MODIFY_TOU_SU_DEPART,
    ADD_TOU_SU_DEPART
  } from '@operation/store/modules/feedback/action-types'
  import { NiDepartSelect } from '@jiuji/nine-ui'
  import Reason from '../../../../complaint/components/reason-select'
  export default defineComponent({
    components: { NiDepartSelect, Reason },
    props: {
      modalDepart: {
        type: Object,
        default: () => ({})
      },
      showDepartModal: {
        type: <PERSON>olean,
        default: false
      }
    },
    setup (props, { emit }) {
      const { proxy } = getCurrentInstance()
      const visible = computed({
        get: () => props.showDepartModal,
        set: val => emit('update:showDepartModal', val)
      })

      const afterClose = function () {
        emit('destroyedModal', 'departModal')
      }

      // 保存责任规划按钮
      const modalDepartOk = async function () {
        const {
          departCode,
          score,
          cat,
          touSuRank,
          ...other
        } = props.modalDepart.data
        const params = {
          tousuId: +proxy.$route.params.id,
          ...other
        }
        params.departId = departCode
        params.scoreDep = score
        if (!departCode) return message.info('选择投诉部门')
        // --- 默认为无 ---
        params.cat = cat || null
        params.touSuRank = touSuRank || null
        const res = await proxy.$store.dispatch(
          `operation/feedback/${
            params.id ? MODIFY_TOU_SU_DEPART : ADD_TOU_SU_DEPART
          }`,
          params
        )
        if (res) {
          visible.value = false
          message.success(res.userMsg)
          emit('getResultDetail')
          emit('getProcess')
        }
      }

      return {
        visible,
        afterClose,
        modalDepartOk
      }
    },
    render () {
      const { modalDepart, visible, modalDepartOk, afterClose } = this
      const { title } = modalDepart
      const { cat, touSuRank } = modelEnum
      return (
      <Modal
        title={title}
        visible={visible}
        mask-closable={false}
        width="900px"
        onOk={modalDepartOk}
        onCancel={() => {
          this.visible = false
        }}
        after-close={afterClose}
      >
        <div class="model">
          <Row gutter={[24, 16]}>
            <Col span={12}>
              <span class="label">归属部门：</span>
              <NiDepartSelect
              class="grow1"
              value={modalDepart.data.departCode}
              onChange={(val) => {
                modalDepart.data.departCode = val
              }}
            />
            </Col>
            <Col span={12}>
              <span class="label">扣分：</span>
              <InputNumber
                v-model={modalDepart.data.score}
                class="grow1"
                allow-clear
                min={0}
                placeholder="输入分值"
              />
            </Col>
          </Row>
          <Row gutter={[24, 16]}>
            <Col span={12}>
              <span class="label">定性分类：</span>
              <Select
                v-model={modalDepart.data.cat}
                class="grow1"
                allow-clear
                placeholder="请选择投诉定性"
                options={cat}
              />
            </Col>
            <Col span={12}>
              <span class="label">投诉等级：</span>
              <Select
                v-model={modalDepart.data.touSuRank}
                class="grow1"
                allow-clear
                placeholder="请选择投诉等级"
                options={touSuRank}
              />
            </Col>
          </Row>
          <Row gutter={[24, 16]}>
            <Col span={12}>
              <span class="label">投诉原因：</span>
              <Reason
                onChange={list => {
                  modalDepart.data.categoryIds = list
                }}
                v-model={modalDepart.data.categoryIds}
                kind={3}
              />
            </Col>
          </Row>
        </div>
      </Modal>
      )
    }
  })
</script>

<style lang="scss" scoped>
.label {
  white-space: nowrap;
  display: block;
  width: 82px;
  text-align: right
}

.grow1 {
  flex-grow: 1;
}

.ant-col {
  &-24 {
    align-items: unset;
    > span {
      white-space: nowrap;
    }

    > div {
      flex-grow: 1;
    }
  }

  display: flex;
  align-items: center;
  margin-bottom: 0.5em;

  > span {
    white-space: nowrap;
  }

  .ant-col {
    > span {
      white-space: nowrap;
      display: block;
      width: 5em;
    }
  }
}
</style>
