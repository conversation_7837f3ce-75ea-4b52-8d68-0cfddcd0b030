<script lang="jsx">
  import { defineComponent, ref, watch, getCurrentInstance } from 'vue'
  import { Radio, Row, Col, Checkbox, Button, Input, DatePicker, TimePicker, message } from 'ant-design-vue'
  import FileUploader from '~/components/upload/FileUploader'
  import Uploader from '~/components/upload/uploader'
  import InputPeople from '~/components/staff/staff-input'
  import moment from 'moment'
  import {
    TOU_SU_PROCESS_DEAL
  } from '@operation/store/modules/feedback/action-types'

  export default defineComponent({
    props: {
      detailData: {
        type: Object,
        default: () => {
          return {}
        }
      }
    },
    components: {
      FileUploader, Uploader, InputPeople
    },
    setup (props, { emit }) {
      const { proxy } = getCurrentInstance()
      const time = ref(null)

      const form = ref({
        notice: false, // 是否通知
        cate: 2, // 类型
        isShow: false, // 展示
        dsc: '', // 备注
        endTime: null, // 截至跟进
        tongzhi: false, // 内部
        duanxin: true, // 短信
        attachFiles: [], // 文件
        noticeUsers: [],
        weixin: false
      })

      const formRes = ref(JSON.parse(JSON.stringify(form.value)))

      const id = proxy.$route.params.id

      watch(() => props.detailData.states, val => {
        if (val === 3) {
          form.value.cate = 3
        }
      }, {
        immediate: true,
        deep: true
      })

      // 限制事件
      const disabledDate = function (current) {
        return current && current < moment().subtract(1, 'days')
      }

      const upLoaderChange = function (files) {
        form.value.attachFiles = files
      }

      const tousuProcessDeal = async function (states, buttonName) {
        let params = { ...form.value, oldStates: props.detailData.states, states, tsId: id, opUser: proxy.$store.state.userInfo.UserName, buttonName }
        if (!params.dsc) return message.info('输入备注')
        if (params.notice) {
          if (!params.noticeUsers.length) return message.info('输入接收人')
          if (!params.tongzhi && !params.duanxin) return message.info('选择通知方式')
          params.endTime = params.endTime && time.value ? `${params.endTime} ${time.value}` : null
        } else {
          params.endTime = null
        }
        const files = []
        params.attachFiles.map(item => {
          const { fid, fileName } = item
          files.push({ fid, filename: fileName })
        })
        delete params.attachFiles
        params.files = files.length ? files : ''
        const res = await proxy.$store.dispatch(
          `operation/feedback/${TOU_SU_PROCESS_DEAL}`,
          params
        )
        if (res) {
          message.success('处理成功')
          form.value = { ...formRes.value }
          // states 进行了受理2、还原7、添加进程null等操作时自动刷新页面
          const timeId = setTimeout(() => {
            proxy.$router.go(0)
            clearTimeout(timeId)
          }, 1000)
        }
      }

      const peopleChange = function (people) {
        form.value.noticeUsers = people.map(d => {
          return {
            noticeUser: d.name,
            ch999Id: d.id
          }
        })
      }

      return {
        time,
        form,
        disabledDate,
        upLoaderChange,
        tousuProcessDeal,
        peopleChange
      }
    },
    render () {
      const { form, detailData, peopleChange, disabledDate, upLoaderChange, tousuProcessDeal } = this
      console.log([5, 6].includes(detailData.states))

      const reenderButton = function (states, buttonName) {
        return <Button type="primary" class="btn mr-5" size='small' onClick={() => { tousuProcessDeal(states, buttonName) }} >{buttonName}</Button>
      }
      return <div>
    <span>添加处理进程：</span>
    <div>
      <div>
        <Radio.Group v-model={form.cate} button-style="solid" size="small">
        {
          detailData.states !== 3 ? <Radio.Button value={0}>客诉处理进程</Radio.Button> : null
        }
        {
          detailData.states !== 3 ? <Radio.Button value={2}>落实反思进程</Radio.Button> : null
        }
          <Radio.Button value={3}>优化点</Radio.Button>
        </Radio.Group>
        <Checkbox style="margin-left:5px" v-model={form.isShow} >合作伙伴可见</Checkbox>
      </div>
      <Row>
        <Col span={24} style="margin:.5em 0 2em 0;">
          <Input.TextArea v-model={form.dsc} placeholder="输入备注" rows={3} />
        </Col>
      </Row>
      <Row>
        <Col span={24} class="flex flex-col-center">
          <span><Checkbox v-model={form.notice} class="red">通知</Checkbox></span>
          {
            form.notice ? <div style="flex: 1;display: flex;">
              <div class="flex flex-col-center">
              <span>接收人：</span>
              <InputPeople multiple={true} data-is-obj-arr={true} style="height:30px; width:180px;" onChange={people => { peopleChange(people) }} placeholder="输入工号或姓名查询" class="grow1" allow-clear />
            </div>
            <div class="flex flex-col-center" style="margin-left:1em;">
              <span>跟进截止：</span>
              <DatePicker format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" disabledDate={disabledDate} v-model={form.endTime} style="height:30px;"/>
              <TimePicker format="HH:mm" valueFormat="HH:mm" v-model={this.time} />
            </div>
            <div class="flex flex-col-center" style="margin-left:1em;">
              <span>通知方式：</span>
              <Radio.Group>
                <Checkbox v-model={form.tongzhi}>站内信</Checkbox>
                <Checkbox v-model={form.duanxin}>短信</Checkbox>
              </Radio.Group>
            </div>
            </div> : null
          }
        </Col>
      </Row>
      <Row>
        <Col>
          <Row>
            <Col>
              <span>附件：</span>
              <Uploader onChange={upLoaderChange} accept=".png,.jpg,.png,"/>
            </Col>
          </Row>
        </Col>
        <Col>
        {
          detailData.states !== 3 ? <span>
          {
            [1].includes(detailData.states) ? reenderButton(2, '受理') : null
          }
          {
            [2].includes(detailData.states) ? reenderButton(7, '事件还原') : null
          }
          {
            [7].includes(detailData.states) ? reenderButton(5, '已处理') : null
          }
          {
            [5].includes(detailData.states) ? reenderButton(8, '界定') : null
          }
          {
            [5, 6].includes(detailData.states) ? reenderButton(3, '完成') : null
          }
          {
            [8].includes(detailData.states) ? reenderButton(9, '申请复核') : null
          }
          {
            [9, 10].includes(detailData.states) ? reenderButton(4, '复核通过') : null
          }
          {
            [9].includes(detailData.states) ? reenderButton(10, '运营仲裁') : null
          }
          {
            [10].includes(detailData.states) ? reenderButton(10, 'COO仲裁') : null
          }
          {
            [10].includes(detailData.states) ? reenderButton(4, '仲裁处理') : null
          }
          {
            [4].includes(detailData.states) ? reenderButton(6, '整改完毕') : null
          }
          </span> : null
        }
        {
          form.cate === 3 || (detailData.states !== 3 && [2, 7, 5, 8, 9, 10, 4, 6].includes(detailData.states)) ? reenderButton(null, '添加进程') : null
        }
        </Col>
        <Col>
        </Col>
      </Row>
    </div>
  </div>
    }
  })
</script>

<style lang="scss" scoped>
.mr-5{
  margin-right: 5px;
}

.ant-col{
  margin: .5em 0;
  min-height: 34px;
}
.flex {
  display: flex;

  > span {
    white-space: nowrap;
  }
}

.flex-col-center {
  align-items: center;
}

.flex-row-center {
  justify-content: center;
}
.btn{
  background:#F5A623;
  border: #F5A623;
}
.red{
  color: #F56C6C;
}
</style>
