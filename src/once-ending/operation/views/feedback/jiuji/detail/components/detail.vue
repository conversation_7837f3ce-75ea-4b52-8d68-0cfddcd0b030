<script lang="jsx">
  import { defineComponent, getCurrentInstance } from 'vue'
  import { Row, Col, Input, Button, message } from 'ant-design-vue'
  import Attachments from '../../../components/attachments'
  import InputPeople from '~/components/staff/staff-input'
  import moment from 'moment'
  import { statesEnum, tagOptions } from '../../../constants'
  import {
    SET_ARCHIVE_CATEGORY
  } from '@operation/store/modules/feedback/action-types'

  export default defineComponent({
    props: {
      detailData: {
        type: Object,
        default: () => {
          return {}
        }
      },
    },
    components: {
      InputPeople,
      Attachments
    },
    setup (props, { emit }) {
      const { proxy } = getCurrentInstance()
      // 保存
      const setArchiveCategory = async function () {
        const { id, processUser } = props.detailData
        const params = { id, processUser }
        const res = await proxy.$store.dispatch(
          `operation/feedback/${SET_ARCHIVE_CATEGORY}`, params
        )
        if (res) {
          message.success('保存成功')
          emit('getDetail')
        }
      }

      return {
        setArchiveCategory
      }
    },
    render () {
      const { detailData, setArchiveCategory } = this
      return <div>
    <Row>
      <Col span={4} style="height: 32px;">
        <span class="label">投诉ID：</span>
        <span>{ detailData.id }</span>
      </Col>
      <Col span={4} style="height: 32px;">
        <span class="label">录入人：</span>
        <span style="color:#E6A23C;">
          { detailData.inuser ? detailData.inuser : '系统' }
        </span>
      </Col>
      <Col span={4} style="height: 32px;">
        <span class="label">投诉性质：</span>
        <span>{ tagOptions.find(it => it.value === detailData.tag)?.label || '--' }</span>
      </Col>
      <Col span={16}>
        <span class="label">跟进人：</span>
        {
          detailData.orgProcessUser ? <Input disabled v-model={detailData.processUser} style="width: 18em; margin-right: 1em"></Input> : <InputPeople
          style="width: 18em; margin-right: 1em;"
          onChange={people => { detailData.processUser = people.value }}
          placeholder="输入工号或姓名查询"
          class="grow1"
          allow-clear />
        }
        <Button type="primary" style="margin-left:.5em;height:22px;" onClick={setArchiveCategory}>保存</Button>
      </Col>
    </Row>
    <Row>
      <Col span={24}>
        <span class="label">投诉内容：</span>
        <div class="full-width border contents">
          { detailData.content }
        </div>
      </Col>
    </Row>
    <Row>
      <Col span={24}>
        <span class="label">附件：</span>
        <Attachments list={detailData.attachments} />
      </Col>
    </Row>
    <Row>
      <Col span={6}>
        <span class="label">投诉时间：</span>
        <div class={detailData.dealTimeout ? 'red' : 'big'}>{ detailData.addTime ? moment(detailData.addTime).format('YYYY-MM-DD HH:mm') : '-'}</div>
      </Col>
      <Col span={6}>
        <span class="label">还原时间：</span>
        <div>{ detailData.huanyuantime ? moment(detailData.huanyuantime).format('YYYY-MM-DD HH:mm') : '-'}</div>
      </Col>
      <Col span={6}>
        <span class="label max-1d5">还原超时时间：</span>
        <div>{ detailData.huanyuantimeout ? moment(detailData.huanyuantimeout).format('YYYY-MM-DD HH:mm') : '-' }</div>
      </Col>
    </Row>
    <Row>
      <Col span={6}>
        <span class="label">处理状态：</span>
        <div style="color:#f56c6c;">{ statesEnum[detailData.states] }</div>
      </Col>
    </Row>
  </div>
    }
  })
</script>

<style lang="scss" scoped>
  .label{
    width: 4.5em;
    text-align: right;
    margin-right: 5px;

    &.max-1d5{
      width: 6.7em;
    }
  }
.ant-row {
  margin-bottom: 1em;
}

.ant-col {
  &-24 {
    align-items: unset;
  }

  display: flex;
  align-items: center;

  > span {
    white-space: nowrap;
  }
}
.big{
  font-weight: 600;
}
.contents {
  min-height: 100px;
  color:#000;
  font-weight:600;
  border-radius: 8px;
  padding: 8px 8px;
  background: #dfdfdf;
}
.tag-em{
  display: inline-block;
  margin-left:1.5em;
  color: #49BDEF;
  padding:.2em 1em;
  border-radius:4px;
  border:1px solid #49BDEF;

  span{
    color: red;
    font-size: 1.1em;
  }
}
</style>
