<script lang="jsx">
  import { defineComponent, ref, nextTick, getCurrentInstance } from 'vue'
  import { Row, Col, Button, Input, message } from 'ant-design-vue'
  import { modelEnum } from '../../../constants'
  import DepartModal from './depart-modal'
  import DutyModal from './duty-modal'
  import {
    DELETE_ZE_REN_REN,
    DELETE_TOU_SU_DEPART
  } from '@operation/store/modules/feedback/action-types'

  export default defineComponent({
    props: {
      resultDetail: {
        type: Object,
        default: () => {
          return {
            touSuDeparts: [],
            zeRenRenList: []
          }
        }
      },
    },
    setup (props, { emit }) {
      const { proxy } = getCurrentInstance()
      const id = ref(proxy.$route.params.id)

      const modalDepart = ref({
        title: '添加',
        visible: false,
        data: {
          id: '',
          cat: 1,
          touSuRank: 1,
        } // 用set处理对象
      })

      const modalDuty = ref({
        visible: false,
        title: '添加投诉责任人',
        data: {
          tousuPoint: 0,
          tousuRank: 1,
          userName: '',
          userId: '',
          id: ''
        }
      })

      const destroyed = ref({
        departModal: true,
        dutyModal: true
      })

      // 添加乐捐
      const addL = function () {
        proxy.$router.push(`/punish/Punish?title=投诉乐捐【投诉ID：${id.value}】&tousuid=${id.value}&type_=1`)
      }

      // 修改/添加责任人
      const editPersonLiable = function (item) {
        modalDuty.value.visible = true
        if (item) {
          modalDuty.value.data = Object.assign({}, item)
        } else {
          modalDuty.value.data = { score: 0, tousuRank: 1, userName: '', userId: '' }
        }
      }

      // 删除责任人
      const deletePersonLiable = async function (item) {
        proxy.$confirm({
          title: '确认删除吗？',
          onOk: () => {
            deleteZeRenRen(item)
          }
        })
      }

      const deleteZeRenRen = async function (item) {
        const params = {
          ...item,
          tousuId: +id.value
        }
        const res = await proxy.$store.dispatch(
          `operation/feedback/${DELETE_ZE_REN_REN}`,
          params
        )
        if (res) {
          message.success(res.userMsg)
          emit('getResultDetail')
        }
      }

      // 点击添加或修改责任划分
      const touSuDepartsAdd = function (title, item) {
        modalDepart.value.visible = true
        modalDepart.value.title = title
        let data = { cat: 1, touSuRank: 1, id: '', departCode: undefined, score: 0, categoryIds: undefined }
        if (title === '修改责任划分') {
          const { cat, touSuRank, id, departId, scoreDep, touSuTypes } = item
          data = {
            cat,
            touSuRank,
            id,
            departCode: departId,
            score: scoreDep || 0,
            categoryIds: touSuTypes
          }
        }
        modalDepart.value.data = data
      }

      // 删除责任规划
      const delTouSuDepart = function (item) {
        proxy.$confirm({
          title: '确认删除吗？',
          onOk: () => {
            deleteTouSuDepart(item)
          }
        })
      }

      const deleteTouSuDepart = async function (item) {
        const params = {
          ...item,
          tousuId: +id.value
        }
        const res = await proxy.$store.dispatch(
          `operation/complaint/${DELETE_TOU_SU_DEPART}`,
          params
        )
        if (res) {
          message.success(res.userMsg)
          emit('getResultDetail')
        }
      }

      // 查找label
      const fineEnumLabel = function (value, name) {
        return modelEnum[name].find(item => item.value === value)?.label || '无'
      }

      const destroyedModal = function (key) {
        destroyed.value[key] = false
        nextTick(() => {
          destroyed.value[key] = true
        })
      }

      return {
        id,
        modalDepart,
        modalDuty,
        addL,
        editPersonLiable,
        deletePersonLiable,
        touSuDepartsAdd,
        fineEnumLabel,
        destroyedModal,
        destroyed,
        delTouSuDepart
      }
    },
    render () {
      const { id, modalDepart, modalDuty, destroyedModal, destroyed, addL, touSuDepartsAdd, fineEnumLabel, delTouSuDepart, editPersonLiable, deletePersonLiable, resultDetail } = this
      return <div>
    <Row>
      <Col span={5}>
        <span>责任划分：</span>
        <Button type="primary" shape="circle" icon="plus" size="small" onClick={() => { touSuDepartsAdd('添加责任划分') }}/>
      </Col>
    </Row>
    <Row>
      { resultDetail.touSuDeparts.map(item => <Col span={18} key={item.id}>
        <div class="divide">
          <div class="department">
            <div class="full-width">
              <Row>
                <Col span={12}><span>归属部门：{item.department}</span></Col>
                <Col span={12}><span>扣分：{item.scoreDep}</span></Col>
              </Row>
              <Row>
                <Col span={12}><span>定性分类：{fineEnumLabel(item.cat, 'cat')}</span></Col>
                <Col span={12}><span>投诉等级：{fineEnumLabel(item.touSuRank, 'touSuRank')}</span></Col>
              </Row>
              <Row>
                <Col span={24}>
                  <span>投诉原因：</span>
                  <div class="flex flex-wrap">
                        {item.touSuType.map((type, index) => (
                          <div key={index}>
                            <span>【{type.name}】</span>
                            {type.value}
                          </div>
                        ))}
                      </div>
                </Col>
              </Row>
            </div>
            <div class="flex flex-justify-end ml-16">
              <Button type="primary" size="small" onClick={() => { touSuDepartsAdd('修改责任划分', item) }}>修改</Button>
              <Button type="danger" size="small" style="margin-left:1em " onClick={() => { delTouSuDepart(item) }}>删除</Button>
            </div>
          </div>
        </div>
      </Col>)
      }

    </Row>
    <Row>
      <Col span={5}>
        <span>投诉责任人：</span>
        <Button
          type="primary" shape="circle" icon="plus" size="small"
          onClick={() => { editPersonLiable('') }}/>
      </Col>
    </Row>
    <Row>
    {
      resultDetail.zeRenRenList.map((item, index) => <Col span={18} key={index}>
        <div class="divide">
          <div class="store" style="padding: 1em">
            <Row>
              <Col span={8}>
                <span>责 任 人：</span>
                { item.userName }
              </Col>
              <Col span={8}>
                <span>扣分：</span>
                { item.tousuPoint }
              </Col>
              <Col span={8}>
                <span>严重程度：</span>
                {item.tousuRank && modelEnum.touSuRank.find(ts => ts.value === item.tousuRank).label}
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <span style="width:10em">90天内被投诉次数：</span>
                { item.complaintCount || 0 }
              </Col>
            </Row>
            <Row style="display: flex;justify-content: flex-end;">
              <Button type="primary" size="small" onClick={() => { editPersonLiable(item) }} >修改</Button>
              <Button type="danger" size="small" style="margin-left:1em " onClick={() => { deletePersonLiable(item) }}>删除</Button>
            </Row>
          </div>
        </div>
      </Col>)
    }
    </Row>
    <Row>
      <Col span={24}>
        <span>奖惩金额：</span>
        <div style="flex-grow: 1;">
          <Row>
            <Col span={12}>
              <span>惩罚</span>
              <Input disabled v-model={resultDetail.fineMoney} />
            </Col>
            <Col span={8}>
              <a href={`/punish/Punish?title=投诉乐捐【投诉ID：${id}】&tousuid=${id}&type_=1`}><Button type="primary" onClick={addL}>添加乐捐单</Button></a>
            </Col>
          </Row>
        </div>
      </Col>
    </Row>
    <Button type="primary" size='large' onClick={() => { this.$emit('getResultDetail') }} style="margin:2em 0 2em 5em;">更新</Button>
    {destroyed.departModal && (
          <DepartModal
            modal-depart={modalDepart}
            show-depart-modal={modalDepart.visible}
            {...{
              on: {
                'update:showDepartModal': val => {
                  modalDepart.visible = val
                }
              }
            }}
            onGetResultDetail={() => { this.$emit('getResultDetail') }}
            onGetProcess={() => { this.$emit('getProcess') }}
            onDestroyedModal={destroyedModal}
          ></DepartModal>
        )}
        {destroyed.dutyModal && (
          <DutyModal
            modal-duty={modalDuty}
            show-duty-modal={modalDuty.visible}
            {...{
              on: {
                'update:showDutyModal': val => {
                  modalDuty.visible = val
                }
              }
            }}
            onGetResultDetail={() => { this.$emit('getResultDetail') }}
            onGetProcess={() => { this.$emit('getProcess') }}
            onDestroyedModal={destroyedModal}
          ></DutyModal>
        )}
  </div>
    }
  })
</script>

<style lang="scss" scoped>
.label {
  white-space: nowrap;
  display: block;
  width: 82px;
}

.grow1 {
  flex-grow: 1;
}

.ant-col {
  &-24 {
    align-items: unset;
    > span {
      white-space: nowrap;
    }

    > div {
      flex-grow: 1;
    }
  }

  display: flex;
  align-items: center;
  margin-bottom: .5em;

  > span {
    white-space: nowrap;
  }

  .ant-col {
    > span {
      white-space: nowrap;
      display: block;
      width: 5em;
    }
  }

  .divide {
    margin-left: 4.5em;
    flex-grow: 1;

    .ant-col {
      > span {
        white-space: nowrap;
        display: block;
        width: 5em;
      }
    }

    .store {
      padding: 2em;
      background: #eaf5ff;
      border-radius: .5em;
    }

    .department {
      padding: 2em;
      border-radius: .5em;
      background: #fbe8ea;
    }
  }
}
</style>
