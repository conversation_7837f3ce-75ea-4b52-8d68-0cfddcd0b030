<script lang="jsx">
  import { defineComponent, ref, getCurrentInstance } from 'vue'
  import { Row, Col, Popover, Input, Button, Icon } from 'ant-design-vue'
  import moment from 'moment'
  import { tagOptions, tagOptMap } from '../../../constants'
  import {
    TOU_SU_END_AND_INVALID
  } from '@operation/store/modules/feedback/action-types'

  export default defineComponent({
    props: {
      detailData: {
        type: Object,
        default: () => {
          return {}
        }
      },
      memberData: {
        type: Object,
        default: () => {
          return {}
        }
      }
    },
    setup (props, { emit }) {
      const { proxy } = getCurrentInstance()
      const id = proxy.$route.params.id

      const joinTousuId = ref('')

      const callcenter = function () {
        const getTime = moment().format('YYYY-MM-DD HH:MM:SS')
        window.location.href = `/callcenter/ch999/listCDR.php?time1=${props.detailData.addTime}&time2=${getTime}&dst=${props.memberData && props.memberData.orgMobile}`
      }

      const getTag = function () {
        const tagItems = tagOptions.filter(d => d.label === props.memberData.tag)
        return tagItems.length ? tagItems[0].value : ''
      }

      const upData = function () {
        const params = { id }
        if (props.memberData.mobile !== props.memberData.orgMobile) {
          params.mobile = props.memberData.mobile
        }
        if (props.memberData.tag) params.tag = getTag()
        emit('tousuMember', params)
      }

      const tousuEndAndinvalid = async function () {
        if (!joinTousuId.value) return message.info('请输入相同的投诉单号')
        const params = {
          id,
          joinTousuId: joinTousuId.value,
          isSupplier: false
        }
        const res = await proxy.$store.dispatch(
          `operation/feedback/${TOU_SU_END_AND_INVALID}`,
          params
        )
        if (res) {
          proxy.$router.go(0)
        }
      }

      const toList = function () {
        window.open(`${proxy.$tnt.oaHost}/staticpc/#/operation/feedback/jiujiList?searchKind=3&key=${props.memberData.memberName}`)
      }

      const tagClick = function (type, tagType) {
        props.memberData.tag = type
        props.memberData.tagType = tagType
      }

      return {
        joinTousuId,
        callcenter,
        upData,
        tousuEndAndinvalid,
        toList,
        tagClick
      }
    },
    render () {
      const { toList, memberData, tousuEndAndinvalid, callcenter, upData, tagClick } = this
      const tagColor = memberData.tagType ? tagOptMap.get(memberData.tagType)[1] : tagOptMap.get(0)[1]
      const tagColor1 = tagOptMap.get(1)[1]
      const tagColor2 = tagOptMap.get(2)[1]
      const tagColor3 = tagOptMap.get(3)[1]
      return <div>
    <Row>
      <Col span={8}>
        <span>投诉人姓名：{memberData.memberName}</span>
        <div>
          <span style={{ color: tagColor }}>【{memberData.tag}】</span>
          {
            memberData.tagType !== 4 &&
              <Popover title="修改性质">
                <template slot="content">
                    <span style={{ color: tagColor1, cursor: 'pointer' }} onClick={() => { tagClick('投诉', 1) }}>【投诉】</span>
                    <span style={{ color: tagColor2, cursor: 'pointer' }} onClick={() => { tagClick('建议', 2) }}>【建议】</span>
                    <span style={{ color: tagColor3, cursor: 'pointer' }} onClick={() => { tagClick('表扬', 3) }}>【表扬】</span>
                </template>
                <Icon type="edit" />
              </Popover>
          }
        </div>
      </Col>
      <Col span={8}>
        <span>所属合作伙伴：</span>
        <span style="color:#E6A23C;">{memberData.xtenantName}</span>
      </Col>
      <Col span={8}>
        <span>联系电话：</span>
        <Input v-model={memberData.mobile} class="width-200"/>
      </Col>
    </Row>
    <Row>
      <Col span={8}>
        <span>历史投诉次数：</span>
        <div>
          <span class="pointer" onClick={toList} style="color:#f56c6c;">【{memberData.tousuCnt}】</span>
        </div>
      </Col>
      <Col span={8}>
        <span>相同投诉：</span>
        <Input allowClear v-model={this.joinTousuId} placeholder="请填写投诉单号" class="width-200"/>
        <Button type="danger" ghost style="margin-left:.5em;height:30px;" onClick={tousuEndAndinvalid}>完结</Button>
      </Col>
      <Col span={8}></Col>
    </Row>
    <Row>
      <Col span={8}>
        <Button type="primary" ghost style="height:30px;" onClick={callcenter}>查看通话录音</Button>
        <Button type="primary" style="margin-left:.5em;height:30px;" onClick={upData}>更新</Button>
      </Col>
    </Row>
  </div>
    }
  })
</script>

<style lang="scss" scoped>
.ant-row{
  margin-bottom: 1em;
}
.ant-col{
  display: flex;
  align-items: center;
  >span{
    white-space: nowrap;
  }
}
.width-200{
  width: 200px;
}
</style>
