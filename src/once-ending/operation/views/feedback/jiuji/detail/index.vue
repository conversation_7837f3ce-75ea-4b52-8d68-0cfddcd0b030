<script lang="jsx">
  import { defineComponent, ref, getCurrentInstance } from 'vue'
  import Member from './components/member.vue'
  import Detail from './components/detail.vue'
  import Result from './components/result.vue'
  import Handle from './components/handle.vue'
  import Process from './components/process'
  import Imglct from './components/img'
  import { Card } from 'ant-design-vue'
  import {
    TOUSU_MEMBER,
    GET_TOU_SU_INFO,
    SHOW_PROCESS_INFO,
    GET_TOU_SU_END_INFO
  } from '@operation/store/modules/feedback/action-types'
  import Browse from '~/components/browse'
  export default defineComponent({
    components: {
      Member, Detail, Result, Handle, Process, Imglct
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const detailData = ref({})

      const memberData = ref({})

      const processLog = ref([])

      const resultDetail = ref({
        touSuDeparts: [],
        zeRenRenList: []
      })

      const id = proxy.$route.params.id

      // 查询投诉人信息
      const tousuMember = async function (params = { id }) {
        const res = await proxy.$store.dispatch(
          `operation/feedback/${TOUSU_MEMBER}`,
          params
        )
        if (res) {
          const { data, data: { mobile: orgMobile } } = res
          memberData.value = { ...data, orgMobile }
        }
      }
      tousuMember()

      // 查询详情
      const getDetail = async function () {
        const params = { id }
        const res = await proxy.$store.dispatch(
          `operation/feedback/${GET_TOU_SU_INFO}`,
          params
        )
        if (res) {
          const { data, data: { processUser: orgProcessUser } } = res
          detailData.value = {
            ...data,
            orgProcessUser
          }
        }
      }
      getDetail()

      // 查询投诉处理进程
      const getProcess = async function () {
        const params = { id }
        const res = await proxy.$store.dispatch(
          `operation/feedback/${SHOW_PROCESS_INFO}`,
          params
        )
        if (res) {
          const { data } = res
          data.forEach((item, index) => {
            item.index = index
          })
          processLog.value = data
        }
      }
      getProcess()

      // 获取投诉结果详情
      const getResultDetail = async function () {
        const params = { id }
        const res = await proxy.$store.dispatch(
          `operation/feedback/${GET_TOU_SU_END_INFO}`,
          params
        )
        if (res) {
          const { data } = res
          resultDetail.value = data
        }
      }
      getResultDetail()

      const handleRefresh = function () {
        getDetail()
        getProcess()
      }

      return {
        detailData,
        memberData,
        processLog,
        resultDetail,
        handleRefresh,
        tousuMember,
        getDetail,
        getProcess,
        getResultDetail
      }
    },
    render () {
      const { detailData, memberData, tousuMember, getDetail, handleRefresh, processLog, getProcess, resultDetail, getResultDetail } = this
      return <page style="background-color: inherit;">
        <template slot="extra">
            <Browse pageType={ 1 } id={ this.$route.params.id }/>
          </template>
  <div class="flex">
    <div style="min-width: 1080px">
      <Card class="card">
        <div class="card-item">
          <span class="title">投诉人信息：</span>
          <Member detail-data={detailData} member-data={memberData} onTousuMember={tousuMember} />
        </div>
        <div class="card-item">
          <span class="title">投诉基础信息：</span>
          <Detail detail-data={detailData} onGetDetail={getDetail} />
        </div>
        <div class="card-item">
          <span class="title">投诉处理：</span>
          <Handle detail-data={detailData} onRefreshData={handleRefresh} />
        </div>
        <div class="card-item">
          <span class="title">投诉处理进程：</span>
          <Process ref="process" process-log={processLog} onGetProcess={getProcess} />
        </div>
        <div class="card-item">
          <span class="title">投诉结果：</span>
          <Result result-detail={resultDetail} onGetResultDetail={getResultDetail} onGetProcess={getProcess} />
        </div>
      </Card>
    </div>
    <Imglct />
  </div>
</page>
    }
  })
</script>

<style lang="scss" scoped>
.card{
  background: #fff;
  &-item{
    .title{
      font-size: 18px;
      line-height: 2.2;
      font-weight: 600;
      color:#909399;
    }
    >div{
      margin-bottom: 2em;
      border-radius: 4px;
      border: 1px solid #d7d7d7;
      padding: 1em;
    }
  }
}

</style>
