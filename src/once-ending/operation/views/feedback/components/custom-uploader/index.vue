<template>
  <div>
    <div class="img-wrap">
      <div v-for="(file, index) in list"
        :key="index"
        class="img-item">
          <!-- 图片 -->
          <template v-if="isImg(file)">
            <lazy-img
              class="img-style"
              width="120"
              height="120"
              :src="file.fileUrl || file.filePath"/>
            <div class="action-warp">
              <file-preview
                v-if="showPreview"
                :showName="edit"
                :file="file"
                :pathKey="pathKey"
                showFileName="false"
                :download="!download" />
              <a v-if="download" class="padding" href="#" @click="downloadFile(file)"><a-icon type="cloud-download" /></a>
              <a v-if="deleteItem" class="padding" @click="deleteFile(file,index)"><a-icon type="delete"/></a>
            </div>
          </template>
          <!-- 视频 -->
          <template v-else>
            <d-player
              class="img-style"
              :frameWidth="108"
              :frameHeight="108"
              :framePath="file.framePath"
              :options="options"
              :fid="file.fid"
            />
            <a v-if="deleteItem" class="video-delete" @click="deleteFile(file,index)"><a-icon type="delete"/></a>
          </template>
      </div>
      <a v-if="showUpload"
        class="videoImg-uploader"
        @click="doUpload"
        :disabled="disabled">
        <a-icon type="plus" />
        <span class="ant-upload-text">上传图片/视频</span>
      </a>
    </div>
  </div>
</template>

<script>
  import { Modal, Upload } from 'ant-design-vue'
  import { mapState } from 'vuex'
  import QrCode from '~/components/qrcode'
  import { saveAs } from 'file-saver'
  import nineUpload from '@jiuji/nine-upload'
  import filePreview from './file-preview'
  import LazyImg from '~/components/lazy-img'
  import DPlayer from './dPlayer'

  export default {
    name: 'Uploader',
    props: {
      // 是否显示重命名按钮
      showRename: {
        type: Boolean,
        default: true
      },
      // 是否禁止上传
      showUpload: {
        type: Boolean,
        default: true
      },
      disabled: {
        type: Boolean,
        default: false
      },
      showPreview: {
        type: Boolean,
        default: true
      },
      // 是否多选
      multiple: {
        type: Boolean,
        default: true
      },
      // 支持上传多个文件
      moreAmount: {
        type: Boolean,
        default: true
      },
      editFileName: {
        type: Boolean,
        default: true
      },
      addSize: {
        type: Boolean,
        default: false
      },
      // 支持上传多个文件
      moreAmountWarn: {
        type: String,
        default: '最多上传一个文件'
      },
      editName: {
        type: Boolean,
        default: true
      },
      // 文件是或否支持下载
      download: {
        type: Boolean,
        default: false
      },
      // 文件是否支持删除
      deleteItem: {
        type: Boolean,
        default: true
      },
      edit: Boolean,
      // path的key名
      pathKey: {
        type: String,
        default: 'fileUrl'
      },
      // 上传的列表
      fileList: {
        type: [Array],
        default: () => [],
        validator: (fileList) => {
          let isRight = true
          fileList?.map(item => {
            if (!item.hasOwnProperty('fid') || !item.hasOwnProperty('filename')) isRight = false
          })
          if (!isRight) console.error('fileList: 参数不正确')
          return isRight
        }
      },
      // 删除的id列表
      delList: {
        type: [Array],
        default: () => []
      },
      // 二维码的地址
      qrCodeUrl: {
        type: String,
        default: ''
      },
      routKey: {
        type: String,
        default: ''
      },
      // 文件上传的类型
      accept: {
        type: String,
        default: 'image/*, video/mp4, video/avi'
      },
      sendMessage: {
        type: Boolean,
        default: false
      },
    },
    components: {
      LazyImg,
      [Upload.name]: Upload,
      [Modal.name]: Modal,
      QrCode,
      filePreview,
      DPlayer
    },

    data () {
      return {
        list: [],
        qrcodeVisible: false,
        // qrcode: '',
        qrContent: '',
        // 上传需要的两个变量值
        appId: '',
        token: '',
        DPlayerFid: null,

        options: {
          container: document.getElementById('dplayer'), // 播放器容器
          mutex: false, //  防止同时播放多个用户，在该用户开始播放时暂停其他用户
          theme: '#b7daff', // 风格颜色，例如播放条，音量条的颜色
        },
        dPlayerOptionsReset: null
      }
    },
    computed: {
      ...mapState({
        userInfo: state => state.userInfo || {}
      }),
    },
    created () {
      this.dPlayerOptionsReset = { ...this.dPlayerOptions }
      this.list = this.fileList
      console.log(this.list)
      const fileMsg = JSON.parse(window.sessionStorage.getItem('fileMsg'))
      if (fileMsg?.appId && fileMsg?.token) {
        this.appId = fileMsg.appId
        this.token = fileMsg.token
      }
    },
    watch: {
      // 监听数组变化和文件修改
      fileList: {
        deep: true,
        handler (newVal) {
          // console.log('--newVal---', newVal)
          this.list = this.fileList
          this.$emit('fileChange', newVal)
        }
      },
    },
    methods: {
      isImg (file) {
        const reg = /\.(png|jpg|gif|jpeg|webp)$/
        const nameIsImg = reg.test(file.filename) || reg.test(file.fileName)
        return nameIsImg
      },
      // 上传
      async doUpload () {
        // 只能上传一个文件时
        if (!this.moreAmount && this.list.length) {
          this.$message.warning(this.moreAmountWarn)
          return
        }
        // 上传
        nineUpload({
          accept: this.accept,
          multiple: this.multiple,
          // appId,
          // token,
          onPickFiles: async files => { // files是文件对象，如果要对文件进行处理，处理完成之后要在返回对象里加上files对象
            if (window.nineUploadData) {
              return window.nineUploadData
            }
            try {
              // 获取appId和token。可以把这个appId和token缓存起来，没必要每次都请求
              const { code = 0, userMsg = '', data = { appId: '', token: '' } } = await this.$api.common.getUploadToken()
              if (code === 0) {
                window.nineUploadData = data
                setTimeout(() => { // appId和token30分钟过期，要清理一下
                  window.nineUploadData = null
                }, 30 * 60 * 1000)
                return data
              } else {
                this.$message.error(userMsg)
              }
            } catch (e) {
              this.$message.error(e)
            }
          },
          onProgress: ({ percent, fileIndex, fileCount }) => {
            this.percent = percent
            this.fileIndex = fileIndex
            this.fileCount = fileCount
          },
          form: {
            collection: 'javaweb'
          }
        }).then(({ res, err }) => {
          if (!this.multiple) {
            res = [res]
          }
          res = res.map(item => {
            let i = {
              fid: item.fid,
              filename: item.fileName || item.fid,
              filepath: item.fileUrl || item.playPath[2].url.replace('.m3u8', ''),
              framePath: item.framePath || item.fileUrl
            }
            if (this.addSize) {
              i.size = item.size
            }
            // 上传成功后的url根据你的pathKey给你返回 // [1]:480p视频 [2] 720
            i[this.pathKey] = item.fileUrl || item.playPath[2].url
            return i
          })
          if (res.length) {
            let filelist = [...this.list]
            filelist = [...filelist, ...res]
            this.$emit('update:fileList', filelist)
            this.$emit('change', filelist)
          }
          err?.map(i => {
            this.$message.info(`${i.name}上传失败,${i.err.message}`)
          })
        })
      },
      // 删除
      deleteFile (item, index) {
        // 单项数据流，防止数据混乱和多次触发change 事件
        let newFileList = Object.assign([], this.list)
        newFileList = newFileList.filter(it => it.fid !== item.fid)
        this.$emit('update:fileList', newFileList)
        // 有id表示在数据库中存储（需要回传id进行删除），没有表示本地上传，只上传到了文件服务器中，没有上传到你的模块数据库中
        if (item.id) {
          // 有id就让后端调文件服务器的删除接口
          let delList = Object.assign([], this.delList)
          delList.push(item.id)
          delList = [...new Set(delList)]
          this.$emit('update:delList', delList)
          this.$emit('delete', item, index)
        } else {
        // 接文件服务器的删除接口
        }
      },
      // 下载文件
      downloadFile (file) {
        saveAs(file[this.pathKey], file.fileName)
      },
    },
  }
</script>

<style lang="scss" scoped>
  $preview-item-width: 120px;
  .img-item{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    position: relative;
  }

  .file-name{
    line-height: 1.4;
  }

  .videoImg-uploader{
    width: $preview-item-width;
    height: $preview-item-width;
    border: 1px dashed #afafaf;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    margin: 0 10px 10px 0;
  }
  .video-delete{
    position: absolute;
    right: 25px;
    bottom: 10px;
  }
.noImg-warp{
  width: $preview-item-width;
  height: $preview-item-width;
  border-radius: 4px;
  background: rgba(200,200,200,0.3);
  margin-right: 12px;
  margin-bottom: 8px;
  position: relative;

  .delete-icon {
    position: absolute;
    bottom: 6px;
    right: 8px;
    padding: 0 10px;
  }
}
.qr-btn {
  margin-left: -240px;
}

.upload-btn {
  width: 350px;
}

.img-style {
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  padding: 6px;
  margin-right: 20px
}
.img-wrap{
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  min-height: 30px;

  .img-style{
    margin: 0 12px 8px 0;
  }
  margin-top: 16px;
}

.action-warp{
  position: absolute;
  width: $preview-item-width;
  bottom: 10px;
  left: 7px;
  display: flex;
  align-items: center;
  > div,
  > a{
    flex: 1;
  }
  a{
    text-align: center;
  }
}
.edit-file {
  display: flex;
  align-items: center;
}
.edit-file {
  .file-name {
    width: 235px;
  }
}
</style>
