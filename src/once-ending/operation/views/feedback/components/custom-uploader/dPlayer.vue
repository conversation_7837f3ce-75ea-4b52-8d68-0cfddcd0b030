<template>
  <div>
    <div class="video-item" @click="handleShowVideo">
      <img
        class="pointer"
        :style="{ width: frameWidth + 'px', height: frameHeight + 'px' }"
        :src="framePath || require('~/assets/images/9ji-no.png')"
        alt=""/>
      <a-icon class="icon-play" type="youtube" />
    </div>

    <a-modal
      v-model="showVideo"
      :title="title"
      :footer="false">
      <d-player
        v-if="fetchEnd"
        ref="dplayer"
        id="dplayers"
        :options="dPlayerOptions"
        class="videoSize"/>
    </a-modal>
  </div>
</template>

<script>
  import VueDPlayer from 'vue-dplayer'
  import 'vue-dplayer/dist/vue-dplayer.css'
  export default {
    components: { 'd-player': VueDPlayer },
    props: {
      fid: {
        type: String,
        default: ''
      },
      showFrame: { // 是否展示封面
        type: Boolean,
        default: true
      },
      framePath: { // 是否展示封面
        type: String,
        default: ''
      },
      frameWidth: { // 封面图片宽度
        type: Number,
        default: 95
      },
      frameHeight: { // 封面图片高度
        type: Number,
        default: 55
      },
      title: { // 视频
        type: String,
        default: '视频'
      },
      options: {
        type: Object,
        default: () => {}
      },
      defaultQuality: {
        type: Number,
        default: 2
      },
    },
    data () {
      return {
        fetchEnd: false,
        showVideo: false,
        dPlayerOptions: {
          container: document.getElementById(this.showFrame ? 'dplayers' : 'dplayer'), // 播放器容器
          lang: 'zh-cn', // 语言，'en', 'zh-cn', 'zh-tw'
          video: {
            url: '',
            quality: [],
            defaultQuality: this.defaultQuality
          },
        }
      }
    },
    methods: {
      handleShowVideo () {
        this.showVideo = true
        this.getVideo()
      },
      getVideo () {
        if (this.options) {
          if (this.options.video) {
            this.dPlayerOptions.video = Object.assign(this.options.video, this.dPlayerOptions.video)
          }
          this.dPlayerOptions = Object.assign(this.options, this.dPlayerOptions)
        }
        this.$indicator.open()
        let params = {
          fid: this.fid
        }
        this.$api.common.getVideoInfoByFidV2(params).then(res => {
          if (res.code === 0) {
            if (res.data && res.data.playPath && res.data.playPath.length) {
              this.dPlayerOptions.video.url = res.data.playPath[0].url
              this.dPlayerOptions.video.quality = res.data.playPath.map(p => ({ // 这里只是上传完的预览，下次打开页面肯定要请求接口来赋值
                name: p.resolution,
                url: p.url,
                type: 'hls'
              }))
              this.fetchEnd = true
            }
          } else {
            this.$message.error(res.userMsg)
          }
        }).finally(() => {
          this.$indicator.close()
        })
      },
    }
  }
</script>

<style scoped lang="scss">
  :deep(.dplayer-video){
    width: auto;
    max-width: 100%;
    margin: 0 auto;
  }
  .video-item{
    position: relative;
    .icon-play{
      font-size: 40px;
      position: absolute;
      left: 50%;
      top: 50%;
      margin-left: -20px;
      margin-top: -15px;
      color: #fff;
      opacity: 0.5;
      cursor: pointer;
    }
  }
  .pointer{
    object-fit: cover;
  }
  .videoSize {
    max-height: 680px;
  }
</style>
