<template>
  <div class="atta-list">
    <div class="img" v-for="(item, index) in list" :key="index">
      <a-popover v-if="isImg(item)" placement="top">
        <template slot="content">
          <div class="img-hover-preview">
            <ni-img
              :src="item.filepath"
              @tap="imgShow(item.filepath)"
              alt='附件'></ni-img>
          </div>
        </template>
        <ni-img
          width="120"
          height="120"
          :src="item.filepath"
          alt='附件'></ni-img>
      </a-popover>
      <d-player
        v-else
        :frameHeight="95"
        :framePath="item.framePath"
        :options="options"
        :fid="item.fid"
      />
    </div>
  </div>
</template>
<script>
  import dPlayer from './custom-uploader/dPlayer'
  import { NiImg } from '@jiuji/nine-ui'

  export default {
    components: {
      dPlayer,
      NiImg
    },
    props: {
      list: {
        type: Array,
        default: () => []
      },
    },
    watch: {
      // 监听数组变化和文件修改
      fileList: {
        deep: true,
        handler (newVal) {
          this.list = this.fileList
          this.$emit('fileChange', newVal)
        }
      }
    },
    data () {
      return {
        options: {
          container: document.getElementById('dplayer'), // 播放器容器
          mutex: false, //  防止同时播放多个用户，在该用户开始播放时暂停其他用户
          theme: '#b7daff', // 风格颜色，例如播放条，音量条的颜色
        }
      }
    },
    methods: {
      imgShow (url) {
        window.open(url)
      },
      isImg (file) {
        const reg = /\.(png|jpg|gif|jpeg|webp)$/
        return reg.test(file.filename)
      },
    }
  }
</script>
<style lang="scss" scoped>
  .atta-list{
    display: flex;
    flex-wrap: wrap;
  }
  .img-hover-preview{
    img{
      width: auto;
      height: 40vh;
    }
  }
  .img {
    padding: .3em;
    width: 7.5em;
    height: 7.5em;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    margin-right: .5em;
    margin-bottom: .5em;
    overflow: hidden;
    img{
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

.ant-row {
  margin-bottom: 1em;
}

.ant-col {
  &-24 {
    align-items: unset;
  }

  display: flex;
  align-items: center;

  > span {
    white-space: nowrap;
  }
}
.big{
  font-weight: 600;
}
.contents {
  min-height: 100px;
  color:#000;
  font-weight:600;
  border-radius: 8px;
  padding: 8px 8px;
  background: #dfdfdf;
}
.tag-em{
  display: inline-block;
  margin-left:1.5em;
  color: #49BDEF;
  padding:.2em 1em;
  border-radius:4px;
  border:1px solid #49BDEF;

  span{
    color: red;
    font-size: 1.1em;
  }
}
</style>
