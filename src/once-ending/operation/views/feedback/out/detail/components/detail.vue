<script lang="jsx">
  import { defineComponent } from 'vue'
  import { Row, Col } from 'ant-design-vue'
  import Attachments from '../../../components/attachments'
  import moment from 'moment'

  export default defineComponent({
    props: {
      detailData: {
        type: Object,
        default: () => {
          return {}
        }
      },
    },
    components: {
      Attachments
    },
    setup () {

    },
    render () {
      const { detailData } = this
      return <div>
    <Row>
      <Col span={4} style="height: 32px;">
        <span class="label">投诉ID：</span>
        <span>
          { detailData.id }
        </span>
      </Col>
    </Row>
    <Row>
      <Col span={24}>
        <span class="label">投诉内容：</span>
        <div class="full-width border contents">
          { detailData.content }
        </div>
      </Col>
    </Row>
    <Row>
      <Col span={24}>
        <span class="label">附件：</span>
        <Attachments list={detailData.attachments} />
      </Col>
    </Row>
    <Row>
      <Col span={6}>
        <span class="label">投诉时间：</span>
        <div class={detailData.dealTimeout ? 'red' : 'big'}>
          { detailData.addTime ? moment(detailData.addTime).format('YYYY-MM-DD HH:mm') : '-'}
        </div>
      </Col>
    </Row>
    <Row>
      <Col span={6}>
        <span class="label">处理状态：</span>
        <div style="color:#f56c6c;">{ detailData.status }</div>
      </Col>
    </Row>
  </div>
    }
  })
</script>

<style lang="scss" scoped>
  .label{
    width: 4.5em;
    text-align: right;
    margin-right: 5px;

    &.max-1d5{
      width: 6.7em;
    }
  }
.ant-row {
  margin-bottom: 1em;
}

.ant-col {
  &-24 {
    align-items: unset;
  }

  display: flex;
  align-items: center;

  > span {
    white-space: nowrap;
  }
}
.big{
  font-weight: 600;
}
.contents {
  min-height: 100px;
  color:#000;
  font-weight:600;
  border-radius: 8px;
  padding: 8px 8px;
  background: #dfdfdf;
}
.tag-em{
  display: inline-block;
  margin-left:1.5em;
  color: #49BDEF;
  padding:.2em 1em;
  border-radius:4px;
  border:1px solid #49BDEF;

  span{
    color: red;
    font-size: 1.1em;
  }
}
</style>
