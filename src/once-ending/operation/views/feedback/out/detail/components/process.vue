<script lang="jsx">
  import { defineComponent, ref, computed, getCurrentInstance } from 'vue'
  import { Input, Button, message } from 'ant-design-vue'
  import {
    ADD_PROCESS
  } from '@operation/store/modules/feedback/action-types'
  import Uploader from '~/components/upload/uploader'

  export default defineComponent({
    components: {
      Uploader
    },
    props: {
      detailData: {
        type: Object,
        default: () => {
          return {
            processLog: []
          }
        }
      }
    },
    setup (props, { emit }) {
      const { proxy } = getCurrentInstance()
      const loading = ref(false)

      const form = ref({
        cate: 1, // 1为客户追问
        content: '',
        attachFiles: []
      })

      const formReset = ref(JSON.parse(JSON.stringify(form.value)))

      const list = computed(() => {
        if (!props.detailData.processLog) return []
        return props.detailData.processLog.filter(d => d.show)
      })

      const addProcess = async function () {
        const { cate, content, attachFiles } = form.value
        const params = {
          cate,
          content,
          complainId: proxy.$route.params.id,
          userName: proxy.$store.state.userInfo.UserName
        }
        if (!params.content) return message.info('请输入进行处理文字')
        if (attachFiles.length) {
          params.files = attachFiles.map(item => ({
            fid: item.fid,
            filename: item.fileName
          }))
        }
        loading.value = true
        const res = await proxy.$store.dispatch(
          `operation/feedback/${ADD_PROCESS}`,
          params
        )
        loading.value = false
        if (res) {
          message.success('处理成功')
          form.value = { ...formReset.value }
          const timeId = setTimeout(() => {
            proxy.$router.go(0)
            clearTimeout(timeId)
          }, 1000)
        }
      }

      return {
        loading,
        form,
        list,
        addProcess
      }
    },
    render () {
      const { form, loading, addProcess, list } = this
      return (
      <div>
        <div class="box">
          <p class="mb-8">添加处理进程</p>
          <Input.TextArea
            class="mb-16"
            v-model={form.content}
            placeholder="添加处理进程"
            rows={3}
          />
          <div class="mb-16">
            <span>附件：</span>
            <Uploader onChange={(files) => { form.attachFiles = files }} accept=".png,.jpg,.png," />
          </div>
          <Button
            type="primary"
            class="btn"
            size="small"
            loading={loading}
            onClick={addProcess}
          >
            添加进程
          </Button>
        </div>
        <div class="box">
          {list.length ? (
            <div>
              {list.map((item, index) => (
                <div
                  class="item flex-justify-between"
                  style="display:flex;"
                  key={index}
                >
                  <div class="flex">
                    <div>
                      <span
                        domPropsInnerHTML={item.dsc_}
                        class={item.fakeLog ? 'red' : ''}
                      ></span>
                      <span>{`【${item.opUser}】`}</span>
                      <span style="margin-left: 1em"> 附件:</span>
                      {item.attachments.length ? (
                        <span>
                          {item.attachments.map(img => (
                            <a
                              style="margin-right:1em;"
                              href={img.filepath}
                              key={img.id}
                              target="view_window"
                            >
                              {img.filename}
                            </a>
                          ))}
                        </span>
                      ) : (
                        <span> 无</span>
                      )}
                      <span style="margin-left:1em;color:#828080;font-size:12px;">
                        {item.intime}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div style="text-align: center;">暂无客诉处理进程</div>
          )}
        </div>
      </div>
      )
    }
  })
</script>

<style lang="scss" scoped>
.box {
  margin: 1em 0.5em;
  .item + .item {
    margin-top: 1.1em;
  }
}
</style>
