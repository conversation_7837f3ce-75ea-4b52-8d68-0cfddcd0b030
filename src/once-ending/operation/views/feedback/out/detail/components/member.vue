<script lang="jsx">
  import { defineComponent } from 'vue'
  import { Row, Col } from 'ant-design-vue'

  export default defineComponent({
    props: {
      detailData: {
        type: Object,
        default: () => {
          return {}
        }
      },
    },
    render () {
      const { detailData } = this
      return <div>
    <Row>
      <Col span={8}>
        <span>投诉人姓名：{detailData.memberName}</span>
      </Col>
      <Col span={8}>
        <span>联系电话：</span>
        <span>{detailData.mobile}</span>
      </Col>
    </Row>
  </div>
    }
  })
</script>

<style lang="scss" scoped>
.ant-row{
  margin-bottom: 1em;
}
.ant-col{
  display: flex;
  align-items: center;
  >span{
    white-space: nowrap;
  }
}
.width-200{
  width: 200px;
}
</style>
