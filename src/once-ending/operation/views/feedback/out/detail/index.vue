<script lang="jsx">
  import { defineComponent, ref, getCurrentInstance } from 'vue'
  import Member from './components/member.vue'
  import Detail from './components/detail.vue'
  import Process from './components/process.vue'
  import {
    DETAIL_FOR_USER
  } from '@operation/store/modules/feedback/action-types'

  export default defineComponent({
    components: {
      Member, Detail, Process
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const detailData = ref({})

      const getDetail = async function () {
        const params = { id: proxy.$route.params.id }
        const res = await proxy.$store.dispatch(
          `operation/feedback/${DETAIL_FOR_USER}`,
          params
        )
        if (res) {
          const { data } = res
          detailData.value = data
        }
      }

      getDetail()
      return {
        detailData,
        getDetail
      }
    },
    render () {
      const { detailData, getDetail } = this
      return <page style="background-color: inherit;">
  <div class="flex">
    <div style="width: 100%">
      <a-card class="card">
        <div class="card-item">
          <span class="title">投诉人信息：</span>
          <member ref="member" detail-data={detailData} />
        </div>
        <div class="card-item">
          <span class="title">投诉基础信息：</span>
          <detail ref="detail" detail-data={detailData} />
        </div>
        <div class="card-item">
          <span class="title">投诉处理进程：</span>
          <process ref="process" detail-data={detailData} onRefreshData={getDetail} />
        </div>
      </a-card>
    </div>
  </div>
</page>
    }
  })
</script>

<style lang="scss" scoped>
.card{
  background: #fff;
  &-item{
    .title{
      font-size: 18px;
      font-weight: 700;
      color:#909399;
    }
    >div{
      margin-bottom: 2em;
      border-radius: 4px;
      border: 1px solid #d7d7d7;
      padding: 1em;
    }
  }
}

</style>
