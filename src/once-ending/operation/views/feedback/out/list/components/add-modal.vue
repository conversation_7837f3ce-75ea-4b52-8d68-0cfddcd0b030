<template>
<a-modal
    title="投诉建议"
    :visible="visible"
    width="890px"
    destroyOnClose
    @cancel="handleCancel"
    :footer="null"
    dialogClass="feedback-add-modal"
  >
    <a-form-model
      :model="formData"
      ref="ruleForm"
      :label-col="{ span: 5 }"
      :wrapper-col="{ span: 16 }"
      :rules="addModalRoues"
    >
      <a-form-model-item label="姓名">
        {{ userInfo.UserName }}
      </a-form-model-item>
      <a-form-model-item label="联系电话">
        {{ mobile }}
      </a-form-model-item>
      <a-form-model-item label="合作伙伴">
        {{ $tnt.title }}
      </a-form-model-item>
      <a-form-model-item label="性质" prop="tag">
        <a-radio-group v-model="formData.tag">
          <template v-for="item in tagOptions">
            <a-radio :key="item.value" :value="item.value">{{
              item.label
            }}</a-radio>
          </template>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item label="描述" prop="content">
        <a-textarea
          v-model="formData.content"
          placeholder="请在此输入您想投诉的问题，我们会第一时间为您处理"
          :auto-size="{ minRows: 4 }"
        />
      </a-form-model-item>
      <a-form-model-item label="图片/视频上传">
        <CustomUploader
          :fileList.sync="formData.files"
          showThumbnail
          :showUpload="formData.files.length < 9"
          :isDetail="true"
          :showRename="false"
          :editFileName="false"
          />
        <div v-if="formData.files.length === 0" class="tip">
          请在此处上传图片或视频，有图片或视频更加利于工作人员还原问题，图片或视频最多支持上传9个
        </div>
      </a-form-model-item>
    </a-form-model>
    <div style="text-align: center; padding-top: 20px">
      <a-button @click="handleCancel" style="margin-right: 30px"
        >取 消</a-button
      >
      <a-button :loading="confirmLoading" type="primary" @click="handleOk"
        >确 定</a-button
      >
    </div>
  </a-modal>
</template>

<script>
  import { defineComponent, computed, ref, watch, getCurrentInstance } from 'vue'
  import { message } from 'ant-design-vue'
  import { addModalRoues, tagOptions } from '../../../constants'
  import CustomUploader from '../../../components/custom-uploader'
  import {
    SUBMIT_FEEDBACK,
    GET_MOBILE
  } from '@operation/store/modules/feedback/action-types'

  export default defineComponent({
    props: {
      showModal: {
        type: Boolean,
        default: false
      }
    },
    components: {
      CustomUploader
    },
    data () {
      return {
        addModalRoues,
        tagOptions
      }
    },
    setup (props, { emit }) {
      const { proxy } = getCurrentInstance()
      const visible = computed({
        get: () => props.showModal,
        set: val => emit('update:showModal', val)
      })

      const userInfo = computed(() => proxy.$store.state.userInfo)

      const confirmLoading = ref(false)

      const formData = ref({
        memberName: '',
        mobile: '',
        tag: undefined,
        content: '',
        files: []
      })

      const mobile = ref('')

      watch(
        () => visible.value,
        val => {
          if (val) getMobile()
        }
      )

      const ruleForm = ref(null)
      const handleOk = async function () {
        ruleForm.value.validate(valid => {
          if (valid) {
            submitFeedback()
          } else {
            return false
          }
        })
      }

      const submitFeedback = async function () {
        const params = {
          userId: userInfo.value.UserID,
          mobile: mobile.value,
          content: formData.value.content,
          memberName: userInfo.value.UserName,
          files: formData.value.files,
          tag: formData.value.tag,
          xtenant: proxy.$tnt.tenantId,
          xtenantName: proxy.$tnt.title,
          source: 1
        }
        const res = await proxy.$store.dispatch(
          `operation/feedback/${SUBMIT_FEEDBACK}`,
          params
        )
        if (res) {
          message.success('提交成功')
          visible.value = false
          emit('destroyModal')
          emit('feedbackList', 1)
        }
      }

      const handleCancel = function () {
        visible.value = false
        emit('destroyModal')
      }

      const getMobile = async function () {
        const params = {
          userId: userInfo.value.UserID
        }
        const res = await proxy.$store.dispatch(
          `operation/feedback/${GET_MOBILE}`,
          params
        )
        if (res) {
          mobile.value = res.data.mobile
        }
      }

      return {
        visible,
        userInfo,
        confirmLoading,
        formData,
        mobile,
        ruleForm,
        handleOk,
        handleCancel
      }
    }

  })
</script>

<style lang="scss" scoped>
.target-box {
  &:hover {
    border-color: #1890ff;
  }
  display: table;
  float: left;
  width: 104px;
  height: 104px;
  margin-right: 8px;
  margin-bottom: 8px;
  text-align: center;
  vertical-align: top;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  transition: border-color 0.3s ease;
  .target {
    display: table-cell;
    width: 100%;
    height: 100%;
    padding: 8px;
    text-align: center;
    vertical-align: middle;
  }
  i {
    font-size: 32px;
    color: #999;
  }
  .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }
}
.tip {
  font-size: 14px;
  line-height: 1.45;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.45);
  margin-top: -5px;
}
</style>
<style lang="scss">
.feedback-add-modal {
  .ant-upload-list-text {
    .ant-upload-item {
      &:hover {
        .anticon-delete {
          opacity: 1;
        }
      }
    }
    .anticon-delete {
      cursor: pointer;
      padding-top: 6px;
      opacity: 0;
    }
  }
}
</style>
