<script lang="jsx">
  import { defineComponent, onActivated, ref, getCurrentInstance, nextTick } from 'vue'
  import { tagOptions, searchStateOptions, customerOptions, dateTypeOptions, searchKindOptions, columns } from '../../constants'
  import AddModal from './components/add-modal.vue'
  import moment from 'moment'
  import { NiListPage, NiFilter, NiFilterItem, NiTable } from '@jiuji/nine-ui'
  import {
    FEEDBACK_LIST
  } from '@operation/store/modules/feedback/action-types'
  import NoData from '@operation/components/no-data'
  export default defineComponent({
    components: {
      NiListPage, NiFilter, NiFilterItem, NiTable, AddModal, NoData
    },
    setup (props, { emit }) {
      const { proxy } = getCurrentInstance()
      const form = ref({
        tag: undefined, // 性质
        searchState: undefined, // 处理状态
        // 高级筛选
        customer: undefined, // 客诉状态
        dateType: 1, // 时间筛选类型
        times: null, // 时间范围
        searchKind: undefined, // 其他筛选条件
        key: undefined
      })

      form.value.times = [
        moment(new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000)).format(
          'YYYY-MM-DD'
        ),
        moment(new Date()).format('YYYY-MM-DD')
      ]

      if (proxy.$route.query.searchKind && proxy.$route.query.key) {
        form.value.searchKind = Number(proxy.$route.query.searchKind)
        form.value.key = proxy.$route.query.key
        form.value.times = null
      }

      const dataSource = ref([])

      const pagination = ref({
        current: 1,
        pageSize: 50,
        total: 0,
        pageSizeOptions: ['20', '50', '100', '200'],
        showQuickJumper: true,
        showTotal: total => `共计${total}条投诉`
      })

      const showModal = ref(false)

      const destroyComponent = ref(true)

      const loading = ref(false)
      const isFeatch = ref(false)
      const feedbackList = async function (cur) {
        if (cur) pagination.value.current = cur
        const params = JSON.parse(JSON.stringify(form.value))
        const { times, dateType } = params
        params.source = 1 // 租户数据来源默认为1
        params.xtenant = proxy.$tnt.tenantId
        if (times && dateType) {
          params.startTime = `${times[0]} 00:00:00`
          params.endTime = `${times[1]} 23:59:59`
        }
        const { current, pageSize: size } = pagination.value
        params.current = current
        params.size = size
        delete params.times
        if (!params.searchKind || !params.key) {
          delete params.searchKind
          delete params.key
        }
        loading.value = true
        const res = await proxy.$store.dispatch(
          `operation/feedback/${FEEDBACK_LIST}`,
          params
        )
        loading.value = false
        isFeatch.value = true
        if (res) {
          const {
            data: { records, total }
          } = res
          dataSource.value = records
          pagination.value.total = total
        }
      }
      feedbackList()

      onActivated(() => {
        feedbackList()
      })

      const tableChange = function (paginations) {
        pagination.value = { ...paginations }
        feedbackList()
      }

      const add = function () {
        showModal.value = true
      }

      const destroyModal = function () {
        // 后端提交投诉过后数据写入延迟,导致查询列表无新增数据,延迟查询列表
        setTimeout(() => {
          feedbackList()
        }, 500)
        destroyComponent.value = false
        nextTick(() => {
          destroyComponent.value = true
        })
      }

      return {
        form,
        dataSource,
        pagination,
        showModal,
        destroyComponent,
        tableChange,
        feedbackList,
        add,
        destroyModal,
        isFeatch,
        loading
      }
    },
    render () {
      const { add, form, loading, isFeatch, feedbackList, dataSource, tableChange, pagination, destroyComponent, showModal, destroyModal } = this
      return <page>
  <template slot="extra">
    <a-button type="primary" class="btn-success" onClick={add}>添加投诉建议</a-button>
  </template>
  <ni-list-page push-filter-to-location={false}>
  <ni-filter
          form={form}
          loading={loading}
          onFilter={() => {
            feedbackList(1)
          }}
          label-width={100}
          immediate={false}
        >
        <ni-filter-item label="性质">
          <a-select allowClear placeholder="请选择" v-model={form.tag} options={tagOptions} />
        </ni-filter-item>
        <ni-filter-item label="处理状态">
          <a-select allowClear placeholder="请选择" v-model={form.searchState} options={searchStateOptions} />
        </ni-filter-item>
        <ni-filter-item label="客诉状态">
          <a-select allowClear placeholder="请选择" v-model={form.customer} options={customerOptions} />
        </ni-filter-item>
        <ni-filter-item class="no-label-none">
          <a-input-group compact style="width:400px">
            <a-select allowClear style="width:30%;" placeholder="请选择" v-model={form.dateType} options={dateTypeOptions} />
            <a-range-picker allowClear={false} style="width: 70%" v-model={form.times} format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"/>
          </a-input-group>
        </ni-filter-item>
        <ni-filter-item class="no-label-none">
          <a-input-group compact style="width:400px">
            <a-select allowClear style="width:30%;" placeholder="请选择" v-model={form.searchKind} options={searchKindOptions.filter(d => d.show.includes(1))} />
            <a-input v-model={form.key} style="width: 70%" placeholder="输入内容"/>
          </a-input-group>
        </ni-filter-item>
        </ni-filter>
        <ni-table
            style="margin-top: 10px;"
            class="feedback-table"
            rowKey="tsId"
            locale={{ emptyText: <NoData is-featch={isFeatch} /> }}
            dataSource={dataSource}
            columns={columns.filter(d => d.show.includes(1))}
            loading={loading}
            onChange={tableChange}
            pagination={pagination}
            scopedSlots={{
              toDetail: (text, item) => (
                <span>
                  <RouterLink to={`detail/${item.tsId}`} target="_blank">
                    {text}
                  </RouterLink>
                </span>
              ),
              action: (text, item) => (
                <RouterLink to={`detail/${item.tsId}`} target="_blank">
                  查看详情
                </RouterLink>
              )
            }}
          ></ni-table>
  </ni-list-page>
        {
          destroyComponent ? <AddModal show-modal={showModal} {...{ on: { 'update:showModal': val => { this.showModal = val } } }} onFeedbackList={feedbackList} onDestroyModal={destroyModal}></AddModal> : null
        }

</page>
    }
  })
</script>

<style lang="scss" scoped>
$success-color: #13cc1a;
$success-hover-color: darken($success-color, 5%);
.btn-success {
  margin-right: 20px;
  background-color: $success-color;
  color: white;
  border: none;

  &:focus {
    color: white;
    background: $success-hover-color;
  }

  &:hover {
    color: white;
    background: $success-hover-color;
  }
}

.ant-btn + .ant-btn {
  margin-left: 8px;
}

.ant-col {
  padding: 10px 5px;
  display: flex;
  flex-wrap: wrap;

  span {
    padding-right: 5px;
    white-space: nowrap;
  }
}
:deep(.no-label-none){
  .label{
    display: none;
  }
}
</style>
