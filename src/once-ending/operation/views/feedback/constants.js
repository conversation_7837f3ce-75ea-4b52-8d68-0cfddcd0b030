// 性质
export const tagOptions = [
  {
    label: '投诉',
    value: 1
  },
  {
    label: '建议',
    value: 2
  },
  {
    label: '表扬',
    value: 3
  },
  {
    label: '金点子',
    value: 4
  },
]
export const tagOptMap = new Map([
  [0, ['', '#00996e']],
  [1, ['投诉', '#f56c6c']],
  [2, ['建议', '#f5a623']],
  [3, ['表扬', '#00996e']],
  [4, ['金点子', '#FA6400']],
])
// 合作伙伴类型options
export const sourceOptions = [
  {
    label: '中大型',
    value: 1
  },
  {
    label: 'NEO',
    value: 2
  }
]

// 定性分类
export const catOptions = [
  {
    label: '有效投诉',
    value: 1
  },
  {
    label: '无效投诉',
    value: 2
  },
  {
    label: '占额度投诉',
    value: 3
  }
]

// 投诉等级
export const complaintRankOptions = [
  {
    label: '轻微',
    value: 1
  },
  {
    label: '一般',
    value: 2
  },
  {
    label: '重要',
    value: 3
  },
  {
    label: '严重',
    value: 4
  },
  {
    label: '重大',
    value: 5
  },
]

// 其他筛选条件
export const searchStateOptions = [
  {
    label: '未处理',
    value: 1
  },
  {
    label: '已受理',
    value: 2
  },
  {
    label: '已完成',
    value: 3
  },
  {
    label: '待整改',
    value: 4
  },
  {
    label: '已处理',
    value: 5
  },
  {
    label: '已整改',
    value: 6
  },
  {
    label: '已还原',
    value: 7
  },
  {
    label: '已界定',
    value: 8
  },
  {
    label: '待复核',
    value: 9
  },
  {
    label: '待仲裁',
    value: 10
  }
]

// 静态
export const plainOptions = [
  {
    label: '有罚款',
    value: 2
  },
  {
    label: '已超时',
    value: 3
  },
]

export const newPlainOptions = plainOptions.concat([
  {
    label: '进行中',
    value: 4
  }
])

// 时间筛选类型
export const dateTypeOptions = [
  {
    label: '投诉时间',
    value: 1
  },
  {
    label: '完结时间',
    value: 2
  },
]
export const searchKindOptions = [
  {
    label: '投诉内容',
    value: 2,
    show: [0, 1]
  },
  {
    label: '投诉ID',
    value: 1,
    show: [0, 1]
  },
  {
    label: '进程内容',
    value: 4,
    show: [0, 1]
  },
  {
    label: '责任人',
    value: 5,
    show: [0]
  },
  {
    label: '跟进人',
    value: 6,
    show: [0]
  },
  {
    label: '投诉人联系电话',
    value: 7,
    show: [0]
  },
  {
    label: '投诉人',
    value: 3,
    show: [0]
  },
]

export const columns = [
  {
    title: '投诉ID',
    dataIndex: 'tsId',
    key: 'tsId',
    scopedSlots: { customRender: 'toDetail' },
    show: [0, 1]
  },
  {
    title: '投诉性质',
    dataIndex: 'tag',
    key: 'tag',
    show: [0],
    scopedSlots: { customRender: 'tag' },
  },
  {
    title: '合作伙伴',
    dataIndex: 'xtenantName',
    key: 'xtenantName',
    show: [0]
  },
  {
    title: '合作伙伴类型',
    dataIndex: 'sourceStr',
    key: 'sourceStr',
    show: [0]
  },
  {
    title: '投诉时间',
    dataIndex: 'addTime',
    key: 'addTime',
    show: [0, 1],
    scopedSlots: { customRender: 'addTime' },
  },
  {
    title: '投诉内容',
    dataIndex: 'content',
    width: '480px',
    key: 'content',
    show: [0, 1]
  },
  {
    title: '处理状态',
    dataIndex: 'states',
    key: 'states',
    show: [0, 1]
  },
  {
    title: '责任部门',
    dataIndex: 'departList',
    align: 'center',
    width: '200px',
    scopedSlots: { customRender: 'departList' },
    show: [0]
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    scopedSlots: { customRender: 'action' },
    show: [0, 1]
  }
]

export const addModalRoues = {
  content: [{ required: true, message: '请输入投诉描述', trigger: 'blur' }],
  tag: [{ required: true, message: '请选择投诉性质', trigger: 'change' }]
}

export const modelEnum = {
  cat: [
    {
      label: '有效投诉',
      value: 1
    },
    {
      label: '无效投诉',
      value: 2
    },
    {
      label: '占额度投诉',
      value: 3
    },
  ],
  touSuRank: [
    {
      label: '轻微',
      value: 1
    },
    {
      label: '一般',
      value: 2
    },
    {
      label: '重要',
      value: 3
    },
    {
      label: '严重',
      value: 4
    },
    {
      label: '重大',
      value: 5
    }
  ]
}

export const statesEnum = {
  1: '未受理',
  2: '已受理',
  7: '已还原',
  5: '已处理',
  8: '已界定',
  9: '待复核',
  10: '待仲裁',
  4: '待整改',
  6: '已整改',
  3: '已完成',
}

// 客诉状态
export const customerOptions = [
  {
    label: '客诉完结',
    value: 1
  },
  {
    label: '进行中',
    value: 2
  }
]
