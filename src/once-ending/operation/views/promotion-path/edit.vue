<template>
  <page>
    <div class="flex white-bg container">
      <div style="width: 350px;margin-right: 60px">
        <a-form-model :model="form" ref="formRef" :rules="rules">
          <a-form-model-item label="组织架构" prop="departId">
            <a-tree-select
              :showSearch="true"
              treeNodeFilterProp='title'
              @change="changeDepart"
              :tree-data="departOptions"
              placeholder="请选择"
              :dropdown-style="{ maxHeight: '600px', overflow: 'auto' }"
              v-model="form.departId"/>
          </a-form-model-item>
          <a-form-model-item label="画布高度" prop="canvasHeight">
            <a-input-number
              :min="1"
              :max="2000"
              :precision="0"
              @change="changeCanvasHeight"
              placeholder="请输入"
              class="full-width"
              v-model="form.canvasHeight"/>
          </a-form-model-item>
          <a-form-model-item label="字体大小" prop="fontSize">
            <a-input-number
              :min="12"
              :max="30"
              :precision="0"
              @change="changeFontSize"
              placeholder="请输入"
              class="full-width"
              v-model="form.fontSize"/>
          </a-form-model-item>
        </a-form-model>
      </div>
      <!-- 左侧工具 -->
      <div id="stencil"></div>
      <!-- 画布容器 -->
      <div id="graph-container"
           :style="{ height: (form.cacheCanvasHeight || 667) + 'px', fontSize: 20 + 'px' }"></div>
      <!-- 右侧详情 -->
      <div id="info-container" class="ml-16" style="width: 500px" v-if="actionNode && form.positionDatas[actionIndex]">
        <a-form-model
          :labelCol="{ span: 5 }"
          :wrapperCol="{ span: 19 }">
          <a-form-model-item label="岗位">
            <a-input
              class="full-width"
              @change="updateJobName"
              placeholder="请输入"
              v-model="form.positionDatas[actionIndex].positionName"/>
          </a-form-model-item>
          <a-form-model-item label="学习项目">
            <a-textarea
              class="full-width"
              :autoSize="{ minRows: 2, maxRows: 20 }"
              placeholder="请输入"
              v-model="form.positionDatas[actionIndex].learningItem"/>
          </a-form-model-item>
          <a-form-model-item label="必要项">
            <a-textarea
              class="full-width"
              :autoSize="{ minRows: 2, maxRows: 20 }"
              placeholder="请输入"
              v-model="form.positionDatas[actionIndex].requirementItem"/>
          </a-form-model-item>
          <a-form-model-item label="加分项">
            <a-textarea
              class="full-width"
              :autoSize="{ minRows: 2, maxRows: 20 }"
              placeholder="请输入"
              v-model="form.positionDatas[actionIndex].addScoreItem"/>
          </a-form-model-item>
        </a-form-model>
      </div>
      <div class="buttons">
        <a-button :loading="loading" type="primary" @click="savePath">保存</a-button>
      </div>
    </div>
  </page>
</template>
<script lang="jsx">
  import {
    defineComponent,
    ref,
    reactive,
    toRefs,
    getCurrentInstance,
    nextTick,
    onMounted
  } from 'vue'
  import { Graph, Shape } from '@antv/x6'
  import { Stencil } from '@antv/x6-plugin-stencil'
  import { Transform } from '@antv/x6-plugin-transform'
  import { Selection } from '@antv/x6-plugin-selection'
  import { Snapline } from '@antv/x6-plugin-snapline'
  import { Keyboard } from '@antv/x6-plugin-keyboard'
  import { Clipboard } from '@antv/x6-plugin-clipboard'
  import { History } from '@antv/x6-plugin-history'
  import { rules, formItem } from './constants'
  import { to } from '~/util/common'
  import promotionPathApi from '@operation/api/promotion-path'
  import { debounce, cloneDeep } from 'lodash'
  export default defineComponent({
    name: 'PromotionPath',
    setup () {
      const { proxy } = getCurrentInstance()
      // const { Department } = proxy.$store.state.userInfo
      const state = reactive({
        loading: false,
        flowChartJson: '',
        departOptions: [],
        form: {
          departId: undefined,
          canvasHeight: undefined,
          cacheCanvasHeight: undefined,
          fontSize: undefined,
          positionDatas: []
        },
        currentItem: cloneDeep(formItem),
        actionIndex: undefined
      })
      const graph = ref(null)
      const stencil = ref(null)
      const actionNode = ref(null)
      const hoverNode = ref(null)
      const hoverLine = ref(null)
      const formRef = ref(null)

      async function getPath () {
        const { departId } = state.form
        proxy.$indicator.open()
        const [err, res] = await to(promotionPathApi.getUserDevelopmentPath({ departId }))
        proxy.$indicator.close()
        if (err) throw err
        const { code, userMsg } = res
        if (code === 0) {
          const { flowChartJson, canvasHeight, fontSize, positionDatas } = res.data
          state.flowChartJson = flowChartJson || ''
          state.form.canvasHeight = canvasHeight || 667
          state.form.cacheCanvasHeight = canvasHeight || 667
          state.form.fontSize = fontSize || 15
          state.form.positionDatas = positionDatas || []
          if (graph.value) {
            importJson()
            addGroup()
          } else {
            nextTick(() => {
              initChart()
            })
          }
        } else {
          proxy.$message.error(userMsg)
        }
      }

      function dealOptions (array) {
        return Array.isArray(array) ? array.map(item => ({
          label: item.name,
          id: item.id,
          value: item.id,
          children: item.childs?.length ? dealOptions(item.childs) : [],
        })) : []
      }
      async function getDepartOptions () {
        proxy.$indicator.open()
        const [err, res] = await to(promotionPathApi.getDepartOptions())
        proxy.$indicator.close()
        if (err) throw err
        const { code, userMsg, data } = res
        if (code === 0) {
          state.departOptions = dealOptions(data)
        } else {
          proxy.$message.success(userMsg)
        }
      }
      getDepartOptions()

      function importJson () {
        const { flowChartJson } = state
        graph.value.fromJSON(flowChartJson ? JSON.parse(flowChartJson) : flowChartJson)
        addEvent()
      }

      function addEvent () {
        document.addEventListener('keydown', (e) => {
          const step = 10
          if (!actionNode.value) return
          const position = actionNode.value.getPosition()
          switch (e.keyCode) {
          case 37: // 左键
            actionNode.value.setPosition({ x: position.x - step, y: position.y })
            break
          case 39: // 右键
            actionNode.value.setPosition({ x: position.x + step, y: position.y })
            break
          case 38: // 上键
            actionNode.value.setPosition({ x: position.x, y: position.y - step })
            break
          case 40: // 下键
            actionNode.value.setPosition({ x: position.x, y: position.y + step })
            break
          default:
            break
          }
        })
      }

      async function savePath () {
        formRef.value.validate(async (valid) => {
          if (valid) {
            if (!graph.value) return
            // 获取所有节点
            const nodes = graph.value.getNodes()
            nodes?.forEach(node => {
              node.removeTools()
            })
            const needNode = nodes.filter(node => !node.attrs?.image)
            const { positionDatas, cacheCanvasHeight, ...other } = state.form
            const datas = needNode.map(o => {
              const item = positionDatas.find(it => it.positionId === o.id)
              return item || { positionId: o.id, positionName: o.label, ...formItem }
            })
            const flowChartJson = JSON.stringify(graph.value.toJSON({ diff: true }))
            const params = {
              ...other,
              positionDatas: datas,
              flowChartJson: datas?.length ? flowChartJson : '' // 有节点才存json
            }
            state.loading = true
            const [err, res] = await to(promotionPathApi.addOrUpdateUserDevelopmentPath(params))
            state.loading = false
            if (err) throw err
            const { code, userMsg } = res
            if (code === 0) {
              proxy.$message.success('保存成功')
            } else {
              proxy.$message.error(userMsg)
            }
          } else {
            proxy.$message.warning('请先完善数据')
          }
        })
      }

      function changeDepart () {
        getPath()
      }

      let changeCanvasHeight = () => {
        const { canvasHeight } = state.form
        state.form.cacheCanvasHeight = canvasHeight
        graph.value.resize(375, canvasHeight)
      }
      changeCanvasHeight = debounce(changeCanvasHeight, 300)

      let changeFontSize = () => {
        const nodes = graph.value?.getNodes()
        const { fontSize } = state.form
        nodes?.forEach(node => {
          node?.attr({
            label: {
              fontSize: fontSize
            }
          })
        })
        addGroup()
      }
      changeFontSize = debounce(changeFontSize, 300)

      function clickNode (event) {
        actionNode.value = event.node
        if (event.node.attrs?.image) {
          state.actionIndex = undefined
          return
        }
        const { id, label } = event.node
        const { positionDatas } = state.form
        const actionIndex = positionDatas?.findIndex(item => item.positionId === id)
        if (actionIndex > -1) {
          state.actionIndex = actionIndex
        } else {
          state.form.positionDatas.push({
            positionId: id,
            positionName: label,
            ...formItem
          })
          state.actionIndex = state.form.positionDatas.length - 1
        }
      }
      function clickBlank () {
        const nodes = graph.value.getNodes()
        nodes?.forEach(node => {
          node.removeTools()
        })
      }

      function updateJobName () {
        const { positionName } = state.form.positionDatas[state.actionIndex]
        if (actionNode.value) {
          actionNode.value.label = positionName || '岗位'
          // actionNode.value.setLabel(positionName)
        }
      }

      onMounted(() => {
        initChart()
      })
      function initChart () {
        // #region 初始化画布
        graph.value = new Graph({
          container: document.getElementById('graph-container'),
          grid: true,
          mousewheel: {
            enabled: true,
            zoomAtMousePosition: true,
            modifiers: 'ctrl',
            minScale: 0.5,
            maxScale: 3,
          },
          connecting: {
            router: {
              name: 'manhattan',
              args: {
                padding: 1,
              },
            },
            connector: {
              name: 'rounded',
              args: {
                radius: 8,
              },
            },
            anchor: 'center',
            connectionPoint: 'anchor',
            allowBlank: false,
            snap: {
              radius: 20,
            },
            createEdge () {
              return new Shape.Edge({
                attrs: {
                  line: {
                    stroke: 'rgba(24, 144, 255, 0.6)',
                    strokeWidth: 2,
                    strokeLinecap: 'round',
                    targetMarker: {
                      tagName: 'none',
                    },
                  },
                },
                zIndex: 0,
              })
            },
            validateConnection ({ targetMagnet }) {
              return !!targetMagnet
            },
          },
          highlighting: {
            magnetAdsorbed: {
              name: 'stroke',
              args: {
                attrs: {
                  fill: '#5F95FF',
                  stroke: '#5F95FF',
                },
              },
            },
          },
          resizing: true,
          rotating: true,
          selecting: {
            enabled: true,
            rubberband: true,
            showNodeSelectionBox: true,
          },
          snapline: true,
          keyboard: true,
          clipboard: true,
        })
        // #endregion
        // #region 使用插件
        graph.value
          .use(
            new Transform({
              resizing: true,
              rotating: true,
            }),
          )
          .use(
            new Selection({
              rubberband: true,
              showNodeSelectionBox: true,
            }),
          )
          .use(new Snapline())
          .use(new Keyboard())
          .use(new Clipboard())
          .use(new History())
        // #region 初始化 stencil
        stencil.value = new Stencil({
          title: '流程图',
          target: graph.value,
          stencilGraphWidth: 200,
          stencilGraphHeight: 80,
          collapsable: true,
          groups: [
            {
              title: '基础流程图',
              name: 'group1',
            },
            {
              title: '系统设计图',
              name: 'group2',
              graphHeight: 250,
            },
          ],
          layoutOptions: {
            columns: 2,
            columnWidth: 80,
            rowHeight: 55,
          },
        })
        document.getElementById('stencil').appendChild(stencil.value.container)
        // #endregion

        // #region 快捷键与事件
        // copy cut paste
        graph.value.bindKey(['meta+c', 'ctrl+c'], () => {
          const cells = graph.value.getSelectedCells()
          if (cells.length) {
            graph.value.copy(cells)
          }
          return false
        })
        graph.value.bindKey(['meta+x', 'ctrl+x'], () => {
          const cells = graph.value.getSelectedCells()
          if (cells.length) {
            graph.value.cut(cells)
          }
          return false
        })
        graph.value.bindKey(['meta+v', 'ctrl+v'], () => {
          if (!graph.value.isClipboardEmpty()) {
            const cells = graph.value.paste({ offset: 32 })
            graph.value.cleanSelection()
            graph.value.select(cells)
          }
          return false
        })

        // undo redo
        graph.value.bindKey(['meta+z', 'ctrl+z'], () => {
          if (graph.value.history.canUndo()) {
            graph.value.history.undo()
          }
          return false
        })
        graph.value.bindKey(['meta+shift+z', 'ctrl+shift+z'], () => {
          if (graph.value.history.canRedo()) {
            graph.value.history.redo()
          }
          return false
        })

        // select all
        graph.value.bindKey(['meta+a', 'ctrl+a'], () => {
          const nodes = graph.value.getNodes()
          if (nodes) {
            graph.value.select(nodes)
          }
        })

        // delete
        graph.value.bindKey('backspace', () => {
          const cells = graph.value.getSelectedCells()
          if (cells.length) {
            graph.value.removeCells(cells)
          }
        })

        // zoom
        graph.value.bindKey(['ctrl+1', 'meta+1'], () => {
          const zoom = graph.value.zoom()
          if (zoom < 1.5) {
            graph.value.zoom(0.1)
          }
        })
        graph.value.bindKey(['ctrl+2', 'meta+2'], () => {
          const zoom = graph.value.zoom()
          if (zoom > 0.5) {
            graph.value.zoom(-0.1)
          }
        })

        // 控制连接桩显示/隐藏
        const showPorts = (ports, show) => {
          for (let i = 0, len = ports.length; i < len; i = i + 1) {
            ports[i].style.visibility = show ? 'visible' : 'hidden'
          }
        }
        graph.value.on('node:mouseenter', () => {
          const container = document.getElementById('graph-container')
          const ports = container.querySelectorAll(
            '.x6-port-body',
          )
          showPorts(ports, true)
        })
        graph.value.on('node:mouseleave', () => {
          const container = document.getElementById('graph-container')
          const ports = container.querySelectorAll(
            '.x6-port-body',
          )
          showPorts(ports, false)
        })
        // #endregion

        // #region 初始化图形
        const ports = {
          groups: {
            top: {
              position: 'top',
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: '#5F95FF',
                  strokeWidth: 1,
                  fill: '#fff',
                  style: {
                    visibility: 'hidden',
                  },
                },
              },
            },
            right: {
              position: 'right',
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: '#5F95FF',
                  strokeWidth: 1,
                  fill: '#fff',
                  style: {
                    visibility: 'hidden',
                  },
                },
                fo: {
                  width: 12,
                  height: 12,
                  x: -6,
                  y: -6,
                  magnet: 'true',
                },
              },
            },
            bottom: {
              position: 'bottom',
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: '#5F95FF',
                  strokeWidth: 1,
                  fill: '#fff',
                  style: {
                    visibility: 'hidden',
                  },
                },
              },
            },
            left: {
              position: 'left',
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: '#5F95FF',
                  strokeWidth: 1,
                  fill: '#fff',
                  style: {
                    visibility: 'hidden',
                  },
                },
              },
            },
          },
          items: [
            { group: 'top', id: 'top' },
            { group: 'right', id: 'right1' },
            { group: 'right', id: 'right2' },
            { group: 'right', id: 'right3' },
            { group: 'bottom', id: 'bottom' },
            { group: 'left', id: 'left1' },
            { group: 'left', id: 'left2' },
            { group: 'left', id: 'left3' },
          ],
        }

        Graph.registerNode(
          'custom-rect',
          {
            inherit: 'rect',
            width: 80,
            height: 38,
            attrs: {
              body: {
                strokeWidth: 1,
                stroke: 'rgba(24, 144, 255, 1)',
                fill: 'rgba(24, 144, 255, 1)',
              },
              text: {
                fontSize: state.form.fontSize || 15,
                fontWeight: 600,
                fill: 'rgba(24, 144, 255, 1)'
              },
              label: {
                padding: 6,
                textWrap: {
                  breakWord: true,
                },
              }
            },
            ports: { ...ports },
          },
          true,
        )

        Graph.registerNode(
          'custom-polygon',
          {
            inherit: 'polygon',
            width: 66,
            height: 38,
            attrs: {
              body: {
                strokeWidth: 1,
                stroke: '#5F95FF',
                fill: '#EFF4FF',
              },
              text: {
                fontSize: 12,
                fill: '#262626',
              },
            },
            ports: {
              ...ports,
              items: [
                {
                  group: 'top',
                },
                {
                  group: 'bottom',
                },
              ],
            },
          },
          true,
        )

        Graph.registerNode(
          'custom-image',
          {
            inherit: 'rect',
            width: 21,
            height: 28,
            markup: [
              {
                tagName: 'rect',
                selector: 'body',
              },
              {
                tagName: 'image',
              },
              {
                tagName: 'text',
                selector: 'label',
              },
            ],
            attrs: {
              body: {
                stroke: '#5F95FF',
                fill: '#5F95FF',
              },
              image: {
                width: 17,
                height: 21,
                refX: 2,
                refY: 6,
              },
              label: {
                refX: 3,
                refY: 2,
                textAnchor: 'left',
                textVerticalAnchor: 'top',
                fontSize: 12,
                fill: '#fff',
              },
            },
            ports: { ...ports },
          },
          true,
        )

        addGroup()

        const imageShapes = [
          {
            label: '箭头',
            image:
              'https://img2.ch999img.com/newstatic/54457/12a57a9a2a92e39d.png',
          },
        ]
        const imageNodes = imageShapes.map((item) =>
          graph.value.createNode({
            shape: 'custom-image',
            // label: item.label,
            attrs: {
              body: {
                fill: 'none',
                strokeWidth: 0,
              },
              image: {
                'xlink:href': item.image,
              },
            },
          }),
        )
        stencil.value.load(imageNodes, 'group2')
        graph.value.on('node:click', clickNode)
        /**
         * 空白画布点击事件
         * */
        graph.value.on('blank:click', clickBlank)
        graph.value.on('node:mouseenter', ({ node }) => {
          hoverNode.value = node
          if (node === hoverNode.value) {
            node.addTools({
              name: 'button-remove',
              args: {
                x: 0,
                y: 0,
                offset: { x: 0, y: 0 },
              }
            })
          }
        })
        graph.value.on('node:removed', ({ node }) => {
          if (node === actionNode.value) {
            actionNode.value = null
          }
        })
        graph.value.on('node:mouseleave', ({ node }) => {
          if (node === hoverNode.value) {
            node.removeTools()
          }
        })
        graph.value.on('edge:mouseenter', ({ edge }) => {
          hoverLine.value = edge
          if (edge === hoverLine.value) {
            edge.addTools({
              name: 'button-remove',
              args: { distance: -40 }
            })
          }
        })
        graph.value.on('edge:mouseleave', ({ edge }) => {
          if (edge === hoverLine.value) {
            edge.removeTools()
          }
        })
        importJson()
      }

      function addGroup () {
        const r1 = graph.value?.createNode({
          shape: 'custom-rect',
          attrs: {
            body: {
              rx: 6,
              ry: 6,
              stroke: 'rgba(24, 144, 255, 1)',
              strokeWidth: 1,
              fill: 'rgba(24,144,255,0.06)',
            },
            text: {
              fontSize: state.form.fontSize || 15,
              fontWeight: 600,
              fill: 'rgba(24, 144, 255, 1)'
            }
          },
          label: '岗位',
        })
        stencil.value?.load([r1], 'group1')
      }

      return {
        ...toRefs(state),
        savePath,
        changeDepart,
        changeCanvasHeight,
        changeFontSize,
        actionNode,
        updateJobName,
        formRef,
        rules
      }
    },
  })
</script>

<style scoped lang="scss">
.container {
  padding: 24px;
  margin-bottom: 90px;
  border-radius: 8px;
}
.buttons {
  width: 100vw;
  position: fixed;
  left: 0;
  bottom: 0;
  background: #fff;
  padding: 24px;
}
:deep(.ant-form-item) {
  display: flex;
  .ant-form-item-control-wrapper {
    flex-grow: 1;
  }
}
#stencil {
  width: 200px;
  height: 667px;
  position: relative;
  flex-shrink: 0;
}

#graph-container {
  width: 375px;
  position: relative;
  flex-shrink: 0;
}
#node-info{
  width: 100%;
  box-sizing: border-box;
  padding: 20px;
  display: none;

}
</style>
