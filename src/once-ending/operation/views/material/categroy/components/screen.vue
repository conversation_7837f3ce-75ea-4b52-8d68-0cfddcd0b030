
<script type='text/jsx' lang="jsx">
  import { onMounted } from 'vue'
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions'

  import { NiFilter, NiFilterItem } from '@jiuji/nine-ui'
  import ExcelAction from '~/pages/operation/components/excel-action'
  import CategroyVsFormSelect from '../../components/select/categroy-vs-form'
  import { dictionary } from '../../common/data.js'

  export default {
    name: 'categroy-screen',
    components: {
      CategroyVsFormSelect,
      ExcelAction,
      NiFilter,
      NiFilterItem,
    },

    setup () {
      const {
        state,
        setScreenForm,
        resetScreenForm,
        initScreenFormReset,
        initCategroyFormReset,
      } = useState()
      const { fetchData } = useActions()

      const onChangeCvsF = ({ categoryId, formId }) => {
        setScreenForm(categoryId, 'categoryId')
        setScreenForm(formId, 'formId')
      }
      const doSearch = () => { fetchData(1) }
      const doReset = () => {
        resetScreenForm()
        doSearch()
      }

      onMounted(() => {
        initScreenFormReset()
        initCategroyFormReset()
        fetchData()
      })

      return {
        state,
        setScreenForm,
        doSearch,
        doReset,
        onChangeCvsF
      }
    },

    render () {
      const {
        state: { screenForm },
        setScreenForm,
        doSearch,
        onChangeCvsF
      } = this
      // loading={ isLoading }
      return (
        <NiFilter
          class="relative"
          layout='neat'
          form={ screenForm }
          onFilter={ doSearch }
          immediate={false}
        >
          <ni-filter-item class='no-label'>
            <CategroyVsFormSelect
              value={{
                categoryId: screenForm.categoryId,
                formId: screenForm.formId
              }}
              onChange={ onChangeCvsF }
            />
          </ni-filter-item>
          <ni-filter-item label="物料状态">
            <a-select
              allowClear
              placeholder="物料状态"
              options={ dictionary.materielStatus }
              value={ screenForm.formStatus }
              onChange={ (value) => {
                setScreenForm(value, 'formStatus')
              } }
            />
          </ni-filter-item>
       </NiFilter>
      )
    }
  }
</script>

<style lang="scss" scope>
  @import '../../common/style';
</style>
