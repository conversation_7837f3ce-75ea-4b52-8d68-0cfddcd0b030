<script type='text/jsx' lang="jsx">
  import { watch } from 'vue'
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions'

  import UploaderImg from '~/components/upload/uploaderImg-byNine'
  import CategroySelect from '../../components/select/categroy-vs-form/categroy'

  export default {
    name: 'modal-categroyForm',
    components: {
      UploaderImg,
      CategroySelect
    },
    setup () {
      const {
        state,
        setCategroyForm,
        setCategroyFormIsShow,
      } = useState()
      const {
        submitCategroy
      } = useActions()

      return {
        state,
        submitCategroy,
        setCategroyFormIsShow,
        setCategroyForm,
      }
    },
    render () {
      const {
        state: {
          screenForm,
          isAdd,
          categroyForm,
          categroyFormIsShow,
        },
        setCategroyForm,
        submitCategroy,
        setCategroyFormIsShow,
      } = this
      const isFrom = categroyForm.type === 2

      return (
        <a-modal
          maskClosable = { false }
          title={ isAdd ? '新增物料' : '编辑物料' }
          visible={ categroyFormIsShow }
          footer={ null }
          onOk={ submitCategroy }
          onCancel={ () => { setCategroyFormIsShow(false) } }
          destroyOnClose
        >
        <a-form class='categroy-form'>
          <a-form-item label={ isAdd ? '新增类型' : '编辑类型' }>
            <a-radio-group
              value={ categroyForm.type }
              onChange={ e => {
                setCategroyForm(e.target.value, 'type')
              } }
            >
              <a-radio value={ 1 }>物料类别</a-radio>
              <a-radio value={ 2 }>物料形式</a-radio>
            </a-radio-group>
          </a-form-item>

          { !isFrom
            ? <div>
                <a-form-item label='名称' required>
                  <a-input
                    value={ categroyForm.categoryName }
                    onChange={ e => {
                      setCategroyForm(e.target.value, 'categoryName')
                    } }
                  />
                </a-form-item>
                <a-form-item label='状态' >
                  <a-switch
                    checked-children="启用"
                    un-checked-children="停用"
                    checked={categroyForm.categoryStatus}
                    onChange={ () => {
                      setCategroyForm(!categroyForm.categoryStatus, 'categoryStatus')
                    } }
                  />
                  {!categroyForm.categoryStatus && <p class='red'>停用后，其下面的子分类都会被停用</p>}
                </a-form-item>
              </div>
            : <div>
                <div class='form-col-2'>
                  <a-form-item label='物料类别' required>
                    <CategroySelect
                      queryStatus= { screenForm.formStatus }
                      value={ categroyForm.categoryId }
                      onChange={ val => {
                        setCategroyForm(val, 'categoryId')
                      } }
                    />
                  </a-form-item>
                  <a-form-item label='物料形式' required>
                    <a-input
                      value={ categroyForm.formName }
                      onChange={ e => {
                        setCategroyForm(e.target.value, 'formName')
                      } }
                    />
                  </a-form-item>
                </div>
                <div class='form-col-2'>
                  <a-form-item label='单店最大陈列数' required>
                    <a-input-Number
                      value={ categroyForm.maxCount }
                      min={ 1 }
                      precision={ 0 }
                      onChange={ value => {
                        setCategroyForm(value, 'maxCount')
                      } }
                    />
                  </a-form-item>
                </div>
                <div class='form-col-2'>
                  <a-form-item label="图样">
                    <UploaderImg
                      maxLen={ 1 }
                      files={ categroyForm.fid }
                      onChangeImg={ file => {
                        setCategroyForm(file, 'fid')
                      } }
                    />
                  </a-form-item>
                  <a-form-item label='状态' >
                    <a-switch
                      checked-children="启用"
                      un-checked-children="停用"
                      checked={categroyForm.formStatus}
                      onChange={ () => {
                        setCategroyForm(!categroyForm.formStatus, 'formStatus')
                      } }
                    />
                  </a-form-item>
                </div>
            </div>
          }
        </a-form>
        <div class="modal-actions">
          <a-button
            class="margin-right"
            onClick={ () => { setCategroyFormIsShow(false) } }
            >
            取消
          </a-button>
          <a-button
            type="primary"
            onClick={ submitCategroy }
            >
            确认
          </a-button>
        </div>
      </a-modal>
      )
    },
  }
</script>
<style lang="scss" scoped>
  @import '../../common/style';

  .categroy-form{
    padding: 0 24px;
    max-height: 63vh;
    overflow-y: auto;
  }

  .ant-input-number{
    width: 100%;
  }
</style>
