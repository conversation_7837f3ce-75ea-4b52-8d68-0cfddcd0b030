<script lang="jsx">
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions'
  import uuid from 'uuid/v4'
  import PictureHoverPreview from '../../components/atom/picture-HoverPreview'
  import { NiTable } from '@jiuji/nine-ui'
  import ModalCategroyForm9ji from './modal-categroyForm-9ji'
  import ModalCategroyForm from './modal-categroyForm'
  import ImgAreaPreview from '~/components/uploader/img-area-preview'

  export default {
    name: 'categroy-table',
    components: {
      PictureHoverPreview,
      NiTable,
      ModalCategroyForm,
      ModalCategroyForm9ji,
      ImgAreaPreview
    },
    data () {
      return {
        columns: [
          {
            title: '物料类别',
            dataIndex: 'categoryName',
            key: 'categoryName',
            width: '20%',
            customRender: (text) => {
              return {
                children: <div style='position:sticky; top: 60px'><pre>{{ text }}</pre></div>,
                style: {
                  verticalAlign: 'top'
                },
              }
            }
          },
          {
            title: '单店最大陈列数',
            dataIndex: 'displayMaxnum',
            key: 'displayMaxnum',
            width: '14%',
            customRender: (_, categroyObj) => {
              const obj = {
                children: this.renderChildTable(categroyObj),
                attrs: {},
              }
              obj.attrs.colSpan = categroyObj.fromList.length ? 6 : 5
              return obj
            },
          },
          {
            title: '物料形式',
            dataIndex: 'materielShape',
            key: 'materielShape',
            width: '31%',
            customRender: this.renderNull
          },
          {
            title: '物料图样',
            dataIndex: 'imgDesign',
            key: 'imgDesign',
            width: '16.4%',
            customRender: this.renderNull
          },
          {
            title: '状态',
            dataIndex: 'enabled',
            key: 'enabled',
            width: '7%',
            customRender: this.renderNull
          },
          {
            title: '',
            key: 'catagroyAction',
            width: '4%',
            customRender: this.renderNull
          },
          {
            title: '操作',
            key: 'action',
            width: '7.6%',
            customRender: (text, catagroyObj) => {
              if (!catagroyObj.fromList.length) {
                return <span style="margin-left: 12px">
                        <a-button
                          type='link'
                          onClick={() => { this.editCategroy(catagroyObj) } }>
                          编辑
                        </a-button>
                      </span>
              } else {
                this.renderNull()
              }
            }
          }
        ],
      }
    },
    setup () {
      const {
        state,
      } = useState()
      const {
        editCategroy,
        handleTableChange
      } = useActions()

      return {
        state,
        editCategroy,
        handleTableChange
      }
    },

    render () {
      const {
        state,
        columns,
        handleTableChange
      } = this
      const is9ji = this.$tnt.xtenant === 0
      const formList = this.$store.state.operation.materialManage.formList
      return (
        <div>
          <NiTable
            class='material-categroy-table'
            rowKey= { () => uuid() }
            columns={ columns }
            dataSource={ formList}
            pagination={ state.pagination }
            onChange={ handleTableChange }
          >
            <template slot="action">
              <a-button onClick={ () => { this.editCategroy() } }>
                新增物料
              </a-button>
            </template>
          </NiTable>
          { state.categroyFormIsShow && is9ji ? <ModalCategroyForm9ji/> : <ModalCategroyForm/> }
          <img-area-preview showGps={false} ref="imgAreaPreview"></img-area-preview>
        </div>
      )
    },
    methods: {
      preview (img) {
        this.$refs.imgAreaPreview.previewImg({
          fileList: [img]
        })
      },
      renderNull () {
        const obj = {
          children: null,
          attrs: {},
        }
        obj.attrs.colSpan = 0
        return obj
      },
      renderChildTable (categroyObj) {
        const data = categroyObj.fromList
        const cloumns = [
          {
            title: '单店最大陈列数',
            dataIndex: 'maxCount',
            key: 'maxCount',
            width: '18%',
          },
          {
            title: '物料形式',
            dataIndex: 'formName',
            key: 'formName',
            width: '37%',
            customRender: (text) => {
              return <pre>{{ text }}</pre>
            }
          },
          {
            title: '物料图样',
            dataIndex: 'fid',
            key: 'fid',
            width: '20%',
            customRender: (text, record) => {
              if (!text?.length) return '-'
              return <div class='material-categroy-table-img-wrap'>
                <NiImg src={ text[0].filePath } width={ 99 } height={ 88 } onTap={() => { this.preview(text[0]) }} />
              </div>
            }
          },
          {
            title: '状态',
            dataIndex: 'formStatus',
            key: 'formStatus',
            width: '12%',
            customRender: (text, record) => (
              <span style="margin-left: -8px">
                {text ? '启用' : '停用'}
              </span>
            )
          },
          {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            width: '11%',
            customRender: (_, formObj) => {
              const obj = {
                children: <span>
                            <a-button
                              type='link'
                              onClick={() => { this.editCategroy(categroyObj, formObj) } }>
                              编辑
                            </a-button>
                          </span>,
                attrs: {},
              }
              obj.attrs.colSpan = 2
              return obj
            },
          },
          {
            dataIndex: 'emp',
            width: '0',
            customRender: this.renderNull
          }
        ]
        return (
          <a-table
            columns={ cloumns }
            dataSource={ data }
            showHeader={false}
            rowKey= { () => uuid() }
            pagination={false}
          />
        )
      }
    }
  }
</script>
<style lang="scss">
.material-categroy-table{
  @import '../../common/table';
  &.nine-table .ant-table-fixed-header .ant-table-scroll .ant-table-body{
    overflow-y: visible !important;
  }
}
.material-categroy-table-img-wrap{
  margin-left: -3px;
  position: relative;
  width: 80px;
  height: 60px;
  margin: -8px 0;
  padding-bottom: 30px;
  overflow: hidden;
  cursor: pointer;
  img{
    position: absolute;
    width: 100%;
  }
}
</style>
<style lang="scss" scoped>
  .material-categroy-table{
    .ant-table-wrapper{
      .ant-table-wrapper{
        margin: -12px -17px -13px !important;
      }
    }
  }

</style>
