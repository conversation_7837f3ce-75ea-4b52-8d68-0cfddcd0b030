<script type='text/jsx' lang="jsx">
  import { watch } from 'vue'
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions'

  import UploaderImg from '~/components/upload/uploaderImg-byNine'
  import CategroySelect from '../../components/select/categroy-vs-form/categroy'

  export default {
    name: 'modal-categroyForm',
    components: {
      UploaderImg,
      CategroySelect
    },
    setup () {
      const {
        state,
        setCategroyForm,
        setCategroyFormIsShow,
        setPriceList
      } = useState()
      const {
        submitCategroy9ji
      } = useActions()

      watch(
        () => state.categroyForm.maxCount,
        (val) => {
          let options = []
          const priceList = state.categroyForm.priceList
          const priceListLeng = state.categroyForm.priceList.length
          if (!priceListLeng) { // 无陈列数
            for (let i = 1; i < (val + 1); i++) {
              options.push({
                code: i,
                price: ''
              })
            }
          } else {
            options = priceList // 陈列数 = 标价数
            if (val < priceListLeng) {
              options = priceList.slice(0, val)
            } else {
              const addListNum = val - priceListLeng // 新增数
              for (let i = 1; i < (addListNum + 1); i++) {
                options.push({
                  code: priceListLeng + i,
                  price: ''
                })
              }
            }
          }
          setCategroyForm(options, 'priceList')
        },
        { immediate: true }
      )

      return {
        state,
        submitCategroy9ji,
        setCategroyFormIsShow,
        setCategroyForm,
        setPriceList
      }
    },
    render () {
      const {
        state: {
          screenForm,
          isAdd,
          categroyForm,
          categroyFormIsShow,
        },
        setPriceList,
        setCategroyForm,
        submitCategroy9ji,
        setCategroyFormIsShow,
      } = this
      const isFrom = categroyForm.type === 2
      const imgLabel = () => <span>图样 <i class='grey-9'>(需小于10MB)</i></span>

      return (
        <a-modal
          maskClosable = { false }
          title={ isAdd ? '新增物料' : '编辑物料' }
          visible={ categroyFormIsShow }
          footer={ null }
          onOk={ submitCategroy9ji }
          onCancel={ () => { setCategroyFormIsShow(false) } }
          destroyOnClose
        >
        <a-form class='categroy-form'>
          <a-form-item label={ isAdd ? '新增类型' : '编辑类型' }>
            <a-radio-group
              value={ categroyForm.type }
              onChange={ e => {
                setCategroyForm(e.target.value, 'type')
              } }
            >
              <a-radio value={ 1 }>物料类别</a-radio>
              <a-radio value={ 2 }>物料形式</a-radio>
            </a-radio-group>
          </a-form-item>

          { !isFrom
            ? <div>
                <a-form-item label='名称' required>
                  <a-input
                    value={ categroyForm.categoryName }
                    onChange={ e => {
                      setCategroyForm(e.target.value, 'categoryName')
                    } }
                  />
                </a-form-item>
                <a-form-item label='状态' >
                  <a-switch
                    checked-children="启用"
                    un-checked-children="停用"
                    checked={categroyForm.categoryStatus}
                    onChange={ () => {
                      setCategroyForm(!categroyForm.categoryStatus, 'categoryStatus')
                    } }
                  />
                  {!categroyForm.categoryStatus && <p class='red'>停用后，其下面的子分类都会被停用</p>}
                </a-form-item>
              </div>
            : <div>
                <div class='form-col-2'>
                  <a-form-item label='物料类别' required>
                    <CategroySelect
                      queryStatus= { screenForm.formStatus }
                      value={ categroyForm.categoryId }
                      onChange={ val => {
                        setCategroyForm(val, 'categoryId')
                      } }
                    />
                  </a-form-item>
                  <a-form-item label='物料形式' required>
                    <a-input
                      value={ categroyForm.formName }
                      onChange={ e => {
                        setCategroyForm(e.target.value, 'formName')
                      } }
                    />
                  </a-form-item>
                </div>
                <div class='form-col-2'>
                  <a-form-item label='单店最大陈列数' required>
                    <a-input-Number
                      value={ categroyForm.maxCount }
                      min={ 1 }
                      precision={ 0 }
                      onChange={ value => {
                        setCategroyForm(value, 'maxCount')
                      } }
                    />
                  </a-form-item>
                </div>
                <div class='form-col-2'>
                {
                  categroyForm.priceList?.length > 0 &&
                  categroyForm.priceList.map(item => (
                      <a-form-item label={ `${item.code}号陈列标价` }>
                        <a-input
                          value={ item.price }
                          onChange={ e => {
                            setPriceList(e.target.value, item.code)
                          } }
                        />
                      </a-form-item>
                  ))
                }
                </div>
                <div class='form-col-2'>
                  <a-form-item label={ imgLabel }>
                    <UploaderImg
                      maxLen={ 1 }
                      collection="oa-operate"
                      files={ categroyForm.fid }
                      isAreaPreview={true}
                      onChangeImg={ file => {
                        setCategroyForm(file, 'fid')
                      } }
                    />
                  </a-form-item>
                  <a-form-item label='状态' >
                    <a-switch
                      checked-children="启用"
                      un-checked-children="停用"
                      checked={categroyForm.formStatus}
                      onChange={ () => {
                        setCategroyForm(!categroyForm.formStatus, 'formStatus')
                      } }
                    />
                  </a-form-item>
                </div>
            </div>
          }
        </a-form>
        <div class="modal-actions">
          <a-button
            class="margin-right"
            onClick={ () => { setCategroyFormIsShow(false) } }
            >
            取消
          </a-button>
          <a-button
            type="primary"
            onClick={ submitCategroy9ji }
            >
            确认
          </a-button>
        </div>
      </a-modal>
      )
    },
  }
</script>
<style lang="scss" scoped>
  @import '../../common/style';

  .categroy-form{
    padding: 0 24px;
    max-height: 63vh;
    overflow-y: auto;
  }

  .ant-input-number{
    width: 100%;
  }
</style>
