
import { computed, getCurrentInstance } from 'vue'
import { message } from 'ant-design-vue'
import {
  FETCH_FORM_LIST,
  SUBMIT_CATEGORY,
  SUBMIT_FORM,
  SUBMIT_FORM_9JI
} from '@operation/store/modules/materialManage/action-types'
import { useState } from './useState.js'
import useCateVsFormActions from '../../components/select/categroy-vs-form/useActions'

export default function useActions () {
  const instance = getCurrentInstance().proxy
  const {
    state,
    setPagination,
    setIsAdd,
    setCategroyFormIsShow,
    setCategroyForm,
    resetCategroyForm
  } = useState()
  const { fetchCategroys, fetchFormOptions } = useCateVsFormActions()

  const fetchData = async (currentPage) => {
    if (currentPage) setPagination(currentPage, 'current')
    const {
      categoryId,
      formId,
      formStatus,
    } = state.screenForm
    const { current, pageSize } = state.pagination
    const params = { current, size: pageSize }
    if (categoryId) params.categoryId = categoryId
    if (formId?.length) {
      params.formIds = formId
      delete params.categoryId
    }
    if (formStatus !== null) params.formStatus = formStatus
    const isSuccess = await instance.$store.dispatch(
      `operation/materialManage/${FETCH_FORM_LIST}`,
      params
    )
    if (!isSuccess) return
    const total = computed(() => instance.$store.state.operation.materialManage.formTotal).value
    setPagination(total, 'total')
  }
  const handleTableChange = (pagination) => {
    setPagination(pagination)
    fetchData()
  }
  /**
   * @param { Object } categroyObj 类别记录
   * @param { Object } formObj 形式记录
   */
  const editCategroy = (categroyObj, formObj) => {
    // 没有记录为新增
    setIsAdd(!categroyObj)
    setCategroyFormIsShow(true)
    let recordNew = {
      ...state.categroyFormReset,
      ...categroyObj,
      ...formObj
    }
    if (!formObj) recordNew.categoryId = +recordNew.categoryId // 类别下没有形式时的处理
    // number 转 boolean
    recordNew.categoryStatus = !!recordNew.categoryStatus
    recordNew.formStatus = !!recordNew.formStatus
    delete recordNew.fromList

    const newForm = state.isAdd ? { ...state.categroyFormReset } : recordNew
    setCategroyForm(newForm)
  }
  /**
   * 类别与形式的编辑与修改
   * 物料类别 submitCategory | 物料形式 submitForm
   */
  const submitCategroy9ji = async () => {
    const {
      type,
      categoryName,
      formName,
      categoryStatus,
      formStatus,
      formId,
      categoryId,
      maxCount,
      priceList,
      fid,
    } = state.categroyForm
    const isFrom = type === 2
    if (isFrom) {
      // 形式校验
      if (!categoryId) {
        message.error('请选择类别')
        return
      }
      if (!formName) {
        message.error('请填写形式名称')
        return
      }
      if (!maxCount) {
        message.error('请填写单店陈列最大数')
        return
      }
    } else {
      // 类别校验
      if (!categoryName) {
        message.error('请填写类别名称')
        return
      }
    }
    // 转化参数：name,status
    const payload = {
      name: isFrom ? formName : categoryName,
      status: isFrom ? formStatus : categoryStatus
    }
    // 形式与类别区分上传参数
    if (isFrom) {
      if (formId) payload.id = formId
      payload.categoryId = categoryId
      payload.maxCount = maxCount
      payload.priceList = priceList
      // 后端需要字符串，图片上传组件需要对象数组。新增状态的图片为含有两个属性的对象，编辑状态已转换成对象数组
      if (fid) {
        if (fid instanceof Array) {
          if (fid.length) payload.fid = fid[0]?.filePath
        } else {
          if (fid.fileList.length) payload.fid = fid.fileList[0]?.response?.data?.filePath
        }
      }
    } else {
      if (categoryId) payload.id = categoryId
    }
    // boolean 转 number
    payload.status = payload.status ? 1 : 0
    const SUBMIT_TYPE = isFrom ? SUBMIT_FORM_9JI : SUBMIT_CATEGORY
    const isSuccess = await instance.$store.dispatch(
      `operation/materialManage/${SUBMIT_TYPE}`,
      payload
    )
    if (!isSuccess) return
    setCategroyFormIsShow(false)
    resetCategroyForm()
    // 刷新筛选区【物料类别】或 【物料形式】options
    isFrom ? fetchFormOptions(categoryId) : fetchCategroys()
    fetchData()
  }
  const submitCategroy = async () => {
    const {
      type,
      categoryName,
      formName,
      categoryStatus,
      formStatus,
      formId,
      categoryId,
      maxCount,
      priceList,
      fid,
    } = state.categroyForm
    const isFrom = type === 2
    if (isFrom) {
      // 形式校验
      if (!categoryId) {
        message.error('请选择类别')
        return
      }
      if (!formName) {
        message.error('请填写形式名称')
        return
      }
      if (!maxCount) {
        message.error('请填写单店陈列最大数')
        return
      }
    } else {
      // 类别校验
      if (!categoryName) {
        message.error('请填写类别名称')
        return
      }
    }
    // 转化参数：name,status
    const payload = {
      name: isFrom ? formName : categoryName,
      status: isFrom ? formStatus : categoryStatus
    }
    // 形式与类别区分上传参数
    if (isFrom) {
      if (formId) payload.id = formId
      payload.categoryId = categoryId
      payload.maxCount = maxCount
      payload.priceList = priceList
      // 后端需要字符串，图片上传组件需要对象数组。新增状态的图片为含有两个属性的对象，编辑状态已转换成对象数组
      if (fid) {
        if (fid instanceof Array) {
          if (fid.length) payload.fid = fid[0]?.filePath
        } else {
          if (fid.fileList.length) payload.fid = fid.fileList[0]?.response?.data?.filePath
        }
      }
    } else {
      if (categoryId) payload.id = categoryId
    }
    // boolean 转 number
    payload.status = payload.status ? 1 : 0
    const SUBMIT_TYPE = isFrom ? SUBMIT_FORM : SUBMIT_CATEGORY
    const isSuccess = await instance.$store.dispatch(
      `operation/materialManage/${SUBMIT_TYPE}`,
      payload
    )
    if (!isSuccess) return
    setCategroyFormIsShow(false)
    resetCategroyForm()
    // 刷新筛选区【物料类别】或 【物料形式】options
    isFrom ? fetchFormOptions(categoryId) : fetchCategroys()
    fetchData()
  }

  return {
    fetchData,
    handleTableChange,
    editCategroy,
    submitCategroy,
    submitCategroy9ji
  }
}
