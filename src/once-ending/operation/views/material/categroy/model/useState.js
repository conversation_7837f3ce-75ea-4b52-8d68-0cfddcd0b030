import { reactive, inject } from 'vue'
import { cloneDeep } from 'lodash'

const key = Symbol('materialCategroy')

export function useState () {
  return inject(key)
}

export function createState (provide) {
  const state = reactive({
    screenFormReset: null, // 重置数据
    screenForm: {
      categoryId: undefined,
      formId: [],
      formStatus: undefined,
    },
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      hideOnSinglePage: true,
      pageSizeOptions: ['10', '20', '50', '100'],
      showSizeChanger: true,
      showTotal: total => `总共 ${total} 条`,
    },

    isAdd: true,
    categroyFormIsShow: false,
    categroyFormReset: null,
    categroyForm: {
      // 判断是类别还是形式用，不提交
      type: 1,
      categoryName: '',
      formName: '',
      categoryStatus: true,
      formStatus: true,

      // 类别字段
      id: null, // *
      // 形式字段（包含类别）
      maxCount: null, // *
      priceList: [], // 填写最大陈列数生成的标价字段集
      fid: [],
      categoryId: null, // *
    }
  })
  const setObj = objKey => (val, key) => {
    if (key && typeof key === 'string') {
      state[objKey][key] = val
    } else {
      state[objKey] = val
    }
  }
  const setScreenForm = setObj('screenForm') // 筛选参数相关
  const initScreenFormReset = () => { state.screenFormReset = cloneDeep(state.screenForm) }
  const resetScreenForm = () => { state.screenFormReset && setScreenForm(cloneDeep(state.screenFormReset)) }
  const setPagination = setObj('pagination')

  const setCategroyForm = setObj('categroyForm')
  const initCategroyFormReset = () => { state.categroyFormReset = cloneDeep(state.categroyForm) } // 设置表单初始值
  const resetCategroyForm = () => { state.categroyFormReset && setCategroyForm(cloneDeep(state.categroyFormReset)) }

  const setIsAdd = val => { state.isAdd = val }
  const setPriceList = (val, code) => {
    const index = state.categroyForm.priceList.findIndex(item => item.code === code)
    state.categroyForm.priceList[index].price = val
  }
  const setCategroyFormIsShow = val => { state.categroyFormIsShow = val }

  const materialCategroy = {
    state,
    setScreenForm,
    initScreenFormReset,
    resetScreenForm,
    setPagination,
    setCategroyForm,
    initCategroyFormReset,
    resetCategroyForm,
    setIsAdd,
    setCategroyFormIsShow,
    setPriceList
  }
  provide(key, materialCategroy)
  return materialCategroy
}
