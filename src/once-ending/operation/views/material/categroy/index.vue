<script type='text/jsx' lang="jsx">
  import { provide } from 'vue'
  import { createState } from './model/useState.js'

  import PageLayout from '@operation/components/layout'
  import BtnRouter from '../components/atom/btn-router'
  import Screen from './components/screen'
  import CategroyTable from './components/table'
  import { NiListPage } from '@jiuji/nine-ui'

  export default {
    name: 'categroy-manage',
    components: {
      PageLayout,
      Screen,
      CategroyTable,
      BtnRouter,
      NiListPage
    },
    setup () {
      createState(provide)
    },
    render () {
      const oAuthManage = this.$store.state.userInfo.Rank?.includes('wlgl')

      return (
        <page>
        <template slot="extra">
          { oAuthManage &&
            <span>
              <BtnRouter target="_blank" routerName="OPR_MaterialManage" text="物料陈列管理" class="mr-16"/>
              <BtnRouter target="_blank" routerName="OPR_MaterialPicture" text="物料画面管理" class="mr-16"/>
            </span>
          }
        </template>
        <NiListPage push-filter-to-location={ false }>
          <Screen class='mb-16'/>
          <CategroyTable/>
          </NiListPage>
        </page>
      )
    },
  }
</script>
<style lang="scss" scoped>
@import '../common/style';
</style>
