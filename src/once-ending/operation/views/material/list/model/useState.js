import { reactive, inject, provide, getCurrentInstance } from 'vue'
import { cloneDeep } from 'lodash'

const key = Symbol('materialList')

export function useState () {
  return inject(key)
}

export function createState () {
  const { proxy } = getCurrentInstance()
  const state = reactive({
    isFeatch: false,
    isFuzzyQuery: true, // 是否模糊查询
    queryParams: {}, // 存储请求的参数(导出表格时用)
    screenFormReset: null, // 重置数据
    screenForm: {
      // 需要处理的参数
      otherType: 0,
      otherValue: undefined,
      timeType: 0,
      timeValue: null,
      nameSelect: undefined,
      areaIds: [],
      categoryId: undefined,
      formId: [],
      codes: '', // 需转化codeList
      statusList: [1, 2, 3],
      zones: undefined,
      aiAudits: [],
      timeoutTypes: [],
      auditUserId: undefined
    },
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      pageSizeOptions: ['10', '20', '50', '100'],
      showSizeChanger: true,
      showTotal: total => `总共 ${total} 条`,
    },

    isBatch: false,
    selectedRowKeys: [],
    selectedRows: [],
    selectedIds: [],
    selectedAreas: [],
    selectedRowsStatus: [], // 选中的项操作状态
    actionOptionsReset: undefined, // actionOptions的初始状态
    actionOptions: [
      { label: '审核', value: '1', disabled: true },
      { label: '删除', value: '2', disabled: false },
      { label: '转待反馈', value: '3', disabled: true },
      // { label: '转待盘点', value: '4', disabled: true },
    ],
    // 批量操作
    auditFormReset: null,
    auditForm: {
      actionType: '2', // 删除
      auditType: '1', // 通过
      remark: '',
      ids: null,
      pass: true,
      endTime: null
    },
    // 盘点
    checkFormReset: null,
    checkForm: {
      areaIds: [],
      categoryId: undefined,
      formId: [],
      code: undefined,
      name: '',
      endTime: null,
    },
    curRecord: undefined, // 当前操作项
    editForm: { // 编辑
      areaCode: undefined,
      areaId: undefined,
      categoryName: undefined,
      code: undefined,
      description: undefined,
      enclosureFid: undefined,
      endTime: undefined,
      expireTime: undefined,
      formId: undefined,
      formName: undefined,
      frameId: undefined,
      height: undefined,
      id: undefined,
      name: undefined,
      pandianTime: undefined,
      smallArea: undefined,
      standardAttachments: [],
      status: undefined,
      uploadFids: undefined,
      uploadTime: undefined,
      width: undefined,
    },
    editFormReset: undefined,
    // 反馈图片
    uploadForm: {
      uploadFiles: [],
    },
    modalBatchIsShow: false, // 批量操作
    modalCheckIsShow: false, // 盘点
    modalEditStoreDisplayIsShow: false, // 编辑
    modalUploadPictureIsShow: false, // 反馈图片
    modalLogsIsShow: false, // 日志
  })
  const { name, nameJson } = proxy.$route.query
  // 因为name业务使用过程中会存在很多特殊符号,如果直接放参数,特殊符号会被转义,导致查询不准确,所以得先加密下name
  // 但是为啥不把老的name取参去掉呢,为了兼容下历史,前端代码已经排查了所有的跳转,怕有其他的跳转,例如推送,所以先把name留着,如果后续其他跳转有问题,再让他们适配下
  // 老的
  if (name) {
    state.screenForm.otherValue = { label: name, key: name }
    state.isFuzzyQuery = false
  }
  // 新的
  if (nameJson) {
    const nameObj = JSON.parse(nameJson)
    state.screenForm.otherValue = { label: nameObj.name, key: nameObj.name }
    state.isFuzzyQuery = false
  }
  const setObj = objKey => (val, key) => {
    if (key && typeof key === 'string') {
      state[objKey][key] = val
    } else {
      state[objKey] = val
    }
  }
  const setScreenForm = setObj('screenForm') // 筛选参数相关
  const initScreenFormReset = () => { state.screenFormReset = cloneDeep(state.screenForm) }
  const resetScreenForm = () => { state.screenFormReset && setScreenForm(cloneDeep(state.screenFormReset)) }
  const setQueryParams = val => { state.queryParams = val }
  const setPagination = setObj('pagination')

  const setAuditForm = setObj('auditForm') // 批量操作相关
  const initAuditFormReset = () => { state.auditFormReset = cloneDeep(state.auditForm) } // 设置表单初始值
  const resetAuditForm = () => { state.auditFormReset && setAuditForm(cloneDeep(state.auditFormReset)) }
  const setActionOptions = (val, value) => {
    const index = state.actionOptions.findIndex(it => it.value === value)
    state.actionOptions[index].disabled = val
  }
  const initActionOptionsReset = () => { state.actionOptionsReset = cloneDeep(state.actionOptions) }
  const resetActionOptions = () => {
    if (!state.actionOptionsReset) return
    state.actionOptions = cloneDeep(state.actionOptionsReset)
  }

  const setIsFeatch = val => { state.isFeatch = val }
  const setIsBatch = val => { state.isBatch = val }
  const setSelectedRowsStatus = val => { state.selectedRowsStatus = val || [] }
  const setSelectedRowKeys = val => { state.selectedRowKeys = val || [] }
  const setSelectedRows = val => { state.selectedRows = val || [] }
  const setSelectedIds = val => { state.selectedIds = val || [] }
  const setSelectedAreas = val => { state.selectedAreas = val || [] }

  const setcheckForm = setObj('checkForm') // 盘点相关
  const initCheckFormReset = () => { state.checkFormReset = cloneDeep(state.checkForm) }
  const resetCheckForm = () => { state.checkFormReset && setcheckForm(cloneDeep(state.checkFormReset)) }

  const setCurRecord = val => { state.curRecord = val } // 当前操作项
  const setEditForm = setObj('editForm') // 编辑
  const initEditFormReset = () => { state.editFormReset = cloneDeep(state.editForm) } // 设置表单初始值
  const resetEditForm = () => { state.editFormReset && setEditForm(cloneDeep(state.editFormReset)) }

  const setUploadForm = setObj('uploadForm') // 反馈图片
  const setModal = (modalType) => (val, curRecord) => {
    state[modalType] = val
    setCurRecord(curRecord) // 打开传当前值模态框，设置当前操作项，关闭重置undefind
  }
  const setModalUploadPictureIsShow = setModal('modalUploadPictureIsShow')
  const setModalLogsIsShow = setModal('modalLogsIsShow')
  const setModalBatchIsShow = setModal('modalBatchIsShow')
  const setModalCheckIsShow = setModal('modalCheckIsShow')
  const setModalEditStoreDisplayIsShow = setModal('modalEditStoreDisplayIsShow')

  const materialList = {
    state,
    setIsFeatch,
    setCurRecord,
    setEditForm,
    setModalEditStoreDisplayIsShow,
    setUploadForm,
    setModalUploadPictureIsShow,

    setScreenForm,
    initScreenFormReset,
    resetScreenForm,

    setAuditForm,
    initAuditFormReset,
    resetAuditForm,
    setActionOptions,
    initActionOptionsReset,
    resetActionOptions,

    setcheckForm,
    initCheckFormReset,
    resetCheckForm,

    initEditFormReset,
    resetEditForm,

    setPagination,
    setQueryParams,
    setModalCheckIsShow,
    setModalBatchIsShow,
    setIsBatch,
    setSelectedRowsStatus,
    setSelectedRowKeys,
    setSelectedRows,
    setSelectedIds,
    setSelectedAreas,
    setModalLogsIsShow
  }
  provide(key, materialList)
  return materialList
}
