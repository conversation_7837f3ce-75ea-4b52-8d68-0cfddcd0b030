
import { computed, getCurrentInstance } from 'vue'
import { message } from 'ant-design-vue'
import { saveAs } from 'file-saver'
import moment from 'moment'
import {
  FETCH_CATEGROY_OPTIONS,
  FETCH_FORM_OPTIONS,
  FETCH_MATERIAL_LIST,
} from '@operation/store/modules/materialManage/action-types'
import {
  // FETCH_START,
  SET_MATERIAL_LIST
} from '@operation/store/modules/materialManage/mutation-types'
import { useState } from '../useState.js'

import { dictionary } from '../../../common/data.js'

export default function useActions () {
  const instance = getCurrentInstance().proxy
  const {
    state,
    setIsFeatch,
    setSelectedRowKeys,
    setSelectedRows,
    setSelectedIds,
    setSelectedAreas,

    initScreenFormReset,
    initAuditFormReset,
    initActionOptionsReset,
    initCheckFormReset,
    initEditFormReset,
    setScreenForm,
    setQueryParams,
    setPagination,
  } = useState()

  const oAuthManage = computed(() => instance.$store.state.userInfo.Rank?.includes('wlgl'))
  const userAreaId = computed(() => instance.$store.state.userInfo.areaid)
  const fetchData = async (currentPage) => {
    // 清空选择
    setSelectedRowKeys()
    setSelectedIds()
    setSelectedAreas()
    if (currentPage) setPagination(currentPage, 'current')

    const {
      areaIds,
      categoryId,
      formId,
      codes,
      statusList,

      otherType,
      otherValue,
      timeType,
      timeValue,
      zones,
      aiAudits,
      timeoutTypes,
      auditUserId
    } = state.screenForm
    const { current, pageSize } = state.pagination
    const params = { current, size: pageSize, isFuzzyQuery: state.isFuzzyQuery }
    if (areaIds?.length) {
      params.areaIds = areaIds
    } else if (!oAuthManage.value && userAreaId.value !== 22) {
      // 有物料管理权限和HQ的人，可清空区域 areaIds（表示可查全部数据）
      // 反之若清空区域则中断查询
      message.info('暂无有权限门店的数据，请选择门店')
      instance.$store.commit(
        `operation/materialManage/${SET_MATERIAL_LIST}`,
        {
          records: [],
          total: 0
        }
      )
      return
    }
    if (categoryId) params.categoryId = categoryId
    if (auditUserId) params.auditUserId = auditUserId
    if (statusList?.length) params.statusList = statusList
    if (aiAudits?.length) params.aiAudits = aiAudits
    if (timeoutTypes?.length) params.timeoutTypes = timeoutTypes
    if (zones?.length) params.zones = zones
    // 处理 formId 与 categoryId 不能同时传
    if (formId?.length) {
      params.formIds = formId
      delete params.categoryId
    }
    if (codes) {
      const codesTostr = codes.replace(/[\uff0c]/g, ',')
      params.codeList = codesTostr.split(',').map(item => (Math.abs(item)))
    }
    // 处理 检索类型
    if (otherValue) {
      const otherTypes = dictionary.otherTypes.map(item => item.key)
      if (otherType === 0) { // name
        params.name = otherValue.label
      } else {
        params[otherTypes[otherType]] = otherValue
      }
    }
    // 处理时间类型
    if (timeValue?.length) {
      const timeTypes = dictionary.timeTypes.map(item => item.key)
      params[`${timeTypes[timeType]}Start`] = moment(timeValue[0]).format('yyyy-MM-DD') + ' 00:00:00'
      params[`${timeTypes[timeType]}End`] = moment(timeValue[1]).format('yyyy-MM-DD') + ' 23:59:59'
    }

    setQueryParams(params) // 用于导出数据时的筛选参数
    const isSuccess = await instance.$store.dispatch(
      `operation/materialManage/${FETCH_MATERIAL_LIST}`,
      params
    )
    if (!isSuccess) return
    setIsFeatch(true)
    const materialTotal = computed(() => instance.$store.state.operation.materialManage.materialTotal).value

    setPagination(materialTotal, 'total')
  }
  const handleTableChange = (pagination) => {
    setPagination(pagination)
    fetchData()
  }
  const showAllText = (text) => {
    const h = instance.$createElement
    instance.$info({
      content: <div>{text}</div>,
      maskClosable: true,
      width: 720,
      icon: () => null,
    })
  }
  // 批量操作
  const handleSelectedRows = (selectedRowKeys, selectedRows) => {
    setSelectedRowKeys(selectedRowKeys)
    setSelectedRows(selectedRows)
    const selectedIds = selectedRows.map(item => item.id)
    setSelectedIds(selectedIds)
    const selectedAreas = selectedRows.map(item => item.areaCode)
    setSelectedAreas(selectedAreas)
  }
  // 下载附件
  const downLoadPicture = (record) => {
    const { enclosureAttachments = [] } = record
    enclosureAttachments?.forEach(img => {
      saveAs(img.filePath, img.fileName)
      message.loading('正在下载，请稍等...')
    })
  }

  const pageInit = () => {
    initScreenFormReset()
    initAuditFormReset()
    initActionOptionsReset()
    initCheckFormReset()
    initEditFormReset()
    console.log('state', state)
    instance.$store.dispatch(`operation/materialManage/${FETCH_CATEGROY_OPTIONS}`)

    // 处理路由参数及权限
    const {
      categoryId = null, formId = null, codeId = null, materialName = ''
    } = instance.$route.query
    if (categoryId) {
      instance.$store.dispatch(`operation/materialManage/${FETCH_FORM_OPTIONS}`, { categoryId })
      setScreenForm(+categoryId, 'categoryId')
    }
    if (formId) {
      const formIdVar = typeof formId === 'number' ? [formId] : formId.split(',').map(d => Number(d))
      setScreenForm(formIdVar, 'formId')
    }
    if (codeId) setScreenForm(codeId + '', 'codes')
    if (materialName) {
      setScreenForm(0, 'otherType') // 物料名称
      const name = { key: 0, label: materialName } // 转成option对象
      setScreenForm(name, 'otherValue')
    }
    // 所查询到的数据控制：除管理员与HQ人员外,根据人员所属门店，查询数据
    if (!oAuthManage.value && userAreaId.value !== 22) {
      const areaIds = [userAreaId.value + '']
      setScreenForm(areaIds, 'areaIds')
    }

    fetchData()
  }
  function changeVal () {
    state.isFuzzyQuery = true
  }
  return {
    fetchData,
    handleTableChange,
    showAllText,
    downLoadPicture,
    handleSelectedRows,
    pageInit,
    changeVal
  }
}
