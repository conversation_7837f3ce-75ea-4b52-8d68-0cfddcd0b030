
import { getCurrentInstance, computed } from 'vue'
import { message } from 'ant-design-vue'
import moment from 'moment'
import {
  FETCH_MATERIAL_DETAIL,
  SUBMIT_MANAGER_EDIT
} from '@operation/store/modules/materialManage/action-types'

import { useActions } from './index.js'
import { useState } from '../useState.js'

/**
 * 编辑相关
 */
export default function useEdit () {
  const instance = getCurrentInstance().proxy
  const {
    state,
    setEditForm,
    resetEditForm,
    setModalEditStoreDisplayIsShow,
  } = useState()
  const {
    fetchData
  } = useActions()

  const fetchDetail = async (id) => {
    if (!id) return
    const isSuccess = await instance.$store.dispatch(
      `operation/materialManage/${FETCH_MATERIAL_DETAIL}`,
      { id }
    )
    if (!isSuccess) return
    const detail = computed(() => instance.$store.state.operation.materialManage.materialDetail)
    setEditForm({ ...detail.value })
  }
  const submitStoreDisplay = async () => {
    const {
      name,
      standardAttachments,
      endTime,
    } = state.editForm
    if (!name) {
      message.error('请输入物料名称')
      return
    }
    if (!standardAttachments.length) {
      message.error('请上传陈列标准')
      return
    }
    if (!endTime) {
      message.error('请选择反馈截止时间')
      return
    }
    const paramName = typeof name === 'string' ? name : name.name
    const params = {
      ...state.editForm,
      name: paramName,
      standardAttachments: standardAttachments && standardAttachments.length ? standardAttachments.map(item => {
        return { fid: item.fid, fileName: item.fileName, filePath: item.fileUrl || item.filePath }
      }) : [],
      endTime: moment(endTime).format('yyyy-MM-DD HH:mm:ss'),
    }
    // 后端返回的字段，但是只有无需陈列才需要使用这个字段
    delete params.notNeedFlag
    // 已更换附件字段，后端需要兼容老数据，前端删除此字段的提交（fix:编辑时，删除附件，只删除了新字段的附件，老字段还会提交附件）
    delete params.enclosureFid
    delete params.standardFid
    if (params.expireTime) params.expireTime = moment(params.expireTime).format('yyyy-MM-DD') + ' 23:59:59'
    if (params.width && !params.height) params.width = null
    if (!params.width && params.height) params.height = null

    const isSuccess = await instance.$store.dispatch(
      `operation/materialManage/${SUBMIT_MANAGER_EDIT}`,
      params
    )
    if (!isSuccess) return
    setModalEditStoreDisplayIsShow(false)
    resetEditForm()
    fetchData()
  }
  return {
    fetchDetail,
    submitStoreDisplay
  }
}
