
import { getCurrentInstance } from 'vue'
import { message } from 'ant-design-vue'
import moment from 'moment'
import {
  REMOVE_DISPLAY_PICTURE,

  TO_WAIT_FEEDBACK,
  TO_WAIT_INVENTORY,
  SUBMIT_AUDIT,
} from '@operation/store/modules/materialManage/action-types'

import { useActions } from './index.js'
import { useState } from '../useState.js'

/**
 * 批量操作
 */
export default function useBatch () {
  const instance = getCurrentInstance().proxy
  const {
    state,
    setSelectedRowKeys,
    setSelectedRows,
    setSelectedRowsStatus,
    setSelectedIds,
    setSelectedAreas,
    setModalBatchIsShow,
    setIsBatch,

    setAuditForm,
    resetAuditForm,
    resetActionOptions,
  } = useState()
  const { fetchData } = useActions()

  // 操作模态框为单项与批量共用
  // 【批量操作】此函数为批量入口，仅用在(管理人员点击【批量操作】按钮)时用
  const handleBatchAudit = () => {
    if (!state.selectedIds.length) {
      message.info('请选择至少一个门店的物料')
      return
    }
    setModalBatchIsShow(true)
    setIsBatch(true) // 批量：关闭模态框重设为否
  }
  /**
   * 审核: 【批量 or 单项】不传 ids的为批量(表格内单条记录操作)
   * @params {boolean} isPass:[false:不通过, true]
   * @params {array[Number]} ids
   */
  const handleAudit = (isPass, record) => {
    const { id, areaCode } = record
    const ids = record ? [id] : state.selectedIds
    setAuditForm(ids, 'ids')
    setSelectedAreas([areaCode])
    if (!isPass) {
      // 不通过需要填写理由,
      setAuditForm('1', 'actionType') // 审核
      setAuditForm('2', 'auditType') // 不通过
      setModalBatchIsShow(true)
      return
    }
    // 通过
    setAuditForm('1', 'auditType')
    handleSubmitAudit()
  }
  // 提交审核
  const handleSubmitAudit = async () => {
    const {
      remark,
      auditType,
      ids
    } = state.auditForm
    // 1: 通过 2:不通过
    setAuditForm(auditType === '1', 'pass')
    // 审核 && 不通过
    if (!state.auditForm.pass) {
      if (!remark) {
        message.error('请填写不通过理由')
        return
      }
    } else {
      setAuditForm('', 'remark') // 通过，置空理由
    }
    const params = {
      remark,
      ids: ids || state.selectedIds,
      pass: state.auditForm.pass,
    }
    const isSuccess = await instance.$store.dispatch(
      `operation/materialManage/${SUBMIT_AUDIT}`,
      params
    )
    if (!isSuccess) return
    resetAuditForm()
    closeModalBatch()
    fetchData()
  }
  // 关闭清空表单
  const closeModalBatch = () => {
    setModalBatchIsShow(false)
    setIsBatch(false)
    resetAuditForm()
    resetActionOptions()
  }
  /**
   * 删除陈列: 【批量 or 单项】不传ids的为批量
   * @params {array[Number]} ids
   */
  const handleRemoveDisplay = async (ids) => {
    const { selectedIds } = state
    const params = ids?.length ? ids : selectedIds
    const isSuccess = await instance.$store.dispatch(
      `operation/materialManage/${REMOVE_DISPLAY_PICTURE}`,
      params
    )
    if (!isSuccess) return
    closeModalBatch()
    fetchData()
  }

  // 【单项操作】转待反馈与待盘点（需重新填写反馈日期，打开批量操作模态框）
  const handleToFeedback = (id, row, status) => {
    // actionType 3：转待反馈 4：转待盘点
    const actionType = '3'
    setSelectedRowKeys() // 点击单条记录,清空多选项
    setAuditForm(actionType, 'actionType')
    setSelectedIds([id])
    setSelectedRows([row]) // 当前操作项
    setSelectedAreas([row.areaCode])
    setSelectedRowsStatus([status])
    setModalBatchIsShow(true)
  }
  // 提交转待反馈
  const handleToWaitFeedback = async () => {
    const {
      selectedIds,
      auditForm: { endTime }
    } = state
    if (!endTime) {
      message.error('请选择反馈截止时间')
      return
    }
    const params = {
      endTime: moment(endTime).format('YYYY-MM-DD HH:mm:ss'),
      ids: selectedIds
    }
    const isSuccess = await instance.$store.dispatch(
      `operation/materialManage/${TO_WAIT_FEEDBACK}`,
      params
    )
    if (!isSuccess) return
    closeModalBatch()
    fetchData()
  }

  return {
    handleBatchAudit,
    handleAudit,
    handleSubmitAudit,
    closeModalBatch,
    handleRemoveDisplay,
    handleToFeedback,
    handleToWaitFeedback
  }
}
