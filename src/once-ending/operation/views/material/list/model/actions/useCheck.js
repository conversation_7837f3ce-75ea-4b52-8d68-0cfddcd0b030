
import { getCurrentInstance } from 'vue'
import { message } from 'ant-design-vue'
import moment from 'moment'
import {
  SUBMIT_CHECK,
} from '@operation/store/modules/materialManage/action-types'

import { useState } from '../useState.js'
import { useActions } from './index.js'

export default function useCheck () {
  const instance = getCurrentInstance().proxy
  const {
    state,
    setModalCheckIsShow,
    resetCheckForm
  } = useState()
  const {
    fetchData
  } = useActions()

  // 盘点
  const handleSubmitCheck = async () => {
    const {
      categoryId,
      formId,
      endTime,
      areaIds,
    } = state.checkForm
    if (!areaIds.length) {
      message.error('请选择地区')
      return
    }
    if (!categoryId) {
      message.error('请选择物料类别')
      return
    }
    if (!formId?.length) {
      message.error('请选择物料形式')
      return
    }
    if (!endTime) {
      message.error('请选择盘点时间')
      return
    }
    const params = { ...state.checkForm, formIds: formId }
    if (endTime) params.endTime = moment(endTime).format('yyyy-MM-DD HH:mm:ss')
    if (formId?.length) delete params.categoryId // 类别与形式只传其一
    const res = await instance.$store.dispatch(
      `operation/materialManage/${SUBMIT_CHECK}`,
      params
    )
    if (!res) return
    setModalCheckIsShow(false)
    resetCheckForm()
    fetchData()
  }

  return {
    handleSubmitCheck
  }
}
