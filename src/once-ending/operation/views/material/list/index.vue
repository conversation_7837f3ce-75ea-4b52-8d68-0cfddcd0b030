<script type="text/javascript" lang="jsx">
  import { getCurrentInstance } from 'vue'
  import { createState } from './model/useState.js'

  import BtnRouter from '../components/atom/btn-router'
  import Screen from './components/screen'
  import DisplayTable from './components/table'
  import { NiListPage } from '@jiuji/nine-ui'

  export default {
    components: {
      Screen,
      DisplayTable,
      BtnRouter,
      NiListPage
    },
    setup (_) {
      const root = getCurrentInstance().proxy
      const { $tnt, $route } = root
      document.title = $tnt.xtenant < 1000 ? '物料陈列' : '物料陈列管理'
      $route.meta.title = $tnt.xtenant < 1000 ? '物料陈列' : '物料陈列管理'
      createState()
    },
    render () {
      const oAuthManage = this.$store.state.userInfo.Rank?.includes('wlgl')
      return (
        <page>
          <template slot="extra">
            { oAuthManage &&
              <span>
                <BtnRouter target="_blank" routerName="OPR_MaterialPicture" text="物料画面管理" class="mr-16"/>
                <BtnRouter target="_blank" routerName="OPR_MaterialCategroy" text="物料类别管理" class="mr-16"/>
              </span>
            }
          </template>
          <NiListPage push-filter-to-location={ false }>
            <Screen class='mb-16'/>
            <DisplayTable/>
          </NiListPage>
        </page>
      )
    }
  }
</script>
<style lang='scss' scoped>
  @import '../common/style';
</style>
