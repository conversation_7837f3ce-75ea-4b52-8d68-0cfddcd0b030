<script type='text/jsx' lang="jsx">
  import Vue, { computed, getCurrentInstance, ref } from 'vue'
  import { useState } from '../model/useState.js'
  import { useBatch, useActions } from '../model/actions'

  import { NiTable, NiImg } from '@jiuji/nine-ui'
  import NoData from '@operation/views/statistics-report/components/no-data.vue'
  import ExcelAction from '~/pages/operation/components/excel-action'
  import RenderStatus from '../../components/atom/render-status'
  import PictureHoverPreview from '../../components/atom/picture-HoverPreview'
  import ModalLogs from '../../components/logs/modal-logs'
  import RenderStatusAction from './renderStatusAction.jsx'
  import ModalCheck from './modal-check'
  import ModalBatch from './modal-batch'
  import ModalEditStoreDisplay from './modal-editStoreDisplay'
  import 'viewerjs/dist/viewer.css'
  import Viewer from 'v-viewer'
  import ImgAreaPreview from '~/components/uploader/img-area-preview'
  Vue.use(Viewer)

  export default {
    name: 'display-table',
    components: {
      RenderStatus,
      ExcelAction,
      PictureHoverPreview,
      RenderStatusAction,
      NiTable,
      NiImg,
      NoData,
      ModalCheck,
      ModalBatch,
      ModalLogs,
      ModalEditStoreDisplay,
      ImgAreaPreview
    },
    data () {
      return {
        columns9ji: [
          {
            dataIndex: 'areaCode',
            key: 'areaCode',
            title: '门店',
            width: 70,
          },
          {
            dataIndex: 'categoryName',
            key: 'categoryName',
            title: '物料类别',
            width: 85,
            customRender: (text) => <pre>{ text }</pre>
          },
          {
            dataIndex: 'formName',
            key: 'formName',
            title: '物料形式',
            width: 85,
            customRender: (text) => <pre>{ text }</pre>
          },
          {
            dataIndex: 'code',
            key: 'code',
            title: '物料编号',
            width: 80
          },
          {
            dataIndex: 'price',
            key: 'price',
            title: '陈列标价',
            width: 80,
            show9ji: true,
          },
          {
            dataIndex: 'name',
            key: 'name',
            title: '物料名称',
            width: 100,
            customRender: (text) => <pre>{ text }</pre>
          },
          {
            dataIndex: 'printMethodName',
            key: 'printMethodName',
            title: '物料印刷方式',
            width: 100,
            show9ji: true,
            customRender: (text) => <span>{ text || '-' }</span>
          },
          {
            dataIndex: 'standardAttachments',
            key: 'standardAttachments',
            title: '门店陈列标准',
            width: 260,
            // customRender: text => <PictureHoverPreview src={text}/>
            customRender: (text, record, index) => {
              return <div class="imageview-tmd-wrap">
                {
                  text && text.length ? <div class="imageview">
                    {
                      text.map((item, index) => {
                        return <div class="flex flex-align-center flex-justify-center mr-8 pointer" style="width: 100px; height: 100px;">
                          <NiImg src={item.filePath} alt="" width="100" height="100" alt="" onTap={() => this.viewImages(text, index, false)}/>
                        </div>
                      })
                    }
                  </div> : ''
                }
              </div>
            }
          },
          {
            dataIndex: 'uploadFids',
            key: 'uploadFids',
            title: '门店当前陈列',
            width: 260,
            customRender: (text) => {
              if (!text?.length) return '-'
              return (
                // <div class='uploadFids'>
                //   {
                //     text.map(item => <PictureHoverPreview src={item.filePath} alt='门店当前陈列'/>)
                //   }
                // </div>
                <div class="imageview-tmd-wrap">
                  {
                    text && text.length ? <div class="imageview">
                      {
                        text.map((item, index) => {
                          return <div class="flex flex-align-center flex-justify-center mr-8 pointer" style="width: 100px; height: 100px;">
                            <NiImg src={item.filePath} alt="" width="100" height="100" onTap={() => this.viewImages(text, index)}/>
                          </div>
                        })
                      }
                    </div> : ''
                  }
                </div>
              )
            }
          },
          {
            dataIndex: 'feedbackDesc',
            key: 'feedbackDesc',
            title: '陈列反馈说明',
            width: 320,
            customRender: (text) => {
              // if (!text?.length) return '-'
              return (<div>
                {
                  text && text.length > 40 ? <a-popover overlayClassName="displayPoperLayTable">
                    <template slot="content">
                    <div>{{ text }}</div>
                    </template>
                    <span class="pointer">{text.substring(0, 40) + '...'}</span>
                  </a-popover> : <span>{text}</span>
                }
              </div>)
            }
          },
          {
            dataIndex: 'withdrawalFeedbackFullPath',
            key: 'withdrawalFeedbackFullPath',
            title: '物料撤除反馈',
            show9ji: true,
            width: 260,
            // customRender: (text) => {
            //   if (!text) return '-'
            //   return (
            //     <div class='uploadFids'>
            //       <PictureHoverPreview src={text} alt='物料撤除反馈'/>
            //     </div>
            //   )
            // }
            customRender: (text, record, index) => {
              return <div class="imageview-tmd-wrap">
                {
                  text ? <div class="imageview">
                    <div class="flex flex-align-center flex-justify-center mr-8 pointer"
                         style="width: 100px; height: 100px;">
                      <NiImg src={text} alt="" width="100" height="100" onTap={() => this.viewImagesOne(record, 0)}/>
                    </div>
                  </div> : ''
                }
              </div>
            }
          },
          {
            dataIndex: 'description',
            key: 'description',
            title: '门店陈列说明',
            width: 180,
            customRender: (text, record) => {
              return <div class="text-wrap">
                <div class='text'>
                {
                  text.length > 40 &&
                  <button class="btn-showAll" onClick={ () => { this.showAllText(text) } }> 全部 </button>
                }
                  { text }
                </div>
              </div>
            }
          },
          {
            dataIndex: 'standardContent',
            key: 'standardContent',
            title: '文案标准',
            width: 180,
            customRender: text => <i class='pre-line'> {text} </i>
          },
          {
            dataIndex: 'status',
            key: 'status',
            title: '状态',
            width: 75,
            customRender: text => text ? <RenderStatus value={text}/> : '--'
          },
          {
            dataIndex: 'aiAudit',
            key: 'aiAudit',
            title: 'AI审核',
            width: 75,
            customRender: text => text ? <RenderStatus value={text} type={'aiAudit'}/> : '--'
          },
          {
            dataIndex: 'matchPercent',
            key: 'matchPercent',
            title: 'AI匹配度',
            width: 75,
            show9ji: true,
            customRender: text => text || text === 0 ? `${text}%` : '--'
          },
          {
            dataIndex: 'timeoutType',
            key: 'timeoutType',
            title: '超时情况',
            width: 75,
            customRender: text => text ? <RenderStatus value={text} type={'timeout'}/> : '--'
          },
          {
            dataIndex: 'materielAttr',
            key: 'materielAttr',
            title: '属性',
            width: 460,
            class: 'attr',
            customRender: (text, record) => {
              return (
                <ul class='atrr-list'>
                  <li>
                    <i>物料陈列ID</i> <span>{ record.id }</span>
                  </li>
                  <li>
                    <i>地区</i> <span>{ record.smallArea || '-' }</span>
                  </li>
                  <li>
                    <i>物料尺寸</i> <span>{ record.width || '-' }{record.width ? '*' : null}{ record.height || '-' }{this.getBan(record)}</span>
                  </li>
                  <li>
                    <i>盘点时间</i>
                    <a-tooltip>
                      <template slot="title">
                        { record.pandianTime }
                      </template>
                      <span>{ record.pandianTime?.slice(0, 10) || '-' }</span>
                    </a-tooltip>
                  </li>
                  <li>
                    <i>反馈截止时间</i>
                    <a-tooltip>
                      <template slot="title">
                        { record.endTime }
                      </template>
                      <span>{ record.endTime?.slice(0, 10) || '-' }</span>
                    </a-tooltip>
                  </li>
                  <li>
                    <i>物料过期时间</i>
                    <a-tooltip>
                      <template slot="title">
                        { record.expireTime }
                      </template>
                      <span>{ record.expireTime?.slice(0, 10) || '-' }</span>
                    </a-tooltip>
                  </li>
                </ul>
              )
            }
          },
          {
            dataIndex: 'actions',
            key: 'actions',
            title: '操作',
            fixed: 'right',
            width: 96,
            class: 'p-0',
            customRender: (_, record) => <RenderStatusAction record={record}/>
          },
        ],
      }
    },
    setup (_) {
      const { $viewerApi, $tnt } = getCurrentInstance().proxy
      const { handleBatchAudit } = useBatch()
      const {
        handleSelectedRows,
        handleTableChange,
        showAllText,
      } = useActions()
      const {
        state,
        setModalCheckIsShow,
        setModalLogsIsShow
      } = useState()

      // 批量审核
      const handleSelectChange = (selectedRowKeys, selectedRows) => {
        handleSelectedRows(selectedRowKeys, selectedRows)
      }

      function getBan (editForm) {
        const { width, height } = editForm
        return width > height ? '(横版)' : width < height ? '(竖版)' : null
      }

      const viewPicturesArr = (pictureUrlStr) => {
        return pictureUrlStr.length ? pictureUrlStr.map(item => item.filePath) : []
      }

      const imgAreaPreview = ref(null)

      const viewImages = (pictureUrl, index, showGps = true) => {
        // $viewerApi({
        //   images: viewPicturesArr(pictureUrl)
        // }).view(index)
        imgAreaPreview.value.previewImg({
          fileList: pictureUrl,
          index,
          showGps
        })
      }
      const viewImagesOne = (record) => {
        if ($tnt.xtenant < 1000) {
          const { withdrawalFeedbackFid, withdrawalFeedbackFullPath } = record
          imgAreaPreview.value.previewImg({
            fileList: [{
              fid: withdrawalFeedbackFid,
              filePath: withdrawalFeedbackFullPath
            }]
          })
        } else {
          $viewerApi({
            images: [record.withdrawalFeedbackFullPath]
          })
        }
      }

      return {
        state,
        rowSelection: computed(() => ({
          selectedRowKeys: state.selectedRowKeys,
          onChange: handleSelectChange,
        })),

        showAllText,
        setModalCheckIsShow,
        setModalLogsIsShow,
        handleBatchAudit,
        handleTableChange,
        getBan,
        viewPicturesArr,
        viewImages,
        viewImagesOne,
        imgAreaPreview
      }
    },
    render () {
      const {
        state,
        setModalLogsIsShow,
        setModalCheckIsShow,
        rowSelection,
        columns9ji,
        handleBatchAudit,
        handleTableChange
      } = this
      const {
        curRecord,
        modalLogsIsShow,
        modalBatchIsShow,
        modalCheckIsShow,
        modalEditStoreDisplayIsShow
      } = state
      const isMaterialManager = this.$store.state.userInfo.Rank?.includes('wlgl')
      const isLoading = this.$store.state.operation.materialManage.isLoading
      const materialDataList = this.$store.state.operation.materialManage.materialList
      const columns = columns9ji.filter(item => !item.show9ji)
      const jiujiColumns = columns9ji.filter(item => item.dataIndex !== 'standardContent')
      const columnsCur = this.$tnt.xtenant === 0 ? jiujiColumns : columns

      return (
        <div class="material-list-table">
          <NiTable
            tableBarFixed
            locale={{ emptyText: <NoData isFeatch={ state.isFeatch }/> }}
            rowKey={ (record, index) => index }
            row-selection={ rowSelection }
            columns={ columnsCur }
            loading={ isLoading }
            dataSource={ materialDataList }
            pagination={ state.pagination }
            onChange={ handleTableChange }
          >
            { isMaterialManager && <a-button slot='tool' class="mr-8" onClick={ handleBatchAudit }>批量操作</a-button> }
            <div slot="action" class='flex'>
              <ExcelAction
                class="excel-btn mr-8"
                btnText="导出"
                params={ state.queryParams }
                scene="material_manager"
                exportTableName="店面物料"
              />
              { isMaterialManager && <a-button onClick={ () => { setModalCheckIsShow(true) } }>盘点</a-button> }
            </div>
          </NiTable>
          {
            modalLogsIsShow && <ModalLogs
              showAddLogs
              visible={ modalLogsIsShow }
              recordId={ curRecord?.id }
              scene= { 'materialLogs' }
              handleCancel={ () => { setModalLogsIsShow(false) } }
            />
          }
          { modalBatchIsShow && <ModalBatch/> }
          { modalCheckIsShow && <ModalCheck/> }
          { modalEditStoreDisplayIsShow && <ModalEditStoreDisplay recordId={ curRecord?.id }/> }
          <img-area-preview showGps={false} ref="imgAreaPreview"></img-area-preview>
        </div>
      )
    }
  }
</script>
<style lang="scss">
.material-list-table{
  @import '../../common/table';
}
.imageview-tmd-wrap {
  width: 212px;
  height: 110px;
  overflow: hidden;
  &:hover{
    overflow-x: auto;
  }
  &::-webkit-scrollbar {
    width: 16px;
    height: 6px;
  }
  .imageview {
    display: flex;
    img{
      display: block;
      width: 100px;
      height: 100px;
      border-radius: 4px;
    }
  }
}
</style>
<style lang="scss" scoped>
  :deep(.text-wrap) {
    display: flex;
    overflow: hidden;

    .text{
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: justify;
      display: -webkit-box;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
      position: relative;

      &::before{
        content: '';
        float: right;
        width: 0;
        height: 100%;
        margin-bottom: -21px;
      }

      .btn-showAll{
        float: right;
        clear: both;
        line-height: 21px;
        cursor: pointer;
        border:0;
        color: #31a6ff;
        background: none;
      }
    }
  }
  :deep(.atrr-list) {
    display: flex;
    flex-wrap: wrap;
    margin: -12px -9px;
    i,span{
      display: inline-block;
      width: 100px;
      flex: 1;
      padding: 8px 12px;
    }

    li{
      width: 50%;
      display: flex;
      border-bottom: 1px solid #eaeaea;
      i {
        border-right: 1px solid #eaeaea;
        background: #fafafa;
      }
      &:nth-child(odd){
        border-left: 1px solid #eaeaea;
        > span {
          border-right: 1px solid #eaeaea;
        }
      }
      &:nth-child(even){
        border-right: 1px solid #eaeaea;
      }
      &:nth-child(5),
      &:nth-child(6){
        border-bottom: none
      }
    }
  }
  :deep(.mx-10) {
    margin-left: -10px;
    margin-right: -10px;
  }
  :deep(.uploadFids) {
    max-width: 300px;
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
    height: 60px;
    &::-webkit-scrollbar{
      height: 4px;
      display: none;
    }
    &:hover{
      &::-webkit-scrollbar{
        display: block;
      }
    }
    .img-wrap{
      margin-right: 5px;
      flex-shrink: 0;
    }
  }
  :deep(.viewer-container) {
    border: 50px solid red !important;
  }
</style>
<style lang="scss">
.displayPoperLayTable > .ant-popover-content > .ant-popover-inner {
  width: 500px;
}
</style>
