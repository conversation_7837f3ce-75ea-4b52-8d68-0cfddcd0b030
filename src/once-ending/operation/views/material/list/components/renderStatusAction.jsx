import { computed } from 'vue'
import { useState } from '../model/useState.js'
import { useBatch, useActions } from '../model/actions'
import BtnA from '../../components/atom/btn-a'

/**
  * PRD：https://jiuji.yuque.com/docs/share/b9f74fdd-e62a-47e2-9073-128daac49abb?
  * 状态：[1待反馈、2待审核 、3已反馈、4未反馈、5已过期、6已删除]
  * 操作按钮：[1.查看日志、2.编辑、3.上传画面、4.下载画面、5.审核通过、6.审核不通过]
  *         查看日志：[全部门 + 全状态]
  * 总部： [审核（2）、编辑(1、2、3、4、6)]
  * 门店： [下载(1)、上传(1、4)]
*/

export default (ctx) => {
  const { props: { record }, parent } = ctx
  const {
    setModalLogsIsShow,
    setModalEditStoreDisplayIsShow
  } = useState()
  const {
    handleToFeedback,
    handleAudit,
    handleRemoveDisplay
  } = useBatch()
  const {
    downLoadPicture
  } = useActions()

  const showLogs = () => {
    setModalLogsIsShow(true, record)
  }

  // 总部:roleMaterialManager normal:其它人员
  const isMaterialManager = computed(() => parent.$store.getters.userInfo.Rank?.includes('wlgl')).value
  const role = isMaterialManager ? 'roleMaterialManager' : 'normal'
  const status = record.status
  const actionType = {
    roleMaterialManager: () => (
      <div>
        { status === 2 && (
          <div>
            <BtnA text="审核通过" actions={ () => { handleAudit(true, record) } }/>
            <BtnA text="审核不通过" actions={ () => { handleAudit(false, record) } }/>
          </div>
        ) }
        { status === 3 && (
          <BtnA
            text='转待反馈'
            actions={ () => { handleToFeedback(record.id, record, status) } }
          />
        ) }
        <div>
          <BtnA text="编辑" actions={ () => {
            setModalEditStoreDisplayIsShow(true, record)
          } }/>
          <a-popconfirm
            title="确定删除陈列?"
            onConfirm={ () => handleRemoveDisplay([record.id]) }
          >
            <BtnA text="删除"/>
          </a-popconfirm>
        </div>
      </div>
    ),
    normal: () => (
      <div>
        { !!record.enclosureAttachments?.length && <BtnA text="画面下载" actions={ () => downLoadPicture(record) }/> }
        {
          [1, 2, 7].includes(status) && (
            <a-tooltip>
              <template slot="title">
                <span>只能在手机端操作，请进入手机端OA进行相关操作</span>
              </template>
              <i class='grey-9'>{ (status === 1 || status === 2) ? '上传陈列图' : '上传盘点图' }</i>
            </a-tooltip>
          )
        }
      </div>
    )
  }
  const roleOprations = actionType[role]()
  return (
    <div style='margin: -12px -8px'>
      <BtnA text="查看日志" actions={ showLogs }/>
      { roleOprations }
    </div>
  )
}
