<script type="text/jsx" lang="jsx">
  import { computed } from 'vue'
  import { useState } from '../model/useState.js'
  import { useCheck } from '../model/actions'

  import AreaDepartSelector from '~/components/staff/area-depart-selector-cache'
  import TimePicker from '../../components/atom/time-picker'
  import CategroyVsFormSelect from '../../components/select/categroy-vs-form'
  import CodeSelect from '../../components/select/code'
  export default {
    name: 'ModalChecks',
    components: {
      AreaDepartSelector,
      CategroyVsFormSelect,
      CodeSelect,
      TimePicker
    },
    setup (_) {
      const {
        state,
        setcheckForm,
        resetCheckForm,
        setModalCheckIsShow
      } = useState()
      const {
        handleSubmitCheck
      } = useCheck()

      const onChangeCvsF = ({ categoryId, formId }) => {
        setcheckForm(categoryId, 'categoryId')
        setcheckForm(formId, 'formId')
      }
      const closeModel = () => {
        setModalCheckIsShow(false)
        resetCheckForm()
      }

      return {
        visible: computed(() => state.modalCheckIsShow),
        checkForm: computed(() => state.checkForm),

        setcheckForm,
        closeModel,
        handleSubmitCheck,
        onChangeCvsF,
      }
    },
    render () {
      const {
        visible,
        checkForm,

        setcheckForm,
        closeModel,
        handleSubmitCheck,
        onChangeCvsF,
      } = this
      const title = () => (
        <div>
          <span class="font-20">盘点</span>
          <small class="grey-9 ml-16">仅待反馈、已反馈状态下支持盘点,其它状态不支持</small>
        </div>
      )
      return (
        <a-modal
          class="modal-check"
          maskClosable = { false }
          title= { title }
          visible={ visible }
          width={ 480 }
          onOk={ handleSubmitCheck }
          onCancel={ closeModel }
        >
          <a-form wrapper-col={{ span: 24 }}>
            <a-form-item label="地区" required>
              <AreaDepartSelector
                treeCheckable
                placeholder="请选择陈列区域或门店"
                showType="SHOW_CHILD"
                treeNodeFilterProp="label"
                onChange={ ids => { setcheckForm(ids, 'areaIds') }}
              />
            </a-form-item>
            <CategroyVsFormSelect
              displayBlock
              value={{
                categoryId: checkForm.categoryId,
                formId: checkForm.formId
              }}
              onChange={ onChangeCvsF }
            />
            <a-form-item label="物料编号">
              <CodeSelect
                value={ checkForm.code }
                formId={ checkForm.formId }
                onChange={ value => { setcheckForm(value, 'code') }}
              />
            </a-form-item>
            <a-form-item label="物料名称">
              <a-input
                placeholder="请输入物料名称"
                value={ checkForm.name }
                onChange={e => {
                  setcheckForm(e.target.value, 'name')
                }}
              />
            </a-form-item>
            <a-form-item label="盘点截止时间" required>
              <TimePicker form={checkForm}/>
            </a-form-item>
          </a-form>
        </a-modal>
      )
    },
  }
</script>

<style lang="scss">
  .modal-check{
    .ant-calendar-picker{
      width: 100%;
    }
  }
</style>
