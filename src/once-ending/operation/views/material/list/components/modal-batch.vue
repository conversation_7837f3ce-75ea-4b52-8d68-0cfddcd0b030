<script type="text/jsx" lang="jsx">
  import { computed, onMounted, watch, getCurrentInstance } from 'vue'
  import { useState } from '../model/useState.js'
  import { useBatch } from '../model/actions'

  import AreaDepartSelector from '~/components/staff/area-depart-selector-cache'

  const auditOptions = [
    { label: '通过', value: '1' },
    { label: '不通过', value: '2' },
  ]
  export default {
    name: 'ModalBatch',
    components: {
      AreaDepartSelector,
    },
    setup (_) {
      const { proxy } = getCurrentInstance()
      const {
        state,
        setAuditForm,
        setActionOptions,
        setSelectedRowsStatus,
      } = useState()
      const {
        closeModalBatch,
        handleSubmitAudit,
        handleRemoveDisplay,
        handleToWaitFeedback
      } = useBatch()

      /**
        * 删除：状态均可以；
        * 审核：2待审核状态
        * 转待反馈：4未反馈状态
        * 转待盘点：8未盘点状态
      */
      const handleOk = () => {
        const submitType = new Map([
          ['1', handleSubmitAudit], // 审核
          ['3', handleToWaitFeedback], // 转待反馈
        ])
        const { actionType } = state.auditForm
        if (!actionType) {
          proxy.$message.info('你所选的操作门店含有已删除项，无可对应的操作项，请重新选择')
          return
        }
        submitType.get(actionType)()
      }
      const init = () => {
        const recordStatus = state.selectedRows.map(item => item.status)
        setSelectedRowsStatus(recordStatus)

        // 单项操作：转待反馈-->actionType【1.审核不通过，3.转待反馈】
        if (!state.isBatch) {
          const { actionType } = state.auditForm
          // 单项操作：[删除]与[审核通过]可在列表内，因此删除项转为disabled
          setActionOptions(true, '2') // 删除
          // 如'转待反馈-->', state.actionOptions[2].disabled
          setActionOptions(false, actionType)
        }
      }

      onMounted(() => {
        init()
      })
      watch(
        () => state.selectedRowsStatus,
        val => {
          // watch所选择项的状态，设置【批量操作】模态框打开的disable及操作类型
          if (state.isBatch) {
            const setType = (val) => {
              setActionOptions(false, val)
              setAuditForm(val, 'actionType')
            }
            const canAudit = val.every(item => item === 2) // 每项都是待审核状态
            const canToWaitFeedback = val.every(item => item === 3) // 3:已反馈可转待反馈
            if (canAudit) setType('1')
            if (canToWaitFeedback) setType('3')
          }
        }
      )

      return {
        visible: computed(() => state.modalBatchIsShow),
        auditForm: computed(() => state.auditForm),
        selectedAreas: computed(() => state.selectedAreas || []),
        actionOptions: computed(() => state.actionOptions || []),
        modalTitle: computed(() => state.isBatch ? '批量操作' : '物料陈列操作'),
        idsLabel: computed(() => state.isBatch ? '批量操作门店' : '操作门店'),

        setAuditForm,
        closeModalBatch,
        handleOk,
        handleRemoveDisplay
      }
    },
    render () {
      const {
        visible,
        auditForm,
        selectedAreas,
        actionOptions,
        modalTitle,
        idsLabel,

        setAuditForm,
        closeModalBatch,
        handleOk,
        handleRemoveDisplay
      } = this
      return (
        <a-modal
          class="modal-audit"
          maskClosable = { false }
          width={ 600 }
          footer={ false }
          zIndex={ 10 }
          visible={ visible }
          title={ modalTitle }
          onCancel={ closeModalBatch }
        >
          <a-form label-col={{ span: 5 }} wrapper-col={{ span: 17 }}>
            <a-form-item label={ idsLabel }>
              { selectedAreas.map(item => <span class='mr-16'>{item}</span>) }
            </a-form-item>
            <a-form-item label="操作">
              <a-radio-group name="radioGroup"
                value={ auditForm.actionType }
                onChange={ e => { setAuditForm(e.target.value, 'actionType') }}
              >
                {
                  actionOptions.map(item => (
                    <a-radio value={ item.value } disabled={ item.disabled }>{ item.label }</a-radio>
                  ))
                }
              </a-radio-group>
            </a-form-item>
            {
              // 转待反馈||待盘点，需填时间
              (auditForm.actionType === '3' || auditForm.actionType === '4') &&
              <a-form-item label={auditForm.actionType === '3' ? '反馈截止时间' : '盘点截止时间' } required>
                <a-date-picker
                  showTime
                  placeholder="选择时间"
                  v-model={ auditForm.endTime }
                />
              </a-form-item>
            }
            {
              // 审核不通过，需填理由
              auditForm.actionType === '1' && <div>
                <a-form-item class="no-label">
                  <a-radio-group
                    value={ auditForm.auditType }
                    onChange={ e => { setAuditForm(e.target.value, 'auditType') }}
                  >
                    {
                      auditOptions.map(item => (
                        <a-radio value={ item.value }>{ item.label }</a-radio>
                      ))
                    }
                  </a-radio-group>
                </a-form-item>
                {
                  auditForm.auditType === '2' && (
                    <a-form-item label="填写不通过理由">
                      <div class="area-wrap">
                        <a-textarea
                          value={ auditForm.remark }
                          rows={4}
                          maxLength={ 500 }
                          onChange={ e => { setAuditForm(e.target.value, 'remark') }}
                        />
                        <i class='text-tips'>{ auditForm.remark.length }/500</i>
                      </div>
                    </a-form-item>)
                }
              </div>
            }
            <div class="text-right">
              <a-button class="mr-8" onClick={ closeModalBatch }> 取消 </a-button>
              {
                auditForm.actionType !== '2'
                ? <a-button type="primary" onClick={ handleOk }> 确认 </a-button>
                : <a-popconfirm placement="top" onConfirm={ handleRemoveDisplay }>
                    <template slot="title">
                      <p>确认删除陈列</p>
                    </template>
                    <a-button type="primary"> 确认 </a-button>
                  </a-popconfirm>
              }
            </div>
          </a-form>
        </a-modal>
      )
    },
  }
</script>

<style lang="scss" scoped>
  :deep(.ant-form-item-label) {
    line-height: 40px;
  }
  .no-label{
    padding-left: 20.83%;
  }
  .area-wrap{
    position: relative;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding-bottom: 1.5em;

    textarea{
      border: none;
    }
    .text-tips {
      position: absolute;
      bottom: 0;
      right: 6px;
      line-height: 1.45;
      color: red;
    }
  }
</style>
