<template>
  <a-modal
    :maskClosable="false"
    title="编辑门店陈列"
    :width='588'
    :visible="visible"
    centered
    :footer="false"
    @cancel="handleCancel"
  >
    <a-form>
      <div class="form-col-2">
        <ul>
          <li>门店：{{editForm.areaCode}}</li>
          <li>物料类别：{{editForm.categoryName}}</li>
          <li>物料形式：{{editForm.formName}}</li>
        </ul>

        <a-form-item label="物料编号">
          <CodeSelect
            v-model="editForm.code"
            :formId="editForm.formId"
            @change="handleChangeCode"
          />
        </a-form-item>
        <a-form-item label="物料名称" required>
          <NameSelect
            v-if="editForm.formId"
            @change="handleChangeName"
            :form="editForm"/>
        </a-form-item>
        <a-form-item label="物料尺寸">
          <div class="flex">
            <a-input-number
              placeholder="宽（cm）"
              v-model="editForm.width"
            />
            <div :style="{ width:'24px', textAlign:'center', flex:'none'}"> X </div>
            <a-input-number
              placeholder="高（cm）"
              v-model="editForm.height"
            />
          </div>
        </a-form-item>

        <a-form-item label="物料过期时间">
          <a-date-picker
            format='YYYY-MM-DD'
            value-format="YYYY-MM-DD"
            v-model="editForm.expireTime"/>
        </a-form-item>
        <a-form-item label="画面附件">
          <FileUploader
            class="material-upload"
            :showQrBtn="false"
            :moreAmount="false"
            :disabled="enclosureDisabled"
            :files.sync="editForm.enclosureAttachments"
            @change="file => editForm.enclosureAttachments = file"
          />
        </a-form-item>
      </div>

      <a-form-item label="陈列标准说明">
        <a-textarea
          placeholder="请输入陈列标准说明"
          v-model="editForm.description"
          :rows="2"/>
      </a-form-item>
      <div class="form-col-2">
        <a-form-item label="反馈截止时间" required>
          <a-date-picker
            showTime
            format='YYYY-MM-DD HH:mm:ss'
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择时间"
            v-model="editForm.endTime"/>
        </a-form-item>
      </div>
      <div>
        <a-form-item label="陈列标准" required>
          <UploaderImg
            :multiple="true"
            :maxLen="20"
            collection="oa-operate"
            :is-area-preview="true"
            @changeImg="tmdFileChange"
            :files.sync="editForm.standardAttachments"/>
        </a-form-item>
      </div>
    </a-form>
    <div class="modal-actions">
      <a-button
        class="margin-right"
        @click="handleCancel"
        >取消</a-button>
      <a-button
        type="primary"
        @click="submitStoreDisplay"
        >确认</a-button>
    </div>
  </a-modal>
</template>

<script type="text/jsx">
  import { computed, onMounted } from 'vue'
  import { useState } from '../model/useState.js'
  import { useEdit } from '../model/actions'

  import CustomUploader from '~/components/custom-uploader'
  import UploaderImg from '../../picture/components/uploaderImg-byNine.vue'
  import FileUploader from '../../components/uploader.vue'
  import CodeSelect from '../../components/select/code'
  import NameSelect from '../../components/select/name'
  import BtnRouter from '../../components/atom/btn-router'

  export default {
    components: {
      FileUploader,
      CustomUploader,
      UploaderImg,
      CodeSelect,
      NameSelect,
      BtnRouter
    },
    props: {
      recordId: {
        type: Number
      },
    },
    setup (props) {
      const stateObj = useState()
      const {
        state,
        setEditForm,
        resetEditForm,
        setModalEditStoreDisplayIsShow
      } = stateObj
      const {
        fetchDetail,
        submitStoreDisplay
      } = useEdit(stateObj)

      /**
       * 改变物料名称，带出陈列标准图
       * @param selected { Objec } 对象{ fid， filePath， fileName }
       */
      const handleChangeName = (selected) => {
        // const urls = selected.standardFid.split('/')
        // const standard = [{
        //   fid: selected.standardFid,
        //   filePath: selected.standardFid,
        //   fileName: urls[urls.length - 1]
        // }]
        const standard = selected.standardAttachments
        handleChangeStandard(standard)
        setEditForm(selected.frameId, 'frameId')
      }
      const handleChangeCode = val => {
        setEditForm(val, 'code')
      }
      /**
       * @param files { Array } 图片对象数组{ fid， filePath， fileName }
       */
      const handleChangeStandard = (files) => {
        setEditForm(files, 'standardAttachments')
      }
      const tmdFileChange = (files) => {
        state.editForm.standardAttachments = files
      }
      const handleCancel = () => {
        setModalEditStoreDisplayIsShow(false)
        resetEditForm()
      }

      onMounted(() => {
        fetchDetail(props.recordId)
      })

      return {
        enclosureDisabled: computed(() => state.editForm.enclosureAttachments?.length > 2),
        visible: computed(() => state.modalEditStoreDisplayIsShow),
        editForm: computed(() => state.editForm),

        handleChangeName,
        handleChangeCode,
        handleChangeStandard,
        tmdFileChange,
        handleCancel,
        submitStoreDisplay,
      }
    }
  }
</script>

<style lang="scss" scoped>
// .percent::after{
//   content: '%';
// }
:deep(.material-upload){
  .upload-btn{
    width: 100%;
  }
}
a.link{
  position: absolute;
  margin-top: -12px;
  font-size: 12px;
}
.uploader{
  margin-top: -16px;
  width: 50%;
}
.block-title{
  font-size: 1.2em;
  font-weight: 600;
  margin: 0.8em 0;
}
.form-col-2{
  ul{
    width: 100%;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    li{
      margin-bottom: 16px;
      width: 45%;
    }
  }
}
:deep(.ant-form-item-label){
  line-height: 1.7;
}
.ant-form-item{
  margin-bottom: 16px;
}
.ant-input-number,
.ant-calendar-picker{
  width: 100%;
}
</style>
