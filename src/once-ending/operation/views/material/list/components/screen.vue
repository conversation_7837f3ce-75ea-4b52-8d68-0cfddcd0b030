
<script type='text/jsx' lang="jsx">
  import { computed, watch, onMounted } from 'vue'
  import { useState } from '../model/useState.js'
  import { useActions } from '../model/actions'
  import { dictionary, statusOpt } from '../../common/data.js'
  import mbaSelect from '@operation/components/mba-select'
  import { NiFilter, NiFilterItem, NiAreaSelect, NiStaffSelect } from '@jiuji/nine-ui'
  import CategroyVsFormSelect from '../../components/select/categroy-vs-form'
  import SearchSelectAsync from '../../components/select/search-async'

  export default {
    name: 'check-screen',
    components: {
      CategroyVsFormSelect,
      SearchSelectAsync,
      NiFilter,
      NiFilterItem,
      NiAreaSelect,
      mbaSelect,
      NiStaffSelect
    },
    setup (_) {
      const stateObj = useState()
      const {
        state,
        setScreenForm,
        setModalCheckIsShow
      } = stateObj
      const { fetchData, pageInit, changeVal } = useActions()

      const onChangeCvsF = ({ categoryId, formId }) => {
        setScreenForm(categoryId, 'categoryId')
        setScreenForm(formId, 'formId')
      }
      const doSearch = () => {
        fetchData(1)
      }
      onMounted(() => {
        pageInit()
      })

      watch(
        () => state.screenForm.otherType,
        () => { setScreenForm(undefined, 'otherValue') }
      )
      return {
        statusOpt,
        queryParams: computed(() => state.queryParams),
        screenForm: computed(() => state.screenForm),
        setScreenForm,
        setModalCheckIsShow,

        doSearch,
        onChangeCvsF,
        changeVal
      }
    },
    render () {
      const {
        statusOpt,
        screenForm,
        setScreenForm,
        doSearch,
        onChangeCvsF,
        changeVal
      } = this
      const isLoading = this.$store.state.operation.materialManage.isLoading
      return (
        <NiFilter
          class="relative"
          layout='neat'
          form={screenForm}
          loading={isLoading}
          onFilter={doSearch}
          immediate={false}
        >
          <ni-filter-item label="地区">
            <NiAreaSelect
              multiple
              allowClear
              mode={2}
              placeholder="请选择地区或者搜索"
              maxTagCount={1}
              value={screenForm.areaIds}
              onChange={ids => {
                setScreenForm(ids, 'areaIds')
              }}
            />
          </ni-filter-item>
          <ni-filter-item label='物料状态'>
            <a-select
              allowClear
              mode="multiple"
              maxTagCount={1}
              placeholder="物料状态"
              options={statusOpt}
              value={screenForm.statusList}
              onChange={(value) => {
                setScreenForm(value, 'statusList')
              }}
            />
          </ni-filter-item>
          <ni-filter-item label='AI审核'>
            <a-select
              allowClear
              mode="multiple"
              maxTagCount={1}
              placeholder="AI审核"
              options={dictionary.aiAudits}
              value={screenForm.aiAudits}
              onChange={(value) => {
                setScreenForm(value, 'aiAudits')
              }}
            />
          </ni-filter-item>
          <ni-filter-item class='no-label'>
            <CategroyVsFormSelect
              value={{
                categoryId: screenForm.categoryId,
                formId: screenForm.formId
              }}
              onChange={onChangeCvsF}/>
          </ni-filter-item>
          <ni-filter-item label='物料编号'>
            <a-tooltip placement="top">
              <template slot="title">
                <span>输入正整数，用逗号隔开</span>
              </template>
              <a-input
                allowClear
                placeholder="物料编号"
                value={screenForm.codes}
                onChange={(e) => {
                  setScreenForm(e.target.value, 'codes')
                }}
              />
            </a-tooltip>
          </ni-filter-item>
          <ni-filter-item class='no-label'>
            <a-input-group compact>
              <a-select
                options={dictionary.otherTypes}
                value={screenForm.otherType}
                onChange={(value) => {
                  setScreenForm(value, 'otherType')
                }}
              />
              {
                screenForm.otherType === 0
                  ? <SearchSelectAsync
                    onChangeVal={changeVal}
                    field='otherValue'
                    form={screenForm}
                  />
                  : <a-input
                    placeholder="输入关键词"
                    value={screenForm.otherValue}
                    onChange={(e) => {
                      setScreenForm(e.target.value, 'otherValue')
                    }}
                  />
              }
            </a-input-group>
          </ni-filter-item>
          <ni-filter-item class='no-label'>
            <a-input-group compact>
              <a-select
                style="width: 35%"
                options={dictionary.timeTypes}
                value={screenForm.timeType}
                onChange={(value) => {
                  setScreenForm(value, 'timeType')
                }}
              />
              <a-range-picker
                allowClear
                style="width: 65%"
                value={screenForm.timeValue}
                onChange={(value) => {
                  setScreenForm(value, 'timeValue')
                }}
              />
            </a-input-group>
          </ni-filter-item>
          <ni-filter-item v-show={this.$tnt.xtenant === 0}>
            <div class="nowrap" slot="label">门店专区：</div>
            <mba-select v-model={screenForm.zones}/>
          </ni-filter-item>
          <ni-filter-item label='超时情况'>
            <a-select
              allowClear
              mode="multiple"
              maxTagCount={1}
              placeholder="超时情况"
              options={dictionary.timeoutTypes}
              value={screenForm.timeoutTypes}
              onChange={(value) => {
                setScreenForm(value, 'timeoutTypes')
              }}
            />
          </ni-filter-item>
          <ni-filter-item label='审核人'>
            <NiStaffSelect v-model={screenForm.auditUserId} placeholder="请输入工号或姓名搜索"/>
          </ni-filter-item>
        </NiFilter>
      )
    }
  }
</script>

<style lang="scss" scoped>
.ant-input-group-compact {
  display: flex;
}

:deep(.no-label) {
  > .label {
    display: none;
  }
}

:deep(.group-item) {
  width: 660px !important;

  > .label {
    display: none;
  }
}
</style>
