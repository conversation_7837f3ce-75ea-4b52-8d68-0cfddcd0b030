
import { getCurrentInstance } from 'vue'
import api from '@operation/api'
import axios from 'axios'
import store from '~/store'
import moment from 'moment'
import { useState } from '../model/useState.js'
import {
  FETCH_STATISTIC
} from '@operation/store/modules/materialManage/action-types'

export default function useActions () {
  const instance = getCurrentInstance().proxy
  const {
    state,

    setQueryParams,
    resetScreenForm,
    setPagination,
  } = useState()

  const resetSearch = () => {
    resetScreenForm()
    fetchData(1)
  }
  const fetchData = async (current) => {
    current && setPagination(1, 'current')
    const {
      categoryId,
      formId,
      codes,
      name
    } = state.screenForm
    const params = {
      current: state.pagination.current,
      size: state.pagination.pageSize
    }
    if (categoryId) params.categoryId = categoryId
    if (formId?.length) {
      params.formIds = formId
      delete params.categoryId
    }
    if (codes) {
      const codesTostr = codes.replace(/[\uff0c]/g, ',')
      params.codeList = codesTostr.split(',').map(item => (Math.abs(item)))
    }
    if (name) params.name = name
    console.log('fetchData,params', params)
    setQueryParams(params)
    await instance.$store.dispatch(`operation/materialManage/${FETCH_STATISTIC}`, params)
  }
  const handleExport = async () => {
    let url = api.materialManage.exportStatistic('ncSegments')
    exportXlsx({
      url,
      params: state.queryParams,
      fileName: `物料陈列统计(${moment().format('YYYY-MM-DD HH：mm')}).xlsx`
    })
  }
  const exportXlsx = ({ url = '', params = {}, method = 'post', headers = {}, fileName = '导出.xlsx' } = {}) => {
    axios({
      method: method,
      url: url,
      data: params,
      responseType: 'blob',
      headers: {
        Authorization: store.state.token,
        ...headers
      }
    }).then((res) => {
      const link = document.createElement('a')
      let blob = new Blob([res.data], { type: 'application/x-excel' })
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    })
  }

  return {
    fetchData,
    resetSearch,
    handleExport
  }
}
