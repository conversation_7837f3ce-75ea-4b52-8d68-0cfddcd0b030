import { reactive, inject } from 'vue'
import { cloneDeep } from 'lodash'

const key = Symbol('statistics')

export function useState () {
  return inject(key)
}

export function createState (provide) {
  const state = reactive({
    spinning: false,
    treeData: [],
    expandedRowKeys: [],
    orderCatagroy: [],

    queryParams: {}, // 存储请求的参数(导出表格时用)
    screenFormReset: null, // 重置数据
    screenForm: {
      categoryId: undefined, // 类别id，不能和形式id一起传
      formId: [],
      // codeList: undefined,
      codes: '', // 需转化codeList
      name: '' // 画面名称
    },
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      hideOnSinglePage: true
    },
    paramsForm: {
      name: '',
    },
    paramsCode: {
      name: '',
    },
    paramsName: {
      searchCode: null,
      name: '',
    },
  })
  const setObj = objKey => (val, key) => {
    if (key && typeof key === 'string') {
      state[objKey][key] = val
    } else {
      state[objKey] = val
    }
  }
  const setScreenForm = setObj('screenForm') // 筛选参数相关
  const resetScreenForm = () => { state.screenFormReset && setScreenForm(cloneDeep(state.screenFormReset)) }
  const initScreenFormReset = () => { state.screenFormReset = cloneDeep(state.screenForm) }
  const setPagination = setObj('pagination')
  const setParamsForm = setObj('paramsForm')
  const setParamsCode = setObj('paramsCode')
  const setParamsName = setObj('paramsName')

  const setQueryParams = (val) => { state.queryParams = val }
  const setSpinning = val => { state.spinning = val || false }
  const setTreeData = val => { state.treeData = val || [] }
  const setExpandedRowKeys = val => { state.expandedRowKeys = val || [] }
  const setOrderCatagroy = val => { state.orderCatagroy = val || [] }

  const statistics = {
    state,

    setQueryParams,
    setSpinning,
    setTreeData,
    setExpandedRowKeys,
    setOrderCatagroy,

    setScreenForm,
    initScreenFormReset,
    resetScreenForm,
    setPagination,
    setParamsForm,
    setParamsCode,
    setParamsName
  }
  provide(key, statistics)
  return statistics
}
