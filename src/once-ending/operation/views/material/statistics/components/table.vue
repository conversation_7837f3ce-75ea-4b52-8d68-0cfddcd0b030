<script type='text/jsx' lang="jsx">
  import { toRefs } from 'vue'
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions'
  import { NiTable } from '@jiuji/nine-ui'
  import BtnRouter from '../../components/atom/btn-router'

  export default {
    name: 'statistics-table',
    components: {
      NiTable,
      BtnRouter
    },
    data () {
      return {
        columns: [
          {
            title: '物料类别',
            dataIndex: 'name',
            key: 'name',
            width: '18%',
            customRender: (value, record) => {
              // 跳转
              const routerParams = {
                categoryId: record.id
              }
              if (this.screenForm.name) routerParams.materialName = this.screenForm.name
              return {
                children: (
                  <div style='position:sticky; top: 60px'>
                    <i class="materail-name">{ value }</i>
                    （ <BtnRouter
                        routerName="OPR_MaterialManage"
                        params={ routerParams }
                        text={ record.num }/> ）
                  </div>
                ),
                style: {
                  verticalAlign: 'top'
                },
              }
            }
          },
          {
            title: '物料形式',
            dataIndex: 'form',
            key: 'form',
            width: '18%',
            customRender: (value, record) => {
              // 传入子级
              const categoryParams = {
                categoryId: record.id
              }
              const obj = {
                children: this.renderForms(record.formList, categoryParams),
                attrs: {},
              }
              obj.attrs.colSpan = 3
              return obj
            },
          },
          {
            title: '物料编号',
            dataIndex: 'code',
            key: 'code',
            width: '14.17%',
            customRender: this.renderNull
          },
          {
            title: '物料名称',
            dataIndex: 'materialName',
            key: 'materialName',
            width: '49.83%',
            customRender: this.renderNull
          },
        ],
      }
    },

    setup () {
      const { state } = useState()
      const { handleExport } = useActions()

      return {
        ...toRefs(state),
        handleExport
      }
    },
    render () {
      const {
        spinning,
        handleExport
      } = this
      const statistic = this.$store.state.operation.materialManage.statistic
      return (
        <div>
          <NiTable
            bordered
            class='material-statistics-table'
            rowKey= { (item, index) => index }
            columns= { this.columns }
            dataSource= { statistic }
            pagination={ false }
          >
          <template slot="action">
            <a-button class="mr-8 mb-8" onClick={ handleExport }>导出</a-button>
          </template>
        </NiTable>
          <a-spin
            spinning={ spinning }
            size="large"
            tip="导出中，请稍等...">
            <div class="spin-content"/>
          </a-spin>
        </div>
      )
    },
    methods: {
      renderForms (data, perantParams) {
        const columns = [
          {
            dataIndex: 'name',
            key: 'name',
            width: '22%',
            customRender: (value, record) => {
              // 跳转
              const routerParams = {
                ...perantParams,
                formId: record.id,
              }
              if (this.screenForm.name) routerParams.materialName = this.screenForm.name
              return <span>
                <i class="materail-name">{ value }</i>
                （ <BtnRouter
                    routerName="OPR_MaterialManage"
                    params={ routerParams }
                    text={ record.num }/> ）
              </span>
            }
          },
          {
            dataIndex: 'form',
            key: 'form',
            width: '78%',
            customRender: (value, record) => {
              // 传入子级
              const routerParams = {
                ...perantParams,
                formId: record.id,
              }
              const obj = {
                children: this.renderCodes(record.codeList, routerParams),
                attrs: {},
              }
              return obj
            },
          }
        ]
        return (
          <a-table
            rowKey= { (item, index) => index }
            showHeader= { false }
            columns= { columns }
            dataSource= { data }
            pagination={ false }
          />
        )
      },
      renderCodes (data, perantParams) {
        const columns = [
          {
            dataIndex: 'name',
            key: 'name',
            width: '22%',
            customRender: (value, record) => {
              const routerParams = {
                ...perantParams,
                codeId: record.id,
              }
              if (this.screenForm.name) routerParams.materialName = this.screenForm.name
              return <span>
                <i class="materail-name">{ value }</i>
                （ <BtnRouter
                    routerName="OPR_MaterialManage"
                    params={ routerParams }
                    text={ record.num }/> ）
              </span>
            }
          },
          {
            dataIndex: 'form',
            key: 'form',
            width: '78%',
            customRender: (value, record) => {
              const routerParams = {
                ...perantParams,
                codeId: record.id,
              }
              const obj = {
                children: this.renderNams(record.managerList, routerParams),
                attrs: {},
              }
              return obj
            },
          }
        ]
        return (
          <a-table
            rowKey= { (item, index) => index }
            showHeader= { false }
            columns= { columns }
            dataSource= { data }
            pagination={ false }
          />
        )
      },
      renderNams (data, perantParams) {
        const columns = [
          {
            dataIndex: 'name',
            key: 'name',
            width: '100%',
            customRender: (value, record) => {
              const routerParams = {
                ...perantParams,
                materialName: record.name,
              }
              return <span>
                <i class="materail-name">{ value }</i>
                （ <BtnRouter
                    routerName="OPR_MaterialManage"
                    params={ routerParams }
                    text={ record.num }/> ）
              </span>
            }
          },
        ]
        return (
          <a-table
            rowKey= { (item, index) => index }
            showHeader= { false }
            columns= { columns }
            dataSource= { data }
            pagination={ false }
          />
        )
      },
      renderNull () {
        const obj = {
          children: null,
          attrs: {},
        }
        obj.attrs.colSpan = 0
        return obj
      },
    },
  }
</script>

<style lang="scss" scope>
   @import '../../common/style.scss';
</style>
