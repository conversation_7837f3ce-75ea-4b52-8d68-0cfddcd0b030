<script type='text/jsx' lang="jsx">
  import { computed, onMounted, } from 'vue'
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions'
  import CategroyVSFormSelect from '../../components/select/categroy-vs-form'
  import { NiFilter, NiFilterItem } from '@jiuji/nine-ui'

  export default {
    name: 'statistics-screen',
    components: {
      CategroyVSFormSelect,
      NiFilter,
      NiFilterItem,
    },

    setup () {
      const {
        state,
        setScreenForm,
        initScreenFormReset
      } = useState()
      const { fetchData } = useActions()

      const onChangeCvsF = ({ categoryId, formId }) => {
        setScreenForm(categoryId, 'categoryId')
        setScreenForm(formId, 'formId')
      }
      onMounted(() => {
        initScreenFormReset()
        fetchData()
      })

      return {
        screenForm: computed(() => state.screenForm),
        setScreenForm,
        fetchData,
        onChangeCvsF
      }
    },
    render () {
      const {
        screenForm,
        setScreenForm,
        fetchData,
        onChangeCvsF
      } = this
      // loading={ isLoading }
      return (
        <NiFilter
          class="relative"
          layout='neat'
          form={ screenForm }
          immediate={false}
          onFilter={ () => { fetchData(1) }}
        >
          <ni-filter-item class='no-label'>
            <CategroyVSFormSelect
              value={{
                categoryId: screenForm.categoryId,
                formId: screenForm.formId
              }}
              onChange={ onChangeCvsF }
            />
            </ni-filter-item>
            <ni-filter-item label='物料编号'>
              <a-tooltip placement="top">
                <template slot="title">
                  <span>输入正整数，用逗号隔开</span>
                </template>
                <a-input
                  allowClear
                  placeholder="物料编号"
                  value={ screenForm.codes }
                  onChange={ (e) => {
                    setScreenForm(e.target.value, 'codes')
                  } }
                />
              </a-tooltip>
            </ni-filter-item>
            <ni-filter-item label='物料名称'>
              <a-input
                allowClear
                placeholder="物料名称"
                value={ screenForm.name }
                onChange={ (e) => {
                  setScreenForm(e.target.value, 'name')
                } }
              />
            </ni-filter-item>
        </NiFilter>
      )
    },
  }
</script>

<style lang="scss" scope>
   @import '../../common/style.scss';
</style>
