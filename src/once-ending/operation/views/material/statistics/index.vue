<script type='text/jsx' lang="jsx">
  import { provide } from 'vue'
  import { createState } from './model/useState.js'

  import Screen from './components/screen'
  import StatisticsTable from './components/table'
  import { NiListPage } from '@jiuji/nine-ui'

  export default {
    name: 'display-statistics',
    components: {
      Screen,
      StatisticsTable,
      NiListPage
    },

    setup () {
      createState(provide)
    },
    render () {
      return (
        <page>
          <NiListPage push-filter-to-location={ false }>
            <Screen class='mb-16'/>
            <StatisticsTable />
          </NiListPage>
        </page>
      )
    },
  }
</script>
<style lang="scss">
.material-statistics-table{
  &.nine-table .ant-table-fixed-header .ant-table-scroll .ant-table-body{
    overflow-y: visible !important;
  }
  .ant-table-wrapper{
    .ant-table-wrapper{
      margin: -17px !important;
    }
  }
}
</style>
<style lang='scss' scoped>
  .materail-name{
    margin-right: 5px;
  }
  :deep(.ant-table-thead > tr > th .ant-table-header-column) {
    font-weight: 600;
  }
  :deep(.ant-table-row-cell-break-word) {
    padding: 16px !important;
  }
</style>
