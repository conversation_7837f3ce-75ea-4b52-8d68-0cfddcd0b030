:deep(.ant-table-thead > tr > th .ant-table-header-column) {
  font-weight: 600;
}
:deep(.ant-table-hide-scrollbar) {
  overflow: hidden hidden !important;
}
:deep(.ant-table-bordered .ant-table-thead > tr > th, .ant-table-bordered .ant-table-tbody > tr > td) {
  border-right: none;
  border-left: none;
}
pre {
  --antd-wave-shadow-color: #1890ff;
  font: 14px/1.5 "BlinkMacSystemFont", "-apple-system", "Microsoft YaHei UI", "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", "sans-serif";
  -webkit-text-size-adjust: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  font-feature-settings: 'tnum';
  text-align: left;
  border-collapse: separate;
  border-spacing: 0;
  margin: 0;
  word-break: break-word;
  box-sizing: border-box;
  transition: background 0.3s;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  word-wrap: break-word;
}
.pre-line{
  white-space: pre-line;
  word-break: break-all;
}
