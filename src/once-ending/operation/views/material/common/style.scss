$gap:                   8px;
$form-item-margin:      $gap * 2;
$form-item-height:      32px;
$form-item-min-width:   180px;
$form-item-width:       200px;
$modal-actions-width:   170px;

.form-col-2{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .ant-form-item{
    width: 45%;
  }
}
.no-label{
  > .label{
    display: none;
  }
}
.ant-input-group-compact{
  display: flex;
 }

:deep(.group-item) {
  width: 660px !important;
  > .label{
    display: none;
  }
}
:deep(.ant-select-selection__choice__remove) {
  visibility:hidden;
}
:deep(.ant-select-selection__choice) {
  max-width: 50%;
}

.modal-actions{
  width: $modal-actions-width;
  margin: $form-item-margin auto 0;
  display: flex;
  justify-content: space-between;
}

// reset antd
.ant-form{
  &-item{
    margin-bottom: $form-item-margin;

    &-label {
      overflow: hidden;
      line-height: 30px;
    }
  }
}
.ant-col-24.ant-form-item-label{
  padding: 0;
}
:deep(.nine-table-bar.nine-table-bar-fixed){
  z-index: 9;
}


