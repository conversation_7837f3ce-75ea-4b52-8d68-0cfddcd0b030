import { getOptMap, getOptFiter } from '@operation/util/common.js'

const dictionary = {
  otherTypes: [
    {
      label: '物料名称',
      key: 'name',
      value: 0
    },
    {
      label: '物料陈列ID',
      key: 'id',
      value: 1
    },
  ],
  isBindDisplay: [
    {
      label: '是',
      key: 'true',
      value: 1
    },
    {
      label: '否',
      key: 'false',
      value: 0
    },
  ],
  pictureTimeTypes: [
    {
      label: '创建时间',
      key: 'creatTime',
      value: 0
    },
  ],
  timeTypes: [
    {
      label: '反馈截止时间',
      key: 'endTime',
      value: 0
    },
    {
      label: '盘点时间',
      key: 'pandianTime',
      value: 1
    },
    {
      label: '物料过期时间',
      key: 'expiredTime',
      value: 2
    },
    {
      label: '审核时间',
      key: 'auditTime',
      value: 3
    },
  ],
  pictureTimeType: [
    {
      label: '创建时间',
      key: 'creatTime',
      value: 1
    },
    {
      label: '物料过期时间',
      key: 'expiredTime',
      value: 2
    },
  ],
  status: (function () {
    const list = [
      {
        value: 1,
        label: '待反馈'
      },
      {
        value: 2,
        label: '待审核',
      },
      {
        value: 3,
        label: '已反馈',
      },
      {
        value: 4,
        label: '未反馈',
      },
      {
        value: 5,
        label: '已过期',
        show: window.tenant.xtenant >= 1000
      },
      {
        value: 6,
        label: '已删除',
      },
      {
        value: 7,
        label: '待盘点',
      },
      {
        value: 8,
        label: '未盘点',
      }, {
        value: 9,
        label: '待撤除',
        show: window.tenant.xtenant < 1000
      }, {
        value: 10,
        label: '已撤除',
        show: window.tenant.xtenant < 1000
      },
    ]
    return list.filter(d => d.show === undefined || d.show)
  }()),
  materielStatus: [
    {
      label: '启用',
      value: 1
    },
    {
      label: '停用',
      value: 0
    },
  ],
  aiAudits: [
    {
      value: 1,
      label: '未审核',
    },
    {
      value: 2,
      label: '合格',
    },
    {
      value: 3,
      label: '不合格',
    }
  ],
  timeoutTypes: [
    {
      value: 1,
      label: '未超时',
    },
    {
      value: 2,
      label: '反馈超时',
    },
    {
      value: 3,
      label: '盘点超时',
    }
  ],
  brandOptions: [
    {
      label: '木质价签大',
      value: 1
    },
    {
      label: '真机品牌画面',
      value: 2
    }
  ],
  brandOptions9ji: [
    {
      label: '大木质价签',
      value: 1
    },
    {
      label: '真机品牌画面',
      value: 2
    }
  ],
  framePosOpt: [
    {
      label: '划线',
      value: 1
    },
    {
      label: '不划线',
      value: 2
    }
  ]
}
const statusMap = getOptMap(dictionary.status)
const aiAuditsMap = getOptMap(dictionary.aiAudits)
const timeoutTypesMap = getOptMap(dictionary.timeoutTypes)
const statusOpt = getOptFiter(dictionary.status, window.tenant.xtenant < 1000 ? [1, 2, 3, 7, 9, 10] : [1, 2, 3, 5, 7])

export {
  dictionary,
  statusMap,
  aiAuditsMap,
  timeoutTypesMap,
  statusOpt
}
