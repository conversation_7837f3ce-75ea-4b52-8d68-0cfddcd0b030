<!-- 生成二维码, 二维码内容: `${this.$tnt.mHost}/up.aspx?id=${routKey}&p=honorApply`, APP拦截该路径跳转原生处理 -->
<!-- 订阅主题 '/exchange/oaupload/'+routKey 可以获取到文件上传状态等 -->

<!-- STOPM: Simple (or Streaming) Text Orientated Messaging Protocol -->
<!-- API文档: https://stomp-js.github.io/api-docs/latest/classes/Stomp.html -->
<template>
    <div>
        <div class="flex">
            <div :class="{ 'show-pc': !showPcBtn }" class="upload-btn" v-if="showUploadBtn">
                <a-upload
                        :action="$api.common.uploadPathNew"
                        @change="uploadChange"
                        :data="{durable:'1'}"
                        :headers="headers"
                        :multiple="multiple"
                        :showUploadList="showUploadList"
                        :accept="accept"
                        @preview="preview"
                        :remove="removeFile"
                        :fileList="fileList"
                        :defaultFileList="fileList"
                       >
                    <a-button type="primary" size="small" :disabled="disabled" v-if="showPcBtn">
                        <a-icon type="upload"></a-icon>
                        {{btnText}}
                    </a-button>
                    <span class="grey-9 font-13">{{Text}}</span>
                    <a-progress v-if="showPercent" :percent="percent" />
                </a-upload>
            </div>
            <div :style="{ marginLeft: showPcBtn ? '-240px' : '-350px' }" class="qr-btn" v-if="showQrBtn">
                <a-button type="primary" size="small" @click="showQr()" :disabled="disabled">
                    <a-icon type="qrcode"></a-icon>
                    {{modalBtnText}}
                </a-button>
            </div>
            <a-modal
                    title="使用OA App扫码"
                    v-model="qrcodeVisible"
                    destroyOnClose
                    :maskClosable="false"
                    @ok="closeQr"
                    @cancel="closeQr"
                    :width="268">
                <QrCode :value="qrContent" style="width: 100%;"></QrCode>
            </a-modal>
        </div>
        <div v-if="showEditList">
          <div v-for="(img, index) in fileList" :key="index" class="flex flex-align-center">
            <a-input v-if="canEditName" v-model="img.fileName" style="width: 400px" :disabled="!showQrBtn || disabled"></a-input>
            <span v-else style="width: 200px;">{{img.fileName}}</span>
            <a-popover title="" trigger="hover" :overlayStyle="{'z-index': 99999}">
              <template slot="content">
                <img :src="img.url" width="200"/>
              </template>
                <a v-if="testImageType(img.url)" :href="img.url" target="_blank" class="padding">
                  <a-icon type="eye"/>
                </a>
            </a-popover>
              <a v-if="download" class="padding" href="#" @click="downloadFile(img)"><a-icon type="cloud-download" /></a>
              <a v-if="deleteItem" :class="{'disabled-pointer grey-9' : !(showDelete || showInfo && img.createUserId && img.createUserId === userInfo.UserID && img.xtenant === $tnt.xtenant)}" class="padding" @click="fileList.splice(index,1)"><a-icon type="delete"/></a>
            <a-popover title="" trigger="hover" v-if="showInfo">
                <template slot="content">
                  <span>附件信息</span>
                  <span>{{img.createUserName}}</span>
                  <span>{{img.createTime}}</span>
                </template>
                <a><a-icon class="padding" type="info-circle"/></a>
              </a-popover>
              <a-button type="primary" size="small" @click="renameFile(img)" v-if="img.id && showRename">重命名</a-button>
          </div>
        </div>
      <img-area-preview :showGps="false" ref="imgAreaPreview"></img-area-preview>
    </div>
</template>

<script>
  import { Modal, Upload } from 'ant-design-vue'
  import uuidv4 from 'uuid/v4'
  import api from '~/api'
  import { mapState } from 'vuex'
  import QrCode from '~/components/qrcode'
  import store from '~/store'
  import { saveAs } from 'file-saver'
  import ImgAreaPreview from '~/components/uploader/img-area-preview'

  const Stomp = require('stompjs/lib/stomp.js').Stomp
  const INIT_MAX = 5
  export default {
    name: 'Uploader',

    props: {
      canEditName: {
        type: Boolean,
        default: true
      },
      // 是否显示重命名按钮
      showRename: {
        type: Boolean,
        default: true
      },
      disabled: {
        type: Boolean,
        default: false
      },
      showDelete: { // 只有创建人才能显示删除按钮 并且不想显示 showInfo 上传信息
        type: Boolean,
        default: false
      },
      multiple: {
        type: Boolean,
        default: true
      },
      download: {
        type: Boolean,
        default: false
      },
      // 是否显示上传人，上传时间信息
      showInfo: {
        type: Boolean,
        default: false
      },
      deleteItem: {
        type: Boolean,
        default: false
      },
      max: {
        type: Number,
        default: Infinity
      },
      showUploadBtn: {
        type: Boolean,
        default: true
      },
      showPcBtn: {
        type: Boolean,
        default: true
      },
      showQrBtn: {
        type: Boolean,
        default: true
      },
      showEditList: { // 显示可编辑图片列表 该属性应和showUploadList互斥
        type: Boolean,
        default: false
      },
      showUploadList: {
        type: [Boolean, Object],
        default: () => ({
          showPreviewIcon: false,
          showRemoveIcon: true
        })
      },
      btnText: {
        type: String,
        default: '添加附件'
      },
      Text: {
        type: String,
        default: '(200MB以内)'
      },
      modalBtnText: {
        type: String,
        default: '手机上传'
      },
      files: {
        type: Array,
        default: () => ([])
      },
      qrCodeUrl: {
        type: String,
        default: ''
      },
      routKey: {
        type: String,
        default: ''
      },
      headers: {
        type: Object,
        default: () => {
          return {
            Authorization: store.state.token
          }
        }
      },
      accept: String,
      sendMessage: {
        type: Boolean,
        default: false
      },
    },

    components: {
      [Upload.name]: Upload,
      [Modal.name]: Modal,
      QrCode,
      ImgAreaPreview
    },

    data () {
      return {
        fileList: [],
        qrcodeVisible: false,
        // qrcode: '',
        qrContent: '',
        phoneUpload: {
          isInit: false,
          ws: null,
          routKey: '',
          isLog: true,
          client: null,
          appKey: 'oanew',
          appSecret: 'oanew',
          host: 'oa',
          // appKey: 'webmsg',
          // appSecret: 'jiuji',
          // host: 'webmsg',
          server: this.$tnt.wss,
          upChange: '/exchange/oaupload/',
          initMax: INIT_MAX,
          initTimerId: 0
        },
        percent: 0,
        showPercent: false
      }
    },
    computed: {
      ...mapState({
        userInfo: state => state.userInfo || {}
      })
    },
    created () {
      this.handleFile()
      if (this.$tnt.mq && this.$tnt.mq.length) {
        let tagetMQ = this.$tnt.mq.find(m => m.sourceVhost === 'oa')
        if (tagetMQ) {
          this.phoneUpload.host = tagetMQ.vhost
          this.phoneUpload.appKey = tagetMQ.username
          this.phoneUpload.appSecret = tagetMQ.pwd
        }
      }
    },
    watch: {
      files: {
        handler (newVal) {
          this.handleFile()
        },
        deep: true,
        immediate: true
      }
    },
    beforeDestroy () {
      this.destroyClient()
    },

    methods: {
      preview (file) {
        if (this.testImageType(file.url) || file.url.includes('.webp')) {
          this.$refs.imgAreaPreview.previewImg({
            fileList: [{
              ...file,
              filePath: file.url
            }]
          })
        } else {
          window.open(file.url, '_blank')
        }
      },
      removeFile (file) {
        this.$emit('remove', file)
      },
      renameFile (file) {
        // todo 需要有异常兜底机制, 防止后端没有返回附件id的情况
        // if (!file.id) {
        //   this.$message.warn('无法修改附件名称,请截屏联系开发处理')
        //   new Error()
        //   return
        // }
        this.$indicator.open()
        this.$api.common.renameFile({
          id: file.id,
          filename: file.fileName
        }).then((res) => {
          if (res.code === 0) {
            this.$message.success(res.userMsg || '修改成功')
          } else {
            this.$message.error(res.userMsg || '修改失败,请重试')
          }
        }).finally(() => {
          this.$indicator.close()
        })
      },
      downloadFile (file) {
        saveAs(file.filePath, file.fileName)
      },
      getImgList () {
        return this.fileList
      },
      handleFile () {
        // console.log('val', this.files)
        this.fileList = (this.files || []).map(f => {
          const url = f.url ? (this.testImageType(f.url) ? `${f.url}.webp` : f.url) : (this.testImageType(f.filePath) ? `${f.filePath}.webp` : f.filePath)
          return {
            ...f,
            status: 'done',
            name: f.fileName || f.name,
            uid: f.uid || f.fid || f.filePath,
            fileName: f.fileName || f.name || f.filename,
            url
          }
        })
      },
      uploadChange (info) {
        // if (info.file.status !== 'uploading') {
        //   // console.log(info.file)
        //   // console.log(info.fileList)
        //   const event = info.event
        //   if (event) {
        //     let percent = Math.floor((event.loaded / event.total) * 100)
        //     this.percent = percent
        //     // console.log(percent)
        //   }
        // }
        this.showPercent = true
        if (info.file.status === 'done') {
          const file = info.fileList[info.fileList.length - 1] // 用info.fileList列表的最后一个上传附件的状态（成功和失败）来判断，用于精确提示信息
          if (file.response && file.response.code === 0) {
            this.$message.success(`${info.file.name} 上传成功`)
          }
        } else if (info.file.status === 'error') {
          this.$message.error('上传失败(文件大于200MB，请修改后提交)')
        } else if (info.file.status === 'removed') {
          this.$message.success(`${info.file.name} 删除成功`)
        }
        if (this.isLog) {
          console.log(info)
        }
        let fileList = info.fileList
        fileList = fileList.map(file => {
          if (file.response) { // 当file.response === undefined时，不走下面的语句
            const { code, msg, userMsg } = file.response
            if (code === 0) {
              file.url = file.response.data.filePath
              file.id = file.response.data.fileid
              return Object.assign({}, file, file.response.data)
            } else {
              // this.$message.error(userMsg || msg)
              this.$message.error('上传失败(文件大于200MB，请修改后提交)')
            }
          } else {
            return file
          }
        })
        const file = info.file
        this.percent = Math.floor(file.percent - 2)
        if (file.response) {
          this.showPercent = false
        }
        this.fileList = fileList.filter(item => item)
        this.$emit('change', this.fileList)
      },
      initQr () {
        this.phoneUpload.routKey = this.routKey || this.phoneUpload.routKey || uuidv4() // 复用routKey
        this.phoneUpload.isLog = false

        let url = this.qrCodeUrl || this.qrContent || `${this.$tnt.mHost}/up.aspx?id=${this.phoneUpload.routKey}&p=honorApply` // 复用链接
        this.qrContent = url
      },
      showQr () {
        this.init()
      },
      closeQr () {
        this.destroyClient()
        this.qrcodeVisible = false
      },
      pushGateway () {
        // let url = `http://m.9ji.com/up.aspx?id=${this.userInfo.UserID}&p=Recoverorder&t=0&dt=0.7936689417876834`
        let params = {
          appName: 'oa',
          title: '上传图片',
          content: '请打开oa上传图片',
          extra: JSON.stringify({
            type: 8,
            isAuto: true,
            url: this.qrContent
          }),
          isTest: false,
          alias: ['staff_' + this.userInfo.UserID]
          // tokens: [store.state.token]
        }
        api.common.pushGateway(params).then(() => {}).finally(() => {})
      },
      init () {
        let { appKey, appSecret, host, server, isInit, routKey } = this.phoneUpload
        // if (isInit || !routKey) {
        //   return
        // }
        /* eslint-disable */
        // 先销毁
        this.destroyClient()
          this.initQr()
        // 再创建
        this.phoneUpload.ws = new WebSocket(server)
        this.phoneUpload.client = Stomp.over(this.phoneUpload.ws)
        this.phoneUpload.client.connect(appKey, appSecret, this.connectCallback, this.errorCallback, host)
        this.phoneUpload.isInit = true
        // this.$mqtt.on((message) => {
        //       const msg = JSON.parse(new TextDecoder('utf-8').decode(message))
        //       console.log('mqttMsg', msg)
        //       if (msg.code === 0 && msg.data.type === 2 && msg.data.action.name === 'cash') {
        //           if (this.id.toString() === msg.data.action.body.orderId.toString()) {
        //               this.cash.payVisible = false
        //               this.$mqtt.unsubscribe(this.sub)
        //               this.cashVisible = false
        //               this.$message.success(channel === 1 ? '支付宝支付成功' : '微信支付成功')
        //               this.$emit('defineCash')
        //           }
        //       }
        //   })
      },
      destroyClient () {
          clearInterval(this.phoneUpload?.initTimerId)
          if (this.phoneUpload?.client) {
              this.phoneUpload.client.disconnect(() => {
                  console.log('stomp disconnected')
                  this.phoneUpload.isInit = false
                  this.phoneUpload.client = null
                  this.phoneUpload.ws = null
              })
          }
      },
      connectCallback () {
          // this.$message.success('websocket连接成功')
        let { upChange, routKey, isLog } = this.phoneUpload
        this.phoneUpload.client.subscribe(upChange + routKey, (d) => {
          if (d.body !== 'Heartbeat') {
            if (isLog) {
              console.log(d.body + '-----')
            }
            this.msgAction(d.body)
          }
        })
        // 连接成功后再显示二维码
          this.qrcodeVisible = true
        // 连接成功回调后再去推送
        if (this.sendMessage && this.$tnt.xtenant.toString() === '0' && this.phoneUpload.initMax === INIT_MAX) { // 九机需要消息推送的时候推送消息
          this.pushGateway()
        }
        // 连接成功后再监听失败
        this.phoneUpload.initTimerId = setInterval(() => {
          this.listenerDisconnect()
        }, 1000)
      },
      errorCallback (e) {
        console.warn(e)
        this.$message.error('websocket连接失败')
      },
      msgAction (result) {
        try {
          let images = JSON.parse(result)
          this.fileList = this.fileList.concat(images.map(pic => {
            // const filePath = pic.fid.split(',') // 2045,017ea359105afa2b
            return Object.assign({}, {
              uid: pic.id,
              name: pic.filename,
              status: 'done',
              fileName: pic.filename,
              // url: `https://moa.ch999.com/static/${pic.fid.split(',')[0]}/${pic.fid.split(',')[1]}${pic.Extension}`,
              // url: `https://img2.ch999img.com/newstatic/${filePath[0]}/${filePath[1]}`
              url: pic.filepath
            }, pic)
          }))
          this.$emit('change', this.fileList)
          this.closeQr()
        } catch (e) {
        }
      },
      listenerDisconnect () {
        if ((this.phoneUpload.client && this.phoneUpload.client.connected) || this.phoneUpload.initMax <= 0) {
          // 正常连接中 或者 重试超过5次
        } else {
          this.phoneUpload.isInit = false
          this.init()
          this.phoneUpload.initMax--
        }
      },
      testImageType (url) {
        const isImage = /\.(png|PNG|jpe?g|JPE?G|gif|GIF|svg|SVG)(\?.*)?$/
        return isImage.test(url)
      }
    },

  }
</script>

<style lang="scss" scoped>
.qr-btn {
  margin-left: -240px;
}
.upload-btn {
  width: 350px;
}
.show-pc {
  margin-top: 40px;
}
</style>
