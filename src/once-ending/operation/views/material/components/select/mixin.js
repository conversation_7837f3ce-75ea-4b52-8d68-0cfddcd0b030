import { mapState, mapGetters, mapActions, mapMutations } from 'vuex'

export default {
  data () {
    return {
      selectedCategoryId: null,
      selectedFormId: null
    }
  },
  computed: {
    ...mapState({
      categroyOptions: state => state.operation.materialManage.categroyOptions,
      formOptions: state => state.operation.materialManage.formOptions,
    }),
    ...mapGetters('operation/materialManage', [
      'maxCountOptions'
    ]),
  },
  methods: {
    ...mapActions('operation/materialManage', [
      'fetchCategroyOptions',
      'fetchFormOptions',
      'fetchFormMaxcount',
    ]),
    ...mapMutations('operation/materialManage', [
      'setFormMaxcount',
      'setFormOptions',
    ]),
    fetchCategroys () {
      const params = {}
      if (this.queryStatus != null) params.status = this.queryStatus
      this.fetchCategroyOptions(params)
    },
    handleChange (value) {
      this.form[this.filed] = value
      this.change && this.change(value)
    },
  }
}
