
<script type='text/jsx' lang="jsx">
  import { watch, computed, ref, getCurrentInstance } from 'vue'
  import {
    SET_FORM_OPTIONS
  } from '@operation/store/modules/materialManage/mutation-types'
  import useActions from './useActions'
  export default {
    name: 'form-select',
    props: {
      value: {
        type: [String, Array, Number],
        default: undefined
      },
      categoryId: {
        type: [Number, String],
        default: ''
      },
      queryStatus: {
        type: [Number, String],
        default: 1
      },
      multiple: {
        type: Boolean,
        default: true
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const options = ref([])
      const { fetchFormOptions } = useActions()

      const handleChange = (value) => {
        proxy.$emit('change', value, 'formId')
      }
      const getOptions = async (categoryId) => {
        const isTrue = await fetchFormOptions(categoryId, props.queryStatus)
        if (isTrue) options.value = computed(() => proxy.$store.state.operation.materialManage.formOptions).value
      }

      watch(
        () => props.categoryId,
        (val, preVal) => {
          // 1.清空形式值
          if (!val || (val && preVal)) handleChange() // 防止路由formId参数清空
          // 2.清空【形式Options】
          proxy.$store.commit(`operation/materialManage/${SET_FORM_OPTIONS}`)
          // 3.重新请求categoryId所对应的【形式Options】
          if (val) {
            getOptions(val)
          } else {
            options.value = []
          }
        },
        { immediate: true }
      )

      return {
        options,
        handleChange
      }
    },
    render () {
      const { value, options } = this
      return (
        <a-tooltip placement="top">
        { options?.length === 0 && <template slot="title">
            <span>请先选择物料类别, 若已选择类别，则所选择的类别下无形式</span>
            </template>
          }
          <a-select
            allowClear
            maxTagCount={1}
            mode={ this.multiple ? 'multiple' : 'default' }
            placeholder="物料形式"
            options={ options }
            disabled={ options?.length === 0 }
            value={ value }
            onChange={ value => {
              this.handleChange(value)
            } }
          />
        </a-tooltip>
      )
    },
  }
</script>
