
<script type='text/jsx' lang="jsx">
  import { onMounted, getCurrentInstance } from 'vue'
  import useActions from './useActions'

  export default {
    name: 'categroy-select',
    props: {
      value: {
        type: [String, Array, Number],
        default: undefined
      },
      queryStatus: {
        type: [Number, String],
        default: 1
      },
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const { fetchCategroys } = useActions()

      const handleChange = (value) => {
        proxy.$emit('change', value, 'categoryId')
      }

      onMounted(() => {
        fetchCategroys(props.queryStatus)
      })

      return {
        handleChange
      }
    },
    render () {
      const { value } = this
      const categroyOptions = this.$store.state.operation.materialManage.categroyOptions
      return (
        <a-select
          allowClear
          placeholder="物料类别"
          options={ categroyOptions }
          value={ value }
          onChange={ value => {
            this.handleChange(value)
          } }
        />
      )
    },
  }
</script>
