
<script type='text/jsx' lang="jsx">
  import { reactive, watch, toRefs, getCurrentInstance } from 'vue'
  import { NiFilterItem } from '@jiuji/nine-ui'
  import CategroySelect from './categroy'
  import FormSelect from './form'

  export default {
    name: 'material-select',
    components: {
      CategroySelect,
      FormSelect,
      NiFilterItem
    },
    props: {
      value: {
        type: Object,
        default: () => ({
          categoryId: undefined,
          formId: undefined
        })
      },
      queryStatus: {
        type: [Number, String],
        default: 1
      },
      formMultiple: {
        type: Boolean,
        default: true
      },
      displayBlock: { // 类型与形式单独排列
        type: Boolean,
        default: false
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const state = reactive({
        categoryId: undefined,
        formId: undefined,
      })

      const changeSelect = (val, type) => {
        state[type] = val
        proxy.$emit('change', state)
      }

      watch(
        () => props.value.categoryId,
        (val) => { // vatch是为了把state对像组对emit
          state.categoryId = val
        },
      )

      watch(
        () => props.value.formId,
        (val) => {
          state.formId = val
        },
      )

      return {
        ...toRefs(state),
        changeSelect,
      }
    },
    /**
     * 组合类别与形式的组件，组件内用的值为props，自建state是为了向上emit一个包含category与form的对象
     * 非 displayBlock 主要用在列表的筛选区
     */
    render () {
      const {
        value,
        queryStatus,
        formMultiple,
        displayBlock
      } = this
      return (
        <div>
          { displayBlock
              ? <span>
                  <a-form-item label="物料类别" required>
                    <CategroySelect
                      value={ value.categoryId }
                      onChange= { this.changeSelect }
                    />
                  </a-form-item>
                  <a-form-item label="物料形式" required>
                    <FormSelect
                      value={ value.formId }
                      categoryId={ value.categoryId }
                      multiple={ formMultiple }
                      onChange= { this.changeSelect }
                    />
                  </a-form-item>
                </span>
              : <a-input-group compact>
                  <CategroySelect
                    style='width: 38%'
                    value={ value.categoryId }
                    queryStatus= { queryStatus }
                    onChange= { this.changeSelect }
                  />
                  <FormSelect
                    style='width: 62%'
                    value={ value.formId }
                    categoryId={ value.categoryId }
                    multiple={ formMultiple }
                    queryStatus= { queryStatus }
                    onChange= { this.changeSelect }
                  />
                </a-input-group>
          }
        </div>
      )
    }
  }
</script>
<style scoped>
  .ant-input-group-compact{
    display: flex;
  }
</style>
