
import { getCurrentInstance } from 'vue'
import {
  FETCH_CATEGROY_OPTIONS,
  FETCH_FORM_OPTIONS,
} from '@operation/store/modules/materialManage/action-types'

export default function useActions () {
  const instance = getCurrentInstance().proxy

  const fetchCategroys = async (queryStatus) => {
    const params = {}
    if (queryStatus != null) params.status = queryStatus
    const isTrue = await instance.$store.dispatch(
      `operation/materialManage/${FETCH_CATEGROY_OPTIONS}`,
      params
    )
    return isTrue
  }
  const fetchFormOptions = async (categoryId, queryStatus) => {
    const params = { categoryId }
    if (queryStatus != null) params.status = queryStatus
    const isTrue = await instance.$store.dispatch(
      `operation/materialManage/${FETCH_FORM_OPTIONS}`,
      params
    )
    return isTrue
  }
  return {
    fetchCategroys,
    fetchFormOptions
  }
}
