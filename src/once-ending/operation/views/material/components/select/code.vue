<script type='text/jsx' lang="jsx">
  import { watch, computed, getCurrentInstance } from 'vue'
  import {
    FETCH_FORM_MAXCOUNT
  } from '@operation/store/modules/materialManage/action-types'
  import {
    SET_FORM_MAXCOUNT
  } from '@operation/store/modules/materialManage/mutation-types'

  export default {
    name: 'code-select',
    props: {
      value: {
        type: [String, Number, Array],
        default: undefined
      },
      formId: { // 根据formId请求codeOptions
        type: [String, Number, Array],
        default: undefined
      },
      multiple: {
        type: Boolean,
        default: false
      },
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const getMaxCountOptions = async () => {
        await proxy.$store.dispatch(
          `operation/materialManage/${FETCH_FORM_MAXCOUNT}`,
          { formIds: (typeof props.formId === 'string' || typeof props.formId === 'number') ? [props.formId] : props.formId.join(',') }
        )
      }
      const codeOptions = computed(() => proxy.$store.getters['operation/materialManage/maxCountOptions'])
      const handleChange = (value) => {
        proxy.$emit('change', value)
      }

      watch(
        () => props.formId,
        val => {
          let formId = (typeof val === 'number') ? val + '' : val
          if (!formId || (formId && !formId.length)) { // 空值与（数组）长度为0
            proxy.$store.commit(`operation/materialManage/${SET_FORM_MAXCOUNT}`)
            proxy.$emit('change', undefined)
            return
          }
          getMaxCountOptions()
        },
        { immediate: true }
      )
      return {
        codeOptions,
        handleChange
      }
    },
    render () {
      const {
        value,
        multiple,
        codeOptions
      } = this

      return (
        <div class="form-item flex-d5">
         <a-tooltip placement="top">
         { codeOptions?.length === 0 && <template slot="title">
            <span>请先选择物料形式</span>
            </template>
          }
            <a-select
              allowClear
              mode={ multiple ? 'multiple' : 'default'}
              maxTagCount={ 1 }
              placeholder="物料编号"
              options={ codeOptions }
              disabled={ codeOptions?.length === 0 }
              value={ value }
              onChange={ value => {
                this.handleChange(value)
              } }
            />
          </a-tooltip>
        </div>
      )
    }
  }
</script>

<style lang="scss" scope>
  @import '../../common/style';
</style>
