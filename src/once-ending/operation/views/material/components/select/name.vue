<template>
  <div class="form-item">
    <a-select
      class="name-select"
      showSearch
      label-in-value
      v-bind="$attrs"
      :placeholder="placeholder"
      v-model="form[field]"
      :filter-option="filterOption"
      @change="handleChange"
    >
      <a-select-option v-for="item in materialNames" :key="item.key">
        {{ item.label }}
      </a-select-option>
    </a-select>
  </div>
</template>

<script>
  import { mapActions, mapState } from 'vuex'
  export default {
    name: 'name-select',
    props: {
      form: {
        type: Object,
        default: {}
      },
      placeholder: {
        type: String,
        default: '输入物料名称关键字查询'
      },
      field: {
        type: String,
        default: 'name'
      },
    },
    created () {
      this.getData()
    },

    computed: {
      ...mapState({
        materialNames: state => state.operation.materialManage.materialNames,
      }),
    },

    methods: {
      ...mapActions('operation/materialManage', [
        'fetchMaterialNames',
      ]),
      async getData () {
        const { formId } = this.form
        if (!formId) return
        await this.fetchMaterialNames({ formId })
        // 物料名称 与 画面Id (frameId) 一一 对应
        this.form[this.field] = this.form.frameId ? this.materialNames.find(item => item.key === this.form.frameId) : {}
      },
      /**
       * @param {value} Object {key, label}
       */
      handleChange (value) {
        const name = value ? this.materialNames.find(item => item.key === value.key) : {}
        this.form[this.field] = name
        this.$emit('change', name)
      },
      filterOption (input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      },
    }
  }
</script>

<style scoped lang="scss" scoped>
  .name-select{
    width: 100% !important;
  }
</style>
