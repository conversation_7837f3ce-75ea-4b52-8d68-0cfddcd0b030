<template>
  <a-select
    class="value-select"
    allowClear
    showSearch
    label-in-value
    style="width:100%"
    :placeholder="placeholder"
    v-model="form[field]"
    :filter-option="false"
    :not-found-content="fetching ? undefined : '暂无数据'"
    @search="getOptions"
    @change="handleChange"
  >
    <a-spin v-if="fetching" slot="notFoundContent" size="small" />
    <a-select-option v-for="item in nameOption" :key="item.key">
      {{ item.label }}
    </a-select-option>
  </a-select>
</template>

<script>
  import debounce from 'lodash/debounce'
  import { mapActions, mapState } from 'vuex'
  export default {
    name: 'search-select-async',
    props: {
      form: {
        type: Object,
        default: () => ({})
      },
      placeholder: {
        type: String,
        default: '输入物料名称关键字'
      },
      field: {
        type: String,
        default: 'nameSelect'
      },
      // 场景区分
      // 枚举['material', 'picture'] (关系到查询接口不同，默认为首页的物料名称枚举，其它场景用需要添加相关接口和store)
      scene: {
        type: String,
        default: 'material'
      }
    },

    data () {
      this.lastFetchId = 0
      this.getOptions = debounce(this.getData, 1000)

      return {
        nameOption: [],
        fetching: false,
      }
    },

    computed: {
      ...mapState({
        nameOptions: state => state.operation.materialManage.nameOptions,
        pictureNameOptions: state => state.operation.materialManage.pictureNameOptions,
      }),
    },

    methods: {
      ...mapActions('operation/materialManage', [
        'fetchNameOptions',
        'fetchPictureNameOptions',
      ]),
      async getData (name) {
        this.inputValue = name
        if (!name) return
        this.lastFetchId += 1
        const fetchId = this.lastFetchId
        this.fetching = true
        const sceneOptions = {
          material: { // 管理首页
            api: 'fetchNameOptions',
            options: 'nameOptions'
          },
          picture: { // 画面管理页
            api: 'fetchPictureNameOptions',
            options: 'pictureNameOptions'
          }
        }
        const isTrue = await this[sceneOptions[this.scene].api]({ name })
        if (!isTrue) return
        this.fetching = false
        if (fetchId !== this.lastFetchId) return
        const nameOption = this[sceneOptions[this.scene].options].map((item, index) => {
          return {
            key: item.trim(),
            label: item.trim()
          }
        })
        if (!nameOption.find(it => it.label === name)) {
          nameOption.unshift({ key: name, label: name })
        }
        this.nameOption = nameOption
      },

      /**
       * @param {value} Object {key, label}
       */
      handleChange (value) {
        this.$emit('changeVal')
        this.form[this.field] = value ? this.nameOption.find(item => item.key === value.key) : undefined
        Object.assign(this, {
          nameOption: [],
          fetching: false,
        })
      },
    }
  }
</script>
