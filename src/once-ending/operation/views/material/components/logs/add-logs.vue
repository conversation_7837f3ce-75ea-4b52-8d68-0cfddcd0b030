<script type="text/jsx" lang="jsx">
  export default ({ props }) => {
    const {
      contentFeild, // 日志内容字段
      newLog, // 日志form
      addLogs
    } = props

    return (
      <a-form wrapper-col={{ span: 24 }}>
        <a-form-item label="添加备注">
          <a-textarea
            placeholder="请输入日志内容"
            value={ newLog[contentFeild] }
            rows={2}
            onChange={ e => { newLog[contentFeild] = e.target.value } }
          />
        </a-form-item>
        <div class="text-right">
          <a-button type="primary" onClick={ addLogs }>添加</a-button>
        </div>
      </a-form>
    )
  }
</script>
