<script type="text/jsx" lang="jsx">
  import AddLogs from './add-logs'
  import Logs from './logs'
  import { mapActions, mapMutations, mapState } from 'vuex'
  /**
   * 日志组件
   * 根据场景[scene]请求和添加日志
   */
  export default {
    name: 'modal-logs',
    components: {
      AddLogs,
      Logs
    },
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      recordId: {
        type: Number,
        default: null
      },
      // 场景：[管理'materialLogs', 陈列'displayLogs']
      scene: {
        type: String,
        default: ''
      },
      showAddLogs: {
        type: Boolean,
        default: false
      },
      handleCancel: {
        type: Function
      },
    },
    data: () => ({
      newLog: {
        content: '', // 每个添加日志接口对应的内容字段不一样
        loading: false
      },
      update: 1,
    }),
    computed: {
      ...mapState({
        materialLogs: state => state.operation.materialManage.materialLogs,
        displayLogs: state => state.operation.materialManage.displayLogs,
      }),
    },
    created () {
      this.featchLogs()
    },
    render () {
      return (
        <a-modal
          title="查看日志"
          visible={ this.visible }
          width={ 600 }
          onCancel={ this.closeModal }
          footer={ false }
        >
          { !!this.update && <Logs logs={ this[this.scene] || [] }/> }
          {
            this.showAddLogs && (
            <AddLogs
              contentFeild='content'
              newLog={this.newLog}
              addLogs={this.addLogsAsync}
            />)
          }
        </a-modal>
      )
    },
    methods: {
      ...mapActions('operation/materialManage', [
        'fetchMaterialLogs',
        'submitMaterialLogs',
        'fetchDisplayLogs',
      ]),
      ...mapMutations('operation/materialManage', [
        'setMaterialLogs',
        'setDisplayLogs',
      ]),
      /**
       * 收下操作都是围绕场景字段
       * 管理'materialLogs': fetchMaterialLogs
       * 画面'displayLogs': fetchPictureLogs
       */
      async featchLogs () {
        // 此对象属性名为传入的场景名
        const logSceneActions = {
          materialLogs: this.fetchMaterialLogs,
          displayLogs: this.fetchDisplayLogs
        }
        const params = { id: this.recordId }
        // 请求[scene]对应的接口
        await logSceneActions[this.scene](params)
        this.update = this.update + 1 // 刷新日志显示组件
      },
      closeModal () {
        // 此对象属性名为传入的场景名
        const logSceneMutations = {
          materialLogs: this.setMaterialLogs,
          displayLogs: this.setDisplayLogs
        }
        // 置空场景对应日志
        logSceneMutations[this.scene]([])
        this.handleCancel()
      },
      async addLogsAsync () { // 添加日志
        if (!this.newLog.content) {
          this.$message.error('备注内容不能为空')
          return
        }
        // 此对象属性名为传入的场景名
        const logSceneActions = {
          materialLogs: this.submitMaterialLogs,
        }
        const params = {
          materialLogs: {
            managerId: this.recordId,
            content: this.newLog.content
          }
        }
        const isSuccess = await logSceneActions[this.scene](params[this.scene])
        if (!isSuccess) return
        this.newLog.content = ''
        this.featchLogs()
      },
    },
  }
</script>
