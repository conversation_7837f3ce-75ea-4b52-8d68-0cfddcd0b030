<script type="text/jsx" lang="jsx">
  import Vue, { getCurrentInstance, ref } from 'vue'
  import 'viewerjs/dist/viewer.css'
  import Viewer from 'v-viewer'
  import ImgAreaPreview from '~/components/uploader/img-area-preview'
  Vue.use(Viewer)
  export default ({ props }) => {
    const {
      logs
    } = props
    const root = getCurrentInstance().proxy
    const { $viewerApi } = root
    const viewImages = (item, index) => {
      if (root.$tnt.xtenant < 1000) {
        const { displayCurrent, displayCurrentFids } = item
        const imgs = viewPicturesArr(displayCurrent).map((d, i) => ({
          fid: displayCurrentFids[i],
          filePath: d
        }))
        root.$refs.imgAreaPreview.previewImg({
          fileList: imgs,
          index
        })
      } else {
        $viewerApi({
          images: viewPicturesArr(item.displayCurrent)
        }).view(index)
      }
    }
    const viewPicturesArr = (pictureUrlStr) => {
      return pictureUrlStr.length ? pictureUrlStr.split(',') : []
    }
    return (
      <ul class="logs-warp">
        {
          logs.map((item, index) => (
            <li key={ index }
              class={[index === logs.length - 1 ? 'last-record' : '']}>
              <div class="time">{ item.createTime }</div>
              <div>
                <h3 class="user-name">{ item.userName }</h3>
                <p class="description">
                  <span class="margin-right">{ item.content }</span>
                  { item.aiAnalysisProcess ? <span class="link" onClick={() => { root.$set(item, 'showAiAnalysis', true) }}>AI分析过程</span> : null }
                </p>
                { item.showAiAnalysis ? <div domPropsInnerHTML={item.aiAnalysisProcess?.replaceAll('\n', '<br/>')}></div> : null}
                <div>
                  {
                    item.displayCurrent && viewPicturesArr(item.displayCurrent) ? viewPicturesArr(item.displayCurrent).map((children, index) => {
                      return <img class="mr-8 mb-8 pointer" style={{ width: '100px', height: '100px' }} src={children} alt="" onClick={() => viewImages(item, index)} />
                    }) : null
                  }
                </div>
              </div>
            </li>
          ))
        }
        <ImgAreaPreview ref="imgAreaPreview"/>
      </ul>
    )
  }
</script>

<style lang="scss" scope>
  $time-width: 90px;
  $timeline-icon-border-width: 3px;
  $timeline-icon-content-size: 4px;
  $timeline-icon-size: $timeline-icon-content-size + $timeline-icon-border-width * 2;

  .logs-warp{
    margin-bottom: 16px;
    max-height: 55vh;
    overflow-y: scroll;
    li {
      padding: 10px 0 10px ($time-width + 20);
      position: relative;

      .time{
        position: absolute;
        width: $time-width;
        text-align: center;
        margin-left: -($time-width + 25);
        line-height: 1.45;
      }
      .user-name{
        font-weight: 600;
      }
      &:before,
      &:after {
        display: block;
        content: '';
        position: absolute;
        z-index: 4;
      }

      &:before {
        width: $timeline-icon-size;
        height: $timeline-icon-size;
        border-radius: 50%;
        background: #ffffff;
        border: $timeline-icon-border-width solid #239DFC;
        left: $time-width;
        top: 18px;
      }

      &:after {
        height: 100%;
        width: 1px;
        left: $time-width + 5;
        background: #239DFC;
        top: 1.5em;
        z-index: 3;
      }

      &.last-record:after {
        display: none;
      }
      .description {
        line-height: 24px;
        color: #828282;
      }
    }
  }
  .text-wrap {
    word-break: break-all;
  }
  .link {
    color: #1890ff;
    cursor: pointer;
    white-space: nowrap;
  }
</style>
