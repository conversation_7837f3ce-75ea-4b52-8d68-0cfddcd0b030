<script type='text/jsx' lang="jsx">
  import { NiImg } from '@jiuji/nine-ui'
  export default {
    name: 'picture-HoverPreview',
    props: {
      alt: {
        type: String,
        default: ''
      },
      src: {
        type: String,
        default: ''
      }
    },
    components: {
      NiImg
    },
    render () {
      return (
        <a-popover placement="top">
          <template slot="content">
            <div class='img-previwe'>
              <a href={`${this.src}.webp`} target="_bank" rel="noopener noreferrer">
                <NiImg alt={this.alt} src={ this.src } width={ 550 } height={ 489 }/>
              </a>
            </div>
          </template>
          <div class='img-wrap'>
            <NiImg alt={this.alt} src={ this.src } width={ 99 } height={ 88 }/>
          </div>
        </a-popover>
      )
    }
  }
</script>

<style lang="scss" scoped>
  $img-width: 80px;
  $img-height: 60px;
  .img-previwe{
    max-width: 480px;
    img{
      width: 100%;
      max-height: 60vh;
    }
  }
  .img-wrap{
    position: relative;
    width: $img-width;
    height: $img-height;
    margin: -8px 0;
    padding-bottom: 30px;
    overflow: hidden;
    cursor: pointer;
    img{
      position: absolute;
      width: 100%;
    }
  }
</style>
