<script lang="jsx" lang="jsx">
  import {
    statusMap,
    aiAuditsMap,
    timeoutTypesMap
  } from '../../common/data.js'
  export default ({ props }) => {
    const { value, type = 'status' } = props
    const varType = {
      status: statusMap,
      aiAudit: aiAuditsMap,
      timeout: timeoutTypesMap
    }
    const statuStr = varType[type].get(value)
    const styleClass = (type || '') + (value || '')
    return <i class={styleClass}>{ statuStr }</i>
  }
</script>

<style lang="scss">
  @import '../../common/var.scss';

  @mixin status($color){
    white-space: nowrap;
    color: $color;
  }
  @each $statu, $color in $status-colors{
    .status#{$statu}{
      @include status($color);
    };
  }
  @each $statu, $color in $ai-status-colors{
    .aiAudit#{$statu}{
      @include status($color);
    };
  }
  @each $statu, $color in $timeout-status-colors{
    .timeout#{$statu}{
      @include status($color);
    };
  }
</style>
