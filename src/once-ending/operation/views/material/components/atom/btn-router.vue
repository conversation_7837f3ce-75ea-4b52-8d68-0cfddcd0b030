<script lang="jsx" lang="jsx">
  export default ({ props, data }) => {
    const {
      routerName, // String
      text, // String
      params, // Object
      type = 'link', // String
      target = ''
    } = props
    return (
      <router-link
        class={ [data.class, type] }
        target={ target }
        to={{ name: routerName, query: params }}>
        { text }
      </router-link>
    )
  }
</script>
<style lang="scss" scoped>
  $btn-padding: 15px;
  $btn-radius: 4px;
  $btn-height: 32px;
  $btn-color: #1890ff;
  $btn-text-color: #fff;

  .button{
    position: relative;
    display: inline-block;
    white-space: nowrap;
    text-align: center;
    color: $btn-text-color;
    background: $btn-color;
    cursor: pointer;
    border-radius: $btn-radius;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    height: $btn-height;
    line-height: $btn-height;
    padding: 0 $btn-padding;
  }

  a.primary{
    @extend .button;

    &:hover{
      background: darken($btn-color, 10%);
    }
  }
  a.link{
    color: $btn-color;
    white-space: nowrap;

    &:hover{
      color: darken($btn-color, 10%);
    }
  }
</style>
