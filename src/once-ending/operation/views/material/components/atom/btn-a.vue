<script lang="jsx" lang="jsx">
  export default ({ props }) => {
    const { icon, text, actions, danger, disabled } = props
    return (
      <a class={ ['action-btn', danger && 'danger', disabled && 'disabled'] }
        rel="noopener noreferrer"
        disabled={ disabled }
        onClick={ actions }>
          { icon && <a-icon type={ icon } /> }
          { text }
      </a>
    )
  }
</script>

<style lang="scss" scoped>
  $icon-size: 18px;
  $font-size: 14px;
  $color-primary: #53b6ff;
  $color-danger: #FF4949;

  .action-btn{
    display: inline-block;
    margin: 0 5px 4px;
    padding: 2px;
    color: $color-primary;
    font-size: $font-size;
    text-align: center;

    &:hover{
      color: darken($color-primary, 10%);
    }

    &.danger{
      color: $color-danger;

      &:hover{
        color: darken($color-danger, 10%);
      }
    }
    &.disabled{
      color: #bfbfbf;
    }

    .anticon {
      display: block;
      color: $color-primary;
      font-size: $icon-size;
      margin-bottom: 4px;
    }
  }

</style>
