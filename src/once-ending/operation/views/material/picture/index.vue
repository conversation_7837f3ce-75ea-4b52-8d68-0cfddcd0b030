<script type="text/jsx" lang="jsx">
  import { provide } from 'vue'
  import { createState } from './model/useState.js'

  import { NiListPage } from '@jiuji/nine-ui'
  import BtnRouter from '../components/atom/btn-router'
  import Screen from './components/screen'
  import PictureTable from './components/table'

  export default {
    name: 'picture-manage',
    components: {
      Screen,
      BtnRouter,
      PictureTable,
    },
    setup () {
      createState(provide)
    },

    render () {
      const oAuthManage = this.$store.state.userInfo.Rank?.includes('wlgl')
      return (
        <page>
          <template slot="extra">
            { oAuthManage &&
              <span>
                <BtnRouter target="_blank" routerName="OPR_MaterialManage" text="物料陈列管理" class="mr-16"/>
                <BtnRouter target="_blank" routerName="OPR_MaterialCategroy" text="物料类别管理" class="mr-16"/>
              </span>
            }
          </template>
          <NiListPage push-filter-to-location={ false }>
            <Screen/>
            <PictureTable/>
          </NiListPage>
        </page>
      )
    }
  }
</script>
<style lang='scss' scoped>
   @import '../common/style';
</style>
