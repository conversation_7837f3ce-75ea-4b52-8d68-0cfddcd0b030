
import moment from 'moment'
import { message } from 'ant-design-vue'
import { getCurrentInstance, computed } from 'vue'
import {
  REMOVE_PICTURE,
  SUBMIT_PICTURE,
  FETCH_PICTURE_DETAIL
} from '@operation/store/modules/materialManage/action-types'
import { useState } from './useState.js'
import useActions from './useActions'

export default function useEditPicture () {
  const instance = getCurrentInstance().proxy
  const {
    state,
    setPictureFormIsShow,
    resetPictureForm,
    setIsEdit,
    setPictureForm
  } = useState()
  const { fetchData } = useActions()

  // 编辑画面
  const editPicture = (id) => {
    setPictureFormIsShow(true)
    setIsEdit(!!id)
    state.isEdit ? fetchDetail(id) : resetPictureForm()
  }
  const cancelSavePicture = () => {
    setPictureFormIsShow(false)
    resetPictureForm()
  }
  const fetchDetail = async (id) => {
    const isSuccess = await instance.$store.dispatch(
      `operation/materialManage/${FETCH_PICTURE_DETAIL}`,
      { id }
    )
    if (!isSuccess) return
    const pictureDetail = computed(() => instance.$store.state.operation.materialManage.pictureDetail).value
    pictureDetail.materialType = pictureDetail.materialType || undefined
    pictureDetail.specialAreaId = pictureDetail.specialAreaId ? pictureDetail.specialAreaId?.split(',')?.map(it => +it) : undefined
    setPictureForm(pictureDetail)
  }
  // 添加、修改画面--> 保存
  const savePicture = async () => {
    const {
      formId,
      name,
      width,
      height,
      standardAttachments,
      expireTime,
      categoryId,
      ppid,
      frameType,
      factoryGuidePriceUnderline,
      standardContent,
      materialType,
      specialAreaId
    } = state.pictureForm
    // 新增状态要验证物料类别，修改状态没有类别categoryId
    if (!state.isEdit && !categoryId) {
      message.error('请选择物料类别')
      return
    }
    if (!formId) {
      message.error('请选择物料形式')
      return
    }
    if (!name) {
      message.error('请填写物料名称')
      return
    }
    if (!standardAttachments?.length) {
      message.error('请上传陈列标准')
      return
    }
    if (ppid && !frameType) {
      message.error('请选择物料画面类型')
      return
    }
    // else if (instance.$tnt.xtenant === 0 && frameType === 2 && !factoryGuidePriceUnderline) {
    //   message.error('请选择厂家指导价')
    //   return
    // }
    // 这里做九机的物料类型  单选，必填
    if (instance.$tnt.xtenant === 0 && !materialType) {
      message.error('请选择物料类型')
      return
    }
    let paramsFormat = {}
    if (expireTime)paramsFormat.expireTime = moment(expireTime).format('yyyy-MM-DD') + ' 23:59:59'
    const params = { ...state.pictureForm, ...paramsFormat }
    params.specialAreaId = specialAreaId ? specialAreaId.join(',') : undefined

    // 处理组件与原始图片的兼容
    // params.standardFid = standard[0].filePath || standard[0]?.response?.data?.filePath
    params.standardAttachments = standardAttachments && standardAttachments.length ? standardAttachments.map(item => {
      return { fid: item.fid, fileName: item.fileName, filePath: item.fileUrl || item.filePath }
    }) : []
    delete params.enclosureFid
    delete params.standard
    if (width && !height) params.width = null
    if (!width && height) params.height = null
    if (!standardContent) params.standardContent = null
    console.log(params)
    const isSuccess = await instance.$store.dispatch(
      `operation/materialManage/${SUBMIT_PICTURE}`,
      params
    )
    if (!isSuccess) return
    const messageStr = state.isEdit ? '画面修改成功' : '画面添加成功'
    message.success(messageStr)
    setPictureFormIsShow(false)
    fetchData()
  }
  // 删除画面
  const removePicture = async (id) => {
    const isSuccess = await instance.$store.dispatch(
      `operation/materialManage/${REMOVE_PICTURE}`,
      { id }
    )
    if (!isSuccess) return
    fetchData()
  }

  return {
    editPicture,
    cancelSavePicture,
    savePicture,
    removePicture
  }
}
