
import { message } from 'ant-design-vue'
import moment from 'moment'
import { getCurrentInstance, computed } from 'vue'
import {
  SUBMIT_DISPLAY,
  FETCH_DISPLAY_LIST,
  REMOVE_DISPLAY,
  IMPORT_AREA_IDS,
  IMPORT_DISPLAY_IDS, SUBMIT_DISPLAY_REPLACE
} from '@operation/store/modules/materialManage/action-types'
import { useState } from './useState.js'
import useActions from './useActions'
import api from '@operation/api'

// 【陈列】增、删、改、查
export default function useEditDisplay () {
  const instance = getCurrentInstance().proxy
  const {
    state,
    setFormVsframeId,
    setDisplayForm,
    setDisplayIsShow,
    setDisplayFormIsShow,
    setDisplayScreenParams,
    resetDisplayForm,

    setEditingKey,
    setModalLogsIsShow,
    setCurrentLogsId,
    setDataList,
    setCacheDataList
  } = useState()
  const { fetchData } = useActions()
  // 列表操作--【添加陈列】
  const addDisplay = (record) => {
    setFormVsframeId(record.id, record.formId)
    setDisplayFormIsShow(true)
  }
  // 列表操作--【查看陈列】
  const showDisplay = (record) => {
    const { codeList } = state.screenForm
    const params = { ...record, codeList }
    setDisplayScreenParams(params)
    // 编辑列表内陈列时用
    setFormVsframeId(record.id, record.formId)
    setDisplayIsShow(true)
  }

  // 关闭模态框--【添加陈列】vs【陈列列表】
  const closeDisplayModal = () => {
    setDisplayIsShow(false)
    setDisplayFormIsShow(false)
    // 清空陈列表单
    resetDisplayForm()
    setFormVsframeId()
    state.displayFlag = 1
  }

  const closecirculateVis = () => {
    state.circulateVis = false
    state.circulateVisId = undefined
    state.circulateInfo.periodType = undefined
    state.circulateInfo.periodValues = []
    state.circulateInfo.periodEndHour = undefined
  }

  const submitCirculateVis = () => {
    if (state.circulateInfo.periodType && !state.circulateInfo.periodEndHour) {
      message.error('请选择循环陈列反馈截止时间')
      return
    }
    if ([2, 3].includes(state.circulateInfo.periodType) && !state.circulateInfo.periodValues.length) {
      message.error('请选择循环陈列周期')
      return
    }
    const params = {
      ...state.circulateInfo,
      materialDpId: state.circulateVisId
    }
    instance.$indicator.open()
    api.materialManage.submitDisplayPeriodSetting(params).then(res => {
      if (res.code === 0) {
        instance.$message.success('编辑成功')
        closecirculateVis()
        getDisplayList()
      }
    }).finally(() => {
      instance.$indicator.close()
    })
  }

  // 添加、修改陈列--> 保存
  const saveDisplay = async () => {
    if (state.displayFlag === 2) {
      saveDisplayReplace()
    } else {
      const {
        areaIds,
        code,
        endTime,
        description
      } = state.displayForm
      if (!areaIds?.length) {
        message.error('请选择陈列区域')
        return
      }
      if (!code) {
        message.error('请选择物料编号')
        return
      }
      if (!description) {
        message.error('陈列标准说明不能为空')
        return
      }
      if (!endTime) {
        message.error('请选择反馈截止时间')
        return
      }
      if (instance.$tnt.xtenant < 1000 && state.displayFlag === 1) {
        console.log('state.displayForm', state.displayForm)
        if (state.displayForm.periodType && !state.displayForm.periodEndHour) {
          message.error('请选择循环陈列反馈截止时间')
          return
        }
        if ([2, 3].includes(state.displayForm.periodType) && !state.displayForm.periodValues.length) {
          message.error('请选择循环陈列周期')
          return
        }
      }
      const params = {
        ...state.displayForm,
        frameId: state.formVsframeId.frameId,
        endTime: moment(endTime).format('YYYY-MM-DD HH:mm:ss'),
      }
      delete params.areas
      delete params.editable
      console.log('params', params)
      params.areaIds = typeof areaIds === 'string' ? [areaIds] : areaIds
      const isSuccess = await instance.$store.dispatch(
        `operation/materialManage/${SUBMIT_DISPLAY}`,
        params
      )
      if (!isSuccess) return
      setDisplayFormIsShow(false)
      resetDisplayForm()
      fetchData()
      return true
    }
  }
  const saveDisplayReplace = async () => {
    const {
      code,
      endTime,
      description,
      displayIds
    } = state.displayForm
    if (!displayIds) {
      message.error('请输入物料陈列ID')
      return
    }
    if (!description) {
      message.error('陈列标准说明不能为空')
      return
    }
    if (!endTime) {
      message.error('请选择反馈截止时间')
      return
    }
    const params = {
      displayIds,
      description,
      frameId: state.formVsframeId.frameId,
      endTime: moment(endTime).format('YYYY-MM-DD HH:mm:ss'),
    }
    params.displayIds = params.displayIds ? params.displayIds.split(',') : []

    const isSuccess = await instance.$store.dispatch(
      `operation/materialManage/${SUBMIT_DISPLAY_REPLACE}`,
      params
    )
    if (!isSuccess) return
    setDisplayFormIsShow(false)
    resetDisplayForm()
    fetchData()
    return true
  }

  // 陈列列表模态框--陈列列表数据
  const getDisplayList = async () => {
    const {
      id,
      codeList
    } = state.displayScreenParams
    const params = { frameId: id }
    if (codeList?.length) params.codeList = codeList
    const isSuccess = await instance.$store.dispatch(
      `operation/materialManage/${FETCH_DISPLAY_LIST}`,
      params
    )
    if (!isSuccess) return
    const storeDisplayList = computed(() => instance.$store.state.operation.materialManage.displayList).value
    const dataList = storeDisplayList.map(item => {
      return {
        ...item,
        showAll: false,
      }
    })
    const cacheDataList = state.dataList.map(item => ({ ...item }))
    setDataList(dataList)
    setCacheDataList(cacheDataList)
  }
  // 陈列列表模态框内列表操作--【编辑】
  const handleEdit = (id) => {
    setEditingKey(id)
    const newData = [...state.dataList]
    const target = newData.find(item => id === item.id)
    if (target) {
      target.editable = true
      setDisplayForm({
        ...state.displayForm,
        ...target
      })
      setDataList(newData)
    }
  }
  const handleCirculate = async (id) => {
    state.circulateVis = true
    state.circulateVisId = id
    await getDisplayPeriodSettingDetail()
  }
  const getDisplayPeriodSettingDetail = () => {
    instance.$indicator.open()
    const params = {
      materialDpId: state.circulateVisId
    }
    api.materialManage.getDisplayPeriodSettingDetail(params).then(res => {
      if (res.code === 0) {
        const {
          periodType,
          periodValues,
          periodEndHour
        } = res.data
        state.circulateInfo.periodType = periodType
        state.circulateInfo.periodValues = periodValues
        state.circulateInfo.periodEndHour = periodEndHour
      } else {
        instance.$message.error(res.userMsg)
      }
    }).finally(() => {
      instance.$indicator.close()
    })
  }
  // 陈列列表模态框内列表操作--【取消保存】
  const handleCancel = (id) => {
    const newData = [...state.dataList]
    const target = newData.filter(item => id === item.id)[0]
    setEditingKey()
    if (target) {
      Object.assign(target, state.cacheDataList.filter(item => id === item.id)[0])
      delete target.editable
      setDataList(newData)
    }
    // 清空陈列表单
    resetDisplayForm()
  }
  // 陈列列表模态框内列表操作--【保存】
  const handleSave = async (id) => {
    const newData = [...state.dataList]
    const target = newData.find(item => id === item.id)
    if (target) {
      delete target.editable
      const isSuccess = await saveDisplay()
      if (!isSuccess) return
      getDisplayList()
    }
    setEditingKey()
  }
  // 陈列列表模态框内列表操作--【查看日志】
  const showLogs = (id) => {
    setModalLogsIsShow(true)
    setCurrentLogsId(id)
  }
  // 陈列列表模态框内列表操作--【删除】
  const handleRemoveDisplay = async (id) => {
    const isSuccess = await instance.$store.dispatch(
      `operation/materialManage/${REMOVE_DISPLAY}`,
      { id }
    )
    if (isSuccess) getDisplayList()
  }

  const importFile = function (type) {
    instance.$refs.fileRef.click()
  }

  const importFilePicture = () => {
    instance.$refs.filePictureRef.click()
  }

  const selectFile = async function (e) {
    const file = e.target.files[0]
    if (file && file.length === 0) return
    const regExcel = /.(xls|xlsx)$/i
    if (!regExcel.test(file.name)) {
      this.$message.error('仅支持导入Excel格式的文件')
      return
    }
    const formData = new FormData()
    formData.append('file', file)
    try {
      const res = await instance.$store.dispatch(`operation/materialManage/${IMPORT_AREA_IDS}`, formData)
      if (res) {
        message.success('导入成功')
        const { areaIds } = state.displayForm
        state.displayForm.areaIds = Array.isArray(areaIds) ? areaIds.concat(res.data) : res.data
      }
    } catch (e) {
      message.error('导入失败')
    } finally {
      e.target.value = ''
    }
  }

  const selectPictureFile = async (e) => {
    const file = e.target.files[0]
    if (file && file.length === 0) return
    const regExcel = /.(xls|xlsx)$/i
    if (!regExcel.test(file.name)) {
      this.$message.error('仅支持导入Excel格式的文件')
      return
    }
    const formData = new FormData()
    formData.append('file', file)
    try {
      const res = await instance.$store.dispatch(`operation/materialManage/${IMPORT_DISPLAY_IDS}`, formData)
      if (res) {
        console.log(res.data)
        message.success('导入成功')
        state.displayForm.displayIds = res.data && res.data.length ? res.data.join(',') : ''
      }
    } catch (e) {
      message.error('导入失败')
    } finally {
      e.target.value = ''
    }
  }

  function downModel () {
    window.location.href = 'https://img2.ch999img.com/newstatic/25800/0840f3326be42fdf.xlsx'
  }

  const downModelPicture = () => {
    window.location.href = 'https://img2.ch999img.com/newstatic/54454/120bc2503e4016ad.xlsx'
  }
  const chenLieGroupChange = () => {
    state.displayForm.displayIds = undefined
    state.displayForm.areaIds = undefined
  }
  const cycleClick = (val) => {
    if (state.displayForm.periodType === val) {
      state.displayForm.periodType = undefined
    }
  }
  const cycleClickTmd = (val) => {
    if (state.circulateInfo.periodType === val) {
      state.circulateInfo.periodType = undefined
    }
  }
  const cycleChange = (e) => {
    state.displayForm.periodValues = []
    state.displayForm.periodEndHour = undefined
  }
  const cycleChangeTmd = (e) => {
    state.circulateInfo.periodValues = []
    state.circulateInfo.periodEndHour = undefined
  }
  return {
    showDisplay,
    closeDisplayModal,
    closecirculateVis,
    submitCirculateVis,
    addDisplay,
    saveDisplay,
    showLogs,
    handleEdit,
    handleCirculate,
    handleCancel,
    getDisplayList,
    handleSave,
    handleRemoveDisplay,
    importFile,
    importFilePicture,
    selectFile,
    selectPictureFile,
    downModel,
    downModelPicture,
    chenLieGroupChange,
    cycleClick,
    cycleChange,
    cycleClickTmd,
    cycleChangeTmd
  }
}
