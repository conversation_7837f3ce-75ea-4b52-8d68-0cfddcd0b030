
import moment from 'moment'
import { computed, getCurrentInstance } from 'vue'
import {
  FETCH_PICTURE_LIST,
} from '@operation/store/modules/materialManage/action-types'
import {
  SET_FORM_OPTIONS,
  SET_FORM_MAXCOUNT
} from '@operation/store/modules/materialManage/mutation-types'
import { useState } from './useState.js'
// 【画面管理】列表Fn
export default function useActions () {
  const instance = getCurrentInstance().proxy
  const {
    state,
    resetScreenForm,
    setPagination,
    setQueryParams,
  } = useState()

  const handleReset = () => {
    resetScreenForm()
    instance.$store.commit(`operation/materialManage/${SET_FORM_OPTIONS}`) // 重置物料形式
    instance.$store.commit(`operation/materialManage/${SET_FORM_MAXCOUNT}`) // 重置物料编号
    fetchData(1)
  }
  const handleTableChange = (pagination) => {
    setPagination(pagination)
    fetchData()
  }
  const fetchData = async (currentPage) => {
    if (currentPage) setPagination(currentPage, 'current')
    const {
      categoryId,
      formId,
      codes,
      nameSelect,
      areaIds,
      bindDisplay,
      zone,
      timeValue,
      materialType
    } = state.screenForm
    const { current, pageSize } = state.pagination
    const params = { current, size: pageSize, zone }
    if (areaIds?.length) params.areaIds = areaIds
    if (categoryId) params.categoryId = categoryId
    if (bindDisplay !== undefined) params.bindDisplay = !!bindDisplay
    if (nameSelect) params.name = nameSelect.label // 改为全模糊搜索
    if (formId?.length) {
      params.formIds = formId
      delete params.categoryId
    }
    if (codes) {
      const codesTostr = codes.replace(/[\uff0c]/g, ',')
      params.codeList = codesTostr.split(',').map(item => (Math.abs(item)))
    }
    if (timeValue) {
      params.startTime = moment(timeValue[0]).format('yyyy-MM-DD') + ' 00:00:00'
      params.endTime = moment(timeValue[1]).format('yyyy-MM-DD') + ' 23:59:59'
    }

    if (instance.$tnt.xtenant < 1000) {
      params.materialType = materialType
    }

    setQueryParams(params)
    const isSuccess = await instance.$store.dispatch(
      `operation/materialManage/${FETCH_PICTURE_LIST}`,
      params
    )
    if (!isSuccess) return
    const pictureTotal = computed(() => instance.$store.state.operation.materialManage.pictureTotal).value || 0
    setPagination(pictureTotal, 'total')
  }

  return {
    handleReset,
    fetchData,
    handleTableChange,
  }
}
