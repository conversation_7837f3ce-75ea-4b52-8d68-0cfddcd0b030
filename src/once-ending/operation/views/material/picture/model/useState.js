import { reactive, inject, getCurrentInstance } from 'vue'
import { cloneDeep } from 'lodash'

const key = Symbol('picture')

export function useState () {
  return inject(key)
}

export function createState (provide) {
  const root = getCurrentInstance().proxy
  const { $tnt } = root
  const state = reactive({
    displayFlag: 1,
    isEdit: true, // 添加物料弹出框是否为编辑状态【新增与编辑】
    logs: null,
    // 画面管理页面查询、列表
    screenFormReset: null, // 重置数据
    screenForm: {
      timeType: 1,
      timeValue: null,

      categoryId: undefined,
      formId: [],
      codes: '', // 需转化codeList
      // codeList: undefined,
      nameSelect: undefined,
      areaIds: [],
      bindDisplay: undefined,
      zone: undefined,
      materialType: undefined,
    },
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      hideOnSinglePage: true,
      pageSizeOptions: ['10', '20', '50', '100'],
      showSizeChanger: true,
      showTotal: total => `总共 ${total} 条`,
    },
    // 【画面】添加、编辑Form
    pictureFormIsShow: false,
    pictureFormReset: null,
    pictureForm: {
      categoryId: undefined,
      formId: undefined,
      name: '',
      width: null,
      height: null,
      standardAttachments: [],
      standardContent: null,
      enclosureAttachments: [],
      expireTime: undefined,
      ppid: undefined,
      frameType: undefined,
      factoryGuidePriceUnderline: undefined,
      printMethod: undefined,
      specialAreaId: []
    },
    // 【陈列】添加、编辑Form
    displayFormIsShow: false,
    displayFormReset: null,
    formVsframeId: { // 单独存放
      frameId: undefined, // 画面ID
      formId: undefined,
    },
    displayForm: {
      frameId: undefined,
      areaIds: undefined,
      code: null,
      description: undefined,
      endTime: undefined,
      displayIds: undefined,
      periodType: undefined,
      periodValues: [],
      periodEndHour: undefined
    },
    // 查看【陈列】
    displayIsShow: false,
    displayScreenParams: null, // 查看陈列的查询参数
    modalLogsIsShow: false,
    currentLogsId: null,
    dataList: [], // 陈列列表
    cacheDataList: [], // 陈列列表数据暂存
    editingKey: '', // 当前编辑项
    circulateVis: false,
    circulateVisId: undefined,
    circulateInfo: {
      periodType: undefined,
      periodValues: [],
      periodEndHour: []
    }
  })
  const setObj = objKey => (val, key) => {
    if (key && typeof key === 'string') {
      state[objKey][key] = val
    } else {
      state[objKey] = val
    }
  }
  const setScreenForm = setObj('screenForm') // 筛选参数相关
  const resetScreenForm = () => { state.screenFormReset && setScreenForm(cloneDeep(state.screenFormReset)) }
  const initScreenFormReset = () => { state.screenFormReset = cloneDeep(state.screenForm) }
  const setQueryParams = (val) => { state.queryParams = val }
  const setPagination = setObj('pagination')

  const setPictureForm = setObj('pictureForm')
  const resetPictureForm = () => { state.pictureFormReset && setPictureForm(cloneDeep(state.pictureFormReset)) }
  const initPictureFormReset = () => { state.pictureFormReset = cloneDeep(state.pictureForm) }

  const setDisplayForm = setObj('displayForm')
  const resetDisplayForm = () => { state.displayFormReset && setDisplayForm(cloneDeep(state.displayFormReset)) }
  const initDisplayReset = () => { state.displayFormReset = cloneDeep(state.displayForm) }

  const setFormVsframeId = (frameId, formId) => { // 【添加陈列】与【查看陈列】操作时设置，关闭清除。不作Obj单独属性处理
    state.formVsframeId.frameId = frameId
    state.formVsframeId.formId = formId
  }
  const setLogs = val => { state.logs = val || [] }
  const setIsEdit = val => { state.isEdit = val }
  const setPictureFormIsShow = val => { state.pictureFormIsShow = val || false }
  const setDisplayFormIsShow = val => { state.displayFormIsShow = val || false }

  const setDisplayIsShow = val => { state.displayIsShow = val || false }
  const setDisplayScreenParams = val => { state.displayScreenParams = val }
  const setModalLogsIsShow = val => { state.modalLogsIsShow = val }
  const setCurrentLogsId = val => { state.currentLogsId = val }
  const setDataList = val => { state.dataList = val }
  const setCacheDataList = val => { state.cacheDataList = val }
  const setEditingKey = val => { state.editingKey = val || '' }

  const picture = {
    state,

    setScreenForm,
    resetScreenForm,
    initScreenFormReset,
    setQueryParams,
    setPagination,

    setPictureForm,
    resetPictureForm,
    initPictureFormReset,

    setDisplayForm,
    resetDisplayForm,
    initDisplayReset,

    setIsEdit,
    setFormVsframeId,
    setLogs,
    setPictureFormIsShow,
    setDisplayFormIsShow,

    setDisplayIsShow,
    setDisplayScreenParams,
    setModalLogsIsShow,
    setCurrentLogsId,
    setDataList,
    setCacheDataList,
    setEditingKey
  }
  provide(key, picture)
  return picture
}
