<template>
  <a-modal
    :maskClosable="false"
    :title="isEdit ? '修改物料画面' : '添加物料画面'"
    :width='580'
    :visible="pictureFormIsShow"
    :footer="false"
    @cancel="cancelSavePicture"
  >
    <a-form>
      <a-row>
        <a-col :span="11">
          <a-form-item v-if="!isEdit" label="物料类别" required>
            <CategroySelect
              v-model="pictureForm.categoryId"
              @change="changeCategroy"
            />
          </a-form-item>
          <p v-else class="mb-16">物料类别: {{pictureForm.categoryName}}</p>
          <a-form-item label="物料名称" required>
            <a-input
              placeholder="请输入物料名称"
              v-model="pictureForm.name"
            />
          </a-form-item>
          <a-form-item label="关联PPID">
            <a-input placeholder="请输入PPID" v-model="pictureForm.ppid" />
          </a-form-item>
          <a-form-item label="物料品牌画面" :required="!!(pictureForm.ppid)">
            <a-radio-group :options="brandOpt" v-model:value="pictureForm.frameType" @change="frameTypeChange"/>
          </a-form-item>
          <a-form-item label="文案标准(50字以内)" v-if="$tnt.xtenant >= 1000">
            <div class="textarea-wrap">
              <a-textarea
                placeholder="标准文案不得输入标点符号及空格，每行—个关键短语"
                v-model="pictureForm.standardContent"
                :rows="4"
                :maxLength='textMaxLength'
                @change='changeStandardContent'
              />
              <i class='text-num'>{{textNum}}/50</i>
            </div>
          </a-form-item>
        </a-col>
        <a-col :span="11" :offset="2">
          <a-form-item v-if="!isEdit" label="物料形式" required>
            <FormSelect
              :multiple="false"
              :categoryId="pictureForm.categoryId"
              v-model="pictureForm.formId"
              @change="changeForm"
            />
          </a-form-item>
          <p v-else class="mb-16">物料形式: {{pictureForm.formName}}</p>
           <!-- showTime -->
          <a-form-item label="物料过期时间">
            <a-date-picker
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              placeholder="不选，代表永久陈列"
              v-model="pictureForm.expireTime"
            />
          </a-form-item>
          <a-form-item v-if="$tnt.xtenant < 1000">
            <div class="flex flex-align-center">
              <span>物料印刷方式：</span>
              <a-radio-group class="flex flex-justify-end flex-child-average" v-model="pictureForm.printMethod">
                <a-radio @click="radioClick(1)" :value="1">单面</a-radio>
                <a-radio @click="radioClick(2)" :value="2">双面</a-radio>
              </a-radio-group>
            </div>
          </a-form-item>
          <a-form-item label="物料类型" required v-if="is9ji">
            <a-select placeholder="请选择" v-model="pictureForm.materialType">
              <a-select-option :value="1">营销物料</a-select-option>
              <a-select-option :value="2">展陈物料</a-select-option>
              <a-select-option :value="3">大疆物料</a-select-option>
            </a-select>
          </a-form-item>
<!--          <a-form-item label="厂家指导价" required v-if="is9ji && pictureForm.frameType === 2">-->
<!--            <a-radio-group :options="dictionary.framePosOpt" v-model="pictureForm.factoryGuidePriceUnderline"/>-->
<!--          </a-form-item>-->
          <a-form-item label="物料尺寸">
            <div class="flex">
              <a-input-number
                placeholder="宽（cm）"
                :min="0"
                v-model="pictureForm.width"
              />
              <div :style="{ width:'24px', textAlign:'center', flex:'none'}">
                X
              </div>
              <a-input-number
                placeholder="高（cm）"
                :min="0"
                v-model="pictureForm.height"
              />
            </div>
          </a-form-item>
          <a-form-item label="画面附件">
            <FileUploader
              class="material-upload"
              :moreAmount="false"
              :disabled="pictureForm.enclosureAttachments && pictureForm.enclosureAttachments.length >= 3"
              :showQrBtn="false"
              @change="files => pictureForm.enclosureAttachments = files"
              :files="pictureForm.enclosureAttachments"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-form-item label="关联陈列专区">
          <a-select
            allowClear
            placeholder="请选择"
            v-model="pictureForm.specialAreaId"
            mode="multiple"
            :maxTagCount="3"
            optionFilterProp="children"
            :options="zoneManageOptions"></a-select>
        </a-form-item>
      </a-row>
      <a-row>
        <a-form-item label="陈列标准" required>
          <UploaderImg
            :multiple="moreImage"
            :maxLen="moreImage ? 20 : 1"
            collection="oa-operate"
            @changeImg="tmdFileChange"
            :is-area-preview="true"
            :files="pictureForm.standardAttachments"/>
        </a-form-item>
      </a-row>
    </a-form>
    <div class="modal-actions">
      <a-button
        class="margin-right"
        @click="cancelSavePicture"
        >取消</a-button>
      <a-button
        type="primary"
        @click="savePicture"
        >确认</a-button>
    </div>
  </a-modal>
</template>

<script type="text/jsx">
  import { toRefs, computed, getCurrentInstance, nextTick, ref } from 'vue'
  import { useState } from '../model/useState.js'
  import useEditPicture from '../model/useEditPicture.js'

  import AreaDepartSelector from '~/components/staff/area-depart-selector-cache'
  import UploaderImg from './uploaderImg-byNine'
  import CategroySelect from '../../components/select/categroy-vs-form/categroy'
  import FormSelect from '../../components/select/categroy-vs-form/form'
  import FileUploader from '../../components/uploader.vue'
  import { dictionary } from '../../common/data'
  import { to } from '~/util/common'
  import visit from '@/operation/api/visit'
  import { message } from 'ant-design-vue'

  export default {
    components: {
      CategroySelect,
      FormSelect,
      AreaDepartSelector,
      FileUploader,
      UploaderImg,
    },
    setup () {
      const { proxy } = getCurrentInstance()
      const {
        state,
        setPictureForm
      } = useState()
      const {
        savePicture,
        cancelSavePicture
      } = useEditPicture()
      const changeArea = (ids, labels) => {
        console.log('选择地区', ids, labels)
      }
      const changeCategroy = val => {
        setPictureForm(val, 'categoryId')
      }
      const changeForm = val => { setPictureForm(val, 'formId') }
      const changeStandardContent = e => {
        const value = e.target.value.replace(/[ ]/g, '')
        setPictureForm(value, 'standardContent')
      }

      const radioClick = function (val) {
        nextTick(() => {
          if (state.pictureForm.printMethod && val === state.pictureForm.printMethod) {
            state.pictureForm.printMethod = undefined
          }
        })
      }
      const tmdFileChange = (files) => {
        state.pictureForm.standardAttachments = files
        console.log(state.pictureForm.standardAttachments)
      }
      const frameTypeChange = function (val) {
        if (!moreImage.value) {
          state.pictureForm.standardAttachments = state.pictureForm.standardAttachments.slice(0, 1)
        }
      }

      const zoneManageOptions = ref([])
      const getZoneManageOptions = async function () {
        const [err, res] = await to(visit.getZoneManageOptions())
        if (err) throw err
        const { code, data, userMsg } = res
        if (code === 0) {
          zoneManageOptions.value = data
        } else {
          message.error(userMsg)
        }
      }
      getZoneManageOptions()
      const moreImage = computed(() => {
        const { frameType } = state.pictureForm
        return (proxy.$tnt.xtenant < 1000 && frameType === 2) || !frameType
      })

      return {
        ...toRefs(state),
        is9ji: computed(() => proxy.$tnt.xtenant === 0),
        brandOpt: computed(() => proxy.$tnt.xtenant === 0 ? dictionary.brandOptions9ji : dictionary.brandOptions),
        textMaxLength: computed(() => {
          // 50字+折行的长度
          const str = state.pictureForm.standardContent || '' // 预防0，undefined与null的情况
          const num = 50 + (str.split('\n').length - 1)
          return num
        }),
        textNum: computed(() => {
          const str = state.pictureForm.standardContent
          // 去掉折行的长度
          return str?.replaceAll('\n', '').length ?? 0
        }),
        dictionary,
        changeArea,
        changeCategroy,
        changeForm,
        savePicture,
        cancelSavePicture,
        changeStandardContent,
        radioClick,
        tmdFileChange,
        frameTypeChange,
        zoneManageOptions,
        moreImage
      }
    }
  }
</script>

<style lang="scss" scoped>
:deep(.material-upload){
  .upload-btn{
    width: 100%;
  }
}
.uploader{
  margin-top: -16px;
  width: 50%;
}
.block-title{
  font-size: 1.2em;
  font-weight: 600;
  margin: 0.8em 0;
}
.modal-actions{
  width:170px;
  margin: 24px auto 0;
  display: flex;
  justify-content: space-between;
}
.textarea-wrap{
  .text-num {
    position: absolute;
    bottom: -1.2em;
    right: 0;
    font-size: 12px;
    line-height: 1.45;
    color: #999;
  }
}
.form-col-2{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  ul{
    width: 100%;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    li{
      margin-bottom: 16px;
      width: 45%;
    }
  }

  .ant-form-item{
    width: 45%;
  }

}
:deep(.ant-form-item-label){
  line-height: 1.7;
}
.ant-form-item{
  margin-bottom: 16px;
}
.ant-input-number,
.ant-calendar-picker{
  width: 100%;
}
</style>
