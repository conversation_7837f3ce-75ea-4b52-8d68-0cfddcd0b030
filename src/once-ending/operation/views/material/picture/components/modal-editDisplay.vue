<template>
  <a-modal
    :maskClosable="false"
    title="添加陈列"
    :width='700'
    :visible="displayFormIsShow"
    :footer="false"
    @cancel="closeDisplayModal"
  >
    <a-form>
      <a-form-item v-if="$tnt.xtenant < 1000">
        <a-radio-group name="chenLieGroup" v-model="displayFlag" @change="chenLieGroupChange">
          <a-radio :value="1">新增陈列</a-radio>
          <a-radio :value="2">替换陈列</a-radio>
        </a-radio-group>
      </a-form-item>
        <a-form-item required v-if="$tnt.xtenant >= 1000 || ($tnt.xtenant < 1000 && displayFlag === 1)">
          <span slot="label">
            <a-tooltip title="指这个物料可以让哪些门店陈列，在门店添加陈列时有限制作用">
              <a-icon class="blue" type="question-circle-o" />
            </a-tooltip>
            陈列区域
          </span>
          <div class="flex flex-align-center flex-nowrap">
          <area-depart-selector
            treeCheckable
            placeholder="请选择陈列区域或门店"
            showType="SHOW_CHILD"
            treeNodeFilterProp="label"
            @change="changeArea"
            :areaType="1"
            :value="displayForm.areaIds"/>
            <a-button type="link" class="button-action" @click="importFile">导入陈列区域</a-button>
            <a-button type="link" class="button-action" @click="downModel">下载模板</a-button>
          </div>
        </a-form-item>
      <a-form-item required v-if="$tnt.xtenant < 1000 && displayFlag === 2">
        <span slot="label">
          <a-tooltip title="此功能用于批量替换当前已陈列的物料画面">
            <a-icon class="blue" type="question-circle-o" />
          </a-tooltip>
          物料陈列ID
        </span>
        <div class="flex flex-align-center flex-nowrap">
          <a-input v-model="displayForm.displayIds" placeholder="多个物料陈列ID中间用英文逗号,分割" @input="idInput"/>
          <a-button type="link" class="button-action" @click="importFilePicture">导入物料陈列ID</a-button>
          <a-button type="link" class="button-action" @click="downModelPicture">下载模板</a-button>
        </div>
      </a-form-item>
      <a-form-item label="陈列标准说明" required>
        <a-textarea
          v-model="displayForm.description"
          :rows="4"/>
      </a-form-item>
      <div class="form-col-2">
        <a-form-item label="反馈截止时间" required>
          <a-date-picker
            showTime
            placeholder="选择时间"
            format="YYYY-MM-DD HH:mm:ss"
            v-model="displayForm.endTime"/>
        </a-form-item>
        <a-form-item label="物料编号" required v-if="displayFlag !== 2">
          <CodeSelect
            :value="displayForm.code"
            :formId="formVsframeId.formId"
            @change="changeCode"
          />
        </a-form-item>
      </div>
      <a-form-item v-if="$tnt.xtenant < 1000 && displayFlag === 1">
          <div class="flex flex-align-center">
            <span slot="label">
              <a-tooltip title="选择后会在对应时间的早上8点，自动给门店下发陈列">
                <a-icon class="blue" type="question-circle-o" />
              </a-tooltip>
              循环陈列周期：
            </span>
            <a-radio-group v-model="displayForm.periodType" @change="cycleChange">
              <a-radio :value="1" @click="cycleClick(1)">每天</a-radio>
              <a-radio :value="2" @click="cycleClick(2)">每周</a-radio>
              <a-radio :value="3" @click="cycleClick(3)">每月</a-radio>
            </a-radio-group>
          </div>
      </a-form-item>
      <div class="flex flex-align-center mb-10" style="margin-left: 116px" v-if="displayForm.periodType && displayForm.periodType === 2">
        <a-checkbox-group v-model="displayForm.periodValues">
          <a-checkbox :value="1">周一</a-checkbox>
          <a-checkbox :value="2">周二</a-checkbox>
          <a-checkbox :value="3">周三</a-checkbox>
          <a-checkbox :value="4">周四</a-checkbox>
          <a-checkbox :value="5">周五</a-checkbox>
          <a-checkbox :value="6">周六</a-checkbox>
          <a-checkbox :value="7">周天</a-checkbox>
        </a-checkbox-group>
      </div>
      <div class="flex flex-align-center mb-10" style="margin-left: 116px" v-if="displayForm.periodType && displayForm.periodType === 3">
        <a-select v-model="displayForm.periodValues" mode="multiple" :maxTagCount="3" style="width: 250px" placeholder="请选择" allowClear>
          <a-select-option :value="item" v-for="item in 31" :key="item">{{ item }}</a-select-option>
        </a-select>
        <span class="ml-8">号</span>
      </div>
      <div class="flex flex-align-center" v-if="displayForm.periodType">
        <span>循环陈列反馈截止时间：</span>
        <span>陈列下发后</span>
        <a-input-number
          v-model="displayForm.periodEndHour"
          style="width: 100px; margin: 0 8px;"
          placeholder="请输入"
          min="1"
          max="999999999"
        />
        <span>小时</span>
      </div>
    </a-form>
    <div class="modal-actions">
      <a-button
        class="margin-right"
        @click="closeDisplayModal"
        >取消</a-button>
      <a-button
        type="primary"
        @click="saveDisplay"
        >确认</a-button>
    </div>
    <input
      style="display: none"
      ref="fileRef"
      type="file"
      accept=".xls,.xlsx"
      @change="selectFile"
      />
    <input
      style="display: none"
      ref="filePictureRef"
      type="file"
      accept=".xls,.xlsx"
      @change="selectPictureFile"
    />
  </a-modal>
</template>

<script type="text/jsx">
  import { toRefs, ref } from 'vue'
  import { useState } from '../model/useState.js'
  import useEditDisplay from '../model/useEditDisplay.js'

  import AreaDepartSelector from '~/components/staff/area-depart-selector-cache'
  import CodeSelect from '../../components/select/code'

  export default {
    components: {
      AreaDepartSelector,
      CodeSelect,
    },
    setup () {
      const {
        state,
        setDisplayForm,
      } = useState()
      const {
        saveDisplay,
        closeDisplayModal,
        importFile,
        importFilePicture,
        selectFile,
        selectPictureFile,
        downModel,
        downModelPicture,
        fileRef,
        filePictureRef,
        chenLieGroupChange,
        cycleClick,
        cycleChange
      } = useEditDisplay()

      const changeArea = (ids, labels) => {
        setDisplayForm(ids, 'areaIds')
      }
      const changeCode = val => {
        setDisplayForm(val, 'code')
      }
      const idInput = () => {
        state.displayForm.displayIds = state.displayForm.displayIds.replace(/，/g, ',')
      }
      return {
        ...toRefs(state),
        idInput,
        changeArea,
        changeCode,
        saveDisplay,
        closeDisplayModal,
        importFile,
        importFilePicture,
        selectFile,
        selectPictureFile,
        downModel,
        downModelPicture,
        fileRef,
        filePictureRef,
        chenLieGroupChange,
        cycleClick,
        cycleChange
      }
    },
  }
</script>

<style lang="scss" scoped>

.uploader{
  margin-top: -16px;
  width: 50%;
}
.block-title{
  font-size: 1.2em;
  font-weight: 600;
  margin: 0.8em 0;
}
.modal-actions{
  width:170px;
  margin: 24px auto 0;
  display: flex;
  justify-content: space-between;
}
.form-col-2{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  ul{
    width: 100%;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    li{
      margin-bottom: 16px;
      width: 45%;
    }
  }

  .ant-form-item{
    width: 45%;
  }

}
:deep(.ant-form-item-label){
  line-height: 1.7;
}
.ant-form-item{
  margin-bottom: 16px;
}
.ant-input-number,
.ant-calendar-picker{
  width: 100%;
}
.button-action {
  padding: 0px;
  margin-left: 16px;
}
</style>
