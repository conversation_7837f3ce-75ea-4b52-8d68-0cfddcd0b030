<script type="text/jsx" lang="jsx">
  import { onMounted, toRefs } from 'vue'
  import { useState } from '../model/useState.js'
  import useEditDisplay from '../model/useEditDisplay.js'

  import AreaDepartSelector from '~/components/staff/area-depart-selector-cache'
  import BtnA from '../../components/atom/btn-a'
  import CodeSelect from '../../components/select/code'
  import ModalLogs from '../../components/logs/modal-logs'
  import { dictionary } from '../../common/data'

  export default {
    components: {
      BtnA,
      AreaDepartSelector,
      CodeSelect,
      ModalLogs
    },
    setup () {
      const {
        state,
        setDisplayForm,
        setModalLogsIsShow,
      } = useState()
      const {
        getDisplayList,
        showLogs,
        handleSave,
        handleEdit,
        handleCirculate,
        handleCancel,
        handleRemoveDisplay,
        closeDisplayModal,
        closecirculateVis,
        submitCirculateVis,
        cycleChangeTmd,
        cycleClickTmd,
      } = useEditDisplay()

      const changeArea = (ids, labels) => {
        setDisplayForm(ids, 'areaIds')
      }
      const changeCode = val => {
        setDisplayForm(val, 'code')
      }

      onMounted(() => {
        getDisplayList()
      })

      return {
        dictionary,
        ...toRefs(state),
        changeArea,
        changeCode,
        setModalLogsIsShow,
        closeDisplayModal,
        closecirculateVis,
        submitCirculateVis,
        showLogs,
        handleSave,
        handleEdit,
        handleCirculate,
        handleCancel,
        handleRemoveDisplay,
        cycleChangeTmd,
        cycleClickTmd,
      }
    },
    data () {
      return {
        columns: [
          {
            dataIndex: 'areas',
            key: 'areas',
            title: '陈列区域',
            width: '30%',
            customRender: (text, record) => {
              return <div class='pr-clear'>
                {
                  record.editable
                    ? <AreaDepartSelector treeCheckable
                        placeholder="请选择陈列区域或门店"
                        showType="SHOW_CHILD"
                        treeNodeFilterProp="label"
                        areaType={1}
                        onChange={ this.changeArea }
                        value={ this.displayForm.areaIds }/>
                    : <div class='ellipsis-text'>
                        <p class={ record.showAll ? '' : 'sEllipsis'}>
                          {
                            text.map(item => <i class="area-item">{ item }</i>)
                          }
                        </p>
                        {
                          text?.length > 12
                          ? !record.showAll
                              ? <BtnA text="全部" actions={ () => { record.showAll = true } }/>
                              : <BtnA text="收起" actions={ () => { record.showAll = false } }/>
                          : null
                        }
                      </div>
                }
              </div>
            }
          },
          {
            dataIndex: 'code',
            key: 'code',
            title: '物料编号',
            width: '10%',
            customRender: (text, record, index) => {
              return <div class='table-code'>
              { record.editable
                  ? <CodeSelect
                      value={ this.displayForm.code }
                      onChange={ this.changeCode }
                      formId={ this.formVsframeId.formId }
                    />
                  : { text }
              }
              </div>
            }
          },
          {
            dataIndex: 'description',
            key: 'description',
            title: '陈列标准说明',
            width: '28%',
            customRender: (text, record, index) => {
              return <div>
              { record.editable
                  ? <a-textarea
                      value={ this.displayForm.description }
                      auto-size={{ minRows: 2, maxRows: 6 }}
                      onChange={ e => {
                        this.displayForm.description = e.target.value
                      } }
                    />
                  : { text }
              }
              </div>
            }
          },
          {
            dataIndex: 'endTime',
            key: 'endTime',
            title: '反馈截止时间',
            width: '13%',
            customRender: (text, record) => {
              return <div>
              { record.editable
                  ? <a-date-picker
                      showTime
                      placeholder="选择时间"
                      format="YYYY-MM-DD HH:mm:ss"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      value={ this.displayForm.endTime }
                      onChange={ e => {
                        this.displayForm.endTime = e
                      } }/>
                  : <i>{ text || '-' }</i>
              }
              </div>
            }
          },
          {
            dataIndex: 'actions',
            key: 'actions',
            title: '操作',
            width: '18%',
            customRender: (text, record, index) => {
              const title = (
                <p style="width:200px">
                  <h4>是否确定删除该物料?</h4>
                  删除物料之后，当前所有陈列该物料店面物料审核数据都会被删除"
                </p>
              )
              const { editingKey } = this
              const icon = <a-icon type="question-circle-o" style="color: red" />
              return (
                <div>
                  {
                    record.editable
                      ? <div>
                          <BtnA
                            text="保存"
                            actions={ () => { this.handleSave(record.id) } }/>
                          <a-popconfirm title="确定取消?" onConfirm={ () => this.handleCancel(record.id) }>
                            <BtnA text="取消"/>
                          </a-popconfirm>
                        </div>
                      : <div>
                          <BtnA
                            text="编辑"
                            disabled={ editingKey !== '' }
                            actions={ () => { this.handleEdit(record.id) } }
                          />
                        { this.$tnt.xtenant < 1000 ? <BtnA
                          text="循环陈列"
                          actions={ () => { this.handleCirculate(record.id) } }
                        /> : null }
                          <BtnA
                            text="日志"
                            class="yellow"
                            actions={ () => { this.showLogs(record.id) } }/>
                          <a-popconfirm
                            title= { title }
                            okText="删除"
                            cancelText="取消"
                            okType="danger"
                            icon={ icon }
                            onConfirm={ () => { this.handleRemoveDisplay(record.id) } }
                          >
                            <a rel="noopener noreferrer" class="red mr-10">删除</a>
                          </a-popconfirm>
                        </div>
                  }
                </div>
              )
            }
          },
        ],
      }
    },
    render () {
      const {
        circulateVis,
        displayIsShow,
        columns,
        dataList,
        currentLogsId,
        modalLogsIsShow,
        setModalLogsIsShow,
        closeDisplayModal,
        circulateInfo,
        closecirculateVis,
        submitCirculateVis,
        cycleChangeTmd,
        cycleClickTmd
      } = this
      const isLoading = this.$store.state.operation.materialManage.isLoading
      return (<div>
        <a-modal
          maskClosable={false}
          title="陈列列表"
          width={1500}
          zIndex={1000}
          visible={displayIsShow}
          footer={false}
          onCancel={closeDisplayModal}
        >
          <div class="mt-20 data-display">
            <a-table
              rowKey={(record, index) => index}
              columns={columns}
              dataSource={dataList}
              loading={isLoading}
            />
            {
              modalLogsIsShow && <ModalLogs
                visible={modalLogsIsShow}
                recordId={currentLogsId}
                scene="displayLogs"
                showAddLogs={false}
                handleCancel={() => {
                  setModalLogsIsShow(false)
                }}
              />
            }
          </div>
        </a-modal>
        <a-modal
          maskClosable={false}
          title="循环陈列设置"
          width={700}
          visible={circulateVis}
          onCancel={closecirculateVis}
          onOk={submitCirculateVis}
        >
          <div class="flex flex-col">
            <div class="flex flex-align-center mb-10">
              <div>
                <a-tooltip title="选择后会在对应时间的早上8点，自动给门店下发陈列">
                  <a-icon class="blue" type="question-circle-o"/>
                </a-tooltip>
                <span class="ml-5">循环陈列周期：</span>
              </div>
              <a-radio-group v-model={circulateInfo.periodType} onChange={() => cycleChangeTmd()}>
                <a-radio value={1} onClick={() => cycleClickTmd(1)}>每天</a-radio>
                <a-radio value={2} onClick={() => cycleClickTmd(2)}>每周</a-radio>
                <a-radio value={3} onClick={() => cycleClickTmd(3)}>每月</a-radio>
              </a-radio-group>
          </div>
            {
              circulateInfo.periodType && circulateInfo.periodType === 2 ? <div class="flex flex-align-center mb-10" style={{ marginLeft: '116px' }}>
                  <a-checkbox-group v-model={circulateInfo.periodValues}>
                    <a-checkbox value={1}>周一</a-checkbox>
                    <a-checkbox value={2}>周二</a-checkbox>
                    <a-checkbox value={3}>周三</a-checkbox>
                    <a-checkbox value={4}>周四</a-checkbox>
                    <a-checkbox value={5}>周五</a-checkbox>
                    <a-checkbox value={6}>周六</a-checkbox>
                    <a-checkbox value={7}>周天</a-checkbox>
              </a-checkbox-group>
              </div> : null
            }
            {
              circulateInfo.periodType && circulateInfo.periodType === 3 ? <div class="flex flex-align-center mb-10" style={{ marginLeft: '116px' }}>
                  <a-select v-model={circulateInfo.periodValues} mode="multiple" maxTagCount={3} style={{ width: '250px' }} placeholder="请选择" allowClear>
                    {
                      [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31].map(item => {
                        return <a-select-option key={item} value={item}>{item}</a-select-option>
                    })
                    }
                  </a-select>
                <span class="ml-8">号</span>
              </div> : null
            }
            {
              circulateInfo.periodType ? <div class="flex flex-align-center">
                <span>循环陈列反馈截止时间：</span>
                <span>陈列下发后</span>
                <a-input-number
                  v-model={circulateInfo.periodEndHour}
                  style={{ width: '100px', margin: '0 8px' }}
                  placeholder="请输入"
                  min={1}
                  max={999999999}
                />
                <span>小时</span>
              </div> : null
            }
          </div>
        </a-modal>
        </div>
      )
    },
  }
</script>

<style lang="scss" scoped>
.pr-clear {
  margin-right: -24px;
}

.uploader {
  margin-top: -16px;
  width: 50%;
}

.block-title {
  font-size: 1.2em;
  font-weight: 600;
  margin: 0.8em 0;
}

.modal-actions{
  width:170px;
  margin: 24px auto 0;
  display: flex;
  justify-content: space-between;
}
.ellipsis-text {
   display: flex;
   align-items: center;
    p {
      width: 360px;
      line-height: 2;
    }
    a {
      width:2.5em
    }
 }
 .sEllipsis{
   overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
 }
.area-item{
  display: inline-block;
  margin-right: 5px;
  &:not(:last-child):after{
    content: ',';
    margin-left: 3px;
  }
}
.form-col-2{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  ul{
    width: 100%;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    li{
      margin-bottom: 16px;
      width: 45%;
    }
  }

  .ant-form-item{
    width: 45%;
  }

}
:deep(.ant-form-item-label){
  line-height: 1.7;
}
:deep(.table-code){
  .ant-select{
    width: 100%;
  }
}
.ant-form-item{
  margin-bottom: 16px;
}
.ant-input-number,
.ant-calendar-picker{
  width: 100%;
}
</style>
