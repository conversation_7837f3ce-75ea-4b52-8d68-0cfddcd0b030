<script type='text/jsx' lang="jsx">
  import { computed, onMounted } from 'vue'
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions'
  import { dictionary } from '../../common/data.js'
  import mbaSelect from '@operation/components/mba-select'
  import { NiFilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import ExcelAction from '~/pages/operation/components/excel-action'
  import CategroyVsFormSelect from '../../components/select/categroy-vs-form'
  import SearchSelectAsync from '../../components/select/search-async'

  export default {
    name: 'check-screen',
    components: {
      ExcelAction,
      CategroyVsFormSelect,
      SearchSelectAsync,
      NiFilter,
      NiFilterItem,
      NiAreaSelect,
      mbaSelect
    },
    setup () {
      const {
        state,
        setScreenForm,
        initScreenFormReset,
        initPictureFormReset,
        initDisplayReset,
      } = useState()
      const {
        handleReset,
        fetchData
      } = useActions()

      const onChangeCvsF = ({ categoryId, formId }) => {
        setScreenForm(categoryId, 'categoryId')
        setScreenForm(formId, 'formId')
      }
      const doSearch = () => {
        fetchData(1)
      }

      onMounted(() => {
        initScreenFormReset()
        initPictureFormReset()
        initDisplayReset()
        fetchData()
      })

      return {
        dictionary,
        screenForm: computed(() => state.screenForm),
        setScreenForm,
        onChangeCvsF,
        handleReset,
        doSearch
      }
    },
    render () {
      const {
        screenForm,
        setScreenForm,
        onChangeCvsF,
        addPicture,
        doSearch
      } = this
      // loading={ isLoading }
      return (
        <NiFilter
          class="relative"
          form={screenForm}
          labelWidth={90}
          layout='neat'
          onFilter={doSearch}
          immediate={false}
        >
          <ni-filter-item class='no-label'>
            <CategroyVsFormSelect
              value={{
                categoryId: screenForm.categoryId,
                formId: screenForm.formId
              }}
              onChange={onChangeCvsF}
            />
          </ni-filter-item>
          <ni-filter-item label="物料编号">
            <a-tooltip placement="top">
              <template slot="title">
                <span>输入正整数，用逗号隔开</span>
              </template>
              <a-input
                allowClear
                placeholder="物料编号"
                value={screenForm.codes}
                onChange={(e) => {
                  setScreenForm(e.target.value, 'codes')
                }}
              />
            </a-tooltip>
          </ni-filter-item>
          <ni-filter-item label="物料名称">
            <SearchSelectAsync
              scene="picture"
              form={screenForm}
            />
          </ni-filter-item>
          <ni-filter-item label="陈列区域">
            <NiAreaSelect
              multiple
              allowClear
              maxTagCount={1}
              value={screenForm.areaIds}
              onChange={ids => {
                setScreenForm(ids, 'areaIds')
              }}
            />
          </ni-filter-item>
          <ni-filter-item label="是否有陈列">
            <a-select
              allowClear
              placeholder="是否有陈列"
              options={dictionary.isBindDisplay}
              value={screenForm.bindDisplay}
              onChange={(value) => {
                setScreenForm(value, 'bindDisplay')
              }}
            />
          </ni-filter-item>
          <ni-filter-item class='no-label'>
            <a-input-group compact>
              <a-select
                style="width: 30%"
                options={dictionary.pictureTimeTypes}
                value={0}
              />
              <a-range-picker
                allowClear
                style="width: 70%"
                value={screenForm.timeValue}
                onChange={(value) => {
                  setScreenForm(value, 'timeValue')
                }}
              />
            </a-input-group>
          </ni-filter-item>
          <ni-filter-item v-show={this.$tnt.xtenant === 0}>
            <div class="nowrap" slot="label">MBA专区：</div>
            <mba-select v-model={screenForm.zone}/>
          </ni-filter-item>
          <ni-filter-item v-show={this.$tnt.xtenant < 1000}>
            <div class="nowrap" slot="label">物料类型：</div>
            <a-select placeholder="请选择" v-mode={screenForm.materialType} onChange={(value) => { setScreenForm(value, 'materialType') }} allowClear>
              <a-select-option value="1">营销物料</a-select-option>
              <a-select-option value="2">展陈物料</a-select-option>
              <a-select-option value="3">大疆物料</a-select-option>
            </a-select>
          </ni-filter-item>
          <div class="screen-actions">
            <a-button type="primary" onClick={addPicture}>
              添加物料画面
            </a-button>
          </div>
        </NiFilter>
      )
    }
  }
</script>

<style lang="scss" scoped>
@import '../../common/style';
</style>
