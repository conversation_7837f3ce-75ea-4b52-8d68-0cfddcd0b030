<template>
  <div>
    <div class="upload-list">
      <template v-if="fileList && fileList.length">
        <div class="item"
          v-for="(item, index) of fileList"
          :key="index">
          <div class="item-info" >
            <!-- 删除图标 -->
            <a @click="remove(item)"
              rel="nooppener"
              class="item-actions remove"
              title="删除图片" >
              <a-icon type="close-circle" class="opration-icon"/>
            </a>
            <a title="预览图片" @click="handlePreview(item)" rel="nooppener">
              <img :src="item.url"
                :width='preWidth'
                :height='preHeight'
                :alt="item.name"
                class="item-img"
              >
            </a>
          </div>
        </div>
      </template>
      <!-- 上传按钮 -->
      <div class="item upload-select"
        v-if="fileList.length < maxLen" @click="doUpload">
        <a-icon type="plus"/>
        <div class="ant-upload-text">上传</div>
      </div>
    </div>
    <img-area-preview :showGps="false" ref="imgAreaPreview"></img-area-preview>
  </div>
</template>

<script>
  /**
   * 尺寸验证功能
   */
  import nineUpload from '@jiuji/nine-upload'
  import ImgAreaPreview from '~/components/uploader/img-area-preview'
  export default {
    name: 'uploaderImg-byNine',
    components: { ImgAreaPreview },
    props: {
      maxLen: {
        type: [Number],
        default: 1
      },
      files: {
        type: [Array],
        default: () => []
      },
      wh: { // 宽高（如100*100）
        type: String,
        default: ''
      },
      maxSize: {
        type: Number,
        default: 10485760, // 10M
      },
      aspectRatioString: { // 宽高比字符串
        type: String,
        default: ''
      },
      multiple: { // 是否多选
        type: Boolean,
        default: false
      },
      accept: { // 是否多选
        type: String,
        default: 'image/*'
      },
      collection: {
        type: String,
        default: 'adm'
      },
      isAreaPreview: {
        type: Boolean,
        default: false
      }
    },
    data () {
      return {
        fileList: [],
        preWidth: 180,
        preHeight: 180,
      }
    },
    computed: {
      header () {
        return {
          Authorization: this.$store.state.token
        }
      }
    },
    watch: {
      files: {
        deep: true,
        immediate: true,
        handler (newValue) {
          if (newValue?.length) {
            this.handleFile(newValue)
          } else {
            this.fileList = newValue
          }
        },
      }
    },
    methods: {
      handleFile (files) {
        files.forEach(f => {
          f.uid = f.uid || f.fid
          f.name = f.name || f.fileName
          f.url = f.url || f.filePath || f.fileUrl
          f.status = 'done'
        })
        this.fileList = files
      },
      handlePreview (file) {
        let imgUrl = ''
        let fid = '1'
        if (file.response) {
          imgUrl = file.response.data.filePath + '.webp'
          fid = file.response.data.fid
        } else {
          imgUrl = file.url + '.webp'
          fid = file.fid
        }
        if (this.isAreaPreview) {
          this.$refs.imgAreaPreview.previewImg({
            fileList: [{
              fid,
              filePath: imgUrl
            }]
          })
        } else {
          window.open(imgUrl)
        }
      },
      async doUpload ({ index }) {
        const width = this.wh ? this.wh.split('*')[0] : ''
        const height = this.wh ? this.wh.split('*')[1] : ''
        let aspectRatio = 0
        let aspectRatioString = ''
        if (width && height) {
          aspectRatio = width / height
          aspectRatioString = this.aspectRatioString || this.wh
        }
        // 赋值 appId vs token
        if (!this.appId) this.fetchUploadToken()
        try {
          const { err, res } = await nineUpload({
            accept: this.accept,
            multiple: this.multiple,
            form: {
              collection: this.collection
            },
            maxSize: this.maxSize,
            aspectRatio, // 宽高比，仅可用于图片上传，0表示任一尺寸
            aspectRatioString, // 宽高比字符串，用作提示

            onPickFiles: async files => { // files是文件对象，如果要对文件进行处理，处理完成之后要在返回对象里加上files对象
              if (window.nineUploadData) {
                return window.nineUploadData
              }
              const data = this.fetchUploadToken(true)
              if (data) return data
            }
          })
          if (this.multiple) {
            if (err.length) {
              err.forEach(e => {
                this.$message.error(`${e.name}上传失败：${e.err}`)
              })
            }
          } else {
            if (err) {
              const sizeMb = this.maxSize / 1024 / 1024
              this.$message.error(`${err.name}上传失败：${err.err} (${sizeMb}Mb)`)
              return false
            }
          }
          if (res) {
            if (this.multiple) {
              const newVal = res.map(item => {
                return {
                  // 适配组件显示
                  uid: item.fid,
                  name: item.fileName,
                  url: item.fileUrl,
                  size: item.size,
                  status: 'done',
                  fid: item.fid,
                  fileName: item.fileName,
                  filePath: item.filePath,
                  fileUrl: item.fileUrl, // 适配路径的不同写法
                }
              })
              this.fileList = this.fileList.concat(...newVal)
              console.log('fileList', this.fileList)
              this.$emit('changeImg', this.fileList)
            } else {
              let obj = {
                // 适配组件显示
                uid: res.fid,
                name: res.fileName,
                url: res.fileUrl,
                size: res.size,
                status: 'done',

                fid: res.fid,
                fileName: res.fileName,
                filePath: res.filePath,
                fileUrl: res.fileUrl, // 适配路径的不同写法
              }
              this.fileList.push(obj)
              this.$emit('changeImg', this.fileList)
            }
          } else {
            this.fileList.splice(index, 1)
          }
        } catch (e) {
          this.fileList.splice(index, 1)
        }
      },
      async fetchUploadToken (isNineUpload) {
        try {
          // 获取appId和token。可以把这个appId和token缓存起来，没必要每次都请求
          const { code = 0, userMsg = '', data = { appId: '', token: '' } } = await this.$api.common.getUploadToken()
          if (code === 0) {
            if (isNineUpload) {
              window.nineUploadData = data
              setTimeout(() => { // appId和token30分钟过期，要清理一下
                window.nineUploadData = null
              }, 30 * 60 * 1000)
              return data
            } else {
              this.appId = data.appId
              this.token = data.token
            }
          } else {
            this.$message.error(userMsg)
          }
        } catch (e) {
          this.$message.error(e)
        }
      },
      remove (file) {
        this.fileList.splice(this.fileList.indexOf(file), 1)
        this.$emit('changeImg', this.fileList)
      },
    }
  }
</script>

<style lang="scss" scoped>
  .upload-list {
      padding-top: 16px;
      line-height: 1.5;
      display: flex;
      flex-wrap: wrap;
      margin-right: -16px;

      .item {
        width: 104px;
        height: 104px;
        margin-right: 16px;
        margin-bottom: 16px;
        padding: 8px;
        text-align: center;
        vertical-align: top;
        background-color: #fafafa;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        position: relative;

        &-info {
          position: relative;
          width: 100%;
          height: 100%;
        }
        &-img {
          position: static;
          width: 100%;
          height: 100%;
          -o-object-fit: cover;
          // object-fit: cover;
          object-fit: contain
        }
        &-actions {
          position: absolute;
          z-index: 10;
          white-space: nowrap;
          opacity: 0;
          transition: all 0.3s;
          &.remove{
            top: -18px;
            right: -18px;
            color: #ff0000;
          }
        }
        .item-info:hover .item-actions,
        .item-actions:hover {
          opacity: 1;
        }
      }

      .upload-select {
        position: relative;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        border: 1px dashed #d9d9d9;
        cursor: pointer;
        transition: border-color 0.3s ease;
        &:hover {
          border-color: #1890ff;
        }
      }
  }
</style>
