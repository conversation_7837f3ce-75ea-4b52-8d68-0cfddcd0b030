<script type='text/jsx' lang="jsx">
  import Vue, { getCurrentInstance, ref } from 'vue'
  import { useState } from '../model/useState.js'
  import useActions from '../model/useActions'
  import useEditDisplay from '../model/useEditDisplay.js'
  import useEditPicture from '../model/useEditPicture.js'

  import { NiTable } from '@jiuji/nine-ui'
  import BtnA from '../../components/atom/btn-a'
  import PictureHoverPreview from '../../components/atom/picture-HoverPreview'
  import ModalEditPicture from './modal-editPicture'
  import ModalEditDisplay from './modal-editDisplay'
  import ModalDisplayList from './modal-displayList'
  import 'viewerjs/dist/viewer.css'
  import Viewer from 'v-viewer'
  import ImgAreaPreview from '~/components/uploader/img-area-preview'
  Vue.use(Viewer)

  export default {
    name: 'manage-display',
    components: {
      BtnA,
      PictureHoverPreview,
      NiTable,
      ModalEditPicture,
      ModalEditDisplay,
      ModalDisplayList,
      ImgAreaPreview
    },
    setup () {
      const { $tnt } = getCurrentInstance().proxy
      const { state } = useState()
      const { handleTableChange } = useActions()
      const { addDisplay, showDisplay } = useEditDisplay()
      const { editPicture, removePicture } = useEditPicture()
      const viewPicturesArr = (pictureUrlStr) => {
        return pictureUrlStr.length ? pictureUrlStr.map(item => item.filePath) : []
      }

      const imgAreaPreview = ref(null)
      const viewImages = (pictureUrl, index) => {
        imgAreaPreview.value.previewImg({
          fileList: pictureUrl,
          index
        })
        // $viewerApi({
        //   images: viewPicturesArr(pictureUrl)
        // }).view(index)
      }
      const columnsOrigin = ref([
        {
          dataIndex: 'categoryName',
          key: 'categoryName',
          title: '物料类别',
          width: '12%',
          customRender: (text) => {
            return <pre>{{ text }}</pre>
          },
          isShow: true
        },
        {
          dataIndex: 'formName',
          key: 'formName',
          title: '物料形式',
          width: '12%',
          isShow: true,
          customRender: (text) => {
            return <pre>{{ text }}</pre>
          }
        },
        {
          dataIndex: 'name',
          key: 'name',
          title: '物料名称',
          width: '16%',
          isShow: true,
          customRender: (text) => {
            return <pre>{{ text }}</pre>
          }
        },
        {
          dataIndex: 'standardAttachments',
          key: 'standardAttachments',
          title: '陈列标准',
          width: 260,
          isShow: true,
          // customRender: text => <PictureHoverPreview src={text}/>
          customRender: (text, record, index) => {
            return <div class="imageview-wrap">
              {
                text && text.length ? <div class="imageview">
                  {
                    text.map((item, index) => {
                      return <div class="flex flex-align-center flex-justify-center mr-8 pointer" style="width: 100px; height: 100px;">
                        <img src={item.filePath} alt="" onClick={() => viewImages(text, index)}/>
                      </div>
                    })
                  }
                </div> : ''
              }
            </div>
          }
        },
        {
          dataIndex: 'standardContent',
          key: 'standardContent',
          title: '文案标准',
          width: '18%',
          isShow: $tnt.xtenant >= 1000,
          customRender: text => <i class='pre-line'> {text} </i>
        },
        {
          dataIndex: 'userName',
          key: 'userName',
          title: '创建人',
          width: '6%',
          isShow: true,
        },
        {
          dataIndex: 'createTime',
          key: 'createTime',
          title: '创建时间',
          width: '8%',
          isShow: true,
        },
        {
          dataIndex: 'bindDisplay',
          key: 'bindDisplay',
          title: '是否有陈列',
          width: '7%',
          isShow: true,
          customRender: (text) => <i>{ text ? '是' : '否'}</i>
        },
        {
          dataIndex: 'actions',
          key: 'actions',
          title: '操作',
          width: '12%',
          isShow: true,
          customRender: (text, record, index) => {
            const title = (
              <p style="width:200px">
                <h4>是否确定删除该物料?</h4>
                删除物料之后，当前所有陈列该物料店面物料审核数据都会被删除"
              </p>
            )
            const icon = <a-icon type="question-circle-o" style="color: red" />
            return (
              <div>
                <BtnA
                  text="添加陈列"
                  actions={ () => { addDisplay(record) } }/>
                {
                  record.bindDisplay && <BtnA
                    text="查看陈列"
                    actions={ () => { showDisplay(record) } }/>
                }
                <router-link target="_blank" class="links-text" to={{
                  path: '/operation/material/manage',
                  query: {
                    // 这里name业务使用过程中会存在很多特殊符号,如果直接放参数,特殊符号会被转义,导致查询不准确,所以先加密下
                    nameJson: JSON.stringify({ name: record.name }),
                    categoryId: record.categoryId,
                    formId: record.formId
                  }
                }}>陈列明细</router-link>
                <BtnA
                  text="编辑"
                  actions={ () => { editPicture(record.id) } }/>
                <a-popconfirm
                  title= { title }
                  okText="删除"
                  cancelText="取消"
                  okType="danger"
                  icon={ icon }
                  onConfirm={ () => { removePicture(record.id) } }
                >
                  <BtnA danger text="删除"/>
                </a-popconfirm>
              </div>
            )
          }
        },
      ])
      return {
        state,
        handleTableChange,
        addDisplay,
        showDisplay,
        editPicture,
        removePicture,
        viewPicturesArr,
        viewImages,
        imgAreaPreview,
        columnsOrigin
      }
    },
    data () {
      return {
        columns: this.columnsOrigin.filter(item => item.isShow),
      }
    },
    render () {
      const {
        state,
        columns,
        handleTableChange,
      } = this
      const pictureList = this.$store.state.operation.materialManage.pictureList
      const isLoading = this.$store.state.operation.materialManage.isLoading
      const pagination = state.pagination
      return (
        <div class="mt-20 data-display material-picture-table">
          <NiTable
            rowKey={ (record, index) => index }
            columns={ columns }
            loading={ isLoading }
            dataSource={ pictureList }
            pagination={ pagination }
            onChange={ handleTableChange }
          >
          <template slot="action">
            <a-button onClick={ () => { this.editPicture() } }>
              添加物料画面
            </a-button>
          </template>
          </NiTable>
          { state.pictureFormIsShow && <ModalEditPicture/> }
          { state.displayFormIsShow && <ModalEditDisplay/> }
          { state.displayIsShow && <ModalDisplayList/>}
          <img-area-preview showGps={false} ref="imgAreaPreview"></img-area-preview>
        </div>
      )
    },
  }
</script>

<style lang="scss">
.material-picture-table{
  @import '../../common/table';
}
  .links-text {
    color: #53b6ff;
    margin: 0 5px 4px;
    white-space: nowrap;
  }
.imageview-wrap {
  width: 212px;
  height: 110px;
  overflow: hidden;
  &:hover{
    overflow-x: auto;
  }
  &::-webkit-scrollbar {
    width: 16px;
    height: 6px;
  }
  .imageview {
    display: flex;
    img{
      width: 100px;
      height: 100px;
      border-radius: 4px;
    }
  }
}
</style>
