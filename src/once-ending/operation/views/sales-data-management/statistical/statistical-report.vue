<template>
  <div class="report-wrapper">
    <div class="mt-20 mb-20 font-18 bold">{{title}}</div>
    <ni-list-page class="mt-20" :pushFilterToLocation="false">
      <ni-filter :form="searchWrapper" :loading="loading" @filter="fetchData" :labelWidth="90">
        <ni-filter-item label="地区">
          <ni-area-select
            v-model="searchWrapper.areaIds"
            :allow-clear="true"
            multiple
            :mode="2"
            show-search
            :max-tag-count="1"
            placeholder="请选择"
          />
        </ni-filter-item>
        <ni-filter-item label="维度">
          <a-select v-model="searchWrapper.dimension" allowClear>
            <a-select-option v-for="item in dimensionOption" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
          </a-select>
        </ni-filter-item>
        <ni-filter-item label="时间">
          <a-range-picker
            v-model="listTime"
            format="YYYY-MM-DD"
            @change="checkPickerTime"
          />
        </ni-filter-item>
        <ni-filter-item label="门店类别">
          <a-select v-model="searchWrapper.areaType" placeholder="请选择" allowClear>
            <a-select-option v-for="item in areaTypeOption" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
          </a-select>
        </ni-filter-item>
        <ni-filter-item label="门店类型">
          <a-select v-model="searchWrapper.areaKind" placeholder="请选择" allowClear>
            <a-select-option v-for="item in areaKindOption" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
          </a-select>
        </ni-filter-item>
      </ni-filter>
<NiTable
  class="mt-20"
  :columns="columns"
  :dataSource="dataSource"
  :pagination="pagination"
  @change="handleTableChange"
  :footerTotalNum="1"
>
<div slot="action">
            <a-button type="primary" @click="exportHandle">导出</a-button>
          </div>
          <div slot="RegionColumnsSlot" slot-scope="text, record">
            <span>{{ record.dimensionName }}</span>
          </div>
          <div slot="upperDepartColumnsSlot" slot-scope="text, record">
            <div v-if="record.upperDepart.length > 8">
              <a-popover overlayClassName="poperLayTable">
                <template slot="content">
                  <div>{{ record.upperDepart }}</div>
                </template>
                <span class="pointer">{{ record.upperDepart.substring(0, 8) + '...' }}</span>
              </a-popover>
            </div>
            <span v-else>{{ record.upperDepart }}</span>
          </div>
          <div slot="lowerDepartColumnsSlot" slot-scope="text, record">
            <div v-if="record.lowerDepart.length > 10">
              <a-popover overlayClassName="poperLayTable">
                <template slot="content">
                  <div>{{ record.lowerDepart }}</div>
                </template>
                <span class="pointer">{{ record.lowerDepart.substring(0, 8) + '...' }}</span>
              </a-popover>
            </div>
            <span v-else>{{ record.lowerDepart }}</span>
          </div>
          <div slot="areaColumnsSlot" slot-scope="text, record">
            <!-- <div v-if="record.area.length > 18">
              <a-popover overlayClassName="poperLayTable">
                <template slot="content">
                  <div>{{ record.area }}</div>
                </template>
                <span class="pointer">{{ record.area.substring(0, 16) + '...' }}</span>
              </a-popover>
            </div>
            <span v-else>{{ record.area }}</span> -->
            <a-popover overlayClassName="poperLayTable">
                <template slot="content">
                  <div>{{ record.area }}</div>
                </template>
                <div class="pointer lines-1">{{ record.area }}</div>
              </a-popover>
          </div>
          <div slot="staffColumnsSlot" slot-scope="text, record">
            <span>{{ record.staff }}</span>
          </div>
          <template slot-scope="text, record" :slot="slotName(item, 'achievementValue')" v-for="item in columnsSlot">
            <span>{{ text }}</span>
          </template>
</NiTable>
      <!-- <div class="mt-20" style="background: #FFFFFF">
        <div class="flex flex-justify-end padding">
          <a-button type="primary" @click="exportHandle">导出</a-button>
        </div>
        <a-table
          :rowKey="(record, index) => index"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="false"
          :scroll='{ x: true }'
        >
          <div slot="action">
            <a-button type="primary" @click="exportHandle">导出</a-button>
          </div>
          <div slot="RegionColumnsSlot" slot-scope="text, record">
            <span>{{ record.dimensionName }}</span>
          </div>
          <div slot="upperDepartColumnsSlot" slot-scope="text, record">
            <div v-if="record.upperDepart.length > 8">
              <a-popover overlayClassName="poperLayTable">
                <template slot="content">
                  <div>{{ record.upperDepart }}</div>
                </template>
                <span class="pointer">{{ record.upperDepart.substring(0, 8) + '...' }}</span>
              </a-popover>
            </div>
            <span v-else>{{ record.upperDepart }}</span>
          </div>
          <div slot="lowerDepartColumnsSlot" slot-scope="text, record">
            <div v-if="record.lowerDepart.length > 10">
              <a-popover overlayClassName="poperLayTable">
                <template slot="content">
                  <div>{{ record.lowerDepart }}</div>
                </template>
                <span class="pointer">{{ record.lowerDepart.substring(0, 8) + '...' }}</span>
              </a-popover>
            </div>
            <span v-else>{{ record.lowerDepart }}</span>
          </div>
          <div slot="areaColumnsSlot" slot-scope="text, record">
            <div v-if="record.area.length > 10">
              <a-popover overlayClassName="poperLayTable">
                <template slot="content">
                  <div>{{ record.area }}</div>
                </template>
                <span class="pointer">{{ record.area.substring(0, 8) + '...' }}</span>
              </a-popover>
            </div>
            <span v-else>{{ record.area }}</span>
          </div>
          <div slot="staffColumnsSlot" slot-scope="text, record">
            <span>{{ record.staff }}</span>
          </div>
          <template slot-scope="text, record" :slot="slotName(item, 'achievementValue')" v-for="item in columnsSlot">
            <span>{{ text }}</span>
          </template>
        </a-table>
        <div class="flex flex-justify-end padding">
          <a-pagination
            v-model="pagination.current"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total"
            show-size-changer
            :show-total="total => `总共 ${total} 条`"
            :page-size="pagination.pageSize"
            @showSizeChange="showSizeChange"
            @change="paginationChange"
          >
          </a-pagination>
        </div>
      </div> -->
    </ni-list-page>
  </div>
</template>

<script type="text/jsx" lang="jsx">
  import { reactive, toRefs, getCurrentInstance, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router/composables'
  import { NiListPage, NiFilter, NiFilterItem, NiAreaSelect, NiTable } from '@jiuji/nine-ui'
  import moment from 'moment'
  import salesTaskApi from '@/operation/api/sales-task-management'
  import { exportFile } from '~/util/export'
  export default {
    components: {
      NiListPage,
      NiFilter,
      NiFilterItem,
      NiTable,
      NiAreaSelect
    },
    setup () {
      const route = useRoute()
      const router = useRouter()
      const root = getCurrentInstance().proxy
      const { $message, $set, $indicator, $store } = root
      const detailId = route.params.id
      const state = reactive({
        searchWrapper: {
          areaIds: undefined,
          dimension: 3,
          areaKind: undefined,
          areaType: undefined,
          startTime: moment().format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        loading: false,
        columns: [],
        columnsSlot: [],
        dataSource: [],
        dimensionOption: [],
        // 门店类别
        areaTypeOption: [],
        // 门店类型
        areaKindOption: [],
        canvasTableAttrs: {
          border: true,
          stripe: true,
          rowHeight: 42,
          customStyle: {
            headerFontWeight: '500',
            borderColor: '#dfdfdf',
            headerBackground: '#f5f5f5',
            color: '#333',
            fontSize: 13,
            activeColBackground: '#F6FBFF',
            activeRowBackground: '#F6FBFF',
            stripeRowBackground: '#fafafa',
            summaryBackground: '#ffffff',
            background: '#fff',
          },
        },
        dataIndexArr: [],
        title: undefined,
        pagination: {
          current: 1,
          pageSize: 10,
          showTotal: total => `共 ${total} 条记录`,
          pageSizeOptions: ['10', '20', '50', '100'],
          showSizeChanger: true,
          total: 0,
        },
      })
      const listTime = ref([state.searchWrapper.startTime, state.searchWrapper.endTime])
      const initData = () => {
        state.columns = []
        state.dataSource = []
        state.columnsSlot = []
        let newDimensionList = []
        const params = {
          groupId: route.params.id,
          // 地区树 报表统计用
          areaIds: state.searchWrapper.areaIds,
          // 维度 报表统计用
          dimensionType: state.searchWrapper.dimension,
          startTime: state.searchWrapper.startTime,
          endTime: state.searchWrapper.endTime,
          areaKind: state.searchWrapper.areaKind,
          areaType: state.searchWrapper.areaType,
          // 类型 1、报表 2、任务
          configType: 1,
          current: state.pagination.current,
          size: state.pagination.pageSize
        }
        $indicator.open()
        salesTaskApi.getSalesTaskTargetDetailForStatistic(params).then(res => {
          if (res.code === 0) {
            state.title = res.data.groupName || '报表'
            state.pagination.total = res.data.targetDataPage.total || 0
            state.targetInfoList = res.data.targetHeaderList || []
            state.targetInfoList.forEach(item => {
              let newList = {
                title: '',
                dataIndex: '',
                // children: [],
              }
              newList.title = item.projectName
              newList.dataIndex = `${item.projectId}achievementValue`

              // if (item.children && item.children.length > 0) {
              //   state.dataIndexArr = item.children.map(item => item.key)
              //   item.children.forEach(children => {
              //     newList.children.push({
              //       title: children.title,
              //       dataIndex: item.projectId + children.key,
              //       scopedSlots: { customRender: item.projectId + children.key }
              //     })
              //   })
              // }
              state.columnsSlot.push(item.projectId)
              state.columns.push(newList)
            })
            // 大区：upperDepartColumns   小区：lowerDepartColumns   门店：areaColumns   员工：staffColumns
            const upperDepartColumns = {
              title: '大区',
              dataIndex: 'upperDepartColumns',
              fixed: 'left',
              width: 140,
              scopedSlots: { customRender: 'upperDepartColumnsSlot' },
            }
            const lowerDepartColumns = {
              title: '小区',
              dataIndex: 'lowerDepartColumns',
              fixed: 'left',
              width: 140,
              scopedSlots: { customRender: 'lowerDepartColumnsSlot' },
            }
            const areaColumns = {
              title: '门店',
              dataIndex: 'areaColumns',
              fixed: 'left',
              width: 240,
              scopedSlots: { customRender: 'areaColumnsSlot' },
            }
            const staffColumns = {
              title: '员工',
              dataIndex: 'staffColumns',
              fixed: 'left',
              width: 140,
              scopedSlots: { customRender: 'staffColumnsSlot' },
            }
            state.columns.unshift(staffColumns)
            state.columns.unshift(areaColumns)
            state.columns.unshift(lowerDepartColumns)
            state.columns.unshift(upperDepartColumns)
            if (state.targetInfoList.length) {
              if (res.data.targetDataPage?.records.length) {
                // const staffShow = res.data.targetDataPage?.records.some(item => item.staff)
                // const areaShow = res.data.targetDataPage?.records.some(item => item.area)
                // const lowerDepartShow = res.data.targetDataPage?.records.some(item => item.lowerDepart)
                // const upperDepartShow = res.data.targetDataPage?.records.some(item => item.upperDepart)
                // if (staffShow) {
                //   state.columns.unshift(staffColumns)
                // }
                // if (areaShow) {
                //   state.columns.unshift(areaColumns)
                // }
                // if (lowerDepartShow) {
                //   state.columns.unshift(lowerDepartColumns)
                // }
                // if (upperDepartShow) {
                //   state.columns.unshift(upperDepartColumns)
                // }
                if (state.searchWrapper.dimension) {
                  if (state.searchWrapper.dimension === 1) {
                    state.columns = state.columns.filter(item => !['lowerDepartColumns', 'areaColumns', 'staffColumns'].includes(item.dataIndex))
                  }
                  if (state.searchWrapper.dimension === 2) {
                    state.columns = state.columns.filter(item => !['areaColumns', 'staffColumns'].includes(item.dataIndex))
                  }
                  if (state.searchWrapper.dimension === 3) {
                    state.columns = state.columns.filter(item => !['staffColumns'].includes(item.dataIndex))
                  }
                }
              }
            }
            // 处理表格数据
            state.dimensionList = res.data.targetDataPage?.records || []
            if (state.dimensionList.length > 0) {
              state.dimensionList.forEach(item => {
                let newDimensionListObj = {}
                newDimensionListObj.dimension = item.dimension
                newDimensionListObj.dimensionId = item.dimensionId
                newDimensionListObj.dimensionName = item.dimensionName
                newDimensionListObj.upperDepart = item.upperDepart
                newDimensionListObj.upperDepartDimension = item.upperDepartDimension
                newDimensionListObj.upperDepartDimensionId = item.upperDepartDimensionId
                newDimensionListObj.lowerDepart = item.lowerDepart
                newDimensionListObj.lowerDepartDimension = item.lowerDepartDimension
                newDimensionListObj.lowerDepartDimensionId = item.lowerDepartDimensionId
                newDimensionListObj.area = item.area
                newDimensionListObj.areaDimension = item.areaDimension
                newDimensionListObj.areaDimensionId = item.areaDimensionId
                newDimensionListObj.staff = item.staff
                newDimensionListObj.staffDimension = item.staffDimension
                newDimensionListObj.staffDimensionId = item.staffDimensionId
                newDimensionListObj.dimensionId = item.dimensionId
                newDimensionListObj.dimensionName = item.dimensionName
                if (item.targetDetailList && item.targetDetailList.length > 0) {
                  // item.targetDetailList.forEach(child => {
                  //   state.dataIndexArr.forEach(item => {
                  //     newDimensionListObj[`${child.projectId}${item}`] = child[item]
                  //   })
                  // })
                  item.targetDetailList.forEach(child => {
                    newDimensionListObj[`${child.projectId}achievementValue`] = child.achievementValue + child.unit
                  })
                } else {
                  // state.columnsSlot.map(child => {
                  //   state.dataIndexArr.forEach(item => {
                  //     newDimensionListObj[`${child}${item}`] = 0
                  //   })
                  // })
                  state.columnsSlot.map(child => {
                    newDimensionListObj[`${child}achievementValue`] = 0 + child.unit
                  })
                }
                newDimensionList.push(newDimensionListObj)
              })
            }
            $set(state, 'dataSource', newDimensionList)
            console.log(state.columns)
            console.log(state.dataSource)
          } else {
            $message.error(res.userMsg)
          }
        }).finally(() => {
          $indicator.close()
        })
      }
      const fetchData = () => {
        state.pagination.current = 1
        state.pagination.pageSize = 10
        initData()
      }
      const checkPickerTime = (date, dateString) => {
        state.searchWrapper.startTime = dateString[0]
        state.searchWrapper.endTime = dateString[1]
      }
      const getGroupConfigTypeEnum = () => {
        salesTaskApi.getGroupConfigTypeEnum('').then(res => {
          if (res.code === 0) {
            // 任务组维度
            state.dimensionOption = filterOptions(res.data, 'dimension')
            // 门店类别
            state.areaTypeOption = filterOptions(res.data, 'areaType')
            // 门店类型
            state.areaKindOption = filterOptions(res.data, 'areaKind')
          } else {
            $message.error(res.userMsg)
          }
        })
      }
      const filterOptions = (data, type) => {
        return data.filter(item => item.key === type)[0]?.itemList
      }
      const exportHandle = () => {
        $indicator.open()
        let params = {
          areaIds: state.searchWrapper.areaIds,
          dimensionType: state.searchWrapper.dimension,
          startTime: state.searchWrapper.startTime,
          endTime: state.searchWrapper.endTime,
          areaKind: state.searchWrapper.areaKind,
          areaType: state.searchWrapper.areaType,
          // 1、报表 2、任务
          configType: 1,
          groupId: route.params.id
        }
        exportFile({
          url: '/cloudapi_nc/ncSegments/api/saleTask/task/exportSalesTaskTarget/v1?xservicename=oa-ncSegments',
          token: $store.state.token,
          method: 'post',
          contentType: 'json',
          params
        }).then((res) => {
          if (!res) return
          $message.error(res?.userMsg || res?.msg || '导出文件失败')
          if (res?.code === 1000) { // 无效token
            router.push('/login?redirect=' + window.location.href)
          }
        }).catch(e => {
          $message.error(`${e?.message || e}`)
        }).finally(() => {
          $indicator.close()
        })
      }
      getGroupConfigTypeEnum()
      const tableHeight = () => {
        return document.body.clientHeight - 200
      }
      const showSizeChange = (page, pageSize) => {
        state.pagination.current = page
        state.pagination.pageSize = pageSize
        initData()
      }
      const paginationChange = (current, size) => {
        state.pagination.current = current
        state.pagination.pageSize = size
        initData()
      }
      const handleTableChange = (pagination) => {
        state.pagination.current = pagination.current
        state.pagination.pageSize = pagination.pageSize
        initData()
      }
      const slotName = (val1, val2) => {
        return val1 + val2
      }
      return {
        detailId,
        ...toRefs(state),
        listTime,
        fetchData,
        checkPickerTime,
        exportHandle,
        tableHeight,
        showSizeChange,
        paginationChange,
        handleTableChange,
        slotName
      }
    }
  }
</script>

<style lang="scss" scoped>
.report-wrapper{
  padding: 0 15px 0 5px;
}
:deep(.ant-table-body) {
  overflow-x: auto !important;
}
</style>
<style lang="scss">
.poperLayTable > .ant-popover-content > .ant-popover-inner {
  width: 150px;
}
</style>
