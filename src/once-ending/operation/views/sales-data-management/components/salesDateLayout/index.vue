<template>
  <div class="sales-data">
    <a-spin size="large" :delay="200" :spinning="loading">
    <a-layout>
      <a-layout-sider v-model="collapsed" :trigger="null" collapsible>
        <div class="sales-logo border-bottom " :class="collapsed ? 'flex-justify-center' : 'flex-justify-between'">
          <router-link class="font-18 bold" v-if="!collapsed" to="/operation/sales-data">任务与报表管理</router-link>
          <a-icon
            class="trigger"
            :type="collapsed ? 'menu-unfold' : 'menu-fold'"
            @click="() => (collapsed = !collapsed)"
          />
        </div>
        <Menu :collapsed="collapsed" :routes="routes"></Menu>
      </a-layout-sider>
      <a-layout class="content-body">
<!--        面包屑导航-->
<!--        <div class="layout-breadcrumb">-->
<!--          <a-breadcrumb>-->
<!--            <a-breadcrumb-item-->
<!--              v-for="(item, index) in $route.matched.slice(2)"-->
<!--              :key="index">-->
<!--              {{ item.meta.title }}-->
<!--            </a-breadcrumb-item>-->
<!--          </a-breadcrumb>-->
<!--        </div>-->
        <a-layout-content>
          <keep-alive>
            <router-view
              v-if="$route.meta.keepAlive && hasPermission"
              :key="$route.fullPath"
            >
            </router-view>
          </keep-alive>
          <router-view
            v-if="!$route.meta.keepAlive && hasPermission"
            :key="$route.fullPath"
          >
          </router-view>
          <page403 :isBack="false" v-if="!hasPermission"></page403>
        </a-layout-content>
      </a-layout>
    </a-layout>
    </a-spin>
  </div>
</template>

<script>
  import { reactive, ref, computed, getCurrentInstance } from 'vue'
  // import { routes } from '../../constants.js'
  import Page403 from '@common/views/abnormal-page/403'
  import Menu from '../menu'
  import salesTaskApi from '../../../../api/sales-task-management'
  export default {
    components: {
      Page403,
      Menu
    },
    setup () {
      const root = getCurrentInstance().proxy
      const { $route, $store, $message, $router } = root
      const state = reactive({
        userInfo: $store.state.userInfo
      })
      const collapsed = ref(false)
      const routes = ref([])
      const hasPermission = computed(() => {
        const hasMatePermission = $route.meta && $route.meta.permission
        if (sessionStorage.getItem('isTest')) {
          return true
        }
        // 只判断当前路由，是否mate中有permission，如果没有，则认为不需要权限，则显示路由页面，由后续处理
        if (!hasMatePermission) {
          return true
        }
        const permission = state.userInfo ? state.userInfo.Rank || [] : []
        return !$route.matched.some(
          r => r.meta.permission && !permission.includes(r.meta.permission)
        )
      })
      const getTaskMenu = () => {
        salesTaskApi.getTaskMenu().then(res => {
          if (res.code === 0) {
            let routesList = res.data
            routesList = routesList.filter(item => !(item.path === 'report-management' && item.children.length < 1))
            routesList = routesList.filter(item => !(item.path === 'statistical-report' && item.children.length < 1))
            routes.value = routesList
            // 如果没有‘xsrw’的权限则跳转到任务达成查询页面
            if ($route.path === '/operation/sales-data/task-configuration/task-project-configuration' && routesList[0].children[0].path !== '/operation/sales-data/task-configuration/task-project-configuration') {
              $router.replace({
                path: '/operation/sales-data/task-configuration/task-achieve-inquire'
              })
            }
          } else {
            $message.error('菜单获取失败')
          }
        })
      }
      getTaskMenu()
      const loading = computed(() => {
        return $store.state.loading
      })
      return {
        collapsed,
        hasPermission,
        routes,
        loading
      }
    }
  }
</script>

<style lang="scss" scoped>
.sales-data{
  .sales-logo {
    display: flex;
    height: 60px;
    align-items: center;
    padding: 0 15px;
    .router-link-active {
      text-decoration: none;
      color: #000000;
    }
  }
  :deep(.ant-layout-sider) {
    background: #FFFFFF;
  }
  :deep(.ant-layout-content) {
    height: 100vh;
  }
  .content-body {
    padding: 0 0 0 8px;
    .layout-breadcrumb{
      box-sizing: border-box;
      padding: 20px 0 0 16px;
    }
  }
}
</style>
