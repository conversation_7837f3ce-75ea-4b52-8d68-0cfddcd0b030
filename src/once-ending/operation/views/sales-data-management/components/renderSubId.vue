<script lang="jsx">
  import { defineComponent } from 'vue'
  import { SUB_ID_URL } from '../constants'

  export default defineComponent({
    props: {
      subKind: {
        type: String,
        default: ''
      },
      subId: {
        type: [String, Number],
        default: ''
      }
    },
    setup () {

    },
    render () {
      const { subKind, subId } = this
      const urlText = SUB_ID_URL.get(subKind)
      const link = this.$tnt.oaHost + urlText + subId
      return !urlText ? <i> { subId } </i> : <a rel="noopener noreferrer" href={ link } target="_blank">{ subId }</a>
    }
  })
</script>
