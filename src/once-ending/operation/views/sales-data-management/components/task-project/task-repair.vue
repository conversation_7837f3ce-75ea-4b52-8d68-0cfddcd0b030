<template>
  <div class="task-com">
    <div class="mt-20 font-18 bold">[{{ taskFlagName }}]任务项配置</div>
    <!--任务项配置-->
    <div class="mt-20 mb-10 font-14 bold">任务项配置</div>
    <div class="border padding border-radius2">
      <a-row class="mb-20">
        <a-col :span="18">
          <div class="flex flex-align-center">
            <div class="labelWidth" style="width: 98px">
              <span class="inline-block red mr-5 mt-5">*</span>
              <p class="font-14 bold">任务项名称：</p>
            </div>
            <div class="flex-child-average">
              <a-input placeholder="请输入" v-model="taskInfo.projectName" :maxLength="100" allowClear :disabled="readOnly"></a-input>
            </div>
            <span class="grey-9">{{ taskInfo.projectName ? taskInfo.projectName.length : 0 }}/100</span>
          </div>
        </a-col>
      </a-row>
      <a-row class="mb-10">
        <a-col>
          <div class="flex flex-align-center">
            <div class="flex flex-align-center ml-16">
              <span class="inline-block red mr-5 mt-5">*</span>
              <p class="font-14 bold">统计指标：</p>
            </div>
            <div class="flex-child-average">
              <a-radio-group name="businessType" v-model="taskInfo.businessType" :disabled="readOnly" @change="businessTypeChange">
                <a-radio :value="item.value" v-for="item in businessTypeOption" :key="item.value" :disabled="item.disabled">
                  <span>{{ item.label }}</span>
                  <a-tooltip placement="top" v-if="item.tips">
                    <template slot="title">
                      <span>{{ item.tips }}</span>
                    </template>
                    <a-icon type="question-circle" style="color: #8C8C8C;" class="mr-8"/>
                  </a-tooltip>
                </a-radio>
              </a-radio-group>
            </div>
          </div>
        </a-col>
      </a-row>
      <a-row class="mb-10">
        <a-col>
          <div class="flex flex-align-center">
            <div class="labelWidth" style="width: 98px">
              <p class="font-14 bold">排序：</p>
            </div>
            <div class="flex-child-average">
              <a-input-number v-model="taskInfo.rank" :min="0" style="width: 150px" :disabled="readOnly"/>
            </div>
          </div>
        </a-col>
      </a-row>
      <div class="mt-20 mb-10 font-14 bold">关联任务组：</div>
      <a-row class="mb-20">
        <a-col :span="18">
          <TaskTransfer title="任务组" :configType="2" :relationType="2" @getTaskTransferVal="getTaskTransferVal" :relationList="relationListOption" :isEdit="!$route.query.projectId" :disabled="readOnly"/>
        </a-col>
      </a-row>
    </div>
    <!--任务项配置-->
    <!--订单配置-->
    <div class="mt-20 mb-10 font-14 bold">订单配置(<span class="normal grey-9">配置此统计指标的订单限制</span>)</div>
    <div class="border padding border-radius2">
      <a-row style="display: flex; align-items: center" class="mb-10">
        <a-col span="11">
          <div class="flex flex-align-center">
            <div class="labelWidth">
              <p class="font-14 bold">接件类型：</p>
              <a-tooltip placement="top">
                <template slot="title">
                  <span>选择硬件则只统计硬件的维修单数据，选择软件则只统计软件的维修单数据，两个都选则统计软硬件的维修单数据</span>
                </template>
                <a-icon type="question-circle" style="color: #8C8C8C;" class="mr-8"/>
              </a-tooltip>
            </div>
            <div class="flex-child-average">
              <a-checkbox-group v-model="taskInfo.subType" name="subTypeOption" :options="taskInfo.businessType === 16 ? subTypeOptionNew : subTypeOption" :disabled="readOnly"/>
            </div>
          </div>
        </a-col>
        <a-col span="2"></a-col>
        <a-col span="11">
          <div class="flex flex-align-center subtract">
            <div class="labelWidth">
              <p class="font-14 bold">剔除退款订单：</p>
              <a-tooltip placement="top">
                <template slot="title">
                  <span>不选择代表不剔除退款订单，选择后代表剔除退款订单</span>
                </template>
                <a-icon type="question-circle" style="color: #8C8C8C;" class="mr-8"/>
              </a-tooltip>
            </div>
            <div class="flex-child-average">
              <a-checkbox-group v-model="taskInfo.returnFlag" name="returnFlag" :options="[{ label: '是', value: 1 }]" :disabled="readOnly"/>
            </div>
          </div>
        </a-col>
      </a-row>
      <a-row>
        <a-col>
          <div class="flex flex-align-center">
            <div class="labelWidth">
              <p class="font-14 bold">userid：</p>
              <a-tooltip placement="top">
                <template slot="title">
                  <span>未填写userid则代表所有用户的订单数据都统计，包含：只统计所填写的userid的订单数据，不包含：不统计所填写的userid的订单数据</span>
                </template>
                <a-icon type="question-circle" style="color: #8C8C8C;" class="mr-8"/>
              </a-tooltip>
            </div>
            <div class="flex-child-average flex">
              <a-select class="mr-8" style="width: 240px" v-model="taskInfo.useridFlag" placeholder="请选择是否包含所填写的userid" :options="containOrNotOption" allowClear :disabled="readOnly"></a-select>
              <a-textarea style="width: 400px" :autoSize="{ minRows: 1, maxRows: 5 }" placeholder="请输入userid,多个userid之间用英文,分隔" v-model="taskInfo.userid" :disabled="readOnly" @input="taskInfo.userid = taskInfo.userid.replace(/[^\d\,]/g,'')" allowClear></a-textarea>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
    <!--订单配置-->
    <!--商品配置-->
    <div class="mt-20 mb-10 font-14 bold">商品配置(<span class="normal grey-9">配置此统计指标的商品限制</span>)</div>
    <div class="border padding border-radius2">
      <a-row class="mb-10">
        <a-col :span="11">
          <div class="flex flex-align-center">
            <div class="labelWidth">
              <p class="font-14 bold">商品统计标签：</p>
            </div>
            <div class="flex-child-average">
              <MarkTree placeholder="请选择" v-model="taskInfo.statisticLabels" :maxTagCount="3" treeCheckable showCheckedStrategy="SHOW_ALL" :disabled="readOnly"/>
            </div>
          </div>
        </a-col>
      </a-row>
      <a-row class="mb-10">
        <a-col :span="11">
          <div class="flex flex-align-center">
            <div class="labelWidth">
              <p class="font-14 bold">商品分类：</p>
            </div>
            <div class="flex-child-average">
              <category v-model="taskInfo.categoryId" style="width: 100%" :maxTagCount="5" @changeCid="taskInfo.brandId = undefined" :disabled="readOnly"/>
            </div>
          </div>
        </a-col>
        <a-col :span="2"></a-col>
        <a-col :span="11">
          <div class="flex flex-align-center">
            <div class="labelWidth">
              <p class="font-14 bold">品牌：</p>
            </div>
            <div class="flex-child-average">
              <brand v-model="taskInfo.brandId" :cateId="taskInfo.categoryId" :maxTagCount="5" :disabled="readOnly"/>
            </div>
          </div>
        </a-col>
      </a-row>
      <a-row class="mb-10">
        <a-col :span="11">
          <div class="flex flex-align-start">
            <div class="labelWidth">
              <p class="font-14 bold">商品id：</p>
            </div>
            <div class="flex-child-average">
              <a-textarea :autoSize="{ minRows: 2, maxRows: 5 }" placeholder="多个商品id之间用英文,号分割" v-model="taskInfo.productId" :disabled="readOnly" @input="taskInfo.productId = taskInfo.productId.replace(/[^\d\,]/g,'')" allowClear></a-textarea>
            </div>
          </div>
        </a-col>
        <a-col :span="2"></a-col>
        <a-col :span="11">
          <div class="flex flex-align-start">
            <div class="labelWidth">
              <p class="font-14 bold">skuid：</p>
            </div>
            <div class="flex-child-average">
              <a-textarea :autoSize="{ minRows: 2, maxRows: 5 }" placeholder="多个商品id之间用英文,号分割" v-model="taskInfo.skuid" :disabled="readOnly" @input="taskInfo.skuid = taskInfo.skuid.replace(/[^\d\,]/g,'')" allowClear></a-textarea>
            </div>
          </div>
        </a-col>
      </a-row>
      <a-row class="mb-10">
        <a-col :span="11">
          <div class="flex flex-align-start">
            <div class="labelWidth">
              <p class="font-14 bold">需排除商品id：</p>
            </div>
            <div class="flex-child-average">
              <a-textarea :autoSize="{ minRows: 2, maxRows: 5 }" placeholder="多个商品id之间用英文,号分割" v-model="taskInfo.productIdExclude" :disabled="readOnly" @input="taskInfo.productIdExclude = taskInfo.productIdExclude.replace(/[^\d\,]/g,'')" allowClear></a-textarea>
            </div>
          </div>
        </a-col>
        <a-col :span="2"></a-col>
        <a-col :span="11">
          <div class="flex flex-align-start">
            <div class="labelWidth">
              <p class="font-14 bold">需排除skuid：</p>
            </div>
            <div class="flex-child-average">
              <a-textarea :autoSize="{ minRows: 2, maxRows: 5 }" placeholder="多个商品id之间用英文,号分割" v-model="taskInfo.skuidExclude" :disabled="readOnly" @input="taskInfo.skuidExclude = taskInfo.skuidExclude.replace(/[^\d\,]/g,'')" allowClear></a-textarea>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
    <!--商品配置-->
    <div class="flex flex-justify-end mt-20 mb-20" v-if="(!useCount || useCount === '0') && !viewOnly">
      <a-button class="mr-20" @click="$router.go(-1)">取消</a-button>
      <a-button type="primary" @click="saveProjectConfig">保存</a-button>
    </div>
    <div class="logs-warp mb-20" v-if="logList">
      <Log :logList="logList"/>
    </div>
  </div>
</template>

<script>
  import { getCurrentInstance, reactive, toRefs, onMounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router/composables'
  import TaskTransfer from '../task-transfer'
  import category from '../category'
  import brand from '../brand'
  import MarkTree from '@/operation/views/sales-data-management/components/markTree/index.vue'
  import salesTaskApi from '@/operation/api/sales-task-management'
  import lodashJoin from 'lodash/join'
  import lodashSplit from 'lodash/split'
  import Log from '@/operation/views/sales-data-management/components/sales-data-log/sales-log.vue'
  import { containOrNotOption } from '../../constants'
  export default {
    components: {
      Log,
      MarkTree,
      category,
      brand,
      TaskTransfer
    },
    props: {
      taskFlagName: null
    },
    setup () {
      const route = useRoute()
      const router = useRouter()
      const root = getCurrentInstance().proxy
      const { $message, $indicator } = root
      const state = reactive({
        // 统计指标
        businessTypeOption: [],
        // 订单类型
        subTypeOption: [],
        subTypeOptionNew: [],
        taskInfo: {
          // 任务项id
          projectId: route.query.projectId || undefined,
          // 任务项名称
          projectName: undefined,
          // 业务类型 1、销售 2、回收 3、维修 4、运营商
          subKind: undefined,
          // 配置分类 1、报表 2、任务
          configType: undefined,
          // 统计指标
          businessType: undefined,
          // 排序
          rank: undefined,
          // // 订单类型勾选
          // subTypeFlag: undefined,
          // 商品标签
          statisticLabels: undefined,
          // 订单类型选项
          subType: undefined,
          // 剔除退款/赎回订单
          returnFlag: false,
          // userid是否包含
          useridFlag: undefined,
          // userid
          userid: undefined,
          // 商品分类
          categoryId: [],
          // 商品品牌
          brandId: undefined,
          // 商品pid
          productId: undefined,
          // 商品排除pid
          productIdExclude: undefined,
          // 商品skuid
          skuid: undefined,
          // 商品排除skuid
          skuidExclude: undefined,
          // 关联数据
          relationList: [],
        },
        relationListOption: [],
        useCount: route.query.useCount,
        viewOnly: route.query.viewOnly === '1',
        readOnly: route.query.readOnly === '1',
        logList: []
      })
      const getConfigTypeEnum = () => {
        const params = {
          subKind: route.query.subKind
        }
        salesTaskApi.getConfigTypeEnum(params).then(res => {
          if (res.code === 0) {
            state.businessTypeOption = res.data.filter(item => item.key === 'businessType')[0]?.itemList
            state.subTypeOption = res.data.filter(item => item.key === 'subType')[0]?.itemList
            state.subTypeOptionNew = state.subTypeOption.filter(item => item.value === 2)
          }
        })
      }
      getConfigTypeEnum()
      const getTaskTransferVal = (val) => {
        state.taskInfo.relationList = val
      }
      const saveProjectConfig = () => {
        let params = {
          ...state.taskInfo,
        }
        // params.subTypeFlag = params.subTypeFlag === 1
        params.subType = params.subType && params.subType.length ? lodashJoin(params.subType) : null
        params.returnFlag = lodashJoin(params.returnFlag) === '1'
        params.categoryId = lodashJoin(params.categoryId)
        params.brandId = lodashJoin(params.brandId)
        params.statisticLabels = lodashJoin(params.statisticLabels)
        // 配置分类 1、报表 2、任务
        params.configType = 2
        params.subKind = route.query.subKind
        console.log(params)
        if (!params.projectName) {
          return $message.warn('请填写任务项名称')
        }
        if (!params.businessType) {
          return $message.warn('请选择统计指标')
        }
        if (params.useridFlag && !params.userid) {
          return $message.warn('请输入userid')
        }
        if (params.userid && !params.useridFlag) {
          return $message.warn('请选择是否包含所填写的userid')
        }
        $indicator.open()
        salesTaskApi.saveProjectConfig(params).then(res => {
          if (res.code === 0) {
            $message.success('保存成功')
            router.replace({
              path: `/operation/sales-data/task-configuration/task-project-configuration/${res.data.id}`,
              query: {
                taskFlag: route.query.taskFlag,
                subKind: route.query.subKind,
                viewOnly: route.query.viewOnly,
                projectId: res.data.id
              }
            })
          } else {
            $message.error(res.userMsg)
          }
        }).finally(() => {
          $indicator.close()
        })
      }
      const getProjectConfigDetail = () => {
        const params = {
          projectId: route.query.projectId
        }
        salesTaskApi.projectConfigDetail(params).then(res => {
          if (res.code === 0) {
            state.taskInfo.projectName = res.data.projectName
            state.taskInfo.rank = res.data.rank
            state.taskInfo.productId = res.data.productId
            state.taskInfo.productIdExclude = res.data.productIdExclude
            state.taskInfo.skuid = res.data.skuid
            state.taskInfo.skuidExclude = res.data.skuidExclude
            state.taskInfo.businessType = res.data.businessType
            // state.taskInfo.subTypeFlag = res.data.subTypeFlag
            state.taskInfo.subType = res.data.subType ? lodashSplit(res.data.subType, ',').map(Number) : undefined
            state.taskInfo.returnFlag = res.data.returnFlag ? [1] : false
            state.taskInfo.useridFlag = res.data.useridFlag || undefined
            state.taskInfo.userid = res.data.userid || undefined
            state.taskInfo.statisticLabels = res.data.statisticLabels ? lodashSplit(res.data.statisticLabels, ',') : undefined
            state.taskInfo.categoryId = res.data.categoryId ? lodashSplit(res.data.categoryId, ',') : []
            state.taskInfo.brandId = res.data.brandId ? lodashSplit(res.data.brandId, ',').map(Number) : undefined
            state.relationListOption = res.data.relationList
          } else {
            $message.error(res.userMsg)
          }
        }).finally(() => {
          $indicator.close()
        })
      }
      if (state.taskInfo.projectId) {
        getProjectConfigDetail()
      }
      onMounted(() => {
        sessionStorage.setItem('tabActive', route.query.taskFlag)
        sessionStorage.setItem('tabTag', route.query.subKind)
      })
      const getLogList = () => {
        const params = {
          businessId: route.params.id,
          businessType: 1
        }
        salesTaskApi.getLogList(params).then(res => {
          if (res.code === 0) {
            state.logList = res.data.map(item => {
              return {
                comment: item.content,
                userName: item.operateUser,
                createTime: item.operateTime
              }
            })
          }
        })
      }
      getLogList()
      const businessTypeChange = () => {
        state.taskInfo.subType = undefined
      }
      return {
        ...toRefs(state),
        getTaskTransferVal,
        saveProjectConfig,
        businessTypeChange,
        containOrNotOption
      }
    }
  }
</script>

<style lang="scss" scoped>
.task-com{
  background: #FFFFFF;
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  padding: 0 15px 10px 15px;
  .labelWidth {
    width: 165px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .border-radius2 {
    border-radius: 2px;
  }
  .basketTypeOption {
    :deep(.ant-checkbox-group-item) {
      padding-bottom: 6px;
    }
  }
}
</style>
