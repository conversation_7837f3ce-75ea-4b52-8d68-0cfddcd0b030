<template>
  <div class="border padding border-radius2 flex">
    <div class="flex flex-col" style="width: 260px">
      <p class="mb-10">全部{{ title }}</p>
      <div class="border padding border-radius2">
        <div class="mb-10">
          <a-input-search v-model="relationName" placeholder="请输入关键词搜索" style="width: 200px" :disabled="disabled"/>
        </div>
        <div class="transfer-wrapper">
          <a-checkbox-group v-model="leftSelect" name="leftConfigList" :options="leftConfigList.filter(item => !relationName || item.label.includes(relationName))" :disabled="disabled"/>
        </div>
      </div>
    </div>
    <div class="flex-child-noshrink ml-20 mr-20 flex flex-align-center">
      <a-button type="primary" class="mt-20" @click="addTask" :disabled="!leftSelect.length">添加<a-icon type="right"/> </a-button>
    </div>
    <div class="flex-child-average flex flex-col">
      <p class="mb-10">已选{{ title }}</p>
      <div class="border padding border-radius2">
        <div class="mb-10">
          <a-input-search v-model="selRelationName" placeholder="请输入关键词搜索" style="width: 200px" :disabled="disabled"/>
        </div>
        <div class="transfer-wrapper">
          <ni-list-page>
            <ni-table
              :columns="columns"
              :data-source="dataSource.filter(item => !selRelationName || item.configName.includes(selRelationName))"
              :setting="false"
            >
              <span slot="displayFlagTitleSlot">
                <span>隐藏任务项</span>
                <a-tooltip placement="top">
                  <template slot="title">
                    <span>选择”是”则表示此任务组里面不显示此任务项，只参与其他统计指标的计算</span>
                  </template>
                  <a-icon type="question-circle" style="color: #8C8C8C;" class="mr-8"/>
                </a-tooltip>
              </span>
                <span slot="examineFlagTitleSlot">
                <span>不参与考核</span>
                <a-tooltip placement="top">
                  <template slot="title">
                    <span>选择”是”则表示此任务组里面不需要配置该任务项的任务量，只用于统计</span>
                  </template>
                  <a-icon type="question-circle" style="color: #8C8C8C;" class="mr-8"/>
                </a-tooltip>
              </span>
              <span slot="sortTitleSlot">
                <span>排序</span>
              </span>
              <div slot="displayFlagSlot" style="width: 80px" class="checkBox-wrapper" slot-scope="text, record">
                <a-checkbox :checked="text" @change="(e) => hideChange(e, record)" :disabled="disabled">是</a-checkbox>
              </div>
              <div slot="examineFlagSlot" class="checkBox-wrapper" slot-scope="text, record">
                <a-checkbox :checked="text" @change="(e) => notJoinChange(e, record)" :disabled="disabled">是</a-checkbox>
              </div>
              <div slot="sortSlot" class="checkBox-wrapper" slot-scope="text, record">
                <a-input-number v-model="text" :min="0" @change="(e) => sortChange(e, record)" style="width: 100%" :disabled="disabled"/>
              </div>
              <div slot="controllerSlot" style="width: 80px" slot-scope="text, record">
                <div class="flex flex-justify-between">
                  <span class="grey-9" v-if="disabled">删除</span>
                  <a-popconfirm
                    v-else
                    title="确定删除?"
                    @confirm="() => deleteItem(record)"
                  >
                    <span class="blue pointer">删除</span>
                  </a-popconfirm>
                </div>
              </div>
            </ni-table>
          </ni-list-page>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { reactive, toRefs, getCurrentInstance, watch, onMounted } from 'vue'
  import { NiListPage, NiTable } from '@jiuji/nine-ui'
  import salesApi from '../../../../api/sales-task-management'
  export default {
    components: {
      NiListPage,
      NiTable
    },
    props: {
      configType: {
        type: String || Number,
        default: 1
      },
      relationType: {
        type: Number,
        default: 1
      },
      relationList: {
        type: Array,
        default: []
      },
      // true 是新增  false 编辑
      isEdit: {
        type: Boolean,
        default: false
      },
      disabled: {
        type: Boolean,
        default: false
      },
      transferType: {
        type: Number,
        default: 2 // 1 报表 2 任务
      },
      title: {
        type: String,
        default: ''
      }
    },
    setup (props, { emit }) {
      const root = getCurrentInstance().proxy
      const { $set, $message } = root
      const state = reactive({
        relationName: undefined,
        selRelationName: undefined,
        allConfigLists: [],
        leftConfigList: [],
        leftSelect: [],
        columnsData: [
          {
            title: props.title + '名称',
            dataIndex: 'configName',
          },
          {
            dataIndex: 'displayFlag',
            slots: { title: 'displayFlagTitleSlot' },
            scopedSlots: { customRender: 'displayFlagSlot' },
          },
          {
            dataIndex: 'examineFlag',
            slots: { title: 'examineFlagTitleSlot' },
            scopedSlots: { customRender: 'examineFlagSlot' },
          },
          {
            dataIndex: 'sort',
            slots: { title: 'sortTitleSlot' },
            scopedSlots: { customRender: 'sortSlot' },
          },
          {
            title: '操作',
            dataIndex: 'controller',
            scopedSlots: { customRender: 'controllerSlot' },
          }
        ],
        columns: [],
        dataSource: [],
      })
      if (props.transferType === 1) {
        state.columns = state.columnsData.filter(item => !['displayFlag', 'examineFlag'].includes(item.dataIndex))
      } else {
        state.columns = state.columnsData
      }
      const allConfigList = () => {
        let params = {
          // 查询分类 1、报表 2、任务
          configType: props.configType,
          // 查询类型 1、任务项 2、任务组
          relationType: props.relationType
        }
        salesApi.allConfigList(params).then(res => {
          if (res.code === 0) {
            state.allConfigLists = res.data.map(item => {
              return {
                label: item.configName,
                value: item.configId
              }
            })
            state.leftConfigList = state.allConfigLists
          } else {
            $message.error(res.userMsg)
          }
        })
      }
      allConfigList()
      const deleteItem = (record) => {
        const deleteArr = state.dataSource.filter(item => item.configId === record.configId)
        state.dataSource = state.dataSource.filter(item => item.configId !== record.configId)
        state.leftConfigList.push({
          label: deleteArr[0].configName,
          value: deleteArr[0].configId
        })
        state.leftConfigList = state.leftConfigList.sort((a, b) => {
          return a.value - b.value
        })
        const taskTransferVal = dataSourceOrganization(state.dataSource)
        emit('getTaskTransferVal', taskTransferVal)
      }
      const hideChange = (e, record) => {
        $set(record, 'displayFlag', e.target.checked)
        const taskTransferVal = dataSourceOrganization(state.dataSource)
        emit('getTaskTransferVal', taskTransferVal)
      }
      const notJoinChange = (e, record) => {
        $set(record, 'examineFlag', e.target.checked)
        const taskTransferVal = dataSourceOrganization(state.dataSource)
        emit('getTaskTransferVal', taskTransferVal)
      }
      const sortChange = (e, record) => {
        $set(record, 'sort', e)
        const taskTransferVal = dataSourceOrganization(state.dataSource)
        emit('getTaskTransferVal', taskTransferVal)
      }
      const addTask = () => {
        const selectArr = []
        state.leftSelect.forEach(item => {
          selectArr.push(state.allConfigLists.filter(child => child.value === item)[0])
        })

        if (state.dataSource.length) {
          selectArr.forEach(item => {
            state.dataSource.push({
              relationId: item.relationId,
              configId: item.value,
              configName: item.label,
              // 是否隐藏
              displayFlag: false,
              // 是否不参与考核
              examineFlag: false,
              sort: item.sort
            })
          })
        } else {
          state.dataSource = selectArr.map(item => {
            return {
              relationId: item.relationId,
              configId: item.value,
              configName: item.label,
              // 是否隐藏
              displayFlag: false,
              // 是否不参与考核
              examineFlag: false,
              sort: item.sort
            }
          })
        }
        state.leftConfigList = state.leftConfigList.filter((v) =>
          selectArr.every((val) => val.value !== v.value)
        )
        state.leftSelect = []
        const taskTransferVal = dataSourceOrganization(state.dataSource)
        emit('getTaskTransferVal', taskTransferVal)
      }
      const dataSourceOrganization = (dataSource) => {
        return dataSource.map(item => {
          return {
            // 关联关系主键 新增的传空
            relationId: item.relationId,
            // 任务组id
            groupId: item.configId,
            displayFlag: item.displayFlag,
            examineFlag: item.examineFlag,
            sort: item.sort
          }
        })
      }
      watch(() => props.relationList, (newVal, oldVal) => {
        if (!props.isEdit) {
          state.dataSource = props.relationList.map(item => {
            return {
              relationId: item.relationId,
              configId: item.groupId,
              configName: item.relationName,
              // 是否隐藏
              displayFlag: item.displayFlag,
              // 是否不参与考核
              examineFlag: item.examineFlag,
              sort: item.sort
            }
          })
          const taskTransferVal = dataSourceOrganization(state.dataSource)
          emit('getTaskTransferVal', taskTransferVal)
        }
      }, {
        deep: true
      })
      watch(() => state.allConfigLists, (newVal, oldVal) => {
        if (newVal && !props.isEdit) {
          state.leftConfigList = state.leftConfigList.filter((v) =>
            state.dataSource.every((val) => val.configId !== v.value)
          )
        }
      }, {
        deep: true
      })
      watch(() => state.dataSource, (newVal, oldVal) => {
        if (newVal && !props.isEdit) {
          state.leftConfigList = state.leftConfigList.filter((v) =>
            newVal.every((val) => val.configId !== v.value)
          )
        }
      })
      return {
        ...toRefs(state),
        deleteItem,
        hideChange,
        notJoinChange,
        addTask,
        sortChange
      }
    }
  }
</script>

<style lang="scss" scoped>
.border-radius2 {
  border-radius: 2px;
}
.transfer-wrapper{
  max-height: 320px;
  overflow-y: auto;
  :deep(.ant-checkbox-group) {
    display: flex;
    flex-direction: column;
  }
  :deep(.ant-checkbox-wrapper) {
    margin-bottom: 10px !important;
  }
  :deep(.nine-table) {
    padding: 0;
  }
  .checkBox-wrapper {
    width: 80px;
    :deep(.ant-checkbox-wrapper) {
      margin-bottom: 0 !important;
    }
  }
}
</style>
