<template>
    <div class="log-wrapper mt-20 padding-top">
      <a-timeline v-if="logList.length > 0">
        <a-timeline-item v-for="item in logList" :key="item.id">
          <div>
            <span>{{item.comment}}</span>
            <span class="logList-wrapper">
              <LogUserDetail :logUserName="item.userName" :showPop="item.userName !== '系统'"></LogUserDetail>
            </span>
            <span>{{item.createTime}}</span>
          </div>
        </a-timeline-item>
      </a-timeline>
      <a-empty v-else>
        <span slot="description" class="grey-9">暂无日志记录</span>
      </a-empty>
    </div>
</template>

<script>
  import LogUserDetail from '~/pages/after-service/order/components/log-user-detail.vue'

  export default {
    components: { LogUserDetail },
    props: {
      logList: {
        type: Array,
        default: []
      },
    },
    setup (props, { emit }) {
      return {}
    }
  }
</script>

<style lang="scss" scoped>
.log-wrapper {
  max-height: 500px;
  overflow: auto;
}
</style>
