<template>
  <a-modal
    title="日志"
    :visible="openLogModel"
    @cancel="handleCancel"
    maskClosable
    :footer="null"
    :z-index="999"
    :width="650"
  >
    <a-timeline v-if="logList.length > 0">
      <a-timeline-item v-for="item in logList" :key="item.id">
        <div>
          <span>{{item.comment}}</span>
          <span class="logList-wrapper">
              <LogUserDetail :logUserName="item.userName" :showPop="item.userName !== '系统'"></LogUserDetail>
            </span>
          <span>{{item.createTime}}</span>
        </div>
      </a-timeline-item>
    </a-timeline>
    <a-empty v-else />
  </a-modal>
</template>

<script>
  import LogUserDetail from '~/pages/after-service/order/components/log-user-detail.vue'

  export default {
    components: { LogUserDetail },
    props: {
      openLogModel: {
        type: Boolean,
        default: false
      },
      logList: {
        type: Array,
        default: []
      },
    },
    setup (props, { emit }) {
      const handleCancel = () => {
        emit('closeLogModel', false)
      }
      return {
        handleCancel
      }
    }
  }
</script>

<style lang="scss" scoped>
:deep(.ant-modal-body) {
  max-height: 500px;
  overflow: auto;
}
</style>
