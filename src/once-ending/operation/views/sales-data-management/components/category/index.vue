<template>
  <a-tree-select
    v-bind="$attrs"
    v-on="listeners"
    showArrow
    :multiple="multiple"
    tree-node-filter-prop="title"
    treeCheckable
    allowClear
    placeholder="请选择分类"
    :maxTagCount="maxTagCount"
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
    :tree-data="cidOptions"
    @change="onChange"
    :value="value"/>
</template>

<script>
  import { reactive, toRefs, getCurrentInstance, computed } from 'vue'
  import { SALE_STATISTICS } from '@operation/store/modules/statistics-report/action-types'
  export default {
    name: 'category',
    props: {
      // 默认选中的值
      value: {
        type: [String, Number, Array],
        default: undefined
      },
      maxTagCount: {
        type: Number,
        default: 1
      },
      multiple: {
        type: Boolean,
        default: true
      },
    },
    setup (props, { emit }) {
      const root = getCurrentInstance().proxy
      const { $store, $listeners } = root
      const state = reactive({
        cidOptions: []
      })
      // 格式化TreeData数据
      const dealTreeData = (array) => {
        let cache = []
        array.forEach(ite => {
          cache.push({
            title: ite.key,
            key: ite.value,
            value: ite.value,
            children: (ite.children && ite.children.length) ? dealTreeData(ite.children) : []
          })
        })
        return cache
      }
      // 获取商品分类
      const commonStatisticHeader = async function (params) {
        const res = await $store.dispatch(
          `operation/statisticsReport/${SALE_STATISTICS}`,
          params
        )
        if (res.code === 0) {
          const searchCidData = res.data.filter(item => item.key === 'cid')
          state.cidOptions = dealTreeData(searchCidData[0].tree)
        }
      }
      // 获取商品分类
      commonStatisticHeader({ isApp: false })
      const listeners = computed(() => {
        const { change, ...listeners } = $listeners
        return listeners
      })
      const onChange = (value, label, extra) => {
        emit('input', value)
        emit('changeCid', value)
      }
      const filterTreeNode = (value, node) => {
        return node.data.props.title.toLocaleLowerCase().indexOf(value.toLocaleLowerCase()) !== -1
      }
      return {
        ...toRefs(state),
        listeners,
        onChange,
        filterTreeNode
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>
