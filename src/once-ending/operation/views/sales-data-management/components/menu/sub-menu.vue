<template>
  <a-sub-menu :key="menuInfo.path" v-bind="$props" v-on="$listeners">
    <span slot="title">
        <a-icon v-if="menuInfo && menuInfo.icon" :type="menuInfo.icon" class="anticon" />
        <span>{{ menuInfo.meta.title }}</span>
    </span>
    <template v-for="item in menuInfo.children">
      <a-menu-item v-if="!item.children || item.children.length < 1" :key="item.path">
        <a-icon v-if="item && item.icon" :type="item.icon" class="anticon" />
        <router-link :to="item.path">
          <span>{{ item.meta.title }}</span>
        </router-link>
      </a-menu-item>
      <sub-menu v-else :key="item.path" :menu-info="item" v-on="$listeners"/>
    </template>
  </a-sub-menu>
</template>
<script>
  import { Menu } from 'ant-design-vue'
  export default {
    name: 'SubMenu',
    props: {
      ...Menu.SubMenu.props,
      menuInfo: {
        type: Object,
        default: () => {}
      },
    }
  }
</script>
<style lang="scss" scoped>
a:visited {
  color: rgba(0,0,0,0.65);
}
:deep(.router-link-exact-active, .router-link-active) {
  color: #1890ff !important;
}
:deep(.ant-menu-item-selected > a) {
  color: #1890ff !important;
}
:deep(.ant-menu-inline) {
  height: 100%;
}
</style>
