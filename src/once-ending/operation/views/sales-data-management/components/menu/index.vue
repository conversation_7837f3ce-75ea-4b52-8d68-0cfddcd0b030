<template>
  <a-menu mode="inline"
          class="sales-left-menu"
          :selectedKeys="$route.path"
          :openKeys="openKeysList"
          @openChange="onOpenChange"
          @click="menuItemHandle">
    <template v-for="item in routesList">
      <a-menu-item v-if="!(item.children&&item.children.length)">
        <router-link :to="item.path">
          <a-icon v-if="item && item.icon" :type="item.icon" class="anticon" />
          <span>{{ item.meta.title }}</span>
        </router-link>
      </a-menu-item>
      <SubMenu v-else :key="item.path" :menuInfo="item"/>
    </template>
  </a-menu>
</template>

<script>
  import { ref, watch, onMounted } from 'vue'
  import SubMenu from './sub-menu.vue'
  import { useRoute } from 'vue-router/composables'
  import salesTaskApi from '@/operation/api/sales-task-management'
  export default {
    components: {
      SubMenu
    },
    props: {
      routes: {
        type: Array,
        default: []
      },
      collapsed: {
        type: <PERSON><PERSON>an,
        default: false
      }
    },
    setup (props) {
      const route = useRoute()
      const openKeysList = ref(['task-configuration'])
      const sessionSelectedKeys = JSON.parse(sessionStorage.getItem('selectedKeys'))
      const selectedKeys = ref(sessionSelectedKeys || [route.path])
      const routesList = ref([])
      const onOpenChange = (openKeys) => {
        // openKeysList.value = openKeys
        // sessionStorage.setItem('openKeys', JSON.stringify(openKeysList.value))
        if (openKeys.length) {
          if (openKeys.length > 1) {
            openKeysList.value = [openKeys[1]]
          } else {
            openKeysList.value = [openKeys[0]]
          }
        } else {
          openKeysList.value = []
        }
        sessionStorage.setItem('openKeys', JSON.stringify(openKeysList.value))
      }
      const menuItemHandle = ({ item, key, keyPath }) => {
        selectedKeys.value = [key]
        sessionStorage.setItem('selectedKeys', JSON.stringify(selectedKeys.value))
      }
      watch(() => props.routes, (newVal, oldVal) => {
        if (newVal) {
          routesList.value = newVal
        }
      }, {
        deep: true
      })
      watch(() => props.collapsed, (newVal, oldVal) => {
        if (newVal) {
          openKeysList.value = []
        } else {
          const openKeys = sessionStorage.getItem('openKeys')
          if (openKeys) {
            openKeysList.value = JSON.parse(openKeys)
          } else {
            openKeysList.value = ['task-configuration']
            sessionStorage.setItem('openKeys', JSON.stringify(openKeysList.value))
          }
        }
      })
      watch(() => route.path, (newVal, oldVal) => {
        if (newVal) {
          checkOpenKeys()
        }
      })
      watch(() => openKeysList.value, (newVal, oldValue) => {
        if (newVal && newVal.length) {
          if (newVal.join() === 'statistical-report') {
            getTaskMenu()
          }
        }
      })
      onMounted(() => {
        checkOpenKeys()
      })
      const getTaskMenu = () => {
        salesTaskApi.getTaskMenu().then(res => {
          if (res.code === 0) {
            routesList.value = res.data
          }
        })
      }
      const checkOpenKeys = () => {
        const openKeysListArr = ['task-configuration', 'report-management', 'statistical-report']
        const currentOpenKeys = openKeysListArr.filter(item => route.path.includes(item))
        if (currentOpenKeys.length) {
          openKeysList.value = currentOpenKeys
        } else {
          openKeysList.value = []
        }
        sessionStorage.setItem('openKeys', JSON.stringify(openKeysList.value))
      }
      return {
        routesList,
        selectedKeys,
        openKeysList,
        onOpenChange,
        menuItemHandle
      }
    }
  }
</script>

<style lang="scss" scoped>
a:visited {
  color: rgba(0,0,0,0.65);
}
:deep(.router-link-exact-active, .router-link-active) {
  color: #1890ff !important;
}
:deep(.ant-menu-item-selected > a) {
  color: #1890ff !important;
}
:deep(.ant-menu-inline) {
  height: 100%;
}
</style>
