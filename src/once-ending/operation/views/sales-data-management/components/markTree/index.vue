<template>
  <a-tree-select
    v-if="treeData && treeData.length > 0"
    v-bind="$attrs"
    v-on="$listeners"
    :value="value"
    style="width: 100%"
    :tree-data="treeData"
    :replaceFields="replaceFields"
    allowClear
    :showCheckedStrategy="getShow()"
    multiple
    :filterTreeNode="filterTreeNode"
    @change="onChange"
  />
</template>

<script>
  import { ref, getCurrentInstance } from 'vue'
  import salesTaskApi from '../../../../api/sales-task-management'
  import { TreeSelect } from 'ant-design-vue'
  export default {
    props: {
      value: {
        type: String || [String],
        default: undefined
      },
      replaceFields: {
        type: Object,
        default: () => {}
      },
      showCheckedStrategy: {
        type: String,
        default: 'SHOW_CHILD'
      },
    },
    setup (props, { emit }) {
      const root = getCurrentInstance().proxy
      const { $message } = root
      const treeData = ref([])
      const showMap = new Map([
        ['SHOW_ALL', TreeSelect.SHOW_ALL],
        ['SHOW_PARENT', TreeSelect.SHOW_PARENT],
        ['SHOW_CHILD', TreeSelect.SHOW_CHILD]
      ])
      const getShow = () => {
        return showMap.get(props.showCheckedStrategy)
      }
      const replaceFields = ref({
        children: 'childTree',
        title: 'proMark',
        key: 'id',
        value: 'id'
      })
      const getMarkTree = () => {
        salesTaskApi.getMarkTree().then(res => {
          if (res.code === 0) {
            treeData.value = res.data
          } else {
            $message.error(res.userMsg)
          }
        })
      }
      getMarkTree()
      const onChange = (value, label, extra) => {
        emit('input', value)
      }
      const filterTreeNode = (value, node) => {
        return node.data.props.title.toLocaleLowerCase().indexOf(value.toLocaleLowerCase()) !== -1
      }
      return {
        treeData,
        replaceFields,
        onChange,
        getShow,
        filterTreeNode
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>
