<template>
  <a-select
    style="width: 100%"
    v-bind="$attrs"
    :value="value"
    :options="selectOptions"
    :filterOption="filterFunc"
    allowClear
    placeholder="请选择品牌"
    :dropdownStyle="{zIndex: '999'}"
    :dropdownMatchSelectWidth="false"
    :getPopupContainer="(triggerNode) => triggerNode.parentNode"
    :mode="multiple ? 'multiple' : ''"
    v-on="listeners"
    @change="onChange"
    @focus="selectFocus"
    @blur="selectBlur"
    @popupScroll="selectPopupScroll($event)"
    @select="handleSelect"
    @search="selectSearch"
    :maxTagCount="maxTagCount"
  ></a-select>
</template>

<script>
  import { reactive, toRefs, computed, getCurrentInstance, watch } from 'vue'
  import { SALE_STATISTICS } from '@operation/store/modules/statistics-report/action-types'
  import lodashUniq from 'lodash/uniqBy'
  import lodashCloneDeep from 'lodash/cloneDeep'
  export default {
    name: 'Brand',
    props: {
      cateId: {
        type: [String, Number, Array],
        default: () => []
      },
      // 是否多选
      multiple: {
        type: Boolean,
        default: true
      },
      // 默认选中的值
      value: {
        type: [String, Number, Array],
        default: undefined
      },
      maxTagCount: {
        type: Number,
        default: 1
      }
    },
    setup (props, { emit }) {
      const root = getCurrentInstance().proxy
      const { $listeners, $store } = root
      const state = reactive({
        options: [],
        selectOptions: [],
        cacheOptions: [],
        selectSearchValue: ''
      })
      const listeners = computed(() => {
        const { change, ...listeners } = $listeners
        return listeners
      })
      const filterFunc = (inputValue, option) => {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
      }
      const onChange = (value, VNode) => {
        let names = []
        let list = []
        if (value.length || value) {
          list = state.selectOptions.filter(item => {
            if (props.multiple && value?.includes(item.value)) {
              names.push(item.label)
              return item
            }
          })
        }
        emit('input', value, names, list)
        emit('change', value, names, list)
      }
      // 获取商品分类
      const commonStatisticHeader = async function (params) {
        const res = await $store.dispatch(
          `operation/statisticsReport/${SALE_STATISTICS}`,
          params
        )
        if (res.code === 0) {
          const brandOptionsData = res.data.filter(item => item.key === 'brandId')
          state.options = brandOptionsData[0].list.map(item => {
            return {
              label: item.name,
              value: item.value,
              category: item.category
            }
          })
          state.cacheOptions = lodashCloneDeep(state.options)
          state.selectOptions = state.cacheOptions.slice(0, 100)
        }
      }
      // 获取商品分类
      commonStatisticHeader({ isApp: false })
      watch(() => props.cateId, (newVal, oldValue) => {
        if (!newVal) return
        const allOptions = lodashCloneDeep(state.options)
        if (!newVal.length) {
          state.selectOptions = allOptions
          return
        }
        state.selectOptions = allOptions.filter(d => {
          const s = new Set(newVal)
          return [...new Set(d.category || [])].filter(x => s.has(`${x}`)).length
        })
      }, {
        deep: true
      })
      const selectFocus = () => {
        console.log('有值的时候')
        console.log(state.selectOptions)
        if (state.selectOptions.length) {
          return
        }
        state.selectOptions = []
        const options = lodashCloneDeep(state.options)
        state.selectOptions = options.splice(0, 100)
        console.log('没值加载的')
        console.log(state.selectOptions)
        state.selectOptions = lodashUniq(state.selectOptions, 'value')
        state.cacheOptions = options
      }
      const selectBlur = () => {
        state.selectOptions = []
        state.cacheOptions = []
      }
      const selectPopupScroll = (e) => {
        if (!state.cacheOptions.length) {
          return
        }
        const { target } = e
        const scrollHeight = target.scrollHeight - target.scrollTop
        const clientHeight = target.clientHeight
        if (scrollHeight < clientHeight + 5) {
          const options = state.cacheOptions.splice(0, 200)
          state.selectOptions = state.selectOptions.concat(options)
          state.selectOptions = lodashUniq(state.selectOptions, 'value')
          console.log('加载更多的时候')
          console.log(state.selectOptions)
        }
      }
      // 每次用户选择以后判断是否为筛选以后查询,如果是,重置下拉数据,
      const handleSelect = () => {
        if (state.selectSearchValue) {
          const options = lodashCloneDeep(state.options)
          state.selectOptions = options.splice(0, 200)
          state.selectOptions = lodashUniq(state.selectOptions, 'value')
          console.log('选择的时候')
          console.log(state.selectOptions)
          state.cacheOptions = options
          state.selectSearchValue = ''
        }
      }
      // 每次用户输入,匹配所有数据,将数据筛选出来
      const selectSearch = (value) => {
        state.selectSearchValue = value
        const options = lodashCloneDeep(state.options)
        state.selectOptions = options.filter(d => d.label.toLocaleLowerCase().includes(value.toLocaleLowerCase()))
        console.log('搜索的时候')
        console.log(state.selectOptions)
        state.cacheOptions = options.filter(d => !d.label.toLocaleLowerCase().includes(value.toLocaleLowerCase()))
      }
      return {
        ...toRefs(state),
        filterFunc,
        listeners,
        onChange,
        selectFocus,
        selectBlur,
        selectPopupScroll,
        handleSelect,
        selectSearch
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>
