<template>
  <div class="report-com">
    <div class="mt-20 font-18 bold">统计数据导入</div>
    <!--统计数据源-->
    <ni-list-page class="mt-20" :pushFilterToLocation="false">
      <ni-filter :immediate="false" :form="searchWrapper" :loading="loading" @filter="fetchData" :labelWidth="90">
        <ni-filter-item label="地区">
          <ni-area-select
            v-model="searchWrapper.areaIds"
            :allow-clear="true"
            multiple
            :mode="2"
            show-search
            :max-tag-count="1"
            placeholder="请选择"
          />
        </ni-filter-item>
        <ni-filter-item label="状态">
          <div class="flex">
            <a-select style="width: 100px" v-model="searchWrapper.timeType">
              <a-select-option value="2">导入时间</a-select-option>
              <a-select-option value="1">数据时间</a-select-option>
            </a-select>
            <a-range-picker
              v-model="searchWrapper.listTime"
              format="YYYY-MM-DD"
              class="flex-child-average"
              @change="checkPickerTime"
            />
          </div>
        </ni-filter-item>
        <ni-filter-item label="导入人员">
          <ni-staff-select v-model="searchWrapper.searchKey"/>
        </ni-filter-item>
        <ni-filter-item label="类型">
          <a-select v-model="searchWrapper.importDataType" placeholder="请选择" allowClear>
            <a-select-option v-for="item in importDataTypeOption" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
          </a-select>
        </ni-filter-item>
      </ni-filter>
      <ni-table
        :columns="columns"
        :dataSource="dataSource"
        @change="handleTableChange"
        :pagination="pagination"
        :rowKey="record => record.importConfigId"
        :row-selection="{ selectedRowKeys: otherSelectedRowKeys, onChange: onSelectList }"
        class="mt-20"
      >
        <div slot="staffSlot" slot-scope="text, record">
          <span v-if="text">{{ text }}</span>
          <p v-else>-</p>
        </div>
        <div slot="importDataTypeStrSlot" slot-scope="text, record">
          <span v-if="text">{{ text }}</span>
          <p v-else>-</p>
        </div>
        <div slot="action">
          <div class="flex flex-align-center">
            <span class="blue mr-16 pointer" @click="downLoadFile">模板下载</span>
            <a-button class="mr-16" type="primary" @click="uploadExcel">导入</a-button>
            <form ref="fileForm">
              <input style="display: none" ref="file" type="file" accept=".xls,.xlsx" @change="selectFile"></input>
            </form>
            <a-button type="primary" @click="batchDelete">批量删除</a-button>
          </div>
        </div>
        <div class="flex flex-justify-between" slot="controllerSlot" slot-scope="text, record">
          <a-popconfirm
            title="确定删除?"
            @confirm="() => deleteItem(record)"
          >
            <span class="blue pointer">删除</span>
          </a-popconfirm>
        </div>
      </ni-table>
    </ni-list-page>
    <!--统计数据源-->
  </div>
</template>

<script>
  import { reactive, toRefs, getCurrentInstance, ref } from 'vue'
  import { NiListPage, NiFilter, NiFilterItem, NiTable, NiAreaSelect, NiStaffSelect } from '@jiuji/nine-ui'
  import salesTaskApi from '../../../api/sales-task-management'
  import moment from 'moment'
  export default {
    components: {
      NiListPage,
      NiFilter,
      NiFilterItem,
      NiTable,
      NiAreaSelect,
      NiStaffSelect
    },
    setup () {
      const fileForm = ref(null)
      const file = ref(null)
      const root = getCurrentInstance().proxy
      const { $message, $indicator, $confirm } = root
      const state = reactive({
        searchWrapper: {
          areaIds: undefined,
          timeType: '1',
          listTime: undefined,
          startTime: '',
          endTime: '',
          searchKey: undefined,
          importDataType: undefined
        },
        loading: false,
        columns: [
          {
            title: '大区',
            dataIndex: 'upperDepart'
          },
          {
            title: '小区',
            dataIndex: 'lowerDepart'
          },
          {
            title: '门店',
            dataIndex: 'area'
          },
          {
            title: '员工',
            dataIndex: 'staff',
            scopedSlots: { customRender: 'staffSlot' },
          },
          {
            title: '日期',
            dataIndex: 'businessDate'
          },
          {
            title: '数值',
            dataIndex: 'value'
          },
          {
            title: '类型',
            dataIndex: 'importDataTypeStr',
            scopedSlots: { customRender: 'importDataTypeStrSlot' },
          },
          {
            title: '导入时间',
            dataIndex: 'importDate'
          },
          {
            title: '导入员工',
            dataIndex: 'importStaff'
          },
          {
            title: '操作',
            dataIndex: 'controller',
            scopedSlots: { customRender: 'controllerSlot' },
          }
        ],
        dataSource: [],
        test: undefined,
        taskName: undefined,
        pagination: {
          current: 1,
          pageSize: 10,
          total: 0,
          pageSizeOptions: ['10', '20', '50', '100', '200'],
          showSizeChanger: true,
          showTotal: total => `总共 ${total} 条`
        },
        otherSelectedRowKeys: [],
        otherListRowlist: [],
        importDataTypeOption: []
      })
      const handleTableChange = (pagination) => {
        state.pagination.current = pagination.current
        state.pagination.pageSize = pagination.pageSize
        initData()
      }
      const deleteItem = (record) => {
        deleteHandle([record.importConfigId])
      }
      const deleteHandle = (params, type) => {
        $indicator.open()
        salesTaskApi.batchDelete(params).then(res => {
          if (res.code === 0) {
            $message.success(res.userMsg)
            if (type === 'all') {
              state.otherSelectedRowKeys = []
              state.otherListRowlist = []
            }
            initData()
          }
        }).finally(() => {
          $indicator.close()
        })
      }
      const onSelectList = (selectedRowKeys, selectedRows) => {
        state.otherSelectedRowKeys = selectedRowKeys
        state.otherListRowlist = selectedRows
        console.log(state.otherSelectedRowKeys)
        console.log(state.otherListRowlist)
      }
      const batchDelete = () => {
        if (!state.otherSelectedRowKeys.length) {
          return $message.warn('请先选择删除项')
        }
        console.log(state.otherSelectedRowKeys)
        $confirm({
          title: '确定删除吗?',
          onOk () {
            deleteHandle(state.otherSelectedRowKeys, 'all')
          },
        })
      }
      const downLoadFile = () => {
        window.open('https://img2.ch999img.com/newstatic/25796/08dff4c425b9eae7.xlsx?dl=1')
      }
      const initData = () => {
        let params = {
          areaIds: state.searchWrapper.areaIds,
          timeType: state.searchWrapper.timeType,
          startTime: state.searchWrapper.startTime ? state.searchWrapper.startTime + ' 00:00:00' : '',
          endTime: state.searchWrapper.endTime ? state.searchWrapper.endTime + ' 23:59:59' : '',
          searchKey: state.searchWrapper.searchKey,
          importDataType: state.searchWrapper.importDataType,
          current: state.pagination.current,
          size: state.pagination.pageSize
        }
        salesTaskApi.importPage(params).then(res => {
          if (res.code === 0) {
            state.dataSource = res.data.records
            state.pagination.total = res.data.total
          } else {
            $message.error(res.userMsg)
          }
        })
      }
      const fetchData = () => {
        state.pagination.current = 1
        state.pagination.pageSize = 10
        initData()
      }
      fetchData()
      const checkPickerTime = (date, dateString) => {
        state.searchWrapper.startTime = dateString[0] + ''
        state.searchWrapper.endTime = dateString[1]
      }
      // 数据导入
      const uploadExcel = () => {
        file.value.click()
      }
      // 选中导入文件
      const selectFile = async (e) => {
        const selectFile = e.target.files[0]
        if (selectFile && selectFile.length === 0) return
        const regExcel = /.(xls|xlsx)$/i
        if (!regExcel.test(selectFile.name)) {
          $message.error('导入模板不符合导入规则，数据导入失败，请重新导入')
          return
        }
        const formData = new FormData()
        formData.append('file', selectFile)
        try {
          const res = await salesTaskApi.importDetail(formData)
          if (res.code === 0) {
            $message.success('导入成功')
            initData()
          } else {
            $message.error(res.userMsg)
          }
        } catch (e) {
          console.error(e)
        } finally {
          e.target.value = ''
        }
      }
      const getConfigTypeEnum = () => {
        const params = {
          subKind: 1
        }
        salesTaskApi.getConfigTypeEnum(params).then(res => {
          if (res.code === 0) {
            state.importDataTypeOption = res.data.filter(item => item.key === 'importDataType')[0]?.itemList
          } else {
            $message.error(res.userMsg)
          }
        })
      }
      getConfigTypeEnum()
      return {
        moment,
        fileForm,
        file,
        ...toRefs(state),
        handleTableChange,
        deleteItem,
        onSelectList,
        batchDelete,
        downLoadFile,
        fetchData,
        checkPickerTime,
        uploadExcel,
        selectFile
      }
    }
  }
</script>

<style lang="scss" scoped>
.report-com{
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  padding: 0 15px 10px 10px;
  .labelWidth {
    width: 165px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .border-radius2 {
    border-radius: 2px;
  }
}
:deep(.nine-table) {
  padding: 0;
}
:deep(.tool-box .actions) {
  .ant-btn {
    display: none;
  }
}
</style>
