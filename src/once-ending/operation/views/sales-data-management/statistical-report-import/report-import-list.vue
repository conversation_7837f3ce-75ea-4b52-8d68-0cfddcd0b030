<template>
  <div class="task-wrapper">
    <div class="mt-20 mb-20 font-18 bold">{{ metaTitle }}</div>
    <ni-list-page>
      <ni-table
        :columns="columns"
        :dataSource="dataSource"
        @change="handleTableChange"
        :pagination="pagination"
        class="mt-20"
      >
        <div slot="cccSlot" slot-scope="text, record">
          <div v-if="text.length > 30">
            <a-popover overlayClassName="poperLayTable">
              <template slot="content">
                <div>{{ text }}</div>
              </template>
              <span class="pointer">{{ text.substring(0, 30) + '...' }}</span>
            </a-popover>
          </div>
          <span v-else>{{ text }}</span>
        </div>
        <div slot="action">
          <a-button type="primary" class="mb-8" @click="goTaskProjectEdit">添加统计数据</a-button>
        </div>
        <div class="flex flex-justify-between" slot="controllerSlot" slot-scope="text, record">
          <span class="blue pointer">详情</span>
          <a-popconfirm
            title="确定删除?"
            @confirm="() => deleteItem(record)"
          >
            <span class="blue pointer">删除</span>
          </a-popconfirm>
          <span class="blue pointer" @click="viewLog">查看日志</span>
        </div>
      </ni-table>
    </ni-list-page>
    <LogModel :logList="logList" :openLogModel="openLogModel" @closeLogModel="closeLogModel"/>
  </div>
</template>

<script>
  import { reactive, toRefs } from 'vue'
  import { useRoute, useRouter } from 'vue-router/composables'
  import { tabList } from '../constants'
  import { NiListPage, NiTable } from '@jiuji/nine-ui'
  import LogModel from '../components/sales-data-log'
  export default {
    components: {
      NiListPage,
      NiTable,
      LogModel
    },
    setup () {
      const route = useRoute()
      const router = useRouter()
      const metaTitle = route.meta.title
      const state = reactive({
        columns: [
          {
            title: '任务项名称',
            dataIndex: 'aaa',
            width: '200',
          },
          {
            title: '统计指标',
            dataIndex: 'bbb',
            width: '200',
          },
          {
            title: '关联任务组',
            dataIndex: 'ccc',
            width: 500,
            scopedSlots: { customRender: 'cccSlot' },
          },
          {
            title: '使用次数',
            dataIndex: 'ddd',
            width: '150',
          },
          {
            title: '排序',
            dataIndex: 'eee',
            width: '150',
          },
          {
            title: '操作',
            dataIndex: 'controller',
            width: '150',
            scopedSlots: { customRender: 'controllerSlot' },
          }
        ],
        dataSource: [
          {
            aaa: '任务项名称',
            bbb: '统计指标',
            ccc: '关联任务组关联任务组关联任务组关联任务组关联任务组关联任务组关联任务组关联任务组关联任务组',
            ddd: '使用次数',
            eee: '排序',
          }
        ],
        pagination: {
          current: 1,
          pageSize: 10,
          total: 0,
          pageSizeOptions: ['10', '20', '50', '100', '200'],
          showSizeChanger: true,
          showTotal: total => `总共 ${total} 条`
        },
        logList: [],
        openLogModel: false
      })
      const handleTableChange = (pagination) => {
        state.pagination.current = pagination.current
        state.pagination.pageSize = pagination.pageSize
      }
      const deleteItem = (record) => {
        console.log(record)
      }
      const viewLog = () => {
        state.openLogModel = true
      }
      const closeLogModel = (value) => {
        state.openLogModel = value
      }
      const goTaskProjectEdit = () => {}
      return {
        ...toRefs(state),
        tabList,
        metaTitle,
        handleTableChange,
        deleteItem,
        viewLog,
        closeLogModel,
        goTaskProjectEdit,
      }
    }
  }
</script>

<style lang="scss" scoped>
.task-wrapper{
  :deep(.nine-table-bar) {
    border-bottom: 1px solid #dedede;
    padding: 8px 0 0 0;
    margin-bottom: 15px;
  }
}
:deep(.ant-table-thead > tr > th) {
  font-weight: bold;
}
</style>
<style lang="scss">
.poperLayTable > .ant-popover-content > .ant-popover-inner {
  width: 300px;
}
</style>
