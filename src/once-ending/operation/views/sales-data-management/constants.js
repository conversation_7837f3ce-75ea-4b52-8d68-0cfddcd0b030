const routes = [
  {
    path: 'task-configuration',
    meta: { title: '任务管理' },
    icon: 'project',
    children: [
      {
        path: '/operation/sales-data/task-configuration/task-project-configuration',
        meta: { title: '任务项配置' },
        icon: '',
        children: []
      },
      {
        path: '/operation/sales-data/task-configuration/task-project-manage',
        meta: { title: '任务组管理' },
        icon: '',
        children: []
      },
      {
        path: '/operation/sales-data/task-configuration/task-configuration-manage',
        meta: { title: '任务配置' },
        icon: '',
        children: []
      },
      {
        path: '/operation/sales-data/task-configuration/task-achieve-inquire',
        meta: { title: '任务达成查询' },
        icon: '',
        children: []
      },
    ]
  },
  {
    path: 'report-management',
    meta: { title: '报表管理' },
    icon: 'pie-chart',
    children: [
      {
        path: '/operation/sales-data/report-management/report-statistics-configuration',
        meta: { title: '报表统计项配置' },
        icon: '',
        children: []
      },
      {
        path: '/operation/sales-data/report-management/report-configuration',
        meta: { title: '报表配置' },
        icon: '',
        children: []
      },
    ]
  },
  {
    path: 'statistical-report',
    meta: { title: '统计报表' },
    icon: 'bar-chart',
    children: [
      {
        path: '/operation/sales-data/statistical-report/report-detail/1',
        meta: { title: '报表1' },
        icon: '',
        children: []
      },
      {
        path: '/operation/sales-data/statistical-report/report-detail/2',
        meta: { title: '报表2' },
        icon: '',
        children: []
      },
    ]
  },
  {
    path: '/operation/sales-data/statistical-report-import',
    meta: { title: '统计项数据导入' },
    icon: 'download',
    children: []
  },
]

// 1、销售 2、回收 3、维修 4、运营商 5、其它
const tabList = [
  {
    label: '销售',
    value: 'sale',
    tabTag: 1
  },
  {
    label: '回收',
    value: 'recycle',
    tabTag: 2
  },
  {
    label: '维修',
    value: 'repair',
    tabTag: 3
  },
  {
    label: '运营商',
    value: 'operator',
    tabTag: 4
  }
]

const containOrNotOption = [
  {
    label: '包含',
    value: 1,
  },
  {
    label: '不包含',
    value: 2,
  }
]

const NumberCheck = (num) => {
  num = num.replace(/[^\d.]/g, '')
  num = num.replace(/\.{2,}/g, '.')
  num = num.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
  num = num.replace(/^(\d+)\.(\d\d).*$/, '$1.$2')
  if (num.indexOf('.') < 0 && num !== '') {
    num = parseFloat(num)
  }
  let strObj = num.toString()
  if (strObj.indexOf('.') > -1 && strObj === '0.00') {
    num = parseFloat(num).toFixed(1)
  }
  return num
}

export const SUB_ID_URL = new Map([
  ['1', '/addOrder/editOrder?SubID='],
  ['2', '/recoverIndex/detail?id='], // 回收
  ['3', '/shouhou/edit/'],
])

export {
  routes,
  tabList,
  containOrNotOption,
  NumberCheck
}
