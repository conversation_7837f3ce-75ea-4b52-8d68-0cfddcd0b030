<template>
  <div class="report-wrapper">
    <div class="mt-20 mb-20 font-18 bold">{{ metaTitle }}</div>
    <ni-list-page>
      <ni-table
        :columns="columns"
        :dataSource="dataSource"
        @change="handleTableChange"
        :pagination="pagination"
        class="mt-20"
      >
        <div slot="relationshipGroupSlot" slot-scope="text, record">
          <div v-if="text.length > 50">
            <a-popover overlayClassName="poperLayTable">
              <template slot="content">
                <div>{{ text }}</div>
              </template>
              <span class="pointer">{{ text.substring(0, 50) + '...' }}</span>
            </a-popover>
          </div>
          <span v-else>{{ text }}</span>
        </div>
        <div slot="tool">
          <ul class="tab-wrapper">
            <li :class="tabActive === item.value ? 'tabActive':''" v-for="item in tabList" :key="item.value" @click="tabActiveChange(item)">{{ item.label }}</li>
          </ul>
        </div>
        <div slot="action">
          <a-button type="primary" @click="goReportProjectEdit()">添加统计项</a-button>
        </div>
        <div slot="projectNameSlot" slot-scope="text, record">
          <router-link :to="{
            path: `/operation/sales-data/report-management/report-statistics-configuration/${record ? record.projectId : 0}`,
            query: {
              taskFlag: tabActive,
              subKind: tabTag,
              projectId: record ? record.projectId : undefined,
              useCount: record ? record.useCount : undefined,
              viewOnly: 1,
              readOnly: 1
            }
          }">{{ text }}</router-link>
        </div>
        <div class="flex" slot="controllerSlot" slot-scope="text, record">
          <a-button type="link" :disabled="record.useCount > 0" @click="goReportProjectEdit(record)">编辑</a-button >
          <a-popconfirm
            v-if="record.useCount === 0"
            title="确定删除?"
            @confirm="() => deleteItem(record)"
          >
            <a-button type="link" :disabled="record.useCount > 0">删除</a-button>
          </a-popconfirm>
          <a-button v-else type="link" :disabled="record.useCount > 0">删除</a-button>
<!--          <a-button type="link" @click="viewLog(record)">查看日志</a-button>-->
        </div>
      </ni-table>
    </ni-list-page>
    <LogModel :logList="logList" :openLogModel="openLogModel" @closeLogModel="closeLogModel"/>
  </div>
</template>

<script>
  import { reactive, toRefs, watch, getCurrentInstance, onUnmounted } from 'vue'
  import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router/composables'
  import { tabList } from '../constants'
  import { NiListPage, NiTable } from '@jiuji/nine-ui'
  import LogModel from '../components/sales-data-log'
  import salesTaskApi from '@/operation/api/sales-task-management'
  export default {
    components: {
      NiListPage,
      NiTable,
      LogModel
    },
    setup () {
      const route = useRoute()
      const router = useRouter()
      const root = getCurrentInstance().proxy
      const { $message, $indicator } = root
      const metaTitle = route.meta.title
      const state = reactive({
        columns: [
          {
            title: '统计项名称',
            dataIndex: 'projectName',
            width: '200px',
            scopedSlots: { customRender: 'projectNameSlot' },
          },
          {
            title: '统计指标',
            dataIndex: 'businessTypeStr',
          },
          {
            title: '关联报表',
            dataIndex: 'relationshipGroup',
            width: '400px',
            scopedSlots: { customRender: 'relationshipGroupSlot' },
          },
          {
            title: '使用次数',
            dataIndex: 'useCount',
          },
          {
            title: '排序',
            dataIndex: 'rank',
          },
          {
            title: '操作',
            dataIndex: 'controller',
            width: '120px',
            scopedSlots: { customRender: 'controllerSlot' },
          }
        ],
        dataSource: [],
        pagination: {
          current: 1,
          pageSize: 10,
          total: 0,
          pageSizeOptions: ['10', '20', '50', '100', '200'],
          showSizeChanger: true,
          showTotal: total => `总共 ${total} 条`
        },
        tabActive: sessionStorage.getItem('tabActive') || 'sale',
        tabTag: Number(sessionStorage.getItem('tabTag')) || 1,
        logList: [],
        openLogModel: false
      })
      const handleTableChange = (pagination) => {
        state.pagination.current = pagination.current
        state.pagination.pageSize = pagination.pageSize
        initData()
      }
      const deleteItem = (record) => {
        $indicator.open()
        const params = {
          projectId: record.projectId
        }
        salesTaskApi.deleteProjectConfig(params).then(res => {
          if (res.code === 0) {
            $message.success('删除成功')
            initData()
          } else {
            $message.error(res.userMsg)
          }
        }).finally(() => {
          $indicator.close()
        })
      }
      const viewLog = async (record) => {
        state.openLogModel = true
        await getLogList(record)
      }
      const getLogList = (record) => {
        const params = {
          businessId: record.projectId,
          businessType: 1
        }
        salesTaskApi.getLogList(params).then(res => {
          if (res.code === 0) {
            state.logList = res.data.map(item => {
              return {
                comment: item.content,
                userName: item.operateUser,
                createTime: item.operateTime
              }
            })
          }
        })
      }
      const closeLogModel = (value) => {
        state.openLogModel = value
      }
      const goReportProjectEdit = (record, type) => {
        router.push({
          path: `/operation/sales-data/report-management/report-statistics-configuration/${record ? record.projectId : 0}`,
          query: {
            taskFlag: state.tabActive,
            subKind: state.tabTag,
            projectId: record ? record.projectId : undefined,
            useCount: record ? record.useCount : undefined,
            viewOnly: type === 'view' ? 1 : 2
          }
        })
      }
      const tabActiveChange = (tabItem) => {
        state.tabActive = tabItem.value
        state.tabTag = tabItem.tabTag
        sessionStorage.setItem('tabActive', state.tabActive)
        sessionStorage.setItem('tabTag', state.tabTag)
      }
      const initData = () => {
        $indicator.open()
        const params = {
          configType: 1,
          subKind: state.tabTag,
          current: state.pagination.current,
          size: state.pagination.pageSize
        }
        salesTaskApi.projectConfigPage(params).then(res => {
          state.dataSource = res.data.records
          state.pagination.total = res.data.total
        }).finally(() => {
          $indicator.close()
        })
      }
      initData()
      watch(() => state.tabTag, (newVal, oldVal) => {
        if (newVal) {
          initData()
        }
      })
      onBeforeRouteLeave((to, from, next) => {
        if (from.path.includes('/report-management/')) {
          state.tabActive = 'sale'
          sessionStorage.setItem('tabTag', 1)
        }
        next()
      })
      onUnmounted(() => {
        sessionStorage.setItem('tabActive', 'sale')
        sessionStorage.setItem('tabTag', 1)
      })
      return {
        ...toRefs(state),
        tabList,
        metaTitle,
        handleTableChange,
        deleteItem,
        viewLog,
        closeLogModel,
        goReportProjectEdit,
        tabActiveChange
      }
    }
  }
</script>

<style lang="scss" scoped>
.report-wrapper{
  :deep(.nine-table-bar) {
    border-bottom: 1px solid #dedede;
    padding: 8px 0 0 0;
    margin-bottom: 15px;
  }
  .tab-wrapper{
    display: flex;
    height: 36px;
    align-items: center;
    li{
      margin-left: 10px;
      margin-right: 30px;
      cursor: pointer;
      &.tabActive {
        color: #1890ff;
        font-weight: bold;
      }
    }
  }
}
:deep(.ant-table-thead > tr > th) {
  font-weight: bold;
}
</style>
<style lang="scss">
.poperLayTable > .ant-popover-content > .ant-popover-inner {
  width: 500px;
}
</style>
