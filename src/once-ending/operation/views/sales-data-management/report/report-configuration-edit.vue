<template>
  <div class="report-com">
    <div class="mt-20 mb-20 font-18 bold">报表配置</div>
    <a-row class="mb-20">
      <a-col :span="11">
        <div class="flex flex-align-center">
          <div class="labelWidth">
            <span class="inline-block red mr-5 mt-5">*</span>
            <p class="font-14 bold">报表名称：</p>
          </div>
          <div class="flex-child-average flex flex-align-center">
            <a-input placeholder="请输入" v-model="taskInfo.groupName" :maxLength="100" style="width: 90%" allowClear :disabled="readOnly"></a-input>
            <span class="grey-9">{{ taskInfo.groupName ? taskInfo.groupName.length : 0 }}/100</span>
          </div>
        </div>
      </a-col>
      <a-col :span="2"></a-col>
      <a-col :span="11">
        <div class="flex flex-align-center">
          <div class="labelWidth">
            <span class="inline-block red mr-5 mt-5">*</span>
            <p class="font-14 bold">排序：</p>
          </div>
          <div class="flex-child-average">
            <a-input-number v-model="taskInfo.rank" :min="0" style="width: 100%" placeholder="请输入" :disabled="readOnly"/>
          </div>
        </div>
      </a-col>
    </a-row>
    <a-row class="mb-20">
      <a-col :span="6">
        <div class="flex flex-align-center">
          <div class="labelWidth">
            <span class="inline-block red mr-5 mt-5">*</span>
            <p class="font-14 bold">状态：</p>
          </div>
          <div class="flex-child-average">
            <a-radio-group name="statusGroup" v-model="taskInfo.status" :disabled="readOnly">
              <a-radio :value="item.value" v-for="item in statusOption" :key="item.value">{{ item.label }}</a-radio>
            </a-radio-group>
          </div>
        </div>
      </a-col>
    </a-row>
    <a-row class="mb-10">
      <a-col :span="11">
        <div class="flex ml-20">
          <span class="inline-block red mr-5 mt-5">*</span>
          <p>报表统计项(<span class="grey-9">选择此报表包含的统计项</span>)</p>
        </div>
      </a-col>
    </a-row>
    <a-row class="mb-20">
      <a-col :span="18" class="ml-20">
        <TaskTransfer :configType="1" title="统计项" :relationType="1" :transferType="1" @getTaskTransferVal="getTaskTransferVal" :relationList="relationListOption" :isEdit="!$route.params.id" :disabled="readOnly"/>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="18" class="ml-20">
        <div class="flex flex-justify-end" v-if="!viewOnly">
          <a-button class="mr-20" @click="$router.go(-1)">取消</a-button>
          <a-button type="primary" @click="saveProjectConfig">保存</a-button>
        </div>
      </a-col>
    </a-row>
    <div class="logs-warp mt-20" v-if="logList">
      <LogList class="mt-20" :logList="logList"></LogList>
    </div>
  </div>
</template>

<script>
  import { getCurrentInstance, reactive, toRefs } from 'vue'
  import { useRoute, useRouter } from 'vue-router/composables'
  import salesTaskApi from '@/operation/api/sales-task-management'
  import TaskTransfer from '@/operation/views/sales-data-management/components/task-transfer/index.vue'
  import LogList from '@/operation/views/sales-data-management/components/sales-data-log/sales-log.vue'
  export default {
    components: { LogList, TaskTransfer },
    setup () {
      const route = useRoute()
      const router = useRouter()
      const root = getCurrentInstance().proxy
      const { $message, $indicator } = root
      const state = reactive({
        statusOption: [],
        // allConfigLists: [],
        taskInfo: {
          // 任务组ID
          groupId: route.params.id && route.params.id !== '0' ? route.params.id : undefined,
          // 任务组名称
          groupName: undefined,
          // 排序
          rank: undefined,
          // 状态
          status: 1,
          relationList: []
        },
        relationListOption: [],
        viewOnly: route.query.viewOnly === '1',
        logList: [],
        readOnly: route.query.readOnly === '1',
      })
      const getGroupConfigTypeEnum = () => {
        salesTaskApi.getGroupConfigTypeEnum('').then(res => {
          if (res.code === 0) {
            // 状态值
            state.statusOption = res.data.filter(item => item.key === 'status')[0]?.itemList
          } else {
            $message.error(res.userMsg)
          }
        })
      }
      getGroupConfigTypeEnum()
      // const allConfigList = () => {
      //   let params = {
      //     // 查询分类 1、报表 2、任务
      //     configType: 1,
      //     // 查询类型 1、任务项 2、任务组（这里的报表配置获取的是报表统计项的配置）
      //     relationType: 1
      //   }
      //   salesTaskApi.allConfigList(params).then(res => {
      //     if (res.code === 0) {
      //       state.allConfigLists = res.data.map(item => {
      //         return {
      //           label: item.configName,
      //           value: item.configId
      //         }
      //       })
      //     } else {
      //       $message.error(res.userMsg)
      //     }
      //   })
      // }
      // allConfigList()
      const saveProjectConfig = () => {
        const params = { ...state.taskInfo }
        if (!params.groupName) return $message.warn('请输入报表名称')
        if (!params.rank && params.rank !== 0) return $message.warn('请输入排序值')
        if (!params.status && params.status !== 0) return $message.warn('请选择状态')
        if (params.relationList.length < 1) return $message.warn('请选择报表统计项')
        params.relationList = params.relationList.map(item => {
          return {
            projectId: item.groupId,
            relationId: item.relationId,
            displayFlag: item.displayFlag,
            examineFlag: item.examineFlag,
            sort: item.sort
          }
        })
        params.configType = 1
        $indicator.open()
        salesTaskApi.saveGroupConfig(params).then(res => {
          if (res.code === 0) {
            $message.success('保存成功')
            router.replace({
              path: `/operation/sales-data/report-management/report-configuration/${res.data.id}`,
              query: {
                viewOnly: route.query.viewOnly
              }
            })
          } else {
            $message.error(res.userMsg)
          }
        }).finally(() => {
          $indicator.close()
        })
      }
      const groupDetail = () => {
        const params = {
          groupId: route.params.id
        }
        salesTaskApi.groupDetail(params).then(res => {
          if (res.code === 0) {
            state.taskInfo.groupName = res.data.groupName
            state.taskInfo.rank = res.data.rank
            state.taskInfo.status = res.data.status
            state.relationListOption = res.data.relationList.map(item => {
              return {
                configType: item.configType,
                displayFlag: item.displayFlag,
                examineFlag: item.examineFlag,
                groupId: item.projectId,
                projectId: item.projectId,
                relationId: item.relationId,
                relationName: item.relationName,
                sort: item.sort
              }
            })
          } else {
            $message.error(res.userMsg)
          }
        })
      }
      if (route.params.id && route.params.id !== '0') {
        groupDetail()
      }
      const getTaskTransferVal = (val) => {
        state.taskInfo.relationList = val
      }
      const getLogList = () => {
        const params = {
          businessId: route.params.id,
          businessType: 2
        }
        salesTaskApi.getLogList(params).then(res => {
          if (res.code === 0) {
            state.logList = res.data.map(item => {
              return {
                comment: item.content,
                userName: item.operateUser,
                createTime: item.operateTime
              }
            })
          }
        })
      }
      getLogList()
      return {
        ...toRefs(state),
        saveProjectConfig,
        getTaskTransferVal
      }
    }
  }
</script>

<style lang="scss" scoped>
.report-com{
  background: #FFFFFF;
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  padding: 0 15px 10px 10px;
  .labelWidth {
    width: 100px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .border-radius2 {
    border-radius: 2px;
  }
  .basketTypeOption {
    :deep(.ant-checkbox-group-item) {
      padding-bottom: 6px;
    }
  }
}
</style>
