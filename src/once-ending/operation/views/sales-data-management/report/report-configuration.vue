<template>
  <div class="report-wrapper">
    <div class="mt-20 mb-20 font-18 bold">{{ metaTitle }}</div>
    <ni-list-page class="mt-20" :pushFilterToLocation="false">
      <ni-filter :immediate="false" :form="searchWrapper" :loading="loading" @filter="fetchData" :labelWidth="90">
        <ni-filter-item label="报表名称">
          <a-input v-model="searchWrapper.groupName" placeholder="请输入报表名称" allowClear/>
        </ni-filter-item>
        <ni-filter-item label="状态">
          <a-select placeholder="请选择" v-model="searchWrapper.status" allowClear>
            <a-select-option value="1">启用</a-select-option>
            <a-select-option value="0">停用</a-select-option>
          </a-select>
        </ni-filter-item>
      </ni-filter>
      <ni-table
        class="mt-20"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="pagination"
        @change="handleTableChange"
      >
        <div slot="groupIdSlot" slot-scope="text, record">
          <router-link :to="{
            path: `/operation/sales-data/report-management/report-configuration/${record ? record.groupId : 0}/report-statistics-detail`,
            query: {
              viewOnly: 1,
              readOnly: 1
            }
          }">{{ text }}</router-link>
        </div>
        <div slot="areaKindSlot" slot-scope="text, record">
          <span v-if="text">{{ text }}</span>
          <span v-else>-</span>
        </div>
        <div slot="areaTypeSlot" slot-scope="text, record">
          <span v-if="text">{{ text }}</span>
          <span v-else>-</span>
        </div>
        <div slot="relationshipProjectSlot" slot-scope="text, record">
          <div v-if="text.length > 50">
            <a-popover overlayClassName="poperLayTable">
              <template slot="content">
                <div>{{ text }}</div>
              </template>
              <span class="pointer">{{ text.substring(0, 50) + '...' }}</span>
            </a-popover>
          </div>
          <span v-else>{{ text }}</span>
        </div>
        <div slot="controllerSlot" slot-scope="text, record">
          <span class="pointer blue mr-16" @click="goTaskManageEdit(record)">编辑</span>
          <span class="pointer blue mr-16" @click="updateGroupStatus(record)">{{ record.status === '启用' ? '停用' : '启用'}}</span>
        </div>
        <div slot="action">
          <a-button type="primary" @click="goTaskManageEdit()">添加报表</a-button>
        </div>
      </ni-table>
    </ni-list-page>
    <LogModel :logList="logList" :openLogModel="openLogModel" @closeLogModel="closeLogModel"/>
  </div>
</template>

<script>
  import { reactive, toRefs, getCurrentInstance, onActivated } from 'vue'
  import { useRoute, useRouter } from 'vue-router/composables'
  import { NiListPage, NiFilter, NiFilterItem, NiTable } from '@jiuji/nine-ui'
  import LogModel from '../components/sales-data-log'
  import salesTaskApi from '../../../api/sales-task-management'
  export default {
    components: {
      NiListPage,
      NiFilter,
      NiFilterItem,
      NiTable,
      LogModel
    },
    beforeRouteEnter (to, from, next) {
      // if (from.path.includes('report-statistics-detail')) {
      //   to.meta.isBack = true
      // } else {
      //   to.meta.isBack = false
      // }
      // next()
      if (from.name !== 'report-statistics-edit' && from.name !== 'report-statistics-detail') {
        to.meta.isBack = true
      }
      next()
    },
    setup () {
      const route = useRoute()
      const router = useRouter()
      const root = getCurrentInstance().proxy
      const { $indicator, $message } = root
      const state = reactive({
        metaTitle: route.meta.title,
        searchWrapper: {
          groupName: undefined,
          status: undefined,
        },
        loading: false,
        logList: [],
        openLogModel: false,
        columns: [
          {
            title: '报表id',
            dataIndex: 'groupId',
            scopedSlots: { customRender: 'groupIdSlot' },
          },
          {
            title: '报表名称',
            dataIndex: 'groupName',
            width: '200px'
          },
          {
            title: '报表统计项',
            dataIndex: 'relationshipProject',
            width: '400px',
            scopedSlots: { customRender: 'relationshipProjectSlot' },
          },
          {
            title: '状态',
            dataIndex: 'status'
          },
          {
            title: '排序',
            dataIndex: 'rank'
          },
          {
            title: '操作',
            dataIndex: 'controller',
            width: '80px',
            scopedSlots: { customRender: 'controllerSlot' },
          }
        ],
        dataSource: [],
        pagination: {
          current: 1,
          pageSize: 10,
          total: 0,
          pageSizeOptions: ['10', '20', '50', '100', '200'],
          showSizeChanger: true,
          showTotal: total => `总共 ${total} 条`
        },
      })
      const initData = () => {
        $indicator.open()
        let params = {
          groupName: state.searchWrapper.groupName,
          status: state.searchWrapper.status,
          configType: 1,
          current: state.pagination.current,
          size: state.pagination.pageSize
        }
        salesTaskApi.groupConfigPage(params).then(res => {
          if (res.code === 0) {
            state.dataSource = res.data.records
            state.pagination.total = res.data.total
          } else {
            $message.error(res.userMsg)
          }
        }).finally(() => {
          $indicator.close()
        })
      }
      const fetchData = () => {
        state.pagination.current = 1
        state.pagination.pageSize = 10
        initData()
      }
      // fetchData()
      const handleTableChange = (pagination) => {
        state.pagination.current = pagination.current
        state.pagination.pageSize = pagination.pageSize
        initData()
      }
      const goTaskManageEdit = (record, type) => {
        router.push({
          path: `/operation/sales-data/report-management/report-configuration/${record ? record.groupId : 0}`,
          query: {
            viewOnly: type === 'view' ? 1 : 2
          }
        })
      }
      const updateGroupStatus = (record) => {
        const params = {
          groupId: record.groupId,
          status: record.status === '启用' ? 0 : 1
        }
        $indicator.open()
        salesTaskApi.updateGroupStatus(params).then(res => {
          if (res.code === 0) {
            $message.success(res.userMsg)
            initData()
          } else {
            $message.warn(res.userMsg)
          }
        }).finally(() => {
          $indicator.close()
        })
      }
      onActivated(() => {
        // if (!route.meta.isBack) {
        //   state.searchWrapper.groupName = undefined
        //   state.searchWrapper.status = undefined
        //   fetchData()
        // }
        // route.meta.isBack = false
        if (route.meta.isBack) {
          route.meta.isBack = false
          state.searchWrapper.groupName = undefined
          state.searchWrapper.status = undefined
        }
        fetchData()
      })
      return {
        ...toRefs(state),
        fetchData,
        handleTableChange,
        goTaskManageEdit,
        updateGroupStatus
      }
    }
  }
</script>

<style lang="scss" scoped>
.report-wrapper{}
</style>
