<template>
  <div>
    <component :is="getComponent(taskFlag)" :taskFlagName="taskFlagName"></component>
  </div>
</template>

<script>
  import { reactive, toRefs } from 'vue'
  import { useRoute } from 'vue-router/composables'
  import ReportSale from '../components/report-project/report-sale.vue'
  import ReportRecycle from '../components/report-project/report-recycle.vue'
  import ReportRepair from '../components/report-project/report-repair.vue'
  import ReportOperator from '../components/report-project/report-operator.vue'
  import { tabList } from '../constants'
  export default {
    components: {
      ReportSale,
      ReportRecycle,
      ReportRepair,
      ReportOperator,
    },
    setup () {
      const route = useRoute()
      const index = route.path.lastIndexOf('/')
      const newPath = route.path.substring(0, index)
      const state = reactive({
        taskFlag: route.query.taskFlag,
        taskFlagName: ''
      })
      const tabsMap = new Map([
        ['sale', ReportSale],
        ['recycle', ReportRecycle],
        ['repair', ReportRepair],
        ['operator', ReportOperator]
      ])
      sessionStorage.setItem('selectedKeys', JSON.stringify([newPath]))
      const getTabListName = () => {
        state.taskFlagName = tabList.filter(item => item.value === route.query.taskFlag)[0].label
      }
      getTabListName()
      const getComponent = (value) => {
        return tabsMap.get(value)
      }
      return {
        ...toRefs(state),
        tabsMap,
        getComponent
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>
