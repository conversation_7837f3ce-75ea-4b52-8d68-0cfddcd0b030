<template>
  <div class="task-wrapper">
    <div class="mt-20 mb-20 font-18 bold ">任务量配置</div>
    <div class="task-con">
      <div class="mb-20 flex flex-align-center">
        <div class="flex flex-align-center mr-16">
          <div class="flex flex-child-noshrink">
            <span class="inline-block red mr-5 mt-3">*</span>
            <p class="font-14 bold">任务组名称：</p>
          </div>
          <a-select style="width: 240px" placeholder="请选择" :disabled="parseInt($route.params.id) > 0 || readOnly" show-search v-model="searchWrapper.groupId" @change="groupChange" :filter-option="filterOption" allowClear>
            <a-select-option v-for="item in allConfigLists" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
          </a-select>
        </div>
        <div class="flex flex-align-center mr-16">
          <div class="flex flex-child-noshrink">
            <span class="inline-block red mr-5 mt-3">*</span>
            <p class="font-14 bold">任务名称：</p>
          </div>
          <a-input placeholder="请输入任务名称" style="width: 300px" v-model="searchWrapper.taskConfigName" :maxLength="100" allowClear :disabled="readOnly"></a-input>
          <span class="grey-9">{{ searchWrapper.taskConfigName ? searchWrapper.taskConfigName.length : 0 }}/100</span>
        </div>
        <div class="flex flex-align-center mr-16">
          <div class="flex flex-child-noshrink">
            <span class="inline-block red mr-5 mt-3">*</span>
            <p class="font-14 bold">任务周期：</p>
          </div>
          <a-select style="width: 240px" placeholder="请选择" :disabled="editFlag || readOnly" v-model="searchWrapper.taskConfigCycle" @change="taskConfigCycleChange" allowClear>
            <a-select-option v-for="item in taskConfigCycle" :key="item.value" :value="item.value+''">{{ item.label }}</a-select-option>
          </a-select>
        </div>
        <div class="flex flex-align-center mr-16" v-if="searchWrapper.taskConfigCycle">
          <div class="flex flex-child-noshrink">
            <span class="inline-block red mr-5 mt-3">*</span>
            <p class="font-14 bold">时间：</p>
          </div>
          <a-range-picker
            style="width: 320px"
            v-if="taskConfigCycleVal === '0'"
            v-model="listTime"
            :mode="['year', 'year']"
            format="YYYY-MM-DD"
            allowClear
            :disabled="editFlag || readOnly"
            :placeholder="['请选择年份', '请选择年份']"
            @panelChange="(val) => checkPickerTime(val, 'year')"
          />
          <a-range-picker
            style="width: 320px"
            v-if="taskConfigCycleVal === '1'"
            v-model="listTime"
            :mode="['month', 'month']"
            format="YYYY-MM-DD"
            allowClear
            :disabled="editFlag || readOnly"
            :placeholder="['请选择月份', '请选择月份']"
            @panelChange="(val) => checkPickerTime(val, 'month')"
          />
          <a-range-picker
            style="width: 320px"
            v-if="taskConfigCycleVal === '2'"
            v-model="listTime"
            format="YYYY-MM-DD"
            allowClear
            :disabled="editFlag || readOnly"
            :placeholder="['请选择时间', '请选择时间']"
            @change="(val) => checkPickerTime(val, '')"
          />
        </div>
      </div>
<!--      <ni-table-->
<!--        bordered-->
<!--        :columns="columns"-->
<!--        :dataSource="dataSource"-->
<!--        :pagination="false"-->
<!--        :rowKey="record => record.dimensionId"-->
<!--      >-->
<!--        <div slot="dimensionName" slot-scope="text, record">-->
<!--          <span class="blue pointer" @click="gotoDetail(record)">{{ text }}</span>-->
<!--        </div>-->
<!--        <template slot-scope="text, record" :slot="slotName(item, 'target')" v-for="item in columnsSlot">-->
<!--          <a-input placeholder="请输入" :disabled="!isEdit" v-model="text" allowClear @change="slotChange(text, record, `${item}target`)"></a-input>-->
<!--        </template>-->
<!--        <template slot-scope="text, record" :slot="slotName(item, 'percent')" v-for="item in columnsSlot">-->
<!--          <a-input placeholder="请输入" :disabled="!isEdit" v-model="text" allowClear @change="slotChange(text, record, `${item}percent`)"></a-input>-->
<!--        </template>-->
<!--      </ni-table>-->
      <div class="white-bg">
        <canvas-table
          v-if="columns.length"
          :data="dataSource"
          :columns="columns"
          ref="canvasTable"
          :max-height="tableHeight()"
          v-bind="canvasTableAttrs"
        />
        <a-empty style="padding: 100px" v-else/>
      </div>
      <div class="flex flex-justify-end mt-20 mb-20" v-if="isEdit && !viewOnly">
        <a-button type="primary" @click="saveConfig">保存</a-button>
      </div>
    </div>
    <div class="logs-warp mt-20" v-if="logList">
      <LogList class="mt-20" :logList="logList"></LogList>
    </div>
  </div>
</template>

<script type="text/jsx" lang="jsx">
  import { reactive, toRefs, ref, computed, getCurrentInstance, onMounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router/composables'
  import moment from 'moment'
  // import { NiTable } from '@jiuji/nine-ui'
  import salesTaskApi from '../../../api/sales-task-management'
  import CanvasTable from '~/components/kt-canvas-table'
  import { NumberCheck } from '../constants'
  import LogList from '@/operation/views/sales-data-management/components/sales-data-log/sales-log.vue'

  export default {
    components: {
      LogList,
      // NiTable,
      CanvasTable
    },
    setup () {
      const route = useRoute()
      const router = useRouter()
      const listTime = ref([])
      const state = reactive({
        searchWrapper: {
          groupId: route.query.groupId ? Number(route.query.groupId) : undefined,
          taskConfigName: undefined,
          taskConfigCycle: undefined,
          startTime: undefined,
          endTime: undefined,
        },
        viewOnly: route.query.viewOnly === '1',
        readOnly: route.query.readOnly === '1',
        taskConfigCycle: undefined,
        columns: [],
        columnsSlot: [],
        dataSource: [],
        allConfigLists: [],
        // 后台返回的表头
        targetInfoList: [],
        // 后台返回的表格数据
        dimensionList: [],
        editFlag: route.query.editFlag === 'false',
        canvasTableAttrs: {
          border: true,
          stripe: true,
          rowHeight: 42,
          customStyle: {
            headerFontWeight: '500',
            borderColor: '#dfdfdf',
            headerBackground: '#f5f5f5',
            color: '#333',
            fontSize: 13,
            activeColBackground: '#F6FBFF',
            activeRowBackground: '#F6FBFF',
            stripeRowBackground: '#fafafa',
            summaryBackground: '#ffffff',
            background: '#fff',
          },
        },
        dataIndexArr: [],
        currentEditFlag: route.query.editFlag,
        logList: []
      })
      const root = getCurrentInstance().proxy
      const { $message, $set, $store, $indicator } = root
      const taskConfigCycleVal = computed(() => {
        return state.searchWrapper.taskConfigCycle
      })
      const tableHeight = () => {
        return document.body.clientHeight - 180
      }
      const isEdit = computed(() => {
        const userInfo = $store.state.userInfo
        if (userInfo.Rank.includes('qqrw') || userInfo.Rank.includes('dqrw') || userInfo.Rank.includes('xqrw') || userInfo.Rank.includes('mdrw')) {
          return true
        } else {
          return false
        }
      })
      const checkPickerTime = (value, type) => {
        console.log(value)
        console.log(type)
        if (type === 'year') {
          const startTime = moment(value[0]).format('YYYY')
          const endTime = moment(value[1]).format('YYYY')
          state.searchWrapper.startTime = startTime + '-01-01'
          state.searchWrapper.endTime = endTime + '-12-31'
        } else if (type === 'month') {
          const startTime = moment(value[0]).format('YYYY-MM')
          const endTime = moment(value[1]).format('YYYY-MM')
          state.searchWrapper.startTime = moment(startTime).startOf('month').format('YYYY-MM-DD')
          state.searchWrapper.endTime = moment(endTime).endOf('month').format('YYYY-MM-DD')
        } else {
          state.searchWrapper.startTime = moment(value[0]).format('YYYY-MM-DD')
          state.searchWrapper.endTime = moment(value[1]).format('YYYY-MM-DD')
        }
        listTime.value = [state.searchWrapper.startTime, state.searchWrapper.endTime]
      }
      const taskConfigCycleChange = () => {
        state.searchWrapper.startTime = undefined
        state.searchWrapper.endTime = undefined
        listTime.value = []
      }
      const getTaskConfigEnum = () => {
        salesTaskApi.getTaskConfigEnum('').then(res => {
          if (res.code === 0) {
            state.taskConfigCycle = res.data.filter(item => item.key === 'taskConfigCycle')[0]?.itemList
          }
        })
      }
      getTaskConfigEnum()
      const allConfigList = () => {
        let params = {
          // 查询分类 1、报表 2、任务
          configType: 2,
          // 查询类型 1、任务项 2、任务组（这里的报表配置获取的是报表统计项的配置）
          relationType: 2
        }
        salesTaskApi.allConfigList(params).then(res => {
          if (res.code === 0) {
            state.allConfigLists = res.data.map(item => {
              return {
                label: item.configName,
                value: item.configId
              }
            })
          } else {
            $message.success(res.userMsg)
          }
        })
      }
      allConfigList()
      const groupChange = () => {
        state.columns = []
        state.dataSource = []
        state.columnsSlot = []
        if (state.searchWrapper.groupId) {
          getConfigDetail()
        }
      }
      const filterOption = (input, option) => {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      }
      const getConfigDetail = () => {
        state.columns = []
        state.dataSource = []
        state.columnsSlot = []
        let newDimensionList = []
        $indicator.open()
        let params = {
          // 任务配置id
          taskConfigId: route.params.id && route.params.id !== '0' ? route.params.id : undefined,
          // 任务组id
          groupId: state.searchWrapper.groupId ? state.searchWrapper.groupId : undefined,
          // 维度
          dimensionType: route.query.dimensionType ? route.query.dimensionType : undefined,
          // 维度id
          regionId:	route.query.regionId ? route.query.regionId : undefined,
          configType: 2
        }
        salesTaskApi.getConfigDetail(params).then(res => {
          if (res.code === 0) {
            if (route.params.id && route.params.id !== '0') {
              state.searchWrapper.groupId = res.data.groupId
              state.searchWrapper.taskConfigName = res.data.taskConfigName
              state.searchWrapper.taskConfigCycle = res.data.taskConfigCycle.toString()
              state.searchWrapper.startTime = res.data.startTime ? moment(res.data.startTime) : undefined
              state.searchWrapper.endTime = res.data.endTime ? moment(res.data.endTime) : undefined
              listTime.value = [state.searchWrapper.startTime, state.searchWrapper.endTime]
            }
            state.targetInfoList = res.data.targetInfoList || []
            state.targetInfoList.forEach(item => {
              let newList = {
                title: '',
                dataIndex: '',
                label: '',
                key: '',
                align: 'center',
                children: []
              }
              newList.title = item.projectName
              newList.dataIndex = item.projectId
              newList.label = item.projectName
              newList.key = item.projectId

              if (item.children && item.children.length > 0) {
                state.dataIndexArr = item.children.map(item => item.key)
                item.children.forEach(children => {
                  newList.children.push({
                    title: children.title,
                    dataIndex: item.projectId + children.key,
                    label: children.title,
                    key: item.projectId + children.key,
                    minWidth: 150,
                    align: 'center',
                    // scopedSlots: { customRender: item.projectId + children.key }
                    customRender: (h, { row, col }) => {
                      return <a-input placeholder="请输入" value={ row[item.projectId + children.key] } disabled={!isEdit || state.readOnly} allowClear onChange={(e) => slotChange(e, row, item.projectId + children.key)}>
                        {
                          children.key.includes('percent') ? <a-tooltip slot="suffix" title="Extra information">
                            <a-icon type="percentage" />
                          </a-tooltip> : null
                        }
                      </a-input>
                    },
                  })
                })
              }
              state.columnsSlot.push(item.projectId)
              state.columns.push(newList)
            })
            const oneColumns = {
              label: '区域',
              key: 'regionSlot',
              // scopedSlots: { customRender: 'dimensionName' },
              width: 200,
              fixed: 'left',
              align: 'center',
              customRender: (h, { row, col }) => {
                return (
                  <div class="full-width text-center">
                    {row.dimensionId === -1 ? <span class="grey-9">{ row.dimensionName }</span> : ''}
                    {row.upperDepart ? <span class="blue pointer" onClick={() => gotoDetail(row, 'upperDepart')}>{row.upperDepart}</span> : ''}
                    {row.lowerDepart ? <span class="blue pointer" onClick={() => gotoDetail(row, 'lowerDepart')}> { row.upperDepart && row.lowerDepart ? ' / ' : '' } {row.lowerDepart}</span> : ''}
                    {row.area ? <span class="blue pointer" onClick={() => gotoDetail(row, 'area')}> { row.upperDepart || row.lowerDepart ? ' / ' : '' } {row.area}</span> : ''}
                    {row.staff ? <span class="black"> { row.upperDepart || row.lowerDepart || row.area ? ' / ' : '' } {row.staff}</span> : ''}
                  </div>
                )
              },
            }
            if (state.targetInfoList.length) {
              state.columns.unshift(oneColumns)
            }
            // 处理表格数据
            state.dimensionList = res.data.dimensionList || []
            if (state.dimensionList.length > 0) {
              state.dimensionList.forEach(item => {
                let newDimensionListObj = {}
                newDimensionListObj.dimension = item.dimension
                newDimensionListObj.dimensionId = item.dimensionId
                newDimensionListObj.dimensionName = item.dimensionName
                newDimensionListObj.upperDepart = item.upperDepart
                newDimensionListObj.upperDepartDimension = item.upperDepartDimension
                newDimensionListObj.upperDepartDimensionId = item.upperDepartDimensionId
                newDimensionListObj.lowerDepart = item.lowerDepart
                newDimensionListObj.lowerDepartDimension = item.lowerDepartDimension
                newDimensionListObj.lowerDepartDimensionId = item.lowerDepartDimensionId
                newDimensionListObj.area = item.area
                newDimensionListObj.areaDimension = item.areaDimension
                newDimensionListObj.areaDimensionId = item.areaDimensionId
                newDimensionListObj.staff = item.staff
                newDimensionListObj.staffDimension = item.staffDimension
                newDimensionListObj.staffDimensionId = item.staffDimensionId
                newDimensionListObj.taskConfigId = res.data.taskConfigId || 0
                newDimensionListObj.selfDimension = item.selfDimension
                if (item.targetList && item.targetList.length > 0) {
                  item.targetList.forEach(child => {
                    newDimensionListObj[`${child.projectId}targetId`] = child.targetId
                    state.dataIndexArr.forEach(item => {
                      newDimensionListObj[`${child.projectId}${item}`] = child[item]
                    })
                  })
                }
                newDimensionList.push(newDimensionListObj)
              })
              console.log(newDimensionList)
              $set(state, 'dataSource', newDimensionList.sort((a, b) => a.dimensionId - b.dimensionId))
            } else {
              if (!state.dimensionList.length && Number(route.query.dimensionType) < 4) {
                router.replace({
                  path: `/operation/sales-data/task-configuration/task-configuration-manage/${res.data.taskConfigId ? res.data.taskConfigId : 0}`,
                  query: {
                    dimensionType: Number(route.query.dimensionType) + 1,
                    editFlag: state.currentEditFlag,
                    configType: 2,
                    dimensionId: route.query.dimensionId,
                    groupId: route.params.id === '0' ? state.searchWrapper.groupId : undefined,
                    viewOnly: route.query.viewOnly,
                    readOnly: route.query.readOnly
                  }
                })
              }
              // else {
              //   if (Number(res.data.groupDimension) < 4) {
              //     router.replace({
              //       path: `/operation/sales-data/task-configuration/task-configuration-manage/${res.data.taskConfigId ? res.data.taskConfigId : 0}`,
              //       query: {
              //         dimensionType: Number(res.data.groupDimension) + 1,
              //         editFlag: state.currentEditFlag,
              //         configType: 2,
              //         dimensionId: route.query.dimensionId,
              //         groupId: route.params.id === '0' ? state.searchWrapper.groupId : undefined,
              //         viewOnly: route.query.viewOnly,
              //         readOnly: route.query.readOnly
              //       }
              //     })
              //   }
              // }
            }
          } else {
            $message.error(res.userMsg)
          }
        }).finally(() => {
          $indicator.close()
        })
      }
      if (route.params.id && route.params.id !== '0') {
        getConfigDetail()
      }
      const saveConfig = () => {
        const { groupId, taskConfigName, taskConfigCycle, startTime, endTime } = state.searchWrapper
        console.log(state.searchWrapper.startTime)
        console.log(state.searchWrapper.endTime)
        if (!groupId) { return $message.warn('请选择任务组') }
        if (!taskConfigName) { return $message.warn('请输入任务名称') }
        if (!taskConfigCycle) { return $message.warn('请选择任务周期') }
        if (taskConfigCycle && listTime.value.length < 2) { return $message.warn('请选择时间') }
        if (taskConfigCycle && !state.searchWrapper.startTime && !state.searchWrapper.endTime) { return $message.warn('请选择时间') }
        let newDataSource = []
        console.log(state.dataSource)
        state.dataSource.forEach(item => {
          let newArrObj = {
            dimensionId: '',
            targetList: []
          }
          newArrObj.dimensionId = item.dimensionId
          state.columnsSlot.forEach(projectId => {
            newArrObj.targetList.push({
              projectId: projectId,
              dimension: item.selfDimension,
              percent: item[projectId + 'percent'],
              target: item[projectId + 'target'],
              targetId: item[projectId + 'targetId']
            })
          })
          newDataSource.push(newArrObj)
        })
        const params = {
          taskConfigId: route.params.id && route.params.id !== '0' ? route.params.id : undefined,
          taskConfigName: taskConfigName,
          groupId: groupId,
          taskConfigCycle: taskConfigCycle,
          startTime: startTime ? moment(startTime).format('YYYY-MM-DD') : undefined,
          endTime: endTime ? moment(endTime).format('YYYY-MM-DD') : undefined,
          dimensionList: newDataSource
        }
        console.log(params)
        $indicator.open()
        salesTaskApi.saveConfig(params).then(res => {
          if (res.code === 0) {
            $message.success(res.userMsg)
            router.replace({
              path: `/operation/sales-data/task-configuration/task-configuration-manage/${res.data.taskConfigId ? res.data.taskConfigId : 0}`,
              query: {
                dimensionType: res.data.dimension ? res.data.dimension : undefined,
                editFlag: res.data.editFlag ? res.data.editFlag : undefined,
                configType: 2
              }
            })
          } else {
            $message.error(res.userMsg)
          }
        }).finally(() => {
          $indicator.close()
        })
      }
      const slotChange = (text, record, keywords) => {
        if (text.target.value) {
          $set(record, keywords, NumberCheck(text.target.value))
        }
        if (text.type === 'click') {
          $set(record, keywords, '')
        }
      }
      const slotName = (val1, val2) => {
        return val1 + val2
      }
      const gotoDetail = (record, type) => {
        console.log(record)
        let dimensionType = record ? record.dimension : undefined
        let editFlag = state.currentEditFlag
        let groupId = state.searchWrapper.groupId
        let regionId = record.dimensionId ? record.dimensionId : undefined
        if (type === 'upperDepart') {
          if (record.lowerDepartDimensionId || record.areaDimensionId || record.staffDimensionId) {
            dimensionType = record.upperDepartDimension
            regionId = undefined
            groupId = route.params.id === '0' ? groupId : undefined
          } else {
            dimensionType = record.dimension
            regionId = record.dimensionId
          }
        } else if (type === 'lowerDepart') {
          if (record.areaDimensionId || record.staffDimensionId) {
            dimensionType = record.lowerDepartDimension
            regionId = record.upperDepartDimensionId
          } else {
            dimensionType = record.dimension
            regionId = record.lowerDepartDimensionId
          }
        } else if (type === 'area') {
          if (record.staffDimensionId) {
            dimensionType = record.areaDimension
            regionId = record.areaDimensionId
          } else {
            dimensionType = record.dimension
            regionId = record.dimensionId
          }
        }
        console.log('groupId:' + groupId)
        router.push({
          path: `/operation/sales-data/task-configuration/task-configuration-manage/${record.taskConfigId ? record.taskConfigId : 0}`,
          query: {
            dimensionType,
            editFlag,
            groupId,
            regionId,
            dimensionId: record.dimensionId,
            viewOnly: route.query.viewOnly,
            readOnly: route.query.readOnly,
          }
        })
      }
      onMounted(() => {
        state.searchWrapper.groupId = route.query.groupId ? Number(route.query.groupId) : undefined
        if (state.searchWrapper.groupId && route.params.id === '0') {
          state.columns = []
          state.dataSource = []
          state.columnsSlot = []
          getConfigDetail()
        }
      })
      const getLogList = () => {
        const params = {
          businessId: route.params.id,
          businessType: 3
        }
        salesTaskApi.getLogList(params).then(res => {
          if (res.code === 0) {
            state.logList = res.data.map(item => {
              return {
                comment: item.content,
                userName: item.operateUser,
                createTime: item.operateTime
              }
            })
          }
        })
      }
      getLogList()
      return {
        moment,
        ...toRefs(state),
        listTime,
        taskConfigCycleVal,
        checkPickerTime,
        taskConfigCycleChange,
        saveConfig,
        slotChange,
        slotName,
        filterOption,
        isEdit,
        gotoDetail,
        tableHeight,
        groupChange
      }
    }
  }
</script>

<style lang="scss" scoped>
.task-wrapper{
  padding: 0 15px 0 5px;
  .task-con {
    padding: 12px;
    background: #FFFFFF;
    .mt-3{
      margin-top: 3px;
    }
  }
}
:deep(.ant-input-suffix) {
  z-index: 1;
}
:deep(.nine-table) {
  padding: 0;
}
</style>
