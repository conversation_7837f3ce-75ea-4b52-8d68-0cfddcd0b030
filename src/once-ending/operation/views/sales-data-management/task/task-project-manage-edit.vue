<template>
  <div class="task-com">
    <div class="mt-20 font-18 bold">任务组配置</div>
    <a-row class="mt-20 mb-20">
      <a-col :span="18">
        <div class="flex flex-align-center">
          <div class="labelWidth">
            <span class="inline-block red mr-5 mt-5">*</span>
            <p class="font-14 bold">任务组名称：</p>
          </div>
          <div class="flex-child-average">
            <a-input placeholder="请输入" v-model="taskInfo.groupName" :maxLength="100" allowClear :disabled="readOnly"></a-input>
          </div>
          <span class="grey-9">{{ taskInfo.groupName ? taskInfo.groupName.length : 0 }}/100</span>
        </div>
      </a-col>
    </a-row>
    <a-row class="mb-20">
      <a-col>
        <div class="flex flex-align-center">
          <div class="labelWidth">
            <span class="inline-block red mr-5 mt-5">*</span>
            <p class="font-14 bold">任务组维度：</p>
            <a-tooltip placement="top">
              <template slot="title">
                <p>大区：选择了大区则此任务组在“地区”里面选择的所有大区，大区下的小区、门店都在一个任务组里</p>
                <p>小区：选择了小区则此任务组在“地区”里面选择的所有小区、小区下的门店都在一个任务组里面</p>
                <p>门店：选择了门店则此任务组在“地区”里面选择的所有门店在一个任务组里</p>
              </template>
              <a-icon type="question-circle" style="color: #8C8C8C;" class="mr-8"/>
            </a-tooltip>
          </div>
          <div class="flex-child-average">
            <a-radio-group name="radioGroup" :disabled="notEditFieldFlag || readOnly" v-model="taskInfo.dimension" @change="radioChange">
              <a-radio :value="item.value" v-for="item in dimensionOption" :key="item.value">{{ item.label }}</a-radio>
            </a-radio-group>
          </div>
        </div>
      </a-col>
    </a-row>
    <a-row class="mb-20">
      <a-col>
        <div class="flex flex-align-center">
          <div class="labelWidth">
            <p class="font-14 bold ml-20">门店类型：</p>
          </div>
          <div class="flex-child-average">
            <a-radio-group name="radioGroup" :disabled="notEditFieldFlag || readOnly" v-model="taskInfo.areaKind">
              <a-radio :value="item.value" v-for="item in areaKindOption" :key="item.value" @click.native="(e) => radioClick(e, 'areaKind')">{{ item.label }}</a-radio>
            </a-radio-group>
          </div>
        </div>
      </a-col>
    </a-row>
    <a-row class="mb-20" v-if="taskInfo.dimension !== 3">
      <a-col>
        <div class="flex flex-align-center">
          <div class="labelWidth">
            <p class="font-14 bold ml-20">门店类别：</p>
          </div>
          <div class="flex-child-average">
            <a-radio-group name="radioGroup" :disabled="notEditFieldFlag || readOnly" v-model="taskInfo.areaType">
              <a-radio :value="item.value" v-for="item in areaTypeOption" :key="item.value" @click.native="(e) => radioClick(e, 'areaType')">{{ item.label }}</a-radio>
            </a-radio-group>
          </div>
        </div>
      </a-col>
    </a-row>
    <a-row class="mb-20">
      <a-col :span="8">
        <div class="flex flex-align-center">
          <div class="labelWidth">
            <p class="font-14 bold" style="width: 60px;padding-left: 10px;">地区：</p>
          </div>
          <div class="flex-child-average">
            <ni-area-select
              v-model="taskInfo.areaIds"
              :allow-clear="true"
              multiple
              show-search
              :max-tag-count="3"
              style="min-width: 500px"
              :disabled="readOnly"
            />
          </div>
        </div>
      </a-col>
    </a-row>
    <a-row class="mb-20">
      <a-col :span="8">
        <div class="flex flex-align-center">
          <div class="labelWidth" style="width: 60px">
            <span class="inline-block red mr-5 mt-5">*</span>
            <p class="font-14 bold">排序：</p>
          </div>
          <div class="flex-child-average">
            <a-input-number v-model="taskInfo.rank" :min="0" style="width: 150px" :disabled="readOnly"/>
          </div>
        </div>
      </a-col>
    </a-row>
    <a-row class="mb-20">
      <a-col>
        <div class="flex flex-align-center">
          <div class="labelWidth" style="width: 60px">
            <span class="inline-block red mr-5 mt-5">*</span>
            <p class="font-14 bold">状态：</p>
          </div>
          <div class="flex-child-average">
            <a-radio-group name="radioGroup" v-model="taskInfo.status" :disabled="readOnly">
              <a-radio :value="item.value" v-for="item in statusOption" :key="item.value">{{ item.label }}</a-radio>
            </a-radio-group>
          </div>
        </div>
      </a-col>
    </a-row>
    <div class="mt-20 mb-20 font-14 bold"><span class="inline-block red mr-5 mt-5">*</span>任务项(<span class="normal grey-9">选择此任务组里面的任务项</span>)</div>
    <a-row class="mb-20">
      <a-col :span="18">
        <TaskTransfer title="任务项" :configType="2" :relationType="1" :transferType="2" @getTaskTransferVal="getTaskTransferVal" :relationList="relationListOption" :isEdit="!$route.params.id" :disabled="readOnly"/>
      </a-col>
    </a-row>
    <a-row class="mb-20">
      <a-col :span="18">
        <div class="flex flex-justify-end mt-20 mb-20" v-if="!viewOnly">
          <a-button class="mr-20" @click="$router.go(-1)">取消</a-button>
          <a-button type="primary" @click="saveProjectConfig">保存</a-button>
        </div>
      </a-col>
    </a-row>
    <div class="logs-warp mb-20" v-if="logList">
      <LogList class="mt-20" :logList="logList"></LogList>
    </div>
  </div>
</template>

<script>
  import { getCurrentInstance, reactive, toRefs, watch } from 'vue'
  import TaskTransfer from '../components/task-transfer'
  import salesTaskApi from '../../../api/sales-task-management'
  import { NiAreaSelect } from '@jiuji/nine-ui'
  import { useRoute, useRouter } from 'vue-router/composables'
  import lodashJoin from 'lodash/join'
  import lodashSplit from 'lodash/split'
  import LogList from '@/operation/views/sales-data-management/components/sales-data-log/sales-log.vue'
  export default {
    components: {
      TaskTransfer,
      NiAreaSelect,
      LogList
    },
    setup () {
      const route = useRoute()
      const router = useRouter()
      const root = getCurrentInstance().proxy
      const { $message, $indicator } = root
      const state = reactive({
        // 任务组维度
        dimensionOption: [],
        // 门店类别
        areaTypeOption: [],
        // 门店类型
        areaKindOption: [],
        // 状态值
        statusOption: [],
        taskInfo: {
          // 任务组ID
          groupId: route.params.id && route.params.id !== '0' ? route.params.id : undefined,
          // 任务组名称
          groupName: undefined,
          // 任务组维度
          dimension: undefined,
          // 配置分类 1、报表 2、任务
          configType: undefined,
          // 门店类型
          areaKind: undefined,
          // 门店类别
          areaType: undefined,
          // 地区
          areaIds: undefined,
          // 排序
          rank: undefined,
          // 状态
          status: 1,
          // 关联关系
          relationList: []
        },
        relationListOption: [],
        // 为ture则不能修改维度、门店类型、门店类别
        notEditFieldFlag: false,
        viewOnly: route.query.viewOnly === '1',
        logList: [],
        readOnly: route.query.readOnly === '1',
      })
      const getGroupConfigTypeEnum = () => {
        salesTaskApi.getGroupConfigTypeEnum('').then(res => {
          if (res.code === 0) {
            // 任务组维度
            state.dimensionOption = filterOptions(res.data, 'dimension').filter(item => item.value !== 4)
            // 门店类别
            state.areaTypeOption = filterOptions(res.data, 'areaType')
            // 门店类型
            state.areaKindOption = filterOptions(res.data, 'areaKind')
            // 状态值
            state.statusOption = filterOptions(res.data, 'status')
          } else {
            $message.success(res.userMsg)
          }
        })
      }
      const filterOptions = (data, type) => {
        return data.filter(item => item.key === type)[0]?.itemList
      }
      getGroupConfigTypeEnum()
      const saveProjectConfig = () => {
        let params = { ...state.taskInfo }
        // 配置分类 1、报表 2、任务
        params.configType = 2
        if (!params.groupName) return $message.warn('请输入任务组名称')
        if (!params.dimension) return $message.warn('请选择任务组维度')
        // if ([1, 2, 4].includes(params.dimension) && !params.areaKind) return $message.warn('请选择门店类型')
        // if ([1, 2, 4].includes(params.dimension) && !params.areaType) return $message.warn('请选择门店类别')
        if (params.dimension && params.dimension === 3) {
          if ((!params.areaIds || (params.areaIds && !params.areaIds.length)) && !params.areaKind) return $message.warn('门店类型、地区必选一项')
        }
        if (params.dimension && params.dimension !== 3) {
          if ((!params.areaIds || (params.areaIds && !params.areaIds.length)) && !params.areaKind && !params.areaType) return $message.warn('门店类型、门店类别、地区必选一项')
        }
        if (!params.rank && params.rank !== 0) return $message.warn('请输入排序值')
        if (!params.status && params.status !== 0) return $message.warn('请选择状态')
        if (params.relationList.length < 1) return $message.warn('请选择任务项')
        params.areaType = params.areaType ? params.areaType : null
        params.areaKind = params.areaKind ? params.areaKind : null
        params.relationList = params.relationList.map(item => {
          return {
            projectId: item.groupId,
            relationId: item.relationId,
            displayFlag: item.displayFlag,
            examineFlag: item.examineFlag,
            sort: item.sort
          }
        })
        params.areaIds = lodashJoin(params.areaIds)
        console.log(params)
        $indicator.open()
        salesTaskApi.saveGroupConfig(params).then(res => {
          if (res.code === 0) {
            $message.success('保存成功')
            router.replace({
              path: `/operation/sales-data/task-configuration/task-project-manage/${res.data.id}`,
              query: {
                viewOnly: route.query.viewOnly
              }
            })
          } else {
            $message.error(res.userMsg)
          }
        }).finally(() => {
          $indicator.close()
        })
      }
      const getTaskTransferVal = (val) => {
        state.taskInfo.relationList = val
        console.log(state.taskInfo.relationList)
      }
      const groupDetail = () => {
        const params = {
          groupId: route.params.id
        }
        salesTaskApi.groupDetail(params).then(res => {
          if (res.code === 0) {
            state.taskInfo = { ...res.data }
            state.notEditFieldFlag = res.data.notEditFieldFlag
            state.taskInfo.areaIds = res.data.areaIds ? lodashSplit(res.data.areaIds, ',') : undefined
            state.taskInfo.areaType = res.data.areaType ? res.data.areaType : undefined
            state.taskInfo.areaKind = res.data.areaKind ? res.data.areaKind : undefined
            state.relationListOption = res.data.relationList.map(item => {
              return {
                configType: item.configType,
                displayFlag: item.displayFlag,
                examineFlag: item.examineFlag,
                groupId: item.projectId,
                projectId: item.projectId,
                relationId: item.relationId,
                relationName: item.relationName,
                sort: item.sort
              }
            })
          } else {
            $message.success(res.userMsg)
          }
        })
      }
      if (route.params.id && route.params.id !== '0') {
        groupDetail()
      }
      const radioChange = (e) => {
        if (e.target.value === 3) {
          state.taskInfo.areaType = undefined
        }
      }
      const radioClick = (e, type) => {
        console.log(state.taskInfo[type])
        console.log(e.target.value)
        if (state.taskInfo[type] === Number(e.target.value)) {
          state.taskInfo[type] = undefined
        }
      }
      const getLogList = () => {
        const params = {
          businessId: route.params.id,
          businessType: 2
        }
        salesTaskApi.getLogList(params).then(res => {
          if (res.code === 0) {
            state.logList = res.data.map(item => {
              return {
                comment: item.content,
                userName: item.operateUser,
                createTime: item.operateTime
              }
            })
          }
        })
      }
      getLogList()
      return {
        ...toRefs(state),
        saveProjectConfig,
        getTaskTransferVal,
        radioClick,
        radioChange
      }
    }
  }
</script>

<style lang="scss" scoped>
.task-com{
  background: #FFFFFF;
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  padding: 0 15px 10px 10px;
  .labelWidth {
    width: 120px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}
</style>
