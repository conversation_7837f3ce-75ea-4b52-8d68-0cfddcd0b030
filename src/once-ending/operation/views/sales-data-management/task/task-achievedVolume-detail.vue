<template>
  <div class="task-com">
    <div class="mt-20 mb-20 font-18 bold">{{ pageTitle }}达成量统计明细</div>
    <ni-list-page>
      <ni-table
        class="mt-20"
        :columns="$route.query.taskFlag === 'sale' ? saleColumns : $route.query.taskFlag === 'recycle' ? recycleColumns : repairColumns"
        :dataSource="dataSource"
        :pagination="pagination"
        @change="handleTableChange"
      ></ni-table>
    </ni-list-page>
  </div>
</template>

<script lang="jsx">
  import { reactive, toRefs, getCurrentInstance } from 'vue'
  import { useRoute } from 'vue-router/composables'
  import { NiListPage, NiTable } from '@jiuji/nine-ui'
  import salesTaskApi from '../../../api/sales-task-management'
  import RenderSubIdre from '../components/renderSubId'
  export default {
    components: {
      NiListPage,
      NiTable
    },
    setup () {
      const route = useRoute()
      const root = getCurrentInstance().proxy
      const { $message, $indicator } = root
      const state = reactive({
        pageTitle: route.query.taskFlag === 'sale' ? '销售' : route.query.taskFlag === 'recycle' ? '回收' : '维修',
        saleColumns: [
          {
            title: '地区',
            dataIndex: 'area',
            width: 100,
          },
          {
            title: '订单号',
            dataIndex: 'subId',
            width: 100,
            customRender: (text, record) => <RenderSubIdre subKind={route.query.subKind} subId={text} />
          },
          {
            title: '商品名称',
            dataIndex: 'productName'
          },
          {
            title: 'PPID',
            dataIndex: 'ppid',
            width: 100,
          },
          {
            title: '数量',
            dataIndex: 'basketCount',
            width: 100,
          },
          {
            title: '商品价格',
            dataIndex: 'price',
            width: 100,
          },
          {
            title: '实收价格',
            dataIndex: 'pricePaid',
            width: 100,
          },
          {
            title: '利润额',
            dataIndex: 'profit',
            width: 100,
          },
          {
            title: '销售人员',
            dataIndex: 'seller',
            width: 100,
          },
          {
            title: '订单状态',
            dataIndex: 'subCheck',
            width: 100,
          },
          {
            title: '出库时间',
            dataIndex: 'tradeDate',
            width: 140,
          },
          {
            title: '交易时间',
            dataIndex: 'tradeDate1',
            width: 140,
          },
        ],
        recycleColumns: [
          {
            title: '地区',
            dataIndex: 'area',
            width: 100,
          },
          {
            title: '回收单号',
            dataIndex: 'subId',
            width: 100,
            customRender: (text, record) => <RenderSubIdre subKind={route.query.subKind} subId={text} />
          },
          {
            title: '商品名称',
            dataIndex: 'productName',
          },
          {
            title: '规格',
            dataIndex: 'productColor',
            width: 100,
          },
          {
            title: 'imei',
            dataIndex: 'imei',
            width: 100,
          },
          {
            title: '单价',
            dataIndex: 'price',
            width: 100,
          },
          {
            title: '销售人',
            dataIndex: 'inUser',
            width: 100,
          },
          {
            title: '检测人',
            dataIndex: 'checkUser',
            width: 100,
          },
          {
            title: '订单状态',
            dataIndex: 'subCheck',
            width: 100,
          },
          {
            title: '加单时间',
            dataIndex: 'addTime',
            width: 140,
          },
          {
            title: '交易时间',
            dataIndex: 'payTime',
            width: 140,
          },
        ],
        repairColumns: [
          {
            title: '地区',
            dataIndex: 'area',
            width: 100,
          },
          {
            title: '维修单号',
            dataIndex: 'Id',
            width: 100,
            customRender: (text, record) => <RenderSubIdre subKind={route.query.subKind} subId={text} />
          },
          {
            title: '接件类型',
            dataIndex: 'isSoft',
            width: 100,
          },
          {
            title: '机型',
            dataIndex: 'productName',
          },
          {
            title: '会员',
            dataIndex: 'userId',
            width: 100,
          },
          {
            title: '应收',
            dataIndex: 'pricePayable',
            width: 100,
          },
          {
            title: '已收',
            dataIndex: 'pricePaid',
            width: 100,
          },
          {
            title: '维修毛利',
            dataIndex: 'profit',
            width: 100,
          },
          {
            title: '维修状态',
            dataIndex: 'status',
            width: 100,
          },
          {
            title: '是否取机',
            dataIndex: 'isTaken',
            width: 100,
          },
          {
            title: '接件时间',
            dataIndex: 'modidate',
            width: 140,
          },
          {
            title: '取机时间',
            dataIndex: 'offTime',
            width: 140,
          },
        ],
        dataSource: [],
        pagination: {
          current: 1,
          pageSize: 10,
          total: 0,
          pageSizeOptions: ['10', '20', '50', '100', '200'],
          showSizeChanger: true,
          showTotal: total => `总共 ${total} 条`
        },
      })
      const getSalesTaskProjectTargetDetail = () => {
        $indicator.open()
        let params = {
          subKind: route.query.subKind,
          projectId: route.params.id,
          configType: 2,
          dimensionId: route.query.dimensionId,
          dimensionType: route.query.dimensionType,
          taskConfigId: route.query.taskConfigId,
          current: state.pagination.current,
          size: state.pagination.pageSize
        }
        salesTaskApi.getSalesTaskProjectTargetDetail(params).then(res => {
          if (res.code === 0) {
            state.dataSource = res.data?.records
            state.pagination.total = res.data?.total
          } else {
            $message.error(res.userMsg)
          }
        }).finally(() => {
          $indicator.close()
        })
      }
      getSalesTaskProjectTargetDetail()
      const handleTableChange = (pagination) => {
        state.pagination.current = pagination.current
        state.pagination.pageSize = pagination.pageSize
        getSalesTaskProjectTargetDetail()
      }
      return {
        ...toRefs(state),
        handleTableChange
      }
    }
  }
</script>

<style lang="scss" scoped>
.task-com{
  padding: 0 15px 0 10px;
}
</style>
