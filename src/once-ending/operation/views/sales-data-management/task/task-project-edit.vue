<template>
  <div>
    <component :is="getComponent(taskFlag)" :taskFlagName="taskFlagName"></component>
  </div>
</template>

<script>
  import { reactive, toRefs } from 'vue'
  import { useRoute } from 'vue-router/composables'
  import TaskSale from '../components/task-project/task-sale.vue'
  import TaskRecycle from '../components/task-project/task-recycle.vue'
  import TaskRepair from '../components/task-project/task-repair.vue'
  import TaskOperator from '../components/task-project/task-operator.vue'
  import { tabList } from '../constants'
  export default {
    components: {
      TaskSale,
      TaskRecycle,
      TaskRepair,
      TaskOperator,
    },
    setup () {
      const route = useRoute()
      const index = route.path.lastIndexOf('/')
      const newPath = route.path.substring(0, index)
      const state = reactive({
        taskFlag: route.query.taskFlag,
        taskFlagName: ''
      })
      const tabsMap = new Map([
        ['sale', TaskSale],
        ['recycle', TaskRecycle],
        ['repair', TaskRepair],
        ['operator', TaskOperator]
      ])
      sessionStorage.setItem('selectedKeys', JSON.stringify([newPath]))
      const getTabListName = () => {
        state.taskFlagName = tabList.filter(item => item.value === route.query.taskFlag)[0].label
      }
      getTabListName()
      const getComponent = (value) => {
        return tabsMap.get(value)
      }
      return {
        ...toRefs(state),
        tabsMap,
        getComponent
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>
