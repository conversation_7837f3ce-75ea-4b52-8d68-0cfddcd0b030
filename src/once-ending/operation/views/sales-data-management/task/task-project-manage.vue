<template>
  <div class="report-wrapper">
    <div class="mt-20 mb-20 font-18 bold">{{ metaTitle }}</div>
    <ni-list-page class="mt-20" :pushFilterToLocation="false">
      <ni-filter :immediate="false" :form="searchWrapper" :loading="loading" @filter="fetchData" :labelWidth="90">
        <ni-filter-item label="任务组名称">
          <a-select style="width: 240px" placeholder="请选择" show-search v-model="searchWrapper.groupId" @change="groupChange" :filter-option="filterOption" allowClear>
            <a-select-option v-for="item in allConfigLists" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
          </a-select>
        </ni-filter-item>
        <ni-filter-item label="状态">
          <a-select placeholder="请选择" v-model="searchWrapper.status" allowClear>
            <a-select-option value="1">启用</a-select-option>
            <a-select-option value="0">停用</a-select-option>
          </a-select>
        </ni-filter-item>
      </ni-filter>
      <ni-table
        class="mt-20"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="pagination"
        @change="handleTableChange"
      >
        <div slot="groupIdSlot" slot-scope="text, record">
          <router-link :to="{
            path: `/operation/sales-data/task-configuration/task-project-manage/${record ? record.groupId : 0}/task-project-detail`,
            query: {
              viewOnly: 1,
              readOnly: 1
            }
          }">{{ text }}</router-link>
        </div>
        <div slot="areaKindSlot" slot-scope="text, record">
          <span v-if="text">{{ text }}</span>
          <span v-else>-</span>
        </div>
        <div slot="areaTypeSlot" slot-scope="text, record">
          <span v-if="text">{{ text }}</span>
          <span v-else>-</span>
        </div>
        <div slot="relationshipProjectSlot" slot-scope="text, record">
          <div v-if="text.length > 50">
            <a-popover overlayClassName="poperLayTable">
              <template slot="content">
                <div>{{ text }}</div>
              </template>
              <span class="pointer">{{ text.substring(0, 50) + '...' }}</span>
            </a-popover>
          </div>
          <span v-else>{{ text }}</span>
        </div>
        <div slot="controllerSlot" slot-scope="text, record">
          <span class="pointer blue mr-16" @click="goTaskManageEdit(record, 'add')">编辑</span>
          <span class="pointer blue mr-16" @click="updateGroupStatus(record)">{{ record.status === '启用' ? '停用' : '启用'}}</span>
<!--          <span class="pointer blue" @click="viewLog(record)">查看日志</span>-->
        </div>
        <div slot="action">
          <a-button type="primary" @click="goTaskManageEdit('', 'add')">添加任务组</a-button>
        </div>
      </ni-table>
    </ni-list-page>
  </div>
</template>

<script>
  import { reactive, toRefs, getCurrentInstance, onActivated } from 'vue'
  import { useRoute, useRouter } from 'vue-router/composables'
  import { NiListPage, NiFilter, NiFilterItem, NiTable } from '@jiuji/nine-ui'
  import salesTaskApi from '../../../api/sales-task-management'
  export default {
    components: {
      NiListPage,
      NiFilter,
      NiFilterItem,
      NiTable,
    },
    beforeRouteEnter (to, from, next) {
      if (from.name !== 'task-project-edit' && from.name !== 'task-project-detail') {
        to.meta.isBack = true
      }
      next()
    },
    setup () {
      const route = useRoute()
      const router = useRouter()
      const root = getCurrentInstance().proxy
      const { $indicator, $message } = root
      const state = reactive({
        metaTitle: route.meta.title,
        searchWrapper: {
          groupId: undefined,
          groupName: undefined,
          status: undefined,
        },
        loading: false,
        openLogModel: false,
        columns: [
          {
            title: '任务组id',
            dataIndex: 'groupId',
            scopedSlots: { customRender: 'groupIdSlot' },
          },
          {
            title: '任务组名称',
            dataIndex: 'groupName',
            width: '200px'
          },
          {
            title: '维度',
            width: '80px',
            dataIndex: 'dimension',
          },
          {
            title: '门店类型',
            width: '80px',
            dataIndex: 'areaKind',
            scopedSlots: { customRender: 'areaKindSlot' },
          },
          {
            title: '门店类别',
            width: '80px',
            dataIndex: 'areaType',
            scopedSlots: { customRender: 'areaTypeSlot' },
          },
          {
            title: '任务项',
            dataIndex: 'relationshipProject',
            width: '400px',
            scopedSlots: { customRender: 'relationshipProjectSlot' },
          },
          {
            title: '状态',
            dataIndex: 'status'
          },
          {
            title: '排序',
            dataIndex: 'rank'
          },
          {
            title: '操作',
            dataIndex: 'controller',
            width: '100px',
            scopedSlots: { customRender: 'controllerSlot' },
          }
        ],
        dataSource: [],
        pagination: {
          current: 1,
          pageSize: 10,
          total: 0,
          pageSizeOptions: ['10', '20', '50', '100', '200'],
          showSizeChanger: true,
          showTotal: total => `总共 ${total} 条`
        },
        allConfigLists: [],
      })
      const initData = () => {
        $indicator.open()
        let params = {
          groupName: state.searchWrapper.groupName,
          status: state.searchWrapper.status,
          configType: 2,
          current: state.pagination.current,
          size: state.pagination.pageSize
        }
        salesTaskApi.groupConfigPage(params).then(res => {
          if (res.code === 0) {
            state.dataSource = res.data.records
            state.pagination.total = res.data.total
          } else {
            $message.error(res.userMsg)
          }
        }).finally(() => {
          $indicator.close()
        })
      }
      const fetchData = () => {
        state.pagination.current = 1
        state.pagination.pageSize = 10
        initData()
      }
      // fetchData()
      const handleTableChange = (pagination) => {
        state.pagination.current = pagination.current
        state.pagination.pageSize = pagination.pageSize
        initData()
      }
      const goTaskManageEdit = (record, type) => {
        router.push({
          path: `/operation/sales-data/task-configuration/task-project-manage/${record ? record.groupId : 0}`,
          query: {
            viewOnly: type === 'view' ? 1 : 2
          }
        })
      }
      const updateGroupStatus = (record) => {
        const params = {
          groupId: record.groupId,
          status: record.status === '启用' ? 0 : 1
        }
        $indicator.open()
        salesTaskApi.updateGroupStatus(params).then(res => {
          if (res.code === 0) {
            $message.success(res.userMsg)
            initData()
          } else {
            $message.warn(res.userMsg)
          }
        }).finally(() => {
          $indicator.close()
        })
      }
      const allConfigList = () => {
        let params = {
          // 查询分类 1、报表 2、任务
          configType: 2,
          // 查询类型 1、任务项 2、任务组（这里的报表配置获取的是报表统计项的配置）
          relationType: 2
        }
        salesTaskApi.allConfigList(params).then(res => {
          if (res.code === 0) {
            state.allConfigLists = res.data.map(item => {
              return {
                label: item.configName,
                value: item.configId
              }
            })
          } else {
            $message.success(res.userMsg)
          }
        })
      }
      // allConfigList()
      const groupChange = (value) => {
        if (value) {
          state.searchWrapper.groupName = state.allConfigLists.filter(item => item.value === value)[0].label
        } else {
          state.searchWrapper.groupName = undefined
        }
      }
      const filterOption = (input, option) => {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      }
      // const firstLoad = ref(false)
      onActivated(() => {
        if (route.meta.isBack) {
          // 重置ifDoFresh
          route.meta.isBack = false
          state.searchWrapper.groupId = undefined
          state.searchWrapper.groupName = undefined
          state.searchWrapper.status = undefined
          // firstLoad.value = true
        }
        // if (!firstLoad.value) {
        //   fetchData()
        //   firstLoad.value = true
        // }
        fetchData()
        allConfigList()
      })
      return {
        ...toRefs(state),
        fetchData,
        handleTableChange,
        goTaskManageEdit,
        updateGroupStatus,
        groupChange,
        filterOption,
      }
    }
  }
</script>

<style lang="scss" scoped>
.report-wrapper{}
</style>
