<template>
  <div class="task-wrapper">
    <div class="flex flex-justify-between mt-20 mb-20 ">
      <div class="font-18 bold">{{ metaTitle }}</div>
    </div>
    <ni-list-page class="mt-20" :pushFilterToLocation="false">
      <ni-filter :immediate="false" :form="searchWrapper" :loading="loading" @filter="fetchData" :labelWidth="90">
        <ni-filter-item label="任务组名称">
          <a-select style="width: 240px" placeholder="请选择" show-search v-model="searchWrapper.groupId" @change="groupChange" :filter-option="filterOption" allowClear>
            <a-select-option v-for="item in allConfigLists" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
          </a-select>
        </ni-filter-item>
        <ni-filter-item label="任务周期">
          <a-select placeholder="请选择" v-model="searchWrapper.taskConfigCycle" @change="taskConfigCycleChange" allowClear>
            <a-select-option v-for="item in taskConfigCycle" :key="item.value" :value="item.value+''">{{ item.label }}</a-select-option>
          </a-select>
        </ni-filter-item>
        <ni-filter-item label="时间" v-if="searchWrapper.taskConfigCycle">
          <a-select placeholder="请选择" style="width: 220px" v-model="searchWrapper.listTime" allowClear>
            <a-select-option v-for="item in timeOptions" :value="item" :key="item">{{ item }}</a-select-option>
          </a-select>
        </ni-filter-item>
      </ni-filter>
      <ni-table
        class="mt-20"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="pagination"
        @change="handleTableChange"
      >
        <div slot="taskConfigNameSlot" slot-scope="text, record">
          <router-link :to="{
            path: `/operation/sales-data/task-configuration/task-configuration-manage/${record ? record.taskConfigId : 0}/task-configuration-detail`,
            query: {
              dimensionType: record ? record.dimension : undefined,
              editFlag: record ? record.editFlag : undefined,
              viewOnly: 1,
              readOnly: 1,
            }
          }">{{ text }}</router-link>
        </div>
        <div slot="action">
          <a-button type="primary" @click="goTaskProjectEdit()" v-if="userInfo.Rank.includes('qqrw') || userInfo.Rank.includes('dqrw') || userInfo.Rank.includes('xqrw') || userInfo.Rank.includes('mdrw')">添加任务</a-button>
        </div>
        <div slot="controllerSlot" slot-scope="text, record" class="flex flex-justify-between">
          <a-button type="link" @click="goTaskProjectEdit(record, 'edit')" :disabled="!(userInfo.Rank.includes('qqrw') || userInfo.Rank.includes('dqrw') || userInfo.Rank.includes('xqrw') || userInfo.Rank.includes('mdrw'))">编辑</a-button>
          <a-button type="link" @click="gotoAchievements(record)">达成量查看</a-button>
          <a-button v-if="!record.editFlag" type="link" disabled>删除</a-button>
          <a-popconfirm
            v-else
            title="确定删除?"
            @confirm="() => deleteItem(record)"
          >
            <a-button type="link">删除</a-button>
          </a-popconfirm>
          <a-popconfirm
            title="确定复制?"
            @confirm="() => copyItem(record)"
          >
            <a-button type="link" class="mr-20">复制</a-button>
          </a-popconfirm>
        </div>
      </ni-table>
    </ni-list-page>
  </div>
</template>

<script>
  import { reactive, toRefs, getCurrentInstance, onActivated, computed } from 'vue'
  import { useRoute, useRouter } from 'vue-router/composables'
  import { NiListPage, NiFilter, NiFilterItem, NiTable } from '@jiuji/nine-ui'
  import moment from 'moment'
  import salesTaskApi from '../../../api/sales-task-management'
  export default {
    components: {
      NiListPage,
      NiFilter,
      NiFilterItem,
      NiTable,
    },
    beforeRouteEnter (to, from, next) {
      // if (from.path.includes('task-configuration-detail')) {
      //   to.meta.isBack = true
      // } else {
      //   to.meta.isBack = false
      // }
      // next()
      if (from.name !== 'task-configuration-edit' && from.name !== 'task-configuration-detail') {
        to.meta.isBack = true
      }
      next()
    },
    setup () {
      const route = useRoute()
      const router = useRouter()
      const root = getCurrentInstance().proxy
      const { $message, $indicator, $store } = root
      const state = reactive({
        metaTitle: route.meta.title,
        searchWrapper: {
          groupId: undefined,
          groupName: undefined,
          taskConfigCycle: undefined,
          listTime: undefined
        },
        taskConfigCycle: undefined,
        timeOptions: [],
        loading: false,
        columns: [
          {
            title: '任务组名称',
            dataIndex: 'groupName'
          },
          {
            title: '任务名称',
            dataIndex: 'taskConfigName',
            scopedSlots: { customRender: 'taskConfigNameSlot' },
          },
          {
            title: '任务周期',
            dataIndex: 'taskConfigCycleStr'
          },
          {
            title: '开始时间',
            dataIndex: 'startTime'
          },
          {
            title: '结束时间',
            dataIndex: 'endTime'
          },
          {
            title: '操作',
            dataIndex: 'controller',
            width: '180px',
            scopedSlots: { customRender: 'controllerSlot' },
          }
        ],
        dataSource: [],
        pagination: {
          current: 1,
          pageSize: 10,
          total: 0,
          pageSizeOptions: ['10', '20', '50', '100', '200'],
          showSizeChanger: true,
          showTotal: total => `总共 ${total} 条`
        },
        openLogModel: false,
        allConfigLists: [],
      })
      const userInfo = computed(() => {
        return $store.state.userInfo
      })
      const initData = () => {
        $indicator.open()
        const listTimeArr = state.searchWrapper.listTime ? state.searchWrapper.listTime.split('~') : []
        const startTime = listTimeArr[0]
        const endTime = listTimeArr[1]
        const params = {
          groupName: state.searchWrapper.groupName,
          taskConfigCycle: state.searchWrapper.taskConfigCycle,
          startTime: startTime,
          endTime: endTime,
          sourceFlag: 1,
          current: state.pagination.current,
          size: state.pagination.pageSize
        }
        salesTaskApi.getConfigPage(params).then(res => {
          if (res.code === 0) {
            state.dataSource = res.data.records || []
            state.pagination.total = res.data.total
          }
        }).finally(() => {
          $indicator.close()
        })
      }
      const fetchData = () => {
        state.pagination.current = 1
        state.pagination.pageSize = 10
        initData()
      }
      // fetchData()
      const handleTableChange = (pagination) => {
        state.pagination.current = pagination.current
        state.pagination.pageSize = pagination.pageSize
        initData()
      }
      const taskConfigCycleChange = () => {
        state.searchWrapper.listTime = undefined
        if (state.searchWrapper.taskConfigCycle) {}
        let params = {
          taskCycle: state.searchWrapper.taskConfigCycle,
          groupId: state.searchWrapper.groupId
        }
        getTaskConfigEnum(params)
      }
      const getLogList = (record) => {
        const params = {
          businessId: record.taskConfigId,
          businessType: 3
        }
        salesTaskApi.getLogList(params).then(res => {
          if (res.code === 0) {
            state.logList = res.data.map(item => {
              return {
                comment: item.content,
                userName: item.operateUser,
                createTime: item.operateTime
              }
            })
          } else {
            $message.success(res.userMsg)
          }
        })
      }
      const deleteItem = (record) => {
        const params = {
          taskConfigId: record.taskConfigId
        }
        salesTaskApi.deleteConfig(params).then(res => {
          if (res.code === 0) {
            $message.success('删除成功')
            initData()
          } else {
            $message.success(res.userMsg)
          }
        })
      }
      const copyItem = (record) => {
        const params = {
          taskConfigId: record.taskConfigId
        }
        salesTaskApi.copyConfig(params).then(res => {
          if (res.code === 0) {
            $message.success(res.userMsg)
            initData()
          } else {
            $message.success(res.userMsg)
          }
        })
      }
      const goTaskProjectEdit = (record, type) => {
        router.push({
          path: `/operation/sales-data/task-configuration/task-configuration-manage/${record ? record.taskConfigId : 0}`,
          query: {
            dimensionType: record ? record.dimension : undefined,
            editFlag: record ? record.editFlag : undefined,
            configType: type === 'edit' ? 2 : undefined,
            viewOnly: type === 'view' ? 1 : 2
          }
        })
      }
      const gotoAchievements = (record) => {
        if (record.taskConfigId) {
          // sessionStorage.setItem('selectedKeys', JSON.stringify(['/operation/sales-data/task-configuration/task-achieve-inquire']))
          router.push({
            path: `/operation/sales-data/task-configuration/task-achieve-inquire/${record.taskConfigId}`,
            query: {
              dimension: record.dimension,
              configType: 2
            }
          })
        }
      }
      const getTaskConfigEnum = (params) => {
        salesTaskApi.getTaskConfigEnum(params).then(res => {
          if (res.code === 0) {
            state.taskConfigCycle = res.data.filter(item => item.key === 'taskConfigCycle')[0]?.itemList
            state.timeOptions = res.data.filter(item => item.key === 'time')[0]?.timeItemList.map(item => `${item.startTime}~${item.endTime}`)
          }
        })
      }
      getTaskConfigEnum('')
      const allConfigList = () => {
        let params = {
          // 查询分类 1、报表 2、任务
          configType: 2,
          // 查询类型 1、任务项 2、任务组（这里的报表配置获取的是报表统计项的配置）
          relationType: 2
        }
        salesTaskApi.allConfigList(params).then(res => {
          if (res.code === 0) {
            state.allConfigLists = res.data.map(item => {
              return {
                label: item.configName,
                value: item.configId
              }
            })
          } else {
            $message.success(res.userMsg)
          }
        })
      }
      // allConfigList()
      const groupChange = (value) => {
        if (value) {
          state.searchWrapper.groupName = state.allConfigLists.filter(item => item.value === value)[0].label
        } else {
          state.searchWrapper.groupName = undefined
        }
        taskConfigCycleChange()
      }
      const filterOption = (input, option) => {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      }
      // const firstLoad = ref(false)
      onActivated(() => {
        // if (!route.meta.isBack) {
        //   state.searchWrapper.groupId = undefined
        //   state.searchWrapper.groupName = undefined
        //   state.searchWrapper.taskConfigCycle = undefined
        //   state.searchWrapper.listTime = undefined
        //   fetchData()
        // }
        // route.meta.isBack = false
        if (route.meta.isBack) {
          route.meta.isBack = false
          state.searchWrapper.groupId = undefined
          state.searchWrapper.groupName = undefined
          state.searchWrapper.taskConfigCycle = undefined
          state.searchWrapper.listTime = undefined
          // firstLoad.value = true
        }
        // if (!firstLoad.value) {
        //   fetchData()
        //   firstLoad.value = true
        // }
        fetchData()
        allConfigList()
      })
      return {
        moment,
        ...toRefs(state),
        fetchData,
        handleTableChange,
        deleteItem,
        goTaskProjectEdit,
        gotoAchievements,
        taskConfigCycleChange,
        copyItem,
        groupChange,
        filterOption,
        userInfo
      }
    }
  }
</script>

<style lang="scss" scoped>
.task-wrapper{
  padding: 0 15px 0 5px;
}
:deep(.tool-box .actions) {
  .ant-btn {
    display: none;
  }
}
</style>
