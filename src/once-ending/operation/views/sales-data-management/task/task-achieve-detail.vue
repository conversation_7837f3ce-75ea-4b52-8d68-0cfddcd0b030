<template>
  <div class="task-wrapper">
    <div class="mt-20 mb-20 flex flex-justify-between">
      <div class="font-18 bold">{{ metaTitle }}</div>
      <a-button type="primary" @click="exportHandle">导出</a-button>
    </div>
<!--    <a-table-->
<!--      bordered-->
<!--      class="mt-20"-->
<!--      :columns="columns"-->
<!--      :dataSource="dataSource"-->
<!--      :scroll="{ y: tableHeight() }"-->
<!--      :pagination="false"-->
<!--    >-->
<!--      <div slot="dimensionName" slot-scope="text, record">-->
<!--        <span class="blue pointer" @click="goTaskContent(record)">{{ text }}</span>-->
<!--      </div>-->
<!--      <template slot-scope="text, record" :slot="slotName(item, 'target')" v-for="item in columnsSlot">-->
<!--        <span>{{ text }}</span>-->
<!--      </template>-->
<!--      <template slot-scope="text, record" :slot="slotName(item, 'achievementValue')" v-for="item in columnsSlot">-->
<!--        <span class="blue pointer" v-if="text > 0" @click="goTaskAchievedVolume(record)">{{ text }}</span>-->
<!--        <span class="blue pointer" v-else>{{ text }}</span>-->
<!--      </template>-->
<!--      <template slot-scope="text, record" :slot="slotName(item, 'margin')" v-for="item in columnsSlot">-->
<!--        <span>{{ text }}</span>-->
<!--      </template>-->
<!--      <template slot-scope="text, record" :slot="slotName(item, 'achievementPercent')" v-for="item in columnsSlot">-->
<!--        <span>{{ text }}</span>-->
<!--      </template>-->
<!--      <template slot-scope="text, record" :slot="slotName(item, 'percent')" v-for="item in columnsSlot">-->
<!--        <span>{{ text }}</span>-->
<!--      </template>-->
<!--    </a-table>-->
    <div class="white-bg">
      <a-spin size="large" :delay="200" :spinning="loading">
        <canvas-table
          v-if="columns.length"
          :data="dataSource"
          :columns="columns"
          ref="canvasTable"
          :max-height="tableHeight()"
          v-bind="canvasTableAttrs"
        />
        <a-empty style="padding: 100px" v-else/>
      </a-spin>
    </div>
  </div>
</template>

<script type="text/jsx" lang="jsx">
  import { reactive, toRefs, getCurrentInstance } from 'vue'
  import { useRoute, useRouter } from 'vue-router/composables'
  import salesTaskApi from '../../../api/sales-task-management'
  import { exportFile } from '~/util/export'
  import CanvasTable from '~/components/kt-canvas-table'
  export default {
    components: {
      CanvasTable
    },
    setup () {
      const root = getCurrentInstance().proxy
      const { $message, $set, $store, $indicator } = root
      const route = useRoute()
      const router = useRouter()
      const index = route.path.lastIndexOf('/')
      const newPath = route.path.substring(0, index)
      const state = reactive({
        loading: false,
        metaTitle: route.meta.title,
        columns: [],
        columnsSlot: [],
        dataSource: [],
        targetInfoList: [],
        dimensionList: [],
        canvasTableAttrs: {
          border: true,
          stripe: true,
          rowHeight: 42,
          customStyle: {
            headerFontWeight: '500',
            borderColor: '#dfdfdf',
            headerBackground: '#f5f5f5',
            color: '#333',
            fontSize: 13,
            activeColBackground: '#F6FBFF',
            activeRowBackground: '#F6FBFF',
            stripeRowBackground: '#fafafa',
            summaryBackground: '#ffffff',
            background: '#fff',
          },
        },
        dataIndexArr: [],
      })
      sessionStorage.setItem('selectedKeys', JSON.stringify([newPath]))
      const goTaskAchievedVolume = (subKind, projectId, areaIds, linkStartTime, linkEndTime, row) => {
        console.log('subKind:' + subKind)
        console.log('projectId：' + projectId)
        console.log('areaIds：' + areaIds)
        console.log('linkStartTime：' + linkStartTime)
        console.log('linkEndTime：' + linkEndTime)
        console.log('dimensionId：' + row.dimensionId)
        console.log('dimensionType：' + row.selfDimension)
        console.log('taskConfigId：' + route.params.id)
        console.log(row)
        if ([1, 2, 3].includes(subKind)) {
          router.push({
            path: `/operation/sales-data/task-configuration/task-achieve-inquire/${projectId}/task-achievedVolume-detail`,
            query: {
              taskFlag: checkTaskFlag(subKind),
              subKind: subKind,
              projectId: projectId,
              configType: 2,
              dimensionId: row.dimensionId,
              dimensionType: row.selfDimension,
              taskConfigId: route.params.id
            }
          })
        } else if (([4].includes(subKind))) {
          // https://oa.dev.9ji.com/staticpc/#/statistics-table/OperatorStatisticDetail?timeKind=1&startTime=2023-01-01 00:00:00&endTime=2023-12-01 00:00:00&copySearch={}&areaIds=2&isTotal=false
          window.open(`${window.tenant.oaHost}/staticpc/#/statistics-table/OperatorStatisticDetail?timeKind=1&startTime=${linkStartTime}&endTime=${linkEndTime}&copySearch={}&areaIds=${areaIds}&isTotal=false`)
        } else {
          return false
        }
      }
      const checkTaskFlag = (kind) => {
        if (kind === 1) {
          return 'sale'
        } else if (kind === 2) {
          return 'recycle'
        } else if (kind === 3) {
          return 'repair'
        } else {
          return ''
        }
      }
      const tableHeight = () => {
        return document.body.clientHeight - 140
      }
      const goTaskContent = (record, type) => {
        let dimension = ''
        let regionId = ''
        let dimensionId
        if (type === 'upperDepart') {
          if (record.lowerDepartDimensionId || record.areaDimensionId || record.staffDimensionId) {
            dimension = record.upperDepartDimension
            dimensionId = record.upperDepartDimensionId
          } else {
            dimension = record.dimension
            regionId = record.dimensionId
          }
        } else if (type === 'lowerDepart') {
          if (record.areaDimensionId || record.staffDimensionId) {
            dimension = record.lowerDepartDimension
            regionId = record.upperDepartDimensionId
          } else {
            dimension = record.dimension
            regionId = record.lowerDepartDimensionId
          }
        } else if (type === 'area') {
          if (record.staffDimensionId) {
            dimension = record.areaDimension
            regionId = record.areaDimensionId
          } else {
            dimension = record.dimension
            regionId = record.dimensionId
          }
        }
        router.push({
          path: `/operation/sales-data/task-configuration/task-achieve-inquire/${route.params.id}`,
          query: {
            // dimension: record.dimension,
            // configType: 2,
            // regionId: record.dimensionId,
            dimension,
            configType: 2,
            regionId,
            dimensionId
          }
        })
      }
      const getSalesTaskTargetDetail = () => {
        state.columns = []
        state.dataSource = []
        state.columnsSlot = []
        let newDimensionList = []
        const params = {
          // 任务配置id
          taskConfigId: route.params.id,
          // 维度
          dimensionType: route.query.dimension,
          // 维度id
          regionId: route.query.regionId,
          // 类型 1、报表 2、任务
          configType: 2,
          dimensionId: route.query.dimensionId
        }
        state.loading = true
        salesTaskApi.getSalesTaskTargetDetail(params).then(res => {
          if (res.code === 0) {
            state.targetInfoList = res.data.targetHeaderList || []
            state.targetInfoList.forEach(item => {
              let newList = {
                label: '',
                key: '',
                align: 'center',
                children: []
              }
              newList.label = item.projectName
              newList.key = item.projectId

              if (item.children && item.children.length > 0) {
                state.dataIndexArr = item.children.map(item => item.key)
                item.children.forEach(children => {
                  if (children.key.includes('achievementValue')) {
                    newList.children.push({
                      label: children.title,
                      key: item.projectId + children.key,
                      minWidth: 120,
                      align: 'center',
                      // scopedSlots: { customRender: item.projectId + children.key }
                      customRender: (h, { row, col }) => {
                        return row[item.projectId + '-subKind'] === 0
                          ? <div class="full-width text-center">{row[item.projectId + children.key]}</div>
                          : ([1, 2, 3].includes(row[item.projectId + '-subKind'])
                            ? <div class="full-width text-center">
                              <router-link
                                to={
                                  {
                                    path: `/operation/sales-data/task-configuration/task-achieve-inquire/${item.projectId}/task-achievedVolume-detail`,
                                    query: {
                                      taskFlag: checkTaskFlag(row[item.projectId + '-subKind']),
                                      subKind: row[item.projectId + '-subKind'],
                                      projectId: item.projectId,
                                      configType: 2,
                                      dimensionId: row.dimensionId,
                                      dimensionType: row.selfDimension,
                                      taskConfigId: route.params.id
                                    }
                                  }
                                }
                                target='_blank'>{row[item.projectId + children.key]}</router-link>
                              </div>
                            : <div class="full-width text-center"><router-link
                                to={
                                  {
                                    path: '/statistics-table/OperatorStatisticDetail',
                                    query: {
                                      timeKind: 7,
                                      startTime: row[item.projectId + '-linkStartTime'],
                                      endTime: row[item.projectId + '-linkEndTime'],
                                      copySearch: JSON.stringify({ startTime: row[item.projectId + '-linkStartTime'], endTime: row[item.projectId + '-linkEndTime'] }),
                                      areaIds: row[item.projectId + '-areaIds'],
                                      isTotal: false
                                    }
                                  }
                                }
                                target='_blank'>{row[item.projectId + children.key]}</router-link></div>
                          )
                      },
                    })
                  } else {
                    newList.children.push({
                      label: children.title,
                      key: item.projectId + children.key,
                      minWidth: 120,
                      align: 'center',
                    })
                  }
                })
              }
              state.columnsSlot.push(item.projectId)
              state.columns.push(newList)
            })
            const oneColumns = {
              label: '区域',
              align: 'center',
              key: 'regionSlot',
              // scopedSlots: { customRender: 'dimensionName' },
              customRender: (h, { row, col }) => {
                return (
                  <div class="full-width">
                    {
                      row.dimensionName === '全部'
                        ? <span class="grey-9">{ row.dimensionName }</span>
                        : <div>
                          { row.upperDepart ? <span class="blue pointer" onClick={() => goTaskContent(row, 'upperDepart')}>{ row.upperDepart }</span> : '' }
                          { row.lowerDepart ? <span class="blue pointer" onClick={() => goTaskContent(row, 'lowerDepart')}> { row.upperDepart && row.lowerDepart ? ' / ' : '' } { row.lowerDepart }</span> : ''}
                          { row.area ? <span class="blue pointer" onClick={() => goTaskContent(row, 'area')}> { row.upperDepart || row.lowerDepart ? ' / ' : '' } { row.area }</span> : ''}
                          { row.staff ? <span class="black"> { row.upperDepart || row.lowerDepart || row.area ? ' / ' : '' } { row.staff }</span> : ''}
                        </div>
                    }
                  </div>
                )
              },
              width: 200,
              fixed: 'left'
            }
            if (state.targetInfoList.length) {
              state.columns.unshift(oneColumns)
            }
            // 处理表格数据
            state.dimensionList = res.data.targetDataList || []
            if (state.dimensionList.length > 0) {
              state.dimensionList.forEach(item => {
                let newDimensionListObj = {}
                newDimensionListObj.dimension = item.dimension
                newDimensionListObj.dimensionId = item.dimensionId
                newDimensionListObj.dimensionName = item.dimensionName
                newDimensionListObj.upperDepart = item.upperDepart
                newDimensionListObj.upperDepartDimension = item.upperDepartDimension
                newDimensionListObj.upperDepartDimensionId = item.upperDepartDimensionId
                newDimensionListObj.lowerDepart = item.lowerDepart
                newDimensionListObj.lowerDepartDimension = item.lowerDepartDimension
                newDimensionListObj.lowerDepartDimensionId = item.lowerDepartDimensionId
                newDimensionListObj.area = item.area
                newDimensionListObj.areaDimension = item.areaDimension
                newDimensionListObj.areaDimensionId = item.areaDimensionId
                newDimensionListObj.staff = item.staff
                newDimensionListObj.staffDimension = item.staffDimension
                newDimensionListObj.staffDimensionId = item.staffDimensionId
                newDimensionListObj.selfDimension = item.selfDimension
                if (item.targetDetailList && item.targetDetailList.length > 0) {
                  item.targetDetailList.forEach(child => {
                    newDimensionListObj[`${child.projectId}-subKind`] = child.subKind
                    newDimensionListObj[`${child.projectId}-areaIds`] = child.areaIds && child.areaIds.length ? child.areaIds.join(',') : ''
                    newDimensionListObj[`${child.projectId}-linkEndTime`] = child.linkEndTime
                    newDimensionListObj[`${child.projectId}-linkStartTime`] = child.linkStartTime
                    newDimensionListObj[`${child.projectId}target`] = child.target + child.unit
                    newDimensionListObj[`${child.projectId}achievementValue`] = child.achievementValue + child.unit
                    newDimensionListObj[`${child.projectId}margin`] = child.margin + child.unit
                    newDimensionListObj[`${child.projectId}achievementPercent`] = child.achievementPercent
                    newDimensionListObj[`${child.projectId}percent`] = child.percent
                  })
                } else {
                  state.columnsSlot.map(child => {
                    // state.dataIndexArr.forEach(item => {
                    //   newDimensionListObj[`${child}${item}`] = 0
                    // })
                    newDimensionListObj[`${child.projectId}-subKind`] = child.subKind
                    newDimensionListObj[`${child.projectId}-areaIds`] = child.areaIds && child.areaIds.length ? child.areaIds.join(',') : ''
                    newDimensionListObj[`${child.projectId}-linkEndTime`] = child.linkEndTime
                    newDimensionListObj[`${child.projectId}-linkStartTime`] = child.linkStartTime
                    newDimensionListObj[`${child.projectId}target`] = child.target + child.unit
                    newDimensionListObj[`${child.projectId}achievementValue`] = child.achievementValue + child.unit
                    newDimensionListObj[`${child.projectId}margin`] = child.margin + child.unit
                    newDimensionListObj[`${child.projectId}achievementPercent`] = child.achievementPercent
                    newDimensionListObj[`${child.projectId}percent`] = child.percent
                  })
                }
                newDimensionList.push(newDimensionListObj)
              })
              $set(state, 'dataSource', newDimensionList.sort((a, b) => a.dimensionId - b.dimensionId))
              console.log(state.dataSource)
            }
            if (!state.dimensionList.length && Number(route.query.dimension) < 4) {
              router.replace({
                path: `/operation/sales-data/task-configuration/task-achieve-inquire/${route.params.id}`,
                query: {
                  dimension: Number(route.query.dimension) + 1,
                  configType: 2,
                  regionId: route.query.regionId,
                  dimensionId: route.query.dimensionId
                }
              })
            }
          } else {
            $message.error(res.userMsg)
          }
        }).finally(() => {
          state.loading = false
        })
      }
      getSalesTaskTargetDetail()
      const slotName = (val1, val2) => {
        return val1 + val2
      }
      const exportHandle = () => {
        $indicator.open()
        let params = {
          taskConfigId: route.params.id,
          dimensionType: route.query.dimension,
          // 维度id
          regionId: route.query.regionId,
          configType: route.query.configType
        }
        exportFile({
          url: '/cloudapi_nc/ncSegments/api/saleTask/task/exportSalesTaskTarget/v1?xservicename=oa-ncSegments',
          token: $store.state.token,
          method: 'post',
          contentType: 'json',
          params
        }).then((res) => {
          if (!res) return
          $message.error(res?.userMsg || res?.msg || '导出文件失败')
          if (res?.code === 1000) { // 无效token
            router.push('/login?redirect=' + window.location.href)
          }
        }).catch(e => {
          $message.error(`${e?.message || e}`)
        }).finally(() => {
          $indicator.close()
        })
      }
      return {
        ...toRefs(state),
        goTaskAchievedVolume,
        slotName,
        exportHandle,
        goTaskContent,
        tableHeight,
        checkTaskFlag
      }
    }
  }
</script>

<style lang="scss" scoped>
.task-wrapper{
  margin-right: 10px;
}
</style>
