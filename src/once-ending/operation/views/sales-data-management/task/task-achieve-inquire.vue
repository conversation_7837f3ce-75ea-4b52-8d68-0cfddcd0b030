<template>
  <div class="task-wrapper">
    <div class="mt-20 mb-20 font-18 bold">{{ metaTitle }}</div>
    <ni-list-page class="mt-20" :pushFilterToLocation="false">
      <ni-filter :immediate="false" :form="searchWrapper" :loading="loading" @filter="fetchData" :labelWidth="90" :itemMinWidth="400" :unfoldCount="5">
        <ni-filter-item label="任务组名称">
          <a-select style="width: 240px" placeholder="请选择" show-search v-model="searchWrapper.groupId" @change="groupChange" :filter-option="filterOption" allowClear>
            <a-select-option v-for="item in allConfigLists" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
          </a-select>
        </ni-filter-item>
        <ni-filter-item label="维度">
          <a-select v-model="searchWrapper.dimension" placeholder="请选择">
            <a-select-option v-for="item in dimensionOption" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
          </a-select>
        </ni-filter-item>
        <ni-filter-item label="任务周期">
          <a-select placeholder="请选择" v-model="searchWrapper.taskConfigCycle" @change="taskConfigCycleChange" allowClear>
            <a-select-option v-for="item in taskConfigCycle" :key="item.value" :value="item.value+''">{{ item.label }}</a-select-option>
          </a-select>
        </ni-filter-item>
        <ni-filter-item label="时间" v-if="searchWrapper.taskConfigCycle">
          <a-select placeholder="请选择" style="width: 200px" v-model="searchWrapper.listTime" allowClear>
            <a-select-option v-for="item in timeOptions" :value="item" :key="item">{{ item }}</a-select-option>
          </a-select>
        </ni-filter-item>
        <ni-filter-item label="状态">
          <a-select v-model="searchWrapper.status" allowClear>
            <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
          </a-select>
        </ni-filter-item>
        <ni-filter-item label="">
          <a-checkbox v-model="searchWrapper.collectFlag">汇总</a-checkbox>
        </ni-filter-item>
      </ni-filter>
      <ni-table
        class="mt-20"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="pagination"
        @change="handleTableChange"
      >
        <div slot="taskConfigNameSlot" slot-scope="text, record">
          <router-link :to="{
            path: `/operation/sales-data/task-configuration/task-achieve-inquire/${record ? record.taskConfigId : 0}`,
            query: {
              dimension: record.dimension,
              dimensionId: record.dimensionId || undefined,
              configType: 2
            }
          }">{{ text }}</router-link>
        </div>
        <div slot="controllerSlot" slot-scope="text, record">
          <span class="pointer blue" @click="gotoDetail(record)">查看</span>
        </div>
      </ni-table>
    </ni-list-page>
  </div>
</template>

<script>
  import { reactive, toRefs, computed, getCurrentInstance, watch, onActivated } from 'vue'
  import { useRoute, useRouter } from 'vue-router/composables'
  import { NiListPage, NiFilter, NiFilterItem, NiTable } from '@jiuji/nine-ui'
  import moment from 'moment'
  import salesTaskApi from '../../../api/sales-task-management'
  const COLUMNS = [
    {
      title: '任务组名称',
      dataIndex: 'groupName'
    },
    {
      title: '任务名称',
      dataIndex: 'taskConfigName',
      scopedSlots: { customRender: 'taskConfigNameSlot' },
    },
    {
      title: '地区/人员',
      dataIndex: 'dimensionInfo'
    },
    {
      title: '任务周期',
      dataIndex: 'taskConfigCycleStr'
    },
    {
      title: '开始时间',
      dataIndex: 'startTime'
    },
    {
      title: '结束时间',
      dataIndex: 'endTime'
    },
    {
      title: '操作',
      dataIndex: 'controller',
      width: '60px',
      scopedSlots: { customRender: 'controllerSlot' },
    }
  ]
  export default {
    components: {
      NiListPage,
      NiFilter,
      NiFilterItem,
      NiTable
    },
    beforeRouteEnter (to, from, next) {
      if (from.path.includes('task-achieve-inquire')) {
        to.meta.isBack = true
      } else {
        to.meta.isBack = false
      }
      next()
    },
    setup () {
      const root = getCurrentInstance().proxy
      const { $store, $message, $indicator, $nextTick, $set } = root
      const route = useRoute()
      const router = useRouter()
      const state = reactive({
        metaTitle: route.meta.title,
        searchWrapper: {
          groupId: undefined,
          groupName: undefined,
          dimension: undefined,
          taskConfigCycle: undefined,
          listTime: undefined,
          status: 1,
          collectFlag: false
        },
        allConfigLists: [],
        taskConfigCycle: [],
        statusOptions: [],
        dimensionOption: [],
        timeOptions: [],
        loading: false,
        columns: [...COLUMNS],
        dataSource: [],
        pagination: {
          current: 1,
          pageSize: 10,
          total: 0,
          pageSizeOptions: ['10', '20', '50', '100', '200'],
          showSizeChanger: true,
          showTotal: total => `总共 ${total} 条`
        },
      })
      const taskConfigCycleVal = computed(() => {
        return state.searchWrapper.taskConfigCycle
      })
      const dimensionDefault = () => {
        const userInfo = $store.state.userInfo
        if (userInfo.Rank.includes('qqrw') || userInfo.Rank.includes('dqrw')) {
          return 1
        } else if (userInfo.Rank.includes('xqrw')) {
          return 2
        } else if (userInfo.Rank.includes('mdrw')) {
          return 3
        } else {
          return 4
        }
      }
      state.searchWrapper.dimension = dimensionDefault()
      const initData = () => {
        $indicator.open()
        state.dataSource = []
        const { groupName, dimension, status, taskConfigCycle, collectFlag } = state.searchWrapper
        const { current, pageSize } = state.pagination
        const listTimeArr = state.searchWrapper.listTime ? state.searchWrapper.listTime.split('~') : []
        const startTime = listTimeArr[0]
        const endTime = listTimeArr[1]
        const params = {
          groupName,
          taskConfigCycle,
          dimension,
          status,
          startTime,
          endTime,
          collectFlag,
          sourceFlag: 2,
          selectFlag: true,
          current,
          size: pageSize
        }
        salesTaskApi.getConfigPage(params).then(res => {
          if (res.code === 0) {
            state.dataSource = res.data.records
            state.pagination.total = res.data.total
          } else {
            $message.error(res.userMsg)
          }
        }).finally(() => {
          $indicator.close()
        })
      }
      const fetchData = () => {
        state.pagination.current = 1
        state.pagination.pageSize = 10
        initData()
      }
      const handleTableChange = (pagination) => {
        state.pagination.current = pagination.current
        state.pagination.pageSize = pagination.pageSize
        initData()
      }
      const gotoDetail = (record) => {
        router.push({
          path: `/operation/sales-data/task-configuration/task-achieve-inquire/${record ? record.taskConfigId : 0}`,
          query: {
            dimension: record.dimension,
            dimensionId: record.dimensionId || undefined,
            configType: 2
          }
        })
      }
      const allConfigList = () => {
        let params = {
          // 查询分类 1、报表 2、任务
          configType: 2,
          // 查询类型 1、任务项 2、任务组（这里的报表配置获取的是报表统计项的配置）
          relationType: 2
        }
        salesTaskApi.allConfigList(params).then(res => {
          if (res.code === 0) {
            state.allConfigLists = res.data.map(item => {
              return {
                label: item.configName,
                value: item.configId
              }
            })
          } else {
            $message.error(res.userMsg)
          }
        })
      }
      allConfigList()
      const taskConfigCycleChange = () => {
        state.searchWrapper.listTime = undefined
        if (state.searchWrapper.taskConfigCycle) {}
        let params = {
          taskCycle: state.searchWrapper.taskConfigCycle,
          groupId: state.searchWrapper.groupId
        }
        getTaskConfigEnum(params)
      }
      const getTaskConfigEnum = (params) => {
        salesTaskApi.getTaskConfigEnum(params).then(res => {
          if (res.code === 0) {
            state.taskConfigCycle = filterOptions(res.data, 'taskConfigCycle')
            state.statusOptions = filterOptions(res.data, 'status')
            state.timeOptions = res.data.filter(item => item.key === 'time')[0]?.timeItemList.map(item => `${item.startTime}~${item.endTime}`)
          } else {
            $message.error(res.userMsg)
          }
        })
      }
      getTaskConfigEnum('')
      const getGroupConfigTypeEnum = () => {
        const params = {
          selectFlag: true
        }
        salesTaskApi.getGroupConfigTypeEnum(params).then(res => {
          if (res.code === 0) {
            // 任务组维度
            state.dimensionOption = filterOptions(res.data, 'dimension')
          } else {
            $message.error(res.userMsg)
          }
        })
      }
      const filterOptions = (data, type) => {
        return data.filter(item => item.key === type)[0]?.itemList
      }
      getGroupConfigTypeEnum()
      const groupChange = (value, option) => {
        if (value) {
          state.searchWrapper.groupName = state.allConfigLists.filter(item => item.value === value)[0].label
        } else {
          state.searchWrapper.groupName = undefined
        }
        taskConfigCycleChange()
      }
      const filterOption = (input, option) => {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      }
      watch(() => state.searchWrapper.dimension, (newVal, oldVal) => {
        if (newVal) {
          const newcolumns = COLUMNS.filter(item => item.dataIndex !== 'dimensionInfo')
          if (state.searchWrapper.collectFlag) {
            $nextTick(() => {
              $set(state, 'columns', newcolumns)
            })
          } else {
            state.columns = COLUMNS
          }
          state.columns = COLUMNS
        } else {
          state.columns = COLUMNS.filter(item => item.dataIndex !== 'dimensionInfo')
        }
        fetchData()
      })
      watch(() => state.searchWrapper.collectFlag, (newVal, oldVal) => {
        if (newVal) {
          state.columns = COLUMNS.filter(item => item.dataIndex !== 'dimensionInfo')
        } else {
          state.columns = COLUMNS
        }
        fetchData()
      })
      onActivated(() => {
        console.log(route.meta.isBack)
        if (!route.meta.isBack) {
          state.searchWrapper.groupId = undefined
          state.searchWrapper.groupName = undefined
          state.searchWrapper.dimension = dimensionDefault()
          state.searchWrapper.taskConfigCycle = undefined
          state.searchWrapper.listTime = undefined
          state.searchWrapper.status = 1
          state.searchWrapper.collectFlag = false
          fetchData()
        }
        route.meta.isBack = false
      })
      return {
        ...toRefs(state),
        taskConfigCycleVal,
        fetchData,
        handleTableChange,
        taskConfigCycleChange,
        moment,
        gotoDetail,
        groupChange,
        filterOption
      }
    }
  }
</script>

<style lang="scss" scoped>
.task-wrapper{
  padding: 0 15px 0 5px;
}
</style>
