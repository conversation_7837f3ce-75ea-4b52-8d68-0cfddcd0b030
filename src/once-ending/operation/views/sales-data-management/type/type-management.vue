<template>
  <div class="type-wrapper">
    <div class="mt-20 mb-20 font-18 bold">{{ metaTitle }}</div>
    <NiListPage>
      <ni-table
        :columns="columns"
        :dataSource="dataSource"
        @change="handleTableChange"
        :pagination="pagination"
      >
        <div slot="action">
          <a-button type="primary" @click="openModel()">添加</a-button>
        </div>
        <div slot="controllerSlot" style="width: 80px" slot-scope="text, record">
          <div class="flex flex-justify-center" v-if="record.isNotDel">
            <span class="blue">--</span>
          </div>
          <div class="flex flex-justify-between" v-else>
            <span class="blue pointer" @click="openModel(record)">编辑</span>
            <a-popconfirm
              title="确定删除?"
              @confirm="() => deleteItem(record)"
            >
              <span class="blue pointer">删除</span>
            </a-popconfirm>
          </div>
        </div>
      </ni-table>
    </NiListPage>
    <a-modal
      title="类型配置"
      :visible="isOpen"
      @ok="handleOk"
      @cancel="handleCancel"
      maskClosable
      :z-index="999"
    >
      <div class="flex flex-col">
        <div class="flex flex-align-center flex-child-noshrink">
          <span class="inline-block red mr-5 mt-5">*</span>
          <p class="font-14">类型名称：</p>
        </div>
        <a-input placeholder="请输入" v-model="typeName" :maxLength="10" allowClear>
          <a-tooltip slot="suffix">{{typeName ? typeName.length : 0}}/10</a-tooltip>
        </a-input>
      </div>
    </a-modal>
  </div>
</template>

<script>
  import { reactive, toRefs, getCurrentInstance } from 'vue'
  import { useRoute } from 'vue-router/composables'
  import { NiListPage, NiTable } from '@jiuji/nine-ui'
  import LogUserDetail from '~/pages/after-service/order/components/log-user-detail.vue'
  export default {
    components: {
      LogUserDetail,
      NiListPage,
      NiTable
    },
    setup () {
      const route = useRoute()
      const metaTitle = route.meta.title
      const root = getCurrentInstance().proxy
      const { $message } = root
      const state = reactive({
        columns: [
          {
            title: '类型名称',
            dataIndex: 'typeName',
            width: '45%'
          },
          {
            title: '添加时间',
            dataIndex: 'bbb',
            width: '45%'
          },
          {
            title: '操作',
            dataIndex: 'controller',
            scopedSlots: { customRender: 'controllerSlot' },
          }
        ],
        dataSource: [
          {
            typeName: '客流',
            bbb: '2023-9-11 19:45:49',
          },
          {
            typeName: '类型1',
            bbb: '2023-9-11 19:46:32',
            isNotDel: true
          },
          {
            typeName: '类型2',
            bbb: '2023-9-11 19:48:11',
          }
        ],
        pagination: {
          current: 1,
          pageSize: 10,
          total: 0,
          pageSizeOptions: ['10', '20', '50', '100', '200'],
          showSizeChanger: true,
          showTotal: total => `总共 ${total} 条`
        },
        isOpen: false,
        typeName: undefined
      })
      const handleTableChange = (pagination) => {
        state.pagination.current = pagination.current
        state.pagination.pageSize = pagination.pageSize
      }
      const deleteItem = (record) => {
        console.log(record)
      }
      const openModel = (record) => {
        state.isOpen = true
        state.typeName = record ? record.typeName : undefined
      }
      const handleOk = () => {
        if (!state.typeName) {
          return $message.warn('请填写类型名称')
        }
        handleCancel()
      }
      const handleCancel = () => {
        state.isOpen = false
        state.typeName = undefined
      }
      return {
        metaTitle,
        ...toRefs(state),
        handleTableChange,
        openModel,
        deleteItem,
        handleCancel,
        handleOk
      }
    }
  }
</script>

<style lang="scss" scoped>
.type-wrapper{
  padding: 0 20px;
}
</style>
