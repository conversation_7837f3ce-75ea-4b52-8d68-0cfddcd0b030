<script type="text/jsx" lang="jsx">
  import { defineComponent, ref, reactive, getCurrentInstance } from 'vue'
  import SearchBox from './components/search-box'
  import TableBox from './components/table-box'
  // import PaginationBox from './components/pagination-box'
  import { NiListPage } from '@jiuji/nine-ui'
  import { AREA_INFO_PAGE_LIST } from '@operation/store/modules/area-info/action-types'
  import { message } from 'ant-design-vue'
  import axios from 'axios'
  export default defineComponent({
    name: 'area-info-manage',
    components: {
      SearchBox,
      TableBox,
      // PaginationBox,
      NiListPage
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const loading = ref(false)
      const exportLoading = ref(false)
      const formData = reactive({
        areaCode: [],
        areaLevel: undefined,
        areaKind: undefined,
        printName: undefined,
        keyKind: 'areaName',
        mainKey: undefined,
        isPass: 1,
        isWeb: undefined,
        zones: undefined,
        authorizeidList: undefined,
        swKind: undefined,
        attributeList: undefined,
        category: undefined,
        areaTags: undefined
      })
      const tableList = ref([])
      const pagination = ref({
        current: 1,
        pageSize: 50,
        total: 0,
        pageSizeOptions: ['20', '50', '100', '200'],
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: total => `共计${total}条`
      })
      const fetchData = async function (current) {
        if (current) pagination.value.current = current
        const params = { ...formData, current: pagination.value.current, size: pagination.value.pageSize }
        loading.value = true
        const res = await proxy.$store.dispatch(`operation/areaInfo/${AREA_INFO_PAGE_LIST}`, params)
        loading.value = false
        if (res) {
          const { data, total } = res
          tableList.value = data.records
          pagination.value.total = data.total
        }
      }
      const handleTableChange = (paginations) => {
        pagination.value = paginations
        fetchData()
      }

      const add = function () {
        window.location.href = '/zitidian/areainfo_edit'
      }
      const exportFile = async function () {
        const params = { ...formData }
        exportLoading.value = true
        axios({
          method: 'post',
          url: '/cloudapi_nc/ncSegments/api/areaInfo/export?xservicename=oa-ncSegments',
          timeout: 900 * 1000,
          data: params,
          responseType: 'blob',
          headers: {
            Authorization: proxy.$store.state.token,
          }
        }).then((res) => {
          if (res.status >= 200 && res.status < 400) {
            const link = document.createElement('a')
            let blob = new Blob([res.data], { type: 'application/x-excel' })
            link.style.display = 'none'
            link.href = URL.createObjectURL(blob)
            link.download = '门店导出.xls'
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
          } else {
            message.error(res.statusText)
          }
        }).catch(function (err) {
          if (err.toString().includes('timeout')) {
            message.error('请求失败：可能是当前网络较慢，或者服务器响应慢，请稍后重试')
          } else {
            message.error(url + '请求失败：' + err.message)
          }
        }).finally(() => {
          exportLoading.value = false
        })
      }

      fetchData()

      return {
        formData,
        tableList,
        pagination,
        exportFile,
        add,
        fetchData,
        loading,
        exportLoading,
        handleTableChange
      }
    },
    render () {
      const { formData, tableList, pagination, exportFile, add, fetchData, exportLoading, loading } = this
      return <page class="area-info-manage">
        <NiListPage pushFilterToLocation={ false }>
          <search-box
            ref='searchBox'
            form-data={formData}
            onFetchData={() => { fetchData(1) }}
          />
          <table-box
            exportLoading={ exportLoading }
            pagination={ pagination }
            loading={ loading }
            table-list={ tableList }
            onAdd={ add }
            onExportFile={ exportFile }
            onFetchData={() => { fetchData(1) }}
            onTableChange={ this.handleTableChange }
          />
        </NiListPage>
      </page>
    }
  })
</script>
