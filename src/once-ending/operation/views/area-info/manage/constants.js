export const keyKindOptions = [
  {
    value: 'areaName',
    label: '名称'
  },
  {
    value: 'areaCode',
    label: '门店代码'
  }
]

export const isPassOptions = [
  {
    value: 1,
    label: '开通'
  },
  {
    value: 0,
    label: '关闭'
  }
]

export const isWebOptions = [
  {
    value: 1,
    label: '显示'
  },
  {
    value: 0,
    label: '不显示'
  }
]

export const columns = [
  {
    title: 'rank',
    dataIndex: 'rank'
  },
  {
    title: 'area',
    dataIndex: 'area'
  },
  {
    title: '名称',
    dataIndex: 'areaName',
    scopedSlots: { customRender: 'areaName' }
  },
  {
    title: '类别',
    dataIndex: 'storeSort'
  },
  {
    title: '类别2',
    dataIndex: 'kindName'
  },
  {
    title: '级别',
    dataIndex: 'areaLevel'
  },
  {
    title: '门店标签',
    dataIndex: 'areaTags',
    only9ji: true,
    width: 200
  },
  {
    title: '授权体系',
    dataIndex: 'authorizeValue',
  },
  {
    title: '公司主体',
    dataIndex: 'printName'
  },
  {
    title: '纳税主体',
    dataIndex: 'swkindStr',
    forceHide: window.tenant.xtenant < 1000
  },
  {
    title: '加盟费',
    dataIndex: 'jiamengM'
  },
  {
    title: '保证金',
    dataIndex: 'baoZhengM'
  },
  {
    title: '闭店时间',
    dataIndex: 'endTime'
  },
  {
    title: '店面负责人',
    dataIndex: 'curAdmin'
  },
  {
    title: '店面第二负责人',
    dataIndex: 'secondAdmin',
    only9ji: true
  },
  {
    title: '上级负责人',
    dataIndex: 'preAdmin'
  },
  {
    title: '上级分管人',
    dataIndex: 'preAdminF'
  },
  {
    title: '状态',
    dataIndex: 'isPass'
  }
]
export const importColumns = [
  {
    title: '门店',
    dataIndex: 'area'
  }, {
    title: '星期',
    dataIndex: 'week'
  }, {
    title: '营业开始时间',
    dataIndex: 'openTime'
  }, {
    title: '营业结束时间',
    dataIndex: 'closeTime'
  }, {
    title: '单独开始配送时间',
    dataIndex: 'deliveryStartTime',
    customRender: text => text || '-'
  }, {
    title: '单独结束配送时间',
    dataIndex: 'deliveryEndTime',
    customRender: text => text || '-'
  }
]
