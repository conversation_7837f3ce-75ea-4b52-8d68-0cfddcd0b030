<script type="text/jsx" lang="jsx">
  import { defineComponent, toRefs, reactive, getCurrentInstance } from 'vue'
  import { Select, Input } from 'ant-design-vue'
  import * as constants from '../constants'
  import mbaSelect from '@operation/components/mba-select'
  import { AREA_INFO_QUERY_CRITERIA } from '@operation/store/modules/area-info/action-types'
  import { NiAreaSelect, NiFilter, NiFilterItem } from '@jiuji/nine-ui'

  export default defineComponent({
    name: 'search-box',
    components: {
      NiAreaSelect,
      NiFilter,
      NiFilterItem,
      mbaSelect
    },
    props: {
      formData: {
        type: Object,
        default: () => ({})
      },
    },
    data () {
      return {
        constants
      }
    },
    setup () {
      const { proxy } = getCurrentInstance()
      const options = reactive({
        areaLevelOptions: [],
        areaKindOptions: [],
        printNameOptions: [],
        authorizeOptions: [],
        taxationOptions: [
          { label: '个人', value: 0 },
          { label: '公司', value: 1 }
        ],
        attributesOptions: [],
        categorieOptions: [],
        areaTagOptions: [],
      })
      const fetchData = function () {
        proxy.$emit('fetchData')
      }

      function dealTree (array) {
        return array.map(item => ({
          label: item.title
        }))
      }

      const queryCriteria = async function () {
        const res = await proxy.$store.dispatch(`operation/areaInfo/${AREA_INFO_QUERY_CRITERIA}`)
        if (res) {
          const { data } = res
          console.log('categories', ('areaLevel').slice(0, -1))
          const list = ['areaKinds', 'areaLevels', 'printNames', 'categories', 'areaTags']
          list.forEach(item => {
            options[`${item.slice(0, -1)}Options`] = data[item] ? data[item].map(d => {
              return {
                value: d.code || d,
                label: d.name || d
              }
            }) : []
          })
          options.attributesOptions = data.attributes
          console.log('options', options)
        }
      }

      queryCriteria()

      return {
        ...toRefs(options),
        fetchData
      }
    },
    render () {
      const permission91 = this.$store.state.userInfo.Rank.includes('91')
      const { formData, areaLevelOptions, areaKindOptions, printNameOptions, fetchData, authorizeOptions, taxationOptions, attributesOptions, categorieOptions, areaTagOptions } = this
      return (
        <NiFilter
          class="relative"
          form={ formData }
          onFilter={ fetchData }
          immediate={false}
          unfoldCount={ 5 }
        >
          <ni-filter-item label='地区'>
            <NiAreaSelect
              multiple
              allowClear
              checkable
              ranks={ permission91 ? null : ['dmbj'] }
              placeholder="请选择地区或者搜索"
              maxTagCount={1}
              treeNodeFilterProp="label"
              v-model={formData.areaCode}
            />
          </ni-filter-item>
          <ni-filter-item label='门店级别'>
            <Select v-model={formData.areaLevel} options={areaLevelOptions} allowClear placeholder="门店级别"></Select>
          </ni-filter-item>
          <ni-filter-item label='门店类别'>
            <Select v-model={formData.areaKind} options={areaKindOptions} allowClear placeholder="门店类别"></Select>
          </ni-filter-item>
          <ni-filter-item label='公司主体'>
            <Select v-model={formData.printName} options={printNameOptions} allowClear placeholder="公司主体"></Select>
          </ni-filter-item>
          { this.$tnt.xtenant >= 1000 ? <ni-filter-item label='纳税主体'>
            <Select v-model={formData.swKind} options={taxationOptions} allowClear placeholder="纳税主体"></Select>
          </ni-filter-item> : null }
          <ni-filter-item label='搜索方式'>
            <a-input-group compact>
              <Select v-model={formData.keyKind} options={constants.keyKindOptions} placeholder="搜索方式"></Select>
              <Input v-model={formData.mainKey} allowClear placeholder="关键词"></Input>
            </a-input-group>
          </ni-filter-item>
          <ni-filter-item label='是否开通'>
            <Select v-model={formData.isPass} options={constants.isPassOptions}></Select>
          </ni-filter-item>
          <ni-filter-item label='网站显示'>
            <Select v-model={formData.isWeb} options={constants.isWebOptions} allowClear placeholder="网站显示"></Select>
          </ni-filter-item>
          <ni-filter-item label='门店专区' v-show={ this.$tnt.xtenant === 0 }>
            <mba-select placeholder="门店专区" v-model={ formData.zones } style="width:130px"/>
          </ni-filter-item>
          <ni-filter-item label='授权体系' v-show={ this.$tnt.xtenant >= 1000 }>
            <Select v-model={formData.authorizeidList} options={authorizeOptions} mode="multiple" maxTagCount={1} allowClear placeholder="授权体系"></Select>
          </ni-filter-item>
          <ni-filter-item label='门店属性'>
            <a-tree-select
            v-model={formData.attributeList}
            treeData={attributesOptions}
            maxTagCount={1}
            treeCheckable={true}
            placeholder="请选择"
            treeNodeFilterProp="title"
            dropdown-style={{ maxHeight: '300px' }}
            get-popup-container={(triggerNode) => triggerNode.parentNode}
            allowClear />
          </ni-filter-item>
          <ni-filter-item label='门店类型'>
            <Select v-model={formData.category} options={categorieOptions} placeholder="请选择" allowClear/>
          </ni-filter-item>
          { this.$tnt.xtenant < 1000 ? <ni-filter-item label='门店标签'>
            <Select mode="multiple" maxTagCount={1} optionFilterProp="children" v-model={formData.areaTags} options={areaTagOptions} placeholder="请选择" allowClear/>
          </ni-filter-item> : null }
          {
            // permissionDMGL && <Button onClick={add} style="margin-right:10px">添加</Button>
          }
          {
            // $tnt.xtenant !== 0 && <Button style="margin-right:10px" loading={exportLoading} onClick={exportFile}>{exportLoading ? '导出中' : '导出'}</Button>
          }
          {
            // $tnt.xtenant !== 0 && <Button style="margin-right:10px" onClick={importTemplate}>导入</Button>
          }
          {
            // $tnt.xtenant !== 0 && <input type="file" ref="import" style="display:none" class="hide" onChange={fileChange}/>
          }
          {
            // $tnt.xtenant !== 0 && <Button style="margin-right:10px" onClick={downloadTemplate}>模板下载</Button>
          }
      </NiFilter>
      )
    }
  })
</script>
<style scoped>
.ant-input-group.ant-input-group-compact{
  display: flex;
}
</style>
