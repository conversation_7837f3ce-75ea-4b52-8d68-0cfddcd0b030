<script type="text/jsx" lang="jsx">
  import { defineComponent, computed, getCurrentInstance } from 'vue'
  import { Pagination } from 'ant-design-vue'

  export default defineComponent({
    name: 'pagination-box',
    props: {
      pagination: {
        type: Object,
        default: () => ({})
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const paginationChange = function (current, size) {
        props.pagination.current = current
        props.pagination.size = size
        proxy.$emit('fetchData')
      }
      const showTotal = function (total) {
        return `共${total}条记录`
      }
      return {
        showTotal,
        paginationChange
      }
    },
    render () {
      const { pagination, paginationChange, showTotal } = this
      return <div class="pagination-box">
      <Pagination
    v-model={pagination.current}
    show-total={showTotal}
    page-size={pagination.size}
    total={pagination.total}
    onChange={paginationChange}
  />
    </div>
    }
  })
</script>
<style lang="scss" scoped>
.pagination-box {
  padding: 10px;
  background: #fff;
  text-align: right;
}
</style>
