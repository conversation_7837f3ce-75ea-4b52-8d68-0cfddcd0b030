<script type="text/jsx" lang="jsx">
  import { defineComponent, getCurrentInstance } from 'vue'
  import { AREA_INFO_EXCEL_IMPORT } from '@operation/store/modules/area-info/action-types'
  import ExcelAction from '@operation/components/excel-action'
  import ImportExcel from '@/logistics/components/import/import-excel.vue'
  import { message } from 'ant-design-vue'
  import { NiTable } from '@jiuji/nine-ui'
  import * as constants from '../constants'

  export default defineComponent({
    name: 'Table-box',
    components: {
      ImportExcel,
      NiTable,
      ExcelAction
    },
    props: {
      tableList: {
        type: Array,
        default: () => ([])
      },
      loading: {
        type: Boolean,
        default: false
      },
      pagination: {
        type: Object,
        default: () => ({})
      },
      exportLoading: {
        type: Boolean,
        default: false
      }
    },
    data () {
      return {
        constants,
        customRenderMap: new Map([
          [
            'areaName',
            (text, record) => {
              return <a href={`/zitidian/areainfo_edit?id=${record.areaId}`} target="_blank">{{ text }}</a>
            }
          ],
          [
            'text',
            (text) => text || text === 0 ? text : '--'
          ]
        ])
      }
    },
    setup (props, ctx) {
      const { proxy } = getCurrentInstance()
      const add = function () {
        ctx.emit('add')
      }
      const exportFile = function () {
        ctx.emit('exportFile')
      }
      const importTemplate = async function () {
        proxy.$refs.import.click()
      }
      const handleTableChange = (paginations) => {
        ctx.emit('tableChange', paginations)
      }
      const downloadTemplate = function () {
        window.location.href = `${window.location.origin}/cloudapi_nc/ncSegments/api/areaInfo/importTemplate?xservicename=oa-ncSegments`
      }
      const fileChange = async function (e) {
        const file = e.target.files[0]
        if (file) {
          const formData = new FormData()
          formData.append('file', file)
          try {
            const res = await proxy.$store.dispatch(`operation/areaInfo/${AREA_INFO_EXCEL_IMPORT}`, formData)
            if (res) {
              message.success('导入成功')
              proxy.$parent.$refs.searchBox.queryCriteria() // 测试
            }
          } catch (e) {
            console.error(e)
          } finally {
            e.target.value = ''
          }
        }
      }
      const downloadTemplate9ji = () => {
        window.location.href = 'https://img2.ch999img.com/newstatic/68302/1b04dbcabba404f3.xlsx'
      }
      const afterSuccess = () => {
        proxy.$emit('fetchData')
      }
      return {
        add,
        exportFile,
        importTemplate,
        downloadTemplate,
        fileChange,
        handleTableChange,
        downloadTemplate9ji,
        afterSuccess
      }
    },
    render () {
      const {
        $tnt,
        tableList,
        loading,
        exportLoading,
        add,
        exportFile,
        importTemplate,
        downloadTemplate,
        fileChange,
        handleTableChange,
        downloadTemplate9ji,
        afterSuccess
      } = this
      const permissionDMGL = this.$store.state.userInfo.Rank.includes('dmgl')
      // const scopedSlots = {
      //   areaName: (text, record) => {
      //     return <a href={`/zitidian/areainfo_edit?id=${record.areaId}`} target="_blank">{{ text }}</a>
      //   }
      // }
      const columns = ($tnt.xtenant === 0 ? constants.columns.filter(item => !item.dataIndex.includes('authorizeValue')) : constants.columns.filter(it => !it.only9ji)).map(it => ({
        ...it,
        customRender: this.customRenderMap.get(it.dataIndex) || this.customRenderMap.get('text')
      }))
      return <div class="table-box">
        <NiTable
          rowKey="area"
          loading={loading}
          columns={columns}
          data-source={tableList}
          pagination={this.pagination}
          onChange={ handleTableChange }
        >
          <div slot = 'action'>
            {
              permissionDMGL && <a-button onClick={add} class='mr-8'>添加</a-button>
            }
            { $tnt.xtenant === 0
              ? <a-popover placement="bottom">
                  <template slot="content">
                    <ExcelAction
                      actionType='upload'
                      scene='area_import'
                      onAfterSuccess={ afterSuccess }
                    />
                    <a-button class='mt-8' onClick={ downloadTemplate9ji }>模板下载</a-button>
                  </template>
                  <a-button>批量导入</a-button>
                </a-popover>
              : <span>
                  <a-button class='mr-8' loading={exportLoading} onClick={exportFile}>{exportLoading ? '导出中' : '导出'}</a-button>
                  <a-button class='mr-8' onClick={importTemplate}>导入</a-button>
                  <input type="file" ref="import" class="hide" onChange={fileChange}/>
                  <a-button onClick={downloadTemplate}>模板下载</a-button>
                </span>
            }
            {$tnt.xtenant === 0
              ? <import-excel
                class="ml-8"
                importBtnText="营业时间导入"
                importBtnType="default"
                size={10}
                rows={10000}
                temUrl="https://img2.ch999img.com/newstatic/51576/0ca9cbd1dfa67404.xls"
                checkApi="areaInfo.importOpeningHoursCheck"
                importApi="areaInfo.importOpeningHours"
                columns={constants.importColumns}
                onOkImport={afterSuccess}
              />
              : null}
          </div>
        </NiTable>
  </div>
    }
  })
</script>
<style lang="scss" scoped>
.table-box {
  margin-top: 20px;
  background: #fff;
}
</style>
