import { inject, provide, reactive } from 'vue'
const key = Symbol('useState')

export const useState = function () {
  return inject(key)
}

export const createState = function () {
  const state = reactive({
    dataCount: {
      totalQuota: 0, // 总额度
      occupyQuota: 0, // 占用额度
      laveQuota: 0, // 剩余额度
    },
    tableData: [],
    dataSource: [],
  })

  const stateDates = {
    state,
  }
  provide(key, stateDates)
  return stateDates
}
