import { getCurrentInstance } from 'vue'
import { useApi } from '../../common/hooks/use-api'
import { useState } from './use-state.js'
import { tableRow, mockData } from '../constants'

export default function useActions () {
  const { proxy: { $store, $indicator } } = getCurrentInstance()
  const { state } = useState()
  const {
    getGoodsQuota
  } = useApi()

  const fetchData = async () => {
    const params = {
      areaId: $store.state.userInfo.areaid,
      // areaId: 2,
    }
    $indicator.open()
    const { data } = await getGoodsQuota(params)
    $indicator.close()
    if (!data) return
    // const { data } = mockData
    console.log('data', data)

    state.dataCount = {
      totalQuota: data.totalQuota, // 总额度
      occupyQuota: data.occupyQuota, // 占用额度
      laveQuota: data.laveQuota, // 剩余额度
    }
    state.tableData = tableRow.map(it => {
      // console.log('-->', data[it.dataIndex])
      return {
        ...it,
        total: data[it.dataIndex].total, // 数量
        totalAmount: data[it.dataIndex].totalAmount, // 	总金额
        amountRatio: (data[it.dataIndex].amountRatio * 100).toFixed(2), // 占比
      }
    })
  }

  return {
    fetchData
  }
}
