<script lang="jsx">
  import { defineComponent, onMounted } from 'vue'
  import { useState } from '../../hooks/use-state'
  import useActions from '../../hooks/use-actions'
  import CardItem from '../../../common/components/card-item.vue'

  const icon1 = require('../../../common/icons/all.png')
  const icon2 = require('../../../common/icons/zy.png')
  const icon3 = require('../../../common/icons/sy.png')
  export default defineComponent({
    components: {
      CardItem
    },
    setup () {
      const { state } = useState()
      const { fetchData } = useActions()

      onMounted(() => {
        fetchData()
      })
      return {
        state
      }
    },
    render () {
      const {
        state: {
          dataCount
        },
      } = this

      return (
        <div class='data-card-wrap'>
          <CardItem
            data={dataCount.totalQuota}
            dataLabel='总额度'
            icon={icon1}
          />
          <CardItem
            data={dataCount.occupyQuota}
            dataColor='#F21C1C'
            dataLabel='占用额度款'
            icon={icon2}
          />
          <CardItem
            data={dataCount.laveQuota}
            dataColor='#2AC57F'
            dataLabel='剩余额度款'
            icon={icon3}
          />
        </div>
      )
    },
  })
</script>
<style lang="scss" scoped>
  .data-card-wrap{
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px
  }
</style>
