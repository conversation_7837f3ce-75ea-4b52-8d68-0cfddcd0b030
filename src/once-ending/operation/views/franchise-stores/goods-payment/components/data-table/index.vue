<script lang="jsx">
  import { defineComponent } from 'vue'
  import { useState } from '../../hooks/use-state'
  import { NiPrice } from '@jiuji/nine-ui'

  export default defineComponent({
    components: {
      NiPrice,
    },
    setup () {
      const { state } = useState()

      const handleShowMore = () => {
        console.log('更多')
      }
      const handleDetailKC = () => {
        console.log('库存占用明细')
      }
      const handleDetailBS = () => {
        console.log('报损占用明细')
      }
      const handleDetailTH = () => {
        console.log('退货占用明细')
      }
      return {
        state,

        handleShowMore,
        handleDetailKC,
        handleDetailBS,
        handleDetailTH
      }
    },
    render () {
      const {
        state: {
          tableData
        },
        columns = [],
      } = this

      return (
        <div class='table-wrap'>
          <div class='table-top-bar'><h4 class='table-title'>额度款占用明细</h4></div>
          <a-table
            dataSource={tableData}
            columns={columns}
            pagination={false}
            rowKey={(record, index) => index}
          />
        </div>
      )
    },
    data () {
      return {
        columns: [
          {
            title: '占用名称',
            dataIndex: 'category',
            width: '40%',
            customRender: (text, record) => {
              return <div class='col-name'>
                <h4>{text}</h4>
                <p>{record.remark}</p>
              </div>
            }
          },
          {
            title: '总金额',
            dataIndex: 'totalAmount',
            width: '18%',
            customRender: (text) => {
              return <NiPrice
                thousand
                precision={2}
                decimalFontSize={14}
                value={ text }/>
            }
          },
          {
            title: '总数量',
            dataIndex: 'total',
            width: '12%',
            customRender: (text) => {
              return <p>{text}</p>
            }
          },
          {
            title: '额度占比',
            dataIndex: 'amountRatio',
            width: '20%',
            customRender: (text) => {
              const textNumber = parseFloat(text)
              return <a-progress
                percent={ textNumber }
                strokeColor={ textNumber > 79 ? '#ff0000' : textNumber > 49 ? '#1890FF' : '#52C41A' }
              />
            }
          },
          {
            title: '操作',
            dataIndex: 'operation',
            width: 20,
            customRender: (text, record) => {
              // const host = process.env.NODE_ENV === 'development' ? 'http://localhost:4949' : this.$tnt.oaHost
              const host = this.$tnt.oaHost
              const areaId = this.$store.state.userInfo.areaid

              return record.needDetail
                ? <span>
                    <a rel="noopener noreferrer" target="_blank" href={ `${host}${record.inventoryLink}${areaId}&HideNegativeInventoryStockOutProduct=1` }>库存占用明细</a>
                    <a rel="noopener noreferrer" target="_blank" href={ `${host}${record.lossLink}${areaId}` }>报损占用明细</a>
                    <a rel="noopener noreferrer" target="_blank" href={ `${host}${record.backLink}${areaId}` }>退货占用明细</a>
                  </span>
                : <a
                    rel="noopener noreferrer"
                    href={ `${host}${record.moreLink}${areaId}` }
                    target="_blank"
                  >
                  查看更多
                </a>
            }
          },
        ]
      }
    }
  })
</script>
<style lang="scss" scoped>
  .table-wrap{
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    padding-bottom: 50px;

    .ant-btn-link{
      padding-left: 0;
    }

    .table-top-bar{
      display: flex;
      position: relative;

      .table-title {
        font-weight: 600;
        font-size: 16px;
        margin-bottom: 25px;

        &::after{
          content: '';
          display: block;
          margin-top: -6px;
          width: 100%;
          height: 6px;
          background: linear-gradient(to right, #1890FF, #fff)
        }
      }
    }
    .col-name{
      p{
        font-size: 14px;
        color: #9c9c9c;
      }
    }
    .ant-table-row-cell-break-word{
      a{
        display: block;
        white-space: nowrap;
      }
    }
  }
</style>
