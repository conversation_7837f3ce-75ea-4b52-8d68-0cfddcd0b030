<script lang="jsx">
  import { defineComponent } from 'vue'
  import DataCard from './components/data-card/index.vue'
  import DataTable from './components/data-table/index.vue'
  import { createApi } from '../common/hooks/use-api'
  import { createState } from './hooks/use-state'

  export default defineComponent({
    components: {
      DataCard,
      DataTable,
    },
    setup () {
      createApi()
      createState()
      return {
      }
    },
    render () {
      return <page>
        <DataCard class='mb-12'/>
        <DataTable/>
      </page>
    }
  })
</script>
<style lang="scss" scoped>
  .mb-12{
    margin-bottom: 12px
  }
</style>
