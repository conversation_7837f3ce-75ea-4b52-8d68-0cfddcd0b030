export const tableRow = [
  {
    category: '大件库存',
    remark: '库存状态：库存、售后、在途、欠款、退货中(包括陈列库存)',
    dataIndex: 'largeInventory',
    moreLink: '/ProductMkc/mkcCheck?actionName=search&kc_check=3,6,8,10,14&areacode_='
  },
  {
    category: '回收良品库存',
    remark: '包括：库存、在途、欠款、售后、寄售状态(包括陈列库存)',
    dataIndex: 'recycleProductInventory',
    moreLink: '/recoverIndex/recoverMkcList?actionName=search&kc_check=3,6,8,10,11&areaCode_='
  },
  {
    category: '销售配件库存',
    remark: '包括销售配件库存(在途、库存)，小件陈列瑕疵库存',
    dataIndex: 'salePartInventory',
    needDetail: true,
    inventoryLink: '/productKC/kcNow?actionName=search&areaCode_=', // 库存占用明细
    lossLink: '/back/backList?actionName=search&checkState=2&type_=2&_split_page=2&areaCode_=', // 报损占用明细
    backLink: '/back/backList?actionName=search&checkState=2&type_=1&_split_page=1&areaCode_=', // 退货占用明细
  },
  {
    category: '维修配件库存',
    remark: '维修配件库存',
    dataIndex: 'repairPartInventory',
    needDetail: true,
    inventoryLink: '/productKC/kcNow?actionName=search&kinds=wx&areaCode_=',
    lossLink: '/back/backList?actionName=search&checkState=2&type_=6&_split_page=6&areaCode_=',
    backLink: '/back/backList?actionName=search&checkState=2&type_=5&_split_page=5&areaCode_=',
  },
  {
    category: '退换小件旧件库存',
    remark: '小件退换生成的旧件库存(转现和报废后不做计算)',
    dataIndex: 'smallOldPartInventory',
    moreLink: '/staticpc/#/small-refund/old-list?status=0&toAreaStatus=3&toAreaIdStrings='
  },
  {
    category: '维修配件换货库存',
    remark: '维修配件',
    dataIndex: 'repairPartExchangeInventory',
    moreLink: '/shouhou/HuishouListhuanhuo?actionName=search&huanhuokind=2&areaCode_='
  },
  {
    category: '备用机库存',
    remark: '状态为：限制和使用中的备用机库存',
    dataIndex: 'standbyInventory',
    moreLink: '/staticpc/#/market/bak-machines?areaIds='
  }
]
export const mockData = {
  data: {
    laveQuota: -1485570.3431,
    occupyQuota: 1485570.3431,
    totalQuota: 0,

    largeInventory: {
      amountRatio: 0.98,
      total: 303,
      totalAmount: 1458374
    },
    recycleProductInventory: {
      amountRatio: 0.01,
      total: 26,
      totalAmount: 21556.6
    },
    repairPartExchangeInventory: {
      amountRatio: 0,
      total: 0,
      totalAmount: 0
    },
    repairPartInventory: {
      amountRatio: 0,
      total: 0,
      totalAmount: 0
    },
    salePartInventory: {
      amountRatio: 0,
      total: 73,
      totalAmount: 1109.1731
    },
    smallOldPartInventory: {
      amountRatio: 0,
      total: 1,
      totalAmount: 1230.02
    },
    standbyInventory: {
      amountRatio: 0,
      total: 10,
      totalAmount: 3300.55
    }
  }
}
