<script lang="jsx">
  import { defineComponent, ref, onMounted, getCurrentInstance } from 'vue'
  import DataCard from './components/data-card/index.vue'
  import DataTable from './components/data-table/index.vue'
  import { createApi } from '../common/hooks/use-api'
  import { createState } from './hooks/use-state'

  export default defineComponent({
    components: {
      DataCard,
      DataTable,
    },
    setup () {
      const { proxy: { $store } } = getCurrentInstance()
      createApi()
      createState()
      const pageTitle = ref('加盟店经营返利')
      onMounted(() => {
        const { Area } = $store.state.userInfo
        pageTitle.value = `加盟店经营返利【${Area}】`
        document.title = pageTitle.value
      })
      return {
        pageTitle
      }
    },
    render () {
      return <page title={ this.pageTitle }>
        <DataCard class='mb-12'/>
        <DataTable/>
      </page>
    }
  })
</script>
<style lang="scss" scoped>
  .mb-12{
    margin-bottom: 12px
  }
</style>
