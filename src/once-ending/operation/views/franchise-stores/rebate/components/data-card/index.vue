<script lang="jsx">
  import { defineComponent, onMounted } from 'vue'
  import { useState } from '../../hooks/use-state'
  import useActions from '../../hooks/use-actions'
  import CardItem from '../../../common/components/card-item.vue'

  const icon1 = require('../../../common/icons/fl-by.png')
  const icon2 = require('../../../common/icons/fl-ls.png')
  const icon3 = require('../../../common/icons/fl-pj.png')
  export default defineComponent({
    components: {
      CardItem
    },
    setup () {
      const { state } = useState()
      const { fetchData } = useActions()

      onMounted(() => {
        fetchData()
      })
      return {
        state
      }
    },
    render () {
      const {
        state: {
          dataCount
        }
      } = this
      return (
        <div class='data-card-wrap'>
          <CardItem
            data={dataCount.expectObtain}
            dataLabel={`本月预计可得(返利比例：${dataCount.rebateRatio}%)`}
            icon={icon1}
          />
          <CardItem
            data={dataCount.totalRebate}
            dataColor='#F21C1C'
            dataLabel='历史总返利'
            icon={icon2}
          />
          <CardItem
            data={dataCount.avgRebate}
            dataColor='#2AC57F'
            dataLabel='平均返利比例'
            dataType='percent'
            icon={icon3}
          />
        </div>
      )
    },
  })
</script>
<style lang="scss" scoped>
  .data-card-wrap{
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px
  }
</style>
