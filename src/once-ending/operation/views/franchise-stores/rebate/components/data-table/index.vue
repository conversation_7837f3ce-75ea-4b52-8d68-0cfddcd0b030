<script lang="jsx">
  import { defineComponent, watch, getCurrentInstance } from 'vue'
  import { useState } from '../../hooks/use-state'
  import useActions from '../../hooks/use-actions'
  import { NiPrice } from '@jiuji/nine-ui'
  import moment from 'moment'

  export default defineComponent({
    components: {
      NiPrice
    },
    setup () {
      const { proxy: { $store } } = getCurrentInstance()
      const { loading, isFeatch, state } = useState()
      const { fetchData } = useActions()

      const handleChange = (moment, str) => {
        state.month = str
        fetchData()
      }
      const handleDownExcel = (record) => {
        const params = [
          {
            type: 'category',
            value: state.month,
            target: ['variable', ['template-tag', 'date']]
          },
          {
            type: 'category',
            value: $store.state.userInfo.areaid,
            target: ['variable', ['template-tag', 'areaid']]
          }
        ]
        const parameters = JSON.stringify(params)
        window.location.href = `${record.detailLink}?parameters=${parameters}`
      }
      const disabledDate = (current) => {
        const time = moment('2023-03').format('YYYY-MM')
        return current && current < moment(time)
      }
      watch(
        () => state.activeKey,
        val => {
          if (val === '1') {
            state.month = state.currentMonth
            fetchData()
          }
        }
      )

      return {
        loading,
        isFeatch,
        state,
        disabledDate,
        handleChange,
        handleDownExcel,
      }
    },
    render () {
      const {
        state,
        state: {
          tableData,
          dataCount
        },
        disabledDate,
        columns,
        handleChange,
      } = this

      const monthDetail = () => (
        <span class='tab-select'>
          <a-month-picker
            placeholder="选择查看历史返利明细"
            format="YYYY-MM"
            value-format="YYYY-MM"
            disabledDate={ disabledDate }
            v-model={state.month}
            onOpenChange={ v => { state.monthPickerShow = v } }
            onChange={ (moment, str) => { handleChange(moment, str) } }
          />
          <span class='picker-text'>
            返利明细
            <i class={['fa', 'ml-8', 'font-16', state.monthPickerShow ? 'fa-chevron-up' : 'fa-chevron-down']}/>
          </span>
        </span>
      )
      return (
        <div class='table-wrap'>
          <a-tabs
            v-model={state.activeKey}
            type='card'
            tabBarGutter={12}
          >
            <a-tab-pane key="1" tab="本月当前毛利"></a-tab-pane>
            <a-tab-pane key="2" tab={monthDetail}></a-tab-pane>
          </a-tabs>
          <p class='refresh-time'>数据更新至：{state.refreshTime}</p>
          <a-table
            dataSource={tableData}
            columns={columns}
            pagination={false}
            rowKey={(record, index) => index}
          >
            <div slot='footer' class='table-acount'>
              合计：
              <NiPrice
                class='red-num'
                thousand
                color='#F21C1C'
                decimalColor='#F21C1C'
                prefixColor='#F21C1C'
                precision={2}
                prefixSize={18}
                fontSize={18}
                decimalFontSize={18}
                value={ dataCount.totalProfit }/>
            </div>
          </a-table>
        </div>
      )
    },
    data () {
      return {
        columns: [
          {
            title: '统计名称',
            dataIndex: 'category',
            width: 300,
            customRender: (text, record) => {
              return <div class='col-name'>
                <h4>{text}</h4>
                <p>{record.remark}</p>
              </div>
            }
          },
          {
            title: '总毛利',
            dataIndex: 'totalProfit',
            width: 110,
            customRender: (text) => {
              return <NiPrice
                thousand
                precision={2}
                decimalFontSize={14}
                value={ text }/>
            }
          },
          {
            title: '额度占比',
            dataIndex: 'profitRatio',
            width: 200,
            customRender: (text) => {
              const textNumber = parseFloat(text)
              return <a-progress
                percent={ textNumber }
                strokeColor={ textNumber > 79 ? '#ff0000' : textNumber > 49 ? '#1890FF' : '#52C41A' }
              />
            }
          },
          {
            title: '操作',
            dataIndex: 'operation',
            width: 20,
            customRender: (text, record) => {
              if (!record.detailLink) return
              return (
                <a
                  rel="noopener noreferrer"
                  href="javascript:;"
                  onClick={ () => { this.handleDownExcel(record) } }
                >
                  下载明细
                </a>
              )
            }
          },
        ]
      }
    }
  })
</script>
<style lang="scss" scoped>
  .table-wrap{
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
  }
  .refresh-time{
    font-size: 14px;
    color: #9c9c9c;
    padding: 12px 0;
    margin-bottom: 8px;
  }
  .table-acount{
    font-size: 16px;
    font-weight: 600;
  }
  .red-num{
    font-size: 18px;
    font-weight: 600;
    color: red;
  }
  .col-name{
    p{
      font-size: 14px;
      color: #9c9c9c;
    }
  }
  .ant-btn-link{
    padding-left: 0;
  }
  :deep(.ant-tabs-bar){
    border-bottom: none;
    margin-bottom: 1px;
  }
  :deep(.ant-tabs.ant-tabs-card .ant-tabs-card-bar){
    .ant-tabs-tab{
      background: #fff;
      border-radius: 4px;
    }
    .ant-tabs-tab-active{
      border: 1px solid #1890ff ;
      .ant-input{
        color:#1890ff;
      }
    }
  }
  :deep(.tab-select){
    position: relative;
    .ant-input{
      border: none;
      width: 136px;
      padding: 0;
      background: transparent;
    }
    .ant-calendar-picker{
      z-index: 2;
      i { display: none;}
    }
    .picker-text{
      position: absolute;
      margin-left: -56%;
      z-index: 1;
    }
  }
</style>
