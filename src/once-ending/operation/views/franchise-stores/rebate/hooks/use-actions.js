import { getCurrentInstance } from 'vue'
import moment from 'moment'
import { useApi } from '../../common/hooks/use-api'
import { useState } from './use-state.js'
import { tableRow } from '../constants'

export default function useActions () {
  const { proxy: { $store, $indicator } } = getCurrentInstance()
  const { state } = useState()
  const {
    getRebate
  } = useApi()

  const fetchData = async () => {
    const params = {
      areaId: $store.state.userInfo.areaid,
      rechargeTime: state.month
    }
    $indicator.open()
    const { data } = await getRebate(params)
    $indicator.close()
    if (!data) return

    const currentTime = moment()
    const curTime = currentTime.format('YYYY-MM-DD HH:mm:ss')
    const curMonth = currentTime.format('YYYY-MM')
    const timeStar = `${state.month}-01 00：00：01`
    const timeEnd = state.month === curMonth ? curTime : `${state.month}-30 11：59：59`
    state.refreshTime = timeStar + ' 至 ' + timeEnd

    state.dataCount = {
      expectObtain: data.expectObtain, // 预计可得
      totalRebate: data.totalRebate, // 历史总返利
      totalProfit: data.totalProfit, // 总毛利
      rebateRatio: data.rebateRatio, // 返利比例
      avgRebate: (data.avgRebate * 100).toFixed(2) // 平均返利比例
    }
    state.tableData = tableRow.map(it => {
      return {
        ...it,
        totalProfit: data[it.categoryIndex].totalProfit,
        profitRatio: (data[it.categoryIndex].profitRatio * 100).toFixed(2),
      }
    })
  }

  return {
    fetchData
  }
}
