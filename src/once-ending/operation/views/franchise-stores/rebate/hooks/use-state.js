import { inject, provide, reactive } from 'vue'
import useCommon from '../../common/hooks/useCommon'
import moment from 'moment'

const currentMonth = moment().format('YYYY-MM')
const key = Symbol('useState')

export const useState = function () {
  return inject(key)
}

export const createState = function () {
  const { loading, isFeatch } = useCommon()
  const state = reactive({
    currentMonth,
    dataSource: [],
    activeKey: '1',
    month: currentMonth,
    refreshTime: '',

    dataCount: {
      totalProfit: 0, // 毛利
      totalRebate: 0, // 返利金额
      rebateRatio: 0, // 返利比例
      avgRebate: 0 // 平均返利
    },
    tableData: [],
  })

  const stateDates = {
    state,
    loading,
    isFeatch
  }
  provide(key, stateDates)
  return stateDates
}
