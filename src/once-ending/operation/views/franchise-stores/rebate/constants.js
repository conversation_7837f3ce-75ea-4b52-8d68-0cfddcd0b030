export const tableRow = [
  {
    category: '大件',
    remark: '大件销售单毛利-大件售后退换毛利(退换毛利-原订单对应商品成本)',
    categoryIndex: 'bigProduct',
    detailLink: 'https://oa.9ji.com/DataAnalysisPlatform/api/public/card/845e18ee-d1eb-4f4d-bc8d-8c4c6e911a6f/query/xlsx',
  },
  {
    category: '小件',
    remark: '新机销售单毛利-小件退款毛利-小件接件换货商品成本+小件接件换货商品费用',
    categoryIndex: 'smallProduct',
    detailLink: 'https://oa.9ji.com/DataAnalysisPlatform/api/public/card/01f9a710-9fa2-40ad-af4a-9555d673e00a/query/xlsx',
  },
  {
    category: '回收',
    remark: '回收设备转售毛利',
    categoryIndex: 'recycle',
    detailLink: 'https://oa.9ji.com/DataAnalysisPlatform/api/public/card/daf9127d-c917-4cdb-aae1-5fabdf2b835e/query/xlsx',
  },
  {
    category: '良品',
    remark: '良品订单的毛利-售后退换毛利',
    categoryIndex: 'goodProduct',
    detailLink: 'https://oa.9ji.com/DataAnalysisPlatform/api/public/card/34d128ad-c0b8-4814-b406-fd0c56917c04/query/xlsx',
  },
  {
    category: '维修毛利',
    remark: '没有出险服务配件的毛利-出险了服务配件的成本',
    categoryIndex: 'repair',
    detailLink: 'https://oa.9ji.com/DataAnalysisPlatform/api/public/card/a603516e-6870-41ea-b628-87a4464a7821/query/xlsx',
  },
  {
    category: '其他',
    remark: '其他毛利核算',
    categoryIndex: 'other',
  }
]
