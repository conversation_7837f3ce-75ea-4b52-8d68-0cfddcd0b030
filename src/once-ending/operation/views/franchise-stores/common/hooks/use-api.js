import { inject, provide } from 'vue'
import storeApi from '@operation/api/store'
import { to } from '~/util/common'
import { message } from 'ant-design-vue'
const franchiseStoreApi = storeApi.franchiseStore

const key = Symbol('useApi')

async function commApi (type, params = {}, url = '') {
  const [err, res] = await to(franchiseStoreApi[type](params, url))
  if (err) throw err
  const { code, userMsg } = res
  if (code !== 0) {
    message.error(userMsg)
  }
  return res
}

export const useApi = function () {
  return inject(key)
}

export const createApi = function () {
  const apiKey = ['getRebate', 'getGoodsQuota']
  const api = {}

  apiKey.forEach((item) => {
    api[item] = async (params) => {
      const res = await commApi(item, params)
      return res
    }
  })

  provide(key, api)
  return api
}
