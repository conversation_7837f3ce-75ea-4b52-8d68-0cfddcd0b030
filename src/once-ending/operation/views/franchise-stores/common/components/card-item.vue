<script lang="jsx">
  import { defineComponent } from 'vue'
  import { NiPrice } from '@jiuji/nine-ui'

  export default defineComponent({
    components: {
      NiPrice
    },
    props: {
      data: {
        type: [String, Number],
        default: '',
      },
      dataType: {
        type: String,
        default: 'price',
      },
      dataLabel: {
        type: String,
        default: '',
      },
      dataColor: {
        type: String,
        default: '',
      },
      icon: {
        type: String,
        default: '',
      }
    },
    setup () {
      return {
      }
    },
    render () {
      const {
        data,
        dataLabel,
        dataColor,
        icon,
        dataType
      } = this

      return (
        <div class='card'
          style={{ backgroundImage: `url(${icon})` }}>
          <div class='data-wrap'
            style={{
              color: dataColor
            }}
          >
            {
              dataType === 'price'
              ? <NiPrice
                  thousand
                  color={dataColor}
                  decimalColor={dataColor}
                  prefixColor={dataColor}
                  precision={2}
                  prefixSize={16}
                  fontSize={24}
                  decimalFontSize={24}
                  value={ data }/>
              : dataType === 'percent'
                ? <span>{ data }%</span>
                : <span>{ data }</span>
            }
            <i class='label'>{dataLabel}</i>
          </div>
        </div>
      )
    },
  })
</script>
<style lang="scss" scoped>
.card{
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: right center;
  background-size: contain;
  background-origin: content-box;
  border-radius: 8px;
  padding: 10px 56px;

  .data-wrap{
    padding: 30px 0;
    font-size: 24px;

    .label{
      display: block;
      color: #9c9c9c;
      font-size: 14px;
    }
  }
}
</style>
