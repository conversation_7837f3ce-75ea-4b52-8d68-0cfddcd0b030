import { inject, provide, reactive, computed, getCurrentInstance, ref } from 'vue'
import useCommon from '@/operation/views/statistics-report/hooks/useCommon'
import useLastUpdateDate from '@/operation/views/statistics-report/hooks/useLastUpdateDate'
import { defaultFormData } from '../constants'
import moment from 'moment'
import cloneDeep from 'lodash/cloneDeep'
import { message } from 'ant-design-vue'
import businessUnitApi from '@operation/api/business-unit'
const key = Symbol('useState')

export const useState = function () {
  return inject(key)
}

export const createState = function (api) {
  const { proxy } = getCurrentInstance()

  const { $store, } = proxy.$root

  const { loading, isFeatch } = useCommon()

  const { lastUpdateDate, setLastUpdateDate } = useLastUpdateDate()

  const state = reactive({
    dataSource: [],
    summary: {},
    sorter: {},
    items: [],
    formData: cloneDeep(defaultFormData),
    flterBoxHeight: 0,
    cacheParams: {}
  })

  function setTime () {
    const startTime = moment().format('YYYY-MM-DD')
    const endTime = moment().format('YYYY-MM-DD')
    const comparisonStartTime = moment().subtract(1, 'days').format('YYYY-MM-DD')
    const comparisonEndTime = moment().subtract(1, 'days').format('YYYY-MM-DD')
    state.formData.timeRang1 = [startTime, endTime]
    state.formData.timeRang2 = [comparisonStartTime, comparisonEndTime]
  }
  setTime()

  const ranks = computed(() => {
    return $store.state.userInfo.Rank || []
  })

  const hasjydy = computed(() => ranks.value?.includes('jydy'))

  const getQueryParams = function () {
    const { timeRang1, timeRang2, ...others } = state.formData
    const suffix1 = ' 00:00:00'
    const suffix2 = ' 23:59:29'
    const params = {
      ...others,
      startTime: timeRang1?.length ? timeRang1[0] + suffix1 : undefined,
      endTime: timeRang1?.length ? timeRang1[1] + suffix2 : undefined,
      comparisonStartTime: timeRang2?.length ? timeRang2[0] + suffix1 : undefined,
      comparisonEndTime: timeRang2?.length ? timeRang2[1] + suffix2 : undefined,
    }
    return params
  }

  const checkForm = function () {
    return false
  }

  const getItem = function (item) {
    Object.keys(item).map(d => {
      const subItem = item[d]
      if (Object.prototype.toString.call(subItem) === '[object Object]') {
        delete item[d]
        const { current, previous, scope, compare, compareItems } = subItem
        const o = {
          [d]: current,
          [`${d}Previous`]: previous,
          [`${d}Scope`]: scope,
          [`${d}Compare`]: compare,
          [`${d}CompareItems`]: compareItems || {},
        }
        item = { ...item, ...o }
      }
    })
    return item
  }

  const canvasTable = ref(null)

  // table组件的方法,重写
  const drawSummary = function (fixed) {
    if (!this.summary) return
    const list = this.bodyColumnsByFixed[fixed]
    const row = this.summary
    for (let i = 0; i < list.length; i++) {
      const col = list[i]
      let width = col.width
      if (col.summarySpan > 1) {
        const endSpan = col.summarySpan - 1 + i
        while (i < endSpan && i < list.length) {
          i++
          width += list[i].width
        }
      }
      let _left = 0
      if (col.fixed === 'left') {
        _left = col._left
      } else if (col.fixed === 'right') {
        _left = col._left - this.maxScrollWidth
      } else {
        _left = col._left - this.scrollX
        if (_left + width < this.fixedLeftWidth) continue
        if (_left > this.wrapperWidth - this.fixedRightWidth) return
      }
      const _top = this.headerHeight + this.bodyHeight
      // 画背景边框
      this.drawCellRect(_left, _top, {
        width,
        height: this.summaryHeight,
        background: this.style.summaryBackground,
        [this.border ? 'border' : 'borderTop']: [1, this.style.borderColor]
      })
      if (col.summarySlot) continue

      if (!this.summaryCellInfo[col.key]) {
        this.summaryCellInfo[col.key] = this.getCellInfo(
          this.ctx,
          width - this.style.padding * 2, this.summaryHeight,
          {},
          {
            ...col,
            type: null,
            formatter: col.summaryFormatter || col.formatter
          },
          {
            color: this.style.color,
            fontSize: this.style.fontSize,
            fontFamily: 'sans-serif',
          },
          col.summaryAlign || col.align || 'left',
          0
        )
      }
      this.summaryCellInfo[col.key].forEach(item => this.drawCellText({
        ...item,
        x: item.x + _left + this.style.padding,
        y: item.y + _top,
        text: item._text,
        textWidth: item._textWidth || 0
      }))
    }
  }

  const fetchData = async function () {
    const params = {
      ...getQueryParams(),
    }
    state.cacheParams = cloneDeep(params)
    loading.value = true
    const res = await businessUnitApi.getList(params)
    loading.value = false
    if (res) {
      isFeatch.value = true
      const { data, total } = res
      state.dataSource = data.map(item => {
        return getItem(item)
      })
      state.summary = { ...getItem(total), areaName: '合计' }
      canvasTable.value.drawSummary = drawSummary
    }
  }
  fetchData()

  function changeRegion (val) {
    const { regionOnly } = state.formData
    state.formData.regionOnly = (regionOnly === val ? undefined : val)
    fetchData()
  }

  const exportDataLoading = ref(false)
  function exportData () {}

  provide(key, {
    state,
    lastUpdateDate,
    setLastUpdateDate,
    loading,
    isFeatch,
    fetchData,
    getQueryParams,
    checkForm,
    canvasTable,
    changeRegion,
    exportData,
    exportDataLoading,
    setTime,
    hasjydy
  })
  return {
    state,
    lastUpdateDate,
    setLastUpdateDate,
    loading,
    isFeatch,
    fetchData,
    getQueryParams,
    checkForm,
    canvasTable,
    changeRegion,
    exportData,
    exportDataLoading
  }
}
