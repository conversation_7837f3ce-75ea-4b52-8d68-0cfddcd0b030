import { ref, h, computed, watch, nextTick, provide, getCurrentInstance } from 'vue'
import { useState } from './use-state'
import ToDetail from '../components/to-detail.vue'
import ContrastData from '../components/contrast-data.vue'
import DisplayNote from '../components/display-note.vue'
import { Icon, message, Tooltip } from 'ant-design-vue'
import { originColumns } from '../constants'
import { cloneDeep } from 'lodash'
import businessUnitApi from '@/operation/api/business-unit'
import axios from 'axios'
import moment from 'moment'

export default function useTable (utilCreateCols, initCheckedList) {
  const { state, fetchData, canvasTable } =
    useState()

  const { proxy } = getCurrentInstance()
  const factorData = ref({
    recoverSingleProfitFactor: undefined,
    recoverSingleProfitNote: '',
    recoverSingleProfitNoteEdit: false,
    selfServiceProfitFactor: undefined,
    selfServiceProfitNote: '',
    selfServiceProfitNoteEdit: false,
  })

  const size = ref('middle')
  provide('size', size)
  const isFullscreen = ref(false)
  provide('isFullscreen', isFullscreen)
  const bordered = ref(true)
  provide('bordered', bordered)
  const checkIndex = ref(false)
  provide('checkIndex', checkIndex)
  const checkboxVal = ref([])
  provide('checkboxVal', checkboxVal)
  const halfCheckboxVal = ref([])
  provide('halfCheckboxVal', halfCheckboxVal)
  const columns = ref([])
  provide('columns', columns)
  const initCheckboxVal = computed(() => {
    return initCheckedList(columns.value, false, halfCheckboxVal)
  })

  watch(() => columns.value, val => {
    initCheckedList(columns.value, false, halfCheckboxVal)
  })
  provide('initCheckboxVal', initCheckboxVal)
  const allCheckboxVal = computed(() => {
    return initCheckedList(columns.value, true)
  })
  provide('allCheckboxVal', allCheckboxVal)
  const cols = ref([])
  const bussinessUnitConfigTable = ref({})
  provide('bussinessUnitConfigTable', bussinessUnitConfigTable)
  const getBussinessUnitConfigTable = function () {
    const config = localStorage.getItem('bussinessUnitConfigTable')
      ? JSON.parse(localStorage.getItem('bussinessUnitConfigTable'))
      : {}
    bussinessUnitConfigTable.value = config
    // 获取配置
    for (const k in bussinessUnitConfigTable.value) {
      if (bussinessUnitConfigTable.value[k].half) {
        !halfCheckboxVal.value.includes(k) && halfCheckboxVal.value.push(k)
      } else {
        if (halfCheckboxVal.value.includes(k)) {
          const index = halfCheckboxVal.value.findIndex((d) => d === k)
          halfCheckboxVal.value.splice(index, 1)
        }
      }
    }

    nextTick(() => {
      getCols()
    })
  }
  provide('getBussinessUnitConfigTable', getBussinessUnitConfigTable)
  const saveBussinessUnitConfigTable = function (data) {
    localStorage.setItem('bussinessUnitConfigTable', JSON.stringify(data))
    getBussinessUnitConfigTable()
  }
  provide('saveBussinessUnitConfigTable', saveBussinessUnitConfigTable)
  const renderIndex = function (text, record, index) {
    return index + 1
  }
  const getCols = function () {
    const l = utilCreateCols(
      columns.value,
      checkboxVal.value.length ? checkboxVal.value : initCheckboxVal.value,
      halfCheckboxVal.value
    )
    if (checkIndex?.value) {
      l.unshift({
        label: '序号',
        key: 'indexaAtive',
        width: 60,
        align: 'center',
        customRender: renderIndex,
      })
    }
    cols.value = l
    // 获取统计项
    const columnsKeys = columns.value
      .filter((d) => d.children && d.children.length)
      .map((d) => d.key)
    // 统计项去除一级表头
    const checkedColumns = checkboxVal.value.length
      ? checkboxVal.value
      : initCheckboxVal.value
    state.items = checkedColumns.filter((d) => !columnsKeys.includes(d))
    nextTick(() => {
      setColumnsConfig()
    })
  }

  const clientHeight = document.body.offsetHeight || document.body.clientHeight

  const canvasTableAttrs = ref({
    border: true,
    stripe: false,
    rowHeight: 42,
    maxHeight: clientHeight - 240,
    customStyle: {
      headerFontWeight: '500',
      borderColor: '#dfdfdf',
      headerBackground: '#f5f5f5',
      color: '#333',
      fontSize: 13,
      activeColBackground: '#F6FBFF',
      activeRowBackground: '#F6FBFF',
      stripeRowBackground: '#fafafa',
      summaryBackground: '#F5F5F5',
      background: '#fff',
    },
  })

  watch(
    () => size.value,
    () => {
      canvasTableAttrs.value.rowHeight =
        size.value === 'middle' ? 42 : size.value === 'default' ? 50 : 38
    }
  )

  watch(
    () => bordered.value,
    () => {
      canvasTableAttrs.value.border = bordered.value
    }
  )

  watch(
    () => [isFullscreen.value, state.flterBoxHeight],
    () => {
      canvasTableAttrs.value.maxHeight = isFullscreen.value
        ? clientHeight - state.flterBoxHeight - 55
        : clientHeight - state.flterBoxHeight - 205
    }
  )

  const raceEditFactorLoading = ref(false)

  const tableSort = function (sort) {
    const { sortField } = sort
    if (sortField === state.sorter.sortField) {
      const map = {
        '': 'desc',
        'desc': 'asc',
        'asc': '',
      }
      state.sorter.sortBy = map[state.sorter.sortBy]
    } else {
      state.sorter.sortBy = 'desc'
    }
    if (!state.sorter.sortBy) {
      state.sorter.sortField = ''
    } else {
      state.sorter.sortField = sortField
    }
    fetchData()
  }
  const headerHeight = ref(0)

  const eachData = function (arr, sorter, setting) {
    return arr.map((d) => {
      const {
        key: dataIndex,
        name: label,
        key,
        name: titleSetting,
        children,
        defaultItem,
        description,
        hasDetail,
        name,
      } = d
      if (setting) {
        return {
          dataIndex,
          titleSetting,
        }
      }
      const o = {
        name,
        dataIndex,
        label,
        key,
        titleSetting,
        hide: defaultItem === false,
        description,
        isEdit: false,
        align: 'center',
        labelAlign: 'center',
        summaryAlign: 'center',
        ...((key === 'area' || key === 'areaName') && { fixed: 'left' }),
        cellStyle: (row, col, index, width) => {
          switch (row.type) {
            case 2:
              return {
                background: '#bae7ff',
              }
            case 1:
              return {
                background: '#e6f7ff',
              }
            default:
              return {
                background: '#fff',
              }
          }
        },
        customRender:
          key === 'area' || key === 'areaName'
            ? (h, { row, col, index }) => (<div style="width:100%">
              { row[key] || row.area ? <Tooltip title={row[key] || row.area}>
                <div style="width:100%;text-align: center;overflow: hidden;text-overflow: ellipsis;white-space: nowrap">
                  <span>{row[key] || row.area}</span>
                </div>
              </Tooltip> : '--' }
            </div>
            )
            : hasDetail
              ? (h, { row, col, index }) => (
                <div
                  class={{
                    'flex': true,
                    'flex-justify-center': true,
                  }}
                  style="width:100%"
                >
                  <ContrastData data-index={key} row={row}>
                    <ToDetail data-index={key} row={row} />
                  </ContrastData>
                </div>
              )
              : (h, { row, col, index }) => (
                <div
                  class={{
                    'flex': true,
                    'flex-justify-center': true,
                  }}
                  style="width:100%"
                >
                  <ContrastData data-index={key} row={row} />
                </div>
              ),
        summaryCustomRender: key === 'area' || key === 'areaName' ? (h, { row, col, index }) => (
          <div
            class={{
              'flex': true,
              'flex-justify-center': true,
            }}
            style="width:100%"
          >
            <span>{row[key]}</span>
          </div>
        )
          : (h, { row, col, index }) => (
            <div
              class={{
                'flex': true,
                'flex-justify-center': true,
              }}
              style="width:100%"
            >
              <ContrastData data-index={key} row={row} >
                { hasDetail ? <ToDetail isTotal={true} data-index={key} row={row} /> : null }
              </ContrastData>
            </div>
          ),
        headerCustomRender: (h, { row, col, index }) => (
          <span
            class={{
              'flex': true,
              'flex-justify-center': true,
              'flex-col': true,
            }}
            style="width:100%"
          >
            <DisplayNote description={description}>
              {titleSetting}
            </DisplayNote>
            {sorter ? (
              <span class="flex flex-justify-center mt-5">
                <a
                  class="sale-sort-icon ml-5"
                  onClick={() => {
                    tableSort({
                      sortField: key,
                    })
                  }}
                >
                  <Icon
                    type="caret-up"
                    class={
                      state.sorter.sortField === key &&
                      state.sorter.sortBy === 'asc'
                        ? 'sale-sort-icon-item sale-icon-active'
                        : 'sale-sort-icon-item'
                    }
                  />
                  <Icon
                    type="caret-down"
                    class={
                      state.sorter.sortField === key &&
                      state.sorter.sortBy === 'desc'
                        ? 'sale-sort-icon-item sale-icon-active'
                        : 'sale-sort-icon-item'
                    }
                  />
                </a>
              </span>
            ) : null}
          </span>
        ),
      }
      // 默认宽度
      o.minWidth = 70
      if (children && children.length) {
        o.children = eachData(children, true)
      }
      if (sorter) {
        if (headerHeight.value < titleSetting.length * 14) {
          headerHeight.value = titleSetting.length * 7 + 35
        }
        o.sortable = true
      }
      return o
    })
  }

  const getHead = function (arr, type) {
    arr.forEach((d) => {
      if (d.children && d.children.length) {
        d.headerHeight = 42
        getHead(d.children, 'children')
      } else {
        d.headerHeight =
          type === 'children' ? headerHeight.value : headerHeight.value + 42
      }
    })
  }

  const setHead = function (arr) {
    arr.forEach((d) => {
      d._height = d.headerHeight
      d._text = ''
      if (d.children && d.children.length) {
        setHead(d.children)
      }
    })
  }

  const setColumnsConfig = function () {
    getHead(columns.value)
    setTimeout(() => {
      canvasTable.value.headerHeight = headerHeight.value + 42
      setHead(canvasTable.value.currentColumns)
      canvasTable.value.draw()
    }, 10)
  }

  // kt-canvas-table不能单独设置某个表格头部的高度,经看源码以后发现组件内部会算一个_height的私有属性来设置表格头部高度,所以这里手动来把这个改掉
  const fetchHearder = async function () {
    columns.value = eachData(cloneDeep(originColumns))
    nextTick(() => {
      setColumnsConfig(canvasTable.value)
    })
    getBussinessUnitConfigTable()
  }

  fetchHearder()

  const exportDataLoading = ref(false)
  async function toExport () {
    const url = businessUnitApi.exportList()
    const fileName = '经营单元统计'
    exportDataLoading.value = true
    const params = {
      ...state.cacheParams,
    }
    exportData(url, fileName, true, params).catch(err => {
      throw new Error(err)
    }).finally(() => {
      exportDataLoading.value = false
    })
  }

  function exportData (url, fileName = '', useTime = true, params = {}) {
    return new Promise((resolve, reject) => {
      axios({
        method: 'post',
        url: url,
        responseType: 'blob',
        timeout: 60 * 1000,
        data: params,
        headers: {
          Authorization: proxy.$store.state.token
        }
      })
        .then(res => {
          const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          const reader = new FileReader()
          reader.readAsText(blob, 'utf8')
          reader.addEventListener('loadend', () => {
            try {
              const result = JSON.parse(reader.result)
              proxy.$message.error(result.userMsg)
              reject(result.userMsg)
            } catch (e) {
              const objectUrl = URL.createObjectURL(blob)
              const link = document.createElement('a')
              link.download = `${fileName}${useTime ? moment().format('YYYYMMDDHHmmss') : ''}.xlsx`
              link.style.display = 'none'
              link.href = objectUrl
              document.body.appendChild(link)
              link.click()
              resolve()
            }
          })
        })
        .catch(e => {
          console.log(e)
          reject(e)
          this.$message.error('下载失败')
        })
    })
  }

  const toolSwitchChange = function () {
    fetchData()
  }

  return {
    raceEditFactorLoading,
    factorData,
    columns,
    toExport,
    exportDataLoading,
    canvasTableAttrs,
    cols,
    toolSwitchChange,
  }
}
