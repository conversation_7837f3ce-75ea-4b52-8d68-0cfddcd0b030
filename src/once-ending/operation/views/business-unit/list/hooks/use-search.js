import { useState } from './use-state'
import { useApi } from './use-api'
import moment from 'moment'
import { ref, watch, nextTick, onMounted } from 'vue'
import { defaultFormData } from '../constants'
import cloneDeep from 'lodash/cloneDeep'

export default function useSearch () {
  const { state, setTime } = useState()

  const reset = function () {
    state.formData = cloneDeep(defaultFormData)
    setTime()
  }

  function changeTime () {
    const times = state.formData.timeRang1
    if (times?.length) {
      const start = moment(times[0])
      const end = moment(times[1])
      const current = moment()
      if (current.isBefore(start)) { return }
      const isFuture = current.isBefore(end)
      const diff = isFuture
        ? current.diff(start, 'days')
        : end.diff(start, 'days')
      const cacheEnd = start.clone().subtract(1, 'days')
      const cacheStart = cacheEnd.clone().subtract(diff, 'days')
      const cacheTimes = [
        cacheStart.format('YYYY-MM-DD'),
        cacheEnd.format('YYYY-MM-DD'),
      ]
      state.formData.timeRang2 = cacheTimes
    }
  }

  const niFilter = ref(null)

  const ro = new ResizeObserver((entries, observer) => {
    for (const entry of entries) {
      const { height } = entry.contentRect
      state.flterBoxHeight = height
    }
  })

  onMounted(() => {
    ro.observe(niFilter.value)
  })

  return {
    reset,
    niFilter,
    changeTime
  }
}
