import moment from 'moment'
export const defaultFormData = {
  areaIds: [],
  timeRang1: [],
  timeRang2: [],
  regionOnly: undefined
}

export const regionOptions = [
  {
    label: '仅小区',
    value: 1
  },
  {
    label: '仅大区',
    value: 2
  }
]
export const compareItemsNames = [
  { key: 'areaRatio', label: '区域环比' },
  { key: 'allAreaTradeData', label: '全区交易时间数据' },
  { key: 'allAreaDiffData', label: '全区差值' },
  { key: 'allAreaRatio', label: '全区环比' },
  { key: 'ratioDiffData', label: '环比差值' }
]

export const ranges = {
  今天: [moment().startOf('day'), moment().endOf('day')],
  昨天: [moment().subtract(1, 'days').startOf('day'), moment().subtract(1, 'days').endOf('day')],
  当周: [moment().startOf('week'), moment().endOf('week')],
  上周: [moment().subtract(1, 'weeks').startOf('week'), moment().subtract(1, 'weeks').endOf('week')],
  当月: [moment().startOf('month'), moment().endOf('month')],
  上月: [moment().subtract(1, 'months').startOf('month'), moment().subtract(1, 'months').endOf('month')],
}

export const originColumns = [
  {
    name: '地区',
    key: 'areaName',
  },
  {
    name: '大件销量',
    key: 'largeCount',
  },
  {
    name: '总毛利',
    key: 'totalProfit',
  },
  {
    name: '总单毛',
    key: 'totalSingleProfit',
  },
  {
    name: '大件毛利',
    key: 'largeProfit',
  },
  {
    name: '大件单毛',
    key: 'largeAverageProfit',
  },
  {
    name: '增值毛利',
    key: 'valueAddedBusinessProfit',
  },
  {
    name: '增值单毛',
    key: 'valueAddedBusinessSingleProfit',
  },
  {
    name: '运营商预估佣金',
    key: 'operatorEstimatedCommission',
  },
  {
    name: '运营商预估产能',
    key: 'operatorPredictAbility',
  },
]
