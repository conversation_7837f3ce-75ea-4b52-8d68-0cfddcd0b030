<script lang="jsx">
  import { defineComponent, toRefs, ref, onMounted } from 'vue'
  import { useState } from '../hooks/use-state'
  import useSearch from '../hooks/use-search'
  import { NiFilter, NiFilterItem } from '@jiuji/nine-ui'
  import { DatePicker } from 'ant-design-vue'
  import CityStore from '@logistics/components/city-store'
  import { ranges, regionOptions } from '../constants'
  import AreaSelect from '../../import-data/components/area-select.vue'

  export default defineComponent({
    components: {
      NiFilter,
      NiFilterItem,
      AreaSelect,
      CityStore,
    },
    setup () {
      const { state, loading, fetchData, changeRegion } = useState()
      const {
        reset,
        niFilter,
        changeTime
      } = useSearch()

      const filter = ref(null)

      const addQuickSearchEvent = function () {
        if (filter.value) {
          filter.value.$el.querySelector('.quick-search')?.addEventListener('click', function (e) {
            if ((e.target.className || '').includes('quick-search-tag') && filter.value.isFold) {
              filter.value.toggleFold()
            }
          })
        }
      }

      onMounted(() => {
        addQuickSearchEvent()
      })

      return {
        ...toRefs(state),
        loading,
        fetchData,
        reset,
        niFilter,
        filter,
        changeRegion,
        changeTime
      }
    },
    render () {
      const {
        loading,
        fetchData,
        formData,
        reset,
        changeRegion,
        changeTime
      } = this
      return (
        <div ref="niFilter">
      <NiFilter
        ref="filter"
        form={formData}
        loading={loading}
        onFilter={() => {
          fetchData()
        }}
        onReset={reset}
        do-filter-when-reset={false}
        immediate={false}
      >
        <ni-filter-item label="地区">
          <AreaSelect
            v-model={formData.areaIds}
            allow-clear={true}
            multiple
            style="max-width: 200px"
            mode={2}
            show-search
            placeholder="请选择地区"
            class="area-selector"
            max-tag-count={1}
          />
        </ni-filter-item>
        <ni-filter-item label="查询时间">
          <DatePicker.RangePicker
            v-model={formData.timeRang1}
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            onChange={changeTime}
            allowClear={false}
            ranges={ranges}
          ></DatePicker.RangePicker>
        </ni-filter-item>
        <ni-filter-item label="对比时间">
          <DatePicker.RangePicker
            v-model={formData.timeRang2}
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            show-time
            allowClear={false}
            ranges={ranges}
          ></DatePicker.RangePicker>
        </ni-filter-item>
        <ni-filter-item>
          <div class="flex">
            { regionOptions.map(item => <a-checkbox
              checked={formData.regionOnly === item.value}
              onClick={() => changeRegion(item.value)}
              value={item.value}>
              {item.label}
            </a-checkbox>) }
          </div>
        </ni-filter-item>
      </NiFilter>
      </div>
      )
    },
  })
</script>
<style lang="scss" scoped>
:deep(.ant-select-selection__choice) {
  max-width: 45%;
}
</style>
