<script lang="jsx">
  import { defineComponent, getCurrentInstance } from 'vue'
  import { useState } from '../hooks/use-state'
  export default defineComponent({
    props: {
      dataIndex: {
        type: String,
        default: ''
      },
      row: {
        type: Object,
        default: () => ({})
      },
      isTotal: {
        type: Boolean,
        default: false
      }
    },
    setup (props) {
      const root = getCurrentInstance()
      const { $tnt } = root.proxy
      const { getBaseParams, state } = useState()

      const toDetail = function () {
        const specialKey = ['operatorEstimatedCommission', 'operatorPredictAbility']
        const query = !specialKey.includes(props.dataIndex) ? {
          item: props.dataIndex,
          ...getBaseParams(),
          areaIds: props.row.areaIds,
          isTotal: props.isTotal,
          title: '赛马业绩统计',
          isNew: true
        } : {
          timeKind: 1,
          startTime: state.formData.times.length ? state.formData.times[0] : '',
          endTime: state.formData.times.length ? state.formData.times[1] : '',
          copySearch: JSON.stringify({ startTime: state.formData.times.length ? state.formData.times[0] : '', endTime: state.formData.times.length ? state.formData.times[1] : '' }),
          areaIds: props.row.areaIds,
          isTotal: props.isTotal
        }
        if ($tnt.xtenant >= 1000) {
          delete query.gift
        } else {
          delete query.eliminateFreebie
        }
        return {
          path: !specialKey.includes(props.dataIndex) ? '/operation/statistics-report/bussiness-report/perfect-order' : '/statistics-table/OperatorStatisticDetail',
          query: query
        }
      }

      return {
        toDetail
      }
    },
    render () {
      const { dataIndex, row, toDetail } = this
      return <router-link to={toDetail()}>{row[dataIndex]}</router-link>
    }
  })
</script>
<style lang="scss" scoped>
.to-detail{
  color: #20a0ff;
  &:hover{
    text-decoration: underline;
    cursor: pointer;
  }
}
</style>
