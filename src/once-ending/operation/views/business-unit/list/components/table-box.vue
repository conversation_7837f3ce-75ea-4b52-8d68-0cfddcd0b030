<script lang="jsx">
  import { defineComponent, toRefs } from 'vue'
  import { useState } from '../hooks/use-state'
  import useTable from '../hooks/use-table'
  import { Button, Spin, Tooltip } from 'ant-design-vue'
  import CanvasTable from '~/components/kt-canvas-table'
  import TableSize from './table-size'
  import TableFullScreen from './table-full-screen'
  import TableBordered from './table-bordered'
  import TableColumns from './table-columns'

  import {
    utilCreateCols,
    initCheckedList
  } from './util/columns'

  export default defineComponent({
    components: {
      CanvasTable,
      TableSize,
      TableFullScreen,
      TableBordered,
      TableColumns
    },
    setup () {
      const { loading, state, canvasTable, hasjydy } = useState()
      const {
        handleRaceGetFactor,
        toExport,
        exportDataLoading,
        canvasTableAttrs,
        cols,
        toolSwitchChange
      } = useTable(utilCreateCols, initCheckedList)

      return {
        loading,
        ...toRefs(state),
        handleRaceGetFactor,
        toExport,
        exportDataLoading,
        canvasTableAttrs,
        cols,
        canvasTable,
        toolSwitchChange,
        hasjydy
      }
    },
    render () {
      const {
        dataSource,
        cols,
        loading,
        toExport,
        exportDataLoading,
        summary,
        canvasTableAttrs,
        hasjydy
      } = this
      return (
      <div class="bussiness-report-table mt-10">
        <div class="table-bar flex flex-align-center">
          <div class="table-bar-main flex-child-average flex flex-align-center flex-justify-end">
            <div class="action">
              <Button type="primary" loading={exportDataLoading} onClick={toExport}>
                {exportDataLoading ? '导出中' : '导出'}
              </Button>
              { hasjydy ? <router-link to="/operation/business-unit/import-data" class="ml-16">
                <Button type="primary">经营单元数据导入</Button>
              </router-link> : null }
            </div>
          </div>
          <div class="table-bar-setting">
            <Tooltip title="边框" class="btns-item">
              <TableBordered />
            </Tooltip>
            <Tooltip title="密度" class="btns-item">
              <TableSize />
            </Tooltip>
            <Tooltip
         title="列设置" class="btns-item">
        <TableColumns />
      </Tooltip>
            <Tooltip title="全屏" class="btns-item">
              <TableFullScreen />
            </Tooltip>
          </div>
        </div>
        <div style="padding:0 16px 16px 16px;background:#fff">
          <Spin spinning={loading}>
            <CanvasTable
              ref="canvasTable"
              data={dataSource}
              columns={cols}
              summary={summary}
              {...{ props: canvasTableAttrs }}
            />
          </Spin>
        </div>
      </div>
      )
    },
  })
</script>
<style lang="scss">
.bussiness-report-table {
  .sale-sort-icon {
    color: #bfbfbf;
    height: 1em;
    width: 1em;
    line-height: 0.5;
    font-size: 11px;
    margin-top: -0.6em;
    //通过传参请求接口排序的，但是后台接口没有排序，先隐藏排序功能
    display: none;
  }
  .sale-sort-icon-item {
    height: 0.5em;
  }
  .sale-icon-active {
    color: #1890ff;
  }
  .table-bar {
    padding: 8px 16px;
    background-color: #fff;
    .table-bar-main {
      padding: 0;
    }

    .table-bar-setting {
      padding: 0;
    }

    .btns-item {
      margin-left: 16px;
    }
  }

  .setting-icon {
    cursor: pointer;
  }

  .kt-canvas-table_border {
    .kt-canvas-table-slot {
      border-right: 0.5px solid #dfdfdf;
      border-bottom: 0.5px solid #dfdfdf;
    }
  }
}
</style>
<style lang="scss" scoped>
.tool {
  height: 100%;
}
.tool,
.factor {
  display: flex;
  align-items: center;
}
.nine-table {
  margin-top: 16px;
}
:deep(.big-area) {
  background: #bae7ff;
}
:deep(.small-area) {
  background: #e6f7ff;
}
</style>
