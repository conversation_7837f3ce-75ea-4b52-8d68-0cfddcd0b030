<script lang="jsx">
  import { defineComponent } from 'vue'
  import { Tooltip, Icon } from 'ant-design-vue'

  export default defineComponent({
    props: {
      description: {
        type: String,
        default: '',
      },
      editType: {
        type: String,
        default: 'table',
      }
    },
    render () {
      const { description, editType } = this
      return editType === 'table' ? (
          <span>{this.$slots.default}</span>
        ) : (
          <span>
          <Icon
            style="color:rgba(0, 0, 0, 0.85);margin-left: 8px;"
            type="question-circle"
          />
        </span>
        )
    },
  })
</script>
