<script lang="jsx">
  import { defineComponent, computed } from 'vue'
  import { Popover } from 'ant-design-vue'
  import { compareItemsNames } from '../constants'
  import { formatNum } from '@operation/views/exhibition/upper-limit/constants'

  export default defineComponent({
    props: {
      dataIndex: {
        type: String,
        default: ''
      },
      row: {
        type: Object,
        default: () => ({})
      }
    },
    setup (props) {
      const color = computed(() => {
        const { dataIndex, row } = props
        if (row[`${dataIndex}Scope`] === 0) {
          return 'green'
        } else if (row[`${dataIndex}Scope`] === 1) {
          return 'red'
        } else {
          return ''
        }
      })

      return {
        color
      }
    },
    render () {
      const { dataIndex, row, color } = this
      const textKey = ['largeCount']
      const compareItems = row[`${dataIndex}CompareItems`] || {}
      return row[dataIndex] ? <Popover>
        <div slot="content" class={[Object.keys(compareItems)?.length ? 'popover-content' : '']}>
          <span>对比时间数据：<span style={{ color }}>{ textKey.includes(dataIndex) ? row[`${dataIndex}Previous`] : formatNum(row[`${dataIndex}Previous`]) }</span></span>
        </div>
        <span style={{ color }}>{this.$slots.default ? this.$slots.default : textKey.includes(dataIndex) ? row[dataIndex] : formatNum(row[dataIndex])}</span>
      </Popover> : <span>--</span>
    }
  })
</script>
<style scoped lang="scss">
.popover-content {
  width: 350px;
  display: flex;
  flex-wrap: wrap;
  span {
    line-height: 28px;
    &:nth-child(2n) {
      width: 38%;
    }
    &:nth-child(2n - 1) {
      width: 62%;
    }
  }
}
</style>
