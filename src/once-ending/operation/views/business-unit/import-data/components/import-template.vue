<script lang="jsx">
  import { defineComponent, reactive, toRefs, getCurrentInstance } from 'vue'
  import { to } from '~/util/common'
  import exhibitionApi from '@/operation/api/exhibition'
  import uploader from '~/components/uploader'
  export default defineComponent({
    components: { uploader },
    props: {
      templateLink: {
        type: String
      },
      importFun: {
        type: Function
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const state = reactive({
        importVis: false,
        importVisFileList: [],
        importVisFiles: [],
        checkVisible: false,
        checkTotal: 0,
        checkSuccess: 0,
        checkFail: 0,
        failFileLink: ''
      })

      function downloadTemplate () {
        window.location.href = props.templateLink
      }

      const openImport = () => {
        state.importVis = true
      }
      const importVisOk = async () => {
        if (!state.importVisFileList.length) return proxy.$message.warning('请先选择文件')
        proxy.$indicator.open()
        const formData = new FormData()
        formData.append('file', state.importVisFiles[0])
        const [err, res] = await to(props.importFun(formData))
        proxy.$indicator.close()
        if (err) throw err
        const { code, userMsg } = res
        if (code === 0) {
          proxy.$message.success('数据导入成功')
          state.importVis = false
          state.importVisFileList = []
          proxy.$emit('success')
        } else if (code === 5005) {
          state.checkVisible = true
          state.checkTotal = res.data.total || 0
          state.checkSuccess = res.data.success || 0
          state.checkFail = res.data.fail || 0
          state.failFileLink = res.data.link || ''
        } else {
          proxy.$message.error(userMsg)
        }
      }
      const importVisCancel = () => {
        state.importVis = false
        state.importVisFileList = []
      }
      const importVisChange = (fileList, files) => {
        if (fileList) {
          state.importVisFileList = fileList
        } else {
          state.importVisFileList = []
        }
        if (files) {
          state.importVisFiles = files
        } else {
          state.importVisFiles = []
        }
      }
      const downloadCheckFile = () => {
        if (state.failFileLink) {
          window.location.href = state.failFileLink
          state.checkVisible = false
          setTimeout(() => {
            state.failFileLink = ''
          }, 5000)
        } else {
          proxy.$message.warning('没有可下载的文件')
        }
      }
      return {
        ...toRefs(state),
        openImport,
        importVisOk,
        importVisCancel,
        importVisChange,
        downloadCheckFile,
        downloadTemplate
      }
    },
    render () {
      const {
        openImport,
        importVisOk,
        importVisCancel,
        importVisChange,
        downloadCheckFile,
        downloadTemplate
      } = this
      const checkTitle = <span>
          <a-icon type="warning" class="red font-24"/>
          <span class="font-16 ml-10 bold">数据校验失败</span>
        </span>
      return <div>
        { this.$slots.default || <a-button type="primary" icon="import" onClick={() => openImport()}>导入</a-button> }
        <a-modal
          title="导入"
          v-model={this.importVis}
          onOk={importVisOk}
          onCancel={importVisCancel}
        >
          <div class="flex flex-align-center">
            <div class="flex-child-noshrink flex flex-align-center">
              <span class="red mt-5 mr-5">*</span>
              <span>文件上传：</span>
            </div>
            <uploader buttonName={['选择文件']}
                      multiple={false}
                      fileList={this.importVisFileList}
                      onChange={(fileList, files) => importVisChange(fileList, files)}
                      accept=".xls,.xlsx"
                      showUploadAPP={false}
                      editFileName={false}
                      showUpload={!this.importVisFileList.length}/>
          </div>
          <p style="font-size: 12px;color: #606266;margin-top: 7px;">
            只支持.xls和.xlsx格式文件，文件小于1M且不超过1000行 <span class="blue pointer" onClick={() => downloadTemplate()}>下载模版</span>
          </p>
        </a-modal>
        <a-modal
          v-model={this.checkVisible}
          maskClosable={false}
          width={600}
          destroyOnClose
          title={checkTitle}
          footer={null}>
          <div class="mb-10">
            共{this.checkTotal || 0}条数据，其中
            <span class="green">{this.checkSuccess || 0}</span>条已校验成功，
            <span class="red">{this.checkFail || 0}</span>条校验失败
          </div>
          <div class="mt-20 flex flex-col flex-justify-center">
            <div class="mb-10">你可以下载校验过的数据表(错误原因在最后一列)，修改后再次导入，也可以在原表格修改后重新导入</div>
            <div class="flex flex-align-center flex-justify-center">
              <a-button style="width: 200px" type="primary" onClick={() => downloadCheckFile()}>下载校验过的数据表</a-button>
            </div>
          </div>
        </a-modal>
      </div>
    }
  })
</script>

<style scoped lang="scss">

</style>
