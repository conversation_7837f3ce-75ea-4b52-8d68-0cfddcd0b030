<script lang="jsx">
  import { defineComponent, getCurrentInstance, toRefs } from 'vue'
  import { NiFilter, NiFilterItem, NiAreaSelect, NiCategory } from '@jiuji/nine-ui'
  import { useState } from '../hooks/useState'
  import { timeTypeOptions } from '../constants'
  import AreaSelect from './area-select.vue'

  export default defineComponent({
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect,
      NiCategory,
      AreaSelect
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const {
        state,
        getData,
      } = useState()

      return {
        ...toRefs(state),
        getData
      }
    },
    render () {
      const {
        searchForm,
        loading,
        getData,
      } = this
      return (
        <ni-filter
          immediate={false}
          form={searchForm}
          onFilter={() => {
            getData('search')
          }}
          loading={loading}
        >
          <ni-filter-item label="地区">
            <AreaSelect
              maxTagCount={1}
              allowClear
              multiple={true}
              style="max-width: 230px"
              v-model={searchForm.areaIds}
              placeholder="请选择地区"/>
          </ni-filter-item>
          <ni-filter-item>
            <a-input-group compact>
              <a-select v-model={searchForm.searchTimeType} options={timeTypeOptions} style="width: 100px"/>
              <a-range-picker
                style="width: calc(100% - 100px)"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model={searchForm.timeRange}/>
            </a-input-group>
          </ni-filter-item>
        </ni-filter>
      )
    }
  })
</script>

<style lang="scss" scoped>
:deep(.ant-select-selection__choice) {
  max-width: 45%;
}
</style>
