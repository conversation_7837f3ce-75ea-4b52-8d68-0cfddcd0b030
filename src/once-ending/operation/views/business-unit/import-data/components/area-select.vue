<script lang="jsx">
  import { defineComponent, watch, ref, getCurrentInstance } from 'vue'
  import businessUnitApi from '@operation/api/business-unit'
  import { TreeSelect } from 'ant-design-vue'

  export default defineComponent({
    props: {
      value: {
        type: [Array, String]
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const valueLocal = ref(undefined)
      watch(() => props.value, (val) => {
        valueLocal.value = val
      }, { immediate: true, deep: true })
      const treeData = ref([])

      async function getAreaData () {
        const key = 'businessUnitAreaScoped'
        const val = sessionStorage.getItem(key)
        if (val) {
          treeData.value = JSON.parse(val)
        } else {
          const params = {
            author: false,
            filterEmptyArea: true,
            mode: 2,
            ranks: null,
            showDw: false,
            strategy: 1,
            taxModel: false
          }
          const res = await businessUnitApi.getArea(params).catch(err => {
            proxy.$message.error(err.message)
          })
          if (res) {
            sessionStorage.setItem(key, JSON.stringify(res))
            treeData.value = res
          }
        }
      }
      getAreaData()

      const filterTreeNodes = (inputValue, treeNode) => {
        return treeNode.props?.title?.toUpperCase()?.includes(inputValue?.toUpperCase()) || treeNode.data?.props?.title?.toUpperCase()?.includes(inputValue?.toUpperCase())
      }

      function change (value, label) {
        proxy.$emit('input', value)
        proxy.$emit('change', value, label)
      }
      return {
        treeData,
        valueLocal,
        filterTreeNodes,
        change
      }
    },
    render () {
      const {
        treeData,
        filterTreeNodes,
        change
      } = this
      return <div class="area-select">
        <a-tree-select
          { ...{ props: { ...this.$attrs } }}
          treeData={treeData}
          onChange={change}
          getPopupContainer={(triggerNode) => triggerNode.parentNode}
          dropdownStyle={{ maxHeight: '400px', overflow: 'auto' }}
          filterTreeNode={(inputValue, treeNode) => this.filterTreeNodes(inputValue, treeNode)}
          v-model={this.valueLocal}
          placeholder="请选择地区"
          allowClear
          treeCheckable
          showCheckedStrategy={TreeSelect.SHOW_CHILD}
          style="width: 100%"/>
      </div>
    }
  })
</script>

<style scoped lang="scss">
.area-select {
  position: relative;
}
</style>
