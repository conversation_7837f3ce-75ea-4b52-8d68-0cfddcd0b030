<script lang="jsx">
  import { defineComponent, toRefs } from 'vue'
  import { NiTable } from '@jiuji/nine-ui'
  import { useState } from '../hooks/useState'
  import { originColumns } from '../constants'
  import { formatNum } from '@operation/views/exhibition/upper-limit/constants'
  import ImportTemplate from './import-template.vue'
  export default defineComponent({
    name: 'table-box',
    components: {
      NiTable,
      ImportTemplate
    },
    setup () {
      const {
        state,
        getData,
        handleTableChange,
        toExport
      } = useState()
      return {
        ...toRefs(state),
        getData,
        handleTableChange,
        toExport
      }
    },
    data () {
      return {
        customRenderMap: new Map([
          [
            'number',
            (text) => <span>{ text || text === 0 ? formatNum(text) : '--' }</span>
          ],
          [
            'text',
            (text) => <span>{ text || text === 0 ? text : '--' }</span>
          ],
        ])
      }
    },
    computed: {
      columns () {
        return originColumns.map(item => {
          const cacheItem = { ...item }
          cacheItem.align = 'center'
          cacheItem.customRender = this.customRenderMap.get(item.showKey) || this.customRenderMap.get(item.dataIndex) || this.customRenderMap.get('text')
          return cacheItem
        })
      }
    },
    render (createElement, context) {
      const {
        loading,
        dataSource,
        pagination,
        columns,
        handleTableChange,
        businessUnitApi,
        getData
      } = this
      return <div>
        <ni-table
          rowKey={(r, i) => i}
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={pagination}
          bordered
          footerTotalNum={1}
          align={'center'}
          onChange={handleTableChange}>
          <div slot="action">
            <ImportTemplate
              onSuccess={() => getData('search')}
              importFun={businessUnitApi.toUpload}
              templateLink="https://img2.ch999img.com/newstatic/26293/1575e8be398335bd.xlsx"
            />
          </div>
        </ni-table>
      </div>
    }
  })
</script>

<style scoped lang="scss">
.link {
  color: #1890ff;
  cursor: pointer;
  font-weight: 400;
}

.center {
  text-align: center;
}

.left {
  text-align: left;
}

.mr-26 {
  margin-right: 26px;
}

.fw-600 {
  font-weight: 600;
}
</style>
