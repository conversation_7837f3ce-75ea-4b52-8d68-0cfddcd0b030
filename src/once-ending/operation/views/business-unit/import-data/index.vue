<script lang="jsx">
  import { defineComponent } from 'vue'
  import { NiListPage } from '@jiuji/nine-ui'
  import FilterBox from './components/filter-box'
  import TableBox from './components/table-box'
  import { createState } from './hooks/useState'
  export default defineComponent({
    components: { NiListPage, FilterBox, TableBox },
    setup () {
      createState()
    },
    render (h) {
      // const {} = this
      return <page>
        <div slot="extra">
          <span>（提示：只能导入近3天的数据，超过3天的数据不可以导入）</span>
        </div>
        <ni-list-page pushFilterToLocation={false}>
          <filter-box/>
          <table-box/>
        </ni-list-page>
      </page>
    }
  })

</script>

<style scoped lang="scss">
:deep(.ant-page-header-heading) {
  display: flex;
  align-items: center;
}
</style>
