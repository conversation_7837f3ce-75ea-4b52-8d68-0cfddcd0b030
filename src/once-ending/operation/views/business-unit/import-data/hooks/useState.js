import { reactive, provide, inject, ref, getCurrentInstance } from 'vue'
import { cloneDeep } from 'lodash'
import businessUnitApi from '@operation/api/business-unit'
import axios from 'axios'
import moment from 'moment/moment'
import { originSearchForm } from '../constants'

const key = Symbol('key')

export function useState () {
  return inject(key)
}

export function createState () {
  const { proxy } = getCurrentInstance()
  const state = reactive({
    searchForm: cloneDeep(originSearchForm),
    loading: false,
    dataSource: [],
    pagination: {
      pageSize: 10,
      current: 1,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      pageSizeOptions: ['10', '20', '30', '50']
    },
    exportLoading: false,
    cacheParams: {},
    businessUnitApi
  })

  async function getData (type) {
    type === 'search' && (state.pagination.current = 1)
    const { searchForm: { timeRange, ...other }, pagination: { current, pageSize } } = state
    const params = {
      ...other,
      current,
      size: pageSize,
      startTime: timeRange?.length ? timeRange[0] + ' 00:00:00' : undefined,
      endTime: timeRange?.length ? timeRange[1] + ' 23:59:59' : undefined
    }
    state.cacheParams = params
    state.loading = true
    const res = await businessUnitApi.getImportList(params).catch(err => {
      proxy.$message.error(err.message)
    })
    state.loading = false
    if (res) {
      const { records, total } = res
      state.dataSource = records
      state.pagination.total = total
    }
  }
  getData('search')

  async function toExport () {
    const url = businessUnitApi.exportList()
    const fileName = '运营执行管控'
    state.exportLoading = true
    const params = {
      ...state.cacheParams,
    }
    exportData(url, fileName, true, params).catch(err => {
      throw new Error(err)
    }).finally(() => {
      state.exportLoading = false
    })
  }

  function exportData (url, fileName = '', useTime = true, params = {}) {
    return new Promise((resolve, reject) => {
      axios({
        method: 'post',
        url: url,
        responseType: 'blob',
        timeout: 60 * 1000,
        data: params,
        headers: {
          Authorization: proxy.$store.state.token
        }
      })
        .then(res => {
          const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          const reader = new FileReader()
          reader.readAsText(blob, 'utf8')
          reader.addEventListener('loadend', () => {
            try {
              const result = JSON.parse(reader.result)
              proxy.$message.error(result.userMsg)
              reject(result.userMsg)
            } catch (e) {
              const objectUrl = URL.createObjectURL(blob)
              const link = document.createElement('a')
              link.download = `${fileName}${useTime ? moment().format('YYYYMMDDHHmmss') : ''}.xlsx`
              link.style.display = 'none'
              link.href = objectUrl
              document.body.appendChild(link)
              link.click()
              resolve()
            }
          })
        })
        .catch(e => {
          console.log(e)
          reject(e)
          this.$message.error('下载失败')
        })
    })
  }

  async function handleTableChange (paginationObj) {
    Object.assign(state.pagination, paginationObj)
    getData()
  }

  const o = {
    state,
    getData,
    handleTableChange,
    toExport
  }
  provide(key, o)
  return o
}
