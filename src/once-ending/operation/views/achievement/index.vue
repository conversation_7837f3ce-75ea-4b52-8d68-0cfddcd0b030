<script lang="jsx">
  import { defineComponent, ref, nextTick, getCurrentInstance } from 'vue'
  import { Button } from 'ant-design-vue'
  import PinCrown from './components/pin-crown'
  import Ranking from './components/ranking'
  import RankingPic from './components/ranking-pic'
  import RuleConfigModal from './components/rule-config-modal'
  import Log from './components/log'
  import { QUERY_SETTINGS, FIELD_NOTE_GET_LIST_BY_TYPE } from '@operation/store/modules/achievement/action-types'

  export default defineComponent({
    components: {
      PinCrown,
      Ranking,
      RankingPic,
      RuleConfigModal,
      Log
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      // 考核毛利
      const examine = ref({
        check: false,
        showButton: false
      })

      // 排名项目配置
      const rankingData = ref([])

      const areaData = ref([])

      const termOptions = ref([])

      const rankingForm = ref({
        areaType: undefined,
        departs: [],
        mainRoles: []
      })

      const otherSettingId = ref(null)

      const areaTypeOptions = ref([])

      const pinCrownForm = ref({
        championItem: undefined
      })

      const showRuleConfigModal = ref(false)

      const showDrawer = ref(false)

      const tip = ref({
        itemSetting: '',
        examine: '',
        rankRule: '',
        rankContent: ''
      })

      const destroyedModal = ref({
        ruleConfig: true,
        log: true
      })
      const handleDestroyedModal = function () {
        destroyedModal.value[val] = false
        nextTick(() => {
          destroyedModal.value[val] = true
        })
      }

      const ruleConfig = function () {
        showRuleConfigModal.value = true
      }

      const querySettings = async function () {
        const res = await proxy.$store.dispatch(
          `operation/achievement/${QUERY_SETTINGS}`
        )
        if (res) {
          const { data: { otherSettingId: id, prItemSetting: { examine: examineData, items, areaType, areaTypeEnums, departs, mainRoles }, prSalesChampionSetting: { areaSettings, championItem } } } = res
          // 配置id
          otherSettingId.value = id
          // 考核毛利配置
          examine.value = examineData
          items.forEach(d => {
            d.rankContent = d.rankContents.find(k => k.select) ? d.rankContents.find(k => k.select).rankContentCode : undefined
            d.rankContents.map(k => {
              k.label = k.rankContent
              k.value = k.rankContentCode
            })
          })
          // 排名项目配置
          rankingData.value = items.sort((a, b) => a.position - b.position)
          // 门店类别配置
          rankingForm.value.areaType = areaType
          areaTypeOptions.value = areaTypeEnums.map(d => {
            return {
              label: d.name,
              value: d.value
            }
          })
          // 地区配置
          rankingForm.value.departs = departs
          // 主要角色
          rankingForm.value.mainRoles = mainRoles
          // 销冠业务项枚举值
          termOptions.value = items.map(k => {
            return {
              value: k.item,
              label: k.itemStr
            }
          })
          // 销冠项目配置
          areaData.value = areaSettings
          // 销冠表单
          pinCrownForm.value.championItem = championItem
        }
      }

      querySettings()

      const fieldNoteGetListByType = async function () {
        const params = {
          type: 101
        }
        const res = await proxy.$store.dispatch(
          `operation/achievement/${FIELD_NOTE_GET_LIST_BY_TYPE}`, params
        )
        if (res) {
          const { data } = res
          tip.value = data
        }
      }

      fieldNoteGetListByType()

      return {
        showRuleConfigModal,
        destroyedModal,
        handleDestroyedModal,
        ruleConfig,
        areaTypeOptions,
        rankingForm,
        querySettings,
        rankingData,
        pinCrownForm,
        areaData,
        termOptions,
        otherSettingId,
        tip,
        fieldNoteGetListByType,
        showDrawer
      }
    },
    render () {
      const { ruleConfig, tip, showDrawer, fieldNoteGetListByType, querySettings, rankingForm, otherSettingId, areaTypeOptions, showRuleConfigModal, destroyedModal, handleDestroyedModal, examine, rankingData, termOptions, areaData, pinCrownForm } = this
      return (
      <page class="achievement">
        <div slot="extra">
          <div></div>
          <Button
            type="primary"
            onClick={() => {
              ruleConfig()
            }}
            style="margin-right:16px"
          >
            规则文案配置
          </Button>
          <Button
            type="primary"
            onClick={() => {
              this.showDrawer = true
            }}
          >
            操作日志
          </Button>
        </div>
        <div class="main">
        <Ranking examine={examine} tip={tip} ranking-data={rankingData} onUpdateRankingData={(val) => { this.rankingData = val }} area-type-options={areaTypeOptions} ranking-form={rankingForm} other-setting-id={otherSettingId} onQuerySettings={querySettings}></Ranking>
        <PinCrown term-options={termOptions} area-data={areaData} pin-crown-form={pinCrownForm} other-setting-id={otherSettingId} onQuerySettings={querySettings} style="margin-top:24px"></PinCrown>
        <RankingPic style="margin-top:24px"></RankingPic>
        </div>
        {
          destroyedModal.ruleConfig ? <RuleConfigModal onFieldNoteGetListByType={fieldNoteGetListByType} value={tip.rankRule} onDestroyedModal={handleDestroyedModal} show-rule-config-modal={showRuleConfigModal} {...{ on: { 'update:showRuleConfigModal': val => { this.showRuleConfigModal = val } } }}></RuleConfigModal> : null
        }
        {
          destroyedModal.log ? <Log show-drawer={showDrawer} {...{ on: { 'update:showDrawer': val => { this.showDrawer = val } } }}></Log> : null
        }
      </page>
      )
    }
  })
</script>
<style lang="scss" scoped>
.achievement {
  padding-bottom: 20px;
  .main {
    margin-top: 16px;
  }
  :deep(.ant-select-selection__choice){
        max-width: 50% !important;
  }
}
</style>
