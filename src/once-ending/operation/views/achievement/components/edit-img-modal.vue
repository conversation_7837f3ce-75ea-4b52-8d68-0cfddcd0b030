<script lang="jsx">
  import { defineComponent, computed } from 'vue'
  import { Modal, Form } from 'ant-design-vue'
  import UploaderImg from '~/components/upload/uploaderImg-byNine'

  export default defineComponent({
    props: {
      editRecord: {
        type: Object,
        default: () => ({})
      },
      showEditImgModal: {
        type: Boolean,
        default: false
      }
    },
    components: {
      UploaderImg
    },
    setup (props, { emit }) {
      const visible = computed({
        get: () => props.showEditImgModal,
        set: val => emit('update:showEditImgModal', val)
      })

      const afterClose = function () {
        emit('destroyedModal', 'editImg')
      }

      const getFiles = function (data, type) {
        if (data?.length) {
          const index = data[0].fid.indexOf('.')
          const fid = data[0].fid.substring(0, index)
          props.editRecord['fid' + type] = fid
          props.editRecord['fid' + type + 'Img'] = data[0].filePath
        }
      }

      const closeModal = function () {
        visible.value = false
      }

      return {
        visible,
        afterClose,
        getFiles,
        closeModal
      }
    },
    render () {
      const { editRecord, visible, closeModal, afterClose, getFiles } = this
      return (
      <Modal
        width={530}
        zIndex={10}
        visible={visible}
        onOk={closeModal}
        title={`${editRecord.name}图片`}
        onCancel={closeModal}
        after-close={afterClose}
      >
        <div class="flex flex-wrap">
          <Form.Item label="上传灰白卡片">
            <UploaderImg
              files={editRecord.arrFid1}
              wh="630*292"
              collection="oa-operate"
              onChangeImg={fileList => {
                getFiles(fileList, 1)
              }}
            />
          </Form.Item>
          <a-form-item label="上传彩卡片">
            <uploader-img
              files={editRecord.arrFid2}
              wh="630*292"
              collection="oa-operate"
              onChangeImg={fileList => {
                getFiles(fileList, 2)
              }}
            />
          </a-form-item>
          <a-form-item label="上传段位小图标">
            <uploader-img
              files={editRecord.arrFid3}
              wh="240*240"
              collection="oa-operate"
              onChangeImg={fileList => {
                getFiles(fileList, 3)
              }}
            />
          </a-form-item>
          <a-form-item label="上传段位大图标">
            <uploader-img
              files={editRecord.arrFid4}
              wh="234*234"
              collection="oa-operate"
              onChangeImg={fileList => {
                getFiles(fileList, 4)
              }}
            />
          </a-form-item>
        </div>
      </Modal>
      )
    }
  })
</script>
