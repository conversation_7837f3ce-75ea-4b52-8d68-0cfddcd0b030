<script lang="jsx">
  import { defineComponent, ref, nextTick, getCurrentInstance } from 'vue'
  import { Input, Table, Button, Icon, message } from 'ant-design-vue'
  import { NiImg } from '@jiuji/nine-ui'
  import EditImgModal from './edit-img-modal.vue'
  import EditRankModal from './edit-rank-modal.vue'
  import { gradingColumns as columns } from '../constants'
  import { PERFORMANCE_RANKING_STAGE_QUERY, PERFORMANCE_RANKING_STAGE_SAVE_OR_UPDATE, PERFORMANCE_RANKING_STAGE_REMOVE } from '@operation/store/modules/achievement/action-types'

  export default defineComponent({
    components: {
      NiImg,
      EditImgModal,
      EditRankModal
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const strongestRank = ref({
        name: undefined,
        fid1Img: '',
        fid2Img: '',
        fid3Img: '',
        fid4Img: '',
        color: '',
        fid1: '',
        orders: 655601
      })

      const showEditImgModal = ref(false)

      const showEditRankModal = ref(false)

      const editRecord = ref({})

      const destroyedModal = ref({
        editImg: true,
        editRank: true
      })

      const editImg = async function (record) {
        editRecord.value = record
        for (let i = 1; i <= 4; i++) {
          editRecord.value['arrFid' + i] = record['fid' + i]
            ? [{ url: record['fid' + i + 'Img'], name: `fid${i}.jpg`, uid: 1 }]
            : []
        }
        showEditImgModal.value = true
      }

      const dataSource = ref([])

      const handleDestroyedModal = function (val) {
        destroyedModal.value[val] = false
        nextTick(() => {
          destroyedModal.value[val] = true
        })
      }

      const performanceRankingStageQuery = async function () {
        const res = await proxy.$store.dispatch(`operation/achievement/${PERFORMANCE_RANKING_STAGE_QUERY}`)
        if (res) {
          const { data } = res
          const strongestRankIndex = data.findIndex(d => d.strongestRank === 1)
          if (strongestRankIndex > -1) strongestRank.value = data.splice(strongestRankIndex, 1)[0]
          data.forEach(d => {
            if (d.areaExps) d.areaExps = d.areaExps.reverse()
          })
          dataSource.value = data
        }
      }

      performanceRankingStageQuery()

      const editGrading = async function (record) {
        editRecord.value = record
        showEditRankModal.value = true
      }

      const handleDelGrading = async function (record, index) {
        if (!record.id) {
          dataSource.value.splice(index, 1)
        } else {
          proxy.$confirm({
            title: '确认删除吗？',
            onOk () {
              delGrading(record, index)
            },
          })
        }
      }

      const delGrading = async function (record) {
        const params = {
          id: record.id
        }
        const res = await proxy.$store.dispatch(`operation/achievement/${PERFORMANCE_RANKING_STAGE_REMOVE}`, params)
        if (res) {
          message.success('删除成功')
          performanceRankingStageQuery()
        }
      }

      const checkForm = function (stages) {
        let flag = false
        for (let i = 0; i < stages.length; i++) {
          const item = stages[i]
          const { strongestRank, orders, name, fid1, fid2, fid3, fid4, color } = item
          if (orders === '') {
            if (strongestRank) {
              message.error('请填写最强段位排序值')
            } else {
              message.error(`请填写第${i}行段位排序值`)
            }
            flag = true
            break
          }
          if (!name) {
            if (strongestRank) {
              message.error('请填写最强段位名称')
            } else {
              message.error(`请填写第${i}行段位名称`)
            }
            flag = true
            break
          }
          if (!fid1 || !fid2 || !fid3 || !fid4) {
            if (strongestRank) {
              message.error('最强段位图片不完整，请先上传')
            } else {
              message.error(`第${i}行段位图片不完整，请先上传`)
            }
            flag = true
            break
          }
          if (!/^#[A-Fa-f0-9]{6}$/.test(color)) {
            if (strongestRank) {
              message.error('请填写正确的最强段位颜色(如 #ffffff )')
            } else {
              message.error(`第${i}行色值错误,请填写正确的段位颜色(如 #ffffff )`)
            }
            flag = true
            break
          }
        }
        return flag
      }

      const saveGrading = async function () {
        const stages = dataSource.value.map(d => {
          const { id, name, orders, fid1, fid2, fid3, fid4, color } = d
          const o = {
            name,
            orders,
            fid1,
            fid2,
            fid3,
            fid4,
            color,
            strongestRank: false
          }
          if (id) o.id = id
          return o
        })
        const { id, name, orders, fid1, fid2, fid3, fid4, color } = strongestRank.value
        const o = {
          name,
          orders,
          fid1,
          fid2,
          fid3,
          fid4,
          color,
          strongestRank: true
        }
        if (o) o.id = id
        stages.push(o)
        if (checkForm(stages)) return
        const params = {
          stages
        }
        const res = await proxy.$store.dispatch(`operation/achievement/${PERFORMANCE_RANKING_STAGE_SAVE_OR_UPDATE}`, params)
        if (res) {
          message.success('保存成功')
          performanceRankingStageQuery()
        }
      }

      const addGrading = function () {
        dataSource.value.push({
          name: undefined,
          color: '',
          fid1: '',
          fid1Img: '',
          fid2: '',
          fid2Img: '',
          fid3: '',
          fid3Img: '',
          fid4: '',
          fid4Img: '',
          orders: 0,
          strongestRank: 0
        })
      }

      return {
        saveGrading,
        strongestRank,
        dataSource,
        editImg,
        destroyedModal,
        handleDestroyedModal,
        showEditImgModal,
        showEditRankModal,
        editGrading,
        handleDelGrading,
        addGrading,
        editRecord,
        performanceRankingStageQuery
      }
    },
    render () {
      const {
        saveGrading,
        dataSource,
        strongestRank,
        editImg,
        showEditImgModal,
        handleDestroyedModal,
        destroyedModal,
        showEditRankModal,
        editGrading,
        addGrading,
        editRecord,
        performanceRankingStageQuery,
        handleDelGrading
      } = this
      return (
      <div class="ranking-pic">
        <div class="form" style="margin-bottom: 20px;">
          <div>
            <span>最强段位配置：</span>
            <Input
              style="width: 200px"
              class="text-center"
              placeholder="段位名称"
              v-model={strongestRank.name}
              allow-clear
            ></Input>
          </div>
          <div style="margin-left:16px">
            <div class="flex flex-align-center flex-justify-center">
              {strongestRank.fid1 ? (
                <div
                  onClick={() => {
                    editImg(strongestRank)
                  }}
                >
                  <NiImg class="img mr-5" src={strongestRank.fid1Img}></NiImg>
                  <NiImg class="img mr-5" src={strongestRank.fid2Img}></NiImg>
                  <NiImg class="img mr-5" src={strongestRank.fid3Img}></NiImg>
                  <NiImg class="img mr-5" src={strongestRank.fid4Img}></NiImg>
                </div>
              ) : (
                <Button
                  onClick={() => {
                    editImg(strongestRank)
                  }}
                >
                  上传
                </Button>
              )}
            </div>
          </div>
          <div class="ml-20 flex flex-align-center">
            <div
              class="circle mr-5"
              style={{ backgroundColor: strongestRank.color }}
            ></div>
            <Input
              v-model={strongestRank.color}
              style="width: 200px"
              class="text-center"
              placeholder="经验条颜色"
            ></Input>
          </div>
        </div>
        <div class="table-box">
          <Table
            columns={columns}
            rowKey={(record, index) => index}
            dataSource={dataSource}
            pagination={false}
            scopedSlots={{
              orders: (text, record) => (
                <Input
                  v-model={record.orders}
                  style="width: 100px"
                  class="text-center"
                ></Input>
              ),
              name: (text, record) => (
                <Input
                  v-model={record.name}
                  style="width: 200px"
                  class="text-center"
                ></Input>
              ),
              img: (text, record, index) => (
                <div class="flex flex-align-center flex-justify-center">
                  {record.fid1 ? (
                    <div
                      onClick={() => {
                        editImg(record, index)
                      }}
                    >
                      <NiImg class="img mr-5" src={record.fid1Img}></NiImg>
                      <NiImg class="img mr-5" src={record.fid2Img}></NiImg>
                      <NiImg class="img mr-5" src={record.fid3Img}></NiImg>
                      <NiImg class="img mr-5" src={record.fid4Img}></NiImg>
                    </div>
                  ) : (
                    <Button
                      onClick={() => {
                        editImg(record)
                      }}
                    >
                    {record.fid1}
                      上传
                    </Button>
                  )}
                </div>
              ),
              color: (text, record) => (
                <div class="flex flex-center">
                  <div
                    class="circle mr-5"
                    style={{ backgroundColor: record.color }}
                  ></div>
                  <Input
                    v-model={record.color}
                    style="width: 150px"
                    placeholder="16进制色值(#ffffff)"
                    class="text-center"
                  ></Input>
                </div>
              ),
              action: (text, record, index) => (
                <div>
                  {record.id ? (
                    <Button
                      type="primary"
                      size="small"
                      style="margin-right: 5px;"
                      onClick={() => {
                        editGrading(record)
                      }}
                    >
                      编辑
                    </Button>
                  ) : null}
                  <Button
                    type="danger"
                    size="small"
                    onClick={() => {
                      handleDelGrading(record, index)
                    }}
                  >
                    删除
                  </Button>
                </div>
              )
            }}
          ></Table>
          <div class="flex flex-center mt-20">
            <Button onClick={addGrading} class="full-width">
              <Icon type="plus" />
              添加
            </Button>
          </div>
          <div class="mt-20" style="text-align: right;">
            <Button type="primary" onClick={saveGrading}>
              保存
            </Button>
          </div>
        </div>
        {destroyedModal.editImg ? (
          <EditImgModal
            onDestroyedModal={handleDestroyedModal}
            show-edit-img-modal={showEditImgModal}
            edit-record={editRecord}
            {...{
              on: {
                'update:showEditImgModal': val => {
                  this.showEditImgModal = val
                }
              }
            }}
          ></EditImgModal>
        ) : null}
        {destroyedModal.editRank ? (
          <EditRankModal
            onDestroyedModal={handleDestroyedModal}
            show-edit-rank-modal={showEditRankModal}
            edit-record={editRecord}
            onPerformanceRankingStageQuery={performanceRankingStageQuery}
            {...{
              on: {
                'update:showEditRankModal': val => {
                  this.showEditRankModal = val
                }
              }
            }}
          ></EditRankModal>
        ) : null}
      </div>
      )
    }
  })
</script>
<style lang="scss" scoped>
.mr-5{
  margin-right: 5px;
}
.ranking-pic {
  background: #fff;
  padding: 24px 16px;
  .form {
    display: flex;
    align-items: center;
  }
  .main,
  .footer {
    padding: 16px;
  }
  .require {
    :deep(label) {
        &::before {
          display: inline-block;
          margin-right: 4px;
          color: #f5222d;
          font-size: 14px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: "*";
        }
    }
  }
  .img {
    width: 80px;
  }
  .circle {
    width: 20px;
    height: 20px;
    border-radius: 100%;
  }
}
</style>
