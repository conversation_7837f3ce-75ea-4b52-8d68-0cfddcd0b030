<script lang="jsx">
  import { defineComponent, getCurrentInstance } from 'vue'
  import HeadTitle from './head-title.vue'
  import { Form, InputNumber, Select, Table, Button, message } from 'ant-design-vue'
  import { rankingColumns as columns } from '../constants'
  import { CHAMPION_SAVE_OR_UPDATE } from '@operation/store/modules/achievement/action-types'

  export default defineComponent({
    props: {
      termOptions: {
        type: Array,
        default: () => ([])
      },
      areaData: {
        type: Array,
        default: () => ([])
      },
      pinCrownForm: {
        type: Object,
        default: () => ({})
      },
      otherSettingId: {
        type: [String, Number],
        default: null
      }
    },
    components: {
      HeadTitle
    },
    setup (props, { emit }) {
      const { proxy } = getCurrentInstance()
      const save = async function () {
        const { championItem } = props.pinCrownForm
        if (!championItem) {
          return message.error('请选择销冠业务项')
        }
        const areaSettings = props.areaData.map(d => {
          const { recordId, area, rankLimit } = d
          return {
            recordId,
            area,
            rankLimit
          }
        })
        const params = {
          otherSettingId: props.otherSettingId,
          championItem,
          areaSettings
        }
        const res = await proxy.$store.dispatch(
          `operation/achievement/${CHAMPION_SAVE_OR_UPDATE}`, params
        )
        if (res) {
          message.success('保存成功')
          emit('querySettings')
        }
      }

      return {
        save
      }
    },
    render () {
      const { termOptions, areaData, save, pinCrownForm } = this
      return (
      <div class="ranking">
        <div class="head">
          <HeadTitle title="销冠项目配置"></HeadTitle>
        </div>
        <div class="main">
          <Form
            layout="inline"
            label-col={{ span: 6 }}
            wrapper-col={{ span: 12 }}
          >
            <Form.Item label="销冠业务项" class="require">
              <Select
                style="width:320px"
                v-model={pinCrownForm.championItem}
                options={termOptions}
                allow-clear
                placeholder="请选择"
              ></Select>
            </Form.Item>
          </Form>
          <div class="table-box">
            <div class="require" style="margin-left: 10px;margin-top: 16px;margin-bottom: 10px;">
              <label>榜单条件</label>
            </div>
            <Table
              columns={columns}
              data-source={areaData}
              row-key="area"
              pagination={false}
              scopedSlots={{
                rankLimit: (text, row) => (
                  <div>
                    <span style="margin-right:8px">前</span>
                    <InputNumber
                      style="width:180px"
                      v-model={row.rankLimit}
                      precision={0}
                      placeholder="请输入"
                    ></InputNumber>
                    <span style="margin-left:8px">名</span>
                  </div>
                )
              }}
            ></Table>
          </div>
        </div>
        <div class="footer">
          <Button type="primary" onClick={save}>
            保存
          </Button>
        </div>
      </div>
      )
    }
  })
</script>
<style lang="scss" scoped>
.ranking {
  background: #fff;
  .head {
    padding: 24px 16px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eff1f7;
  }
  .main,
  .footer {
    padding: 16px;
  }
  .footer{
    text-align: right;
  }
  .require {
    :deep(label) {
        &::before {
          display: inline-block;
          margin-right: 4px;
          color: #f5222d;
          font-size: 14px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: "*";
        }
    }
  }
}
</style>
