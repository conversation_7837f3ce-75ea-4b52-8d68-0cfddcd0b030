<script lang="jsx">
  import { defineComponent, ref, computed, watch, getCurrentInstance } from 'vue'
  import { Drawer, Steps, Popover } from 'ant-design-vue'
  import {
    PERFORMANCE_RANKING_LOGS_QUERY,
    USER_INFO
  } from '@operation/store/modules/achievement/action-types'
  export default defineComponent({
    props: {
      showDrawer: {
        type: Boolean,
        default: false
      }
    },
    setup (props, { emit }) {
      const { proxy } = getCurrentInstance()
      const visible = computed({
        set: val => {
          emit('update:showDrawer', val)
        },
        get: () => props.showDrawer
      })
      const operationLog = ref([])
      const flagDisplay = ref(false)
      const current = ref(1)

      watch(() => visible.value, val => {
        val && getOperationLog('init')
      })
      const getOperationLog = async function (type) {
        const params = {
          current: current.value,
          size: 10
        }
        const res = await proxy.$store.dispatch(
          `operation/achievement/${PERFORMANCE_RANKING_LOGS_QUERY}`, params)
        if (res) {
          if (type) operationLog.value = []
          const { data: { records, total } } = res
          operationLog.value = operationLog.value.concat(records)
          flagDisplay.value = operationLog.value.length < total
        }
      }
      const mutShowDrawer = function () {
        current.value++
        getOperationLog()
      }

      const afterVisibleChange = function (visible) {
        if (!visible) {
          emit('destroyedModal', 'log')
        }
      }

      const userData = ref({})
      const userObjId = ref(null)
      const userMouseenter = async (d) => {
        if (userObjId.value === d.id) return
        userObjId.value = d.id
        const params = {
          key: d.operatorId
        }
        const res = await proxy.$store.dispatch(
          `operation/achievement/${USER_INFO}`, params)
        if (res) {
          const { data } = res
          userData.value = data
        }
      }

      return {
        visible,
        operationLog,
        flagDisplay,
        mutShowDrawer,
        afterVisibleChange,
        userData,
        userMouseenter
      }
    },
    render () {
      const { visible, operationLog, flagDisplay, mutShowDrawer, afterVisibleChange, userData, userMouseenter } = this
      return (
      <Drawer title="操作日志" placement="right" visible={visible} width={480} mask-closable={true} onClose={() => { this.visible = false }} after-visible-change={afterVisibleChange}>
        <Steps direction="vertical" progress-dot current={0}>
          {operationLog.map(d => (
            <Steps.Step>
              <template slot="title">
                <span domPropsInnerHTML={`${d.operationType === 1 ? '排名项目' : d.operationType === 2 ? '销冠项目' : '段位'}配置`}></span>
              </template>
              <template slot="description">
              <span domPropsInnerHTML={d.content}></span>
              <br />
              <Popover
              title={d.operatorName}
              mouseEnterDelay={0.5}
              placement="bottom"
            >
              <div slot='content'>
                <div class="ni-log-user-content" style="min-width:200px">
                <p class="ni-log-user-p">手机：{userData.mobile}</p>
                <p class="ni-log-user-p">
                  地区：{userData.area}（{userData.combinationArea}）
                </p>
                <p class="ni-log-user-p">部门：{userData.department}</p>
                <p class="ni-log-user-p">岗位：{userData.role}</p>
                <p class="ni-log-user-p">职能：{userData.workKeys}</p>
              </div>
              </div>
              <span
              onMouseenter={() => { userMouseenter(d) }}
                  class="blue"
                >【{d.operatorName}】</span>
            </Popover>
                <span>{d.createTime}</span>
                <br />
              </template>
            </Steps.Step>
          ))}
        </Steps>
        <div style="right: 0;bottom: 0;width: 100%;border-top: 1px solid #e9e9e9;padding: 20px 16px 10px 16px;background: #fff;text-align: center;z-index: 1;">
          {flagDisplay ? (
            <a-button type="primary" onClick={mutShowDrawer}>
              查看更多
            </a-button>
          ) : (
            <p style="color:#bababa">没有更多数据</p>
          )}
        </div>
      </Drawer>
      )
    }
  })
</script>
<style lang="scss" scoped>
:deep(.ant-steps-item-content){
  overflow: visible;
}
:deep(.ant-steps-item-title){
  width: 400px;
    height: auto;
    margin: -20px 0 0 20px;
}
:deep(.ant-steps-item-description){
  width: 400px;
    margin: 0 0 0 20px;
    font-size: 15px;
}
</style>
