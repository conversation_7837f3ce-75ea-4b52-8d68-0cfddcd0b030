<script lang="jsx">
  import { defineComponent, ref, getCurrentInstance } from 'vue'
  import HeadTitle from './head-title.vue'
  import {
    Checkbox,
    Tooltip,
    Icon,
    Table,
    InputNumber,
    Select,
    Button,
    Form,
    message
  } from 'ant-design-vue'
  import { pinCrownColumns as columns } from '../constants'
  import { EXCITATION_ADD_ENUMS, ITEM_SAVE_OR_UPDATE } from '@operation/store/modules/achievement/action-types'
  import { NiAreaSelect } from '@jiuji/nine-ui'

  export default defineComponent({
    props: {
      examine: {
        type: Object,
        default: () => ({})
      },
      rankingData: {
        type: Array,
        default: () => ([])
      },
      areaTypeOptions: {
        type: Array,
        default: () => ([])
      },
      rankingForm: {
        type: Object,
        default: () => ({})
      },
      otherSettingId: {
        type: [String, Number],
        default: null
      },
      tip: {
        type: Object,
        default: () => ({})
      }
    },
    components: {
      <PERSON>Title,
      NiAreaSelect
    },
    setup (props, { emit }) {
      const { proxy } = getCurrentInstance()
      const rolesOptions = ref([])

      const getEnums = async function () {
        const res = await proxy.$store.dispatch(
          `operation/achievement/${EXCITATION_ADD_ENUMS}`
        )
        if (res) {
          const {
            data: { roles }
          } = res
          rolesOptions.value = roles || []
        }
      }

      getEnums()

      const lastData = ref([])
      const dragItem = ref({})
      const dragIndex = ref(null)
      const targetArr = ref([])
      const customRow = function (record, index) {
        return {
          on: {
            // 鼠标移入
            mouseenter: event => {
              // 兼容IE
              var ev = event || window.event
              if (ev && ev.target) {
                ev.target.draggable = true
              }
            },
            // 开始拖拽
            dragstart: event => {
              // 兼容IE
              var ev = event || window.event
              // 阻止冒泡
              ev && ev.stopPropagation()
              // 得到源目标数据
              dragItem.value = record
              dragIndex.value = index
            },
            // 拖动元素经过的元素
            dragover: event => {
              // 兼容 IE
              var ev = event || window.event
              ev.dataTransfer.dropEffect = 'move'
              // 阻止默认行为*/
              ev && ev.preventDefault()
              // 拖拽在自己身上不需要做操作
              if (index === dragIndex.value) {
                return
              }
              // 在经过的元素的上面或者下面添加一条线
              var nowLine = ev?.target.closest('tr.ant-table-row ')
              if (!targetArr.value.includes(nowLine)) {
                targetArr.value.push(nowLine)
              }
              if (index > dragIndex.value) {
                if (!nowLine.classList.contains('after-line')) {
                  targetArr.value.forEach(item => {
                    item.classList.remove('befor-line')
                    item.classList.remove('after-line')
                  })
                  nowLine.classList.add('after-line')
                }
              } else {
                if (!nowLine.classList.contains('befor-line')) {
                  targetArr.value.forEach(item => {
                    item.classList.remove('befor-line')
                    item.classList.remove('after-line')
                  })
                  nowLine.classList.add('befor-line')
                }
              }
            },
            // 鼠标松开
            drop: event => {
              // 兼容IE
              var ev = event || window.event
              // 阻止冒泡
              ev && ev.stopPropagation()
              // 过滤出来源目标数据
              let data2 = props.rankingData.filter(
                item => item.item === dragItem.value.item
              )
              // 过滤出来除了源数据的数据
              lastData.value = props.rankingData.filter(
                item => item.item !== dragItem.value.item
              )
              // 将源数据插入到相应的数据中去
              lastData.value.splice(index, 0, ...data2)
              emit('updateRankingData', lastData.value)
              targetArr.value.forEach(item => {
                item.classList.remove('befor-line')
                item.classList.remove('after-line')
              })
              targetArr.value = []
            }
          }
        }
      }

      const save = async function () {
        const { mainRoles, departs, areaType } = props.rankingForm
        const items = props.rankingData.map((d, i) => {
          const { recordId, item, rankContent, expRatio } = d
          return {
            recordId,
            item,
            rankContent,
            expRatio,
            position: i + 1
          }
        })
        const params = {
          otherSettingId: props.otherSettingId,
          examine: props.examine.check,
          mainRoles,
          departs,
          areaType,
          items
        }
        const res = await proxy.$store.dispatch(
          `operation/achievement/${ITEM_SAVE_OR_UPDATE}`, params
        )
        if (res) {
          message.success('保存成功')
          emit('querySettings')
        }
      }

      return {
        customRow,
        save,
        rolesOptions
      }
    },
    render () {
      const { customRow, save, examine, rankingData, rolesOptions, rankingForm, areaTypeOptions, tip } = this
      return (
      <div class="pin-crown">
        <div class="head">
          <HeadTitle
            title="排名项目配置"
            tip={tip.itemSetting}
          ></HeadTitle>
          {
            examine.showButton ? <div class="gross-profit">
            <Checkbox
              checked={examine.check}
              onChange={e => {
                examine.check = e.target.checked
              }}
            >
              考核毛利
            </Checkbox>
            <Tooltip>
              <template slot="title">
                {tip.examine}
              </template>
              <Icon
                style="color: rgba(0, 0, 0, 0.65);"
                type="question-circle"
              />
            </Tooltip>
          </div> : null
          }

        </div>
        <div class="main">
        <div class="form-box">
        <div style="margin-left: 8px;margin-bottom: 10px;">
              <label>参与条件</label>
            </div>
        <Form
            layout="inline"
            label-col={{ span: 6 }}
            wrapper-col={{ span: 18 }}
          >
            <Form.Item label="主要角色">
              <Select
                placeholder="请选择"
                style="width:220px"
                max-tag-count={1}
                mode="multiple"
                allowClear
                option-filter-prop="children"
                options={rolesOptions}
                v-model={rankingForm.mainRoles}
              />
            </Form.Item>
            <Form.Item label="地区" class="departs">
              <NiAreaSelect
              v-model={rankingForm.departs}
              allow-clear={true}
              multiple
              show-search
              style="width:255px"
              placeholder="请选择"
              class="area-selector"
              max-tag-count={1}
            />
            </Form.Item>
            <Form.Item label="门店类别">
              <Select
                placeholder="请选择"
                style="width:220px"
                allowClear
                option-filter-prop="children"
                options={areaTypeOptions}
                v-model={rankingForm.areaType}
              />
            </Form.Item>
          </Form></div>
          <div class="table-box">
          <div class="require" style="margin-left: 10px;margin-top: 16px;margin-bottom: 10px;">
              <label>业务配置</label>
            </div>
          <Table
            columns={columns}
            data-source={rankingData}
            row-key="item"
            custom-row={customRow}
            pagination={false}
            scopedSlots={{
              rankTitle: () => (
                <div>
                  <span style="margin-right: 5px;">排名内容</span>
                  <Tooltip>
                    <template slot="title">
                      {tip.rankContent}
                    </template>
                    <Icon
                      style="color: rgba(0, 0, 0, 0.65);"
                      type="question-circle"
                    />
                  </Tooltip>
                </div>
              ),
              rank: (text, row) => (
                <Select
                  style="width:320px"
                  v-model={row.rankContent}
                  options={row.rankContents}
                  placeholder="请选择"
                ></Select>
              ),
              expRatio: (text, row) => (
                <InputNumber
                  style="width:120px"
                  v-model={row.expRatio}
                  min={0.01}
                  max={100}
                  precision={2}
                  placeholder="请输入"
                ></InputNumber>
              )
            }}
          ></Table>
          </div>

        </div>
        <div class="footer">
          <Button type="primary" onClick={save}>保存</Button>
        </div>
      </div>
      )
    }
  })
</script>
<style lang="scss" scoped>
.pin-crown {
  background: #fff;
  .head {
    padding: 24px 16px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eff1f7;
  }
  .main,.footer {
    padding: 16px;
  }
  .footer{
    text-align: right;
  }
  :deep(.ant-form-item){
    width: 300px;
  }
  :deep(.departs){
    .ant-form-item-label{
        width: 40px;
      }
  }
  :deep(.befor-line){
    td{
        border-top: 1.1px dashed #1890ff !important;
      }
  }
  :deep(.after-line) {
      td{
        border-bottom: 1px dashed #1890ff !important;
      }
  }
  .require {
    :deep(label) {
        &::before {
          display: inline-block;
          margin-right: 4px;
          color: #f5222d;
          font-size: 14px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: "*";
        }
    }
  }
}
</style>
