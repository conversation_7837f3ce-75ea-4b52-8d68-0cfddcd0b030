<script lang="jsx">
  import { defineComponent, computed, getCurrentInstance } from 'vue'
  import { Modal, Input, message } from 'ant-design-vue'
  import { PERFORMANCE_RANKING_STAGE_EXP_UPDATE } from '@operation/store/modules/achievement/action-types'

  export default defineComponent({
    props: {
      editRecord: {
        type: Object,
        default: () => ({
          areaExps: []
        })
      },
      showEditRankModal: {
        type: Boolean,
        default: false
      },
    },
    setup (props, { emit }) {
      const { proxy } = getCurrentInstance()
      const visible = computed({
        get: () => props.showEditRankModal,
        set: val => emit('update:showEditRankModal', val)
      })

      const afterClose = function () {
        emit('destroyedModal', 'editRank')
      }

      const updateRankEx = async function () {
        const params = {
          stageExps: props.editRecord.areaExps.map(d => {
            const { id, exp } = d
            return {
              id,
              exp
            }
          })
        }
        const res = await proxy.$store.dispatch(`operation/achievement/${PERFORMANCE_RANKING_STAGE_EXP_UPDATE}`, params)
        if (res) {
          message.success('修改成功')
          visible.value = false
          emit('performanceRankingStageQuery')
        }
      }

      return {
        visible,
        afterClose,
        updateRankEx
      }
    },
    render () {
      const { editRecord, visible, updateRankEx, afterClose } = this
      return (
      <Modal
        visible={visible}
        onOk={updateRankEx}
        title={`${editRecord.name}编辑`}
        onCancel={() => {
          this.visible = false
        }}
        after-close={afterClose}
      >
        <div class="flex flex-align-center flex-justify-between padding">
          <div class="bold">
            段位起始值
          </div>
          <div class="text-center bold" style="width: 100px">
            应用门店
          </div>
        </div>
        {editRecord.areaExps ? editRecord.areaExps.map(item => (
          <div class="flex flex-align-center flex-justify-between padding border-top">
            <Input style="width: 100px" v-model={item.exp}></Input>
            <div class="text-center" style="width: 100px">
              {item.areaName}
            </div>
          </div>
        )) : null}
      </Modal>
      )
    }
  })
</script>
