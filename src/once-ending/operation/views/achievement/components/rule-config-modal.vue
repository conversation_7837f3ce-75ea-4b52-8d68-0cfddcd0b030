<script lang="jsx">
  import { defineComponent, computed, ref, watch, getCurrentInstance } from 'vue'
  import { Modal, message, Input } from 'ant-design-vue'
  import { SALE_STATISTICS_EDIT_NOTE } from '@operation/store/modules/achievement/action-types'

  export default defineComponent({
    props: {
      value: {
        type: String,
        default: ''
      },
      showRuleConfigModal: {
        type: Boolean,
        default: false
      }
    },
    setup (props, { emit }) {
      const { proxy } = getCurrentInstance()
      const visible = computed({
        get: () => props.showRuleConfigModal,
        set: val => emit('update:showRuleConfigModal', val)
      })

      const content = ref(undefined)

      watch(() => visible.value, val => {
        if (val) {
          content.value = props.value
        }
      })

      const afterClose = function () {
        emit('destroyedModal', 'ruleConfig')
      }

      const ok = async function () {
        if (content.value.length > 1000) return message.error('规则文案最多只能输入1000字')
        const params = {
          type: 101,
          field: 'rankRule',
          note: content.value
        }
        const res = await proxy.$store.dispatch(
          `operation/achievement/${SALE_STATISTICS_EDIT_NOTE}`, params
        )
        if (res) {
          message.success('保存成功')
          emit('fieldNoteGetListByType')
          visible.value = false
        }
      }

      return {
        visible,
        afterClose,
        ok,
        content
      }
    },
    render () {
      const { visible, afterClose, ok, content } = this
      return <Modal
      title="规则文案配置"
        visible={visible}
        mask-closable={false}
        width="600px"
        onOk={ok}
        onCancel={() => {
          this.visible = false
        }}
        after-close={afterClose}>
        <div class="relative">
        <Input.TextArea v-model={this.content} placeholder="请输入" auto-size={{ minRows: 4 }} allow-clear>
        </Input.TextArea>
        <div class="limit absolute" style={{ color: content && content.length > 1000 ? 'red' : '' }}>{`${content ? content.length : 0}/1000`}</div>
        </div>
      </Modal>
    }
  })
</script>
<style lang="scss" scoped>
.limit{
    right: 10px;
    bottom: 5px;
}
</style>
