<script lang="jsx">
  import { defineComponent } from 'vue'
  import { Tooltip, Icon } from 'ant-design-vue'

  export default defineComponent({
    props: {
      title: {
        type: String,
        default: ''
      },
      tip: {
        type: String,
        default: ''
      }
    },
    render () {
      const { title, tip } = this
      return (
      <div class="title-box">
        <span class="title">{title}</span>
        {
          tip ? <Tooltip>
          <template slot="title">{tip}</template>
          <Icon
            style="color: rgba(0, 0, 0, 0.65);"
            type="question-circle"
          />
        </Tooltip> : null
        }
      </div>
      )
    }
  })
</script>
<style lang="scss" scoped>
.title-box {
  margin-right: 32px;
  .title {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    margin-right: 5px;
  }
}
</style>
