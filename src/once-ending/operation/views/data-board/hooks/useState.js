import { provide, inject, reactive, computed, getCurrentInstance } from 'vue'
import moment from 'moment'
import operationApi from '~/api/operation'
import { to } from '~/util/common'
import api from '@/operation/api/data-board'

const key = Symbol('state')

export function useState () {
  return inject(key)
}

export function createState () {
  const { proxy } = getCurrentInstance()
  const stateCommon = reactive({
    screen: {
      timeDimension: 1,
      time: undefined,
      timeStr: [],
      areaIds: [],
      areaLabels: [],
      attribute: ['1101'],
      attributeName: ['品牌综合店'],
      storeType: [],
      storeKind: [],
      loading: false
    },
    needRefresh: false,
    key: 'dataBoardParamsKey',
    role: undefined
  })
  const params = computed(() => {
    const { screen: { time, attributeName, loading, ...other } } = stateCommon
    return {
      ...other,
      startTime: Array.isArray(time) ? time[0] : time || undefined,
      endTime: Array.isArray(time) ? time[1] : time || undefined
    }
  })
  async function getRole () {
    const [err, res] = await to(api.getRole())
    if (err) throw err
    if (res?.code === 0) {
      stateCommon.role = res.data
    }
  }
  getRole()
  function setDefaultScreen () {
    stateCommon.screen.time = moment().format('YYYY-MM-DD')
    const par = sessionStorage.getItem(stateCommon.key)
    if (par) {
      Object.assign(stateCommon.screen, JSON.parse(par))
    }
  }
  function refresh () {
    const rank = proxy.$store.state?.userInfo?.Rank || []
    const { startTime, endTime } = params.value
    // if (!rank.includes('9o9') && startTime && endTime && moment(startTime).add(3, 'month').format('YYYY-MM-DD') < endTime) {
    //   return proxy.$message.warning('最多可查看3个月的数据')
    // }
    stateCommon.needRefresh = true
    setTimeout(() => {
      stateCommon.needRefresh = false
    }, 10)
  }
  setDefaultScreen()

  const o = {
    stateCommon,
    params,
    refresh,
    getRole
  }
  provide(key, o)
  return o
}
