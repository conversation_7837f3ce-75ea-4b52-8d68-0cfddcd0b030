import { cloneDeep } from 'lodash'
import api from '@/operation/api/data-board'
import { handleIcon } from './common/handleIcon.js'
import BigNumber from 'bignumber.js'
import axios from 'axios'
import store from '~/store'
import moment from 'moment'
// import { Toast } from 'vant'

export function getFloorData (params) {
  const { areaLabels, timeStr, ...other } = params
  return new Promise((resolve) => {
    api.getDataSource({ ...other }).then(res => {
      if (res?.code === 0 && res?.data?.floorList?.length) {
        resolve(res)
      } else {
        // Toast(res?.userMsg)
        resolve()
      }
    })
  })
}

export function exportXlsx ({ method, url, params, fileName = '' } = {}) {
  return new Promise((resolve) => {
    axios({
      method: method,
      url: url,
      data: params,
      responseType: 'blob',
      headers: {
        Authorization: store.state.token,
        contentType: 'application/json'
      }
    }).then((res) => {
      const link = document.createElement('a')
      let blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      link.download = fileName + moment().format('YYYYMMDDHHmmss') + '.xlsx'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      resolve()
    })
  })
}

export function setAppViewHeight (nextTick, box) {
  nextTick(() => {
    const containerBox = document.querySelector(box)
    if (containerBox?.offsetHeight && window.nineJsBridge && window.nineJsBridge.webviewHighCallback) {
      window.nineJsBridge.webviewHighCallback(containerBox.offsetHeight)
    }
  })
}

export const areaTypeOptions = [
  { label: '大区', value: 1 },
  { label: '小区', value: 2 },
  { label: '门店', value: 3 },
]

export const icons = {
  back: 'https://img2.ch999img.com/newstatic/51167/0b93d6f1bf4fe4c0.png',
  link: 'https://img2.ch999img.com/newstatic/25546/0d0f925e33b4dbbc.png',
  linkChange: 'https://img2.ch999img.com/newstatic/52534/0d402c6bc59af88c.png',
  linkFinish: 'https://img2.ch999img.com/newstatic/52538/0d46704484aeda53.png',
  dataUp: 'https://img2.ch999img.com/newstatic/25548/0d1044601d40f634.png',
  dataDown: 'https://img2.ch999img.com/newstatic/38294/0d104461a4d59fc2.png',
  smallBack: 'https://img2.ch999img.com/newstatic/51165/0bb805656360af30.png',
  arrowRight: 'https://img2.ch999img.com/newstatic/38296/0d17e53de9e6aa77.png'
}

export const tenScoreColorList = [
  '#FFF5E6',
  '#ECEFFB',
  '#FFF0F0',
]

export const bigSalesProColorList = [
  { color: getGradationColor('#1890FF', '#66B5FF'), borderColor: '#1890FF' },
  { color: getGradationColor('#47D45F', '#7DEC98'), borderColor: '#47D45E' },
  { color: getGradationColor('#737AFC', '#ABB2FE'), borderColor: '#737AFC' },
]
export const rankBg = [
  'https://img2.ch999img.com/newstatic/25547/0d171fab5f181c7e.png',
  'https://img2.ch999img.com/newstatic/25544/0d171fac9f2c9489.png',
  'https://img2.ch999img.com/newstatic/38294/0d171fad5006b635.png',
  'https://img2.ch999img.com/newstatic/51169/0d171faf5ad90f5d.png',
]

export const bulkyBrandTenColorList = [
  { color: getGradationColor('#FF6B4D', '#FF2B00'), areaColor: getGradationColor('rgba(255,42,0,0.3)', 'rgba(255,42,0,0)'), borderColor: '#FF2A00', rankColor: '#8C5200' },
  { color: getGradationColor('#1890FF', '#66B5FF'), areaColor: getGradationColor('rgba(24,144,255,0.3)', 'rgba(24,144,255,0)'), borderColor: '#1890FF', rankColor: '#626D95' },
  { color: getGradationColor('#09BCBC', '#15DFDF'), areaColor: getGradationColor('rgba(9,188,188,0.3)', 'rgba(9,188,188,0)'), borderColor: '#09BCBC', rankColor: '#95523C' },
  { color: getGradationColor('#FF8000', '#FFB366'), areaColor: getGradationColor('rgba(255,128,0,0.3)', 'rgba(255,128,0,0)'), borderColor: '#FF8000' },
  { color: getGradationColor('#2B8039', '#89CE94'), areaColor: getGradationColor('rgba(43,128,57,0.3)', 'rgba(43,128,57,0)'), borderColor: '#2B8039' },
  { color: getGradationColor('#ABB2FE', '#737AFC'), areaColor: getGradationColor('rgba(77,105,255,0.3)', 'rgba(77,105,255,0)'), borderColor: '#4D69FF' },
  { color: '#1428A0', areaColor: getGradationColor('rgba(20,40,160,0.3)', 'rgba(20,40,160,0)'), borderColor: '#1428A0' },
  { color: getGradationColor('#FFA425', '#FFCA80'), areaColor: getGradationColor('rgba(255,188,61,0.3)', 'rgba(255,188,61,0)'), borderColor: '#FFBC3D' },
  { color: getGradationColor('#CC0000', '#CC5252'), areaColor: getGradationColor('rgba(204,0,0,0.3)', 'rgba(204,0,0,0)'), borderColor: '#CC0000' },
  { color: getGradationColor('#47D45E', '#7DEC98'), areaColor: getGradationColor('rgba(71,212,94,0.3)', 'rgba(71,212,94,0)'), borderColor: '#47D45E' },
  { color: getGradationColor('#9C9C9C', '#CCCCCC'), areaColor: getGradationColor('rgba(156,156,156,0.3)', 'rgba(156,156,156,0)'), borderColor: '#9C9C9C' },
]

export const originPerformanceData = [
  { currentRate: 0 },
  { currentRate: 0 },
  { currentRate: 0 },
  { currentRate: 0 },
  { currentRate: 0 },
  { currentRate: 0 },
]
export function getPerformanceDataColor (rate, timeLine) {
  let color = []
  if (rate && timeLine) {
    color = ['rgba(24, 144, 255, 1)', 'rgba(102, 181, 255, 1)'] // 蓝色
    const r = rate.replace('%', '')
    const t = timeLine.replace('%', '')
    const cacheTB = (new BigNumber(t)).plus(5)
    const cacheSB = (new BigNumber(t)).minus(5)
    const bigV = (new BigNumber(r)).comparedTo(cacheTB)
    const smallV = (new BigNumber(r)).comparedTo(cacheSB)
    if (bigV !== -1) {
      color = ['rgba(71, 212, 95, 1)', 'rgba(125, 236, 152, 1)'] // 绿色
    } else if (smallV === -1) {
      color = ['rgba(255, 77, 77, 1)', 'rgba(255, 153, 153, 1)'] // 红色
    }
  }
  return color
}

export const salesCommissionColorList = [
  { borderColor: ' #FA6400', areaColor: getGradationColor('rgba(250,100,0,0.3)', 'rgba(250,100,0,0)') }
]

export const bulkyTargetFinishColorList = [
  { areaColor: getGradationColor('rgba(24,144,255,0.3)', 'rgba(24,144,255,0)'), borderColor: '#1890FF' },
  { areaColor: getGradationColor('rgba(250,100,0,0.3)', 'rgba(250,100,0,0)'), borderColor: '#FA6400' },
  { areaColor: getGradationColor('rgba(255,42,0,0.3)', 'rgba(255,42,0,0)'), borderColor: '#FF2A00' },
  { areaColor: getGradationColor('rgba(71,212,94,0.3)', 'rgba(71,212,94,0)'), borderColor: '#47D45E' },
  { areaColor: getGradationColor('rgba(255,188,61,0.3)', 'rgba(255,188,61,0)'), borderColor: '#FFBC3D' }
]

const bulkyTargetFinishTip = [
  { borderColor: '#1890FF', name: '大件销量合计', key: '大件销量合计' },
  { borderColor: '#FF2A00', name: '增值业务单毛', key: '增值业务单毛' },
  { borderColor: '#FFBC3D', name: '自营服务利润比', key: '自营服务利润比' },
  { borderColor: '#FA6400', name: '总毛利', key: '总毛利' },
  { borderColor: '#47D45E', name: '配件利润比', key: '配件利润比' },
]

export function bulkyTargetFinishOption (dataSource) {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
      },
      backgroundColor: '#f5f5f5',
      textStyle: {
        color: '#333'
      },
      formatter: (params) => {
        console.log('tip', params)
        if (!params?.length) {
          return ''
        }
        const dataObj = {}
        function getValues (array) {
          dataObj.item = params[0]?.axisValueLabel
          array.forEach(it => {
            dataObj[it.item] = it.sales?.find(o => o.label === dataObj.item)?.value
          })
        }
        getValues(dataSource)
        const borderColors = bulkyTargetFinishTip.filter(it => params.find(o => o.color === it.borderColor))
        console.log('tip', borderColors)
        return `<div style="${styles.min100}">
        <p>${dataObj.item}</p>
        <div style="${styles.content};${styles.maxH80}">
        ${borderColors.map((it, index) => {
          return getSingleTip(dataObj, it, index, params)
        }).join('')}
        </div>
        </div>`
      }
    },
    legend: {
      show: false
    },
    grid: {
      left: '5%',
      top: '14%',
      right: '6%',
      bottom: '9%'
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: [],
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#828282',
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#CCCCCC'
        }
      }
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      name: '完成率(%)',
      nameTextStyle: {
        color: '#828282',
        padding: [0, 6, 0, 0],
      },
      axisLabel: {
        color: '#828282',
        formatter: function (value, index) {
          return value
        },
      },
      splitLine: {
        lineStyle: {
          color: '#EBEBEB',
          type: 'dashed'
        }
      },
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
    },
    series: []
  }
}

export const commonEchartPieDefaultOption = {
  series: [
    {
      type: 'pie',
      radius: ['50%', '87%'],
      label: {
        show: false,
        position: 'center',
      },
      emphasis: {
        label: {
          show: false,
        },
        scale: true,
        scaleSize: 4,
      },
      labelLine: {
        show: false,
      },
      data: [],
    },
  ],
}

export function getGradationColor (colorStart, colorEnd) { // 获取渐变色
  return {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [{
      offset: 0, color: colorStart // 0% 处的颜色
    }, {
      offset: 1, color: colorEnd // 100% 处的颜色
    }],
    global: false // 缺省为 false
  }
}
export function dealYData (array, toWan) {
  const cacheD = {
    unit: '元',
    data: array.map(val => +val)
  }
  if (array.find(v => (new BigNumber(v)).comparedTo(10000) !== -1) || toWan) {
    cacheD.unit = '万'
    cacheD.data = array.map(val => {
      const cachaN = (new BigNumber(val))?.div(10000)
      const decimals = (new BigNumber(cachaN))?.comparedTo(1) !== -1 ? 2 : 4
      const num = +(cachaN?.toFixed(decimals))
      return num
    })
  }
  return cacheD
}

function getYAxisCommon (nameAlign, namePadding, yLabel) {
  return {
    type: 'value',
    offset: 0,
    nameGap: 16,
    nameTextStyle: {
      align: nameAlign,
      color: '#828282',
      padding: namePadding,
    },
    min: 0,
    splitNumber: 5,
    axisLabel: {
      formatter: function (value, index) {
        // const arr = value?.toString()?.split('.')
        // const val = arr[1]?.length > 0 ? value?.toFixed(0) : value
        return yLabel ? value + yLabel : value
      },
      color: '#828282',
    },
    axisPointer: {
      type: 'line',
      axis: 'y'
    },
    boundaryGap: false,
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    splitLine: {
      lineStyle: {
        color: '#EBEBEB',
        type: 'dashed'
      }
    }
  }
}

function getXAxisCommon (showLabel, showLine) {
  return {
    type: 'category',
    data: [],
    axisTick: {
      show: false
    },
    axisLabel: {
      show: showLabel,
      color: '#828282',
    },
    axisLine: {
      show: showLine,
      lineStyle: {
        color: '#CCCCCC'
      }
    }
  }
}

export const dataZoom = [
  {
    type: 'slider',
    xAxisIndex: [0],
    show: true,
    start: 0,
    end: 100,
    brushSelect: false,
    backgroundColor: '#F7F8FA',
    dataBackground: {
      lineStyle: {
        color: '#CCCCCC'
      },
      areaStyle: {
        color: '#EDEEF0',
        opacity: 1
      }
    },
    selectedDataBackground: {
      lineStyle: {
        color: '#1890FF'
      },
      areaStyle: {
        color: '#CCE6FF',
        opacity: 1
      }
    },
    showDetail: false,
    borderColor: '#F7F8FA',
    borderRadius: 2,
    handleIcon: `path://${handleIcon}`,
    handleSize: 28,
    fillerColor: 'rgba(24,144,255,0.1)',
    height: 28,
  },
  {
    type: 'inside',
    xAxisIndex: [0],
  }
]
const styles = {
  flex: 'display: flex',
  flexJustifyBetween: 'justify-content: space-between',
  flexAlignCenter: 'align-items: center',
  min100: 'min-width: 100px;max-width: 500px',
  borders: 'width: 12px;height: 12px; border-radius: 50%;box-sizing: border-box;margin-top: 5px',
  borderSales: 'border: 4px solid #1890FF',
  borderProfit: 'border:4px solid #FA6400',
  label: 'font-size: 12px;color: #9C9C9C;margin-left: 2px',
  mt10: 'margin-top: 10px',
  mt5: 'margin-top: 5px',
  ml10: 'margin-left: 10px',
  ml6: 'margin-left: 6px',
  content: 'display: flex;flex-direction: column;flex-wrap: wrap',
  maxH56: 'max-height: 56px',
  maxH80: 'max-height: 80px',
}

function getSingleTip (dataObj, itemObj, index, borderColors) {
  const isMl10 = borderColors?.length > 2 && index > (parseInt(borderColors.length / 2) - (borderColors.length % 2 === 0 ? 1 : 0))
  return `<div style="${styles.flex};${styles.flexJustifyBetween};${isMl10 ? styles.ml10 : ''}">
    <div style="${styles.flex}">
      <div style="${styles.borders};border:4px solid ${itemObj.color || itemObj.borderColor}"></div>
      <div style="${styles.label}">${itemObj.name}</div>
    </div>
    <span style="${styles.ml10}">${dataObj[itemObj.key]}</span>
  </div>`
}

function getTooltip (borderColors, dataSource) {
  return {
    trigger: 'axis',
    axisPointer: {
      type: 'line',
    },
    backgroundColor: '#f5f5f5',
    textStyle: {
      color: '#333'
    },
    formatter: (params) => {
      console.log('tip', params)
      if (!params?.length) {
        return ''
      }
      const dataObj = dataSource[params[0]?.dataIndex] || {}
      return `<div style="${styles.min100}">
      <p>${dataObj.item}</p>
      <div style="${styles.content};${styles.maxH56}">
      ${borderColors.map((it, index) => {
        return getSingleTip(dataObj, it, index, borderColors)
      }).join('')}
      </div>
      </div>`
    }
  }
}

const nameTipOptionDataOverview = [
  { color: '#1890FF', name: '大件销量', key: 'salesShow' },
  { color: '#FA6400', name: '总毛利', key: 'profitShow' },
]
export function bulkySalesTrendOption (dataSource, yUnit) {
  return {
    tooltip: { ...getTooltip(nameTipOptionDataOverview, dataSource) },
    grid: {
      top: '14%',
      left: '6%',
      right: '6%',
      bottom: '28%',
    },
    legend: {
      show: false
    },
    title: {
      show: false
    },
    xAxis: [
      { ...getXAxisCommon(true, true) }
    ],
    yAxis: [
      {
        name: '大件销量',
        ...getYAxisCommon('right', [0, -9, 0, 0])
      },
      {
        name: `总毛利(${yUnit})`,
        ...getYAxisCommon('left', [0, 0, 0, -22])
      }
    ],
    dataZoom: cloneDeep(dataZoom),
    series: [
      {
        name: '大件销量',
        type: 'bar',
        data: [],
        itemStyle: {
          color: getGradationColor('#66B5FF', '#1890FF')
        },
        barMaxWidth: 40
      },
      {
        name: '总毛利',
        type: 'line',
        symbol: dataSource?.length === 1 ? 'circle' : 'none',
        yAxisIndex: 1,
        xAxisIndex: 0,
        data: [],
        lineStyle: {
          color: getGradationColor('rgba(250, 100, 0, 1)', 'rgba(255, 204, 51, 1)'),
          width: 4
        },
        itemStyle: {
          color: getGradationColor('rgba(250, 100, 0, 1)', 'rgba(255, 204, 51, 1)'),
        },
      }
    ]
  }
}
const nameTipOptionRecoveryProbability = [
  { color: '#1890FF', name: '回收单毛', key: 'recoverSingleProfitShow' },
  { color: '#47D45E', name: '单独跟机率', key: 'singleFollowRateShow' },
  { color: '#FFA425', name: '连带跟机率', key: 'ldFollowRateShow' },
  { color: '#4D69FF', name: '总跟机率', key: 'totalFollowRateShow' },
]

export function recoveryProbabilityOption (dataSource) {
  return {
    tooltip: { ...getTooltip(nameTipOptionRecoveryProbability, dataSource) },
    grid: {
      top: '14%',
      left: '6%',
      right: '6%',
      bottom: '28%',
    },
    legend: {
      show: false
    },
    title: {
      show: false
    },
    xAxis: [
      { ...getXAxisCommon(true, true) }
    ],
    yAxis: [
      {
        name: '回收单毛',
        ...getYAxisCommon('right', [0, -22, 0, 0])
      },
      {
        name: '跟机率',
        ...getYAxisCommon('left', [0, 0, 0, -10])
      }
    ],
    dataZoom: cloneDeep(dataZoom),
    series: [
      {
        type: 'bar',
        yAxisIndex: 0,
        xAxisIndex: 0,
        itemStyle: {
          color: getGradationColor('#66B5FF', '#1890FF')
        },
        data: [],
        barMaxWidth: 40
      },
      {
        name: '连带跟机率',
        type: 'line',
        yAxisIndex: 1,
        xAxisIndex: 0,
        showSymbol: false,
        data: [],
        lineStyle: {
          color: getGradationColor('#FFA425', '#FFDF80'),
          width: 4
        },
      },
      {
        name: '单独跟机率',
        type: 'line',
        yAxisIndex: 1,
        xAxisIndex: 0,
        showSymbol: false,
        data: [],
        lineStyle: {
          color: getGradationColor('#47D45F', '#7DEC98'),
          width: 4
        },
      },
      {
        name: '总跟机率',
        type: 'line',
        yAxisIndex: 1,
        xAxisIndex: 0,
        showSymbol: false,
        data: [],
        lineStyle: {
          color: getGradationColor('#4D69FF', '#8094FF'),
          width: 4
        },
      },
    ]
  }
}
const nameTipOptionTying = [
  { color: '#1890FF', name: '总量', key: 'total' },
  { color: '#FFA425', name: '搭售量', key: 'tyingSaleCount' },
  { color: '#47D45E', name: '搭售率', key: 'tyingSaleRate' }
]
export function tyingOption (dataSource) {
  return {
    tooltip: { ...getTooltip(nameTipOptionTying, dataSource) },
    grid: {
      top: '14%',
      left: '6%',
      right: '6%',
      bottom: '28%',
    },
    legend: {
      show: false
    },
    title: {
      show: false
    },
    xAxis: [
      { ...getXAxisCommon(true, true) }
    ],
    yAxis: [
      {
        name: '连带量',
        ...getYAxisCommon('right', [0, -10, 0, 0])
      },
      {
        name: '搭售率',
        ...getYAxisCommon('left', [0, 0, 0, -2])
      }
    ],
    dataZoom: cloneDeep(dataZoom),
    series: [
      {
        type: 'bar',
        yAxisIndex: 0,
        xAxisIndex: 0,
        data: [],
        barMaxWidth: 40
      },
      {
        type: 'bar',
        yAxisIndex: 0,
        xAxisIndex: 0,
        data: [],
        barGap: '-100%',
        barMaxWidth: 40
      },
      {
        name: '搭售率',
        type: 'line',
        yAxisIndex: 1,
        xAxisIndex: 0,
        showSymbol: false,
        data: [],
        lineStyle: {
          color: getGradationColor('rgba(71, 212, 95, 1)', 'rgba(125, 236, 152, 1)'),
          width: 4
        },
      },
    ]
  }
}

export const dataOverviewColor = [['#66B5FF', '#1890FF'], ['rgba(250, 100, 0, 1)', 'rgba(255, 204, 51, 1)']]

export function dataOverviewOption (dataSource, timeDimension, currentTab) {
  const tooltip = {
    show: true,
    trigger: 'axis',
    axisPointer: {
      type: 'line',
    },
    backgroundColor: '#f5f5f5',
    textStyle: {
      color: '#333'
    },
    formatter: (params) => {
      if (!params?.length) {
        return ''
      }

      const getCompareRatio = function () {
        const value = new BigNumber(dataList[0].compareRatio.replace('%', ''))
        const color = value.comparedTo(new BigNumber(-5)) === -1 ? '#F21C1C' : value.comparedTo(new BigNumber(5)) === 1 ? '#52CC7A' : ''

        return `<div style="${styles.flex};${styles.flexJustifyBetween}">
        <div style="${styles.flex}">
          <div style="${styles.label}">${`${timeDimension === 2 ? '环比' : '同比'}${timeDimension === 2 ? `上${dataList[0].item}` : '去年'}`}</div>
        </div>
        <span style="${styles.ml10};color:${color}">${dataList[0].compareRatio}</span>
      </div>`
      }

      const xLabel = params[0]?.name
      const dataList = dataSource.map(d => d.data).map(d => d.find(d => d.item === xLabel))
      return `<div style="${styles.min100}">
      <p>${xLabel}</p>
      ${params.map((it, index) => {
        return `<div style="${styles.flex};${styles.flexJustifyBetween}">
        <div style="${styles.flex}">
          <div style="${styles.borders};border:4px solid ${dataOverviewColor[index][1]}"></div>
          <div style="${styles.label}">${dataSource[index].item}</div>
        </div>
        <span style="${styles.ml10}">${`${it.value}${it.data.unit === '万' ? '万' : ''}`}</span>
      </div>`
      }).join('')}
      ${
        dataList[0].compareRatio ? getCompareRatio() : ''
      }
      </div>`
    }
  }

  const getSeries = function () {
    const length = timeDimension === 1 ? 1 : 2
    return [...new Array(length)].map((d, i) => {
      const color = dataOverviewColor[i]
      return {
        name: '',
        type: 'line',
        symbolSize: 6,
        data: [],
        label: {
          show: true,
          formatter: (params) => {
            return params.data.value
          }
        },
        itemStyle: {
          color: getGradationColor(color[0], color[1]),
        },
        lineStyle: {
          color: getGradationColor(color[0], color[1]),
          width: 4
        },
      }
    })
  }

  return {
    tooltip,
    grid: {
      top: '16%',
      left: '12%',
      right: '5%',
      bottom: '30%',
    },
    legend: {
      show: false
    },
    title: {
      show: false
    },
    xAxis: { ...getXAxisCommon(true, true) },
    yAxis: {
      ...getYAxisCommon('right', currentTab === 'largeCount' ? [0, -10, 0, 0] : [0, -15, 0, 0]),
      min: 'dataMin',
      max: 'dataMax'
    },
    dataZoom: cloneDeep(dataZoom),
    series: getSeries()
  }
}
