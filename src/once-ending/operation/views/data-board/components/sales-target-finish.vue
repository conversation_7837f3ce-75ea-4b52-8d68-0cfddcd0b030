<script lang="jsx">
  import { defineComponent, reactive, toRefs, getCurrentInstance, provide, ref } from 'vue'
  import selectTab from './select-tab.vue'
  import Title from './title.vue'
  import { icons, areaTypeOptions } from '../constants.js'
  import SalesTargetChart from './sales-target-chart.vue'
  import SalesTargetTable from './sales-target-table.vue'

  export default defineComponent({
    components: { selectTab, Title, SalesTargetChart, SalesTargetTable },
    setup () {
      const { proxy } = getCurrentInstance()
      const state = reactive({
        type: 1
      })
      const tabsOptions = ref([])
      const currentTab = ref(1)
      const tableDimension = ref(1)
      const exportLoading = ref(false)
      provide('tabsOptions', tabsOptions)
      provide('currentTab', currentTab)
      provide('tableDimension', tableDimension)
      provide('exportLoading', exportLoading)

      function changeType () {
        state.type = state.type === 1 ? 2 : 1
        tableDimension.value = 1
        currentTab.value = 1
      }

      function changeArea (val) {
        tableDimension.value = val
        proxy.$refs.SalesTargetTable.getData()
      }

      function changeTab () {
        proxy.$refs.SalesTargetChart.getDataSource()
      }

      function exportTable () {
        proxy.$refs.SalesTargetTable.exportTable()
      }

      return {
        ...toRefs(state),
        changeType,
        changeArea,
        tabsOptions,
        changeTab,
        currentTab,
        tableDimension,
        exportLoading,
        exportTable
      }
    },
    render () {
      const {
        type,
        tabsOptions,
        changeType,
        tableDimension,
        changeArea,
        changeTab,
        currentTab,
        exportLoading,
        exportTable
      } = this
      return <div class="container-box">
        <Title name="销售目标完成率">
          {type === 1 ? <div class="flex flex-align-center">
            {tabsOptions?.length > 1 ? <selectTab onChange={changeTab} options={tabsOptions} v-model={this.currentTab}/> : null}
            <NiImg onTap={changeType} class="ml-20 links" src={icons.link}/>
            </div> : <div class="flex flex-align-center">
              <div class="flex flex-align-center">
              { areaTypeOptions.map(it => <a-checkbox onClick={() => changeArea(it.value)} checked={tableDimension === it.value}>{it.label}</a-checkbox>) }
            </div>
            <a-button size="small" type="primary" class="ml-16" onClick={exportTable} loading={exportLoading}>导出</a-button>
            <NiImg onTap={changeType} class="ml-20 links" src={icons.linkFinish}/>
          </div> }
        </Title>
        {type === 1 ? <SalesTargetChart ref="SalesTargetChart"/> : <SalesTargetTable ref="SalesTargetTable"/>}
      </div>
    }
  })
</script>
<style scoped lang="scss">
@import '../common/mian.scss';
.container-box {
  width: calc((100% - 14px) / 2);
}
.links {
  width: 16px;
  height: 16px;
  cursor: pointer;
}
</style>
