<script lang="jsx">
  import { defineComponent, reactive, toRefs, ref, nextTick, watch } from 'vue'
  import chartPie from './chart-pie.vue'
  import { bulkyBrandTenColorList, getFloorData, icons } from '../constants.js'
  import EmptyData from './empty-data.vue'
  import { NiImg } from '@jiuji/nine-ui'
  import { useState } from '../hooks/useState.js'
  import Title from './title.vue'

  export default defineComponent({
    components: { chartPie, EmptyData, NiImg, Title },
    setup () {
      const { stateCommon, params } = useState()
      const state = reactive({
        dataSource: [],
        currentTab: 7,
        tabsOptions: [],
        loading: false,
      })

      watch(() => stateCommon.needRefresh, (val) => {
        if (val) {
          getDataSource()
        }
      }, { immediate: true })

      async function getDataSource () {
        state.loading = true
        state.dataSource = []
        const res = await getFloorData({
          ...params.value,
          floorId: 5,
        })
        state.loading = false
        if (res) {
          const { floorList } = res.data
          const { statistics } = floorList[0]?.data
          state.dataSource = statistics?.map((it, i) => ({
            ...it,
            name: it.item,
            value: it.ratio?.replace('%', ''),
            ...bulkyBrandTenColorList[i],
            itemStyle: { color: bulkyBrandTenColorList[i].color }
          }))
        }
      }
      getDataSource()

      return {
        ...toRefs(state),
      }
    },
    render () {
      const {
        dataSource,
        loading,
      } = this
      return <div class="container-box">
        <Title name="大件品牌占比TOP10"/>
       <div class="content flex flex-center">
        {dataSource?.length ? <chartPie isTen={true} showOther={true} data={dataSource}/> : <EmptyData height={248} loading={loading}/>}
        </div>
      </div>
    }
  })
</script>
<style scoped lang="scss">
@import '../common/mian.scss';
.container-box {
  width: calc((100% - 12px) / 2);
  height: 322px;
  min-width: 590px;
}
.content {
  height: 268px;
  padding-top: 20px;
  box-sizing: border-box;
}
</style>
