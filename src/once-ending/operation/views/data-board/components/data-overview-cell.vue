<script lang="jsx">
  import { defineComponent, reactive, toRefs, ref, watch } from 'vue'
  import selectTab from './select-tab.vue'
  import { getFloorData, icons } from '../constants.js'
  import EmptyData from './empty-data.vue'
  import Title from './title.vue'
  import { useState } from '../hooks/useState.js'
  import { NiImg } from '@jiuji/nine-ui'

  export default defineComponent({
    components: { selectTab, EmptyData, Title, NiImg },
    setup () {
      const { stateCommon, params } = useState()
      const state = reactive({
        loading: false,
        dataSource: []
      })

      watch(() => stateCommon.needRefresh, (val) => {
        if (val) {
          getData()
        }
      }, { immediate: true })
      async function getData () {
        state.loading = true
        state.dataSource = []
        const res = await getFloorData({
          ...params.value,
          floorId: 1
        })
        state.loading = false
        if (res) {
          const { floorList } = res.data
          state.dataSource = floorList[0]?.data?.statistics || []
        }
      }
      getData()

      return {
        ...toRefs(state),
        getData
      }
    },
    render () {
      const {
        loading,
        dataSource
      } = this
      const labelItem = (array, valueClass, mt, type) => <p class="flex flex-wrap flex-align-center font-12">
        { array?.map((it, index) => <p class={['flex flex-align-center line-12', index === 0 ? 'mr-20' : '', mt]}>
          { it.value || it.value === 0 ? <span class="label nowrap">{it.label}</span> : null }
          <span class={['nowrap', valueClass]} style={{ color: it.color }}>{it.value}</span>
          { it.value1 ? '（' : null }
          { it.trends ? <NiImg class="img" src={icons[it.trends === 'down' ? 'dataDown' : 'dataUp']} /> : null }
          { it.value1 }
          { it.value1 ? '）' : null }
        </p>) }
      </p>
      return <div class="content">
          { dataSource?.length ? <div class="flex flex-justify-between flex-wrap">
            { dataSource.map((item, index) => <div class="sale-item">
              <p class="type">{item.item}</p>
              <div class="flex flex-align-center mt-10 ml-10">
                <p class="flex flex-align-end">
                  <p class="value">{item.value}</p>
                  <span class="unit">{item.valueUnit}</span>
                </p>
                <div class="margin-left">{ labelItem(item.data?.slice(0, 1), 'label', '', 1) }</div>
              </div>
              <p class="mt-4 ml-10 line-17">
                { labelItem(item.data?.slice(1, 3), '', 'mt-6') }
              </p>
            </div>) }
          </div> : <EmptyData height={339} loading={loading}/> }
        </div>
    }
  })
</script>
<style lang="scss" scoped>
@import '../common/mian.scss';
.sale-item {
  width: calc((100% - 40px) / 5);
  padding-bottom: 10px;
  background: #F7F8FA;
  border-radius: 4px;
  margin-top: 10px;
  .type {
    background: rgba(24, 144, 255, 0.1);
    border-radius: 4px 0px 4px 0px;
    padding: 6px 8px;
    color: #1890FF;
    line-height: 20px;
    font-weight: 600;
    display: inline-block;
  }
  .value {
    font-weight: 600;
    font-size: 24px;
    line-height: 24px;
  }
  .unit {
    font-size: 16px;
    font-weight: 600;
    line-height: 16px;
  }
  .img {
    width: 10px;
    height: 12px;
    margin-left: 2px;
  }
}
</style>
