<template>
  <div class="flex flex-center" :style="{height: height ? height + 'px' : 'auto'}">
    <a-empty
      v-if="!loading"
      :image="noData"
      :description="description"
    />
    <div v-else class="flex flex-col flex-center loading full-height">
      <a-spin :spinning="loading"></a-spin>
      <p class="mt-4">数据加载中...</p>
    </div>
  </div>
</template>

<script>
  import noData from '../common/no-data.png'
  export default {
    name: 'empty',
    props: {
      description: {
        type: String,
        default: '暂无数据',
      },
      showGray: {
        type: <PERSON>olean,
        default: false,
      },
      loading: {
        type: Boolean,
        default: false,
      },
      height: {
        type: Number,
        default: 142
      },
    },
    data () {
      return {
        noData
      }
    }
  }
</script>

<style lang="less" scoped>
.loading {
  color: #9C9C9C
}
:deep(.ant-empty-description) {
  color: #9C9C9C;
}
</style>
