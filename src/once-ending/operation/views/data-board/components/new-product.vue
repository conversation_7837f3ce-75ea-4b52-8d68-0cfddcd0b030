<script lang="jsx">
  import { defineComponent, reactive, toRefs, watch, nextTick } from 'vue'
  import selectTab from './select-tab.vue'
  import { tenScoreColorList, getFloorData } from '../constants'
  import { NiImg } from '@jiuji/nine-ui'
  import EmptyData from './empty-data.vue'
  import rank1 from '../common/top1.png'
  import rank2 from '../common/top2.png'
  import rank3 from '../common/top3.png'
  import rank from '../common/top.png'
  import { useState } from '../hooks/useState.js'
  import Title from './title.vue'

  const rankImgMap = new Map([
    [1, rank1],
    [2, rank2],
    [3, rank3],
  ])

  const columns = [
    { title: '排名', key: 'rank', width: '11%' },
    { title: '机型', key: 'product', width: '40%' },
    { title: '销量', key: 'saleCount', width: '17%' },
    { title: '总销量', key: 'totalSaleCount', width: '17%' },
    { title: '增值单毛', key: 'valueAddSingleProfit', width: '15%' },
  ]

  export default defineComponent({
    components: { selectTab, NiImg, EmptyData, Title },
    setup () {
      const { stateCommon, params } = useState()
      const state = reactive({
        currentTab: 5,
        tabsOptions: [],
        dataSource: [],
        loading: false,
        containerBoxHeight: undefined
      })
      watch(() => stateCommon.needRefresh, (val) => {
        if (val) {
          getData()
        }
      }, { immediate: true })
      async function getData (type) {
        state.loading = true
        state.dataSource = []
        const { currentTab } = state
        const res = await getFloorData({
          ...params.value,
          floorId: 8,
          tabId: currentTab
        })
        state.loading = false
        if (res) {
          const { floorList } = res.data
          const { areaLabels } = params.value
          const { data: { statistics = [] }, tabs } = floorList[0]
          if (tabs?.length && areaLabels?.length) {
            tabs[0].title = areaLabels.join(',')
          }
          state.tabsOptions = tabs || []
          state.dataSource = statistics
        }
      }
      getData()

      return {
        ...toRefs(state),
        getData
      }
    },
    render () {
      const {
        dataSource,
        loading
      } = this
      return <div class="container-box">
        <Title name="新品战报"/>
        <div class="content" style="paddingBottom: 19px">
          {dataSource.length ? (
          <div class="main">
            <div class="mt-10 flex">
            { columns.map(column => <div class="label" style={{
              width: column.width,
              marginLeft: column.labelLeft,
              marginRight: column.labelRight,
              lineHeight: '17px',
              textAlign: column.textAlign
              }}>
              { column.title }
            </div>) }
          </div>
            {dataSource?.slice(0, 5).map((d, i) => (
              <div
                class="item flex flex-align-center"
                style={{
                  background: tenScoreColorList[i] || 'linear-gradient(270deg, #f7f8fa 0%, #ffffff 100%)',
                }}
              >
              { columns.map(column => <div style={{ width: column.width, textAlign: column.textAlign }}>
                { column.key === 'rank' ? <div class="rank">
                    <div class="relative flex flex-align-center flex-justify-center">
                      <NiImg class="img" src={rankImgMap.get(i + 1) || rank} />
                      {!rankImgMap.get(i + 1) ? (
                        <span class="blue nowrap font-11 bold absolute">Top{i + 1}</span>
                      ) : null}
                    </div>
                </div> : <div>{ d[column.key] }</div> }
              </div>)}
              </div>
            ))}
          </div>
        ) : null}
        {!dataSource.length ? <EmptyData loading={loading} height={249}/> : null}
        </div>
      </div>
    }
  })
</script>
<style scoped lang="scss">
@import '../common/mian.scss';
.container-box {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  min-width: 400px;
}
.main {
  .item {
    border-radius: 8px;
    padding: 5px 0;
    margin-top: 13px;
    word-break: break-all;
    &:nth-child(2) {
      margin-top: 10px;
    }
    .rank {
      color: #9c9c9c;
      font-weight: 600;
      width: 44px;
      height: 22px;
      .img {
        width: 44px;
        height: 22px;
      }
    }
  }
}
</style>
