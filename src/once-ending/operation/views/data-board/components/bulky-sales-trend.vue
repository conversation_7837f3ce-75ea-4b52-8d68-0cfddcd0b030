<script lang="jsx">
  import {
    defineComponent,
    reactive,
    toRefs,
    ref,
    nextTick,
    watch,
    getCurrentInstance,
  } from 'vue'
  import selectTab from './select-tab.vue'
  import * as echarts from 'echarts5'
  import {
    dataOverviewOption,
    getFloorData,
    dataOverviewColor,
  } from '../constants.js'
  import { cloneDeep } from 'lodash'
  import EmptyData from './empty-data.vue'
  import BigNumber from 'bignumber.js'
  import { Checkbox } from 'ant-design-vue'
  import Title from './title.vue'
  import { useState } from '../hooks/useState.js'

  export default defineComponent({
    components: { selectTab, EmptyData, Title },
    setup (props) {
      const { stateCommon, params } = useState()
      const { proxy } = getCurrentInstance()
      const dataOverviewScoped = ref(null)
      const target = ref(null)
      const myChart = ref(null)
      const option = ref(null)
      const isSingle = false
      const state = reactive({
        saleInfo: {
          largeSales: undefined,
          totalSingleProfit: undefined,
          totalProfit: undefined,
          valueAddSingleProfit: undefined,
          totalProfitUnit: undefined,
          largeSalesUnit: undefined,
          largeSalesJump: undefined,
        },
        tabsOptions: [],
        currentTab: 'largeCount',
        loading: false,
        statistics: [],
        backP: {},
        cacheP: {},
        showBaseValue: false,
        baseValue: [
          {
            key: 'workdayBaseValue',
            label: '工作日基准值',
            value: 0,
          },
          {
            key: 'weekendBaseValue',
            label: '节假日基准值',
            value: 0,
          },
        ],
      })

      const legendOptions = ref([
        { title: '', borderColor: '', value: false },
        { title: '', borderColor: '', marginLeft: '20px', value: false },
      ])

      watch(() => stateCommon.needRefresh, (val) => {
        if (val) {
          getData(state.cacheP)
        }
      }, { immediate: true })

      const getUnit = function (array) {
        let unit = '元'
        const allData = [].concat(...array.map((d) => d.data))
        if (
          allData.find((v) => new BigNumber(v.value).comparedTo(10000) !== -1)
        ) {
          unit = '万'
        } else if (allData.find((v) => new BigNumber(v.value).comparedTo(1000) !== -1)) {
          unit = '千'
        }
        return unit
      }

      const formatterData = function (val, unit) {
        const div = unit === '万' ? 10000 : 1000
        const cachaN = new BigNumber(val)?.div(div)
        const num = +cachaN?.toFixed(1)
        return num
      }

      async function getData (par = {}) {
        const { currentTab } = state
        const p = {
          ...params.value,
          floorId: 2,
          itemKey: currentTab,
          ...par,
          isSingle,
        }
        state.cacheP = { ...par }
        state.statistics = []
        myChart.value = null
        state.loading = true
        const res = await getFloorData(p)
        state.loading = false
        if (res) {
          const { floorList } = res.data
          const {
            data: { statistics = [] },
            data,
            tabs = [],
          } = floorList[0]
          state.tabsOptions = tabs
          if (currentTab === 'largeCount') {
            state.showBaseValue = !isNaN(data.workdayBaseValue) && !isNaN(data.weekendBaseValue)
            if (state.showBaseValue) {
              state.baseValue.map((d) => {
                const { key } = d
                d.value = data[key]
              })
            }
          }
          const currentTabO = state.tabsOptions.find(
            (d) => d.value === currentTab
          )
          const { timeDimension = 1 } = p
          if (timeDimension === 1) {
            state.statistics = [
              {
                item: currentTabO.title,
                data: statistics,
              },
            ]
          } else {
            state.statistics = statistics
          }

          if (!statistics?.length) {
            myChart.value = null
            return
          }
          option.value = cloneDeep(
            dataOverviewOption(state.statistics, timeDimension, currentTab)
          )
          const unit =
            currentTab !== 'largeCount' ? getUnit(state.statistics) : ''
          option.value.yAxis.name =
            currentTab === 'largeCount'
              ? currentTabO.title
              : `${currentTabO.title}(${unit})`
          option.value.xAxis.data = state.statistics[0].data.map((it) => it.item)
          state.statistics.map((d, i) => {
            const serie = option.value.series[i]
            const { item, data } = d
            // 查询维度总毛利且没有环比,serieName加上单位
            serie.name =
              currentTab !== 'largeCount' && state.statistics.length === 1
                ? `${item}(${unit})`
                : item
            serie.data =
              data.map((it, index) => {
                let position = index % 2 === 0 ? 'top' : 'bottom'
                if (state.statistics.length > 1) { // 两条折线，数字大的在上，数字小的在下,相等一上一下
                  const otherValue = state.statistics[i === 0 ? 1 : 0]?.data[index]?.value || 0
                  const compared = new BigNumber(+it.value).comparedTo(otherValue)
                  position = (compared === 1 ? 'top' : compared === 0 ? (i === 0 ? 'top' : 'bottom') : 'bottom')
                }
                return {
                  value: currentTab === 'largeCount' || (unit !== '万' && unit !== '千') ? +new BigNumber(+it.value).toFixed(1) : formatterData(+it.value, unit),
                  unit,
                  label: {
                    show: true,
                    position,
                    offset: [0, position === 'top' ? 5 : -5],
                    color:
                      it.valueColor === '#F21C1C'
                        ? it.valueColor
                        : dataOverviewColor[i][0],
                  },
                  fontWeight: it.valueColor === '#F21C1C' ? 'bold' : 'normal'
                }
              })
          })
          const { series } = option.value
          // const allData = [].concat(...series.map((d) => d.data))
          // const interval = new BigNumber(Math.max(
          //   ...allData.map((d) => (typeof d === 'object' ? d.value : d))
          // ) / 5).toNumber()
          // option.value.yAxis.interval = currentTab === 'largeCount' ? Math.ceil(new BigNumber(interval).toNumber()) : +new BigNumber(interval).toFixed(1)
          // option.value.yAxis.max = currentTab === 'largeCount' ? Math.ceil(new BigNumber(interval * 5.5).toNumber()) : +new BigNumber(interval * 5.5).toFixed(1)
          if (series.length > 1) {
            series.map((d, i) => {
              legendOptions.value[i].title = d.name
              legendOptions.value[i].borderColor = dataOverviewColor[i][0]
              legendOptions.value[i].value = true
            })
          }
          // 日维度下默认选中8点至24点
          if (timeDimension === 1) {
            const timeList = state.statistics[0].data.map((d) =>
              new BigNumber(d.item.replace('时', '')).toNumber()
            )
            const index = timeList.findIndex((d) => d === 8)
            option.value.dataZoom[0].startValue = index === -1 ? 0 : index
            option.value.dataZoom[0].endValue = timeList.length - 1
            delete option.value.dataZoom[0].start
            delete option.value.dataZoom[0].end
          }
          nextTick(() => {
            if (!myChart.value) {
              initChart()
            } else {
              setOption()
            }
          })
        }
      }
      getData()

      const initChart = function () {
        if (!target.value) return
        myChart.value = echarts.init(target.value)
        setOption()
      }

      async function setOption () {
        myChart.value.clear()
        myChart.value.setOption(option.value)
      }

      function getAllData (time) {
        getData({}, time)
      }

      const legendChange = function (d) {
        const { value } = d
        d.value = !value
        myChart.value.dispatchAction({
          type: 'legendToggleSelect',
          name: d.title,
        })
      }

      return {
        target,
        ...toRefs(state),
        getData,
        isSingle,
        getAllData,
        dataOverviewScoped,
        option,
        legendChange,
        legendOptions,
      }
    },
    render () {
      const {
        saleInfo,
        getData,
        tabsOptions,
        statistics,
        loading,
        isSingle,
        getAllData,
        option,
        legendChange,
        legendOptions,
        baseValue,
        showBaseValue
      } = this
      return (
      <div class="container-box">
        <Title name="大件销量趋势">
          {tabsOptions?.length > 1 ? <selectTab onChange={() => getData({})} options={tabsOptions} v-model={this.currentTab}/> : null }
        </Title>
        <div class="content">
          {statistics?.length ? (
            <div class="chart-box">
              <div class="chart" ref="target"></div>
              {option.series.length > 1 ? (
                <div class="mt-16 mb-16 flex flex-center">
                  {legendOptions.map((it) => (
                    <div
                      class="flex flex-align-center"
                      style={{ marginLeft: it.marginLeft }}
                    >
                      <Checkbox
                        checked={it.value}
                        shape="square"
                        icon-size={14}
                        onClick={() => {
                          legendChange(it)
                        }}
                      />
                      <div
                        class="circle ml-8"
                        style={{ border: `4px solid ${it.borderColor}` }}
                      />
                      <span class="label ml-2">{it.title}</span>
                    </div>
                  ))}
                </div>
              ) : null}
              {this.currentTab === 'largeCount' && showBaseValue ? (
                <div class="mt-16 flex flex-align-center flex-justify-center">
                  {baseValue.map((d, i) => (
                    <div class={{ 'label': true, 'mr-16': i === 0 }}>
                      {d.label}：{d.value}
                    </div>
                  ))}
                </div>
              ) : null}
            </div>
          ) : (
            <EmptyData loading={loading} height={283} />
          )}
        </div>
      </div>
      )
    },
  })
</script>
<style lang="scss" scoped>
@import '../common/mian.scss';
.container-box {
  width: calc((100% - 14px) / 2);
}
.chart-box {
  overflow: hidden;
}
.chart {
  width: 100%;
  height: 255px;
  z-index: 1;
}
.circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  box-sizing: border-box;
}
</style>
