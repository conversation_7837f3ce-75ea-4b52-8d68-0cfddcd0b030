<template>
  <div class="super-table">
    <div v-if="columns.length" class="relative full-width label-box" :style="{ height: 'auto', fontSize: fontSize, paddingBottom: showTotal ? '36px' : '0px' }">
      <div @scroll="whileScroll" ref="tableContent" id="tableContent" :class="needScroll ? 'scroll' : 'no-scroll'" class="scroll-y full-width full-height">
        <table class="full-width">
          <thead class="full-width thead">
            <th id="first" class="border-r box">
              <div style="opacity: 1" class="thead-first-col" :style="{ width: columns[0].width || 'auto' }">
                <span v-html="columns[0].title"></span>
              </div>
            </th>
            <th style="opacity: 1" class="th border-r box" v-for="item in column" :key="item.key">
              <div :style="{ width: item.width || 'auto' }">
                <span v-html="item.title"></span>
              </div>
            </th>
          </thead>
          <tbody v-if="dataSource.length" ref="tbody">
            <tr class="tr body-item" v-for="(it, index) in dataSource" :key="index">
              <td style="opacity: 0" :class="{ high: !!it.high, low: !!it.low }">
                <div class="texts">
                  <span style="text-align: center">
                    <span v-if="isObj(it[columns[0].key])">
                      {{ it[columns[0].key] }}
                    </span>
                    <span :style="{ color: it[columns[0].key].color || '#333333' }" v-else>{{ it[columns[0].key].value }}</span>
                  </span>
                </div>
              </td>
              <td :style="{ color: title.color || '' }" :class="{ high: !!it.high, low: !!it.low }" v-for="title in column" :key="title.key" @click="goDetail(title.link, it, it[title.key])">
                <div class="texts">
                  <span style="text-align: center">
                    <span v-if="isObj(it[title.key])">
                      {{ it[title.key] }}
                    </span>
                    <span :style="{ color: it[title.key].color || '#333333' }" v-else>{{ it[title.key].value }}</span>
                  </span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div ref="top" class="top">
        <table class="full-width">
          <thead class="full-width thead" ref="thead">
            <th style="opacity: 0">
              <div :style="{ width: firstWidth || 'auto' }" class="thead-first-col"></div>
            </th>
            <th v-for="(item, index) in column" :key="item.key">
              <div :style="{ width: widthArr[index] }" class="titles border-b"><span v-html="item.title"></span></div>
            </th>
          </thead>
        </table>
      </div>

      <div ref="bottom" class="bottom" v-if="showTotal && dataSource.length">
        <table class="full-width">
          <tfoot>
            <tr ref="tfoot">
              <td style="opacity: 0">
                <div :style="{ width: firstWidth || 'auto' }" class="footer">合计</div>
              </td>
              <td class="border-t" v-for="(item, index) in column" :key="item.key">
                <div class="footer" :style="{ width: widthArr[index] }">
                  <div class="content">{{ total[item.key] }}</div>
                  <div class="border"></div>
                </div>
              </td>
            </tr>
          </tfoot>
        </table>
      </div>

      <div v-if="dataSource[0]" ref="left" class="left white-bg">
        <table>
          <thead>
            <th :style="{ width: firstWidth || 'auto' }">
              <div class="thead-first-col"></div>
            </th>
          </thead>
          <tbody ref="leftTbody">
            <tr v-for="(it, index) in dataSource" :key="index" class="body-item">
              <td :style="{ color: columns[0].color || '', width: firstWidth || '', height: firstHeight[index] || '' }" class="border-r border-b" style="box-sizing: border-box" :class="{ high: !!it.high, low: !!it.low }">
                <div @click="goDetail(columns[0].link, it, it[columns[0].key])" class="texts">
                  <div style="text-align: center" :style="{ fontSize: fontSize }">
                    <span style="text-align: center">
                      <span v-if="isObj(it[columns[0].key])">
                        {{ it[columns[0].key] }}
                      </span>
                      <span :style="{ color: it[columns[0].key].color || '#333333' }" v-else>{{ it[columns[0].key].value }}</span>
                    </span>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div v-if="columns[0]" class="left bg-fa">
        <table>
          <thead ref="leftThead">
            <th :style="{ width: firstWidth || 'auto' }" class="color-g">
              <div class="thead-first-col border-b" :style="{ fontSize: '12px' }">
                <span v-html="columns[0].title"></span>
              </div>
            </th>
          </thead>
        </table>
      </div>

      <div v-if="showTotal && dataSource.length" class="bottom">
        <table>
          <tfoot>
            <tr>
              <td class="border-t">
                <div class="footer" :style="{ width: firstWidth || 'auto' }">
                  <div class="content">合计</div>
                  <div class="border"></div>
                </div>
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>

    <div v-if="dataSource.length && visible && loadMore" class="load flex full-width flex-center">
      <div class="flex flex-align-center">
        <spinner type="snake" :color="color" :size="14"></spinner>
        <span class="margin-left">{{ text }}</span>
      </div>
    </div>
    <div v-if="!dataSource.length" class="full-width empty flex flex-center">
      <van-empty description="暂无数据" />
    </div>
  </div>
</template>

<script>
  import { Empty } from 'vant'
  import { Spinner } from 'mint-ui'
  import { throttle } from '~/util/common'
  import Vue from 'vue'
  import lodash from 'lodash'
  Vue.use(Empty)
  export default {
    name: 'super-table',
    components: { Spinner },
    props: {
      columns: {
        type: [Array],
        default: () => []
      },
      dataSource: {
        type: [Array],
        default: () => []
      },
      current: {
        type: [Number],
        default: 0
      },
      pages: {
        type: [Number],
        default: 0
      },
      topHeight: {
        type: [Number],
        default: 52
      },
      text: {
        type: [String],
        default: '正在加载...'
      },
      color: {
        type: [String],
        default: '#3caaff'
      },
      fontSize: {
        type: [String],
        default: '14px'
      },
      needScroll: {
        type: [Boolean],
        default: false
      },
      showTotal: {
        type: [Boolean],
        default: false
      },
      total: {
        type: [Object],
        default: () => {}
      }
    },
    data () {
      this.change = throttle(this.change, 1500)
      return {
        scrollTop: 0,
        scrollDirection: '',
        widthArr: [],
        firstHeight: [],
        firstWidth: '',
        firstTrigger: false, // 载入时的触发,
        loadMore: false
      }
    },
    mounted () {
      this.calculate()
      const self = this
      self.$watch('scrollTop', function () {
        if (self.firstTrigger) {
          return
        }
        const tag = document.getElementById('tableContent')
        const clientHeight = tag.clientHeight
        const scrollHeight = tag.scrollHeight
        if (clientHeight + self.scrollTop === scrollHeight) {
          self.firstTrigger = true
          if (!this.visible) return
          self.loadMore = true
          self.change()
        }
      })
    },
    watch: {
      columns: function () {
        if (!this.dataSource.length) return
        this.calculate()
      },
      dataSource () {
        if (!this.dataSource.length) return
        this.calculate()
      }
    },
    computed: {
      visible () {
        return this.current - this.pages < 0
      },
      column () {
        if (this.columns.length) {
          const cache = lodash.cloneDeep(this.columns)
          return cache.slice(1, this.columns.length)
        } else {
          return []
        }
      }
    },
    methods: {
      calculate () {
        if (this.columns && this.columns.length) {
          this.$nextTick(() => {
            this.widthArr = []
            this.firstHeight = []
            // 获取主表的表头每个单元格的宽度赋值给头部覆盖表格
            document.querySelectorAll('.th').forEach((item) => {
              this.widthArr.push(item.getBoundingClientRect().width + 'px')
            })
            // 获取主表的第一列每行高度
            document.querySelectorAll('.tr').forEach((item) => {
              this.firstHeight.push(item.getBoundingClientRect().height + 'px')
            })
            this.firstWidth = ''
            const cache = document.getElementById('first')
            this.firstWidth = cache.getBoundingClientRect().width + 'px'
          })
        }
      },
      whileScroll (e) {
        this.loads()
        throttle(this.scroll(e), 20)
      },
      goDetail (link, item, value) {
        // 路由跳转
        if (link) {
          if (typeof value === 'string' || (typeof value !== 'string' && value.isLink)) {
            this.$emit('goDetail', link, item, value)
          }
        }
      },
      changeScrollLeft (e) {
        this.$refs.top.style.transform = `translateX(${-e.target.scrollLeft}px)`
        if (this.showTotal && this.dataSource.length) {
          this.$refs.bottom.style.transform = `translateX(${-e.target.scrollLeft}px)`
        }
      },
      scroll (e) {
        this.changeScrollLeft(e)
        if (this.dataSource.length) {
          this.$refs.left.style.transform = `translateY(${-e.target.scrollTop}px)`
        }
      },
      loads () {
        const self = this
        const tag = document.getElementById('tableContent')
        const scrollTop = tag.scrollTop
        const clientHeight = tag.clientHeight
        const scrollHeight = tag.scrollHeight
        if (scrollTop > self.scrollTop) {
          // 向下滚
          self.scrollDirection = 'down'
          if (clientHeight + scrollTop === scrollHeight) {
            self.loadMore = true
            self.change()
          }
        } else {
          self.scrollDirection = 'up'
        }
        self.scrollTop = scrollTop
      },
      change () {
        if (this.visible) {
          this.$emit('load')
        }
        this.loadMore = false
      },
      isObj (o) {
        return typeof o !== 'object'
      }
    }
  }
</script>

<style scoped lang="less">
#tableContent::-webkit-scrollbar { display: none;}
.super-table {
  td {
    padding: 0 10px;
  }
  tfoot{
  td{
    padding: 0 !important;
  }
}
}

.high {
  background: rgba(11, 190, 105, 0.1);
  color: #0bbe69;
}
.low {
  background: rgba(241, 86, 67, 0.1);
  color: #f15643;
}
.label-box {
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  //height: calc(100vh - 24px);
}
.top {
  pointer-events: none;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
}
.padd {
  padding: 0 20px;
}
.bg-fa {
  background: #fff;
}
.load {
  height: 36px;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 10;
  background: #ffffff;
}
.empty {
  position: fixed;
  top: 30vh;
  left: 0;
}
.content {
  width: 100px;
  text-align: center;
}
.texts {
  width: 100%;
  min-height: 36px;
  align-items: center;
  display: flex;
  justify-content: center;
  box-sizing: border-box;
}
.titles {
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  box-sizing: border-box;
}
.thead {
  background: #fff;
  > th {
    color: #9C9C9C;
    height: 36px;
    font-size: 12px;
  }
  > th:last-child {
    border-right: none;
  }
}
.border-r {
  // border-right: 1px solid #ffffff;
}
.border-b {
  // border-bottom: 1px solid #ffffff;
}
.tr {
  > td {
    font-size: 12px;
    // border-right: 1px solid #ffffff;
    // border-bottom: 1px solid #ffffff;
  }
  > td:last-child {
    //border-right: none;
  }
}
.scroll {
  overflow-x: scroll;
}
.no-scroll {
  overflow-x: hidden;
}
.scroll-y {
  overflow-y: scroll;
}
.thead-first-col {
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  white-space: nowrap;
}
.left {
  //pointer-events: none;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
}
.bottom {
  position: fixed;
  left: 0;
  bottom: 0;
  background: #ffffff;
}
.color-g {
  color: #9C9C9C;
}
.footer {
  min-height: 36px;
  display: flex;
  width: 100%;
  justify-content: space-between;
  .content {
    width: calc(100%);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .border {
    width: 1px;
    height: 36px;
    background: #ebebeb;
    margin-right: -0.5px;
  }
}
.border-t {
  // border-top: 1px solid #ffffff;
}
.box {
  box-sizing: border-box;
}
.body-item {
  background: #fff;
  &:nth-child(2n - 1) {
    background: #F7F8FA;
  }
}
</style>
