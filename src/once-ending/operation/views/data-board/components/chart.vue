<script lang="jsx">
  import { defineComponent, ref, onMounted, watch } from 'vue'
  import * as echarts from 'echarts5'
  import { shopCommonEchartDefaultOption } from '../constants'
  import { cloneDeep } from 'lodash'

  export default defineComponent({
    props: {
      data: {
        type: Array,
        default: () => [],
      },
      unit: {
        type: String,
        default: '%',
      },
    },
    setup (props) {
      const target = ref(null)
      const myChart = ref(null)
      const currentItem = ref({})
      const option = ref(cloneDeep(shopCommonEchartDefaultOption))
      watch(
        () => props.data,
        (val) => {
          option.value.series[0].data = val
          if (!myChart.value) {
            initChart(val)
          } else {
            setOption()
          }
        }
      )
      const initChart = function (data) {
        myChart.value = echarts.init(target.value)
        option.value.series[0].data = data || props.data
        myChart.value.on('click', function (e) {
          if (e.name === currentItem.value.name) return
          myChart.value.dispatchAction({
            type: 'downplay',
            name: currentItem.value.name,
          })
          myChart.value.dispatchAction({ type: 'highlight', name: e.name })
          currentItem.value = { value: e.value, name: e.name }
        })
        setOption()
      }

      const setOption = function () {
        // 会存在setOption后还会有上次数据,每次都clear一次
        myChart.value.clear()
        myChart.value.setOption(option.value)
        if (!props.data.length) return
        const item = props.data.find(d => `${d.value}` !== '0')
        myChart.value.dispatchAction({
          type: 'highlight',
          name: item ? item.name : props.data[0].name,
        })
        currentItem.value = item || props.data[0]
      }

      onMounted(() => {
        initChart()
      })
      return {
        target,
        currentItem,
      }
    },
    render () {
      const { data, unit } = this
      return (
      <div class="content flex flex-align-center">
        <div class="chart-box relative">
          <div class="chart" ref="target"></div>
          <div class="item flex flex-center flex-col absolute">
            {/* <div class="title">{currentItem.name}</div>
            <div class="value">{`${currentItem.value}${unit}`}</div> */}
          </div>
        </div>
        <div class="legend-box">
          {data.map((d, i) => (
            <div
              class={[
                `legend-item flex flex-align-center flex-justify-between ${
                  i !== 0 ? ' mt-16' : ''
                }`,
              ]}
            >
              <div class="flex flex-align-center mr-12">
                <div
                  class="dot"
                  style={{ border: `4px solid ${d.itemStyle.color}` }}
                ></div>
                <div class="title">{d.name}</div>
              </div>
              <div class="value">
                {d.value}
                {unit}
              </div>
            </div>
          ))}
        </div>
      </div>
      )
    },
  })
</script>
<style lang="scss" scoped>
.content {
  padding: 4px 0 10px;
  box-sizing: border-box;
  .chart-box {
    width: 156px;
    height: 156px;
    .chart {
      width: 100%;
      height: 100%;
      z-index: 1;
      position: inherit;
    }
    .item {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 0;
      .title {
        font-size: 12px;
        font-weight: 400;
        color: #9c9c9c;
        line-height: 12px;
      }
      .value {
        font-size: 20px;
        font-weight: 600;
        color: #333333;
        line-height: 20px;
        margin-top: 4px;
      }
    }
  }
  .legend-box {
    margin-left: 60px;
    width: 130px;
    height: 125px;
    .legend-item {
      .dot {
        width: 4px;
        height: 4px;
        border-radius: 50%;
      }
      .title {
        font-size: 12px;
        font-weight: 400;
        color: #828282;
        line-height: 12px;
        margin-left: 4px;
      }
      .value {
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        line-height: 12px;
      }
    }
  }
}
</style>
