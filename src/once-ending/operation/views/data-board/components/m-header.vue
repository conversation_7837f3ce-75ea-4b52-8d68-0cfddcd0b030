<script lang="jsx">
  import { defineComponent, getCurrentInstance, onActivated, computed } from 'vue'
  import { NiImg } from '@jiuji/nine-ui'
  import { icons } from '../constants'
  import { createAppHeaderState } from '~/pages/operation/medium/common/useAppHeader'

  export default defineComponent({
    name: 'm-header',
    components: { NiImg },
    props: {
      title: {
        type: [String],
        default: ''
      },
      headerHeight: {
        type: [Number],
        default: 44
      }
    },
    setup () {
      const { proxy } = getCurrentInstance()
      const navigationHeight = computed(() => proxy.$store.state.navigationHeight)
      const { setAppHeader } = createAppHeaderState()
      onActivated(() => { setAppHeader(proxy.$route) })
      setAppHeader(proxy.$route)
      function filter () {
        proxy.$emit('filter')
      }
      return {
        navigationHeight,
        setAppHeader,
        filter
      }
    },
    render () {
      const { navigationHeight, headerHeight, title, filter } = this
      const totalHeight = (+(navigationHeight || 0) + headerHeight) + 'px'
      return <div style={{ height: totalHeight }}>
        <div style={{ height: totalHeight }}/>
        <div class="box" style={{ height: totalHeight, zIndex: 99999999 }}>
          <div class="white-bg" style={{ height: navigationHeight + 'px' }} />
          <div class="header-box flex flex-center" style={{ height: headerHeight + 'px' }}>
            <div class="back flex flex-center" onClick={() => { this.$router.back() }}>
              <NiImg class="img" src={icons.back}/>
            </div>
            <span>{ title || this.$route.meta.title }</span>
            <div class="filter flex flex-center" onClick={filter}>
              <NiImg class="img" src={icons.filter}/>
            </div>
          </div>
        </div>
    </div>
    }
  })
</script>
<style scoped lang="scss">
.box {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
}
.header-box {
  text-align: center;
  position: relative;
  font-size: 18px;
  font-weight: 500;
  padding: 0 10px;
  box-sizing: border-box;
  background: #fff;
}
.back {
  position: absolute;
  left: 12px;
  top: 0;
  height: 100%;
}
.filter {
  position: absolute;
  right: 10px;
  top: 0;
  height: 100%;
}
.img {
    width: 22px;
    height: 22px;
  }
</style>
