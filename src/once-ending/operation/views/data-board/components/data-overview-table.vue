<script lang="jsx">
  import { defineComponent, reactive, toRefs, ref, watch, inject, getCurrentInstance } from 'vue'
  import selectTab from './select-tab.vue'
  import { getFloorData, icons, exportXlsx } from '../constants.js'
  import EmptyData from './empty-data.vue'
  import Title from './title.vue'
  import { useState } from '../hooks/useState.js'
  import { NiImg, NiTable, NiListPage } from '@jiuji/nine-ui'
  import api from '@/operation/api/data-board'

  const originColumns = [
    {
      title: '区域',
      dataIndex: 'item',
      width: 140,
      fixed: 'left'
    },
    {
      title: '大件销量',
      dataIndex: 'largeCount',
      width: 250
    },
    {
      title: '总毛利(环比，同比)',
      dataIndex: 'totalProfit',
      width: 250
    },
    {
      title: '店均单产(环比，同比)',
      dataIndex: 'totalProfitStoreAvg',
      width: 250
    },
    {
      title: '总单毛(环比，同比)',
      dataIndex: 'totalSingleProfit',
      width: 250
    },
    {
      title: '大件单毛(环比，同比)',
      dataIndex: 'largeSingleProfit',
      width: 250
    },
    {
      title: '增值单毛(环比，同比)',
      dataIndex: 'valueAddSingleProfit',
      width: 250
    },
    {
      title: '配件利润比(环比，同比)',
      dataIndex: 'accessoryProfitRatio',
      width: 250
    },
    {
      title: '服务利润比(环比，同比)',
      dataIndex: 'selfServiceProfitRatio',
      width: 250
    },
    {
      title: '回收跟机率(环比，同比)',
      dataIndex: 'recoverFollowRate',
      width: 250
    },
    {
      title: '智能利润比(环比,同比)',
      dataIndex: 'smartProfitRatio',
      width: 250
    },
    {
      title: '维修配比(环比,同比)',
      dataIndex: 'recoverFollowRate',
      width: 250
    },
    {
      title: '运营商预估佣金(环比,同比)',
      dataIndex: 'repairRatio',
      width: 250
    },
    {
      title: '良品销量(环比,同比)',
      dataIndex: 'goodProductsCount',
      width: 250
    },
    {
      title: '非五星率(环比,同比)',
      dataIndex: 'nonFiveStarRate',
      width: 250
    },
    {
      title: '投诉扣分(环比,同比)',
      dataIndex: 'tousuDeduct',
      width: 250
    }
  ]

  export default defineComponent({
    components: { selectTab, EmptyData, Title, NiImg, NiTable, NiListPage },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const { stateCommon, params } = useState()
      const tableDimension = inject('tableDimension')
      const state = reactive({
        loading: false,
        dataSource: [],
        pagination: {
          current: 1,
          pageSize: 10,
          total: 0,
          pageSizeOptions: ['10', '20', '50', '100'],
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: total => `总共 ${total} 条`,
        },
        cacheParams: {}
      })

      watch(() => stateCommon.needRefresh, (val) => {
        if (val) {
          state.pagination.current = 1
          getData()
        }
      }, { immediate: true })
      async function getData () {
        state.loading = true
        state.dataSource = []
        const { current, pageSize } = state.pagination
        const p = {
          ...params.value,
          floorId: 1,
          tableDimension: tableDimension.value,
          current,
          size: pageSize
        }
        state.cacheParams = { ...p }
        const res = await getFloorData(p)
        state.loading = false
        if (res) {
          const { floorList } = res.data
          const { statistics, page: { total } } = floorList[0]?.data
          state.dataSource = statistics || []
          state.pagination.total = total || 0
        }
      }
      getData()

      function tableChange (newP) {
        Object.assign(state.pagination, newP)
        getData()
      }

      const exportLoading = inject('exportLoading')
      async function exportTable () {
        if (!state.dataSource?.length) {
          return proxy.$message.warning('暂无数据可导出')
        }
        exportLoading.value = true
        await exportXlsx({
          method: 'post',
          url: api.exportUrl(1),
          params: state.cacheParams,
          fileName: '数据总览'
        })
        exportLoading.value = false
      }

      return {
        ...toRefs(state),
        getData,
        tableChange,
        exportTable
      }
    },
    computed: {
      columns () {
        return originColumns.map(item => {
          const cacheItem = { ...item }
          item.dataIndex !== 'item' && (cacheItem.customRender = this.scopedSlotsMap.get('special')(item.dataIndex))
          return cacheItem
        })
      }
    },
    methods: {
      getShow (type, dataIndex, record) {
        const key = type + dataIndex.slice(0, 1).toUpperCase() + dataIndex.slice(1)
        const trendsKey = key + 'Trends'
        return <span class="nowrap" style={{ color: record[trendsKey] === 'down' ? '#F21C1C' : record[trendsKey] === 'up' ? '#52CC7A' : '#333333' }}>{record[key]}</span>
      }
    },
    data () {
      return {
        scopedSlotsMap: new Map([
          [
            'special',
            (dataIndex) => {
              return (text, record) => <span>
                {text}（{this.getShow('chain', dataIndex, record)}，{this.getShow('yoy', dataIndex, record)}）
              </span>
            }
          ]
        ])
      }
    },
    render () {
      const {
        loading,
        dataSource,
        columns,
        pagination,
        tableChange
      } = this
      return <div class="contents">
          { dataSource?.length ? <a-table
              columns={columns}
              setting={false}
              rowKey={(r, i) => i}
              scroll={{ y: 228, x: 1500 }}
              bordered={false}
              pagination={pagination}
              ignoreAffix={true}
              onChange={tableChange}
              dataSource={dataSource}/> : <EmptyData height={339} loading={loading}/> }
        </div>
    }
  })
</script>
<style lang="scss" scoped>
@import '../common/mian.scss';
.contents {
  padding: 0 18px 20px 18px;
}
:deep(.ant-table-thead > tr > th ) {
  background: #F7F8FA;
  font-weight: 600;
  color: #333333;
}
:deep(.ant-table-pagination.ant-pagination) {
  margin: 25px 0 0 0;
}
</style>
