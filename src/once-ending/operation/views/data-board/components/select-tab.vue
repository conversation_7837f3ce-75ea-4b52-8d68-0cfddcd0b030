<script lang="jsx">
  import { defineComponent, watch, ref, getCurrentInstance } from 'vue'
  import { NiImg } from '@jiuji/nine-ui'
  import { icons } from '../constants'

  export default defineComponent({
    components: {
      NiImg
    },
    props: {
      value: {
        type: [String, Number]
      },
      options: {
        type: Array,
        default: () => []
      },
      showBack: {
        type: Boolean,
        default: false
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const valueLocal = ref(undefined)
      watch(() => props.value, (val) => {
        valueLocal.value = val
      }, { immediate: true })

      function changeValue (val) {
        valueLocal.value = val
        proxy.$emit('input', val)
        proxy.$emit('change', val)
      }
      return {
        valueLocal,
        changeValue
      }
    },
    render () {
      const { options, valueLocal, changeValue, showBack } = this
      return <div class="flex">
            { showBack ? <div class="flex flex-align-center margin-right pointer" onClick={() => { this.$emit('back') }}>
              <NiImg src={icons.smallBack} class="back mt-2"/>
              <span class="label">返回</span>
            </div> : null }
            <div class="tabs flex">
              { options?.map(it => <div onClick={() => changeValue(it.value)} class={['flex flex-center text-item pointer', valueLocal === it.value ? 'checked' : 'normal']}>
              <span>{ it.title }</span>
            </div>) }
            </div>
          </div>
    }
  })
</script>
<style scoped lang="scss">
@import '../common/mian.scss';
.back {
  width: 12px;
  height: 12px;
}
.tabs {
  height: 28px;
  background: #F0F2F5;
  border-radius: 8px;
  padding: 2px;
  box-sizing: border-box;
  .text-item {
    min-width: 40px;
    height: 24px;
    font-size: 12px;
    padding: 0 8px;
    box-sizing: border-box;
    max-width: 100px;
    span {
      white-space: nowrap; /* 不换行 */
      overflow: hidden;    /* 隐藏超出部分 */
      text-overflow: ellipsis; /* 显示省略号 */
    }
  }
  .checked {
    background: #FFFFFF;
    border-radius: 6px;
    color: #1890FF;
    font-weight: 600;
  }
  .normal {
    font-weight: 400;
    color: #333333;
  }
}
</style>
