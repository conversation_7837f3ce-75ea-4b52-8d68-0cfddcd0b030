<script lang="jsx">
  import { defineComponent, reactive, toRefs, ref, nextTick, watch, inject } from 'vue'
  import chartPie from './chart-pie.vue'
  import { bulkyTargetFinishColorList, bulkyTargetFinishOption, getFloorData } from '../constants.js'
  import * as echarts from 'echarts5'
  import selectTab from './select-tab.vue'
  import EmptyData from './empty-data.vue'
  import { useState } from '../hooks/useState.js'
  import Title from './title.vue'

  export default defineComponent({
    components: { chartPie, selectTab, EmptyData, Title },
    setup () {
      const { stateCommon, params } = useState()
      const target = ref(null)
      const myChart = ref(null)
      const legendChecked = ref([])
      const option = ref()
      const state = reactive({
        dataSource: [],
        loading: false,
      })
      const tabsOptions = inject('tabsOptions')
      const currentTab = inject('currentTab')

      watch(() => stateCommon.needRefresh, (val) => {
        if (val) {
          getDataSource()
        }
      }, { immediate: true })

      async function getDataSource () {
        state.loading = true
        state.dataSource = []
        myChart.value = null
        const res = await getFloorData({
          ...params.value,
          floorId: 3,
          tabId: currentTab.value
        })
        state.loading = false
        if (res) {
          const { floorList } = res.data
          floorList[0].data.statistics = floorList[0].data.statistics.slice(0, 5)
          const { statistics, tabs } = floorList[0]?.data
          tabsOptions.value = floorList[0]?.tabs || []
          console.log('tabsOptions.value', tabsOptions.value)
          state.dataSource = statistics?.map((it, i) => {
            const trendItem = {
              name: it.item,
              type: 'line',
              smooth: true,
              symbol: it?.sales?.length === 1 ? 'circle' : 'none',
              lineStyle: { color: bulkyTargetFinishColorList[i].borderColor },
              areaStyle: { color: bulkyTargetFinishColorList[i].areaColor },
              itemStyle: { color: bulkyTargetFinishColorList[i].borderColor },
              data: []
            }
            trendItem.data = it?.sales?.map(o => o.value?.replace('%', '') || 0)
            return trendItem
          })
          option.value = bulkyTargetFinishOption(statistics)
          legendChecked.value = statistics?.map(it => it.item)
          option.value.xAxis.data = statistics[0]?.sales?.map(it => it.label) || []
          option.value.series = state.dataSource
          console.log('option.value.series', option.value.series)
          nextTick(() => {
            if (!myChart.value) {
              initChart()
            } else {
              setOption()
            }
          })
        }
      }
      getDataSource()

      const initChart = function () {
        if (!target.value) return
        myChart.value = echarts.init(target.value)
        setOption()
      }

      const setOption = function () {
        // 会存在setOption后还会有上次数据,每次都clear一次
        myChart.value.clear()
        myChart.value.setOption(option.value)
        dispatchAction('legendAllSelect')
      }

      const legendChange = function (d) {
        if (legendChecked.value.includes(d.name)) {
          const index = legendChecked.value.findIndex((k) => k === d.name)
          legendChecked.value.splice(index, 1)
          dispatchAction('legendUnSelect', d.name)
        } else {
          legendChecked.value.push(d.name)
          dispatchAction('legendSelect', d.name)
        }
      }

      const dispatchAction = function (type, name) {
        const o = {
          type,
        }
        if (name) o.name = name
        myChart.value.dispatchAction(o)
      }

      return {
        target,
        legendChecked,
        ...toRefs(state),
        legendChange,
        getDataSource
      }
    },
    render () {
      const {
        dataSource,
        loading,
        legendChecked,
        legendChange,
        getDataSource,
        currentTab
      } = this
      return <div class="content">
          { dataSource.length ? <div>
            <div class="chart-box relative">
              <div class="chart" ref="target"></div>
            </div>
            {currentTab}
            <div class="legend-box mt-18 flex flex-center flex-wrap">
            {dataSource.map((d) => (
              <a-checkbox
                checked={legendChecked.includes(d.name)}
                onClick={() => {
                  legendChange(d)
                }}
              >
              <div style="display: inline-block">
                <div class="flex flex-align-center">
                  <div
                    class="dot flex flex-child-noshrink"
                    style={{ border: `4px solid ${d.lineStyle.color}` }}
                  ></div>
                  <div class="font-12 flex flex-child-noshrink">{d.name}</div>
                </div>
              </div>
              </a-checkbox>
            ))}
          </div>
          </div> : <EmptyData height={283} loading={loading}/> }
        </div>
    }
  })
</script>
<style scoped lang="scss">
@import '../common/mian.scss';
.container-box {
  width: calc((100% - 14px) / 2);
}
.chart-box {
  .chart {
    width: 100%;
    height: 249px;
    z-index: 1;
    position: inherit;
  }
  .legend {
    color: #828282;
    font-size: 12px;
    font-weight: 400;
    top: 10px;
    right: 0;
  }
}
.legend-box {
  box-sizing: border-box;
  height: 16px;
  .dot {
    margin-right: 2px;
  }
}
.up-down {
  width: 10px;
  height: 12px;
  margin-left: 4px;
}
</style>
