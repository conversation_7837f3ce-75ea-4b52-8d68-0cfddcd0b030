<script lang="jsx">
  import { defineComponent, ref, onMounted, watch } from 'vue'
  import * as echarts from 'echarts5'
  import { commonEchartPieDefaultOption, rankBg } from '../constants'
  import { cloneDeep } from 'lodash'

  export default defineComponent({
    props: {
      data: {
        type: Array,
        default: () => [],
      },
      unit: {
        type: String,
        default: '%',
      },
      showOther: {
        type: Boolean,
        default: false,
      },
      isTen: {
        type: Boolean,
        default: false,
      }
    },
    setup (props) {
      const target = ref(null)
      const myChart = ref(null)
      const currentItem = ref({})
      const tenBottomData = ref([])
      const option = ref(cloneDeep(commonEchartPieDefaultOption))
      watch(
        () => props.data,
        (val) => {
          if (!val?.length) return
          option.value.series[0].data = val
          if (!myChart.value) {
            initChart(val)
          } else {
            setOption()
          }
        },
      )
      const initChart = function (data) {
        if (!target.value) return
        myChart.value = echarts.init(target.value)
        option.value.series[0].data = data || props.data
        myChart.value.on('click', function (e) {
          console.log('e', e)
          if (e.name === currentItem.value.name) return
          myChart.value.dispatchAction({
            type: 'downplay',
            name: currentItem.value.name,
          })
          myChart.value.dispatchAction({ type: 'highlight', name: e.name })
          currentItem.value = { value: e.value, name: e.name }
        })
        setOption()
      }

      const setOption = function () {
        // 会存在setOption后还会有上次数据,每次都clear一次
        myChart.value.clear()
        myChart.value.setOption(option.value)
        if (!props.data.length) return
        const item = props.data.find(d => `${d.value}` !== '0')
        myChart.value.dispatchAction({
          type: 'highlight',
          name: item ? item.name : props.data[0].name,
        })
        currentItem.value = item || props.data[0]
      }

      onMounted(() => {
        initChart()
      })
      return {
        target,
        currentItem,
        tenBottomData
      }
    },
    render () {
      const { currentItem, data, unit, showOther, isTen } = this
      return (
      <div class="contents full-width">
        <div class=" flex flex-center">
          <div class="chart-box relative">
            <div class="chart" ref="target"></div>
            <div class="item flex flex-center flex-col absolute">
              <div class="title lines-1 flex flex-child-noshrink" style="max-width: 70px">{currentItem.name}</div>
              <div class="value">{`${currentItem.value}${unit}`}</div>
            </div>
          </div>
          <div class={['legend-box', isTen ? 'flex flex-child-grow' : '']}>
          { !isTen ? data.map((d, i) => (
            <div
              class={[
                `legend-item flex flex-align-center ${
                  i !== 0 ? ' mt-16' : ''
                }`,
              ]}
            >
              <div class="flex flex-align-center">
                <div
                  class="dot"
                  style={{ border: `4px solid ${d.borderColor}` }}
                ></div>
                <div class="title lines-1 flex flex-child-noshrink">{d.name}</div>
              </div>
              <div class="value">
                {d.value}
                {unit}
              </div>
              {showOther ? <div class="value">
                {d.otherValue}
              </div> : null}
            </div>
          )) : <div class="flex flex-col flex-wrap ten-box">
            { data.map((d, i) => <div
              class={['items flex flex-align-center', [0, 6].includes(i) ? '' : 'margin-top']}
            style={{
              background: `url("${rankBg[i] || rankBg[3]}") center no-repeat`,
backgroundSize: '100% 100%',
              marginRight: i < 6 ? '30px' : ''
            }}>
            <div class={[i < 3 ? 'rank-top' : 'rank-normal', 'nowrap flex-child-noshrink']}
            style={{ color: d.rankColor }}>{i < 3 ? 'NO.' : ''}{i + 1}</div>
            <div class="dot" style={{ border: `4px solid ${d.borderColor}` }}/>
            <div class="title lines-1 flex-child-noshrink">{d.name}</div>
            <div class="value">{d.value}{unit}</div>
            </div>) }
          </div> }
        </div>
      </div>
      </div>
      )
    },
  })
</script>
<style lang="scss" scoped>
.contents {
  box-sizing: border-box;
  .chart-box {
    width: 200px;
    height: 200px;
    .chart {
      width: 200px;
      height: 200px;
      z-index: 1;
      position: inherit;
    }
    .item {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 0;
      .title {
        font-size: 12px;
        font-weight: 400;
        color: #9c9c9c;
        line-height: 12px;
      }
      .value {
        font-size: 20px;
        font-weight: 600;
        color: #333333;
        line-height: 20px;
        margin-top: 4px;
      }
    }
  }
  .legend-box {
    margin-left: 40px;
    .title {
      font-size: 12px;
      font-weight: 400;
      color: #828282;
      line-height: 12px;
      width: 50px;
    }
    .value {
      min-width: 42px;
      font-size: 12px;
      font-weight: 400;
      color: #333333;
      line-height: 12px;
      white-space: nowrap;
    }
    .legend-item {
    }
    .items {
      height: 32px;
      width: 140px;
      .rank-top {
        font-family: DINAlternate, DINAlternate;
        font-weight: bold;
        width: 39px;
      }
      .rank-normal {
        font-weight: 600;
        color: #828282;
        font-size: 12px;
        margin-left: 21px;
        width: 18px;
      }
    }
    .items-bottom {
      height: 36px;
      margin-right: 15px;
      margin-left: 10px;
      .rank-bottom {
        width: 18px;
        color: #828282;
        font-weight: 600;
      }
    }
  }
}
.ten-box {
  height: 232px;
}
.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
  margin-right: 4px;
}
.mt-4 {
  margin-top: 4px;
}
.half-width {
  width: 50%;
}
.line-12 {
  line-height: 12px;
}
</style>
