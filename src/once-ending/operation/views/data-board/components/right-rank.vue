<script lang="jsx">
  import { defineComponent, reactive, toRefs } from 'vue'
  import { icons } from '../constants'
  import SalesTen from './sales-ten.vue'
  import BulkyPriceProportion from './bulky-price-proportion.vue'

  export default defineComponent({
    components: { SalesTen, BulkyPriceProportion },
    setup () {
      const state = reactive({
        type: 1
      })
      return {
        ...toRefs(state)
      }
    },
    render () {
      const { type } = this
      return <div class="container-boxs">
        <div class="content-box">
          <div class="title flex flex-justify-between flex-align-center">
            <span class="name">{ type === 1 ? '近7天销量top10机型' : '大件价位段占比' }</span>
            <span class="flex flex-align-center pointer" onClick={() => { this.type = type === 1 ? 2 : 1 }}>
              { type === 1 ? '大件价位段占比' : '近7天销量top10机型' }
              <img src={icons.arrowRight} class="arrow-right"/>
            </span>
          </div>
          <div class="box">
            { type === 1 ? <SalesTen/> : <BulkyPriceProportion/> }
          </div>
        </div>
      </div>
    }
  })
</script>
<style scoped lang="scss">
@import '../common/mian.scss';
.container-boxs {
  width: calc((100% - 24px) / 3);
  min-width: 540px;
  background: #fff;
  border-radius: 8px;
  display: flex;
  flex-shrink: 0;
}
.content-box {
  width: 100%;
  height: 100%;
  background: url('../common/box-bg.png') no-repeat;
  background-size: 100% 200px;
}
.box {
  padding: 0 20px;
  min-height: 590px;
}
.title {
  height: 54px;
  padding: 0 20px;
  .name {
    font-size: 18px;
    font-weight: 600;
    color: #FFFFFF;
  }
  .arrow-right {
    width: 14px;
    height: 14px;
  }
}
</style>
