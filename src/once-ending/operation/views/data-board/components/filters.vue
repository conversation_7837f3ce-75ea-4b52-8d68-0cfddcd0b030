<script lang="jsx">
  import { defineComponent, reactive, toRefs, ref, getCurrentInstance, nextTick } from 'vue'
  import { NiAreaSelect, Ni<PERSON>ilter, NiFilterItem } from '@jiuji/nine-ui'
  import moment from 'moment'
  import { icons } from '../constants'
  import { cloneDeep } from 'lodash'
  import api from '@operation/api/data-board.js'
  import { to } from '~/util/common'
  import { useState } from '../hooks/useState.js'

  export default defineComponent({
    components: {
      Ni<PERSON>reaSelect,
      NiFilter,
      NiFilterItem
    },
    setup (props, context) {
      const { proxy } = getCurrentInstance()
      const { stateCommon, refresh } = useState()
      const enums = ref([])
      async function getEnums () {
        const [err, res] = await to(api.getEnums())
        if (err) throw err
        if (res?.code === 0) {
          res.data[0].key = 'attribute'
          const { list } = res.data[3]
          // res.data[3].list = list.concat([{ label: '自定义', value: 4 }])
          enums.value = res?.data || []
        }
      }
      getEnums()

      function getSingleEnums (key) {
        const item = enums.value?.find(it => it.key === key)
        return (key === 'attribute' ? item?.tree : item?.list) || []
      }
      function changeDateType (value) {
        props.screen.timeDimension = value
        getCurrentDate()
        refresh()
      }
      function getCurrentDate (type) {
        const { timeDimension } = stateCommon.screen
        if ([1, 4].includes(timeDimension)) {
          stateCommon.screen.time = timeDimension === 1 ? moment().format('YYYY-MM-DD') : undefined
        }
        timeDimension === 2 && getWeek('default')
        timeDimension === 3 && getMonth('default')
        timeDimension === 5 && getYear('default')
      }

      function changeOneDate (type) {
        const { timeDimension, time } = props.screen
        const unit = timeDimension === 3 ? 'months' : 'days'
        const algorithm = type === 'recoil' ? 'subtract' : 'add'
        const number = timeDimension === 2 ? 7 : 1
        const startTime = Array.isArray(time) ? time[0] : time
        const endTime = Array.isArray(time) ? time[1] : time
        const sTime = moment(startTime)[algorithm](number, unit)
        const eTime = moment(endTime)[algorithm](number, unit)
        const startTimeR = timeDimension === 3 ? sTime.startOf('month').format('YYYY-MM-DD') : sTime.format('YYYY-MM-DD')
        const endTimeR = timeDimension === 3 ? eTime.endOf('month').format('YYYY-MM-DD') : eTime.format('YYYY-MM-DD')
        props.screen.time = Array.isArray(time) ? [startTimeR, endTimeR] : startTimeR
        refresh()
      }

      function getContainer () {
        return document.body
      }

      function getWeek (type, val) {
        const date = type === 'default' ? new Date() : new Date(moment(val._d).format('YYYY-MM-DD'))
        const lastMonday = moment(date).startOf('isoWeek').format('YYYY-MM-DD')
        const lastSunday = moment(date).endOf('isoWeek').format('YYYY-MM-DD')
        stateCommon.screen.time = [lastMonday, lastSunday]
        stateCommon.screen.timeStr = lastMonday + ' ~ ' + lastSunday
      }

      function getMonth (type, val) {
        console.log('val', val)
        const date = type === 'default' ? new Date() : new Date(moment(val + '-01').format('YYYY-MM-DD'))
        const lastMonday = moment(date).startOf('month').format('YYYY-MM-DD')
        const lastSunday = moment(date).endOf('month').format('YYYY-MM-DD')
        stateCommon.screen.time = [lastMonday, lastSunday]
      }

      function getYear (type, val) {
        const date = type === 'default' ? moment().format('YYYY') : val.format('YYYY')
        const yearStart = moment(date).startOf('year').format('YYYY-MM-DD')
        const yearEnd = moment(date).endOf('year').format('YYYY-MM-DD')
        stateCommon.screen.timeStr = yearStart + ' ~ ' + yearEnd
        stateCommon.screen.time = [yearStart, yearEnd]
      }

      return {
        ...toRefs(stateCommon),
        refresh,
        enums,
        getSingleEnums,
        changeDateType,
        changeOneDate,
        getContainer,
        getWeek,
        getMonth,
        getYear,
        getCurrentDate
      }
    },
    render () {
      const {
        enums,
        getSingleEnums,
        screen,
        loading,
        refresh,
        getCurrentDate,
        getWeek,
        getMonth,
        getYear
      } = this
      return <NiFilter
          form={screen}
          fold={false}
          immediate={false}
          loading={loading}
          onFilter={() => refresh()}>
          <ni-filter-item label="地区">
            <NiAreaSelect
            multiple
            maxTagCount={1}
            allow-clear={true}
            placeholder="请选择地区"
            mode={2}
            v-model={screen.areaIds}/>
          </ni-filter-item>
          <ni-filter-item label="时间维度">
            <a-select onChange={() => getCurrentDate()} options={getSingleEnums('timeDimension')} v-model={screen.timeDimension}/>
          </ni-filter-item>
          <ni-filter-item label="时间">
            { screen.timeDimension === 1 ? <a-date-picker
            valueFormat="YYYY-MM-DD"
            placeholder="请选择时间"
            allowClear={false}
            v-model={screen.time}
            /> : screen.timeDimension === 2 ? <div class="week-picker">
            <a-week-picker
            ref="weekPickerRef"
            class="full-width"
            placeholder="请选择时间"
            allowClear={false}
            onChange={(val) => getWeek('', val)}
            >
            <div slot="suffixIcon" class="flex-justify-between" style="display: flex">
              {screen.timeStr}
              <a-icon style="color: rgba(0, 0, 0, 0.25)" type="calendar"/>
            </div>
            </a-week-picker>
              </div>
            : screen.timeDimension === 3 ? <a-month-picker
            placeholder="请选择时间"
            allowClear={false}
            valueFormat="YYYY-MM"
            onChange={(val) => getMonth('', val)}
            value={screen.time ? screen.time[0] : undefined}
            /> : screen.timeDimension === 4 ? <a-range-picker
            valueFormat="YYYY-MM-DD"
            v-model={screen.time}
            /> : <div class="year-picker"><a-date-picker
            placeholder="请选择年份"
            allowClear={false}
            getCalendarContainer={() => document.querySelector('.year-picker')}
            ref="yearPickerRef"
            valueFormat="YYYY"
            mode="year"
            class="full-width"
            onPanelChange={(val) => getYear('', val) }
            value={screen.time?.length ? screen.time[0] : undefined}
            >
            <div slot="suffixIcon" class="flex-justify-between" style="display: flex">
              {screen.timeStr}
              <a-icon style="color: rgba(0, 0, 0, 0.25)" type="calendar"/>
            </div>
            </a-date-picker>
          </div> }
          </ni-filter-item>
          <ni-filter-item label="门店属性">
            <a-select mode="multiple" allowClear placeholder="请选择" v-model={screen.attribute} maxTagCount={1}>
              { getSingleEnums('attribute').map(item => <a-select-opt-group>
                <span slot="label">{item.key}</span>
                { item.children.map(it => <a-select-option key={it.value} value={it.value}>
                  { it.key }
                </a-select-option>) }
              </a-select-opt-group>) }
            </a-select>
          </ni-filter-item>
          <ni-filter-item label="门店类别">
            <a-select
            mode="multiple"
            allowClear
            placeholder="请选择"
            options={getSingleEnums('storeKind')}
            v-model={screen.storeKind}
            maxTagCount={1}/>
          </ni-filter-item>
          <ni-filter-item label="门店类型">
            <a-select
            mode="multiple"
            allowClear
            placeholder="请选择"
            options={getSingleEnums('storeType')}
            v-model={screen.storeType}
            maxTagCount={1}/>
          </ni-filter-item>
      </NiFilter>
    }
  })
</script>
<style scoped lang="scss">
:deep(.week-picker), :deep(.year-picker) {
  .ant-calendar-picker-icon {
  left: 12px;
  z-index: 999;
  background: #fff;
  color: #333;
  width: auto;
  }
  .ant-calendar-input-wrap {
    display: none;
  }
}
</style>
