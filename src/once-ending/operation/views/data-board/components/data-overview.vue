<script lang="jsx">
  import { defineComponent, reactive, toRefs, getCurrentInstance, provide, ref } from 'vue'
  import { icons, areaTypeOptions } from '../constants.js'
  import EmptyData from './empty-data.vue'
  import Title from './title.vue'
  import { NiImg } from '@jiuji/nine-ui'
  import dataOverviewCell from './data-overview-cell.vue'
  import dataOverviewTable from './data-overview-table.vue'

  export default defineComponent({
    components: { dataOverviewCell, dataOverviewTable, Title, NiImg },
    setup () {
      const { proxy } = getCurrentInstance()
      const exportLoading = ref(false)
      const tableDimension = ref(1)
      provide('exportLoading', exportLoading)
      provide('tableDimension', tableDimension)

      const state = reactive({
        type: 1,
      })
      function changeType () {
        state.type = (state.type === 1 ? 2 : 1)
        tableDimension.value = 1
      }
      function changeArea (val) {
        tableDimension.value = val
        proxy.$refs.dataOverviewTable.getData()
      }
      function exportTable () {
        proxy.$refs.dataOverviewTable.exportTable()
      }
      return {
        ...toRefs(state),
        changeType,
        changeArea,
        exportLoading,
        exportTable,
        tableDimension
      }
    },
    render () {
      const {
        type,
        changeType,
        tableDimension,
        changeArea,
        exportLoading,
        exportTable
      } = this
      return <div class="container-box">
        <Title name="数据总览" showBg={true}>
          { type === 2 ? <div class="flex flex-align-center">
            <div class="flex flex-align-center">
              { areaTypeOptions.map(it => <a-checkbox onClick={() => changeArea(it.value)} checked={tableDimension === it.value}>{it.label}</a-checkbox>) }
            </div>
            <a-button size="small" type="primary" class="ml-16" loading={exportLoading} onClick={exportTable}>导出</a-button>
            <NiImg onTap={changeType} class="ml-20 links" src={icons.link}/>
          </div> : <NiImg onTap={changeType} class="links" src={icons.linkChange}/> }
        </Title>
        {type === 1 ? <dataOverviewCell/> : <dataOverviewTable ref="dataOverviewTable" tableDimension={tableDimension}/>}
      </div>
    }
  })
</script>
<style lang="scss" scoped>
@import '../common/mian.scss';
.links {
  width: 16px;
  height: 16px;
  cursor: pointer;
}
:deep(.ant-checkbox + span) {
  padding-left: 4px;
}
</style>
