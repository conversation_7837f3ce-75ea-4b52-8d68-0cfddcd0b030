<script lang="jsx">
  import { defineComponent, reactive, toRefs, watch } from 'vue'
  import { getFloorData } from '../constants'
  import EmptyData from './empty-data.vue'
  import { useState } from '../hooks/useState.js'

  const bigPriceColumns = [
    { title: '价位段', key: 'item', width: '28%', labelLeft: '20px' },
    { title: '销量占比', key: 'salesRatio', width: '17%', textAlign: 'left' },
    { title: '大件DM', key: 'largeDM', width: '17%', textAlign: 'left' },
    { title: '连带DM', key: 'associatedDM', width: '17%', textAlign: 'left' },
    { title: '连带总DM(环比)', key: 'totalDM', width: '21%', textAlign: 'left' },
  ]
  export default defineComponent({
    components: { EmptyData },
    setup () {
      const { stateCommon, params } = useState()
      const state = reactive({
        dataSource: [],
        loading: false
      })
      watch(() => stateCommon.needRefresh, (val) => {
        if (val) {
          getData()
        }
      }, { immediate: true })
      async function getData () {
        state.loading = true
        state.dataSource = []
        const res = await getFloorData({
          ...params.value,
          floorId: 7,
        })
        state.loading = false
        if (res) {
          const { floorList } = res.data
          const { statistics } = floorList[0]?.data
          state.dataSource = statistics?.map((it, i) => ({
            ...it,
          }))
        }
      }
      getData()
      return {
        ...toRefs(state)
      }
    },
    render () {
      const {
        dataSource,
        loading
      } = this
      return <div class="container-box mt-10 ">
        <div class="content" style="paddingBottom: 20px">
          {dataSource.length ? (
          <div class="main">
            <div class="mt-20 flex">
            { bigPriceColumns.map(column => <div class="label" style={{
              width: column.width,
              paddingLeft: column.labelLeft,
              paddingRight: column.labelRight,
              lineHeight: '20px',
              boxSizing: 'border-box',
              textAlign: column.textAlign
              }}>
              { column.title }
            </div>) }
          </div>
            {dataSource.map((d, i) => (
              <div
                class="item flex flex-align-center full-width"
              >
              { bigPriceColumns.map(column => <div style={{ width: column.width, textAlign: column.textAlign }}>
                { <div class={[column.key === 'item' ? 'special' : '']}>
                  { d[column.key] }
                  { column.key === 'totalDM' && d.totalDMRatio ? <span>(<span style={{ color: d.totalDMRatioColor }}>{ d.totalDMRatio }</span>)</span> : null }
                </div> }
              </div>)}
              </div>
            ))}
          </div>
        ) : null}
        {!dataSource.length ? <EmptyData loading={loading} height={573}/> : null}
        </div>
      </div>
    }
  })
</script>
<style scoped lang="scss">
@import '../common/mian.scss';
.container-box {
  margin-top: 9px;
}
.item {
  padding: 17px 0;
  line-height: 20px;
  word-break: break-all;
  margin-top: 4px;
  &:nth-child(2n) {
    background: #F7F8FA;
  }
  &:nth-child(2) {
    margin-top: 15px;
  }
}
.special {
  padding-left: 20px;
  font-weight: 600;
  box-sizing: border-box;
}
</style>
