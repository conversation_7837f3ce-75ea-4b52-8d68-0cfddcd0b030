<script lang="jsx">
  import { defineComponent, reactive, toRefs, watch, nextTick } from 'vue'
  import selectTab from './select-tab.vue'
  import { tenScoreColorList, getFloorData } from '../constants'
  import { NiImg } from '@jiuji/nine-ui'
  import EmptyData from './empty-data.vue'
  import rank1 from '../common/rank-1.png'
  import rank2 from '../common/rank-2.png'
  import rank3 from '../common/rank-3.png'
  import rank from '../common/rank.png'
  import { useState } from '../hooks/useState.js'

  const rankImgMap = new Map([
    [1, rank1],
    [2, rank2],
    [3, rank3],
  ])

  const columns = function (show) {
    return [
      { title: '排名', key: 'rank', width: 40 },
      { title: '机型', key: 'product', width: 150, labelLeft: 2 },
      { title: `总单毛${show ? '(与全区差异)' : ''}`, key: 'totalSingleProfit', width: 120, textAlign: 'right', labelRight: 10 },
      {
        title: `增值单毛${show ? '(与全区差异)' : ''}`,
        key: 'valueAddSingleProfit',
        width: 135,
        textAlign: 'center',
        labelRight: 10
      },
      { title: `配件利润比${show ? '(与全区差异)' : ''}`, key: 'accessoryProfitRatio', width: 145, textAlign: 'center', labelRight: 10 },
      {
        title: `服务利润比${show ? '(与全区差异)' : ''}`,
        key: 'selfServiceProfitRatio',
        width: 145,
        textAlign: 'center',
        labelRight: 10
      },
      {
        title: `回收跟机率${show ? '(与全区差异)' : ''}`,
        key: 'recoverFollowRate',
        width: 145,
        textAlign: 'center',
      },
    ]
  }

  export default defineComponent({
    components: { selectTab, NiImg, EmptyData },
    setup () {
      const { stateCommon, params } = useState()
      const state = reactive({
        currentTab: 5,
        tabsOptions: [],
        dataSource: [],
        loading: false,
        containerBoxHeight: undefined
      })
      watch(() => stateCommon.needRefresh, (val) => {
        if (val) {
          getData()
        }
      }, { immediate: true })
      async function getData (type) {
        state.loading = true
        state.dataSource = []
        const { currentTab } = state
        const res = await getFloorData({
          ...params.value,
          floorId: 6,
        })
        state.loading = false
        if (res) {
          const { floorList } = res.data
          const { areaLabels } = params.value
          const { data: { statistics = [] }, tabs } = floorList[0]
          // if (tabs?.length && areaLabels?.length) {
          //   tabs[0].title = areaLabels.join(',')
          // }
          state.tabsOptions = tabs || []
          state.dataSource = statistics
        }
      }
      getData()

      return {
        ...toRefs(state),
        getData,
        stateCommon,
        params
      }
    },
    render () {
      const {
        dataSource,
        loading,
        params,
        stateCommon: { role }
      } = this
      const show = !!(role !== 5 || params.areaIds?.length !== 0)
      const columnsData = columns(show)
      console.log('role', role, params)
      return <div class="container-box mt-10 sales-ten">
        <div class="content" style="paddingBottom: 7px">
          {dataSource.length ? (
            <div class="main relative">
              <div class="left absolute">
                <div class="margin-top flex white-bg">
                  {columnsData.slice(0, 2).map((column) => (
                    <div
                      class="label flex-child-noshrink"
                      style={{
                        width: `${column.width}px`,
                        marginLeft: `${column.labelLeft}px`,
                        marginRight: `${column.labelRight}px`,
                        lineHeight: '17px',
                        textAlign: column.textAlign,
                      }}
                    >
                      {column.title}
                    </div>
                  ))}
                </div>
                {dataSource.map((d, i) => (
                <div
                  class="item flex flex-align-center"
                  style={{
                    background:
                      tenScoreColorList[i] || '#f7f8fa',
                    width: `${columnsData
                      .slice(0, 2)
                      .map((d) => d.width)
                      .reduce((total, current) => total + current, 0)}px`,
                  }}
                >
                  {columnsData.slice(0, 2).map((column) => (
                    <div
                      class="flex-child-noshrink"
                      style={{
                        width: `${column.width}px`,
                        textAlign: column.textAlign,
                        marginLeft: `${column.labelLeft}px`,
                        marginRight: `${column.labelRight}px`,
                      }}
                    >
                      {column.key === 'rank' ? (
                        <div class="rank">
                          <div class="relative flex flex-align-center flex-justify-center">
                            <NiImg
                              class="img"
                              src={rankImgMap.get(i + 1) || rank}
                            />
                            {!rankImgMap.get(i + 1) ? (
                              <span class="blue font-11 bold absolute">
                                {i + 1}
                              </span>
                            ) : null}
                          </div>
                        </div>
                      ) : (
                        <div class="ellipsis" style="padding-right:5px" domPropsInnerHTML={d[column.key]}></div>
                      )}
                    </div>
                  ))}
                </div>
              ))}
              </div>
              <div class="scroll-box">
                <div class="margin-top flex">
                  {columnsData.map((column, i) => (
                    <div
                      class={['label flex-child-noshrink', i < 2 ? 'opacity-0' : '']}
                      style={{
                        width: `${column.width}px`,
                        marginLeft: `${column.labelLeft}px`,
                        marginRight: `${column.labelRight}px`,
                        lineHeight: '17px',
                        textAlign: column.textAlign,
                      }}
                    >
                      {column.title}
                    </div>
                  ))}
                </div>
                {dataSource.map((d, i) => (
                  <div
                    class="item flex flex-align-center"
                    style={{
                      background:
                        tenScoreColorList[i] || '#f7f8fa',
                      width: `${columnsData
                        .map((d) => d.width + ~~d.labelLeft + ~~d.labelRight)
                        .reduce((total, current) => total + current, 0)}px`,
                    }}
                  >
                    {columnsData.map((column) => (
                      <div
                        class="flex-child-noshrink"
                        style={{
                          width: `${column.width}px`,
                          textAlign: column.textAlign,
                          marginLeft: `${column.labelLeft}px`,
                          marginRight: `${column.labelRight}px`
                        }}
                      >
                        {column.key === 'rank' ? (
                          <div class="rank">
                            <div class="relative flex flex-align-center flex-justify-center">
                              <NiImg
                                class="img"
                                src={rankImgMap.get(i + 1) || rank}
                              />
                              {!rankImgMap.get(i + 1) ? (
                                <span class="blue font-11 bold absolute">
                                  {i + 1}
                                </span>
                              ) : null}
                            </div>
                          </div>
                        ) : (
                          <div class="ellipsis" style="padding-right:5px" domPropsInnerHTML={d[column.key]}></div>
                        )}
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </div>
          ) : null}
        {!dataSource.length ? <EmptyData loading={loading} height={573}/> : null}
        </div>
      </div>
    }
  })
</script>
<style scoped lang="scss">
@import '../common/mian.scss';
.content {
  .main {
    .scroll-box{
      overflow-x: auto;
      &::-webkit-scrollbar{
        // display: none;
      }
    }
    .item {
      border-radius: 8px;
      padding: 8px 10px 8px 10px;
      margin-top: 14px;
      word-break: break-all;
      overflow: hidden;
      &:last-child {
        margin-bottom: 10px;
      }
      .rank {
        color: #9c9c9c;
        font-weight: 600;
        width: 24px;
        height: 24px;
        .img {
          width: 24px;
          height: 24px;
        }
      }
    }
    .left{
      top: 0;
      left: -1px;
      bottom: 10px;
      z-index: 1;
      // background: #fff;
      .item{
        border-radius: 8px 0 0 8px;
      }
    }
  }
}
</style>
