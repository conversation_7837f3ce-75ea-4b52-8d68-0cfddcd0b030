<script lang="jsx">
  import Vue, { defineComponent, reactive, toRefs, ref, nextTick, watch } from 'vue'
  import selectTab from './select-tab.vue'
  import * as echarts from 'echarts5'
  import { tyingOption, getGradationColor, getFloorData, dealYData } from '../constants.js'
  import EmptyData from './empty-data.vue'
  import { cloneDeep } from 'lodash'
  import { useState } from '../hooks/useState.js'
  import Title from './title.vue'

  const titleOptions = [
    { title: '总量', value: 1, borderColor: '#1890FF' },
    { title: '搭售量', value: 2, borderColor: '#FFA425', marginLeft: '20px' },
    { title: '搭售率', value: 3, borderColor: '#47D45E', marginLeft: '20px' }
  ]
  const typeOptions = [
    { title: '搭售九机服务', type: 10, },
    { title: '搭售配件', type: 11 },
    { title: '搭售智能', type: 12 }
  ]
  export default defineComponent({
    components: { selectTab, EmptyData, Title },
    props: {
      type: {
        type: Number,
        default: 10
      }
    },
    setup (props) {
      const { stateCommon, params } = useState()
      const target = ref(null)
      const myChart = ref(null)
      const option = ref(null)
      const state = reactive({
        currentDataKey: undefined,
        currentDataItem: {},
        showBack: false,
        timeline: '0%',
        tips: undefined,
        dataSource: [],
        tabsOptions: {
          follow: [],
          tendency: []
        },
        statistics: [],
        loading: false,
        loadingT: false,
        backP: {},
        cacheP: {},
        areaLevel: 1
      })

      watch(() => stateCommon.needRefresh, (val) => {
        if (val) {
          getDataTendency()
        }
      }, { immediate: true })

      async function getDataTendency () {
        const { currentDataItem } = state
        state.statistics = []
        myChart.value = null
        state.loading = true
        const res = await getFloorData({
          ...params.value,
          floorId: props.type,
        })
        state.loading = false
        if (res) {
          const { floorList } = res.data
          const originData = {
            total: [],
            tiedSale: []
          }
          const { tabs, data: { statistics } } = floorList[0]
          state.tabsOptions.tendency = tabs
          state.statistics = statistics
          if (!statistics?.length) {
            myChart.value = null
            return
          }
          option.value = cloneDeep(tyingOption(statistics))
          option.value.xAxis[0].data = statistics?.map(it => it.item)
          // 由于柱形图需要重叠，更改数据格式
          originData.total = statistics?.map(it => ({
            value: +it.total,
            name: it.item,
            itemStyle: { color: getGradationColor('#66B5FF', '#1890FF') }
          }))
          originData.tiedSale = statistics?.map(it => ({
            value: +it.tyingSaleCount,
            name: it.item,
            itemStyle: { color: getGradationColor('#FFA425', '#FFCA80') }
          }))
          option.value.series[2].data = statistics?.map(it => ({
            value: +(it.tyingSaleRate?.replace('%', '')),
            name: it.item,
            id: it.item,
          }))
          const smallData = []
          const bigData = []
          originData.total.map((it, index) => {
            const originFinish = originData.total[index]
            const originTarget = originData.tiedSale[index]
            const maxV = Math.max(originFinish.value, originTarget.value)
            smallData.push(maxV === originFinish.value ? originTarget : originFinish)
            bigData.push(maxV === originFinish.value ? originFinish : originTarget)
          })
          // y轴数据有过万，转为以万为单位
          const yDataB = dealYData(bigData.map(it => it.value))
          const yDataS = dealYData(smallData.map(it => it.value), true)
          // 解决柱形图被覆盖问题
          option.value.series[0].data = bigData.map((it, i) => ({ ...it, value: yDataB.data[i] }))
          option.value.series[1].data = (yDataB.unit === '万' ? smallData.map((it, i) => ({ ...it, value: yDataS.data[i] })) : smallData)
          const { series } = option.value
          const maxNumber = Math.max(...yDataB.data)
          const intervalLeft = Math.ceil(maxNumber / 5)
          const intervalRight = Math.ceil((Math.max(...series[2].data.map(it => it.value))) / 5)
          option.value.yAxis[0].interval = intervalLeft
          option.value.yAxis[0].max = intervalLeft * 5
          option.value.yAxis[1].interval = intervalRight
          option.value.yAxis[1].max = intervalRight * 5
          // option.value.yAxis[0].name = `连带量(${yDataB.unit})`
          const bigLength = (statistics?.length > 10)
          const { areaLevel } = state
          const start = bigLength ? 30 : 0
          const end = bigLength ? 70 : 100
          option.value.dataZoom[0].start = start
          option.value.dataZoom[0].end = end
          nextTick(() => {
            if (!myChart.value) {
              initChart()
            } else {
              setOption()
            }
          })
        }
      }
      getDataTendency()

      function cliclChartItem (params) {
        const { name } = params
        const { statistics } = state
        const currentItem = statistics?.find(it => it.item === name) || {}
        console.log('currentItem', currentItem, params)
        if (state.currentTabT === 2 && currentItem.itemType !== 3) {
          const { itemType, itemValue } = currentItem
          state.showBack = true
          state.areaLevel = currentItem.itemType + 1
          const cacheP = { tabParentId: itemValue, tabParentType: itemType }
          currentItem.itemType === 1 && (state.backP = { ...cacheP })
          getDataTendency(null, { ...cacheP })
        }
      }

      function back () {
        state.areaLevel -= 1
        const { areaLevel } = state
        state.showBack = (areaLevel !== 1)
        state.currentTabT = 2
        getDataTendency(null, areaLevel === 2 ? { ...state.backP } : {})
      }

      const initChart = function () {
        if (!target.value) return
        myChart.value = echarts.init(target.value)
        // myChart.value.on('click', function (params) {
        //   cliclChartItem(params)
        // })
        setOption()
      }

      function setOption () {
        myChart.value.clear()
        myChart.value.setOption(option.value)
      }

      function changeDataItem (item) {
        state.currentDataKey = item.key
        state.currentDataItem = state.dataSource.find(it => it.key === item.key) || {}
        getDataTendency()
      }

      function changeTab () {
      }

      return {
        target,
        ...toRefs(state),
        changeDataItem,
        changeTab,
        getDataTendency,
        back
      }
    },
    render () {
      const {
        currentTab,
        dataSource,
        currentDataKey,
        changeDataItem,
        currentDataItem,
        timeline,
        tabsOptions,
        getDataTendency,
        statistics,
        loading,
        back,
        showBack,
        type
      } = this

      return <div class="container-box">
         <Title name={typeOptions.find(it => it.type === type)?.title}>
          {tabsOptions?.length > 1 ? <selectTab onChange={getDataTendency} showBack={showBack} onBack={back} options={tabsOptions} v-model={this.currentTab}/> : null }
        </Title>
        <div class="content">
          { statistics?.length ? <div class="chart-box">
                <div class="chart" ref="target"></div>
                 <div class="mt-16 flex flex-center">
                   { titleOptions.map(it => <div class="flex flex-align-center" style={{ marginLeft: it.marginLeft }}>
                  <div class="circle" style={{ border: `4px solid ${it.borderColor}` }}/>
                    <span class="label ml-2">{it.title}</span>
                  </div>) }
                </div>
              </div> : <EmptyData height={249} loading={loading}/>}
        </div>
      </div>
    }
  })
</script>
<style lang="scss" scoped>
@import '../common/mian.scss';
.container-box {
  width: calc((100% - 12px) / 2);
}

.chart {
  width: 100%;
  height: 220px;
  z-index: 1;
}
.circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  box-sizing: border-box;
}
</style>
