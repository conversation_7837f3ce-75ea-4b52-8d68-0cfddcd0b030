<script lang="jsx">
  import { defineComponent, reactive, toRefs, watch } from 'vue'
  import chartPie from './chart-pie.vue'
  import { bigSalesProColorList, getFloorData } from '../constants.js'
  import EmptyData from './empty-data.vue'
  import { useState } from '../hooks/useState.js'
  import Title from './title.vue'

  export default defineComponent({
    components: { chartPie, EmptyData, Title },
    setup () {
      const { stateCommon, params } = useState()
      const state = reactive({
        dataSource: [],
        loading: false
      })

      watch(() => stateCommon.needRefresh, (val) => {
        if (val) {
          getData()
        }
      }, { immediate: true })
      async function getData () {
        state.loading = true
        state.dataSource = []
        const res = await getFloorData({
          ...params.value,
          floorId: 4,
        })
        state.loading = false
        if (res) {
          const { floorList } = res.data
          const { statistics } = floorList[0]?.data
          state.dataSource = statistics?.map((it, i) => ({
            ...it,
            name: it.item,
            value: it.ratio?.replace('%', ''),
            otherValue: it.sales,
            borderColor: bigSalesProColorList[i].borderColor,
            itemStyle: { color: bigSalesProColorList[i].color }
          })) || []
        }
      }
      getData()

      return {
        ...toRefs(state)
      }
    },
    render () {
      const {
        dataSource,
        loading
      } = this
      return <div class="container-box">
        <Title name="大件销量占比"/>
       <div class="content flex flex-center">
        {dataSource?.length ? <chartPie showOther={true} data={dataSource}/> : <EmptyData height={248} loading={loading}/>}
        </div>
      </div>
    }
  })
</script>
<style scoped lang="scss">
@import '../common/mian.scss';
.container-box {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  height: 322px;
  min-width: 400px;
}
.content {
  height: 268px;
}
</style>
