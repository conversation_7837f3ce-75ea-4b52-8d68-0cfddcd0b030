<script lang="jsx">
  import { defineComponent } from 'vue'
  import { icons } from '../constants'

  export default defineComponent({
    props: {
      name: {
        type: String,
      },
      showBg: {
        type: Boolean,
        default: false
      },
      link: {
        type: String,
      },
    },
    setup () {

    },
    render () {
      const { name, showBg, link } = this
      return <div class={['flex flex-align-center flex-justify-between title', showBg ? 'bg' : '']}>
        <span class="name">{ name }</span>
        <div class="flex flex-align-center">
          {this.$slots.default}
          {link ? <img class="img" src={icons.link}/> : null}
        </div>
      </div>
    }
  })
</script>
<style scoped lang="scss">
.title {
  height: 54px;
  padding: 0 20px;
  box-sizing: border-box;
  border-radius: 8px 8px 0px 0px;
  position: relative;
  &:before {
    content: '';
    position: absolute;
    top: 18px;
    left: 20px;
    width: 4px;
    height: 18px;
    background: #1890FF;
    border-radius: 2px;
    box-sizing: border-box;
  }
}
.bg {
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  background: linear-gradient(180deg, #E6F2FF 0%, #FFFFFF 100%);
}
.name {
  font-weight: 600;
  font-size: 18px;
  margin-left: 12px;
}
.img {
  width: 16px;
  height: 16px;
  margin-left: 22px;
}
</style>
