<script lang="jsx">
  import Vue, { defineComponent, reactive, toRefs, ref, nextTick, watch } from 'vue'
  import selectTab from './select-tab.vue'
  import * as echarts from 'echarts5'
  import { recoveryProbabilityOption, getGradationColor, getFloorData, dealYData } from '../constants.js'
  import EmptyData from './empty-data.vue'
  import { cloneDeep } from 'lodash'
  import { useState } from '../hooks/useState.js'
  import Title from './title.vue'

  const titleOptions = [
    { title: '回收单毛', value: 1, borderColor: '#1890FF' },
    { title: '连带跟机率', value: 2, borderColor: '#FFA425', marginLeft: '20px' },
    { title: '单独跟机率', value: 3, borderColor: '#47D45E', marginLeft: '20px' },
    { title: '总跟机率', value: 4, borderColor: '#4D69FF', marginLeft: '20px' },
  ]
  export default defineComponent({
    components: { selectTab, EmptyData, Title },
    setup () {
      const { stateCommon, params } = useState()
      const target = ref(null)
      const myChart = ref(null)
      const option = ref(null)
      const state = reactive({
        currentTab: 1,
        currentDataKey: undefined,
        currentDataItem: {},
        showBack: false,
        timeline: '0%',
        tips: undefined,
        dataSource: [],
        tabsOptions: [],
        statistics: [],
        loading: false,
        loadingT: false,
        backP: {},
        cacheP: {},
        areaLevel: 1
      })

      watch(() => stateCommon.needRefresh, (val) => {
        if (val) {
          getDataTendency()
        }
      }, { immediate: true })

      async function getDataTendency (type, par = {}) {
        const { currentTab, currentDataItem } = state
        state.statistics = []
        myChart.value = null
        state.loading = true
        state.cacheP = {
          ...par,
          itemKey: currentDataItem.key
        }
        const res = await getFloorData({
          ...params.value,
          floorId: 9,
          tabId: currentTab,
          ...par,
          itemKey: currentDataItem.key
        })
        state.loading = false
        if (res) {
          (currentTab === 3 || currentTab === 1) && (state.showBack = false)
          const { floorList } = res.data
          const originData = {
            finish: [],
            target: []
          }
          const { tabs, data: { statistics } } = floorList[0]
          state.tabsOptions = tabs
          !state.currentTab && (state.currentTab = tabs[0].value)
          state.statistics = statistics
          if (!statistics?.length) {
            myChart.value = null
            return
          }
          option.value = cloneDeep(recoveryProbabilityOption(statistics))
          option.value.xAxis[0].data = statistics?.map(it => it.item)
          option.value.series[0].data = statistics?.map((it, i) => +it.recoverSingleProfit)
          option.value.series[1].data = statistics?.map((it, i) => +it.ldFollowRate * 100)
          option.value.series[2].data = statistics?.map((it, i) => +it.singleFollowRate * 100)
          option.value.series[3].data = statistics?.map((it, i) => +it.totalFollowRate * 100)
          const { series } = option.value
          const rightAllData = [...series[1].data, ...series[2].data, ...series[3].data]
          const intervalLeft = Math.ceil((Math.max(...series[0].data)) / 5)
          const intervalRight = Math.ceil((Math.max(...rightAllData)) / 5)
          option.value.yAxis[0].interval = intervalLeft
          option.value.yAxis[0].max = intervalLeft * 5
          console.log('intervalRight', intervalRight)
          option.value.yAxis[1].interval = intervalRight
          option.value.yAxis[1].max = intervalRight * 5
          // option.value.yAxis[1].name = `完成量/目标量(${yDataB.unit})`
          const bigLength = (statistics?.length > 31)
          const { areaLevel } = state
          const bigArea = !!(areaLevel === 1 && currentTab === 2)
          const start = bigLength ? 45 : 0
          const end = bigArea ? 50 : bigLength ? 60 : 100
          option.value.dataZoom[0].start = start
          option.value.dataZoom[0].end = end
          type === 1 && (myChart.value = null)
          nextTick(() => {
            if (!myChart.value) {
              initChart()
            } else {
              setOption()
            }
          })
        }
      }
      getDataTendency()

      function cliclChartItem (params) {
        const { name } = params
        const { statistics } = state
        const currentItem = statistics?.find(it => it.item === name) || {}
        console.log('currentItem', currentItem, params)
        if (state.currentTab === 2 && currentItem.itemType !== 3) {
          const { itemType, itemValue } = currentItem
          state.showBack = true
          state.areaLevel = currentItem.itemType + 1
          const cacheP = { tabParentId: itemValue, tabParentType: itemType }
          currentItem.itemType === 1 && (state.backP = { ...cacheP })
          getDataTendency(null, { ...cacheP })
        }
      }

      function back () {
        state.areaLevel -= 1
        const { areaLevel } = state
        state.showBack = (areaLevel !== 1)
        state.currentTab = 2
        getDataTendency(null, areaLevel === 2 ? { ...state.backP } : {})
      }

      const initChart = function () {
        if (!target.value) return
        myChart.value = echarts.init(target.value)
        myChart.value.on('click', function (params) {
          cliclChartItem(params)
        })
        setOption()
      }

      function setOption () {
        myChart.value.clear()
        myChart.value.setOption(option.value)
      }

      function changeDataItem (item) {
        state.currentDataKey = item.key
        state.currentDataItem = state.dataSource.find(it => it.key === item.key) || {}
        getDataTendency()
      }

      function changeTab () {
      }

      return {
        target,
        ...toRefs(state),
        changeDataItem,
        changeTab,
        getDataTendency,
        back
      }
    },
    render () {
      const {
        currentTab,
        dataSource,
        currentDataKey,
        changeDataItem,
        currentDataItem,
        timeline,
        tabsOptions,
        getDataTendency,
        statistics,
        loading,
        back,
        showBack
      } = this

      return <div class="container-box">
         <Title name="回收跟机率趋势">
          {tabsOptions?.length > 1 ? <selectTab onChange={getDataTendency} showBack={showBack} onBack={back} options={tabsOptions} v-model={this.currentTab}/> : null }
        </Title>
        <div class="content">
          { statistics?.length ? <div class="chart-box">
                <div class="chart" ref="target"></div>
                 <div class="mt-16 flex flex-center">
                   { titleOptions.map(it => <div class="flex flex-align-center" style={{ marginLeft: it.marginLeft }}>
                  <div class="circle" style={{ border: `4px solid ${it.borderColor}` }}/>
                    <span class="label ml-2">{it.title}</span>
                  </div>) }
                </div>
              </div> : <EmptyData height={248} loading={loading}/>}
        </div>
      </div>
    }
  })
</script>
<style lang="scss" scoped>
@import '../common/mian.scss';
.container-box {
  width: calc((100% - 12px) / 2);
  min-width: 590px;
}

.chart {
  width: 100%;
  height: 220px;
  z-index: 1;
}
.circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  box-sizing: border-box;
}
</style>
