<script lang="jsx">
  import { defineComponent, ref, onMounted } from 'vue'
  import { NiListPage } from '@jiuji/nine-ui'
  import Filters from '../components/filters.vue'
  import { createState } from '../hooks/useState.js'
  import DataOverview from '../components/data-overview.vue'
  import BulkySalesTrend from '../components/bulky-sales-trend.vue'
  import BulkyTargetFinish from '../components/sales-target-finish.vue'
  import SalesProportion from '../components/sales-proportion.vue'
  import BulkyBrandTen from '../components/bulky-brand-ten.vue'
  import RightRank from '../components/right-rank.vue'
  import NewProduct from '../components/new-product.vue'
  import RecoveryProbability from '../components/recovery-probability.vue'
  import Tying from '../components/tying.vue'
  import BigNumber from 'bignumber.js'

  export default defineComponent({
    components: {
      NiListPage,
      Filters,
      DataOverview,
      BulkyTargetFinish,
      SalesProportion,
      BulkyBrandTen,
      RightRank,
      NewProduct,
      RecoveryProbability,
      Tying
    },
    setup () {
      const boxWidth = ref(1920)
      createState()
      onMounted(() => {
        const productRecovery = document.querySelector('.product-recovery')
        const rightRank = document.querySelector('.right-rank')
        if (productRecovery && rightRank) {
          const productRecoveryWidth = productRecovery.getBoundingClientRect()?.width
          const rightRankWidth = rightRank.getBoundingClientRect()?.width
          const width = new BigNumber(productRecoveryWidth).plus(rightRankWidth).plus(60).toNumber().toFixed(0)
          width && (boxWidth.value = width)
        }
      })
      return {
        boxWidth
      }
    },
    render () {
      const {
        boxWidth
      } = this
      return <page style={{ width: boxWidth + 'px' }} id="page-container-box" class="page-container">
        <NiListPage pushFilterToLocation={false}>
          <Filters style="border-radius: 8px"/>
        </NiListPage>
        <DataOverview class="mt-12"/>
        <div class="flex flex-wrap">
          <BulkySalesTrend class="mt-12"/>
          <BulkyTargetFinish class="mt-12 ml-14"/>
        </div>
        <div class="flex full-width mt-12">
          <div class="flex flex-wrap flex-child-grow">
            <div class="flex flex-child-grow">
              <SalesProportion/>
              <BulkyBrandTen class="ml-12"/>
            </div>
            <div class="flex flex-child-grow product-recovery">
              <NewProduct class="mt-12"/>
              <RecoveryProbability class="mt-12 ml-12"/>
            </div>
          </div>
          <RightRank class="ml-12 right-rank"/>
        </div>
        <div class="flex full-width mt-12">
          <Tying type={10}/>
          <Tying type={11} class="ml-12"/>
          <Tying type={12} class="ml-12"/>
        </div>
      </page>
    }
  })
</script>
<style scoped lang="scss">
.page-container {
  padding-bottom: 24px;
}
.mt-12 {
  margin-top: 12px;
}
.ml-12 {
  margin-left: 12px;
}
.ml-14 {
  margin-left: 14px;
}
</style>
