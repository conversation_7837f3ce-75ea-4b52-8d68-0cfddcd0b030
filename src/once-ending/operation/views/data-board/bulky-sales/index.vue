<script lang="jsx">
  import { defineComponent, toRefs } from 'vue'
  import { createState } from '../hooks/useState'
  import { NiImg } from '@jiuji/nine-ui'
  import mHeader from '../components/m-header.vue'
  import filters from '../components/filters.vue'
  import salesProportion from '../components/sales-proportion.vue'
  import bulkyBrandTen from '../components/bulky-brand-ten.vue'
  import bulkyPriceProportion from '../components/bulky-price-proportion.vue'

  export default defineComponent({
    components: {
      mHeader,
      filters,
      NiImg,
      salesProportion,
      bulkyBrandTen,
      bulkyPriceProportion
    },
    setup () {
      const { state, refresh, params } = createState()
      return {
        ...toRefs(state),
        refresh,
        params
      }
    },
    beforeRouteLeave (to, from, next) {
      sessionStorage.removeItem(this.key)
      next()
    },
    render () {
      const { screen, params, needRefresh, refresh } = this
      return <div class="wrapper container">
        <mHeader onFilter={() => { this.$refs.filtersRef.moreSelect = true }}/>
        <filters ref="filtersRef" screen={screen} onRefresh={refresh}/>
        <salesProportion params={params} needRefresh={needRefresh} class="margin-top"/>
        <bulkyBrandTen params={params} needRefresh={needRefresh} class="margin-top"/>
        <bulkyPriceProportion params={params} needRefresh={needRefresh} class="margin-top"/>
      </div>
    }
  })
</script>
<style scoped lang="scss">
.container {
  background: #F7F8FA;
  min-height: 100vh;
  padding-bottom: 24px;
  box-sizing: border-box;
  overflow: hidden;
  .sales {
    height: 48px;
    background: #FFFFFF;
    border-radius: 12px;
    margin: 10px;
    padding: 0 10px;
    .title {
      font-size: 16px;
      font-weight: 600;
    }
    img {
      width: 12px;
      height: 12px;
    }
  }
}
</style>
