.container-box {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  .content {
    padding-right: 20px;
    padding-bottom: 20px;
    padding-left: 20px;
    overflow: hidden;
    border-radius: 0 0 8px 8px;
  }
  .px-10 {
    padding: 0 10px;
  }
  .mt-10 {
    margin-top: 10px;
  }
  .mt-20 {
    margin-top: 20px;
  }
  .mt-18 {
    margin-top: 18px;
  }
  .color-b {
    color: #1890FF;
  }
  .line-16 {
    line-height: 16px;
  }
  .line-17 {
    line-height: 17px;
  }
  .line-20 {
    line-height: 20px;
  }
  .line-14 {
    line-height: 14px;
  }
  .line-12 {
    line-height: 12px;
  }
  .h-16 {
    height: 16px;
  }
  .line-100 {
    line-height: 1
  }
  .ml-2 {
    margin-left: 2px;
  }
  .ml-3 {
    margin-left: 3px;
  }
  .ml-24 {
    margin-left: 24px;
  }
  .ml-20 {
    margin-left: 20px;
  }
  .mt-2 {
    margin-top: 2px;
  }
  .mt-4 {
    margin-top: 4px;
  }
  .mt-6 {
    margin-top: 6px;
  }
  .pt-20 {
    padding-top: 20px;
  }
  .mt-12 {
    margin-top: 12px;
  }
  .mb-4 {
    margin-bottom: 4px;
  }
  .color-r {
    color: #F15643
  }
  .color-y {
    color: #FA6400;
  }
  .half-width {
    width: 50%;
  }
  .label {
    color: #9C9C9C;
    font-size: 12px;
    line-height: 12px;
  }
  .font-11 {
    font-size: 11px;
  }
}
.pr-20 {
  padding-right: 20px;
}
.table-boxs {
  .title-item {
    display: flex;
    padding: 0 20px 0 10px;
  }
  .table-item-box {
    display: flex;
    align-items: center;
    min-height: 36px;
    box-sizing: border-box;
    line-height: 18px;
    background: #fff;
    padding: 9px 20px 9px 10px;
    word-break: break-all;
    &:nth-child(2) {
      margin-top: 10px;
    }
    &:nth-child(2n) {
      background: #F7F8FA;
    }
    &:last-child {
      margin-bottom: 16px;
    }
  }
}
.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  box-sizing: border-box;
}