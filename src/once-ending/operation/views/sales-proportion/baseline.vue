<template>
	<page>
    <div class="sales_rate">
      <div class="flex flex-align-center flex-justify-between" style="border-bottom: 1px solid #DFDFDF;">
        <ul class="list">
          <li class="flex flex-align-center">
            <span>月份：</span>
            <a-month-picker value-format="YYYY-MM" :getPopupContainer="(triggerNode) => triggerNode.parentNode" style="width: 200px;" :allowClear="false" v-model="month" @change="monthChange" placeholder="选择月份" />
          </li>
          <li class="flex flex-align-center">
            <span>地区：</span>
            <ni-areaSelect
              v-model="areaId"
              style="width: 300px;"
              placeholder="请选择地区"
              search-placeholder="请输入"
              @change="getCalendar"
            ></ni-areaSelect>
          </li>
          <li>
            <span>工作日基准值：</span>
            <a-input-number v-model="workDay" :min="1" :precision="0" @change="workdayChange" />
          </li>
          <li>
            <span>周末基准值：</span>
            <a-input-number v-model="weekend" :min="1" :precision="0" @change="weekendChange" />
          </li>
          <li>
            <span>节假日基准值：</span>
            <a-input-number v-model="holiday" :min="1" :precision="0" @change="holidayChange" />
          </li>
        </ul>
        <div v-if="$tnt.xtenant < 1000">
          <a href="https://img2.ch999img.com/newstatic/51169/130f59181adf6ee3.xlsx" class="mr-8">下载导入模板</a>
          <span class="pointer" style="color:#1890ff" @click="importData">导入</span>
          <form ref="fileForm" style="display: none" name="fileForm">
            <input ref="file" type="file" accept=".xls,.xlsx" name="file"
                   @change="selectFile"/>
          </form>
        </div>
      </div>
      <Calendar v-model="detailList" >
        <template v-slot:edit="{ d }">
          <div v-if="d.belongToCurrent">
            <p class="rest" v-if="d.holiday">休</p>
            <p class="date">{{d.day}}</p>
            <div class="ipt"><a-input-number v-model="d.baseValue" style="width: 80px;" :min="1" :precision="0" /></div>
          </div>
          <p v-else class="date" style="color: rgba(0, 0, 0, 0.05)">{{d.day}}</p>
        </template>
      </Calendar>
      <div class="buttons flex flex-align-center">
        <a-button :loding="confirmLoading" @click="handleOk" type="primary">保存</a-button>
      </div>
    <!-- </a-modal> -->
  </div>
  </page>
</template>

<script>
  import Calendar from '@hr/views/sales-task/calendar.vue'
  import salesProportionApi from '@operation/api/sales-proportion'
  import { to } from '@common/utils'
  import { message } from 'ant-design-vue'
  import moment from 'moment'
  import {
    NiAreaSelect
  } from '@jiuji/nine-ui'

  export default {
    name: 'edit',
    components: {
      Calendar,
      NiAreaSelect
    },
    created () {
      this.getCalendar()
    },
    data () {
      return {
        confirmLoading: false,
        detailList: [],
        workDay: '',
        weekend: '',
        holiday: '',
        month: moment().startOf('month').format('YYYY-MM'),
        areaId: `${this.$store.state.userInfo.areaid}`,
        orgAreaId: ''
      }
    },
    methods: {
      importData () {
        this.$refs.file.click()
      },
      async selectFile (e) {
        let file = e.target.files[0]
        if (file && file.length === 0) return
        const regExcel = /.(xls|xlsx)$/i
        if (!regExcel.test(file.name)) {
          this.$message.error('仅支持导入Excel格式的文件')
          return
        }
        const formData = new FormData()
        formData.append('file', file)
        for (let key of formData.entries()) {
        }
        try {
          this.$message.info('导入中......')
          let res = await salesProportionApi.importData(formData)
          if (res.code === 0) {
            this.$message.success('导入成功')
            this.getCalendar()
          } else {
            if (res.data) window.location.href = res.data
            this.$message.error(res.userMsg)
          }
        } catch (e) {
          this.$message.error('导入失败')
        } finally {
          this.$refs.fileForm.reset()
        }
      },
      async getCalendar () {
        const params = {
          areaId: this.areaId,
          month: this.month
        }
        const [err, res] = await to(salesProportionApi.getByMonth(params))
        if (err) { throw err }
        const { code, data, userMsg } = res
        if (code !== 0) {
          message.error(userMsg)
          return
        }
        this.detailList = data.detailList.map(d => {
          d.map(d => {
            d.orgDay = d.day
            const day = moment(d.day).date()
            d.day = day < 10 ? `0${day}` : `${day}`
            d.baseValue = d.baseValue || undefined
            return d
          })
          return d
        })
        this.orgAreaId = data.areaId
        this.workDay = data.workDay || ''
        this.weekend = data.weekend || ''
        this.holiday = data.holiday || ''
      },
      async handleOk () {
        const dayTask = []
        this.detailList.forEach(item => {
          item.forEach(d => {
            if (d.belongToCurrent && d.baseValue) {
              dayTask.push({
                baseValue: d.baseValue,
                day: d.orgDay
              })
            }
          })
        })
        const params = {
          areaId: this.orgAreaId,
          month: this.month,
          holiday: this.holiday,
          weekend: this.weekend,
          workDay: this.workDay,
          detailList: dayTask
        }
        this.confirmLoading = true
        const [err, res] = await to(salesProportionApi.salesBaseValue(params))
        if (err) { throw err }
        const { code, userMsg } = res
        if (code !== 0) {
          message.error(userMsg)
        } else {
          message.success('保存成功！')
          this.getCalendar()
        }
        this.confirmLoading = false
      },
      workdayChange (value) {
        this.detailList.forEach(item => {
          item.forEach(i => {
            if (!i.holiday && !i.weekend) {
              i.baseValue = value
            }
          })
        })
      },
      weekendChange (value) {
        this.detailList.forEach(item => {
          item.forEach(i => {
            if (i.weekend) {
              i.baseValue = value
            }
          })
        })
      },
      holidayChange (value) {
        this.detailList.forEach(item => {
          item.forEach(i => {
            if (i.holiday) {
              i.baseValue = value
            }
          })
        })
      },
      monthChange () {
        this.getCalendar()
      },
    }
  }
</script>

<style scoped lang="less">
  .sales_title{
    font-weight: bold;
    font-size: 16px;
    color: rgba(0, 0, 0, .85);
  }
  .list{
    display: flex;
    margin-top: 22px;
    padding-bottom: 15px;
    align-items: center;
    li{
      font-size: 14px;
      color: rgba(0, 0, 0, .85);
      margin-right: 16px;
    }
    .date{
      font-size: 16px;
      font-weight: bold;
    }
  }
  .tip{
    margin-top: 14px;
    font-size: 14px;
    color: #FF4D4F;
  }
  .disabled-box {
    position: absolute;
    cursor: not-allowed;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    z-index: 2;
    background: rgba(0, 0, 0, 0.03);
  }
  .buttons {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    background: #fff;
    height: 54px;
    padding-left: 24px;
  }
  .sales_rate {
    background: #fff;
    padding: 24px;
  }
</style>
