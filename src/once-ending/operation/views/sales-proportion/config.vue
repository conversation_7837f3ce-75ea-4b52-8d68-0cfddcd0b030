<template>
	<page>
    <div class="sales_rate">
      <p class="sales_title"><span>占比分配率：</span><span style="color: #FF4D4F;">{{rate}}</span>/<span>100</span></p>
      <ul class="list">
        <li class="flex flex-align-center">
          <span>月份：</span>
          <a-month-picker :getPopupContainer="(triggerNode) => triggerNode.parentNode" style="width: 200px;" :allowClear="false" v-model="month" @change="monthChange" placeholder="选择月份" />
        </li>
        <li>
          <span>工作日占比：</span>
          <a-input-number v-model="workdayPercent" :min="0" @change="workdayChange" /> %
        </li>
        <li>
          <span>周末占比：</span>
          <a-input-number v-model="weekendPercent" :min="0" @change="weekendChange" /> %
        </li>
        <li>
          <span>节假日占比：</span>
          <a-input-number v-model="holidayPercent" :min="0" @change="holidayChange" /> %
        </li>
      </ul>
      <Calendar v-model="dayTaskCalendar" >
        <template v-slot:edit="{ d }">
          <div v-if="d.belongToCurrent">
            <p class="rest" v-if="d.holiday">休</p>
            <p class="date">{{d.day}}</p>
            <div class="ipt"><a-input-number v-model="d.taskPercent" style="width: 60px;" :min="0" @blur="closeEdit(d)" /> %</div>
          </div>
          <p v-else class="date" style="color: rgba(0, 0, 0, 0.05)">{{d.day}}</p>
        </template>
      </Calendar>
      <p class="tip">*每日占比双击占比数字可修改</p>
      <div class="buttons flex flex-align-center">
        <a-button @click="handleOk" type="primary">保存</a-button>
      </div>
    <!-- </a-modal> -->
  </div>
  </page>
</template>

<script>
  import Calendar from '@hr/views/sales-task/calendar.vue'
  import salesTaskApi from '@hr/api/salesTask'
  import { to } from '@common/utils'
  import { message } from 'ant-design-vue'
  import moment from 'moment'

  export default {
    name: 'edit',
    components: {
      Calendar
    },
    created () {
      let date = new Date()
      this.currentDate = date.getDate()
      this.getCalendar()
    },
    data () {
      return {
        visible: false,
        confirmLoading: false,
        dayTaskCalendar: [],
        workdayPercent: '',
        weekendPercent: '',
        holidayPercent: '',
        calendarId: '',
        currentDate: '',
        perSales: '',
        month: moment().startOf('month').format('YYYY-MM')
      }
    },
    computed: {
      showMonth () {
        let arr = this.month.split('-')
        return `${arr[0]}年${arr[1]}月`
      },
      rate () {
        let count = 0
        this.dayTaskCalendar.forEach(item => {
          item.forEach(d => {
            if (d.belongToCurrent) {
              count += d.taskPercent
            }
          })
        })
        return count
      }
    },
    methods: {
      async getCalendar () {
        const [err, res] = await to(salesTaskApi.getCalendar(this.month + '-01'))
        if (err) { throw err }
        const { code, data, userMsg } = res
        if (code !== 0) {
          message.error(userMsg)
          return
        }
        this.dayTaskCalendar = data.dayTaskCalendar
        this.workdayPercent = data.workdayPercent
        this.weekendPercent = data.weekendPercent
        this.holidayPercent = data.holidayPercent
        this.calendarId = data.id
        this.perSales = data.perSales
      },
      async handleOk () {
        console.log(this.dayTaskCalendar)
        const dayTask = []
        this.dayTaskCalendar.forEach(item => {
          item.forEach(d => {
            if (d.belongToCurrent) {
              dayTask.push({
                alloTime: this.month + '-' + d.day,
                taskPercent: d.taskPercent
              })
            }
          })
        })
        const params = {
          alloDate: this.month,
          dayTask,
          holidayPercent: this.holidayPercent,
          weekendPercent: this.weekendPercent,
          workdayPercent: this.workdayPercent,
          id: this.calendarId,
          perSales: this.perSales
        }
        this.confirmLoading = true
        const [err, res] = await to(salesTaskApi.saveTask(params))
        if (err) { throw err }
        const { code, userMsg } = res
        if (code !== 0) {
          message.error(userMsg)
        } else {
          message.success('保存成功！')
          this.visible = false
          this.getCalendar()
        }
        this.confirmLoading = false
      },
      handleCancel () {
        this.visible = false
      },
      workdayChange (value) {
        this.dayTaskCalendar.forEach(item => {
          item.forEach(i => {
            if (!i.holiday && !i.weekend) {
              i.taskPercent = value
            }
          })
        })
      },
      weekendChange (value) {
        this.dayTaskCalendar.forEach(item => {
          item.forEach(i => {
            if (i.weekend) {
              i.taskPercent = value
            }
          })
        })
      },
      holidayChange (value) {
        this.dayTaskCalendar.forEach(item => {
          item.forEach(i => {
            if (i.holiday) {
              i.taskPercent = value
            }
          })
        })
      },
      closeEdit (d) {
        d.edit = false
      },
      showEdit (d) {
        d.edit = true
      },
      monthChange (value, str) {
        this.month = str
        this.getCalendar()
      },
    }
  }
</script>

<style scoped lang="less">
  .sales_title{
    font-weight: bold;
    font-size: 16px;
    color: rgba(0, 0, 0, .85);
  }
  .list{
    display: flex;
    margin-top: 22px;
    padding-bottom: 15px;
    align-items: center;
    border-bottom: 1px solid #DFDFDF;
    li{
      font-size: 14px;
      color: rgba(0, 0, 0, .85);
      margin-right: 16px;
    }
    .date{
      font-size: 16px;
      font-weight: bold;
    }
  }
  .tip{
    margin-top: 14px;
    font-size: 14px;
    color: #FF4D4F;
  }
  .disabled-box {
    position: absolute;
    cursor: not-allowed;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    z-index: 2;
    background: rgba(0, 0, 0, 0.03);
  }
  .buttons {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    background: #fff;
    height: 54px;
    padding-left: 24px;
  }
  .sales_rate {
    background: #fff;
    padding: 24px;
  }
</style>
