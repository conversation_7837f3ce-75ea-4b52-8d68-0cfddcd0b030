// 商品分类下拉options
export const OPTIONS = {
  onlineState: [
    {
      label: '所有',
      value: 0
    },
    {
      label: '离线',
      value: -1
    },
    {
      label: '在线',
      value: 1
    }
  ],
  defenceStatus: [
    {
      label: '所有',
      value: 0
    },
    {
      label: '未设防',
      value: -1
    },
    {
      label: '已设防',
      value: 1
    }
  ],
  bluetoothStatus: [
    {
      label: '所有',
      value: 0
    },
    {
      label: '未启用',
      value: -1
    },
    {
      label: '启用',
      value: 1
    }
  ],
  hasAlarmReceiver: [
    {
      label: '有',
      value: 1
    },
    {
      label: '无',
      value: -1
    }
  ]
}

export const actionTypeMap = new Map([
  ['add', '新增'],
  ['edit', '编辑'],
  ['showLogs', '日志'],
  ['matchEquipment', '配对'],
  ['resetEquipment', '重置'],
  ['deployEquipment', '设防'],
  ['relieveEquipment', '撤防'],
  ['unbindDevice', '解绑'],
  ['enableBluetooth', '更改蓝牙打开状态'],
  ['remark', '备注'],
  ['delete', '删除'],
])
export const deviceState = {
  match: {
    false: {
      stateStr: '未配对',
      action: 'matchEquipment',
      actionStr: '配对'
    },
    true: {
      stateStr: '已配对',
      action: 'resetEquipment',
      actionStr: '重置'
    }
  },
  security: {
    false: {
      stateStr: '未设防',
      action: 'deployEquipment',
      actionStr: '设防'
    },
    true: {
      stateStr: '已设防',
      action: 'relieveEquipment',
      actionStr: '撤防'
    },
  },
  online: {
    false: {
      stateStr: '离线',
      action: '',
      actionStr: '',
      color: '#aaa'
    },
    true: {
      stateStr: '在线',
      action: '',
      actionStr: '',
      color: '#3c3c3c'
    },
  },
  bluetooth: {
    false: {
      stateStr: '禁用',
      action: 'enableBluetooth',
      actionStr: '启用蓝牙打卡',
      color: ''
    },
    true: {
      stateStr: '启用',
      action: 'enableBluetooth',
      actionStr: '禁用蓝牙打卡',
      color: ''
    },
  }
}
