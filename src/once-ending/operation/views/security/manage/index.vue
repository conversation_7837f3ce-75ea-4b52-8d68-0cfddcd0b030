<script type="text/jsx" lang="jsx">
  import { provide, defineComponent } from 'vue'
  import { NiListPage } from '@jiuji/nine-ui'
  import LastUpdateDate from './components/lastUpdateDate'
  import SearchBox from './components/search-box'
  import TableBox from './components/table-box'

  import { createSecurityState } from './components/useState.js'

  export default defineComponent({
    components: {
      NiListPage,
      LastUpdateDate,
      SearchBox,
      TableBox
    },
    setup () {
      createSecurityState(provide)
    },
    render () {
      return (
        <page class="security">
          <div slot="extra">
            <LastUpdateDate class="last-updata-data"/>
          </div>
          <NiListPage push-filter-to-location={ false }>
            <SearchBox class="mb-16"/>
            <TableBox/>
          </NiListPage>
        </page>
      )
    }
  })
</script>
<style lang="scss">
.security{
  .ant-page-header-heading-extra{
    float: none;
    .last-updata-data{
      display: inline-block;
      margin-top: 1em;
    }
  }
}
</style>
