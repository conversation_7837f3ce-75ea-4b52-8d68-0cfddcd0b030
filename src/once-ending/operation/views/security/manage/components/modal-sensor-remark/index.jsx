import useSensor from './useSensorRemark.js'
import { useSecurityState } from '../useState.js'

export default () => {
  const { state, setRemarkForm } = useSecurityState()
  const { closeRemarkModal, handleRemarkOk } = useSensor()

  return (
    <a-modal
      destroyOnClose
      title={ `设备备注` }
      visible={ state.remarkModalIsShow }
      confirm-loading={ state.confirmLoading }
      onOk={ handleRemarkOk }
      onCancel={ closeRemarkModal }
      maskClosable={ false }
    >
      <a-alert message={ state.remarkForm.title } type="success" show-icon />
      <a-form labelCol={{ span: 6 } }wrapperCol={{ span: 16 }}>
        <a-form-item>&nbsp;</a-form-item>
        <a-form-item label="设     备ID">
          <i>{ state.remarkForm.deviceId }</i>
        </a-form-item>
        <a-form-item label="传感器ID">
          <i>{ state.remarkForm.sensorId }</i>
        </a-form-item>
        <a-form-item name="remark" label="传感器备注">
          <a-input placeholder="请输入传感器备注" value={ state.remarkForm.remark }
            onChange={ e => { setRemarkForm(e.target.value, 'remark') } }/>
        </a-form-item>
      </a-form>
    </a-modal>
  )
}
