import { getCurrentInstance } from 'vue'
import { message } from 'ant-design-vue'

import { useSecurityState } from '../useState.js'
import useSearch from '../search-box/useSearch.js'

export default function useRemark () {
  const instance = getCurrentInstance().proxy
  const { fetchList } = useSearch()
  const {
    state: { remarkForm },
    setRemarkModalIsShow,
    setRemarkForm,
    setConfirmLoading
  } = useSecurityState()

  const openRemarkModal = async () => {
    setRemarkModalIsShow(true)
    // 打开时，初始化表单(remarkForm)
    setRemarkForm()
  }
  const closeRemarkModal = () => {
    setRemarkModalIsShow(false)
    setRemarkForm()
  }

  // 提交编辑表单
  const handleRemarkOk = async () => {
    if (!remarkForm.remark) {
      message.error('请填写备注')
      return
    }
    const params = {
      deviceId: remarkForm.deviceId,
      sensorId: remarkForm.sensorId,
      remark: remarkForm.remark,
    }
    setConfirmLoading(true)
    const flag = await instance.$store.dispatch(
      `operation/security/updateRemarkForSensor`,
      params
    )
    setConfirmLoading(false)
    if (!flag) return
    closeRemarkModal()
    if (remarkForm.edit) {
      instance.$store.dispatch(
        'operation/security/getDeviceMatchedSensors',
        { deviceId: remarkForm.deviceId }
      )
    } else {
      await fetchList()
    }
  }

  return {
    openRemarkModal,
    closeRemarkModal,
    handleRemarkOk,
  }
}
