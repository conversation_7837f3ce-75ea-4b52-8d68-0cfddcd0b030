import { getCurrentInstance } from 'vue'
import { Modal } from 'ant-design-vue'
import { actionTypeMap } from '../../constants'

import { useSecurityState } from '../useState.js'
import useSearch from '../search-box/useSearch.js'
import useEdit from '../modal-edit/useEdit.js'
import useLogs from '../modal-logs/useLogs.js'
import useSensorRemark from '../modal-sensor-remark/useSensorRemark.js'
import useSensor from '../modal-sensor/useSensor.js'
import useDefence from '../modal-defence/useDefence.js'

export default function useTableActions () {
  const instance = getCurrentInstance().proxy
  const {
    setEditType,
    setCurrentRecordId,
    setCurrentDeviceId,
    setCurrentRecord,
    setRemarkForm,
    setPagination,
    setDefenceForm
  } = useSecurityState()
  const { openEditModal } = useEdit()
  const { openLogsModal } = useLogs()
  const { fetchList, exportList } = useSearch()
  const { openRemarkModal } = useSensorRemark()
  const { openSensorModal } = useSensor()
  const { openDefenceModal } = useDefence()

  const tableChange = (pagination) => {
    setPagination({ ...pagination })
    fetchList().then(r => {})
  }

  // 记录操作【新增、编辑、日志、配对、重置、撤防】入口
  const handleAction = async (actionType, record) => {
    if (actionType === 'add') {
      setEditType(actionType)
      if (record?.deviceId) {
        setCurrentRecordId(record.deviceId)
        setCurrentRecord(record)
      }
      await openEditModal()
      return
    }
    if (actionType === 'export') {
      handleExport()
      return
    }
    if (!record?.deviceId) return

    // 设置当前编辑项的ID。关闭操作modal(包括：日志、重置与配对、设防与撤防、编辑)时清除
    setCurrentRecordId(record.deviceId)
    if (actionType === 'showLogs') {
      if (record.deviceId) setCurrentDeviceId(record.deviceId)
      openLogsModal()
      return
    }

    if (actionType === 'sensorManage') {
      if (record.deviceId) setCurrentDeviceId(record.deviceId)
      openSensorModal()
      return
    }

    if (actionType === 'deployEquipment') {
      setDefenceForm(record.deviceId, 'deviceId')
      setDefenceForm(record.defenceBeginTime + ' :00', 'beginTime')
      setDefenceForm(record.defenceEndTime + ' :00', 'endTime')
      openDefenceModal()
      return
    }

    // 配对、重置、设防、撤防
    handleOperate(actionType, record)
  }

  // 处理列表的操作按钮
  const handleOperate = (actionType, record) => {
    const confirmActions = ['matchEquipment', 'resetEquipment', 'deployEquipment', 'relieveEquipment', 'unbindDevice',
      'enableBluetooth', 'sensorManage']
    if (confirmActions.includes(actionType)) {
      const params = {
        deviceId: record.deviceId,
        areaId: record.areaId
      }
      if (actionType === 'enableBluetooth') {
        params.enable = !record.enableBluetooth
      }
      Modal.confirm({
        title: '温馨提示',
        content: `你确定需要${actionTypeMap.get(actionType)}此设备？`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          await instance.$store.dispatch(`operation/security/${actionType}`, params).then(res => {
            if (actionType === 'matchEquipment' && res) {
              record.sensorId = res.data.sensorId
              setRemarkForm(res.data.sensorId, 'sensorId')
              setRemarkForm('配对成功，请为传感器添加备注信息', 'title')
              setRemarkForm(false, 'edit')
              setCurrentRecord(record)
              openRemarkModal()
            }
          }).catch(err => {
            alert(err)
          })
          setCurrentRecordId(undefined)
          await fetchList()
        }
      })
    }
  }

  // 数据导出
  const handleExport = () => {
    Modal.confirm({
      title: '温馨提示',
      content: `你确定需要导出当前的数据？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        await exportList()
      }
    })
  }

  return {
    tableChange,
    handleAction
  }
}
