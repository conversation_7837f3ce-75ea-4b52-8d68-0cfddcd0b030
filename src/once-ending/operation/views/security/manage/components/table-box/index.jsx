
import { useSecurityState } from '../useState.js'
import useTable from './useTable.js'
import { NiTable, NiImg } from '@jiuji/nine-ui'
import ModalEdit from '../modal-edit'
import ModalLogs from '../modal-logs/index.vue'
import ModalSensorRemark from '../modal-sensor-remark'
import { deviceState } from '../../constants.js'
import ModalSensor from '../modal-sensor'
import ModalDefence from '../modal-defence'
import { Tooltip } from 'ant-design-vue'

export default {
  data () {
    const hasEditRank = this.$store.getters.userInfo.Rank.includes('afxz')
    const hasLydk = this.$store.getters.userInfo.Rank.includes('lydk')
    return {
      columns: [
        {
          dataIndex: 'areaName',
          key: 'areaName',
          title: '门店',
          width: 150
        },
        {
          dataIndex: 'deviceId',
          key: 'deviceId',
          title: '设备ID',
          width: 150
        },
        {
          dataIndex: 'matchedSensorCount',
          key: 'matchedSensorCount',
          title: '配对状态',
          customRender: text => {
            text = text > 0
            return <i>{ deviceState.match[text].stateStr }</i>
          },
          width: 100
        },
        {
          dataIndex: 'location',
          key: 'location',
          title: '位置'
        },
        {
          dataIndex: 'matchedSensorCount',
          key: 'matchedSensorCount',
          title: '传感器数',
          customRender: (_, record) => {
            return <a-button type="link" onClick={ () => { this.handleAction('sensorManage', record) }}>{ record.matchedSensorCount }</a-button>
          },
          width: 100
        },
        // {
        //   dataIndex: 'defenceBeginTime',
        //   key: 'defenceBeginTime',
        //   title: '开始时间',
        //   width: 100
        // },
        // {
        //   dataIndex: 'defenceEndTime',
        //   key: 'defenceEndTime',
        //   title: '结束时间',
        //   width: 100
        // },
        {
          dataIndex: 'onlineStatus',
          key: 'onlineStatus',
          title: '状态',
          customRender: text => {
            return <i>{ deviceState.online[text].stateStr }</i>
          },
          width: 100
        },
        {
          dataIndex: 'defenceStatus',
          key: 'defenceStatus',
          title: '安防状态',
          customRender: text => {
            return <i>{ deviceState.security[text].stateStr }</i>
          },
          width: 100
        },
        {
          dataIndex: 'alarmReceivers',
          key: 'alarmReceivers',
          title: '报警人',
          customRender: text => {
            return text && text.length ? <Tooltip title={text} placement="topLeft">
              <div class="ellipsis">{text && text.length ? text.join(',') : '-'}</div>
            </Tooltip> : <div>-</div>
          },
          width: 200
        },
        {
          dataIndex: 'enableBluetooth',
          key: 'enableBluetooth',
          title: '蓝牙打卡状态',
          customRender: text => {
            return <i>{ deviceState.bluetooth[text].stateStr }</i>
          },
          width: 100
        },
        {
          key: 'action',
          title: '操作',
          customRender: (_, record) => {
            const securityState = deviceState.security[record.defenceStatus]
            const bluetoothState = deviceState.bluetooth[record.enableBluetooth]
            return (
              <div>
                <a-button
                  type="link"
                  onClick={ () => { this.handleAction('showLogs', record) }}>
                  日志
                </a-button>
                <a-button
                  type="link"
                  disabled={ !record.onlineStatus }
                  onClick={ () => { this.handleAction('matchEquipment', record) }}>
                  配对
                </a-button>
                <a-button
                  type="link"
                  disabled={ !record.onlineStatus }
                  onClick={ () => { this.handleAction('resetEquipment', record) }}>
                  重置
                </a-button>
                <a-button
                  type="link"
                  disabled={ !record.onlineStatus }
                  onClick={ () => { this.handleAction(securityState.action, record) }}>
                  { securityState.actionStr }
                </a-button>
                <a-button type="link"
                  disabled={ !hasEditRank } onClick={ () => { this.handleAction('unbindDevice', record) }}>解绑</a-button>
                {
                  hasLydk ? <a-button
                    type="link"
                    disabled={ !record.onlineStatus }
                    onClick={ () => { this.handleAction(bluetoothState.action, record) }}>
                    { bluetoothState.actionStr }
                  </a-button> : null
                }

              </div>
            )
          },
          width: 300
        }
      ]
    }
  },
  setup () {
    const { state } = useSecurityState() // 页面状态
    const { tableChange, handleAction } = useTable()

    return {
      state,
      tableChange,
      handleAction
    }
  },
  render () {
    const { state, tableChange, handleAction, columns } = this
    const hasEditRank = this.$store.getters.userInfo.Rank.includes('afxz')
    const storeState = this.$store.state.operation.security
    const noData = () => (
      <div class='my-8vh'>
        <NiImg class='noDataImg'
          src="https://img2.ch999img.com/newstatic/14450/04349c93014c93f0.png"/>
        <p class='noDataText'>{ state.isFeatch ? '抱歉，没有查询到数据' : '请点击查询按钮查看数据' }</p>
      </div>
    )

    return (
      <div>
        <NiTable
          locale={{ emptyText: noData }}
          dataSource={ storeState.securityList }
          columns={ columns }
          pagination={ state.pagination }
          footerTotalNum={ 1 }
          onChange={ tableChange }
        >
          <div slot="tool" class='right flex'>
            <div class='mr-8'>
              {
                hasEditRank ? <a-button icon="plus"
                  type="primary"
                  onClick={ () => handleAction('add') }>
                  新增
                </a-button> : ''
              }
            </div>
            <a-button icon="upload"
              onClick={ () => handleAction('export') }>
              导出
            </a-button>
          </div>
        </NiTable>
        { state.editModalIsShow && <ModalEdit/> }
        { state.logsModalIsShow && <ModalLogs/> }
        { state.remarkModalIsShow && <ModalSensorRemark/> }
        { state.sensorModalIsShow && <ModalSensor/> }
        { state.defenceModalIsShow && <ModalDefence/> }
      </div>
    )
  }

}
