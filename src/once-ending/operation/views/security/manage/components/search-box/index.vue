<template>
  <ni-filter
    :form="filterForm"
    :loading="loading"
    @filter="doSearch"
    :label-width="120"
    :item-min-width="420"
    :immediate="false"
  >
    <ni-filter-item label="设备ID">
      <a-input
        allowClear
        placeholder="请输入设备ID"
        v-model="filterForm.deviceId"
      />
    </ni-filter-item>
    <ni-filter-item label="门店">
      <ni-area-select
        allowClear
        multiple
        showSearch
        style="min-width: 320px"
        placeholder="请选择"
        :disabled="!isHQ"
        :maxTagCount="1"
        v-model="filterForm.areaIds"
      />
    </ni-filter-item>
    <ni-filter-item label="在线状态">
      <a-select
        allowClear
        showArrow
        placeholder="请选择"
        :options="OPTIONS.onlineState"
        v-model="filterForm.onlineStatus"
      />
    </ni-filter-item>
    <ni-filter-item label="安防状态">
      <a-select
        allowClear
        showArrow
        placeholder="请选择"
        :options="OPTIONS.defenceStatus"
        v-model="filterForm.defenceStatus"
      />
    </ni-filter-item>
    <ni-filter-item label="蓝牙打卡状态">
      <a-select
        allowClear
        showArrow
        placeholder="请选择"
        :options="OPTIONS.bluetoothStatus"
        v-model="filterForm.bluetoothStatus"
      />
    </ni-filter-item>
    <ni-filter-item label="传感器数小于">
      <a-input-number v-model="filterForm.SensorCountSmallThan" :min="1" />
    </ni-filter-item>
    <ni-filter-item label="有无报警人">
      <a-select
        allowClear
        showArrow
        placeholder="请选择"
        :options="OPTIONS.hasAlarmReceiver"
        v-model="filterForm.hasAlarmReceiver"
      />
    </ni-filter-item>
</ni-filter>
</template>

<script type="text/jsx">
  import { computed, onMounted, getCurrentInstance } from 'vue'
  import { NiFilter, NiFilterItem, NiAreaSelect } from '@jiuji/nine-ui'
  import { OPTIONS } from '../../constants'

  import { useSecurityState } from '../useState.js'
  import useSearch from './useSearch.js'

  export default {
    name: 'search',
    components: {
      NiFilter,
      NiFilterItem,
      NiAreaSelect
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const loading = computed(() => proxy.$store.state.operation.security.loading)
      const { state } = useSecurityState()
      const { fetchList, fetchIsHQ } = useSearch()
      const doSearch = () => {
        fetchList(1) // 筛选区点【查询】按钮，把页面置为第1页
      }
      onMounted(() => {
        fetchIsHQ()
      })
      return {
        loading,
        isHQ: computed(() => state.isHQ),
        filterForm: computed(() => state.filterForm),
        OPTIONS,
        doSearch,
      }
    },
  }
</script>
