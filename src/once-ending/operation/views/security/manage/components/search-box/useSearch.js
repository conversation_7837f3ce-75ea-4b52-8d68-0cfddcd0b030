
import { getCurrentInstance, computed } from 'vue'
import { useSecurityState } from '../useState.js'
import useLastUpdateDate from '../lastUpdateDate/uselastUpdateDate.js'
import { message } from 'ant-design-vue'
import { to } from '@common/utils/common'
import api from '~/api/index.js'

export default function useSearch () {
  const instance = getCurrentInstance().proxy
  const {
    state,
    setIsHQ,
    setIsFetch,
    setPagination
  } = useSecurityState()

  const fetchList = async (doSearch) => {
    const { dispatch, state: { operation } } = instance.$store
    const { setLastUpdateDate } = useLastUpdateDate()
    if (doSearch) setPagination(1, 'current') // 点击查询按钮，页面置为1

    const { current, pageSize } = state.pagination
    const { deviceId, areaIds, onlineStatus, defenceStatus, bluetoothStatus, SensorCountSmallThan, hasAlarmReceiver } = state.filterForm
    let params = {
      currentPage: current,
      pageSize: pageSize,
      deviceId: deviceId,
      areaIds: areaIds,
      onlineStatus: onlineStatus,
      defenceStatus: defenceStatus,
      bluetoothStatus: bluetoothStatus,
      SensorCountSmallThan: SensorCountSmallThan,
      hasAlarmReceiver: hasAlarmReceiver !== undefined ? hasAlarmReceiver : 0
    }

    const flag = await dispatch(
      'operation/security/fetchSecurityList',
      params
    )
    if (flag) {
      const securityListTotal = computed(() => operation.security.securityListTotal).value
      setPagination(securityListTotal, 'total')
      setLastUpdateDate()
      setIsFetch(true)
    }
  }

  const exportList = async (doSearch) => {
    const { dispatch } = instance.$store
    if (doSearch) setPagination(1, 'current') // 点击查询按钮，页面置为1

    const { current, pageSize } = state.pagination
    const { deviceId, areaIds, onlineStatus, defenceStatus, bluetoothStatus, SensorCountSmallThan, hasAlarmReceiver } = state.filterForm
    let params = {
      currentPage: current,
      pageSize: pageSize,
      deviceId: deviceId,
      areaIds: areaIds,
      onlineStatus: onlineStatus,
      defenceStatus: defenceStatus,
      bluetoothStatus: bluetoothStatus,
      SensorCountSmallThan: SensorCountSmallThan,
      hasAlarmReceiver: hasAlarmReceiver !== undefined ? hasAlarmReceiver : 0
    }

    await dispatch(
      'operation/security/exportDeviceInfo',
      params
    ).then(res => {
      if (res.status === 200) {
        const elink = document.createElement('a')
        elink.href = window.URL.createObjectURL(new Blob([res.data], { type: `application/xlsx` }))
        elink.style.display = 'none'
        elink.setAttribute('download', '安防设备.xlsx')
        document.body.appendChild(elink)
        elink.click()
        URL.revokeObjectURL(elink.href) // 释放URL 对象
        document.body.removeChild(elink)
        instance.$message.success('导出成功!')
      } else {
        instance.$message.success('导出失败!')
      }
    }).catch(err => {
      console.error(err)
    })
  }

  const fetchIsHQ = async () => {
    const areaId = computed(() => instance.$store.state.userInfo.areaid).value
    const [err, res] = await to(api.common.getIsCurAreaHQ(areaId))
    if (err) throw err

    const { code, data, userMsg } = res
    if (code !== 0) {
      message.error(userMsg)
      return
    }
    setIsHQ(data)
  }

  return {
    fetchList,
    fetchIsHQ,
    exportList
  }
}
