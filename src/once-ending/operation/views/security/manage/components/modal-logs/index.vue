<script lang="jsx">
  import { getCurrentInstance, ref, computed, defineComponent, toRefs } from 'vue'
  import { useSecurityState } from '../useState.js'
  import useLogs from './useLogs.js'
  export default defineComponent({
    setup () {
      const { proxy } = getCurrentInstance()
      const securityLogs = computed(() => proxy.$store.getters['operation/security/securityLogs'])
      const {
        state
      } = useSecurityState()
      const { closeLogsModal } = useLogs()

      const current = ref(1)

      const logs = computed(() => securityLogs.value.slice(0, current.value * 10))

      const showAll = computed(() => logs.value.length < securityLogs.value.length)

      const loadMore = function () {
        current.value++
      }

      const userData = ref({})

      const userObjId = ref(null)

      const userMouseenter = async function (d) {
        if (userObjId.value === d.userName) return
        userObjId.value = d.userName
        const params = {
          key: d.userName
        }
        const res = await proxy.$api.office.demand.userInfo(params)
        if (res.code === 0) {
          const { data } = res
          userData.value = data
        }
      }

      return {
        ...toRefs(state),
        closeLogsModal,
        logs,
        loadMore,
        showAll,
        userData,
        userMouseenter
      }
    },
    render () {
      const { logsModalIsShow, currentDeviceId, closeLogsModal, fetchLogsLoading, logs, loadMore, showAll, userData, userMouseenter } = this
      return (
<a-modal
  destroyOnClose
  width={ 680 }
  class='securityLogs'
  visible={ logsModalIsShow }
  title={ `设备ID为${currentDeviceId}的日志` }
  footer={ null }
  onCancel={ closeLogsModal }
>
  {
  fetchLogsLoading
  ? <div class="text-center">
  <a-spin/>
</div>
  : <div class="log-box">
  <div class="ni-log-wrap">
    <div class="ant-spin-nested-loading">
      <div class="ant-spin-container">
        <ul class="ant-timeline">
          {
          logs.map((d, i) => <div
          class={['ni-flex', i === logs.length - 1 ? 'ant-timeline-item-last' : '']}>
          <li
            class={['ant-timeline-item ni-log-timeline-item', i === logs.length - 1 ? 'ni-log-timeline-last' : '']}>
          <div class="ant-timeline-item-tail"></div>
          <div
            class="ant-timeline-item-head ant-timeline-item-head-blue"
          ></div>
          <div class="ant-timeline-item-content">
            <div class="ni-flex">
              <div class="ni-ml-10 ni-font-14">
                                <span class="ni-text-333 break-spaces"
                                >{d.comment}</span
                                ><span class="ni-ml-5 inline-block"
              >【<span>
                {
                  d.userName === '系统' ? <span>{d.userName}</span> : <a-popover
                  title={userData.userName}
                  trigger="click"
                  placement="top"
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                  >
                  <div slot="content">
                  <div class="ni-log-user-content" style="min-width: 200px">
                  <p class="ni-log-user-p">手机：{ userData.mobile }</p>
                  <p class="ni-log-user-p">
                  地区：{ userData.area }（{
                userData.combinationArea
              }）
                        </p>
                        <p class="ni-log-user-p">
                          部门：{ userData.department }
                        </p>
                        <p class="ni-log-user-p">岗位：{ userData.role }</p>
                        <p class="ni-log-user-p">
                          职能：{ userData.workKeys }
                        </p>
              </div>
            </div>
            <span onMouseenter={() => { userMouseenter(d) }} class="blue" style="cursor: pointer;"
            >【{ d.userName }】</span
          >
          </a-popover>
                }

                                </span
              >】</span
              ><span class="ni-ml-5 inline-block">{d.time}</span>
              </div>
            </div>
          </div>
          </li>
      </div>)
      }
      </ul>
    </div>
  </div>
  {
  showAll
  ? <div class="ni-log-show-all"><a class="show-all-btn" onClick={loadMore}>查看更多</a>
</div> : null
  }

</div>
  </div>
  }
</a-modal>
)
    }
  })
</script>

<style lang="scss" scoped>
.log-box{
  max-height: 75vh;
  overflow-y: auto;
}
</style>
