
import { getCurrentInstance } from 'vue'
import { useSecurityState } from '../useState.js'

export default function useLogs () {
  const instance = getCurrentInstance().proxy
  const {
    state,
    setLogsModalIsShow,
    setFetchLogsLoading,
    setCurrentRecordId,
    setCurrentDeviceId,
  } = useSecurityState()

  const openLogsModal = () => {
    setLogsModalIsShow(true)
    fetchLogs()
  }
  const closeLogsModal = () => {
    setLogsModalIsShow(false)
    setCurrentRecordId(undefined)
    setCurrentDeviceId(undefined)
    instance.$store.commit('operation/security/setSecurityLogs', [])
  }
  const fetchLogs = async () => {
    setFetchLogsLoading(true)
    await instance.$store.dispatch(
      'operation/security/fetchSecurityLogs',
      { deviceId: state.currentRecordId }
    )
    setFetchLogsLoading(false)
  }

  return {
    fetchLogs,
    openLogsModal,
    closeLogsModal,
  }
}
