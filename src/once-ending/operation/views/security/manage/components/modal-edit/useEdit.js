
import { getCurrentInstance } from 'vue'
import moment from 'moment'
import { message } from 'ant-design-vue'

import { useSecurityState } from '../useState.js'
import useSearch from '../search-box/useSearch.js'

export default function useEdit () {
  const instance = getCurrentInstance().proxy
  const { fetchList } = useSearch()
  const {
    state,
    state: { editForm },
    setEditModalIsShow,
    setEditForm,
    setCurrentRecord,
    setCurrentRecordId,
    setEditType,
    setConfirmLoading
  } = useSecurityState()

  const openEditModal = async () => {
    setEditModalIsShow(true)
    // 新增时，请求可用设备列表 options
    if (state.editType === 'add') {
      await instance.$store.dispatch('operation/security/fetchAvailableEquipments')
    }
    // 打开时，初始化表单(editForm)
    setEditForm()
  }
  const closeEditModal = () => {
    setEditModalIsShow(false)
    // 关闭时，1.清空表单 2.保证没有当前编辑项
    setCurrentRecordId(undefined)
    setCurrentRecord(undefined)
    setEditType(undefined)
    setEditForm()
  }

  // 提交编辑表单
  const handleEditOk = async () => {
    const { editType, currentRecordId } = state
    // 无currentRecordId 为【新增】
    if (!currentRecordId && !editForm.deviceId) {
      message.error('请选择设备ID')
      return
    }
    if (!editForm.areaId) {
      message.error('请选择地区')
      return
    }
    // if (!editForm.beginTime) {
    //   message.error('请设置开始时间')
    //   return
    // }
    // if (!editForm.endTime) {
    //   message.error('请设置结束时间')
    //   return
    // }
    const params = {
      deviceId: editForm.deviceId,
      areaId: +editForm.areaId, // number
      areaName: editForm.areaName,
      location: editForm.location,
      beginTime: '00:00',
      endTime: '23:59'
    }
    if (currentRecordId) params.id = currentRecordId // 有为【编辑】
    setConfirmLoading(true)
    const apiType = {
      add: 'submitSecurityAdd',
      edit: 'submitSecurityEdit'
    }
    const flag = await instance.$store.dispatch(
      `operation/security/${apiType[editType]}`,
      params
    )
    setConfirmLoading(false)
    if (!flag) return
    closeEditModal()
    await fetchList()
  }

  return {
    openEditModal,
    closeEditModal,
    handleEditOk,
  }
}
