
import { getCurrentInstance } from 'vue'
import { NiAreaSelect } from '@jiuji/nine-ui'

import { actionTypeMap } from '../../constants'
import useEdit from './useEdit.js'
import { useSecurityState } from '../useState.js'

export default () => {
  const { state, setEditForm } = useSecurityState()
  const { closeEditModal, handleEditOk } = useEdit()
  const instance = getCurrentInstance().proxy
  const idOptions = instance.$store.getters['operation/security/availableEquipmentsOptions']
  const hasEditRank = instance.$store.getters.userInfo.Rank.includes('afxz')

  return (
    <a-modal
      title={ `设备${actionTypeMap.get(state.editType)}` }
      visible={ state.editModalIsShow }
      confirm-loading={ state.confirmLoading }
      onOk={ handleEditOk }
      onCancel={ closeEditModal }
      maskClosable={ false }
    >
      <a-form labelCol={{ span: 6 } }wrapperCol={{ span: 16 }}>
        <a-form-item label="设备ID">
          {
            state.editType === 'edit' ? <i>{ state.editForm.deviceId }</i> : (
              <a-select
                showArrow
                disabled={ !hasEditRank }
                class="full-width"
                placeholder="请选择"
                options={ idOptions }
                showSearch
                value={ state.editForm.deviceId }
                onChange={ e => { setEditForm(e, 'deviceId') } }
              />
            )
          }
        </a-form-item>
        <a-form-item label="绑定地区">
          <NiAreaSelect
            disabled={ !hasEditRank }
            showSearch
            placeholder="请选择"
            class="full-width"
            value={ state.editForm.areaId }
            onChange={ (key, value) => {
              setEditForm(key, 'areaId')
              if (value === undefined || value.length === 0) {
                return
              }
              setEditForm(value, 'areaName')
            }}
          />
        </a-form-item>
        <a-form-item label="位置">
          <a-input
            allowClear
            placeholder="请输入位置"
            value={ state.editForm.location }
            onChange={ e => { setEditForm(e?.target?.value, 'location') } }
          />
        </a-form-item>
        {/* <a-form-item label="开始时间"> */}
        {/*   <a-time-picker */}
        {/*     class="full-width" */}
        {/*     value={ state.editForm.beginTime } */}
        {/*     onChange={ e => { setEditForm(e, 'beginTime') } } */}
        {/*   /> */}
        {/* </a-form-item> */}
        {/* <a-form-item label="结束时间"> */}
        {/*   <a-time-picker */}
        {/*     class="full-width" */}
        {/*     value={ state.editForm.endTime } */}
        {/*     onChange={ e => { setEditForm(e, 'endTime') } } */}
        {/*   /> */}
        {/* </a-form-item> */}
      </a-form>
    </a-modal>
  )
}
