import { useSecurityState } from '../useState.js'
import { getCurrentInstance } from 'vue'
import useSensor from './useSensor.js'
import { NiTable, NiImg } from '@jiuji/nine-ui'

export default {
  data () {
    return {
      columns: [
        {
          dataIndex: 'deviceId',
          key: 'deviceId',
          title: '设备ID'
        },
        {
          dataIndex: 'sensorId',
          key: 'sensorId',
          title: '传感器ID'
        },
        {
          dataIndex: 'sensorName',
          key: 'sensorName',
          title: '传感器备注'
        },
        {
          dataIndex: 'matchTime',
          key: 'matchTime',
          title: '配对时间',
          customRender: text => {
            return <i>{ text }</i>
          }
        },
        {
          dataIndex: 'sensorAvailable',
          key: 'sensorAvailable',
          title: '传感器状态',
          customRender: text => {
            return <i>{ text ? '可用' : '不可用'}</i>
          }
        },
        {
          key: 'action',
          title: '操作',
          width: '10em',
          customRender: (_, record) => {
            return (
              <div>
                <a-button
                  type="link"
                  onClick={ () => { this.handleAction('edit', record) }}>
                  修改
                </a-button>
                <a-button
                  type="link"
                  onClick={ () => { this.handleAction('delete', record) }}>
                  删除
                </a-button>
              </div>
            )
          }
        }
      ]
    }
  },
  setup () {
    const { state } = useSecurityState() // 页面状态
    const { closeSensorModal, handleAction } = useSensor()

    return {
      state,
      closeSensorModal,
      handleAction
    }
  },
  render () {
    const { state, columns } = this
    const sensorList = getCurrentInstance().proxy.$store.getters['operation/security/sensorList']
    const noData = () => (
      <div class='my-8vh'>
        <NiImg class='noDataImg'
          src="https://img2.ch999img.com/newstatic/14450/04349c93014c93f0.png"/>
        <p class='noDataText'>{ state.isFeatch ? '抱歉，没有查询到数据' : '请点击查询按钮查看数据' }</p>
      </div>
    )

    return (
      <a-modal
        destroyOnClose
        width={ 1440 }
        visible={ state.sensorModalIsShow }
        title={ `传感器管理` }
        onOk={ this.closeSensorModal }
        onCancel={ this.closeSensorModal }
      >
        <NiTable
          locale={{ emptyText: noData }}
          dataSource={ sensorList }
          columns={ columns }
          footerTotalNum={ 1 }
        >
        </NiTable>
      </a-modal>
    )
  }

}
