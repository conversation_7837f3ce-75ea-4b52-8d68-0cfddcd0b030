import { getCurrentInstance } from 'vue'
import { Modal } from 'ant-design-vue'
import { useSecurityState } from '../useState.js'
import useSearch from '../search-box/useSearch.js'
import { actionTypeMap } from '../../constants'
import useSensorRemark from '../modal-sensor-remark/useSensorRemark.js'

export default function useTableActions () {
  const instance = getCurrentInstance().proxy
  const { fetchList } = useSearch()
  const {
    state,
    setSensorModalIsShow,
    setCurrentRecord,
    setRemarkForm,
  } = useSecurityState()
  const openSensorModal = () => {
    setSensorModalIsShow(true)
    fetchSensor()
  }
  const closeSensorModal = () => {
    setSensorModalIsShow(false)
  }
  const { openRemarkModal } = useSensorRemark()

  // 处理数据
  const handleAction = (actionType, record) => {
    const params = {
      deviceId: record.deviceId,
      sensorId: record.sensorId
    }
    if (actionType === 'edit') {
      setRemarkForm(record.sensorId, 'sensorId')
      setRemarkForm('请修改传感器的备注信息', 'title')
      setRemarkForm(record.sensorName, 'remark')
      setRemarkForm(true, 'edit')
      setCurrentRecord(record)
      openRemarkModal()
      return
    }
    Modal.confirm({
      title: '温馨提示',
      content: `你确定需要${actionTypeMap.get(actionType)}此传感器？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        await instance.$store.dispatch(
          `operation/security/deleteDeviceSensor`,
          params
        ).then(resp => {
          fetchList()
          openSensorModal()
        })
      }
    })
  }
  const fetchSensor = async () => {
    await instance.$store.dispatch(
      'operation/security/getDeviceMatchedSensors',
      { deviceId: state.currentRecordId }
    )
  }

  return {
    openSensorModal,
    closeSensorModal,
    handleAction,
    fetchSensor
  }
}
