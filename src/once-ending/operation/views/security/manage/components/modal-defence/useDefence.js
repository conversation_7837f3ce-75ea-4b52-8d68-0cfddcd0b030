import { getCurrentInstance } from 'vue'
import { message } from 'ant-design-vue'
import moment from 'moment'
import { useSecurityState } from '../useState.js'
import useSearch from '../search-box/useSearch.js'
import security from '@/operation/api/security'

export default function useDefence () {
  const instance = getCurrentInstance().proxy
  const { fetchList } = useSearch()
  const {
    state: { defenceForm },
    setDefenceModalIsShow,
    setConfirmLoading
  } = useSecurityState()

  const openDefenceModal = async () => {
    setDefenceModalIsShow(true)
  }
  const closeDefenceModal = () => {
    setDefenceModalIsShow(false)
  }

  // 提交编辑表单
  const handleDefenceOk = async () => {
    if (!defenceForm.beginTime) {
      message.error('请设置开始时间')
      return
    }
    if (!defenceForm.endTime) {
      message.error('请设置结束时间')
      return
    }
    const params = {
      deviceId: defenceForm.deviceId,
      beginTime: moment(defenceForm.beginTime).format('HH:mm'),
      endTime: moment(defenceForm.endTime).format('HH:mm')
    }
    setConfirmLoading(true)
    const flag = await instance.$store.dispatch(
      `operation/security/deployEquipment`,
      params
    )
    // 调用一下删除延迟设防记录的接口
    security.deleteDelayRecord({ deviceId: defenceForm.deviceId })
    setConfirmLoading(false)
    if (!flag) return
    closeDefenceModal()
    await fetchList()
  }

  return {
    openDefenceModal,
    closeDefenceModal,
    handleDefenceOk,
  }
}
