import useDefence from './useDefence.js'
import { useSecurityState } from '../useState.js'

export default () => {
  const { state, setDefenceForm } = useSecurityState()
  const { closeDefenceModal, handleDefenceOk } = useDefence()
  return (
    <a-modal
      destroyOnClose
      title={ `设备设防` }
      visible={ state.defenceModalIsShow }
      confirm-loading={ state.confirmLoading }
      onOk={ handleDefenceOk }
      onCancel={ closeDefenceModal }
      maskClosable={ false }
    >
      <a-form labelCol={{ span: 6 } }wrapperCol={{ span: 16 }}>
        <a-form-item label="设备ID">
          <i>{ state.defenceForm.deviceId }</i>
        </a-form-item>
        <a-form-item label="开始时间">
          <a-time-picker
            class="full-width"
            value={ state.defenceForm.beginTime }
            onChange={ e => { setDefenceForm(e, 'beginTime') } }
          />
        </a-form-item>
        <a-form-item label="结束时间">
          <a-time-picker
            class="full-width"
            value={ state.defenceForm.endTime }
            onChange={ e => { setDefenceForm(e, 'endTime') } }
          />
        </a-form-item>
      </a-form>
    </a-modal>
  )
}
