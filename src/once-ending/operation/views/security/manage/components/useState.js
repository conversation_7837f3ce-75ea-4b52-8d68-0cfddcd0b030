import { reactive, inject } from 'vue'
import moment from 'moment'
const key = Symbol('security')

export function useSecurityState () {
  return inject(key)
}

export function createSecurityState (provide) {
  const state = reactive({
    // 查询参数 主searchLoading 放在store
    filterForm: {
      deviceId: undefined,
      areaId: undefined,
      onlineStatus: 0, // 在线状态，0：所有，1：在线，-1：离线
      defenceStatus: 0, // 设防状态，0：所有，1：在线，-1：离线
      bluetoothStatus: 0, // 蓝牙打卡状态，0：所有，1：启用，-1：未启用
      SensorCountSmallThan: undefined, // 传感器数量少于，0：不过滤，>0,过滤传感器数量少于指定值的记录
      hasAlarmReceiver: undefined // 报警人
    },
    isFetch: false,
    isHQ: false,
    // 表格
    currentRecordId: undefined,
    currentDeviceId: undefined,
    currentRecord: {}, // 当前操作记录。 打开操作modal,赋值； 关闭操作modal清空
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      pageSizeOptions: ['1', '10', '20', '50', '100', '200'],
      showQuickJumper: true,
      showTotal: total => `共计${total}条`
    },
    // 日志
    logsModalIsShow: false,
    fetchLogsLoading: false,
    // 编辑
    editModalIsShow: false,
    editType: undefined,
    confirmLoading: false,
    editForm: {
      deviceId: undefined,
      areaId: undefined,
      areaName: undefined,
      location: undefined,
      beginTime: undefined,
      endTime: undefined
    },
    remarkForm: {
      deviceId: undefined,
      sensorId: undefined,
      remark: undefined,
      title: undefined,
      edit: undefined,
    },
    remarkModalIsShow: false,
    sensorForm: {
      deviceId: undefined,
      sensorId: undefined,
      sensorName: undefined,
      matchTime: undefined,
      sensorAvailable: undefined,
    },
    sensorModalIsShow: false,
    defenceForm: {
      deviceId: undefined,
      beginTime: undefined,
      endTime: undefined
    },
    defenceModalIsShow: false,
  })

  const setFilterForm = (val, key) => {
    if (key && typeof key === 'string') {
      state.filterForm[key] = val
    } else {
      state.filterForm = val
    }
  }
  const setRemarkForm = (val, key) => {
    if (key) {
      state.remarkForm[key] = val
      return
    }
    Object.keys(state.editForm).forEach(item => {
      state.remarkForm[item] = state.currentRecord[item]
    })
  }
  const setSensorForm = (val, key) => {
    if (key) {
      state.sensorForm[key] = val
      return
    }
    Object.keys(state.sensorForm).forEach(item => {
      state.sensorForm[item] = state.currentRecord[item]
    })
  }
  const setDefenceForm = (val, key) => {
    if (key) {
      state.defenceForm[key] = val
      return
    }
    Object.keys(state.defenceForm).forEach(item => {
      state.defenceForm[item] = state.currentRecord[item]
    })
  }
  const setDefenceModalIsShow = val => { state.defenceModalIsShow = val }
  const setSensorModalIsShow = val => { state.sensorModalIsShow = val }
  const setRemarkModalIsShow = val => { state.remarkModalIsShow = val }
  const setIsFetch = val => { state.isFeatch = val }
  const setIsHQ = val => { state.isHQ = val }
  const setCurrentRecord = val => { state.currentRecord = val || {} }
  const setPagination = (val, key) => {
    if (key && typeof key === 'string') {
      state.pagination[key] = val
    } else {
      state.pagination = val
    }
  }
  const setLogsModalIsShow = val => { state.logsModalIsShow = val }
  const setFetchLogsLoading = val => { state.fetchLogsLoading = val }
  const setCurrentRecordId = val => { state.currentRecordId = val }
  const setCurrentDeviceId = val => { state.currentDeviceId = val }
  const setEditModalIsShow = val => { state.editModalIsShow = val }
  const setEditType = val => { state.editType = val }
  const setConfirmLoading = val => { state.confirmLoading = val }
  const setEditForm = (value, key) => {
    if (key) { // 传key,value 用作editForm字段赋值
      state.editForm[key] = value
      return
    }
    // 不传参，可用作 记录新增、编辑操作，和重置editForm
    // 用作新增与重置时，保证当前编辑记录（currentRecord）为空对象或字段项为空
    const beginTime = moment().hours(22).minute(0).second(0)
    Object.keys(state.editForm).forEach(item => {
      if (item === 'beginTime') {
        state.editForm[item] = state.currentRecord[item]
          ? moment(state.currentRecord[item], 'HH:mm:ss')
          : beginTime
      } else if (item === 'endTime') {
        state.editForm[item] = state.currentRecord[item]
          ? moment(state.currentRecord[item], 'HH:mm:ss')
          : moment(beginTime).add(11, 'H')
      } else if (item === 'areaId') {
        state.editForm[item] = [state.currentRecord[item]]
      } else {
        state.editForm[item] = state.currentRecord[item]
      }
    })
  }

  const security = {
    state,

    setFilterForm,
    setIsFetch,
    setIsHQ,
    setCurrentRecordId,
    setCurrentDeviceId,
    setCurrentRecord,
    setPagination,
    setLogsModalIsShow,
    setFetchLogsLoading,
    setEditModalIsShow,
    setEditType,
    setConfirmLoading,
    setEditForm,
    setRemarkForm,
    setRemarkModalIsShow,
    setSensorForm,
    setSensorModalIsShow,
    setDefenceForm,
    setDefenceModalIsShow,
  }
  provide(key, security)
  return security
}
