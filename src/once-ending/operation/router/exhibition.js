import layout from '@common/layout/layout'
import routerContainer from '../layout/router-container'

function load (component) {
  return () => import(`../views/exhibition/${component}.vue`)
}

export default {
  path: '/store',
  component: layout,
  children: [
    {
      path: 'exhibition',
      component: routerContainer,
      children: [
        {
          path: '',
          component: () => import('../views/exhibition/engineering-picture/index.vue'),
          name: 'STORE_Exhibition',
          meta: { title: '陈列柜管理' }
        },
        {
          path: 'goodsDisplay',
          component: () => import('../views/exhibition/goods-display/index.vue'),
          meta: { title: window.tenant.xtenant < 1000 ? '门店展陈' : '货品陈列', keepAlive: true, statistics: true, statisticsUrl: '/staticpc/#/store/exhibition/goodsDisplay' }
        },
        {
          path: 'cad',
          component: () => import('../views/exhibition/cad/index.vue'),
          name: 'STORE_ExhibitionCad',
          meta: { title: '门店工程图' }
        },
        {
          path: 'upper-limit',
          name: 'STORE_ExhibitionUpperLimit',
          component: () => import('../views/exhibition/upper-limit/index.vue'),
          meta: { title: '陈列上限设置' }
        },
        {
          path: 'product-detail',
          name: 'STORE_ExhibitionDetail',
          component: () => import('../views/exhibition/product-detail/index.vue'),
          meta: { title: '陈列商品明细' }
        },
        {
          path: 'stay-display-product',
          name: 'stayDisplayProduct',
          component: () => import('../views/exhibition/stay-display-product/index.vue'),
          meta: { title: '待陈列商品明细' }
        },
        {
          path: 'alert-manage',
          name: 'STORE_AlertManage',
          component: () => import('../views/exhibition/alert-manage/index.vue'),
          meta: { title: '预警管理' }
        },
        {
          path: 'product-manage',
          component: () => import('../views/exhibition/product-manage/index.vue'),
          meta: { title: '陈列商品管理' }
        },
        {
          path: 'product-small',
          component: () => import('../views/exhibition/product-small/index.vue'),
          meta: { title: '小件陈列商品参考清单' }
        },
        {
          path: 'cabinet-label',
          component: () => import('../views/exhibition/cabinet-label/index.vue'),
          meta: { title: '陈列柜标签管理' }
        },
        {
          path: 'single-feedback',
          name: 'STORE_ExhibitionFeedback',
          component: () => import('../views/exhibition/single-feedback/index.vue'),
          meta: { title: '单品陈列反馈' }
        },
        {
          path: 'single-manage',
          name: 'STORE_ExhibitionSingleManage',
          component: () => import('../views/exhibition/single-manage/index.vue'),
          meta: { title: '单品陈列管理' }
        },
        {
          path: 'zone-manage',
          name: 'STORE_ExhibitionZoneManage',
          component: () => import('../views/exhibition/zone-manage/index.vue'),
          meta: { title: '专区管理' }
        },
      ]
    }
  ]
}
