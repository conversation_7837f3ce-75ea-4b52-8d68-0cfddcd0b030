import routerContainer from '@layout/router-container'

function load (component) {
  return () => import(`../views/message/${component}.vue`)
}

export default {
  path: 'member',
  component: routerContainer,
  children: [
    {
      path: 'group-list',
      name: 'group-list',
      component: () => import('../views/member/group-list/index.vue'),
      meta: { title: '用户分群', keepAlive: true }
    },
    {
      path: 'vip-list/:id',
      component: () => import('../views/member/vip-list/index.vue'),
      meta: { title: '会员列表' }
    },
    {
      path: 'operate/:id',
      component: () => import('../views/member/operate/index.vue'),
      meta: { title: '用户画像' }
    },
    {
      path: 'group-edit/:id',
      component: () => import('../views/member/group-edit/index.vue'),
      meta: { title: '用户人群新增/编辑' }
    }
  ]
}
