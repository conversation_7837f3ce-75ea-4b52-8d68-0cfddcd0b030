import layout from '@common/layout/layout'
import routerContainer from '@common/layout/router-container'

function load (component) {
  return () => import(`../views/statistics-report/${component}.vue`)
}

export default {
  path: 'statistics-report',
  component: routerContainer,
  children: [
    {
      path: 'saleStatistics',
      component: () => import('../views/statistics-report/sale-statistics/index.vue'),
      meta: { title: '销售报表', keepAlive: true }
    },
    {
      path: 'saleStatisticsCopy',
      component: () => import('../views/statistics-report/sale-statistics/index-copy.vue'),
      meta: { title: '销售报表', keepAlive: true }
    },
    {
      path: 'saleStatisticsDetail',
      component: () => import('../views/statistics-report/sale-statistics/detail.vue'),
      meta: { title: '销售详情' }
    },
    {
      path: 'bulky-brand-sale',
      component: () => import('../views/statistics-report/bulky-brand-sale/index.vue'),
      meta: { title: '大件品牌销售统计', keepAlive: true, requirePWD: true }
    },
    {
      path: 'value-added-analysis',
      component: () => import('../views/statistics-report/value-added-analysis/list/index.vue'),
      meta: { title: '增值业务分析', keepAlive: true, requirePWD: true }
    },
    {
      path: 'add-sub',
      component: () => import('../views/statistics-report/add-sub/index.vue'),
      meta: { title: '识别码加单统计', keepAlive: true, requirePWD: true, permission: '2c5' }
    },
    {
      path: 'add-sub-area',
      component: () => import('../views/statistics-report/add-sub/area.vue'),
      meta: { title: '门店号码加单量统计', keepAlive: true }
    },
    {
      path: 'add-sub-people',
      component: () => import('../views/statistics-report/add-sub/people.vue'),
      meta: { title: '用户号码加单量统计', keepAlive: true }
    },
    {
      path: 'bussiness-report',
      component: () => import('../views/statistics-report/bussiness-report/index.vue'),
      meta: { title: '赛马业绩统计', keepAlive: true, requirePWD: true }
    },
    {
      path: 'bussiness-report/perfect-order',
      component: () => import('../views/statistics-report/perfect-order/index.vue'),
      meta: { title: '销售统计明细' }
    },
    {
      path: 'complaint-overtime',
      component: () => import('../views/statistics-report/complaint-overtime/index.vue'),
      meta: { title: '投诉超时统计' }
    },
    {
      path: 'feedback-complaint-overtime',
      component: () => import('../views/statistics-report/feedback-complaint-overtime/index.vue'),
      meta: { title: '投诉超时统计' }
    },
    {
      path: 'store-reception',
      component: () => import('../views/statistics-report/store-reception/index.vue'),
      meta: { title: '门店接待统计' },
    },
    {
      path: 'store-reception/detail',
      component: () => import('../views/statistics-report/store-reception/index.vue'),
      meta: { title: '门店接待统计明细' }
    },
    {
      path: 'store-reception/chart',
      component: () => import('../views/statistics-report/store-reception/index.vue'),
      meta: { title: '门店接单与订单转化走势图' }
    },
    {
      path: 'business',
      component: () => import('../views/statistics-report/business/index.vue'),
      meta: { title: '加盟店经营分析报表' }
    },
    {
      path: 'afterSales',
      component: () => import('../views/statistics-report/after-sales/index.vue'),
      meta: { title: '售后费用报表' }
    },
    {
      path: 'sales-person',
      component: () => import('../views/statistics-report/sales-person/index.vue'),
      meta: { title: '销售人员业绩统计', requirePWD: true }
    },
    {
      path: 'dj-bussiness-report',
      component: () => import('../views/statistics-report/dj-bussiness-report/index.vue'),
      meta: { title: '大疆赛马业绩统计', keepAlive: true, requirePWD: true, permission: '2c5' }
    }
  ]
}
