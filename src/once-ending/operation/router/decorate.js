import routerContainer from '@layout/router-container'

export default {
  path: 'decorate',
  component: routerContainer,
  children: [
    {
      path: 'list',
      component: () => import('../views/decorate/list/index.vue'),
      meta: { title: window.tenant?.xtenant < 1000 ? '合作伙伴装修设计' : '门店设计', keepAlive: true, permission: 'zxgd' }
    },
    {
      path: 'detail/:id',
      component: () => import('../views/decorate/detail/index.vue'),
      meta: { title: '项目详情', permission: 'zxgd' }
    },
    {
      path: 'edit-work-order/:id',
      component: () => import('../views/decorate/edit-work-order/index.vue'),
      meta: { title: '添加工单', permission: 'zxgd' }
    },
    {
      path: 'work-order-detail/:id',
      component: () => import('../views/decorate/edit-work-order/index.vue'),
      meta: { title: '工单详情', permission: 'zxgd' }
    },
  ]
}
