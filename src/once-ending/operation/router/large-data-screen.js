import layout from '@common/layout/layout'
import routerContainer from '@common/layout/router-container'

function load (component) {
  return () => import(`../views/large-data-screen/${component}.vue`)
}

export default {
  path: '',
  component: routerContainer,
  children: [
    {
      path: 'large-data-screen',
      name: 'large-data-screen',
      component: () => import('../views/large-data-screen/index.vue'),
      meta: { title: '数据大屏配置', keepAlive: true }
    }
  ]
}
