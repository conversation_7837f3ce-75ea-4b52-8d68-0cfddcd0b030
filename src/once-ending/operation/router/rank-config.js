import layout from '@common/layout/layout'
import routerContainer from '../layout/router-container'

function load (component) {
  return () => import(`../views/rank-config/${component}.vue`)
}

export default {
  path: '/',
  component: layout,
  children: [
    {
      path: 'rank-config',
      name: 'rank-config',
      component: routerContainer,
      children: [
        {
          path: '',
          component: () => import('../views/rank-config/list/index.vue'),
          meta: { title: '管控排行配置' }
        },
        {
          path: 'user',
          component: () => import('../views/rank-config/user/index.vue'),
          meta: { title: '经营质量定级' }
        },
      ]
    }
  ]
}
