import layout from '@common/layout/layout'
import routerContainer from '../layout/router-container'

function load (component) {
  return () => import(`../views/work-order/${component}.vue`)
}

export default [
  {
    path: '/',
    component: layout,
    children: [
      {
        path: 'operation/work-order/statistics',
        name: 'statistics',
        component: () => import('../views/work-order/statistics/index.vue'),
        meta: { title: '工单统计', keepAlive: true }
      }
    ]
  },
  {
    path: '/',
    component: layout,
    children: [
      {
        path: 'work-order',
        name: 'work-order',
        component: routerContainer,
        children: [
          {
            path: '',
            component: () => import('../views/work-order/list/index.vue'),
            meta: { title: '工单管理' },
          },
          {
            path: 'detail/:id',
            component: () => import('../views/work-order/detail/index.vue'),
            meta: {
              title: '工单详情',
              statistics: true,
              statisticsUrl: (to) => `/staticpc/#/work-order/detail/${to.params.id}`
            },
          },
          {
            path: 'categroy-config',
            component: () => import('../views/work-order/categroy-config/index.vue'),
            meta: { title: '管控分类配置', permission: 'gdpz' },
          },
        ]
      }
    ]
  }
]
