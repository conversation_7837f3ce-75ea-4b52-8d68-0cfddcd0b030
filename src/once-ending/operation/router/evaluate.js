import layout from '@layout/layout'

function load (component) {
  return () => import(`../views/evaluate/${component}.vue`)
}
export default [
  {
    path: '/operation/evaluate',
    component: layout,
    children: [
      {
        path: 'label',
        name: 'label',
        component: () => import('../views/evaluate/label/index.vue'),
        meta: { title: '评价标签管理' }
      },
      {
        path: 'questionnaire',
        name: 'questionnaire',
        component: () => import('../views/evaluate/questionnaire/index.vue'),
        meta: { title: '客评问卷调查' }
      },
      {
        path: 'questionnaire/:id',
        name: 'questionnaire-detail',
        component: () => import('../views/evaluate/questionnaire/detail.vue'),
        meta: { title: '问卷详情' }
      },
      {
        path: 'integral-manage',
        name: 'integral-managel',
        component: () => import('../views/evaluate/integral-manage/list/index.vue'),
        meta: { title: '客评投诉积分管理' }
      },
      {
        path: 'integral-manage/:id',
        name: 'integral-managel-detail',
        component: () => import('../views/evaluate/integral-manage/detail/index.vue'),
        meta: { title: '客评投诉积分详情' }
      },
      {
        path: 'rank-list',
        component: () => import('../views/evaluate/rank-list/index.vue'),
        meta: { title: window.tenant.xtenant < 1000 ? '服务&运营管控分排行' : '绩效排行' }
      },
      {
        path: 'service-score-statistics',
        component: () => import('../views/evaluate/service-score-statistics/index.vue'),
        meta: { title: '服务分统计', keepAlive: true }
      }
    ]
  },
  {
    path: '/evaluate',
    name: 'evaluate',
    component: layout,
    meta: { title: '评价' },
    children: [
      // 客户评价列表
      {
        path: '',
        component: () => import('../views/evaluate/list/index.vue'),
        meta: { title: '评价管理' }
      },
      {
        path: 'detail',
        component: () => import('../views/evaluate/detail/index.vue'),
        meta: { title: '客户评价详情' }
      },
      {
        path: 'score-statistics',
        component: () => import('../views/evaluate/score-statistics/index.vue'),
        meta: { title: '客户评价得分统计', keepAlive: true }
      },
      {
        path: 'score-statistics-detail',
        component: () => import('../views/evaluate/score-statistics-detail/index.vue'),
        meta: { title: '客户评价得分统计详情' }
      }
    ]
  }
]
