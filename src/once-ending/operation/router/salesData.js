import salesDateLayout from '../views/sales-data-management/components/salesDateLayout'
import routerContainer from '@operation/layout/router-container'

export default {
  path: 'sales-data',
  component: salesDateLayout,
  meta: { title: '销售任务管理' },
  redirect: 'sales-data/task-configuration',
  children: [
    {
      path: 'task-configuration',
      meta: { title: '任务管理' },
      icon: 'user',
      component: routerContainer,
      redirect: 'task-configuration/task-project-configuration',
      children: [
        {
          path: 'task-project-configuration',
          meta: { title: '任务项配置' },
          component: () => import('../views/sales-data-management/task/task-project-configuration.vue'),
        },
        {
          path: 'task-project-manage',
          meta: { title: '任务组管理', keepAlive: true, permission: 'xsrw', isBack: false },
          component: () => import('../views/sales-data-management/task/task-project-manage.vue'),
        },
        {
          path: 'task-project-manage/:id',
          name: 'task-project-edit',
          meta: { title: '任务组管理' },
          component: () => import('../views/sales-data-management/task/task-project-manage-edit.vue'),
        },
        {
          path: 'task-project-manage/:id/task-project-detail',
          name: 'task-project-detail',
          meta: { title: '任务组管理' },
          component: () => import('../views/sales-data-management/task/task-project-manage-edit.vue'),
        },
        {
          path: 'task-configuration-manage',
          meta: { title: '任务配置', keepAlive: true, permission: 'xsrw', isBack: false },
          component: () => import('../views/sales-data-management/task/task-configuration.vue'),
        },
        {
          path: 'task-configuration-manage/:id',
          name: 'task-configuration-edit',
          meta: { title: '任务量配置' },
          component: () => import('../views/sales-data-management/task-configuration/task-configuration-edit.vue'),
        },
        {
          path: 'task-configuration-manage/:id/task-configuration-detail',
          name: 'task-configuration-detail',
          meta: { title: '任务量配置' },
          component: () => import('../views/sales-data-management/task-configuration/task-configuration-edit.vue'),
        },
        {
          path: 'task-achieve-inquire',
          meta: { title: '任务达成查询', keepAlive: true },
          component: () => import('../views/sales-data-management/task/task-achieve-inquire.vue'),
        },
        {
          path: 'task-project-configuration/:id',
          meta: { title: '任务项配置' },
          component: () => import('../views/sales-data-management/task/task-project-edit.vue'),
        },
        {
          path: 'task-achieve-inquire/:id',
          meta: { title: '任务达成量' },
          component: () => import('../views/sales-data-management/task/task-achieve-detail.vue'),
        },
        {
          path: 'task-achieve-inquire/:id/task-achievedVolume-detail',
          meta: { title: '任务达成量' },
          component: () => import('../views/sales-data-management/task/task-achievedVolume-detail.vue'),
        },
      ]
    },
    {
      path: 'report-management',
      meta: { title: '报表管理' },
      icon: 'align-center',
      component: routerContainer,
      redirect: 'report-management/report-statistics-configuration',
      children: [
        {
          path: 'report-statistics-configuration',
          meta: { title: '报表统计项配置', permission: 'xsrw' },
          component: () => import('../views/sales-data-management/report/report-statistics-configuration.vue'),
        },
        {
          path: 'report-configuration',
          meta: { title: '报表配置', keepAlive: true, permission: 'xsrw', isBack: false },
          component: () => import('../views/sales-data-management/report/report-configuration.vue'),
        },
        {
          path: 'report-statistics-configuration/:id',
          meta: { title: '统计项配置' },
          component: () => import('../views/sales-data-management/report/report-project-edit.vue'),
        },
        {
          path: 'report-configuration/:id',
          name: 'report-statistics-edit',
          meta: { title: '报表配置' },
          component: () => import('../views/sales-data-management/report/report-configuration-edit.vue'),
        },
        {
          path: 'report-configuration/:id/report-statistics-detail',
          name: 'report-statistics-detail',
          meta: { title: '报表配置' },
          component: () => import('../views/sales-data-management/report/report-configuration-edit.vue'),
        },
      ]
    },
    {
      path: 'statistical-report',
      meta: { title: '统计报表' },
      icon: 'align-center',
      component: routerContainer,
      redirect: 'statistical-report/report-detail/:id',
      children: [
        {
          path: 'report-detail/:id',
          meta: { title: '报表' },
          component: () => import('../views/sales-data-management/statistical/statistical-report.vue'),
        },
      ]
    },
    // {
    //   path: 'type-management',
    //   meta: { title: '类型管理' },
    //   icon: 'align-center',
    //   component: () => import('../views/sales-data-management/type/type-management.vue'),
    // },
    {
      path: 'statistical-import',
      meta: { title: '统计数据导入' },
      icon: 'download',
      component: () => import('../views/sales-data-management/statistical-report-import/report-import-configuration.vue'),
    },
  ],
}
