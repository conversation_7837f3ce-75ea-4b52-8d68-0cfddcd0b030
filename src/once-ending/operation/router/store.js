import layout from '@common/layout/layout'
import routerContainer from '@common/layout/router-container'

export default {
  path: 'store',
  component: routerContainer,
  meta: { title: '门店' },
  children: [
    {
      path: 'leave',
      component: () => import('../views/store/leave/index.vue'),
      meta: { title: '暂离统计' }
    },
    {
      path: 'cut-statistics',
      component: () => import('../views/store/cutting/cut-statistics/index.vue'),
      meta: { title: '切膜数据统计' }
    },
    {
      path: 'smart-check',
      component: () => import('../views/store/smart-check/list/index.vue'),
      meta: { title: '智能化检查', permission: 'znjc' }
    },
    {
      path: 'competition',
      component: () => import('../views/store/competition/index.vue'),
      meta: { title: '竞争门店信息' }
    }, {
      path: 'cutting-count-statistics',
      component: () => import('../views/store/cutting/cutting-count-statistics/index.vue'),
      meta: { title: '切膜数量统计', permission: 'qmtj' }
    },
    {
      path: 'cutting-statistics',
      component: () => import('../views/store/cutting/cutting-statistics/index.vue'),
      meta: { title: '切膜统计', permission: 'scqm' }
    },
    {
      path: 'duty-log/list',
      component: () => import('../views/store/duty-log/list/index.vue'),
      meta: { title: '值班信息录入', permission: 'zblr' }
    },
    {
      path: 'duty-log/config',
      component: () => import('../views/store/duty-log/config/index.vue'),
      meta: { title: '值班部门配置', permission: 'zblr' }
    }
  ]
}
