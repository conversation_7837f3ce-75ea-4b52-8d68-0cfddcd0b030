import layout from '@common/layout/layout'
import routerContainer from '../layout/router-container'

function load (component) {
  return () => import(`../views/achievement/${component}.vue`)
}

export default {
  path: '/store',
  component: layout,
  children: [
    {
      path: 'performance',
      component: routerContainer,
      children: [
        {
          path: 'season',
          component: () => import('../views/achievement/index.vue'),
          meta: { title: '业绩榜单配置' }
        }
      ]
    }
  ]
}
