import routerContainer from '@common/layout/router-container'

export default {
  path: 'insurance',
  component: routerContainer,
  children: [
    {
      path: 'list',
      component: () => import('../views/insurance/list/index.vue'),
      meta: { title: '保险管理', keepAlive: true, permission: 'glbx' }
    },
    {
      path: 'detail/:id',
      component: () => import('../views/insurance/detail/index.vue'),
      meta: { title: '保险详情', permission: 'glbx' }
    },
    {
      path: 'edit/:id',
      component: () => import('../views/insurance/edit/index.vue'),
      meta: { title: '保险记录', permission: 'glbx' }
    }
  ]
}
