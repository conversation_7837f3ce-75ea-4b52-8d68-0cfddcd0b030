import routerContainer from '@layout/router-container'

export default {
  path: 'culture',
  component: routerContainer,
  children: [
    {
      path: 'list',
      component: () => import('../views/culture/list/index.vue'),
      meta: { title: '带培管理', keepAlive: true }
    },
    {
      path: 'my',
      component: () => import('../views/culture/list/index.vue'),
      meta: { title: '我的带培', keepAlive: true }
    },
    {
      path: 'file-template',
      component: () => import('../views/culture/file-template/index.vue'),
      meta: { title: '附件模板管理', keepAlive: true }
    },
    {
      path: 'template',
      component: () => import('../views/culture/template/index.vue'),
      meta: { title: '带培模板管理', keepAlive: true }
    },
    {
      path: 'template/:id',
      component: () => import('../views/culture/template-add/index.vue'),
      meta: { title: '模板信息' }
    },
    {
      path: ':id',
      component: () => import('../views/culture/detail/index.vue'),
      meta: { title: '带培详情' }
    },
  ]
}
