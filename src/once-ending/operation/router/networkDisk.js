import layout from '../views/network-disk/layout/index.vue'
import routerContainer from '../views/network-disk/components/routerContainer'

export default {
  path: '/operation',
  component: layout,
  meta: { title: 'OA网盘' },
  children: [
    {
      path: 'network-disk',
      name: 'OA网盘',
      meta: { title: 'OA网盘', keepAlive: true },
      component: routerContainer,
      children: [
        {
          path: '/',
          meta: { title: 'OA网盘' },
          redirect: 'recent/0',
        },
        {
          path: 'recent/:id',
          name: 'recent',
          meta: { title: '最近' },
          component: () => import('../views/network-disk/recent/index.vue')
        },
        {
          path: 'my/:id',
          name: 'my',
          meta: { title: '我的' },
          component: () => import('../views/network-disk/my/index.vue')
        },
        {
          path: 'commonFolder/:id',
          name: 'commonFolder',
          meta: { title: '公共' },
          component: () => import('../views/network-disk/commonFolder/index.vue'),
        },
        {
          path: 'recycle/:id',
          name: 'recycle',
          meta: { title: '回收站' },
          component: () => import('../views/network-disk/recycle/index.vue')
        },
        {
          path: 'share/:id',
          name: 'share',
          meta: { title: '分享' },
          component: () => import('../views/network-disk/share/index.vue')
        },
      ]
    },
  ]
}
