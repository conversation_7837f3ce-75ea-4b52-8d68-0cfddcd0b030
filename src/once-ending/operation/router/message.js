import layout from '@common/layout/layout'
import routerContainer from '@common/layout/router-container'

function load (component) {
  return () => import(`../views/message/${component}.vue`)
}

export default {
  path: 'message',
  component: routerContainer,
  children: [
    {
      path: 'sale-detail',
      name: 'sale-detail',
      component: () => import('../views/message/sale-detail.vue'),
      meta: { title: '销售业绩', keepAlive: true }
    },
    {
      path: 'initiation',
      name: 'initiation',
      component: () => import('../views/message/initiation.vue'),
      meta: { title: '入会信息查询' }
    },
    {
      path: 'config',
      name: 'config',
      component: () => import('../views/message/config/index.vue'),
      meta: { title: '短信配置' }
    }
  ]
}
