import layout from '@common/layout/layout'
import routerContainer from '@common/layout/router-container'

function load (component) {
  return () => import(`../views/franchise-stores/${component}.vue`)
}

export default {
  path: '',
  component: routerContainer,
  children: [
    {
      path: 'franchise-stores',
      name: 'franchise-stores',
      component: router<PERSON>ontainer,
      children: [
        {
          path: 'goods-payment',
          name: 'franchise_goods-payment',
          component: () => import('../views/franchise-stores/goods-payment/index.vue'),
          meta: { title: '货品额度款' }
        },
        {
          path: 'rebate',
          name: 'franchise_rebate',
          component: () => import('../views/franchise-stores/rebate/index.vue'),
          meta: { title: '加盟店经营返利' }
        }
      ]
    }
  ]
}
