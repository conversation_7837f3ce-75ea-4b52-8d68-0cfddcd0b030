import layout from '@common/layout/layout'
import routerContainer from '@common/layout/router-container'

function load (component) {
  return () => import(`../views/report/${component}.vue`)
}

export default {
  path: 'report',
  component: routerContainer,
  children: [
    {
      path: 'list',
      name: 'list',
      component: () => import('../views/report/list/index.vue'),
      meta: { title: '报备管理', keepAlive: true }
    },
    {
      path: 'config',
      name: 'config',
      component: () => import('../views/report/config/index.vue'),
      meta: { title: '报备类型配置', keepAlive: true }
    }
  ]
}
