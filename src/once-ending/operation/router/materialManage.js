import layout from '@common/layout/layout'
import routerContainer from '@common/layout/router-container'

function load (component) {
  return () => import(`../views/material/${component}.vue`)
}

export default {
  path: 'material',
  redirect: 'material/manage',
  component: routerContainer,
  meta: { title: '物料管理' },
  children: [
    {
      path: 'manage',
      name: 'OPR_MaterialManage',
      component: () => import('../views/material/list/index.vue'),
      meta: { title: '物料陈列管理', keepAlive: true }
    },
    {
      path: 'picture',
      name: 'OPR_MaterialPicture',
      component: () => import('../views/material/picture/index.vue'),
      meta: { title: '物料画面管理', keepAlive: true }
    },
    {
      path: 'categroy',
      name: 'OPR_MaterialCategroy',
      component: () => import('../views/material/categroy/index.vue'),
      meta: { title: '物料类别管理', keepAlive: true }
    },
    {
      path: 'statistics',
      name: 'OPR_MaterialStatistics',
      component: () => import('../views/material/statistics/index.vue'),
      meta: { title: '物料陈列统计', keepAlive: true }
    },
  ]
}
