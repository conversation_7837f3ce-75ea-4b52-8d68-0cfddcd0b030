import layout from '@layout/layout'

function load (component) {
  return () => import(`../views/complaint/${component}.vue`)
}

export default {
  path: '/complaint',
  name: 'complaint',
  component: layout,
  meta: { title: '投诉管理' },
  children: [
    {
      path: 'list',
      name: 'complaintList',
      component: () => import('../views/complaint/list/index.vue'),
      meta: { title: '投诉管理', keepAlive: true }
    },
    {
      path: 'add',
      component: () => import('../views/complaint/add/index.vue'),
      meta: { title: '添加投诉' }
    },
    {
      path: 'detail/:id',
      component: () => import('../views/complaint/detail/index.vue'),
      meta: { title: '投诉详情', statistics: true, statisticsUrl: (to) => `/staticpc/#/complaint/detail/${to.params.id}` }
    },
    {
      path: 'statistics',
      component: () => import(`../views/complaint/statistics/index.vue`),
      meta: { title: '投诉统计' }
    },
    {
      path: 'chart',
      component: () => import(`../views/complaint/chart/index.vue`),
      meta: { title: '投诉图表统计', disableWatermark: true }
    },
  ]
}
