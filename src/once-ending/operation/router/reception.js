import layout from '@common/layout/layout'
import routerContainer from '@common/layout/router-container'

function load (component) {
  return () => import(`../views/reception/${component}.vue`)
}

export default {
  path: 'reception',
  component: routerContainer,
  children: [
    {
      path: 'index',
      component: () => import('../views/reception/index.vue'),
      meta: { title: '目标客户资源池', keepAlive: true }
    }
  ]
}
