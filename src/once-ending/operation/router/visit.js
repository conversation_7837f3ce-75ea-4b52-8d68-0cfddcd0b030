import layout from '@layout/layout'
import routerContainer from '@common/layout/router-container'

function load (component) {
  return () => import(`../views/visit/${component}.vue`)
}

export default {
  path: 'visit',
  name: 'visit',
  redirect: 'visit/config',
  component: routerContainer,
  meta: { title: '巡店' },
  children: [
    {
      path: 'config',
      name: 'visit-config',
      component: () => import('../views/visit/config/index.vue'),
      meta: { title: '巡店项目管理', keepAlive: true }
    },
    {
      path: 'check',
      name: 'visit-check',
      component: routerContainer,
      meta: { title: '巡店情况检查' },
      children: [
        {
          path: 'self/:type',
          name: 'self',
          component: () => import('../views/visit/check/self/index.vue'),
          meta: { title: window.tenant.xtenant < 1000 ? '检查记录' : '自检情况检查' }
        },
        {
          path: 'visitShop',
          name: 'visit-shop',
          component: () => import('../views/visit/check/visit-shop/index.vue'),
          meta: { title: '巡店情况检查' }
        },
        {
          path: 'statistics',
          name: 'check-statistics',
          component: () => import('../views/visit/check/statistics/index.vue'),
          meta: { title: '盘点情况统计' }
        },
      ]
    },
    {
      path: 'statistics',
      component: () => import('../views/visit/statistics/index.vue'),
      meta: { title: '巡店数据统计', keepAlive: true }
    },
  ]
}
