import layout from '@common/layout/layout'
import routerContainer from '@common/layout/router-container'

function load (component) {
  return () => import(`../views/area-info/${component}.vue`)
}

export default {
  path: '',
  component: routerContainer,
  children: [
    {
      path: 'area-info',
      name: 'area-info',
      component: router<PERSON>ontainer,
      children: [
        {
          path: 'manage',
          name: 'manage',
          component: () => import('../views/area-info/manage/index.vue'),
          meta: { title: '店面管理', keepAlive: true, requirePWD: true }
        }
      ]
    }
  ]
}
