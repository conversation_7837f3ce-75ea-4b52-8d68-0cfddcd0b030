import layout from '@common/layout/layout'
import routerContainer from '@common/layout/router-container'
function load (component) {
  return () => import(`../views/staff/${component}.vue`)
}

export default {
  path: 'staff',
  component: routerContainer,
  children: [
    {
      path: 'business-index',
      name: 'businessIndex',
      component: () => import('../views/staff/business/index.vue'),
      // component: load('business/index'),
      meta: { title: '员工经手业务查询', keepAlive: true }
    },
    {
      path: 'dimission',
      name: 'dimission',
      component: () => import('../views/staff/dimission/index.vue'),
      // component: load('dimission/index'),
      meta: { title: '员工离职业务推送人配置', keepAlive: true }
    },
    {
      path: 'business-detail/:id',
      name: 'businessDetail',
      component: () => import('../views/staff/business/detail.vue'),
      // component: load('business/detail'),
      meta: { title: '员工经手业务详情' }
    },
  ]
}

export const detailToImage = {
  path: '/operation/staff/business-detail-image/:id',
  name: 'businessDetailImage',
  component: () => import('../views/staff/business/to-image.vue'),
  meta: { title: '下载员工经手业务详情', disableWatermark: true }
}
