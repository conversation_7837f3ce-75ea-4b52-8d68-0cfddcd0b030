import layout from '@common/layout/layout'
import routerContainer from '@common/layout/router-container'

function load (component) {
  return () => import(`../views/feedback/${component}.vue`)
}

export default {
  path: 'feedback',
  component: routerContainer,
  meta: { title: '投诉建议' },
  children: [
    // 输出
    {
      path: 'list',
      component: () => import('../views/feedback/out/list/index.vue'),
      meta: { title: '投诉建议' }
    },
    // 九机
    {
      path: 'jiujiList',
      component: () => import('../views/feedback/jiuji/list/index.vue'),
      meta: { title: '合作伙伴投诉建议' }
    },
    // 输出
    {
      path: 'detail/:id',
      component: () => import('../views/feedback/out/detail/index.vue'),
      meta: { title: '投诉建议详情' }
    },
    // 九机
    {
      path: 'jiujiDetail/:id',
      component: () => import('../views/feedback/jiuji/detail/index.vue'),
      meta: { title: '投诉建议详情', statistics: true, statisticsUrl: (to) => `/staticpc/#/complaint/detail/${to.params.id}` }
    },
  ]
}
