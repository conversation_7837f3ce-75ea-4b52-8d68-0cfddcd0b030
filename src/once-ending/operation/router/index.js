import layout from '@common/layout/layout'
import routerContainer from '@layout/router-container'
import materialManage from './materialManage'
import visit from './visit'
import workOrder from './work-order'
import evaluate from './evaluate'
import report from './report'
import excitation from './excitation'
import areaInfo from './area-info'
import statisticsReport from './statistics-report'
import security from './security'
import largeDataScreen from './large-data-screen'
import complaint from './complaint'
import store from './store'
import feedback from './feedback'
import achievement from './achievement'
import exhibition from './exhibition'
import reception from './reception'
import message from './message'
import rankConfig from './rank-config'
import franchiseStores from './franchise-stores'
import salesData from './salesData'
import salesProportion from './sales-proportion'
import member from './member'
import staff, { detailToImage } from './staff'
import dataBoard from './data-board'
import metabase from './metabase'
import decorate from './decorate'
import promotionPath from './promotion-path'
import insurance from './insurance'
import culture from './culture'
import businessUnit from './business-unit'
import dashboard from './dashboard'
import networkDisk from './networkDisk'

// 旧模块路由以固定,如果按照模块前都加上operation,可能需要其他项目,C#也要修改代码,故旧模块不加operation,新模块统一加operation
const routes = [
  complaint,
  rankConfig,
  ...workOrder,
  achievement,
  exhibition,
  ...evaluate,
  detailToImage,
  networkDisk,
  {
    path: '/operation',
    component: layout,
    meta: { title: '运营' },
    children: [
      materialManage,
      visit,
      report,
      areaInfo,
      excitation,
      statisticsReport,
      security,
      largeDataScreen,
      feedback,
      store,
      reception,
      message,
      franchiseStores,
      salesData,
      salesProportion,
      member,
      staff,
      dataBoard,
      metabase,
      decorate,
      promotionPath,
      insurance,
      culture,
      businessUnit,
      dashboard
    ]
  }]

export default routes
