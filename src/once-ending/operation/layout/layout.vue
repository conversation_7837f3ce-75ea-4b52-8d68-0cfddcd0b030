<template>
  <a-spin size="large" :delay="200" :spinning="loading">
    <a-config-provider :locale="zhCN">
      <a-layout
        id="components-layout-custom-trigger"
        class="flex flex-col"
        v-if="userInfo"
      >
        <a-layout-content class="flex-child-grow">
          <keep-alive>
            <router-view
              v-if="$route.meta.keepAlive && hasPermission"
              :key="$route.fullPath"
            >
            </router-view>
          </keep-alive>
          <router-view
            v-if="!$route.meta.keepAlive && hasPermission"
            :key="$route.fullPath"
          >
          </router-view>
          <page403 v-if="!hasPermission"></page403>
        </a-layout-content>
        <QuickQuery />
      </a-layout>
    </a-config-provider>
  </a-spin>
</template>
<script>
  import Vue from 'vue'
  import {
    Spin,
    ConfigProvider,
    Layout,
    Menu,
    Dropdown,
    Badge,
    Avatar,
  } from 'ant-design-vue'
  import zhCN from 'ant-design-vue/es/locale/zh_CN'
  import 'moment/dist/locale/zh-cn'
  import { mapState } from 'vuex'

  import QuickQuery from '~/components/quick-query/QuickQueryVue'
  import page403 from '~/components/abnormal-page/403'

  Vue.use(Spin)
    .use(ConfigProvider)
    .use(Layout)
    .use(Menu)
    .use(Dropdown)
    .use(Badge)
    .use(Avatar)

  export default {
    components: { QuickQuery, page403 },
    data () {
      return {
        collapsed: false,
        openKey: [],
        selectedKey: [],
        zhCN: zhCN,
      }
    },
    created () {
      this.openKey = this.$route.matched.map((r) => r.path)
      this.selectedKey = this.openKey
    },
    computed: {
      ...mapState({
        userInfo: (state) => state.userInfo && state.userInfo,
        loading: (state) => state.loading,
      }),
      hasPermission (state) {
        const hasMatePermission = this.$route.meta && this.$route.meta.permission
        // 搞一个开发测试小后门
        if (sessionStorage.getItem('isTest')) {
          return true
        }
        // 只判断当前路由，是否mate中有permission，如果没有，则认为不需要权限，则显示路由页面，由后续处理
        if (!hasMatePermission) {
          return true
        }
        const permission = state.userInfo ? state.userInfo.Rank || [] : []
        return !this.$route.matched.some(
          (r) => r.meta.permission && !permission.includes(r.meta.permission)
        )
      },
    },
    methods: {
      handleMessageClick (e) {
        console.log(e)
      },
      logout () {
        window.sessionStorage.removeItem('pcOaToken')
        this.$store.commit('setToken', '')
        this.$router.push('/login')
      },
    },
  }
</script>
<style lang="less" type="text/less">
.ant-spin-nested-loading > div > .ant-spin {
  z-index: 99999;
  max-height: 100vh;
  background: rgba(0, 0, 0, 0.1);
} /*让他大于modal的层级, 增加背景色, 让加载状态的modal视觉效果更像不可操作*/
#components-layout-custom-trigger {
  min-height: 100vh;
  .header {
    background: #1890ff;
    height: 96px;
    line-height: 1.5;
    color: #fff;
    .logo {
      height: 64px;
      padding-left: 24px;
      img {
        width: 32px;
        height: 32px;
      }
      h1 {
        font-size: 20px;
        margin: 0 0 0 12px;
        font-family: Myriad Pro, Helvetica Neue, Arial, Helvetica, sans-serif;
        color: #fff;
        white-space: nowrap;
      }
    }
    .menu-btn {
      background: #001529;
      padding: 0 15px;
      margin-left: 20px;
      color: #fff;
      height: 32px;
    }
    .user-info {
      .user-item {
        height: 96px;
        padding: 0 12px;
        cursor: pointer;
        &:hover {
          background: #001529;
        }
      }
    }
  }
}
</style>
