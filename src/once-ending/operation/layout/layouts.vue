<script type='text/jsx' lang="jsx">

  export default {
    name: 'page-layout',

    render () {
      return (
        <div class="page-admin">
          <div class="page-header">
            <div class="title">
              <h1 class="title-text">{this.$route.meta.title}</h1>
              <div> {this.$slots.pageHeaderActions} </div>
            </div>
            <div class="content">
              <div> {this.$slots.pageHeaderContent} </div>
            </div>
          </div>
          <div class="page-body">
            {this.$slots.default}
          </div>
        </div>
      )
    },
  }
</script>

<style lang='scss' scoped>
  $page-gap: 20px;
  .page{
    &-admin{
      margin:-24px;
    }
    &-header{
      position: fix;
      padding: 0 24px;
      background: #fff;

      .title{
        display: flex;
        align-items: center;
        height: 50px;
        justify-content: space-between;

        .title-text{
          font-weight: 600;
          font-size: 20px;
        }
      }
      // .content{
      //   padding: $page-gap;
      // }
    }
    &-body{
      background: #fff;
      margin: $page-gap;
      padding: $page-gap;
    }
  }
</style>
