<!--渠道搜素-->
<template>
  <a-select
    show-search
    placeholder="搜索渠道"
   :filter-option="false"
    style="width: 100%"
    @change="onChangeChannel"
    @search="searchData"
    :allowClear="allowClear"
    :getPopupContainer="getPopupContainer"
    :mode="mode"
    :options="searchResult"
    :label-in-value="true"
    :maxTagCount="maxTagCount"
    v-model="channelData"
    :disabled="disabled"
  />
</template>

<script>
  export default {
    props: {
      value: {
        type: Number | Array,
        default: undefined,
      },
      allowClear: {
        type: Boolean,
        default: false,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      maxTagCount: {
        type: Number,
        default: 2,
      },
      mode: {
        validator: function (value) {
          return ['default', 'multiple', 'tags', 'combobox'].indexOf(value) !== -1
        },
        default: 'default'
      },
      getPopupContainer: {
        type: Function,
        default: () => document.body
      },
      kinds: {
        type: Number,
        default: undefined
      }
    },
    watch: {
      value (e) {
        this.channelData = e
      }
    },
    data () {
      return {
        channelData: this.value,
        searchResult: [],
        insourceId: null
      }
    },
    methods: {
      // 搜索渠道名称
      onSearchChannel (insourceId, searchKey) {
        let data = { q: searchKey, limit: 100 }
        this.kinds && (data.kinds = this.kinds)
        if (insourceId) {
          this.insourceId = insourceId
          data.insourceid = insourceId
        }
        this.$api.channel
          .newGetInsourceChannelList(data)
          .then((res) => {
            if (res.code === 0) {
              this.searchResult = res.data?.map(d => ({
                label: d.companyJc + `（${d.id}）`,
                value: d.id
              })) || []
            } else {
              this.$message.error(res.msg || '查询渠道数据出错')
            }
          })
      },
      onChangeChannel (val) {
        this.$emit('input', val)
        console.log(val)
      },
      searchData (data) {
        this.onSearchChannel(this.insourceId, data)
      }
    },
  }
</script>

<style>
</style>
