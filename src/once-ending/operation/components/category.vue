<script lang="jsx">
  import { defineComponent, ref, watch, getCurrentInstance } from 'vue'
  import { TreeSelect, message } from 'ant-design-vue'
  import { to } from '@common/utils/common'
  import store from '@operation/api/store'

  const SHOW_ALL = TreeSelect.SHOW_ALL
  const SHOW_PARENT = TreeSelect.SHOW_PARENT
  const SHOW_CHILD = TreeSelect.SHOW_CHILD
  const types = {
    SHOW_ALL,
    SHOW_PARENT,
    SHOW_CHILD
  }

  export default defineComponent({
    props: {
      value: {
        type: [Array, String, Number],
        default: () => []
      },
      placeholder: {
        type: String,
        default: '请选择商品分类'
      },
      multiple: {
        type: Boolean,
        default: true
      },
      allowClear: {
        type: Boolean,
        default: true
      },
      showArrow: {
        type: Boolean,
        default: true
      },
      showType: {
        type: String,
        default: 'SHOW_ALL'
      },
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const localValue = ref(undefined)
      watch(
        () => props.value,
        (val) => {
          localValue.value = val
        },
        {
          immediate: true
        })

      const cateData = ref([])
      const queryData = async function () {
        const [err, res] = await to(store.getCategory())
        if (err) throw err
        const { code, data, userMsg } = res
        if (code === 0) {
          cateData.value = data
        } else {
          message.error(userMsg)
        }
      }
      queryData()

      const change = (val) => {
        proxy.$emit('input', val)
        proxy.$emit('change', val)
      }
      return {
        localValue,
        cateData,
        change
      }
    },
    render () {
      const { cateData, placeholder, multiple, change, allowClear, showType, showArrow } = this
      return <TreeSelect
                { ...{ props: { ...this.$attrs } } }
                v-model={this.localValue}
                treeData={cateData}
                multiple={multiple}
                showArrow={showArrow}
                showCheckedStrategy={types[showType]}
                allowClear={allowClear}
                tree-checkable={multiple}
                tree-node-filter-prop="title"
                placeholder={placeholder}
                onChange={change}
                replaceFields={{ children: 'childTree', title: 'name', key: 'id', value: 'id' }}
                dropdownStyle={{ maxHeight: '300px', overflow: 'auto' }}
                />
    }
  })
</script>
