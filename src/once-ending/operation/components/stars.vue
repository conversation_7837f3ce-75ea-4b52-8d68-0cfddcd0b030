<script lang="jsx">
  import { defineComponent } from 'vue'
  import { NiImg } from '@jiuji/nine-ui'

  export default defineComponent({
    components: {
      NiImg
    },
    props: {
      starNumber: {
        type: Number
      },
      inTable: {
        type: Boolean,
        default: false
      },
      labelStyle: {
        type: Object,
        default: () => {
          return {}
        }
      }
    },
    render () {
      const { starNumber, showLabel, inTable, labelStyle } = this
      return starNumber && window.tenant.xtenant < 1000 ? <span class="margin-left">
        <NiImg src="https://img2.ch999img.com/newstatic/27193/091428eed6310b59.png" class="stars"/>
        <span>*{starNumber}</span>
      </span> : null
    }
  })
</script>
<style scoped lang="scss">
.stars {
  width: 20px;
  height: 20px;
  margin-right: 4px;
}
</style>
