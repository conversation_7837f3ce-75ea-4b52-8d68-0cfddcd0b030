<script lang="jsx" lang="jsx">
  export default ({ props, data }) => {
    const {
      routerName, // String
      text, // String
      disabled,
      params, // Object
      type = 'link' // String
    } = props
    return (
      <router-link
        class={ [data.class, type] }
        disabled={ disabled || false }
        target="_blank"
        to={{ name: routerName, query: params }}>
        { text }
      </router-link>
    )
  }
</script>
query:{year:'screening.year'}
<style lang="scss" scoped>
  $btn-padding: 15px;
  $btn-radius: 4px;
  $btn-height: 32px;
  $btn-color: #1890ff;
  $btn-text-color: #fff;

  .button{
    position: relative;
    display: inline-block;
    white-space: nowrap;
    text-align: center;
    color: $btn-text-color;
    background: $btn-color;
    cursor: pointer;
    border-radius: $btn-radius;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    height: $btn-height;
    line-height: $btn-height;
    padding: 0 $btn-padding;
  }

  a.primary{
    @extend .button;

    &:hover{
      background: darken($btn-color, 10%);
    }
  }
  a.link{
    color: $btn-color;
    &:hover{
      color: darken($btn-color, 10%);
    }
  }
  a[disabled] {
    color: rgba(0, 0, 0, 0.25);
  }
</style>
