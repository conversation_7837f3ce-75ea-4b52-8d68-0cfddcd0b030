<script type='text/jsx' lang="jsx">
  import { NiImg } from '@jiuji/nine-ui'
  export default {
    name: 'picture-HoverPreview',
    props: {
      alt: {
        type: String,
        default: ''
      },
      src: {
        type: String,
        default: ''
      },
      preWidth: {
        type: Number,
        default: 550
      },
      preHeight: {
        type: Number,
        default: 489
      },
      width: {
        type: Number,
        default: 99
      },
      height: {
        type: Number,
        default: 88
      },
    },
    components: {
      NiImg
    },
    render () {
      return (
        <a-popover placement="top">
          <template slot="content">
            <div class='img-previwe'>
              <a href={`${this.src}.webp`} target="_bank" rel="noopener noreferrer">
                <NiImg alt={this.alt} src={ this.src } width={ this.width * 5.5 } height={ this.height * 5.5 }/>
              </a>
            </div>
          </template>
          <div class='img-wrap'
            style={{
                '--w': this.width + 'px',
                '--h': this.height + 'px',
              }}
          >
            <NiImg
              alt={this.alt}
              src={ this.src }
              width={ this.width }
              height={ this.hight }
            />
          </div>
        </a-popover>
      )
    }
  }
</script>

<style lang="scss" scoped>
  $img-width: var(--w);
  $img-height: var(--h);
  .img-previwe{
    max-width: 480px;
    img{
      width: 100%;
      max-height: 60vh;
    }
  }
  .img-wrap{
    position: relative;
    width: $img-width;
    height: $img-height;
    padding-bottom: 40px;
    overflow: hidden;
    cursor: pointer;
    img{
      position: absolute;
      width: 100%;
      object-fit: cover;
    }
  }
</style>
