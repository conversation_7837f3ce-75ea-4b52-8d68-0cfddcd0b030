<template>
  <a-tree-select
    v-model="selAras"
    search-placeholder="请选择门店"
    :replaceFields="{
      children:'children',
      key: 'uniqueKey',
      value: 'uniqueKey',
      title: 'label'
    }"
    :defaultValue="defaultValue"
    :maxTagCount="0"
    :tree-data="list"
    treeNodeFilterProp="label"
    tree-checkable
    allowClear
    :showSearch="true"
    :show-checked-strategy="SHOW_PARENT"
    :dropdownStyle="_dropdownStyle"
    @change="onChange"
  />
</template>

<script>
  import API from '~/api/recovery'
  import { mapState } from 'vuex'
  import { TreeSelect } from 'ant-design-vue'
  const SHOW_PARENT = TreeSelect.SHOW_CHILD

  const defaultValue = []
  function getTreeData (treeData) {
    const listData = []

    treeData.forEach(item => {
      defaultValue.push(item.areaInfoBasic.uniqueKey)
      let obj = {}
      if (item.children && item.children.length) {
        obj = item.areaInfoBasic
        obj.label = item.areaInfoBasic.area || item.areaInfoBasic.departName
        obj.children = getTreeData(item.children)
      } else {
        obj = item.areaInfoBasic
        obj.label = item.areaInfoBasic.area || item.areaInfoBasic.departName
        obj.children = []
      }
      listData.push(obj)
    })
    return listData
  }

  export default {
    name: 'AreaDepartSelect',
    props: {
      getUserArea: {
        type: Function,
        default: treeData => treeData
      },
    },
    data () {
      return {
        treeData: [],
        treeDataFlatten: [],
        valueLocal: [],
        treeCheckable: false,
        type: 'areaRole',

        selAras: [],
        defaultValue: [],
        list: [],
        SHOW_PARENT,
      }
    },
    computed: {
      _dropdownStyle () {
        return {
          maxHeight: '300px',
          ...this.dropdownStyle
        }
      },
      ...mapState({
        userAreaId: (state) => state.userInfo.areaid,
        userId: (state) => state.userInfo.UserID || {},
      }),
    },
    async mounted () {
      await this.getAreasByUserId()
      this.defaultValue = [this.userAreaId + '']
      this.selAras = [this.userAreaId + '']
    },
    methods: {
      getAreasByUserId () {
        const { userId } = this
        API.getAreasByUserId({ userId }).then(res => {
          if (res.code === 0) {
            this.list = getTreeData(res.data.children)
            let areaIds = []
            if (this.selAras.length) {
              areaIds = this.selAras.map(item => {
                if (item.split('-')[0]) {
                  return item.split('-')[0]
                }
              })
            } else {
              areaIds = defaultValue.map(item => {
                if (item.split('-')[0]) {
                  return item.split('-')[0]
                }
              })
            }
            this.getUserArea && this.getUserArea(areaIds)
          }
        })
      },
      onChange (value, label, extra) {
        if (!Array.isArray(value)) value = [value] // value: string[], 不管是不是
        value = value.filter(item => item)// multiple模式
        this.$emit('change', value, label, extra)
      }
    }
  }
</script>

<style scoped>
</style>
