<template>
  <a-tree-select
    :treeData="treeData"
    v-model="valueLocal"
    showSearch
    allowClear
    :maxTagCount="maxTagCount"
    :showCheckedStrategy="showType"
    searchPlaceholder="请输入"
    style="width: 100%"
    v-bind="$attrs"
    :dropdownStyle="_dropdownStyle"
    :treeCheckable="treeCheckable || $attrs.treeCheckable"
    @change="onChange"
  />
</template>

<script>
  import { TreeSelect } from 'ant-design-vue'
  import { treeShake, treeToArray, treeWalk } from '~/util/treeUtils'

  const SHOW_ALL = TreeSelect.SHOW_ALL
  const SHOW_PARENT = TreeSelect.SHOW_PARENT
  const SHOW_CHILD = TreeSelect.SHOW_CHILD

  export default {
    name: 'AreaDepartSelector',
    model: {
      prop: 'value', // string[], 不管是不是multiple模式
      event: 'change'
    },
    props: {
      value: null,
      // area-区域,department-部门,role=部门角色, departmentAuth=登录为分公司时只能查看该分公司以下的部门
      type: {
        validator: val => ['area', 'department', 'role', 'departmentAuth'].includes(val),
        default: 'area'
      },
      // 只显示用户有权限的地区
      userAreaOnly: {
        type: Boolean,
        default: false
      },
      maxTagCount: {
        type: Number,
        default: 1
      },
      /**
       * 传入对树数据操作的函数
       * @param {Array<{ id: string, label: string, children?: Array<any>}>} treeData
       * @returns treeData
       */
      filterNode: {
        type: Function,
        default: treeData => treeData
      },
      // 只能选择末节点
      leafOnly: {
        type: Boolean,
        default: false
      },
      // 回填方式
      showType: {
        type: String,
        default: 'SHOW_PARENT'
      },
      dropdownStyle: null,
      // 显示节点最终等级,根据dataType过滤 大区3 小区4 门店0
      showLevel: {
        type: Number,
        default: 0
      }
    },
    computed: {
      _dropdownStyle () {
        return {
          maxHeight: '300px',
          ...this.dropdownStyle
        }
      }
    },
    watch: {
      value (value) {
        if (!value) {
          this.valueLocal = undefined
        } else {
          let tmpValue = this.value
          if (!Array.isArray(tmpValue)) tmpValue = [tmpValue]
          this.valueLocal = tmpValue.map(it => typeof it === 'object' ? String(it.id) : String(it))
        }
      },
      showLevel () {
        this.setTreeData()
      }
    },
    data () {
      return {
        SHOW_PARENT,
        SHOW_CHILD,
        SHOW_ALL,
        treeData: [],
        copyTreeData: [],
        treeDataFlatten: [],
        valueLocal: [],
        treeCheckable: false
      }
    },
    async created () {
      // 解决初始值不响应页面的问题
      if (!this.value) {
        this.valueLocal = undefined
      } else {
        let tmpValue = this.value
        if (!Array.isArray(tmpValue)) tmpValue = [tmpValue]
        this.valueLocal = tmpValue.map(it => typeof it === 'object' ? String(it.id) : String(it))
      }
      await this.loadTreeData()
    },
    methods: {
      async loadTreeData () {
        let res = null
        let UserID = this.$store.state.userInfo.UserID
        const type = this.userAreaOnly ? 'areaRole' : this.type
        res = await this.$api.common.getAreaTree(type, UserID)
        if (res.code !== 0) return
        this.copyTreeData = JSON.parse(JSON.stringify(res.data))
        this.setTreeData()
        // 针对某些界面如果门店为空的时候要传全部的门店id，需要通知当前数据已请求完毕，可以拿数据来取
        this.$emit('isLoad', true)
      },
      setTreeData () {
        let treeData
        const copyTreeData = JSON.parse(JSON.stringify(this.copyTreeData))
        if (this.type === 'area') {
          treeData = this.spliceTreeData(this.showLevelFun(copyTreeData.areaOptions))
        }
        if (this.type === 'department') {
          treeData = res.data.departOptions
        }
        let treeDataFlatten = treeToArray(treeData)
        treeDataFlatten.forEach(it => {
          it.key = it.id
          it.value = it.id
          it.title = it.label
        })
        treeDataFlatten.sort((a, b) => a.key > b.key ? 1 : -1)

        if (this.filterNode) {
          treeData = this.filterNode(treeData)
        }

        if (this.userAreaOnly) {
          treeData = this.shakeTree(treeData)
        }

        if (this.leafOnly) {
          this.treeCheckable = true
          treeWalk(treeData, node => (node.disableCheckbox = !!node?.children?.length))
        }

        Object.assign(this, { treeData, treeDataFlatten })
      },
      // 删除当前用户没有权限的节点
      shakeTree (treeData) {
        let userAreas = this.$store.state.userInfo?.Areas || []
        let predicateFn = node => !!userAreas.find(area => node.title.includes(area))
        return treeShake(treeData, predicateFn)
      },
      // 根据最终显示节点过滤
      showLevelFun (treeData, parent) {
        return treeData.map((d, i) => {
          d.parent = parent
          if (d.dataType === this.showLevel) {
            d.children = []
            d.showLevel = true
            this.setParent(d.parent)
          } else {
            d.showLevel = false
            if (d.children && d.children.length) {
              d.children = this.showLevelFun(d.children, d)
            }
          }
          return d
        })
      },
      spliceTreeData (treeData) {
        return treeData.filter(d => d.showLevel)
      },
      setParent (parent) {
        if (parent) {
          parent.showLevel = true
          if (parent.parent) this.setParent(parent.parent)
        }
      },

      // A callback function, can be executed when you select a treeNode.
      onChange (value, label, extra) {
        if (!Array.isArray(value)) value = [value] // value: string[], 不管是不是
        value = value.filter(item => item)// multiple模式
        this.$emit('change', value, label, extra)
      }

    }
  }
</script>

<style scoped>
</style>
