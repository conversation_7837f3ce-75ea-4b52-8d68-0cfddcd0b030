<template>
  <div>
    <a-button
      v-if="actionType === 'export'"
      :type="btnType"
      @click="exportData">
      <a-icon type="export" />
      {{ btnText }}
    </a-button>

    <a-upload
      v-if="actionType === 'upload'"
      accept=".xls,.xlsx,.et"
      name="file"
      :multiple="false"
      :showUploadList="false"
      :beforeUpload="beforeUpload"
      @change="importFile"
    >
      <a-button :type="btnType">
        导入数据
      </a-button>
    </a-upload>

    <a-button
      v-if="actionType === 'getExcelTemplate'"
      :type="btnType" @click="getExcelTemplate">
      <a-icon type="download" /> 下载模板
    </a-button>
  </div>
</template>

<script>
  import axios from 'axios'
  import store from '~/store'
  import moment from 'moment'

  export default {
    name: 'excel-action',
    /**
     * actionType: ['export', 'upload', 'getExcelTemplate'] ==> [导出，导入数据，获取导入数据的模板]
     * scene : 场景（后端定义一个场景，给到前端）必传
     * exportTableName : 表格名称
     * params：查询参数
     * service：服务
     */
    props: {
      actionType: {
        type: String,
        default: 'export'
      },
      btnText: {
        type: String,
        default: '导出数据'
      },
      scene: {
        type: String,
        default: ''
      },
      params: {
        type: Object,
        default: () => {}
      },
      service: {
        type: String,
        default: 'ncSegments'
      },
      exportTableName: {
        type: String,
        default: '列表'
      },
      btnType: {
        type: String,
        default: 'default'
      },
      dataSource: { // 功能：未查询的时候显示的提示信息
        type: Array,
        default: () => ([])
      },
    },

    methods: {
      exportData () {
        if (!this.dataSource.length) {
          this.$message.error('请先查询数据')
          return
        }
        let url = this.$api.excelAction.exportExcel(this.service)
        const headers = { scene: this.scene }
        this.exportXlsx({
          url,
          params: this.params,
          headers,
          fileName: `${this.exportTableName}(${moment().format('YYYY-MM-DD HH：mm')}).xlsx`
        })
      },

      /**
       * 导出表格方法
       * @param { Object } param
       * @param { Object } headers 主要是传入后端 @方鑫 所定义的表格数据导出的公共接口的场景 { scene：'' }
       */
      exportXlsx ({ url = '', params = {}, method = 'post', headers = {}, fileName = '导出.xlsx' } = {}) {
        axios({
          method: method,
          url: url,
          data: params,
          responseType: 'blob',
          headers: {
            Authorization: store.state.token,
            ...headers
          }
        }).then((res) => {
          const link = document.createElement('a')
          let blob = new Blob([res.data], { type: 'application/x-excel' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = fileName
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        })
      },

      beforeUpload (file) {
        return false
      },
      async importFile ({ file }) {
        if (file && file.length === 0) return
        const regExcel = /.(xls|xlsx)$/i
        if (!regExcel.test(file.name)) {
          message.error('数据导入失败，请重新导入.xls或.xlsx后缀的表格文件')
          return
        }
        let data = new FormData()
        data.append('file', file)
        const postHeader = { scene: this.scene }
        try {
          let res = await this.$api.excelAction.importExcel(data, postHeader)
          if (res.code === 0) {
            this.$message.success(res.userMsg || res.msg || '导入成功')
            this.$emit('afterSuccess')
          } else {
            this.$message.error(res.userMsg || res.msg || '上传失败')
          }
        } catch (e) {
          this.$message.error(e.message || '请求失败')
        }
      },

      async getExcelTemplate () {
        const headers = { scene: this.scene }
        let { code, data, userMsg } = await this.$api.excelAction.getExcelTemplate(headers)
        if (code === 0) {
          console.log('下载模板', data)
        } else {
          this.$message.error(userMsg)
        }
      }
    }
  }
</script>
