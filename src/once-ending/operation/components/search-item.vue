<template>
  <div class="flex flex-align-center margins">
    <span v-if="title" class="flex-child-noshrink mr-5">
      {{title}}
      <span v-if="showColon">：</span>
    </span>
    <slot></slot>
  </div>
</template>

<script>
  export default {
    name: 'search-item',
    props: {
      title: {
        type: String,
        default: ''
      },
      showColon: {
        type: Boolean,
        default: true
      }
    }
  }
</script>

<style scoped>
.margins {
  margin: 8px 11px;
}
</style>
