<script lang="jsx">
  import { defineComponent, ref, watch, nextTick } from 'vue'
  import { Select, message } from 'ant-design-vue'
  import cloneDeep from 'lodash/cloneDeep'
  import store from '@operation/api/store'
  import { to } from '@common/utils/common'

  export default defineComponent({
    props: {
      value: {
        type: Array,
        default: () => [],
      },
      cateId: {
        type: [Array, String, Number],
        default: () => [],
      },
      multiple: {
        type: Boolean,
        default: true,
      },
    },
    setup (props) {
      const localValue = ref([])
      watch(
        () => props.value,
        (val) => {
          localValue.value = val
        },
        {
          immediate: true
        }
      )

      // 显示的options
      const selectOptions = ref([])

      // 缓存的options
      const cacheOptions = ref([])
      const selectSearchValue = ref('')
      // 全部options
      const allOptions = ref([])

      // 商品分类和品牌联动
      watch(
        () => props.cateId,
        (val) => {
          queryBrandData()
        }
      )

      const queryBrandData = async function () {
        const params = {
          cids: Array.isArray(props.cateId) ? props.cateId.join(',') : props.cateId,
        }
        const [err, res] = await to(store.getBrands(params))
        if (err) throw err
        const { code, data, userMsg } = res
        if (code === 0) {
          allOptions.value = data.map((item) => ({
            value: item.brandId,
            label: item.brandName,
          }))
          // 手动触发商品品牌获取焦点事件,解决默认值反显问题
          nextTick(() => {
            selectFocusOrBlur()
          })
        } else {
          message.error(userMsg)
        }
      }

      // 每次下拉框获取焦点或者失去焦点,初始化下拉框数据
      const selectFocusOrBlur = function () {
        const options = cloneDeep(allOptions.value)
        const initOptions = options.splice(0, 50)
        if (localValue.value.length) {
          localValue.value.forEach((item) => {
            const index = options.findIndex((d) => d.value === item)
            if (index !== -1) {
              initOptions.unshift(options.splice(index, 1)[0])
            }
          })
        }
        selectOptions.value = initOptions
        cacheOptions.value = options
      }

      // 每次用户输入,匹配所有数据,将数据筛选出来
      const selectSearch = function (val) {
        if (!val) {
          selectFocusOrBlur()
          return
        }
        selectSearchValue.value = val
        const options = cloneDeep(allOptions.value)
        selectOptions.value = options.filter((d) =>
          d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase())
        )
        cacheOptions.value = options.filter(
          (d) => !d.label.toLocaleLowerCase().includes(val.toLocaleLowerCase())
        )
      }

      const handleSelect = function () {
        selectFocusOrBlur()
        selectSearchValue.value = ''
      }

      // 每次下拉框滚动条滚到底部,加载缓存数据
      const selectPopupScroll = function (e) {
        if (!cacheOptions.value.length) {
          return
        }
        const { target } = e
        const scrollHeight = target.scrollHeight - target.scrollTop
        const clientHeight = target.clientHeight
        if (scrollHeight < clientHeight + 5) {
          const options = cacheOptions.value.splice(0, 50)
          selectOptions.value = selectOptions.value.concat(options)
        }
      }

      queryBrandData()

      return {
        localValue,
        selectOptions,
        selectFocusOrBlur,
        selectSearch,
        handleSelect,
        selectPopupScroll,
      }
    },
    render () {
      const {
        multiple,
        selectOptions,
        selectFocusOrBlur,
        handleSelect,
        selectSearch,
        selectPopupScroll,
      } = this
      return (
      <Select
        { ...{ props: { ...this.$attrs } } }
        placeholder="请选择品牌"
        get-popup-container={(triggerNode) => triggerNode.parentNode}
        show-arrow
        max-tag-count={1}
        mode={multiple ? 'multiple' : 'default'}
        v-model={this.localValue}
        option-filter-prop="children"
        onFocus={selectFocusOrBlur}
        onBlur={selectFocusOrBlur}
        onSelect={handleSelect}
        onSearch={selectSearch}
        onPopupScroll={selectPopupScroll}
        allow-clear
        options={selectOptions}
        onChange={(val) => {
          this.$emit('input', val)
        }}
      ></Select>
      )
    },
  })
</script>
