<!-- 用法
// https://code.9ji.com/gaoqiangqiang/default/src/branch/master/最新上传附件的组件.md
 -->
<template>
  <div>
    <div class="flex mt-16" v-if="showUpload">
      <div class="flex">
        <!-- 上传按钮 -->
        <div style="margin-right:1em;">
            <a-button type="primary" size="small" @click="doUpload" :disabled="disabled">
              <a-icon type="upload"></a-icon>
              {{buttonName[0]}}
            </a-button>
        </div>
        <!-- 二维码上传 -->
        <div  v-if="showQrBtn">
            <a-button type="primary" size="small" @click="showQr()" :disabled="disabled">
                <a-icon type="qrcode"></a-icon>
                {{buttonName[1]}}
            </a-button>
        </div>
      </div>
      <a-modal
        title="使用OA App扫码"
        v-model="qrcodeVisible"
        destroyOnClose
        :maskClosable="false"
        @ok="closeQr"
        @cancel="closeQr"
        :width="268">
          <QrCode :value="qrContent" style="width: 100%;"></QrCode>
      </a-modal>
    </div>
    <div v-if="list.length" class="img-wrap">
      <div v-for="(file, index) in list" :key="index" class="flex flex-align-center relative">
        <template v-if="isImg(file)">
          <lazy-img
            v-if="showThumbnail"
            class="img-style"
            width="100"
            height="100"
            :src="file.fileUrl || file.filePath"/>
          <div class="action-warp">
            <file-preview
              v-if="showPreview"
              :showName="edit"
              :file="file"
              :pathKey="pathKey"
              showFileName="false"
              :download="!download" />
            <a v-if="download" class="padding" href="#" @click="downloadFile(file)"><a-icon type="cloud-download" /></a>
            <a v-if="deleteItem" @click="deleteFile(file,index)"><a-icon type="delete"/></a>
            <a-button v-if="file.id && showRename" type="primary" size="small" @click="renameFile(file)">重命名</a-button>
          </div>
        </template>
        <template v-else>
          <div class="noImg-warp">
            <a-input v-if="editFileName" v-model="file.fileName" class="file-name" :disabled="!editName"/>
            <div v-else  >
              <a-tooltip class="file-name">
                <template slot="title">
                  {{ file.fileName }}
                </template>
                {{ file.fileName }}
              </a-tooltip>
            </div>
            <div class="action-warp">
              <file-preview
                v-if="showPreview"
                :showName="edit"
                :file="file"
                :pathKey="pathKey"
                showFileName="false"
                :download="!download" />
              <a v-if="download" class="padding" href="#" @click="downloadFile(file)"><a-icon type="cloud-download" /></a>
              <a v-if="deleteItem" class="padding" @click="deleteFile(file,index)"><a-icon type="delete"/></a>
              <a-button v-if="file.id && showRename" type="primary" size="small" @click="renameFile(file)">重命名</a-button>
            </div>
          </div>
        </template>
        <a-popover title="" trigger="hover" v-if="showInfo">
          <template slot="content">
            <span>附件信息</span>
            <span>{{file.createUserName}}</span>
            <span>{{file.createTime}}</span>
          </template>
          <a><a-icon class="padding" type="info-circle"/></a>
        </a-popover>
      </div>
    </div>
  </div>
</template>

<script>
  import { Modal, Upload } from 'ant-design-vue'
  import uuidv4 from 'uuid/v4'
  import api from '~/api'
  import { mapState } from 'vuex'
  import QrCode from '~/components/qrcode'
  import { saveAs } from 'file-saver'
  import nineUpload from '@jiuji/nine-upload'
  import filePreview from './file-preview'
  import LazyImg from '~/components/lazy-img'

  const Stomp = require('stompjs/lib/stomp.js').Stomp
  const INIT_MAX = 5
  export default {
    name: 'Uploader',
    props: {
      // 图片展示缩略图,其余照旧展示
      showThumbnail: {
        type: Boolean,
        default: false
      },
      // 是否显示重命名按钮
      showRename: {
        type: Boolean,
        default: true
      },
      // 是否禁止上传
      showUpload: {
        type: Boolean,
        default: true
      },
      disabled: {
        type: Boolean,
        default: false
      },
      showPreview: {
        type: Boolean,
        default: true
      },
      // 按钮名字
      buttonName: {
        type: Array,
        default: () => ['添加附件', '手机上传']
      },
      // 是否多选
      multiple: {
        type: Boolean,
        default: true
      },
      // 支持上传多个文件
      moreAmount: {
        type: Boolean,
        default: true
      },
      editFileName: {
        type: Boolean,
        default: true
      },
      addSize: {
        type: Boolean,
        default: false
      },
      // 支持上传多个文件
      moreAmountWarn: {
        type: String,
        default: '最多上传一个文件'
      },
      editName: {
        type: Boolean,
        default: true
      },
      // 文件是或否支持下载
      download: {
        type: Boolean,
        default: false
      },
      // 文件是否支持删除
      deleteItem: {
        type: Boolean,
        default: true
      },
      // 是否显示上传人，上传时间信息
      showInfo: {
        type: Boolean,
        default: false
      },
      // 是否展示 手机上传的按钮
      showQrBtn: {
        type: Boolean,
        default: true
      },
      edit: Boolean,
      // path的key名
      pathKey: {
        type: String,
        default: 'fileUrl'
      },
      // 上传的列表
      fileList: {
        type: [Array],
        default: () => [],
        validator: (fileList) => {
          let isRight = true
          fileList?.map(item => {
            if (!item.hasOwnProperty('fid') || !item.hasOwnProperty('fileName')) isRight = false
          })
          if (!isRight) console.error('fileList: 参数不正确')
          return isRight
        }
      },
      // 删除的id列表
      delList: {
        type: [Array],
        default: () => []
      },
      // 二维码的地址
      qrCodeUrl: {
        type: String,
        default: ''
      },
      routKey: {
        type: String,
        default: ''
      },
      // 文件上传的类型
      accept: {
        type: String,
        default: 'image/*,.pdf,.pdfx,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt'
      },
      sendMessage: {
        type: Boolean,
        default: false
      },
    },
    components: {
      LazyImg,
      [Upload.name]: Upload,
      [Modal.name]: Modal,
      QrCode,
      filePreview
    },

    data () {
      return {
        list: [],
        qrcodeVisible: false,
        // qrcode: '',
        qrContent: '',
        phoneUpload: {
          isInit: false,
          ws: null,
          routKey: '',
          isLog: true,
          client: null,
          appKey: 'oanew',
          appSecret: 'oanew',
          host: 'oa',
          // appKey: 'webmsg',
          // appSecret: 'jiuji',
          // host: 'webmsg',
          server: this.$tnt.wss,
          upChange: '/exchange/oaupload/',
          initMax: INIT_MAX,
          initTimerId: 0
        },
        // 上传需要的两个变量值
        appId: '',
        token: ''
      }
    },
    computed: {
      ...mapState({
        userInfo: state => state.userInfo || {}
      })
    },
    created () {
      this.list = this.fileList
      console.log(this.list)
      // this.handleFile()
      if (this.$tnt.mq && this.$tnt.mq.length) {
        let tagetMQ = this.$tnt.mq.find(m => m.sourceVhost === 'oa')
        if (tagetMQ) {
          this.phoneUpload.host = tagetMQ.vhost
          this.phoneUpload.appKey = tagetMQ.username
          this.phoneUpload.appSecret = tagetMQ.pwd
        }
      }
      const fileMsg = JSON.parse(window.sessionStorage.getItem('fileMsg'))
      if (fileMsg?.appId && fileMsg?.token) {
        this.appId = fileMsg.appId
        this.token = fileMsg.token
      }
    },
    watch: {
      // 监听数组变化和文件修改
      fileList: {
        deep: true,
        handler (newVal) {
          console.log('--newVal---', newVal)
          this.list = this.fileList
          this.$emit('fileChange', newVal)
        }
      }
    },
    methods: {
      isImg (file) {
        const reg = /\.(png|jpg|gif|jpeg|webp)$/
        return reg.test(file.fileName)
      },
      // fixme 组件升级已弃用该方法 获取上传需要的token
      getUploadToken () {
        return new Promise(async (resolve, reject) => {
          if (this.appId && this.token) return resolve(true)
          const fileMsg = JSON.parse(window.sessionStorage.getItem('fileMsg'))
          if (fileMsg?.appId && fileMsg?.token) {
            this.appId = fileMsg.appId
            this.token = fileMsg.token
            resolve(true)
          } else {
            const res = await this.$api.common.getUploadToken()
            if (res.code === 0) {
              this.appId = res.data.appId
              this.token = res.data.token
              window.sessionStorage.setItem('fileMsg', JSON.stringify({
                appId: this.appId,
                token: this.token
              }))
              resolve(true)
            } else {
              this.disabled = true
              this.$message.error('获取appID失败禁止上传！')
              resolve(false)
            }
          }
        })
      },
      // 上传
      async doUpload () {
        // 只能上传一个文件时
        if (!this.moreAmount && this.list.length) {
          this.$message.warning(this.moreAmountWarn)
          return
        }
        // 上传
        nineUpload({
          accept: this.accept,
          multiple: this.multiple,
          // appId,
          // token,
          onPickFiles: async files => { // files是文件对象，如果要对文件进行处理，处理完成之后要在返回对象里加上files对象
            if (window.nineUploadData) {
              return window.nineUploadData
            }
            try {
              // 获取appId和token。可以把这个appId和token缓存起来，没必要每次都请求
              const { code = 0, userMsg = '', data = { appId: '', token: '' } } = await this.$api.common.getUploadToken()
              if (code === 0) {
                window.nineUploadData = data
                setTimeout(() => { // appId和token30分钟过期，要清理一下
                  window.nineUploadData = null
                }, 30 * 60 * 1000)
                return data
              } else {
                this.$message.error(userMsg)
              }
            } catch (e) {
              this.$message.error(e)
            }
          },
          onProgress: ({ percent, fileIndex, fileCount }) => {
            this.percent = percent
            this.fileIndex = fileIndex
            this.fileCount = fileCount
          },
          form: {
            collection: 'javaweb'
          }
        }).then(({ res, err }) => {
          if (!this.multiple) {
            res = [res]
          }
          res = res.map(item => {
            let i = { fid: item.fid, fileName: item.fileName }
            if (this.addSize) {
              i.size = item.size
            }
            // 上传成功后的url根据你的pathKey给你返回
            i[this.pathKey] = item.fileUrl
            return i
          })
          if (res.length) {
            let filelist = [...this.list]
            filelist = [...filelist, ...res]
            this.$emit('update:fileList', filelist)
            this.$emit('change', filelist)
          }
          err?.map(i => {
            this.$message.info(`${i.name}上传失败,${i.err.message}`)
          })
        })
      },
      // 删除
      deleteFile (item, index) {
        // 单项数据流，防止数据混乱和多次触发change 事件
        let newFileList = Object.assign([], this.list)
        newFileList.splice(index, 1)
        this.$emit('update:fileList', newFileList)
        // 有id表示在数据库中存储（需要回传id进行删除），没有表示本地上传，只上传到了文件服务器中，没有上传到你的模块数据库中
        if (item.id) {
          // 有id就让后端调文件服务器的删除接口
          let delList = Object.assign([], this.delList)
          delList.push(item.id)
          delList = [...new Set(delList)]
          this.$emit('update:delList', delList)
          this.$emit('delete', item, index)
        } else {
        // 接文件服务器的删除接口
        }
      },
      // 下载文件
      downloadFile (file) {
        saveAs(file[this.pathKey], file.fileName)
      },
      initQr () {
        this.phoneUpload.routKey = this.routKey || this.phoneUpload.routKey || uuidv4() // 复用routKey
        this.phoneUpload.isLog = false

        let url = this.qrCodeUrl || this.qrContent || `${this.$tnt.mHost}/up.aspx?id=${this.phoneUpload.routKey}&p=honorApply` // 复用链接
        this.qrContent = url
      },
      showQr () {
        this.init()
      },
      closeQr () {
        this.destroyClient()
        this.qrcodeVisible = false
      },
      pushGateway () {
        // let url = `http://m.9ji.com/up.aspx?id=${this.userInfo.UserID}&p=Recoverorder&t=0&dt=0.7936689417876834`
        let params = {
          appName: 'oa',
          title: '上传图片',
          content: '请打开oa上传图片',
          extra: JSON.stringify({
            type: 8,
            isAuto: true,
            url: this.qrContent
          }),
          isTest: false,
          alias: ['staff_' + this.userInfo.UserID]
        }
        api.common.pushGateway(params).then(() => {
        }).finally(() => {
        })
      },
      init () {
        let { appKey, appSecret, host, server, isInit, routKey } = this.phoneUpload
        // eslint-disable
        // 先销毁
        this.destroyClient()
        this.initQr()
        // 再创建
        this.phoneUpload.ws = new WebSocket(server)
        this.phoneUpload.client = Stomp.over(this.phoneUpload.ws)
        this.phoneUpload.client.connect(appKey, appSecret, this.connectCallback, this.errorCallback, host)
        this.phoneUpload.isInit = true
      },
      destroyClient () {
        clearInterval(this.phoneUpload?.initTimerId)
        if (this.phoneUpload?.client) {
          this.phoneUpload.client.disconnect(() => {
            this.phoneUpload.isInit = false
            this.phoneUpload.client = null
            this.phoneUpload.ws = null
          })
        }
      },
      connectCallback () {
        // this.$message.success('websocket连接成功')
        let { upChange, routKey, isLog } = this.phoneUpload
        this.phoneUpload.client.subscribe(upChange + routKey, (d) => {
          if (d.body !== 'Heartbeat') {
            if (isLog) {
            }
            this.msgAction(d.body)
          }
        })
        // 连接成功后再显示二维码
        this.qrcodeVisible = true
        // 连接成功回调后再去推送
        if (this.sendMessage && this.$tnt.xtenant.toString() === '0' && this.phoneUpload.initMax === INIT_MAX) { // 九机需要消息推送的时候推送消息
          this.pushGateway()
        }
        // 连接成功后再监听失败
        this.phoneUpload.initTimerId = setInterval(() => {
          this.listenerDisconnect()
        }, 1000)
      },
      errorCallback (e) {
        console.warn(e)
        this.$message.error('websocket连接失败')
      },
      msgAction (result) {
        let list = JSON.parse(result)
        list = list.map(pic => {
          let item = {
            uid: pic.id,
            name: pic.filename,
            status: 'done',
            fileName: pic.filename,
          }
          item[this.pathKey] = pic.filepath
          return Object.assign({}, item, pic)
        })
        this.$emit('update:fileList', [...this.fileList, ...list])
        this.$emit('change', [...this.fileList, ...list])
        this.closeQr()
      },
      listenerDisconnect () {
        if ((this.phoneUpload.client && this.phoneUpload.client.connected) || this.phoneUpload.initMax <= 0) {
        // 正常连接中 或者 重试超过5次
        } else {
          this.phoneUpload.isInit = false
          this.init()
          this.phoneUpload.initMax--
        }
      },
      renameFile (file) {
        // todo 需要有异常兜底机制, 防止后端没有返回附件id的情况
        // if (!file.id) {
        //   this.$message.warn('无法修改附件名称,请截屏联系开发处理')
        //   new Error()
        //   return
        // }
        this.$indicator.open()
        this.$api.common.renameFile({
          id: file.id,
          filename: file.fileName
        }).then((res) => {
          if (res.code === 0) {
            this.$message.success(res.userMsg || '修改成功')
          } else {
            this.$message.error(res.userMsg || '修改失败,请重试')
          }
        }).finally(() => {
          this.$indicator.close()
        })
      }
    },
    beforeDestroy () {
      this.destroyClient()
    },

  }
</script>

<style lang="scss" scoped>
  $preview-item-width: 100px;

.noImg-warp{
  width: $preview-item-width;
  height: $preview-item-width;
  border-radius: 4px;
  padding:10px;
  background: rgba(200,200,200,0.3);

  .file-name{
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
    width: 113%;
    text-align: center;
    background: rgba(200,200,200,0.3);
    padding:10px;
    margin:-5px -5px 0;
  }
}
.qr-btn {
  margin-left: -240px;
}

.upload-btn {
  width: 350px;
}

.img-style {
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  padding: 6px;
  margin-right: 20px
}
.img-wrap{
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  min-height: 30px;
  .img-style{
    margin: 0 8px 8px 0;
  }
  margin-top: 16px;
}

.action-warp{
  position: absolute;
  width: $preview-item-width;
  bottom: 10px;
  left: 7px;
  display: flex;
  align-items: center;
  > div,
  > a{
    flex: 1;
  }
  a{
    text-align: center;
  }
}
</style>
