<template>
  <div style="display: flex">
    <img :alt="file[fileName]" :src="file[pathKey]" @error="onError" width="200" style="visibility: hidden;width: 1px;height: 1px;"/>
    <a-popover title="" trigger="hover" :getPopupContainer="trigger => trigger.parentElement" >
        <template slot="content">
          <a-button v-show="!isImg" @click="open" type="link">附件预览</a-button>
          <a-button v-show="isImg" @click="showImg" type="link">附件预览</a-button>
          <a-button v-if="download" @click="downloadFile" type="link">附件下载</a-button>
        </template>
        <span v-if="showName"  style="color:#1890ff;margin: .5em .5em;">{{file.fileName}}</span>
        <a-icon v-else style="color:#1890ff;margin:0em .5em;" type="eye"/>
    </a-popover>
    <a-modal v-model="visible" :title="file.fileName" width="30%" :footer="null" :closable="false" >
      <a :href="file[pathKey]" target="_blank" >
        <img ref="img" :alt="file[fileName]" :src="file[pathKey]" @error="onError" width="100%"/>
      </a>
    </a-modal>
  </div>
</template>
<script>
  import { saveAs } from 'file-saver'
  export default {
    props: {
      file: {
        type: Object,
        default: () => ({ fileName: '' }),
        validator: (fileList) => {
          let isRight = true
          if (!fileList.hasOwnProperty('fileName')) isRight = false
          if (!isRight) console.error('file: 参数不正确fileName')
          return isRight
        }
      },
      pathKey: {
        type: String,
        default: 'fileUrl'
      },
      showName: {
        type: Boolean,
        default: true
      },
      download: {
        type: Boolean,
        default: true
      }
    },
    data () {
      return {
        visible: false, // 预览图片窗口
        width: 1080, // 图片的大小
        isImg: true
      }
    },
    created () {
    },
    methods: {
      // 预加载图片获取是否图片和图片的width
      // 获取图片真实宽度会导致图片显示不完整
      loadImg () {
        let img = new Image()
        let width = 0
        img.src = this.file[this.pathKey]
        if (img.complete) {
          width = img.width
          this.width = img.width
        } else {
          img.onload = () => {
            img.onload = null
            this.width = img.width
          }
          img.onerror = () => {
            this.width = img.width
            img.onerror = null
          }
        }
        this.isImg = Boolean(this.width)
      },
      // 获取img的 width
      showImg () {
        this.visible = true
      },
      // 下载文件
      downloadFile (isImg) {
        saveAs(this.file[this.pathKey], this.file.fileName)
      },
      saveAs,
      // 图片加载失败
      onError () {
        this.isImg = false
      },
      // 预览
      open () {
        if (this.file.fileName.includes('.txt') || this.file[this.pathKey].includes('.txt')) {
          window.open(`https://view.xdocin.com/xdoc?_xdoc=${this.file[this.pathKey]}`)
        } else if (this.file.fileName.includes('.pdf')) {
          window.open(this.file[this.pathKey])
        } else window.open(`https://view.officeapps.live.com/op/view.aspx?src=${this.file[this.pathKey]}`)
      }
    }
  }
</script>
<style lang='scss' scoped>

</style>
