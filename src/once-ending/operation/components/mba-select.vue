<script lang="jsx">
  import { ref, defineComponent, watch } from 'vue'
  export default defineComponent({
    name: 'mba-select',
    props: {
      value: {
        type: [Number, String],
        default: undefined
      },
      placeholder: {
        type: String,
        default: '请选择'
      }
    },
    setup (props, ctx) {
      const options = [
        { label: 'MBA', value: 1 },
        { label: '大疆专区', value: 2 },
        { label: 'PC专区', value: 4 },
        { label: 'Pico专区', value: 8 },
        { label: 'Switch专区', value: 16 },
        { label: 'CS', value: 32 },
        { label: 'AAR', value: 64 },
      ]
      const modeValue = ref(undefined)
      watch(() => props.value, (val) => { modeValue.value = val }, { immediate: true })
      function changeSelect () {
        ctx.emit('input', modeValue.value)
      }
      return {
        options,
        modeValue,
        changeSelect
      }
    },
    render () {
      const { options, changeSelect, placeholder } = this
      return <a-select
        mode="multiple"
        maxTagCount={1}
        v-model={ this.modeValue }
        options={ options }
        allowClear
        optionFilterProp="children"
        placeholder={ placeholder }
        onChange={ (e) => { changeSelect(e) } }/>
    }
  })
</script>

<style scoped>

</style>
