<script lang="jsx">
  import { defineComponent, reactive, getCurrentInstance, watch, nextTick } from 'vue'
  import { NiImg } from '@jiuji/nine-ui'
  import Upvote from './upvote.vue'
  import JTextarea from './j-textarea.vue'
  import CollapseTransition from 'element-ui/lib/transitions/collapse-transition'
  import ShowChildren from './images/show-children.png'

  export default defineComponent({
    name: 'comment-list',
    components: { NiImg, Upvote, JTextarea, 'el-collapse-transition': CollapseTransition, },
    props: {
      list: {
        type: Array,
        default: () => {
          return []
        }
      },
      totalComment: {
        type: [Number, String],
      },
      refType: {
        type: [Number, String],
      },
      showBg: {
        type: Boolean,
        default: true
      },
      usePage: {
        type: Boolean,
        default: false
      },
      loadingMore: {
        type: Boolean,
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()

      function closeShowWrite (array) {
        if (Array.isArray(array)) {
          array.forEach(it => {
            it.showWriteComment = false
            it.children?.length && closeShowWrite(it.children)
          })
        }
      }

      function toComment (item, isChild, index) {
        const { showWriteComment } = item
        closeShowWrite(props.list)
        item.showWriteComment = !showWriteComment
        proxy.$emit('comment', item, isChild, index)
      }

      function changeShowChildren (item) {
        item.showChildren = !item.showChildren
        if (props.usePage && !item.children?.length && item.showChildren && !props.loadingMore) {
          item.current = 1
          item && (item.loadingMore = true)
          proxy.$emit('getMore', item) // 第一次获取子级评论
        }
      }

      function getMore (item) {
        if ((item && item.loading) || (!item && props.loadingMore)) {
          return
        }
        if (item) {
          item.loadingMore = true
          item.current += 1
        }
        item && (item.loadingMore = true)
        proxy.$emit('getMore', item) // 有item,获取更多子级评论，没有item，获取一级评论
      }

      return {
        toComment,
        closeShowWrite,
        changeShowChildren,
        getMore
      }
    },
    render () {
      const {
        list,
        totalComment,
        refType,
        toComment,
        changeShowChildren,
        showBg,
        usePage,
        loadingMore,
        getMore
      } = this
      const singleComment = (item, isChild, parent, index) => <div class="flex mt-20 full-width">
          <NiImg src={item.avatar || 'https://img2.ch999img.com/newstatic/1183,c13e0d2eb61885.jpg'} class={['b-50', isChild ? 'small-avatar' : 'big-avatar']} />
          <div class="flex flex-child-grow flex-col ml-10">
            <div class={[isChild ? 'mt-5' : 'mt-6']}>
              <span class="name">{item.name}</span>
              {item.replyName ? <span>
                <span class="grey-9 font-16">回复</span>
                <span class="name">{item.replyName}</span>
              </span> : null}
            </div>
            <div class="content">{item.content}</div>
            <div class="flex flex-aligen-center mt-10">
              <span class="font-12 grey-9 nowrap">{item.time}</span>
              <Upvote
                class="ml-20"
                {...{
                  props: this.$attrs,
                  on: this.$listeners
                }}
                showViews={false}
                item={item}
                refType={refType}
                showComment={true}
                showCommentCount={false}
                onChangeLike={(type) => this.$emit('changeLike', item, type)}
                onComment={() => toComment(item, isChild, index)}/>
            </div>
            <JTextarea
              loading={item.loading}
              placeholder={'回复  ' + item.name}
              class="mt-10"
              {...{
                props: this.$attrs,
                on: this.$listeners
              }}
              onAddComment={(form) => { this.$emit('addComment', form, item, parent) }}
              showComment={item.showWriteComment}/>
            {item.children?.length || item.subCommentCount > 0 ? <el-collapse-transition><div
              class={['child-content', showBg ? 'show-bg' : '']}>
              {item.children.slice(0, item.showChildren ? item.children.length : 1).map((child, ins) => singleComment(child, true, item, ins))}
              { usePage && item.children?.length < item.subCommentCount && item.showChildren ? <div class="flex flex-align-center show-children ml-42" onClick={() => getMore(item)}>
                <span>加载更多</span>
                <NiImg class={['img']} src={ShowChildren}/>
                { item.loadingMore ? <a-spin size="small"/> : null }
              </div> : null }
              { item.children?.length > 1 || (item.subCommentCount > 0 && item.children?.length !== 1) ? <div class="flex flex-align-center show-children" onClick={() => changeShowChildren(item)}>
                <span>{item.showChildren ? '收起' : '展开'}全部回复</span>
                <NiImg class={['img', item.showChildren ? 'transform' : '']} src={ShowChildren}/>
                { item.loadingMore && !item.showChildren ? <a-spin size="small"/> : null }
              </div> : null }
            </div></el-collapse-transition> : null}
          </div>
        </div>
      return <div class="comment-list">
        <div class="total">{totalComment || list?.length || 0}条评论</div>
        <div class={['full-width', list?.length ? '' : 'flex mt-20 flex-center no-comment']}>
          { list?.length ? <div>
            { list.map((item, index) => <div class="full-width">
            {singleComment(item, false, {}, index)}
          </div>) }
            { usePage && list.length < totalComment ? <div class="flex flex-center show-all" onClick={() => getMore(0)}>
              <span>加载更多</span>
              <NiImg class={['img']} src={ShowChildren}/>
              { loadingMore ? <a-spin size="small"/> : null }
            </div> : null }
          </div> : <span class="grey-9">
            暂无评论，快抢沙发吧~
          </span> }
        </div>
      </div>
    }
  })

</script>

<style scoped lang="scss">
.comment-list {
  .total {
    font-weight: 600;
    font-size: 16px;
    color: #0E182B;
    line-height: 22px;
  }
  .b-50 {
    border-radius: 50% ;
  }
  .big-avatar {
    width: 48px;
    height: 48px;
  }
  .small-avatar {
    width: 32px;
    height: 32px;
  }
  .name {
    font-weight: 600;
    font-size: 16px;
    line-height: 22px;
  }
  .content {
    line-height: 21px;
    margin-top: 10px;
  }
  .child-content {
    border-radius: 4px;
    padding-bottom: 20px;
    margin-top: 10px;
    transition: height 0.5s ease;
    min-height: 0;
  }
  .show-bg {
    background: #FAFAFA;
    padding-left: 20px;
    padding-right: 20px;
  }
  .max-height {
    height: auto;
  }
  .show-all {
    color: #239DFC;
    line-height: 14px;
    margin-top: 20px;
    cursor: pointer;
    .img {
      width: 14px;
      height: 14px;
      transition: all 0.5s ease
    }
  }
  .show-children {
    font-size: 12px;
    color: #239DFC;
    line-height: 12px;
    margin-top: 20px;
    cursor: pointer;
    .img {
      width: 12px;
      height: 12px;
      transition: all 0.5s ease
    }
    .transform {
      transform: rotate(180deg);
    }
  }
  .mt-5 {
    margin-top: 5px;
  }
  .mt-6 {
    margin-top: 6px;
  }
  .ml-20 {
    margin-left: 20px;
  }
  .mt-20 {
    margin-top: 20px;
  }
  .mt-10 {
    margin-top: 10px;
  }
  .ml-42 {
    margin-left: 42px;
  }
}
</style>
