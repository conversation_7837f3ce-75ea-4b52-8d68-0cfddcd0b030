<script lang="jsx">
  import { defineComponent, reactive, getCurrentInstance, watch, ref, nextTick } from 'vue'

  export default defineComponent({
    name: 'j-textarea',
    props: {
      showComment: {
        type: Boolean,
        default: false
      },
      placeholder: {
        type: String,
        default: '添加评论'
      },
      showAnonymous: {
        type: Boolean,
        default: true
      },
      loading: {
        type: Boolean,
        default: false
      },
      maxLength: {
        type: Number,
        default: 99999
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()
      const textareaRef = ref()

      watch(() => props.showComment, (show) => {
        clear()
        show && (nextTick(() => {
          textareaRef.value?.focus()
        }))
      })

      const form = reactive({
        content: undefined,
        anonymous: undefined
      })
      function addComment () {
        if (!form.content) {
          return proxy.$message.warning('请输入评论内容')
        }
        if (form.content.length > props.maxLength) {
          return proxy.$message.warning(`评论内容最多${props.maxLength}字`)
        }
        proxy.$emit('addComment', form)
      }
      function clear () {
        form.content = undefined
        form.anonymous = undefined
      }
      return {
        form,
        addComment,
        clear,
        textareaRef
      }
    },
    render () {
      const {
        addComment,
        form,
        showComment,
        placeholder,
        showAnonymous,
        loading,
        maxLength
      } = this
      return showComment ? <div class="comment-form flex flex-col flex-align-end full-width">
        <a-textarea
          ref="textareaRef"
          class="textarea"
          maxLength={maxLength}
          placeholder={placeholder}
          autoSize={{ minRows: 3, maxRows: 10 }}
          v-model={form.content}/>
        <div>
          {showAnonymous ? <span>匿名：<a-switch v-model={form.anonymous}></a-switch></span> : null }
          <a-button loading={loading} type="primary" class="ml-16 mt-16" onClick={addComment}>评论</a-button>
      </div>
    </div> : null
    }
  })

</script>

<style scoped lang="scss">
.comment-form{
  .textarea {
    display: block;
    outline: none;
    width: 100%;
    box-sizing: border-box;
    background: rgb(245 245 245);
    padding:10px;
  }
}
</style>
