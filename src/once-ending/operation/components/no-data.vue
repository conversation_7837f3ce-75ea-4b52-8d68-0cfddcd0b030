<script lang="jsx">
  import { defineComponent } from 'vue'
  import { NiImg } from '@jiuji/nine-ui'

  export default defineComponent({
    components: {
      NiImg
    },
    props: {
      isFeatch: {
        type: Boolean,
        default: false
      }
    },
    render () {
      const { isFeatch } = this
      return (
      <div class="no-data">
        <NiImg
          class="no-data-img"
          src="https://img2.ch999img.com/newstatic/14450/04349c93014c93f0.png"
        />
        {!isFeatch && <p class="no-query">请点击查询按钮查看数据</p>}
        {isFeatch && <p class="no-data-text">抱歉，没有查询到数据</p>}
      </div>
      )
    }
  })
</script>
<style lang="scss" scoped>
.no-data-img {
  height: 140px;
  width: 140px;
}
.no-data-text {
  width: 160px;
  height: 16px;
  font-size: 16px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.35);
  line-height: 16px;
  margin-top: 24px;
  text-align: center;
  width: 100%;
}
.no-query {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.35);
  line-height: 32px;
  margin-top: 16px;
  text-align: center;
  width: 100%;
}
</style>
