import axios from 'axios'
import store from '~/store'
import moment from 'moment'
import { message } from 'ant-design-vue'
import { error } from 'jquery'
export function download (params, url, name, otherName) {
  return new Promise((resolve, reject) => {
    axios({
      method: 'post',
      url,
      data: params,
      responseType: 'blob',
      timeout: 60000,
      headers: {
        Authorization: store.state.token
      }
    }).then(async (res) => {
      try {
        const enc = new TextDecoder('utf-8')
        const buffer = await res.data.arrayBuffer()
        const response = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
        if (response?.code !== 0 || !response.data) {
          const messageKey = response?.code !== 0 ? 'error' : 'info'
          message[messageKey](response?.userMsg || '无数据')
          resolve('无数据')
        }
      } catch {
        const link = document.createElement('a')
        let blob = new Blob([res.data], { type: 'application/x-excel' })
        link.style.display = 'none'
        link.href = URL.createObjectURL(blob)
        link.download = otherName || (name + moment().format('YYYYMMDDHHmmss') + '.xlsx')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        resolve('请求成功')
      }
    }).catch((err) => {
      reject(err)
    })
  })
}
