<template>
  <div class="flex flex-align-center">
    <div class="flex-child-noshrink">{{title}}</div>
    <el-autocomplete
      style="width:100%;"
      v-if="!multiple"
      v-model="text"
      :disabled="disabled"
      :fetch-suggestions="queryStaff"
      :placeholder="placeholder"
      :trigger-on-focus="false"
      :clearable="allowClear"
      @clear="select"
      @select="select"
    >
      <template slot-scope="{ item }">
        <div class="flex flex-justify-between">
          <span>{{ item.value }}</span>
          <span class="grey-9 font-12">{{item.department}}</span>
        </div>
      </template>
    </el-autocomplete>
    <el-select
      ref="selectcom"
      @change="select"
      @remove-tag="removeTag"
      style="width:100%;"
      v-if="multiple"
      v-model="text"
      multiple
      filterable
      :value-key="dataIsObjArr ? 'id' : ''"
      remote
      :placeholder="placeholder"
      :remote-method="queryStaff">
      <el-option
        v-for="item in options"
        :key="item.id"
        :label="item.label"
        :value="dataIsObjArr ? item : item.id">
        <span style="float: left">{{ item.label }}</span>
        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.department }}</span>
      </el-option>
    </el-select>
  </div>
</template>
<script>
  import Vue from 'vue'
  import { Autocomplete, Select, Option } from 'element-ui'
  Vue.prototype.$ELEMENT = { size: 'small', zIndex: 3000 }
  Vue.use(Autocomplete).use(Select).use(Option)
  export default {
    name: 'StaffInput',
    props: {
      allowClear: { // 清空派发的也是select事件,请注意返回值为undefined的判断
        type: Boolean,
        default: false
      },
      value: {
        type: [String, Array],
        default: ''
      },
      placeholder: {
        type: [String],
        default: ''
      },
      format: { // 1：1664-周子贤，2：1664，3：周子贤
        type: [Number],
        default: 1
      },
      multiple: {
        type: [Boolean],
        default: false
      },
      selectedBlur: {
        type: [Boolean],
        default: false
      },
      title: {
        type: [String],
        default: ''
      },
      disabled: { // 是否禁用
        type: [Boolean],
        default: false
      },
      dataIsObjArr: { // 是否禁用
        type: [Boolean],
        default: false
      }
    },
    watch: {
      value: function () {
        this.$nextTick(() => {
          this.text = this.value
        })
      },
      text: function (val) {
        if (!this.text) {
          this.$emit('clear')
        }
        this.$emit('input', val)
      }
    },
    data: () => ({
      text: '',
      options: [],
      selectObj: [],
    }),
    created () {
      this.text = this.value
      // this.multiple && this.queryStaff('', () => {}, this.text)
    },
    methods: {
      queryStaff (text, cb, ch999Ids) {
        if (!text && !ch999Ids) return
        this.$api.common.getStaffSuggest(text, ch999Ids).then(res => {
          if (res.stats === 1) {
            if (this.multiple === false) {
              let arr = res.data.map(d => ({
                value: this.format === 1 ? d.ch999_id + '-' + d.ch999_name : this.format === 2 ? d.ch999_id : d.ch999_name,
                department: d.departName,
                id: d.ch999_id,
                name: d.ch999_name,
                mobile: d.mobile
              }))
              cb(arr)
            } else {
              this.options = res.data.map(d => ({
                value: d.id,
                label: this.format === 1 ? d.ch999_id + '-' + d.ch999_name : this.format === 2 ? d.ch999_id : d.ch999_name,
                department: d.departName,
                id: d.ch999_id,
                name: d.ch999_name
              }))
            }
          } else {
            cb()
            /* fixme：暂时规避多选默认数据调用报错问题，考虑优化该情况为不发起请求 */
            if (this.value && this.multiple) return
            this.$message.error(res.msg || '没有搜索到数据')
          }
        })
      },
      select (e) {
        this.$emit('input', this.text)
        this.$emit('change', e)
        if (this.selectedBlur) {
          this.$nextTick(() => {
            this.$refs.selectcom.blur()
          })
        }
        if (this.multiple && this.selectObj.length < this.text.length) {
          this.options.forEach(item => {
            if (this.dataIsObjArr) {
              if (this.text.find(d => d.id === item.id) && this.selectObj.every(val => val.id !== item.id)) {
                this.selectObj.push(item)
              }
            } else {
              if (this.text.indexOf(item.id) !== -1 && this.selectObj.every(val => val.id !== item.id)) {
                this.selectObj.push(item)
              }
            }
          })
          this.$emit('select', this.selectObj)
        }
      },
      removeTag (value) {
        this.selectObj.forEach((item, index) => {
          if (item.id === value) {
            this.selectObj.splice(index, 1)
          }
        })
        this.$emit('select', this.selectObj)
      }
    }
  }
</script>
