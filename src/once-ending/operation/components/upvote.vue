<script lang="jsx">
  import { defineComponent, getCurrentInstance, watch } from 'vue'
  import { NiImg } from '@jiuji/nine-ui'
  import toLikeBig from './images/toLikeBig.png'
  import isLike from './images/isLike.png'
  import toLikeSmall from './images/toLikeSmall.png'
  import toTrampleSmall from './images/toTrampleSmall.png'
  import toTrampleBig from './images/toTrampleBig.png'
  import isTrample from './images/isTrample.png'
  import toStarSmall from './images/toStarSmall.png'
  import toStarBig from './images/toStarBig.png'
  import isStar from './images/isStar.png'
  import comment from './images/comment.png'
  import view from './images/view.png'
  import upvoteApi from '@operation/api/upvote'
  import { to } from '~/util/common'

  const buttonOptions = [
    { action: 'views', img: view, numberKey: 'views', showKey: 'showViews' },
    { action: 'comment', img: comment, numberKey: 'commentCount', showKey: 'showComment', showNumberKey: 'showCommentCount' },
    { action: 'like', label: '赞一个', isLabel: '已赞', booleanKey: 'likeStatus', img: toLikeBig, isImg: isLike, numberKey: 'likeCount', smallImg: toLikeSmall },
    { action: 'dislike', label: '踩一个', isLabel: '已踩', booleanKey: 'dislikeStatus', showKey: 'showDislike', img: toTrampleBig, isImg: isTrample, numberKey: 'dislikeCount', smallImg: toTrampleSmall },
    { action: 'collect', label: '收藏', isLabel: '已收藏', booleanKey: 'collectStatus', img: toStarBig, isImg: isStar, numberKey: 'collectCount', smallImg: toStarSmall, showKey: 'showCollect', showNumberKey: 'showCollectCount' },
  ]

  export default defineComponent({
    name: 'upvote',
    components: { NiImg },
    props: {
      refType: {
        type: String, // 门店留言-1, 心声-2，九机人家-5，10-公告详情，11-公告评论
      },
      bigButton: { // 按钮
        type: Boolean,
      },
      showCollect: {
        type: Boolean,
      },
      showComment: {
        type: Boolean,
      },
      showViews: {
        type: Boolean,
        default: true,
      },
      showDislike: {
        type: Boolean,
        default: true,
      },
      showCollectCount: {
        type: Boolean,
        default: true,
      },
      showCommentCount: {
        type: Boolean,
        default: true,
      },
      commentCount: { // 评论数量
        type: Number,
      },
      useLikeApi: {
        type: Boolean,
        default: true,
      },
      item: {
        type: Object,
        default: () => {
          return {
            id: undefined,
            views: undefined,
            likeStatus: undefined,
            likeCount: undefined,
            dislikeStatus: undefined,
            dislikeCount: undefined,
            collectStatus: undefined,
            collectCount: undefined,
          }
        }
      }
    },
    setup (props) {
      const { proxy } = getCurrentInstance()

      async function likeOrDislike (it) {
        const { action } = it
        const { refType, item } = props
        const params = {
          id: item.id,
          type: action,
          refType,
          likeKey: `${action}Flag`
        }
        params[`${action}Flag`] = item[it.booleanKey] ? 0 : 1
        const [err, res] = await to(upvoteApi.likeOrDislike(params))
        if (err) throw err
        const { code, userMsg, data } = res
        if (code === 0) {
          // proxy.$message.success('操作成功')
          // proxy.$emit('success')
          Object.assign(item, data)
          console.log('item', item)
        } else {
          proxy.$message.error(userMsg)
        }
      }
      function toAction (it) {
        const { useLikeApi } = props
        switch (it.action) {
        case 'views':
          proxy.$emit('views')
          break
        case 'comment':
          proxy.$emit('comment')
          break
        case 'like':
          useLikeApi ? likeOrDislike(it) : proxy.$emit('changeLike', it.action,)
          break
        case 'dislike':
          useLikeApi ? likeOrDislike(it) : proxy.$emit('changeLike', it.action)
          break
        case 'collect':
          proxy.$emit('collect')
          break
        }
      }
      return {
        toAction
      }
    },
    render () {
      const {
        bigButton,
        item,
        toAction,
        showViewsDetail,
        commentCount
      } = this
      return <div>
        { bigButton ? <div class="flex flex-align-center">
          { buttonOptions.slice(2, 5).map((it, index) => it.showKey && !this[it.showKey] ? null : <div
            onClick={() => toAction(it)}
            class={['big-button', item[it.booleanKey] ? 'special' : 'normal', it.action ? 'pointer' : '']}>
              <NiImg class={[[0, 1].includes(index) ? 'img-15' : 'img-14', 'img']} src={item[it.booleanKey] ? it.isImg : it.img}/>
              <span class="ml-2">{item[it.booleanKey] ? it.isLabel : it.label}</span>
            { it.showNumberKey && !this[it.showNumberKey] ? null : <span class="ml-2">
              {(it.numberKey === 'commentCount' ? commentCount || item[it.numberKey] : item[it.numberKey]) || 0}
            </span> }
            </div>
          )}
        </div> : <div class="flex flex-align-center">
          { buttonOptions.map((it, index) => it.showKey && !this[it.showKey] ? null : <div
            onClick={() => toAction(it)}
            class={['flex flex-align-center small', it.action === 'views' ? (showViewsDetail ? 'views-detail' : '') : it.action ? 'pointer' : '']}>
            <NiImg class={[[2, 3].includes(index) ? 'img-15' : 'img-14', 'img']} src={item[it.booleanKey] ? it.isImg : (it.smallImg || it.img)}/>
            { it.showNumberKey && !this[it.showNumberKey] ? null : <span class="ml-2">
              {(it.numberKey === 'commentCount' ? commentCount || item[it.numberKey] : item[it.numberKey]) || 0}
            </span> }
          </div>) }
        </div> }
      </div>
    }
  })
</script>

<style scoped lang="scss">
.big-button {
  width: 88px;
  height: 32px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
  font-size: 12px;
  &:first-child {
    margin-left: 0;
  }
}
.img {
  height: 14px;
}
.img-15 {
  width: 15px;
}
.img-14 {
  width: 14px;
}
.ml-2 {
  margin-left: 2px;
}
.normal {
  border: 1px solid #CCCCCC;
}
.special {
  border: 1px solid #1890FF;
  color: #1890FF;
}
.small {
  font-size: 12px;
  color: #828282;
  margin-left: 16px;
  &:first-child {
    margin-left: 0;
  }
}
.views-detail {
  color: #1890ff;
  cursor: pointer;
}
</style>
