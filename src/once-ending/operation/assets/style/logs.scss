  @import './var';

  .logs-warp{
    margin-bottom: 16px;
    li {
      padding: 10px 0 10px ($time-width + 20);
      position: relative;

      &:before,
      &:after {
        display: block;
        content: '';
        position: absolute;
        z-index: 4;
      }

      &:before {
        width: $timeline-icon-size;
        height: $timeline-icon-size;
        border-radius: 50%;
        background: #ffffff;
        border: $time-border;
        left: $time-width;
        top: 18px;
      }

      &:after {
        height: 100%;
        width: 1px;
        left: $time-width + 5;
        background: $primary-color;
        top: 20px;
        z-index: 3;
      }

      &:last-child:after {
        display: none;
      }
      .time{
        position: absolute;
        width: $time-width;
        text-align: center;
        margin-left: -($time-width + 25);
        line-height: 1.45;
      }
      .title, .user-name{
        font-weight: 600;
      }
      .description {
        line-height: $log-description-line-height;
        color: $log-description-color;
      }
    }
  }