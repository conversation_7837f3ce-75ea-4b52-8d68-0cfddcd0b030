/**
 * Created by ebi on 2017/5/11.
 */
import fetch from './fetch'
import { orderservice, ncSegments, org, trainService, web, orgWithCode } from './fetch-xservice'
import store from './store'
import qs from 'qs'
import partTime from './part-time'
import medal from './medal'
import ruleConfig from './rule-config'
import smallRefund from './small-refund'
import humanResources from './human-resources'
import corp from './corp'
import member from './member'
import executive from './executive'
import order from './order'
import report from './report'
import recovery from './recovery'
import afterService from './after-service'
import merchantManagement from './merchant-management'
import bigpro from '~/api/bigpro'
import finance from './finance'
import performance from './performance'
import purchase from './purchase'
import { treeWalk } from '~/util/treeUtils'
import coupon from './coupon'
import monitor from './monitor'
import appleCare from './apple-care'
import visit from './visit'
import bussiness from './bussiness'
import training from './training'
import partner from './partner'
import rankConfig from './rank-config'
import cooperation from '~/api/cooperation'
import evaluate from '~/api/evaluate'
import stock from '~/api/stock'
import edition from '~/api/edition'
import statistics from '~/api/statistics'
import supplierFeedback from '~/api/supplier-feedback'
import staff from './staff'
import job from './job'
import channel from './channel'
import delivery from '~/api/delivery'
import profitStatement from './profit-statement'
import robotGoods from './robot-goods'
import hr from './hr'
import newPrint from './new-print'
import repair from './repair'
import memorandum from './memorandum'
import rent from './rent'
import family from './family'
import pdaGoods from './pda-goods'
import help from './help'
import mobilePackage from './mobile-package'
import wuliuException from './wuliu-exception'
import smallCheck from './small-check'
import globalConfig from './global-config'
import excelAction from './excelAction'
import operation from './operation'
import distribution from './distribution'
import workOrder from './work-order'
import account from './account'
import office from './office'
import axios from 'axios'
import storeState from '../store'
export default {
  partTime,
  visit,
  medal,
  humanResources,
  corp,
  executive,
  afterService,
  merchantManagement,
  bigpro,
  performance,
  rankConfig,
  coupon,
  purchase,
  monitor,
  appleCare,
  bussiness,
  cooperation,
  evaluate,
  statistics,
  supplierFeedback,
  staff,
  job,
  delivery,
  channel,
  profitStatement,
  robotGoods,
  hr,
  newPrint,
  repair,
  memorandum,
  rent,
  family,
  pdaGoods,
  mobilePackage,
  wuliuException,
  excelAction,
  operation,
  distribution,
  workOrder,
  office,
  common: {
    // 公共附件表文件重命名
    attachmentRename (params) {
      return ncSegments('/api/ncAttachments/rename/v1', params)
    },
    uploadPath: '/commonApi/uploadFile',
    uploadPathNew: '/cloudapi_nc/orderservice/api/minFile/upload/v1?xservicename=oa-orderservice&collection=oa',
    downloadPath: '/api/staffAchievement/certificationApply/downloadFile',
    // 获取城市
    getAllAreaTree (params) {
      return ncSegments('/inapi/area/getAllAreaTree/v1', params)
    },
    // 主要角色
    listAllRoles () {
      return org('/inapi/ch999User/listAllRoles')
    },
    logUserInfo (params) {
      return org('/api/front/component/user-info/log', params)
    },
    getAreaAuthorizeList () {
      return fetch(`/noticeApi/GetAreaAuthorizeList`)
    },
    getIsHQ (params) {
      return fetch(`/noticeApi/GetIsHQ`, params)
    },
    // 上传文件时获取用户的 appid token
    getUploadToken () {
      return fetch('/cloudapi_nc/org_service/fileService/api/getToken/v1?xservicename=oa-org', '', 'get', 'json')
    },
    // https://apidoc.9ji.com/web/#/173?page_id=2035
    getVideoInfoByFidV2 (params, host) { // 根据fid获取视频信息
      let mHost = 'https://upload.9xun.com'
      if ([10002, 10003, 10004].includes(window.tenant.tenantId)) { // 易腾
        mHost = 'https://upload.iteng.com'
      }
      if ([10001].includes(window.tenant.tenantId)) { // 智乐方
        mHost = 'https://upload.zlf.co'
      }
      // 租户的统一上传域名
      let uhost = host || mHost
      console.log('uhost', uhost)
      return fetch(`${uhost}/vodserver/getInfoByFidV2`, params)
    },
    getAccountCode (params) {
      return fetch('/api/GetAccountCode', params, 'get', 'json')
    },
    // 重命名文件
    renameFile (params) {
      return fetch('/upload/UpdateFileName', params, 'post', 'json')
    },
    // 附件重命名 C#端
    rename (params) {
      return fetch('/commonApi/UpdateFileNameBatch', { readyChangeFileNames: params }, 'post', 'form')
    },
    // pc的登录验证token和session
    getUserInfo () {
      // return fetch('/commonApi/getLoginInfo')
      if (process.env.NODE_ENV === 'development') {
        return fetch('/commonApi/getLoginInfo')
      }
      return fetch('/commonApi/getPCLoginInfo')
    },
    // 获取租户名称
    getPrintName () {
      return fetch('/commonApi/GetPrintName')
    },
    // 统计报表请求头
    CommonStatisticHeader (params) {
      return fetch('/commonApi/CommonStatisticHeader', params, 'post', 'json')
    },
    // 统计报表主体
    GetCommonStatisticData (params) {
      return fetch('/commonApi/GetCommonStatisticData', params, 'post', 'json')
    },
    // 消息通知回调
    waitCall (params) {
      return fetch('/push/sub', params)
    },
    // 获取省市区门店树
    getAreaInfoAll (params) {
      return fetch('/api/getAreaInfoAll', params)
    },
    pushGateway (params) { // app消息推送
      return fetch('/cloudapi_nc/pushGateway/api/pushGateway/pushMsg/v1', params, 'post', 'json', { xservicename: 'push-gateway' })
    },
    getGoodsCategory () { // 查询商品分类
      return orderservice(`/api/oaorder/category/getCategoryTree/v1`)
    },
    getSmallGoodsCategory (params) { // 查询小件商品分类
      return orderservice(`/api/oaorder/category/getCategoryTree/v1`, params)
    },
    getProductLabelCategoryTree (params) { // 查询商品生命周期管理: 小件商品分类
      return orderservice('/api/oaorder/category/getProductLabelCategoryTree', params)
    },
    listCategoryByIds (params) { // 根据商品分类id获取所有子节点
      return orderservice('/api/oaorder/category/listCategoryByIds', params)
    },
    getArea () { // 获取省市区
      return fetch('/commonApi/getAreaJson')
    },
    getStaffSuggest (keywords = '', ch999Ids, authorizeid = '') {
      return fetch('/ajaxapi/getAutoCh999User', { q: keywords, ch999Ids, authorizeid })
    },
    getJiujiUserInfo (data) {
      return fetch(`/cloudapi_nc/org_service/inapi/cloud/front/search/user?xservicename=oa-org`, data, 'post', 'json')
    },
    async getAreaTree (type = 'area', userId, params, showClose = false) { // 默认type='area',假如有type传入，就自动走下面的判断
      let url = ''
      type === 'area' && (url = `/commonApi/getDepartAndAreaTreeData?showClose=${showClose}`)
      type === 'department' && (url = '/commonApi/getDepartAndAreaTreeData')
      type === 'departmentAuth' && (url = `/cloudapi_nc/org_service/api/departInfo/hr/getAllDepartAuthorizeTree?xservicename=oa-org`)
      type === 'role' && (url = '/commonApi/getDepartRoleTreeData')
      type === 'areaRole' && (url = `/cloudapi_nc/org_service/inapi/areainfo/c-tree?xservicename=oa-org&userId=${userId}`)
      let res = await fetch(url, params)
      if (type === 'departmentAuth') {
        treeWalk(res?.data || [], node => (node.id = node?.value)) // 和其他接口统一数据结构
      }
      return res
    },
    getStaffRank () {
      return fetch('/commonApi/getPositionData')
    },
    getStaffRole () {
      return fetch('/commonApi/getRoleTreeData')
    },
    getSelectOption () {
      return fetch('/coach/instructor/api/common/getDict/v1')
    },
    CShapUploadImg (params) {
      return fetch('/commonApi/uploadFile', params, 'post', 'file')
    },
    uploadFiles (params) { // 多文件分片上传
      return fetch('/docapi/DocumentOp/uploadFile', params, 'post', 'file')
    },
    uploadImgPath (params) {
      return fetch('/cloudapi_nc/orderservice/api/minFile/upload/v1?xservicename=oa-orderservice&collection=oa', params, 'post', 'file')
    },
    uploadImgApi: '/cloudapi_nc/orderservice/api/minFile/upload/v1?xservicename=oa-orderservice&collection=oa',
    checkFile (params) { // 上传文件前验证是否存在
      return fetch('/vodserver/checkFile', params, 'post')
    },
    vodserverUpload (params) { // 分片上传
      let url = window.tenant.staticPath
      return fetch(url + '/vodserver/upload', params, 'post')
    },
    getCategory () {
      return fetch('/api/cidList?issubitem=false')
    },
    getCategoryTree () {
      return fetch('/api/cidList')
    },
    getBrandListByCid (cids) {
      return fetch(`/ajaxApi/loadBrandByCids`, { cids })
    },
    getTenantList () {
      return trainService(`/api/tenant`, '', 'get', 'json')
    },
    getVideoInfoByFid (params) {
      return fetch(`https://upload.9xun.com/vodserver/getInfoByFidV2`, params, 'get')
    },
    getRSAByT () { // 过渡页接口
      return fetch('/cloudapi_nc/org_service/api/ch999User/convert-token?xservicename=oa-org', {}, 'get')
    },
    getStatistic (params) {
      return ncSegments(`/inApi/urlView/getTotal/v1`, params, 'post', 'json')
    },
    getIsCurAreaHQ (areaId) { // 过渡页接口
      return fetch(`/cloudapi_nc/org_service/inapi/areainfo/isCurAreaHQ/${areaId}?xservicename=oa-org`, {}, 'get')
    },
    isEndArea (areaId) { // 返回areaType：0闭店、1前端、2后端、NULL运营商
      return fetch(`/cloudapi_nc/org_service/inapi/areainfo/isEndArea/${areaId}?xservicename=oa-org`, {}, 'get')
    },
    checkIsolate (sign, id) { // 是否授权隔离
      return fetch(`/cloudapi_nc/office/api/authorizeSwitchConfig/checkIsolate/v1?xservicename=oa-office&sign=${sign}&xTenant=${id}`)
    },
    searchStaff (params) {
      return org(`/inapi/cloud/front/search/user`, params, 'post', 'json')
    },
    GetImgExInfo (params) {
      return fetch('/app/GetImgExInfo', params, 'post', 'json')
    },
    getNewCity (optimize = 1) {
      return web('/api/area/allAreaTree/v1', { optimize })
    }
  },
  menu: {
    getAllMenuList (params) { // 获取所有菜单
      return fetch('/cloudapi_nc/office/api/menuManage/all/v1?xservicename=oa-office', params)
    },
    getSubPageList (params) { // 获取该菜单下的四级
      return fetch('/cloudapi_nc/office/api/menuManage/page/v1?xservicename=oa-office', params)
    },
    getSubMenuList (params) { // 获取该菜单下的子菜单
      return fetch('/cloudapi_nc/office/api/menuManage/list/v1?xservicename=oa-office', params)
    },
    getMenuItem (params) { // 获取菜单详情
      return fetch('/cloudapi_nc/office/api/menuManage/detail/v1?xservicename=oa-office', params)
    },
    updateSort (params) { // 更新排序
      return fetch('/cloudapi_nc/office/api/menuManage/sortValue/v1?xservicename=oa-office', params, 'post', 'json')
    },
    getEnumData () { // 获取添加菜单显示的枚举列表
      return fetch('/cloudapi_nc/office/api/menuManage/getEnum/v1?xservicename=oa-office')
    },
    addOrUpdateMenuItem (params) { // 新增or修改菜单
      let api = params.id ? '/cloudapi_nc/office/api/menuManage/update/v1?xservicename=oa-office' : '/cloudapi_nc/office/api/menuManage/add/v1?xservicename=oa-office'
      return fetch(api, params, 'post', 'json')
    },
    deleteMenuItem (params) { // 删除菜单
      return fetch('/cloudapi_nc/office/api/menuManage/remove/v1?xservicename=oa-office', params, 'delete', 'form')
    },
    updateMenuStatus (params) { // 修改菜单状态
      return fetch('/cloudapi_nc/office/api/menuManage/updateState/v1?xservicename=oa-office', params)
    },
    getParentMenuList (params) { // 获取菜单显示的下拉列表
      return fetch('/cloudapi_nc/office/api/menuManage/parent/v1?xservicename=oa-office', params)
    },
    getViewTemplateList (params) { // 获取租户模板列表
      return fetch('/cloudapi_nc/office/api/menuManage/viewTemplate/v1?xservicename=oa-office', params)
    },
    addViewTemplateList (params) { // 添加租户模板列表
      return fetch('/cloudapi_nc/office/api/menuManage/template/v1?xservicename=oa-office', params, 'post', 'json')
    },
    getOutputMenuList (params) {
      return fetch('/cloudapi_nc/office/api/menuManage/pcOutput/v1?xservicename=oa-office', params)
    },
    getPreviewMenuList (params) {
      return fetch('/cloudapi_nc/office/api/menuManage/enable/v1?xservicename=oa-office', params)
    },
    getCommonMenuList (params) {
      return fetch('/cloudapi_nc/office/api/menuManage/publicMenu/v1?xservicename=oa-office', params)
    }
  },
  notice: {
    getCate () {
      return fetch('/noticeApi/GetNoticeTypeList')
    },
    getList (params) {
      return fetch('/noticeApi/loadNoticeList', params, 'post')
    },
    getDetail (id, isEdit = 0) {
      return fetch('/noticeApi/loadNoticeInfo', { id, isEdit })
    },
    exportFile (id) {
      let url = '/noticeApi/NoticeViewsExport?id=' + id
      return {
        url,
        method: 'get'
      }
    },
    save (params) {
      return fetch('/noticeApi/addNotice', params, 'post', 'json')
    },
    deleteNotice (params) {
      return fetch('/noticeApi/delNotice', params, 'post')
    },
    favorite (id = 0, type = 'delcollect') {
      return fetch('/noticeApi/markNotice', { id, type }, 'post')
    },
    praise (id = 0) {
      return fetch('/noticeApi/markNotice', { id, type: 'parse' }, 'post')
    },
    markAsRead (id = 0, type) {
      return fetch('/noticeApi/markNotice', { id, type }, 'post')
    },
    getViewsInfo (params) {
      return fetch('/noticeApi/loadNoticeViews', params)
    },
    getNoticeComment (NoticeId = 0) {
      return fetch('/noticeApi/getNoticeComments', { NoticeId })
    },
    addComment (params) { // 添加评论
      return fetch('/noticeApi/addNoticeComment', params)
    },
    getAuditLog (id = 0) {
      return fetch('/noticeApi/getNoticeCheckLog', { id })
    },
    doAudit (params) {
      return fetch('/noticeApi/noticeCheck', params, 'post')
    },
    getFileCateTree () {
      return fetch('/noticeApi/loadFileTree')
    },
    getViewsLog (params) {
      return fetch('/noticeApi/loadNoticeViews', params)
    },
    replyComment (params) { // 回复评论
      return fetch('/noticeApi/addNoticeComment', params)
    },
    noticeCommentZan (commentId) { // 文章点赞
      return fetch('/noticeApi/noticeCommentZan', { commentId })
    },
    AddAttachment (params) {
      return fetch('/Upload/AddAttachment', params, 'post', 'file')
    }
  },
  keliu: {
    getQueryOption () { // 获取下拉菜单初始数据
      return fetch('/keliu/api/customerFlow/getQueryOption/v1')
    },
    getCountData (params) { // 获取转化率统计数据
      return fetch('/keliu/api/customerFlow/getCountData/v1', params, 'post', 'json')
    }
  },
  coach: {
    cocherBigData (year = '') { // 教练制统计表
      return fetch('/coach/instructor/api/hr/cocherBigData/v1', { year })
    },
    getBaseContractList () { // 获取学员合约的基本信息列表
      return fetch('/coach/instructor/api/student/getBaseContractList/v1')
    },
    getNewContract () { // 教练点击新建合同的时候调用的接口
      return fetch('/coach/instructor/api/coach/getNewContract/v1')
    },
    getCoachContract (coachId = '') { // 获取合约详情表
      return fetch(`/coach/instructor/api/coach/getCoachContract/v1`, { coachId })
    },
    getCoacherList (keyword = '') { // 获取教练信息
      return fetch('/coach/instructor/api/coach/getCoacherList/v1', { keyword })
    },
    getStaffInfoByKeywords (keyword = '') { // 获取教练信息
      return fetch('/coach/instructor/api/common/getStaffInfoByKeywords/v1', { keyword })
    },
    addCoach (params) { // 新建
      return fetch('/coach/instructor/api/coach/addCoach/v1', params, 'post', 'json')
    },
    updateCoach (params) { // 修改合同
      return fetch('/coach/instructor/api/coach/updateCoach/v1', params, 'post', 'json')
    },
    getContract (params) { // 人资界面
      return fetch('/coach/instructor/api/hr/getCoacherList/v1', params)
    },
    getCoachTree () { // 获取教练关系树
      return fetch('/coach/instructor/api/hr/getCoachTree/v1')
    },
    coacherListGroup (params) { // 获取教练聚合数据
      return fetch('/coach/instructor/api/hr/coacherListGroup/v1', params)
    },
    updateContractBindStatus (params) { // 人资界面
      return fetch('/coach/instructor/api/hr/updateContractBindStatus/v1', params, 'post')
    },
    passBatch (params) { // 批量同意教练关系
      return fetch('/coach/instructor/api/hr/passBatch/v1', params, 'post', 'json')
    },
    getStudentDetail (ch999Id = '') { // 获取教练学员的信息
      return fetch('/coach/instructor/api/coach/getStudentDetail/v1', { ch999Id })
    },
    coachFace (params) { // 获取教练学员的信息
      return fetch('/coach/instructor/api/coach/coachFace/v1', params)
    },
    getDepartmentTree () { // 获取中心/区域
      return fetch('/coach/instructor/api/common/getDepartmentTree/v1')
    },
    getAreaList (departmentCode = '') { // 获取部门/门店
      return fetch('/coach/instructor/api/common/getAreaList/v1', { departmentCode })
    },
    getCurrentCoacherInfo (params) { // 获取教练信息
      return fetch('/coach/instructor/api/coach/getCurrentCoacherInfo/v1', params)
    },
    removeCoachById (coachId = '') { // 删除合同
      return fetch('/coach/instructor/api/coach/removeCoachById/v1', { coachId }, 'post')
    },
    recoveryCoachById (coachId = '') { // 恢复合同
      return fetch('/coach/instructor/api/coach/recovery', { coachId }, 'post')
    },
    cloneCoachById (coachId = '') { // 续约合同
      return fetch('/coach/instructor/api/coach/clone', { coachId }, 'post')
    },
    exportTable (params) { // 导出合同
      let url = '/coach/instructor/api/hr/coacherListExport/v1'
      return url + '?' + qs.stringify(params)
    },
    exportStatistics (params) { // 教练聚合数据导出
      let url = '/coach/instructor/api/hr/coacherListGroupExport/v1'
      return url + '?' + qs.stringify(params)
    },
    getScoreDetail (params) { // 积分明细查询
      return fetch('/coach/instructor/api/coach/getScoreDetail/v1', params)
    }
  },
  user: {
    getUserInfo (params) { // 获取个人信息接口
      return fetch('/ch999UserApi/getUserInfo', params)
    },
    getAchievementAmounts (params) {
      return fetch('/ch999UserApi/GetUserAchievementAmounts', params)
    },
    uploadUserView (params) { // 获取个人信息接口
      return fetch('/app/UploadUserView', params, 'post', 'json')
    },
    getUser (params) { // 用ch999UserId查询用户详情
      return fetch(`/cloudapi_nc/org_service/api/ch999User/getCh999UserInfo`, params, 'get', 'form', { xservicename: 'oa-org' })
    },
    getUserLog (params) { // 日志查询接口
      return fetch(' /ch999UserApi/getUserLog', params)
    },
    getUserRecordLog (params) { // 履职日志查询接口
      return fetch(' /ch999UserApi/GetEmploymentRecords', params)
    },
    getPositionRealData (params) { // 获取职级
      return fetch('/commonApi/getPositionRealData', params)
    },
    getVerificationRank (params) { // 能否查看人才盘点
      return orgWithCode('/api/ch999User/checkInventoryRank/v1', params)
    },
    getInterestTags (id) { // 获取兴趣标签
      return fetch('/ch999UserApi/interestTags', { ch999_id: id })
    },
    saveInterestTags (tags) { // 保存兴趣标签
      return fetch('/ch999UserApi/userTagsSet', { tags: tags })
    },
    saveUserHomeInfo (params) { // 员工保存自己家庭地址信息
      return fetch('/ch999UserApi/saveUserHomeInfo', params, 'post', 'json')
    },
    saveUserBaseInfo (params) { // 基本信息保存接口 tab - 0
      return fetch('/ch999UserApi/saveUserBaseInfo', params, 'post', 'json')
    },
    toDeleteUser (params) { // 从企业微信删除员工
      return orgWithCode('/inApi/wechat/corp/user/removeUserByCh999Id/v1', params, 'post', 'json')
    },
    saveUserUpdate (params) { // 提示更新个人信息接口
      return fetch('/ch999UserApi/SaveCh999UserTipData', params, 'post', 'json')
    },
    saveUserExperience (params) { // 个人经历信息保存接口 tab - 1
      return fetch('/ch999UserApi/saveUserExperience', params, 'post', 'json')
    },
    saveUserPlan (params) { // 个人规划信息保存接口 tab - 2
      return fetch('/ch999UserApi/saveUserPlan', params, 'post', 'json')
    },
    saveUserSalary (params) { // 薪酬数据保存 tab - 3
      return fetch('/ch999UserApi/saveUserSalary', params, 'post', 'json')
    },
    validtPiqian (code) { // 添加日志时校验批签号接口
      return fetch('/ch999UserApi/validtPiqian', { piqianhao: code })
    },
    addUserLog (params) { // 添加日志
      return fetch('/ch999UserApi/addUserLog', params, 'post', 'form')
    },
    saveEmploymentRecord (params) { // 员工履历添加
      return fetch('/ch999UserApi/saveEmploymentRecord', params, 'post', 'form')
    },
    updateEmploymentRecord (params) { // 员工履历修改
      return fetch('/ch999UserApi/UpdateEmploymentRecords', params, 'post', 'form')
    },
    deleteEmploymentRecord (params) { // 员工履历删除
      return fetch('/ch999UserApi/DeleteEmploymentRecord', params, 'post', 'json')
    },
    addCreditPoints (params) { // 征信分添加
      return fetch('/ch999UserApi/AddCreditPoints', params, 'post', 'form')
    },
    getCreditPointsRecords (params) { // 征信分查询
      return fetch('/ch999UserApi/GetCreditPointsRecords', params, 'post', 'form')
    },
    getCh999Detail (phone) { // 根据号码获取员工基本信息
      return fetch('/ch999UserApi/GetCh999Detail', { phone: phone })
    },
    getEducationList () {
      return fetch('/ch999UserApi/GetEducationList')
    },
    getCh999DetailInfo (phone) {
      return org(`/api/hrEntryStaffBasicInfo/queryByPhoneNum/${phone}/v1`)
    },
    getCh999UserView (params) {
      return fetch('/app/UserViewPagination', params, 'post', 'json')
    },
    setCh999TipFlag (type) { // 个人信息修改确认接口
      return fetch('/ch999UserApi/setCh999TipFlag', { type })
    },
    getGraduateTags () { // 获取应届生标签
      return fetch('/ch999UserApi/GetGraduateTags', {}, 'get', 'json')
    },
    createGraduateTag (params) { // 创建应届生标签
      return fetch('/ch999UserApi/CreateGraduateTag', params, 'post', 'form')
    },
    interestTagsDel (params) { // 删除应届生标签
      return fetch('/ch999UserApi/interestTagsDel', params, 'post', 'form')
    },
    graduateTags () {
      return fetch('/ch999UserApi/graduateTags', {}, 'get', 'form')
    },
    savePayBalance (params) {
      return org('/api/salary-balance', params, 'put', 'json')
    },
    getBalance (params) {
      return org(`/api/salary-balance`, params, 'get', 'form')
    },
    getLog (id) {
      return org(`/api/salary-balance/logs/${id}`, '', 'get', 'json')
    },
    getUserTenure (id) {
      return org(`/api/appointment-tenure/getUserTenure?id=${id}`, '', 'get', 'json')
    },
    salaryCategory () {
      return fetch(`/commonApi/GetSalaryCategoryList`)
    },
    getCenter (params) {
      return org(`/api/salary-balance/getCenter`, params)
    },
    getUserAttachment (params) {
      return fetch('/ch999UserApi/GetUserAttachment', params, 'get')
    },
    userAttachmentSave (params) {
      return fetch('/ch999UserApi/UserAttachmentSave', params, 'post', 'json')
    },
    interviewBatchAdd (params) {
      return org('/api/ch999Interview/batchAdd/v1', params, 'post', 'json')
    },
    interviewBatchList (params) {
      return org('/api/ch999Interview/list/v1', params, 'get')
    },
    interviewBatchDelete (params) {
      return org('/api/ch999Interview/delete/v1', params, 'get')
    },
    getInventoryInfo (params) {
      return trainService('/api/inventoryResult/getInventoryInfo/v1', params)
    },
    saveOrUpdateInventoryScore (params) {
      return trainService('/api/inventoryResult/saveOrUpdateInventoryScore/v1', params, 'post', 'json')
    },
    removeInventoryScore (params) {
      return trainService('/api/inventoryResult/removeInventoryScore/v1', params)
    },
    getUserAttachmentTypeList () {
      return fetch('/ch999UserApi/GetUserAttachmentTypeList')
    },
    inventoryResultGetEnums () {
      return trainService('/api/inventoryResult/getEnums/v1')
    },
    departPerformanceGetPage (params) {
      return org('/api/departInfo/departPerformanceGetPage/v1', params, 'post', 'json')
    },
    deleteDepartPerformanceById (params) {
      return org('/api/departInfo/deleteDepartPerformanceById/v1', params)
    },
    getLogByLogType (params) {
      return org('/api/workLog/getLogByLogType/v1', params, 'post', 'json')
    },
    exportDepartPerformanceTemplate () {
      return new Promise((resolve, reject) => {
        axios({
          method: 'post',
          timeout: 60 * 1000,
          url: `/cloudapi_nc/org_service/api/departInfo/exportDepartPerformanceTemplate/v1?xservicename=oa-org`,
          responseType: 'blob',
          headers: {
            Authorization: storeState.state.token
          }
        }).then((res) => {
          resolve(res)
        }).catch((err) => {
          reject(err)
        })
      })
    }
  },
  wiki: {
    WikiQuestionList (params) { // 个人信息修改确认接口
      return fetch('/docapi/DocumentOp/WikiQuestionList', params, 'post', 'json')
    },
    AddWikiQuestion (params) { // 添加问题
      return fetch('/docapi/DocumentOp/AddWikiQuestion', params, 'post', 'json')
    },
    AddWikiAnswer (params) { // 添加问题
      return fetch('/docapi/DocumentOp/AddWikiAnswer', params, 'post', 'json')
    }
  },
  docList: {
    getDocTree () {
      return fetch('/docapi/DocumentOp/DocTree')
    },
    getDocumentNum () {
      return fetch('/docapi/DocumentOp/DocumentCount') // 默认是get，第三个参数可以不传
    },
    getDocumentList (params) {
      return fetch('/docapi/DocumentOp/DocumnetList', params, 'post', 'json')
    },
    collectDocument (params) {
      return fetch('/docapi/DocumentOp/Collected', params)
    },
    getDocumentManageList (params) {
      return fetch('/docapi/DocumentOp/DocumnetsMgr', params, 'post', 'json')
    },
    stopDocument (params) {
      return fetch('/docapi/DocumentOp/EnableDoc', params)
    },
    stopDocuments (params) {
      return fetch('/docapi/DocumentOp/EnableDocs', params)
    },
    getDpt () {
      return fetch('/docapi/DocumentOp/getDepartment')
    },
    delDoc (params) {
      return fetch('/docapi/DocumentOp/DelDoc', params)
    },
    setSort (params) {
      return fetch('/docapi/DocumentOp/SetSort', params)
    },
    getDocCategroy (params) {
      return fetch('/docapi/DocumentOp/getDocCategroy', params)
    },
    getDocCategroyTree () {
      return fetch('/docapi/DocumentOp/getDocCategroyTree')
    },
    editDocCategroy (params) {
      return fetch('/docapi/DocumentOp/EditDocCategroy', params, 'post')
    },
    delDocCategroy (id) {
      return fetch('/docapi/DocumentOp/DelDocCategroy?id=' + id, {}, 'post')
    },
    getDocumentReviewList (params) {
      return fetch('/docapi/DocumentOp/DocumentReviewList', params, 'post')
    },
    getDocumentReview (params) {
      return fetch('/docapi/DocumentOp/DocumentReview', params, 'post', 'json')
    },
    getDocumentLogView (params) {
      return fetch('/docapi/DocumentOp/GetDocumentLogView', params)
    },
    getHistoryList (params) {
      return fetch('/api/DocumentOp/GetHistoryList', params, 'get', 'json')
    }
  },
  docDetail: {
    getDocumentDetail (params) {
      return fetch('/docapi/DocumentOp/Get', params)
    },
    goodDocument (params) {
      return fetch('/docapi/DocumentOp/GetGood', params)
    },
    badDocument (params) {
      return fetch('/docapi/DocumentOp/GetBad', params)
    },
    sendComment (params) {
      return fetch('/docapi/Comment/AddComment', params, 'post', 'json')
    },
    sendReply (params) {
      return fetch('/docapi/Comment/sendReply', params, 'post', 'json')
    },
    getCommentList (params) {
      return fetch('/docapi/Comment/CommentList', params)
    },
    getReplyList (params) {
      return fetch('/docapi/Comment/ReplayList', params)
    },
    readVideoDone (id) {
      return fetch('/docapi/DocumentOp/ReadVideoDone?id=' + id)
    },
    quicklyUpdate (params) {
      return fetch('/docapi/DocumentOp/UpdateDocFile', params, 'post', 'json')
    },
    checkFileName (params) {
      return fetch('/docapi/DocumentOp/checkFileName', params, 'post', 'form')
    },
    uploadFile (params) {
      return fetch('/docapi/DocumentOp/UploadFile', params, 'post', 'file')
    }
  },
  docNewAndEdit: {
    getDpt (params) {
      return fetch('/docapi/DocumentOp/getDepartAndRoleInfo', params)
    },
    getPerson (params) {
      return fetch('/docapi/DocumentOp/getPerson', params)
    },
    submitDocument (params) {
      let url = '/docapi/DocumentOp/AddDoc'
      if (params.documentId) {
        url = '/docapi/DocumentOp/SaveDoc'
      }
      return fetch(url, params, 'post', 'json')
    },
    getEditInfo (params) {
      return fetch('/docapi/DocumentOp/EditInfo', params)
    },
    getPath () {
      return fetch('/docapi/DocumentOp/getDocPath')
    },
    getDefaultCenter () {
      return fetch('/docapi/DocumentOp/getDefaultCenter')
    },
    addNewFolder (params = { pid: '', name: '', centerCode: '' }) {
      // 新建文件夹,返回数据data: {id: '123'}
      return fetch('/docapi/Dir/CreateDir', params)
    },
    folderRename (params = { id: '', name: '', centerCode: '' }) {
      // 重命名文件夹
      return fetch('/docapi/Dir/Rename', params)
    },
    deleteDir (params = { id: '' }) {
      return fetch('docapi/Dir/DelDir', params)
    },
    getCatalog (params = { centerCode: '' }) {
      // 获取文件目录结构，结构同下方部门ztree
      return fetch('/docapi/Dir/GetCenterDirs', params)
    },
    GetReadStatus (params = { documentId: '', isreaded: true, page: 1 }) {
      return fetch('/docapi/DocumentOp/GetReadStatus', params)
    },
    GetFrontRoles () { // 获取前端角色
      return ncSegments('/api/document/getFrontRole')
    },
    GetCategoryList (documentType = 5) { // 获取百科分类
      return fetch('/docapi/DocumentOp/getCategoryList', { documentType })
    },
    GetAllRoles () { // 获取SOP
      return fetch('/docapi/DocumentOp/getAllRoles')
    }
  },
  study: { // 学习积分
    startRead (params) { // 标记文章开始阅读接口（进到页面后先调取该接口）
      return fetch('/commonApi/startRead', params)
    },
    commitLearningTime (params) { // 阅读时长标记接口
      return fetch('/commonApi/commitLearningTime', params, 'post')
    },
    commitLearningComment (params) { // 评论计分接口
      return fetch('/commonApi/commitLearningComment', params, 'post')
    }
  },
  print: {
    getTemplateType (params) {
      return fetch('/commonApi/GetTicketTemplateType', params)
    },
    getList (params) {
      return fetch('/commonApi/QueryTicketTemplates', params)
    },
    getDetail (id) {
      return fetch('/commonApi/GetTicketTemplate', { templateId: id })
    },
    save (params) {
      return fetch('/commonApi/AddOrUpdateTicketTemplates', params, 'post', 'json')
    },
    deleteItem (id = 0) {
      return fetch('/commonApi/DeleteTicketTemplates', { templateId: id }, 'post')
    },
    getMaterial (type = 0) {
      return fetch('/commonApi/GetTicketItems', { type })
    },
    getDistributionById (id = 0) {
      return fetch('/commonApi/GetDistributionOrderDetail', { subId: id })
    }
  },
  help: {
    getList (params) {
      return fetch('/HelpCenter/Documents', { PageSize: params.size, PageIndex: params.current })
    },
    deleteItem (id = 0) {
      return fetch('/HelpCenter/Delete', { Id: id }, 'post')
    },
    getDetail (id) {
      return fetch('/HelpCenter/Document', { Id: id, t: new Date().getTime() })
    },
    getDetailByPath (path) {
      return org('/api/common/getTableFieldHelp', { url: path }, 'get')
    },
    save (params) {
      let url = '/HelpCenter/Add'
      if (params.Id) {
        url = '/HelpCenter/Edit'
      }
      return fetch(url, params, 'post', 'json')
    },
    getAnnotationDetail (params) {
      return fetch('/HelpCenter/GetFieldDocumentById', params)
    },
    updateAnnotationDetail (params) {
      return fetch('/HelpCenter/EditForField ', params, 'post', 'json')
    },
    ...help
  },
  product: {
    getProductList (params) { // 获取商品列表
      return orderservice('/api/product/label/page', params, 'post', 'json')
    },
    updatedState (params) { // 商品标签变更
      return orderservice('/api/product/label', params, 'post', 'json')
    },
    getProductLabelAttachemnts (id) { // 获取附件信息
      return orderservice(`/api/product/label/getProductLabelAttachemnts?productId=${id}`)
    },
    getStateHis (productId) { // 获取标签变化历史
      return orderservice('/api/product/label/' + productId)
    },
    getSmallTop (name) {
      return orderservice('/api/productinfo/small/top5', { name })
    },
    soldStatistics (params, isJiuji) {
      return orderservice(`/api/product/label/soldStatistics${isJiuji ? '/v2' : ''}`, params, 'post', 'json')
    },
    afterRepairChart (params, isJiuji) {
      return orderservice(`/api/product/label/afterSalesStatistics${isJiuji ? '/v2' : ''}`, params, 'post', 'json')
    },
    getEnums () {
      return orderservice('/api/product/label/getEnums')
    },
    addRemark (params) { // 添加备注
      return orderservice('/api/product/label/addRemark', params, 'post', 'json')
    },
    getDcArea (params) { // 排除大仓
      return orderservice('/api/product/label/getDcArea/v1', params, 'get', 'json')
    }
  },
  shortMessage: {
    getSmsSignReportList (params) { // 签名记录
      return fetch('/cloudapi_nc/extends/api/smsSignReport/search?xservicename=web-extends', params, 'post', 'json')
    },
    getSmsSignReportDetail (id) { // 签名详情
      return fetch('/cloudapi_nc/extends/api/smsSignReport/detail?xservicename=web-extends', { id }, 'post', 'json')
    },
    saveSmsSignReport (params) { // 编辑签名
      return fetch('/cloudapi_nc/extends/api/smsSignReport/edit?xservicename=web-extends', params, 'post', 'json')
    },
    getSmsSignlogsList (id) { // 签名日志
      return fetch('/cloudapi_nc/extends/api/smsSignReport/logs?xservicename=web-extends', { id }, 'post', 'json')
    },
    getIdCardAuth (fid) { // 根据身份证正面照识别信息
      return fetch('/cloudapi_nc/extends/api/smsSignReport/idCardAuth?xservicename=web-extends', { fid }, 'post', 'json')
    }
  },
  dict: {
    getEnums (type) {
      return ncSegments(`/api/dict/getEnums`, { type })
    },
    getDictByType (params) {
      return ncSegments(`/api/dict/type`, params, 'get', 'json')
    },
    // 新增
    saveItem (params) {
      return ncSegments('/api/dict/saveItem', params, 'post', 'json')
    },
    // 更新
    update (params) {
      return ncSegments('/api/dict/item', params, 'put', 'json')
    },
    // 删除
    delete (id) {
      return ncSegments(`/api/dict/item/${id}`, '', 'delete', 'json')
    },
    // 查询
    get (id) {
      return ncSegments(`/api/dict/item/${id}`, '', 'get', 'json')
    },
  },
  collect: {
    list (params) {
      return ncSegments('/api/userCollection/searchArticleNotice/v1', params, 'post', 'json')
    }
  },
  finance,
  store,
  ruleConfig,
  smallRefund,
  member,
  order,
  report,
  recovery,
  training,
  partner,
  stock,
  edition,
  smallCheck,
  globalConfig,
  account
}
