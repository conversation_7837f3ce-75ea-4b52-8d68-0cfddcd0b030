import fetch from './fetch'
import { org, fetchX } from './fetch-xservice'

// 会员相关接口

const member = {
  name: 'oa-ncSegments',
  prefix: '/cloudapi_nc/ncSegments'
}
function request (url = '', params = {}, method = 'get', contentType = 'json', headers = { xservicename: member.name }) {
  return fetch(member.prefix + url, params, method, contentType, headers)
}

export default {
  getTemplates (subType = '', subId = '') { // 发送短信获取短信模板
    return request(`/api/sms/smsTemplate/getSmsTemplates?subType=${subType}&subId=${subId}`)
  },
  getChannel () { // 获取发送渠道
    return request(`/api/sms/smsTemplate/getSmsChannelConfig`)
  },
  getChannelNew () { // 获取发送渠道新版
    return request(`/api/sms/smsTemplate/getSmsChannelConfig/v2`)
  },
  getPhones (id) { // 查询编号下的号码组
    return request(`/api/sms/smsTemplate/getSmsNumberMobileCount?id=${id}`)
  },
  getStoreInfo () { // 查询门店详情
    return fetch(`/cloudapi_nc/org_service/api/areainfo/listPosition?xservicename=oa-org`)
  },
  getInternalSmsNum (params) { // 根据电话号码查询站内信条数
    return fetch(`/cloudapi_nc/ncSegments/api/sms/smsSend/checkUsableUserId?xservicename=oa-ncSegments`, params, 'post', 'json')
  },
  saveSms (params) { // 保存短信
    return request(`/api/sms/smsSend/saveSmsSendContent`, params, 'post')
  },
  saveSmsNew (params) { // 保存短信新版
    return request(`/api/sms/smsSend/saveSmsSendContent/v2`, params, 'post')
  },
  getSmsRecords (params) {
    return request(`/api/sms/smsSend/record/page/v2`, params, 'post', 'json')
  },
  exportSmsRecords (params) {
    return request('/api/sms/smsSend/export/sms/record/v2', params, 'post', 'json')
  },
  /// ncSegments/api/sms/smsSend/cost/record/v1'
  costSmsRecords (params) {
    return request('/api/sms/smsSend/cost/record/v1', params, 'post', 'json')
  },
  downloadSmsRecords (params) {
    return request('/api/sms/smsSend/download/sms/record/v2', params, 'post', 'json')
  },
  getShortLink (params) { // 获取短链
    return request(`/api/sms/smsTemplate/getShortUrl`, params)
  },
  getShortLinkByOrder (params) { // 通过订单号和userId生成短链
    return org('/api/shorturl/', params)
  },
  getSmsBalance () { // 获取短信剩余条数
    return fetch('/cloudapi_nc/ncSegments/api/sms/smsSend/query/tenant/balance/v1?xservicename=oa-ncSegments')
  },
  getLastExcludePhone (params) { // 获取上次排除号码
    return fetch('/cloudapi_nc/ncSegments/api/sms/smsSend/queryLastExcludePhone/v1?xservicename=oa-ncSegments', params, 'get')
  },
  getSmsSendPhone (params) { // 获取接收号码数量
    return fetch('/cloudapi_nc/ncSegments/api/sms/smsSend/checkSendPhone/v1?xservicename=oa-ncSegments', params, 'post', 'json')
  },
  getFilms (params) { // 我的贴膜
    return fetch(`/cloudapi_nc/oaApi/api/order/getSubFilmList/v3`, params)
  },
  getCounts (params) { // 我的贴膜--获取同城、所有量
    return fetch(`/app/queryXianhuoCount`, params)
  },
  GetTiemoService (ppid) { // 我的贴膜--购买年包获取服务信息
    return fetch(`/tiemoCard/GetTiemoService?ganghuamoPppid=${ppid}&_=${new Date().getTime()}`)
  },
  addOrderTiemo (params) { // 我的贴膜--购买年包
    return fetch('/addOrder/tiemoServices', params, 'post', 'json')
  },
  getProductRec (params) { // 我的贴膜-获取半价复购商品
    return fetch(`/cloudapi_nc/web/api/products/halfPriceRepurchase/getProductRec/v1`, params)
  },
  submitFilmSub (params) { // 我的贴膜-半价复购
    return fetch(`/app/submitFilmSub`, params)
  },
  getReplace (params) { // 我的贴膜--已更换贴膜
    return fetch(`/cloudapi_nc/afterservice/api/patchSearch/patchSearch/getList?xservicename=oa-afterservice`, params, 'post', 'json')
  },
  getPatch (params) { // 我的贴膜--已更换贴膜
    return fetch(`/cloudapi_nc/afterservice/api/patchSearch/patchSearch/getPatch?xservicename=oa-afterservice`, params, 'post', 'json')
  },
  getAddOrderCode (staff, area) {
    return fetch(`/cloudapi_nc/extends/api/weixin/createStaffOrderCode?xservicename=web-extends&staff=${staff}`, {}, 'post', 'form', { area })
  },
  waitNumber (userId, area) {
    return request('/waitNumber/wait/v1', { userId, area }, 'post', 'json')
  },
  scanSet (params) {
    return fetch('/commonApi/memberScanSet', params)
  },
  SubmitOrderByUserFilm (params) {
    return fetch('/commonApi/SubmitOrderByUserFilm', params, 'get', 'form')
  },
  crmVisitTaskList (params) { // 回访管理-回访任务列表
    return fetch('/cloudapi_nc/org_service/api/crmVisitTask/searchPage/v1?xservicename=oa-org', params, 'post', 'json')
  },
  crmVisitTaskEdit (params) { // 回访管理-回访任务列表编辑
    return fetch('/cloudapi_nc/org_service/api/crmVisitTask/edit/v1?xservicename=oa-org', params, 'post', 'json')
  },
  crmVisitTaskDetail (params) {
    return fetch('/cloudapi_nc/org_service/api/crmVisitTask/detail/v1?xservicename=oa-org', params)
  },
  cluesList () { // 回访管理-线索来源列表
    return fetch('/cloudapi_nc/org_service/api/crmSourceConf/listAll/v1?xservicename=oa-org')
  },
  deleteTask (id) {
    return fetch(`/cloudapi_nc/org_service/api/crmVisitTask/delete/v1?xservicename=oa-org`, { id }, 'post', 'form')
  },
  cluesSave (params) { // 回访管理-线索来源保存
    return fetch('/cloudapi_nc/org_service/api/crmSourceConf/editBatch/v1?xservicename=oa-org', params, 'post', 'json')
  },
  crmLabeList (params) { // 回访管理-意向标签列表
    return fetch('/cloudapi_nc/org_service/api/crmLabelConf/listByLevel/v1?xservicename=oa-org', params)
  },
  editBatch (params) { // 回访管理-意向标签批量保存
    return fetch('/cloudapi_nc/org_service/api/crmLabelConf/editBatch/v1?xservicename=oa-org', params, 'post', 'json')
  },
  crmLabeTypeList (params) { // 回访管理-意向标签类型列表
    return fetch('/cloudapi_nc/org_service/api/crmLevelConf/listAll/v1?xservicename=oa-org', params)
  },
  editLabeTypeList (params) { // 回访管理-意向标签类型批量保存
    return fetch('/cloudapi_nc/org_service/api/crmLevelConf/editBatch/v1?xservicename=oa-org', params, 'post', 'json')
  },
  batchChangeInvalid (params) { // 回访任务列表线索转无效
    return fetch('/cloudapi_nc/org_service/api/crmVisitTask/shop/batchChangeInvalid/v1?xservicename=oa-org', params, 'post', 'json')
  },
  crmScriptList (params) { // 回访管理-话术列表
    return fetch('/cloudapi_nc/org_service/api/crmScriptConf/searchPage/v1?xservicename=oa-org', params)
  },
  crmScriptEdit (params) { // 回访管理-话术编辑
    return fetch('/cloudapi_nc/org_service/api/crmScriptConf/edit/v1?xservicename=oa-org', params, 'post', 'json')
  },
  crmScriptDetail (params) { // 回访管理-话术详情
    return fetch('/cloudapi_nc/org_service/api/crmScriptConf/detail/v1?xservicename=oa-org', params)
  },
  crmScriptDelete (params) { // 回访管理-话术删除
    return fetch('/cloudapi_nc/org_service/api/crmScriptConf/delete/v1?xservicename=oa-org', params, 'post', 'form')
  },
  crmShopList (params) {
    return fetch('/cloudapi_nc/org_service/api/crmVisitTask/shop/searchPage/v1?xservicename=oa-org', params, 'post', 'json')
  },
  crmShopDetailList (params) {
    return fetch('/cloudapi_nc/org_service/api/crmVisitTask/shopDetail/searchPage/v1?xservicename=oa-org', params, 'post', 'json')
  },
  exportStatistics (params) {
    return fetch('/cloudapi_nc/org_service/api/crmVisitTask/shopDetail/export/v1?xservicename=oa-org', params, 'post', 'json')
  },
  crmImportUserInfo (params) { // 导入用户
    return fetch('/cloudapi_nc/org_service/api/crmVisitTask/importUserInfo/v1?xservicename=oa-org', params, 'post', 'file')
  },
  crmImportProgress (params) { // 导入用户进度
    return fetch('/cloudapi_nc/org_service/api/crmVisitTask/getImportResult/v1?xservicename=oa-org', params)
  },
  crmShopDetailStatistical (params) { // 数据统计
    return fetch('/cloudapi_nc/org_service/api/crmVisitTask/shopDetail/total/v1?xservicename=oa-org', params)
  },
  // 回访详情
  crmShopDetail (params) {
    return fetch('/cloudapi_nc/org_service/api/crmVisitTask/shopDetail/recordList/v1?xservicename=oa-org', params)
  },
  batchChangeShop (params) { // 更换任务门店
    return fetch('/cloudapi_nc/org_service/api/crmVisitTask/shopDetail/batchChangeShop/v1?xservicename=oa-org', params, 'post', 'json')
  },
  /**
   * @typeof {Object} Params
   * @property {number} status 状态，0-待交接，1-已交接
   * @property {string} dimissionTimeStart 离职时间范围（开始）
   * @property {string} dimissionTimeEnd 离职时间范围（结束）
   * @property {number[]} shopIdList 离职人员部门
   * @property {string} staffInfo 离职人员工号/姓名
   * @property {string} takeoverTimeStart 交接完成时间范围（开始）
   * @property {string} takeoverTimeEnd 交接完成时间范围（结束）
   * @property {number} current 当前分页页码
   * @property {number} size 分页条数
   *
   * 获取离职企微客户交接列表
   * @param {Params} params
   */
  getEnterpriseWechatHandOverList (params) {
    return org('/wechat/corpDimissionRecord/searchPage', params, 'post', 'json')
  },
  /**
   * 获取离职企微客户交接数据更新时间
   */
  getEnterpriseWechatHandOverDataUpdateTime () {
    return org('/wechat/corpDimissionRecord/getLastUpdateTime', 'get')
  },
  /**
   * 手动更新离职企微客户交接数据
   */
  updateEnterpriseWechatHandOverData () {
    return org('/wechat/corpDimissionRecord/updateData', {}, 'post')
  },
  /**
   * @typeof {Object} Params
   * @property {number} status 状态，0-待交接，1-已交接
   * @property {string} handoverUserid 离职人员企微id
   * @property {string} userClass	用户等级
   * @property {string} staffInfo 离职人员工号/姓名
   * @property {string} mobile 用户电话
   * @property {number} current 当前分页页码
   * @property {number} size 分页条数
   *
   * 获取离职企微客户交接明细
   * @param {Params} params
   */
  getEnterpriseWechatHandOverDetail (params) {
    return org('/wechat/corpDimissionRecord/detail/searchPage', params, 'post', 'json')
  },
  /**
   * @typeof {Object} Params
   * @property {string} handoverUserid 离职人员企微id
   * @property {string[]} externalUseridList 分配用户的企业微信id
   * @property {string} distributionStaffId 分配的员工id
   *
   * 交接企业微信客户
   * @param {Params} params
   */
  handOverEnterpriseWechat (params) {
    return org('/wechat/corpDimissionRecord/distribution', params, 'post', 'json')
  },
  /**
   * @typeof {Object} BatchDataList
   * @property {string} distributionStaffId 分配人员id
   * @property {number} num 分配数量
   *
   * @typeof {Object} Params
   * @property {string} handoverUserid 离职人员企微id
   * @property {BatchDataList[]} batchDataList 分配明细
   *
   * 批量交接企业微信客户
   * @param {Params} params
   */
  batchHandOverEnterpriseWechat (params) {
    return org('/wechat/corpDimissionRecord/batch/distribution', params, 'post', 'json')
  }
}
