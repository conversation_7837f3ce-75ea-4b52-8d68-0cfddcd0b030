<template>
  <a-modal
    title="企业微信删除员工确认"
    :destroyOnClose="true"
    :maskClosable="false"
    :centered="true"
    v-model="visible">
    <div>是否将员工同步从企业微信删除，删除后员工会自动从企业微信全员群及其他工作群退出，<span class="red">无法再参与群聊相关操作，删除操作不可恢复</span>，如需加入企业需重新邀请进入，<span class="red">请HRBP提醒城市经理和店长做好离职客户交接</span></div>
    <div class="sure-box" :class="checked ? 'sure-box-checked' : ''">
      <a-checkbox v-model="checked">我已知悉删除后不可恢复，确认删除</a-checkbox>
    </div>
    <div class="tip">（注：个人信息保存后才会生效）</div>
    <div slot="footer">
      <a-button @click="visible = false">仅暂停登录</a-button>
      <a-button :disabled="!checked" @click="sure" type="primary" class="ml-16">确认删除</a-button>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, defineProps, defineExpose, getCurrentInstance } from 'vue'

  const props = defineProps({
    ch999Id: {
      type: [String, Number],
    }
  })
  const { proxy } = getCurrentInstance()
  const visible = ref(false)
  const checked = ref(false)
  const sureDelete = ref(false)
  function open () {
    checked.value = false
    sureDelete.value = false
    visible.value = true
  }
  function sure () {
    sureDelete.value = true
    visible.value = false
  }

  async function toDeleteUser () {
    if (!sureDelete.value) return
    const res = proxy.$api.user.toDeleteUser({ ch999Id: props.ch999Id })
    if (res) {
      checked.value = false
      sureDelete.value = false
    }
  }

  defineExpose({
    open,
    toDeleteUser
  })
</script>

<style scoped lang="scss">
.red {
  color: #FF4949
}
.tip {
  margin-top: 16px;
  color: #FF4949;
  font-weight: 600;
  font-size: 15px;
}
.sure-box {
  margin-top: 16px;
  padding: 8px 10px;
  border: 1px solid #EBEBEB;
  border-radius: 4px;
}
:deep(.sure-box-checked) {
  border: 1px solid #1890ff;
  span {
    color: #1890ff;
  }
}
</style>
