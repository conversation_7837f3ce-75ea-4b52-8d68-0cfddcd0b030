<template>
  <div class="base-info" v-if="baseInfo.userInfo">
    <div ref="tabs" :class="['flex flex-wrap tabs-list-com',isSticky ? 'box-shadow' : '']">
      <div class="tabs-list-item flex-child-noshrink" :class="tabsActiveIndex === item.value ? 'tabs-list-item-active' : ''" v-for="item in (canWatchVerification  ? tabsArr : tabsArr.filter(item => item.value !== 7))" @click="tabsChange(item)">{{ item.label }}</div>
    </div>
    <!--在职信息-->
    <div class="list-wrap" id="tabs1Wrap">
      <div class="flex flex-align-center list-wrap-top" @click="tabs1Show = !tabs1Show">
        <img class="left-ico" src="../../../assets/images/userInfo/incumbencyIco.png" alt="">
        <span class="font-16 mr-10 bold">在职信息</span>
        <img src="../../../assets/images/userInfo/down.png" :class="tabs1Show ? 'spanRotate' : 'spanReset'" width="14" height="14" alt="">
      </div>
      <div class="list-wrap-con" v-if="tabs1Show">
        <a-row>
          <div class="flex flex-wrap">
            <a-col :span="5" class="aRowSpacing mb-24">
              <div class="flex flex-align-center">
                <div class="label-title">工号：</div>
                <a-input disabled v-model="baseInfo.userInfo.ch999_id"/>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24">
                <div class="flex flex-align-center">
                  <div class="label-title">姓名：</div>
                  <a-input disabled v-model="baseInfo.userInfo.ch999_name"/>
                </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24">
              <div class="flex flex-align-center">
                <div class="label-title">所属地区：</div>
                <div class="flex-child-average required">
                  <ni-area-select
                    showSearch
                    :disabled="!rank.isAdmin && !rank.isCheck"
                    v-model="baseInfo.userInfo.area1id"
                    :allow-clear="true"
                    class="full-width"
                    placeholder="请选择地区"
                  />
                </div>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24">
              <div class="flex flex-align-center">
                <div class="label-title">部门：</div>
                <div class="flex-child-average required">
<!--                  <ni-depart-select-->
<!--                    v-model="baseInfo.userInfo.departCode"-->
<!--                    placeholder="请选择部门"-->
<!--                    @change="changeDepart"-->
<!--                    class="full-width"/>-->
                  <a-tree-select
                    class="full-width"
                    v-if="department.length > 0"
                    :treeData="department"
                    showSearch
                    :disabled="!rank.isAdmin && !rank.isCheck"
                    treeNodeFilterProp="label"
                    :treeDefaultExpandedKeys="[baseInfo.userInfo.departCode]"
                    :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                    v-model="baseInfo.userInfo.departCode"
                    placeholder="请选择"
                  >
                  </a-tree-select>
                </div>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24">
              <div class="flex flex-align-center">
                <div class="label-title">职务：</div>
                <a-select class="full-width" placeholder="请选择" v-model="baseInfo.userInfo.zhiwuid" allowClear :disabled="!rank.isAdmin">
                  <a-select-opt-group v-for="(zw, i) in info.zhiwuList" :label="zw.name" :key="i">
                    <a-select-option :value="z.id" v-for="z in zw.options" :key="z.id">{{ z.name }}</a-select-option>
                  </a-select-opt-group>
                </a-select>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24">
              <div class="flex flex-align-center">
                <div class="label-title">职级：</div>
                <a-cascader
                  class="full-width"
                  :disabled="!rank.isAdmin"
                  v-model="zhijiCur"
                  :options="zhijiData"
                  :fieldNames="{
                      label: 'label',
                      value: 'id',
                      children: 'children'
                    }"
                  placeholder="请选择"
                  @change="getZhiji"
                >
                </a-cascader>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24">
              <div class="flex flex-align-center">
                <div class="label-title">入职时间：</div>
                <a-date-picker
                  :disabled="!rank.isAdmin"
                  :value="baseInfo?.userInfo?.indate ? moment(baseInfo.userInfo.indate, dateFormat) : null"
                  @change="inDateChange"
                  class="full-width required"
                ></a-date-picker>
<!--                <a-date-picker @change="inDateChange" :disabled="!rank.isAdmin" v-else class="full-width"></a-date-picker>-->
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24">
              <div class="flex flex-align-center">
                <div class="label-title">工龄：</div>
                <a-input disabled v-model="baseInfo.userInfo.workYears"/>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24">
              <div class="flex flex-align-center">
                <div class="label-title">转正时间：</div>
                <a-date-picker
                  :value="baseInfo?.userInfo?.zhuanzhendate ? moment(baseInfo.userInfo.zhuanzhendate, dateFormat) : null"
                  @change="zhuanzhendateChange"
                  :disabled="!rank.isAdmin"
                  class="full-width"
                ></a-date-picker>
<!--                <a-date-picker-->
<!--                  @change="zhuanzhendateChange"-->
<!--                  :disabled="!rank.isAdmin"-->
<!--                  v-else-->
<!--                  class="full-width"-->
<!--                ></a-date-picker>-->
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24">
              <div class="flex flex-align-center">
                <div class="label-title">在职状态：</div>
                <a-select class="full-width" :disabled="!rank.isAdmin" v-model="baseInfo.userInfo.iszaizhi">
                  <a-select-option value="true">在职</a-select-option>
                  <a-select-option value="false">离职</a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24" v-if="baseInfo.userInfo.iszaizhi === 'false'">
              <div class="flex flex-align-center">
                <div class="label-title">离职时间：</div>
                <a-date-picker
                  v-if="baseInfo.userInfo.offtime"
                  :disabled="!rank.isAdmin"
                  :value="moment(baseInfo.userInfo.offtime, dateFormat)"
                  @change="offtimeChange"
                  class="full-width"
                ></a-date-picker>
                <a-date-picker @change="offtimeChange" :disabled="!rank.isAdmin" v-else class="full-width"></a-date-picker>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24" v-if="baseInfo.userInfo.iszaizhi === 'false'">
              <div class="flex flex-align-center">
                <div class="label-title">离职标签：</div>
                <a-select
                  v-model="baseInfo.userInfo.QuitTag"
                  :disabled="!rank.isAdmin"
                  class="full-width"
                  allowClear
                  :options="dimissionOptions"
                  placeholder="请选择"/>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24">
              <div class="flex flex-align-center">
                <div :span="6" class="label-title">岗位：</div>
                <div class="flex-child-average required">
                  <a-select
                    class="full-width"
                    v-model="baseInfo.userInfo.mainRole"
                    showSearch
                    :disabled="!rank.isAdmin && !rank.isCheck"
                    :filterOption="filterOption"
                    optionFilterProp="children"
                    allowClear
                    placeholder="请选择岗位"
                  >
                    <a-select-option
                      :value="r.id"
                      v-for="r in info.PostRoleList"
                      :key="r.id"
                    >{{ r.name }}</a-select-option
                    >
                  </a-select>
                </div>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24">
              <div class="flex flex-align-center">
                <div :span="6" class="label-title">岗位类型：</div>
                <div class="flex-child-average required">
                  <a-select
                    class="full-width"
                    v-model="baseInfo.userInfo.positionType"
                    showSearch
                    :disabled="!rank.isAdmin && !rank.isCheck"
                    :filterOption="filterOption"
                    optionFilterProp="children"
                    allowClear
                    placeholder="请选择岗位类型"
                  >
                    <a-select-option
                      :value="r.id"
                      v-for="r in positionTypeOptions"
                      :key="r.id"
                    >{{ r.name }}</a-select-option
                    >
                  </a-select>
                </div>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24">
              <div class="flex flex-align-center">
                <div class="label-title">{{baseInfo.ch999_userEx.postUserType}}：</div>
                <ni-staff-select
                  :disabled="!rank.isAdmin && !rank.isCheck"
                  v-model="baseInfo.ch999_userEx.postUserId"
                  style="width:100%"
                  show-type="name"
                  placeholder="请选择继任人"
                />
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24">
              <div class="flex flex-align-center">
                <div class="label-title">二次入职标签：</div>
                <a-select :disabled="!rank.isAdmin && !rank.isCheck" v-model="baseInfo.userInfo.second_entry_flag" placeholder="请选择" class="full-width" allowClear>
                  <a-select-option v-for="item in baseInfo.secondEntryList" :value="item.Value">{{ item.Key }}</a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24" v-if="rank.isSensitive">
              <div class="flex flex-align-center">
                <div class="label-title">劳动合同到期：</div>
                <a-date-picker
                  class="full-width"
                  :disabled="!rank.isAdmin"
                  v-if="baseInfo.userInfo.workhtdate"
                  :value="moment(baseInfo.userInfo.workhtdate, dateFormat)"
                  @change="workhtdateChange"
                ></a-date-picker>
                <a-date-picker
                  class="full-width"
                  :disabled="!rank.isAdmin"
                  @change="workhtdateChange"
                  v-else
                ></a-date-picker>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24">
              <div class="flex flex-align-center">
                <div class="label-title">入职渠道：</div>
                <a-input
                  class="full-width"
                  :disabled="!rank.isAdmin"
                  placeholder="入职渠道"
                  v-model="baseInfo.userInfo.EntryChannel"
                ></a-input>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24">
              <div class="flex flex-align-center">
                <div class="label-title">业务带培人：</div>
                <ni-staff-select
                  v-model="baseInfo.userInfo.businessTrainCh999Id"
                  class="full-width"
                  show-type="name"
                  :disabled="!rank.isAdmin && !rank.isCheck"
                  placeholder="请选择业务带培人"
                  @change="businessChange"
                />
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing mb-24">
              <div class="flex flex-align-center">
                <div class="label-title">文化带培人：</div>
                <ni-staff-select
                  v-model="baseInfo.userInfo.cultureTrainCh999Id"
                  class="full-width"
                  show-type="name"
                  :disabled="!rank.isAdmin && !rank.isCheck"
                  placeholder="请选择文化带培人"
                  @change="cultureChange"
                />
              </div>
            </a-col>
          </div>
          <div class="flex">
            <div style="width: 43.5%;" class="flex flex-align-center aRowSpacing">
              <div class="label-title">员工标签：</div>
              <freshman-tags :disabled="!rank.isAdmin" :selectGraduateTag="baseInfo.graduateTags || []" />
            </div>
            <div style="width: 43.5%;" class="flex flex-align-center aRowSpacing">
              <div class="label-title">主要职责：</div>
              <a-input class="flex-child-average" :disabled="!rank.isAdmin" v-model="baseInfo.userInfo.WorkKeys"></a-input>
            </div>
          </div>
        </a-row>
      </div>
    </div>
    <!--个人信息-->
    <div class="list-wrap" id="tabs2Wrap">
      <div class="flex flex-align-center list-wrap-top" @click="tabs2Show = !tabs2Show">
        <img class="left-ico" src="../../../assets/images/userInfo/personalInformation.png" alt="">
        <span class="font-16 mr-10 bold">个人信息</span>
        <img src="../../../assets/images/userInfo/down.png" :class="tabs2Show ? 'spanRotate' : 'spanReset'" width="14" height="14" alt="">
      </div>
      <div class="list-wrap-con" v-if="tabs2Show">
        <a-row>
          <div class="flex mb-24">
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">手机号：</div>
                <div class="flex-child-average required">
                  <a-input v-model="baseInfo.userInfo.mobile" :disabled="!rank.isAdmin && !rank.isCheck" class="full-width"></a-input>
                </div>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">身份证号：</div>
                <a-input v-model="baseInfo.userInfo.IDnumber" :disabled="!rank.isAdmin" class="full-width"></a-input>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">身份证到期时间：</div>
                <a-date-picker :disabled="!rank.isAdmin && !rank.isCheck" :value="baseInfo.userInfo.IDEtime ? moment(baseInfo.userInfo.IDEtime, dateFormat) : undefined" @change="idEtimeChange" class="full-width"></a-date-picker>
              </div>
            </a-col>
            <a-col :span="5">
              <div class="flex flex-align-center">
                <div class="label-title">学历：</div>
                <a-select
                  class="full-width"
                  v-model="baseInfo.userInfo.Education"
                  :disabled="!rank.isAdmin || educationDisabled"
                  placeholder="请选择"
                >
                  <template v-for="(item, i) in educationList">
                    <a-select-option :key="`${i}_KEY`" :value="item.value">{{item.label}}</a-select-option>
                  </template>
                </a-select>
              </div>
            </a-col>
          </div>
        </a-row>
        <a-row>
          <div class="flex mb-24">
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">性别：</div>
                <div class="flex-child-average required">
                  <a-select class="full-width" v-model="baseInfo.userInfo.usersex" :disabled="!rank.isAdmin && !rank.isCheck" placeholder="选择性别">
                    <a-select-option value="true">男</a-select-option>
                    <a-select-option value="false">女</a-select-option>
                  </a-select>
                </div>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">民族：</div>
                <div class="flex-child-average required">
                  <a-input :disabled="!rank.isAdmin && !rank.isCheck" v-model="baseInfo.userInfo.nation"></a-input>
                </div>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">政治面貌：</div>
<!--                <a-input :disabled="!rank.isAdmin" v-model="baseInfo.userInfo.zhengzhimianbao"></a-input>-->
                <a-select
                  :disabled="!rank.isAdmin"
                  class="full-width"
                  v-model="baseInfo.userInfo.zhengzhimianbao"
                  placeholder="请选择"
                  :options="[
                    {
                      label: '中共党员（含预备党员）',
                      value: '1'
                    },
                    {
                      label: '团员',
                      value: '2'
                    },
                    {
                      label: '群众',
                      value: '3'
                    },
                    {
                      label: '民主党派',
                      value: '4'
                    },
                    {
                      label: '无党派人士',
                      value: '5'
                    }
                  ]"
                ></a-select>
              </div>
            </a-col>
            <a-col :span="5">
              <div class="flex flex-align-center">
                <div class="label-title">生日：</div>
                <a-date-picker :disabled="!rank.isAdmin" v-if="baseInfo.userInfo.birsday" :value="moment(baseInfo.userInfo.birsday, dateFormat)" @change="birsdayChange" class="full-width"></a-date-picker>
                <a-date-picker :disabled="!rank.isAdmin" v-else @change="birsdayChange" class="full-width"></a-date-picker>
              </div>
            </a-col>
          </div>
        </a-row>
        <a-row>
          <div class="flex mb-24">
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">年龄：</div>
                <a-input class="full-width" v-model="userAge" disabled></a-input>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">婚姻状况：</div>
                <a-select class="full-width" :disabled="!rank.isAdmin" v-model="baseInfo.userInfo.isMarry" :options="marryOptions" placeholder="请选择"></a-select>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">身高：</div>
                <div class="flex-child-average">
                  <a-input type="number" style="width: 86%" :disabled="!rank.isAdmin" v-model="baseInfo.userInfo.height"></a-input>
                  <em class="font-12">cm</em>
                </div>
              </div>
            </a-col>
            <a-col :span="5">
              <div class="flex flex-align-center">
                <div class="label-title">体重：</div>
                <div class="flex-child-average">
                  <a-input type="number" style="width: 86%" :disabled="!rank.isAdmin" v-model="baseInfo.userInfo.weight"></a-input>
                  <em class="font-12">kg</em>
                </div>
              </div>
            </a-col>
          </div>
        </a-row>
        <a-row>
          <div class="flex mb-24">
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">外语水平：</div>
                <div class="flex-child-average flex flex-align-center">
                  <a-select class="full-width" placeholder="请选择" :disabled="!rank.isAdmin" v-model="baseInfo.ch999_userEx.englishLevel" :options="englishLeaveOptions"></a-select>
                </div>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">邮箱：</div>
                <a-input :disabled="!rank.isAdmin" v-model="baseInfo.userInfo.email" placeholder="请输入" @blur="emailChange"></a-input>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">籍贯地址：</div>
                <city-select
                  :changeOnSelect="false"
                  style="min-width: 220px;"
                  :cityIdIsAll="true"
                  :disabled="!rank.isAdmin && !rank.isCheck"
                  :cityId="baseInfo.ch999_userEx.nativeCityId && baseInfo.ch999_userEx.nativeCountyId && baseInfo.ch999_userEx.nativeProvinceId ? [baseInfo.ch999_userEx.nativeProvinceId,baseInfo.ch999_userEx.nativeCityId,baseInfo.ch999_userEx.nativeCountyId] : ''"
                  @change="jiguanCityidChange"
                ></city-select>
              </div>
            </a-col>
            <a-col :span="5">
              <div class="flex flex-align-center">
                <div class="label-title">户籍性质：</div>
                <a-select :disabled="!rank.isAdmin && !rank.isCheck" class="full-width" v-model="baseInfo.userInfo.registerNatur" placeholder="请选择" allowClear :options="registerNaturOptions"></a-select>
              </div>
            </a-col>
          </div>
        </a-row>
        <a-row>
          <div class="flex mb-24">
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">兴趣爱好：</div>
                <div class="flex-child-average flex flex-align-center">
                  <a-tree-select
                    class="flex-child-average"
                    allowClear
                    :disabled="!rank.isAdmin && !rank.isSelf"
                    :treeData="interestTags.list"
                    v-model="interestTags.selected"
                    :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                    treeCheckable
                    placeholder="请选择"
                    :maxTagCount="1"
                  >
                  </a-tree-select>
                  <a-button class="margin-left" @click="saveInterestTags" :loading="interestTagsBtnLoading" v-if="rank.isAdmin || rank.isSelf">保存</a-button>
                </div>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">健康状况：</div>
                <a-input :disabled="!rank.isAdmin" v-model="baseInfo.userInfo.healthCondition"></a-input>
              </div>
            </a-col>
            <a-col :span="5">
              <div class="flex flex-align-center">
                <div class="label-title">健康承诺及传染病史：</div>
                <a-input :disabled="!rank.isAdmin" class="f-input" placeholder="请输入" v-model="baseInfo.userInfo.medical_history"></a-input>
              </div>
            </a-col>
          </div>
        </a-row>
        <a-row>
          <div style="width: 43.5%;" class="flex flex-align-center aRowSpacing">
            <div class="label-title">住址：</div>
            <city-select
              style="min-width: 220px"
              :disabled="!rank.isAdmin && !rank.isCheck"
              :cityId="baseInfo.userInfo.dcityid"
              @change="dcityidChange"
            ></city-select>
            <a-input :disabled="!rank.isAdmin && !rank.isCheck" class="f-input" v-model="baseInfo.userInfo.address"></a-input>
          </div>
        </a-row>
      </div>
    </div>
    <!--家庭背景-->
    <div class="list-wrap" id="tabs13Wrap">
      <div class="flex flex-align-center list-wrap-top" @click="tabs13Show = !tabs13Show">
        <img class="left-ico" src="../../../assets/images/userInfo/homeIco.png" alt="">
        <span class="font-16 mr-10 bold">家庭背景</span>
        <img src="../../../assets/images/userInfo/down.png" :class="tabs13Show ? 'spanRotate' : 'spanReset'" width="14" height="14" alt="">
      </div>
      <div class="list-wrap-con" v-if="tabs13Show">
        <a-row class="mb-24">
          <a-col :span="10">
            <div class="flex flex-align-center">
              <div class="label-title">家庭联系人：</div>
              <a-input :disabled="!rank.isAdmin && !rank.isSelf" class="full-width" v-model="baseInfo.userInfo.jtname"></a-input>
              <em class="ml-5 red flex-child-noshrink">例：张XX（父亲）</em>
            </div>
          </a-col>
          <a-col :span="2"></a-col>
          <a-col :span="10">
            <div class="flex flex-align-center">
              <div class="label-title">家庭电话：</div>
              <a-input :disabled="!rank.isAdmin && !rank.isSelf" class="full-width" v-model="baseInfo.userInfo.hometel"></a-input>
              <em class="ml-5 red flex-child-noshrink">必须填写直系亲属联系方式</em>
            </div>
          </a-col>
        </a-row>
        <a-row class="mb-24">
          <a-col>
            <div class="flex flex-align-center">
              <div class="label-title">家庭详细地址：</div>
              <div class="flex-child-average flex flex-align-center">
                <city-select
                  :disabled="!rank.isAdmin && !rank.isSelf"
                  style="min-width: 220px"
                  :cityId="baseInfo.userInfo.cityid"
                  @change="cityidChange"
                ></city-select>
                <a-input
                  class="margin-left"
                  :disabled="!rank.isAdmin && !rank.isSelf"
                  style="width: 200px"
                  v-model="baseInfo.userInfo.homeaddress"
                  placeholder="街道"
                ></a-input>
                <a-input
                  class="margin-left"
                  style="width: 150px"
                  :disabled="!rank.isAdmin && !rank.isSelf"
                  v-model="baseInfo.userInfo.homeaddrqu"
                  placeholder="小区"
                ></a-input>
                <a-input
                  class="margin-left"
                  style="width: 150px"
                  :disabled="!rank.isAdmin && !rank.isSelf"
                  v-model="baseInfo.userInfo.homeaddrdoor"
                  placeholder="楼栋"
                ></a-input>
                <a-input
                  class="margin-left"
                  :disabled="!rank.isAdmin && !rank.isSelf"
                  style="width: 150px"
                  v-model="baseInfo.userInfo.homeaddrrom"
                  placeholder="门牌号"
                ></a-input>
                <em class="margin-left red">地址信息需要精确到门牌号 示例：XX市XX区/县XX街道XX小区XX栋XX单元XX门牌号</em>
              </div>
            </div>
          </a-col>
        </a-row>
        <!-- <a-row class="mb-24">
          <a-col :span="10">
            <div class="flex flex-align-center">
              <div class="label-title">详细住址：</div>
              <div class="flex-child-average flex">
                <city-select
                  style="min-width: 220px"
                  :cityId="baseInfo.userInfo.dcityid"
                  @change="dcityidChange"
                ></city-select>
                <a-input
                  class="full-width margin-left"
                  v-model="baseInfo.userInfo.address"
                  placeholder="详细住址"
                ></a-input>
              </div>
            </div>
          </a-col>
        </a-row> -->
        <a-row v-if="rank.isSelf || rank.isAdmin">
          <a-col :span="5">
            <div class="flex flex-align-center">
              <div class="label-title"></div>
              <span class="add-btn" @click="saveUserHomeInfo">
                <span>保存地址信息</span>
              </span>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>
<!--    家庭成员-->
    <div class="list-wrap" id="tabs15Wrap">
      <div class="flex flex-align-center list-wrap-top" @click="tabs15Show = !tabs15Show">
        <img class="left-ico" src="../../../assets/images/userInfo/homeIco.png" alt="">
        <span class="font-16 mr-10 bold">家庭成员</span>
        <img src="../../../assets/images/userInfo/down.png" :class="tabs15Show ? 'spanRotate' : 'spanReset'" width="14" height="14" alt="">
      </div>
      <div class="list-wrap-con" v-if="tabs15Show">
        <div v-if="familyList && familyList.length">
          <div class="urgentList" v-for="(item, index) in familyList">
            <div class="urgentList-index color_82 mb-10 flex flex-justify-between">
              <div>家庭成员{{ index + 1 }}</div>
            </div>
            <a-row>
              <div class="flex mb-24">
                <a-col :span="5" class="aRowSpacing">
                  <div class="flex flex-align-center">
                    <div class="label-title">姓名：</div>
                    <a-input :disabled="!rank.isAdmin" placeholder="请输入姓名" v-model="item.place"></a-input>
                  </div>
                </a-col>
                <a-col :span="5" class="aRowSpacing">
                  <div class="flex flex-align-center">
                    <div class="label-title">关系：</div>
                    <a-select :disabled="!rank.isAdmin" class="full-width" placeholder="请选择关系" v-model="item.major">
                      <a-select-option :value="r.Value + ''" v-for="r in info.familyRelationList" :key="r.Value">{{ r.Key }}</a-select-option>
                    </a-select>
                  </div>
                </a-col>
                <a-col :span="5">
                  <div class="flex flex-align-center">
                    <div class="label-title">联系方式：</div>
                    <a-input :disabled="!rank.isAdmin" class="full-width" placeholder="请输入联系方式" v-model="item.zhicheng"></a-input>
                  </div>
                </a-col>
                <a-col :span="5">
                  <div class="flex flex-align-center">
                    <div class="label-title">现工作单位及职务：</div>
                    <a-input :disabled="!rank.isAdmin" class="full-width" placeholder="请输入现工作单位及职务" v-model="item.mainDuty"></a-input>
                  </div>
                </a-col>
                <a-col :span="2" v-if="rank.isAdmin">
                  <div style="margin-left: 24px;" class="h-32 flex flex-align-center">
                    <div class="pointer red" @click="delFamily(index)">删除</div>
                  </div>
                </a-col>
              </div>
            </a-row>
          </div>
        </div>
        <a-empty v-else description="暂无家庭成员"></a-empty>
        <span class="add-btn" @click="addFamily" v-if="rank.isAdmin">
          <img src="../../../assets/images/userInfo/addIco.png" width="14" height="14" alt="" class="add-btn-img">
          <span>添加家庭成员</span>
        </span>
      </div>
    </div>
    <!--紧急联系人-->
    <div class="list-wrap" id="tabs3Wrap">
      <div class="flex flex-align-center list-wrap-top" @click="tabs3Show = !tabs3Show">
        <img class="left-ico" src="../../../assets/images/userInfo/emergencyContacts.png" alt="">
        <span class="font-16 mr-10 bold">紧急联系人</span>
        <img src="../../../assets/images/userInfo/down.png" :class="tabs3Show ? 'spanRotate' : 'spanReset'" width="14" height="14" alt="">
      </div>
      <div class="list-wrap-con" v-if="tabs3Show">
        <div v-if="emergencyContacts && emergencyContacts.length">
          <div class="urgentList" v-for="(item, index) in emergencyContacts">
            <div class="urgentList-index color_82 mb-10 flex flex-justify-between">
              <div>紧急联系人{{ index + 1 }}</div>
            </div>
            <a-row>
              <div class="flex mb-24">
                <a-col :span="5" class="aRowSpacing">
                  <div class="flex flex-align-center">
                    <div class="label-title">联系人姓名：</div>
                    <a-input :disabled="!rank.isAdmin" placeholder="请输入" v-model="item.place"></a-input>
                  </div>
                </a-col>
                <a-col :span="5" class="aRowSpacing">
                  <div class="flex flex-align-center">
                    <div class="label-title">与联系人关系：</div>
                    <a-select :disabled="!rank.isAdmin" class="full-width" placeholder="选择关系" v-model="item.major">
                      <a-select-option :value="r.Value + ''" v-for="r in info.relationshipList" :key="r.Value">{{ r.Key }}</a-select-option>
                    </a-select>
                  </div>
                </a-col>
                <a-col :span="5">
                  <div class="flex flex-align-center">
                    <div class="label-title">联系人手机号：</div>
                    <a-input :disabled="!rank.isAdmin" class="full-width" placeholder="请输入" v-model="item.zhicheng"></a-input>
                  </div>
                </a-col>
              </div>
            </a-row>
            <a-row>
              <div class="flex mb-24">
                <a-col style="width: 66%" class="aRowSpacing">
                  <div class="flex flex-align-center">
                    <div class="label-title">联系人住址：</div>
                    <city-select
                      style="min-width: 220px"
                      :disabled="!rank.isAdmin && !rank.isCheck"
                      :cityIdIsAll="true"
                      :cityId="item.provinceId && item.cityId && item.countyId ? [item.provinceId, item.cityId, item.countyId] : undefined"
                      @change="(val) => jinjiidChange(val, index)"
                      placeholder="请选择"
                    ></city-select>
                    <a-input :disabled="!rank.isAdmin && !rank.isCheck" class="f-input" v-model="item.mainDuty" placeholder="请输入"></a-input>
                  </div>
                </a-col>
                <a-col :span="2" v-if="rank.isAdmin">
                  <div class="h-32 flex flex-align-center">
                    <div class="pointer red" @click="delEmergency(index)">删除</div>
                  </div>
                </a-col>
              </div>
            </a-row>
          </div>
        </div>
        <a-empty v-else description="暂无紧急联系人"></a-empty>
        <span class="add-btn" @click="addEmergency" v-if="rank.isAdmin">
          <img src="../../../assets/images/userInfo/addIco.png" width="14" height="14" alt="" class="add-btn-img">
          <span>添加紧急联系人</span>
        </span>
      </div>
    </div>
    <!--教育经历-->
    <div class="list-wrap" id="tabs4Wrap">
      <div class="flex flex-align-center list-wrap-top" @click="tabs4Show = !tabs4Show">
        <img class="left-ico" src="../../../assets/images/userInfo/educationIco.png" alt="">
        <span class="font-16 mr-10 bold">教育经历</span>
        <img src="../../../assets/images/userInfo/down.png" :class="tabs4Show ? 'spanRotate' : 'spanReset'" width="14" height="14" alt="">
      </div>
      <div class="list-wrap-con" v-if="tabs4Show">
        <div v-for="(item, index) in educationTableData" :key="index">
          <a-row>
            <div class="flex mb-24">
              <a-col :span="5" class="aRowSpacing">
                <div class="flex flex-align-center">
                  <div class="label-title">学历：</div>
                  <a-select :disabled="!rank.isAdmin" class="full-width" v-model="item.Education" placeholder="请选择" @change="educationChange(item,index)">
                    <template v-for="(item, i) in educationList">
                      <a-select-option :key="`${i}_KEY`" :value="item.value">{{ item.label }}</a-select-option>
                    </template>
                  </a-select>
                </div>
              </a-col>
              <a-col :span="5" class="aRowSpacing">
                <div class="flex flex-align-center">
                  <div class="label-title">起止时间：</div>
                  <a-range-picker :disabled="!rank.isAdmin" :value="[item.stime ? moment(item.stime, dateFormat) : '', item.etime ? moment(item.etime, dateFormat) : '']" @change="(dates, dateStrings) => educationTime(dates, dateStrings, index,item)"/>
                </div>
              </a-col>
              <a-col :span="10">
                <div class="flex flex-align-center">
                  <div class="label-title">毕业学校：</div>
                  <a-input :disabled="!rank.isAdmin" allowClear v-model="item.place" placeholder="请输入"></a-input>
                </div>
              </a-col>
            </div>
          </a-row>
          <a-row>
            <div class="flex mb-24">
              <a-col :span="5" class="aRowSpacing">
                <div class="flex flex-align-center">
                  <div class="label-title">教学性质：</div>
                  <a-select :disabled="!rank.isAdmin" v-model="item.DegreeTeachingMethod" placeholder="请选择" class="full-width">
                    <a-select-option value="全日制">全日制</a-select-option>
                    <a-select-option value="非全日制">非全日制</a-select-option>
                    <a-select-option value="肄业">肄业</a-select-option>
                  </a-select>
                </div>
              </a-col>
              <a-col :span="5" class="aRowSpacing">
                <div class="flex flex-align-center">
                  <div class="label-title">院校性质：</div>
                  <a-select :disabled="!rank.isAdmin" class="full-width" placeholder="请选择" v-model="item.EducationType">
                    <template v-for="children in EducationTypeListOptions(item.Education)">
                      <a-select-option :key="children.value" :value="children.value">{{ children.label }}</a-select-option>
                    </template>
                  </a-select>
                </div>
              </a-col>
              <a-col :span="10" class="aRowSpacing">
                <div class="flex flex-align-center">
                  <div class="label-title">专业：</div>
                  <a-input :disabled="!rank.isAdmin" allowClear v-model="item.major" placeholder="请输入"></a-input>
                </div>
              </a-col>
<!--              <a-col :span="5" class="aRowSpacing">-->
<!--                <div class="flex flex-align-center">-->
<!--                  <div class="label-title">职称：</div>-->
<!--                  <a-input :disabled="!rank.isAdmin" allowClear v-model="item.zhicheng" placeholder="请输入"></a-input>-->
<!--                </div>-->
<!--              </a-col>-->
              <a-col :span="5" class="aRowSpacing">
                <div class="h-32 flex flex-align-center">
                  <div class="label-title">
                    <div class="pointer red text-left" @click="delEducation(index)">删除</div>
                  </div>
                </div>
              </a-col>
            </div>
          </a-row>
        </div>
        <span class="add-btn" @click="addEducation" v-if="rank.isAdmin">
        <img src="../../../assets/images/userInfo/addIco.png" width="14" height="14" alt="" class="add-btn-img">
        <span>添加教育经历</span>
      </span>
      </div>
    </div>
    <!--外部工作经历-->
    <div class="list-wrap" id="tabs10Wrap">
      <div class="flex flex-align-center list-wrap-top" @click="tabs10Show = !tabs10Show">
        <img class="left-ico" src="../../../assets/images/userInfo/externalExperience.png" alt="">
        <span class="font-16 mr-10 bold">外部工作经历</span>
        <img src="../../../assets/images/userInfo/down.png" :class="tabs10Show ? 'spanRotate' : 'spanReset'" width="14" height="14" alt="">
      </div>
      <div class="list-wrap-con" v-if="tabs10Show">
        <div v-if="workTableData && workTableData.length">
          <div v-for="(item, index) in workTableData" :key="index">
          <a-row class="mb-36">
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">起止时间：</div>
                <a-range-picker :disabled="!rank.isAdmin" :value="[item.stime ? moment(item.stime, dateFormat) : '', item.etime ? moment(item.etime, dateFormat) : '']" @change="(dates, dateStrings) => workTime(dates, dateStrings, index)"/>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">公司：</div>
                <a-input :disabled="!rank.isAdmin" placeholder="请输入" v-model="item.place"/>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">职位：</div>
                <a-input :disabled="!rank.isAdmin" placeholder="请输入" v-model="item.major"/>
              </div>
            </a-col>
          </a-row>
          <a-row class="mb-36">
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">职能：</div>
                <a-textarea autoSize :disabled="!rank.isAdmin" placeholder="请输入" v-model="item.mainDuty"/>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">证明人：</div>
                <a-input :disabled="!rank.isAdmin" placeholder="请输入" v-model="item.zhicheng"/>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">证明人电话：</div>
                <a-input :disabled="!rank.isAdmin" placeholder="请输入" v-model="item.DegreeTeachingMethod"/>
              </div>
            </a-col>
            <a-col :span="2" v-if="rank.isAdmin">
              <div class="h-32 flex flex-align-center">
                <div class="pointer red" @click="delWorkTableData(index)">删除</div>
              </div>
            </a-col>
          </a-row>
        </div>
        </div>
        <a-empty v-else description="外部工作经历"></a-empty>
        <span class="add-btn" @click="addWork" v-if="rank.isAdmin">
          <img src="../../../assets/images/userInfo/addIco.png" width="14" height="14" alt="" class="add-btn-img">
          <span>添加外部工作经历</span>
        </span>
      </div>
    </div>
    <!--公司任职履历-->
    <div class="list-wrap" id="tabs5Wrap">
      <div class="flex flex-align-center list-wrap-top" @click="tabs5Show = !tabs5Show">
        <img class="left-ico" src="../../../assets/images/userInfo/companyIco.png" alt="">
        <span class="font-16 mr-10 bold">公司任职履历</span>
        <img src="../../../assets/images/userInfo/down.png" :class="tabs5Show ? 'spanRotate' : 'spanReset'" width="14" height="14" alt="">
      </div>
      <div class="list-wrap-con" v-if="tabs5Show">
        <div v-if="baseInfo.userResumeList.length">
          <div class="flex mb-24" v-for="(item, index) in baseInfo.userResumeList" :key="index">
            <div class="companyIco-title flex-child-noshrink mr-60">{{ item.startDate }} - {{ item.endDate }}</div>
            <div>{{ item.comment }}</div>
          </div>
        </div>
        <a-empty v-else description="暂无履历"></a-empty>
      </div>
    </div>
    <!--绩效信息-->
    <div class="list-wrap" id="tabs6Wrap" :style="{'margin-bottom': tabs6Show ? 0: '16px'}">
      <div class="flex flex-align-center list-wrap-top" @click="tabs6Show = !tabs6Show">
        <img class="left-ico" src="../../../assets/images/userInfo/performanceIco.png" alt="">
        <span class="font-16 mr-10 bold">绩效信息</span>
        <img src="../../../assets/images/userInfo/down.png" :class="tabs6Show ? 'spanRotate' : 'spanReset'" width="14" height="14" alt="">
      </div>
      <div class="list-wrap-con" v-if="tabs6Show"  style="padding-bottom:0">
        <div v-if="userYearAchievement.length || userYearAchievementList.length">
          <div class="flex mb-30">
            <div class="flex flex-align-center mr-60">
              <span class="performanceDot"></span>
              <span>本年平均绩效系数：{{ userYearAchievement[0].avgRatio }}</span>
            </div>
            <div class="flex flex-align-center mr-60" v-for="item in userYearAchievementList" :key="item.year">
              <span class="performanceDot"></span>
              <span>{{ item.year }}平均绩效系数：</span>
              <span class="blue pointer" @click="openPerformanceVis(`${item.year}平均绩效系数`, item)">{{ item.avgRatio }}</span>
            </div>
          </div>
          <div class="flex flex-wrap" v-if="userYearAchievement.length">
            <div class="performance-wrap" v-for="item in userYearAchievement[0].achievements" :key="item.month">
              <div class="top">{{ item.month }}月</div>
              <div class="bottom">{{ item.ratio }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="list-wrap" id="tabs14Wrap">
      <div class="flex flex-align-center list-wrap-top" @click="tabs14Show = !tabs14Show">
        <img class="left-ico" src="../../../assets/images/userInfo/interviewIco.png" alt="">
        <span class="font-16 mr-10 bold">面谈记录</span>
        <img src="../../../assets/images/userInfo/down.png" :class="tabs14Show ? 'spanRotate' : 'spanReset'" width="14" height="14" alt="">
      </div>
      <div class="list-wrap-con interview-table" style="padding: 0;" v-if="tabs14Show">
        <a-table
          :columns="interviewColumns"
          :dataSource="interview"
          :pagination="false"
          :rowKey="record => record.id"
        >
          <template slot="ch999State" slot-scope="text, record">
            <span>{{ interviewOptions1.filter(item => item.value === text)[0].label }}</span>
          </template>
          <template slot="jobMatch" slot-scope="text, record">
            <span>{{ interviewOptions2.filter(item => item.value === text)[0].label }}</span>
          </template>
          <template slot="outputSalaryMatch" slot-scope="text, record">
            <span>{{ interviewOptions2.filter(item => item.value === text)[0].label }}</span>
          </template>
          <template slot="interviewContent" slot-scope="text, record">
            <a-popover overlayClassName="interviewPoperLayTable" v-if="text && text.length > 50">
              <template slot="content">
                <div v-html="formatWord(text)"></div>
              </template>
              <span class="pointer">{{ text.substring(0, 50) + '...' }}</span>
            </a-popover>
            <span v-else>{{ text }}</span>
          </template>
          <template v-if="$route.query.id && (parseInt($route.query.id) !== $store.state.userInfo.UserID) && rank.isAdmin" slot="actionSlot" slot-scope="text, record">
            <span class="blue pointer mr-10" @click="addInterview(record)">编辑</span>
            <a-popconfirm
              title="确定删除该条记录吗?"
              ok-text="确定"
              cancel-text="取消"
              @confirm="delInterview(record)"
            >
              <span class="pointer red ml-8">删除</span>
            </a-popconfirm>
          </template>
        </a-table>
        <div class="flex flex-align-center" style="margin: 20px 0" v-if="$route.query.id && (parseInt($route.query.id) !== $store.state.userInfo.UserID) && rank.isAdmin">
          <span class="add-btn" @click="addInterview()">
            <img src="../../../assets/images/userInfo/addIco.png" width="14" height="14" alt="" class="add-btn-img">
            <span>添加面谈记录</span>
          </span>
        </div>
      </div>
    </div>
    <!--人才盘点信息-->
    <div class="list-wrap" id="tabs7Wrap" v-if="canWatchVerification">
      <div class="flex flex-align-center list-wrap-top" @click="tabs7Show = !tabs7Show">
        <img class="left-ico" src="../../../assets/images/userInfo/talent-inventory.png" alt="">
        <span class="font-16 mr-10 bold">人才盘点信息</span>
        <img src="../../../assets/images/userInfo/down.png" :class="tabs7Show ? 'spanRotate' : 'spanReset'" width="14" height="14" alt="">
      </div>
      <div class="list-wrap-con interview-table" style="padding: 0;" v-if="tabs7Show">
        <a-table
          :columns="talentInventoryColumns"
          :dataSource="talentInventory"
          :pagination="false"
          :rowKey="record => record.id"
        >
          <template slot="commentSlot" slot-scope="text, record">
            <div style="word-break: break-all;">
<!--              <div v-if="record.resultType === '1'">{{ record.comment }}</div>-->
<!--              <div v-if="record.resultType === '2'">人才盘点评分：{{ record.score }}，评价内容：{{ record.comment }}</div>-->
              <div>评价内容：{{ record.comment }}</div>
            </div>
          </template>
          <template slot="attachmentListSlot" slot-scope="text, record, index">
            <div v-if="record.attachmentList && record.attachmentList.length">
              <FilePreview :list="record.attachmentList"/>
            </div>
            <span v-else>-</span>
          </template>
          <template slot="actionSlot" slot-scope="text, record">
            <div v-if="record.resultType === '1'">-</div>
            <div v-if="record.resultType === '2' && $route.query.id && (parseInt($route.query.id) !== $store.state.userInfo.UserID) && (rank.isAdmin || rank.isCheck)">
              <span class="blue pointer mr-10" @click="addTalentInventory(record)">编辑</span>
              <a-popconfirm
                title="确定删除该条记录吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="talentInventoryBatchDelete(record)"
              >
                <span class="pointer red ml-8">删除</span>
              </a-popconfirm>
            </div>
          </template>
        </a-table>
<!--2025-05-16 一会说要表格，一会说不要表格，下面的代码先留着，不要删，不好说哪天又要用-->
<!--        <div class="flex flex-col">-->
<!--          <div class="flex flex-col talentInventory-wrap" v-for="(item, index) in talentInventory">-->
<!--            <div class="flex flex-align-center" style="line-height: 35px">-->
<!--              <span class="text1">盘点记录{{index + 1}}</span>-->
<!--              <div class="flex flex-align-center" v-if="$route.query.id || (parseInt($route.query.id) !== $store.state.userInfo.UserID)">-->
<!--                <span class="blue pointer mr-10" @click="addTalentInventory(item)">编辑</span>-->
<!--                <a-popconfirm-->
<!--                  title="确定删除该条记录吗?"-->
<!--                  ok-text="确定"-->
<!--                  cancel-text="取消"-->
<!--                  @confirm="talentInventoryBatchDelete(record)"-->
<!--                >-->
<!--                  <span class="pointer red ml-8">删除</span>-->
<!--                </a-popconfirm>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="flex flex-align-center flex-wrap">-->
<!--              <div class="flex flex-align-center talentInventory-text-wrap" v-if="item.inventoryDate">-->
<!--                <span class="text2">盘点日期：</span>-->
<!--                <span class="text3">{{ item.inventoryDate }}</span>-->
<!--              </div>-->
<!--              <div class="flex flex-align-center talentInventory-text-wrap" v-if="item.kindStr">-->
<!--                <span class="text2">盘点类型：</span>-->
<!--                <span class="text3">{{ item.kindStr }}</span>-->
<!--              </div>-->
<!--              <div class="flex flex-align-center talentInventory-text-wrap" v-if="item.kindStr && item.score">-->
<!--                <span class="text2">盘点评分：</span>-->
<!--                <span class="text3">{{ item.score }}</span>-->
<!--              </div>-->
<!--              <div class="flex flex-align-center talentInventory-text-wrap">-->
<!--                <span class="text2">胜任力评分：</span>-->
<!--                <span class="text3">胜任力评分</span>-->
<!--              </div>-->
<!--              <div class="flex flex-align-center talentInventory-text-wrap">-->
<!--                <span class="text2">潜力评分：</span>-->
<!--                <span class="text3">潜力评分</span>-->
<!--              </div>-->
<!--              <div class="flex flex-align-center talentInventory-text-wrap">-->
<!--                <span class="text2">高潜评分：</span>-->
<!--                <span class="text3">高潜评分</span>-->
<!--              </div>-->
<!--              <div class="flex flex-align-center talentInventory-text-wrap">-->
<!--                <span class="text2">季度评分：</span>-->
<!--                <span class="text3">季度评分</span>-->
<!--              </div>-->
<!--              <div class="flex flex-align-center talentInventory-text-wrap" v-if="item.participateStaffNames">-->
<!--                <span class="text2">参与评价人员：</span>-->
<!--                <span class="text3">{{item.participateStaffNames}}</span>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="flex flex-align-start margin-bottom" v-if="item.comment">-->
<!--              <span class="text2">评价内容：</span>-->
<!--              <div class="flex-child-average flex-wrap text3">{{ item.comment }}</div>-->
<!--            </div>-->
<!--            <div class="flex flex-align-start margin-bottom" v-if="item.nextStageAdvice">-->
<!--              <span class="text2">下阶段用人建议：</span>-->
<!--              <div class="flex-child-average flex-wrap text3">{{ item.nextStageAdvice }}</div>-->
<!--            </div>-->
<!--            <div class="flex flex-align-start" v-if="item.attachmentList && item.attachmentList.length">-->
<!--              <span class="text2">附件：</span>-->
<!--              <span>-->
<!--                <FilePreview :list="item.attachmentList"/>-->
<!--              </span>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->
        <div class="flex flex-align-center" style="margin: 20px 0" v-if="$store.state.userInfo.Rank.includes('rygl') || $store.state.userInfo.Rank.includes('ryck')">
          <span class="add-btn" @click="addTalentInventory()">
            <img src="../../../assets/images/userInfo/addIco.png" width="14" height="14" alt="" class="add-btn-img">
            <span>添加人才盘点记录</span>
          </span>
        </div>
      </div>
    </div>
    <!--关键事件-->
    <div class="list-wrap" id="tabs8Wrap">
      <div class="flex flex-align-center list-wrap-top" @click="tabs8Show = !tabs8Show">
        <img class="left-ico" src="../../../assets/images/userInfo/keyEvents.png" alt="">
        <span class="font-16 mr-10 bold">关键事件</span>
        <img src="../../../assets/images/userInfo/down.png" :class="tabs8Show ? 'spanRotate' : 'spanReset'" width="14" height="14" alt="">
      </div>
      <div class="list-wrap-con" v-if="tabs8Show">
        <a-row :span="24" class="mb-36" v-for="(item, index) in keyEvents" :key="index">
          <a-col :span="5">
            <div class="flex flex-align-center">
              <div class="label-title">事件类型：</div>
              <a-select :disabled="!rank.isAdmin" v-model="item.major" class="full-width" allowClear placeholder="请选择">
                <a-select-option value="奖励">奖励</a-select-option>
                <a-select-option value="惩罚">惩罚</a-select-option>
              </a-select>
            </div>
          </a-col>
          <a-col :span="5" >
            <div class="flex flex-align-center">
              <div class="label-title">级别：</div>
              <a-select v-if="item.major === '奖励'" v-model="item.key_level" class="full-width" allowClear placeholder="请选择">
                <a-select-option :value="1">公司级</a-select-option>
                <a-select-option :value="2">中心级</a-select-option>
                <a-select-option :value="3">分公司级</a-select-option>
              </a-select>
              <span v-else style="height: 32px" class="flex flex-align-center">-</span>
            </div>
          </a-col>
          <a-col :span="5" >
            <div class="flex flex-align-center">
              <div class="label-title">事件日期：</div>
              <a-date-picker :disabled="!rank.isAdmin" class="full-width" v-model="item.stime" allowClear placeholder="请选择"></a-date-picker>
            </div>
          </a-col>
          <a-col :span="7" class="aRowSpacing">
            <div class="flex flex-align-center">
              <div class="label-title">事件内容：</div>
              <a-input :disabled="!rank.isAdmin" class="full-width" v-model="item.mainDuty" placeholder="请输入" allowClear/>
            </div>
          </a-col>
          <a-col :span="1" v-if="rank.isAdmin">
            <div class="h-32 flex flex-align-center">
              <div class="pointer red" @click="delKeyEvents(index)">删除</div>
            </div>
          </a-col>
        </a-row>
        <span class="add-btn" @click="addKeyEvents" v-if="rank.isAdmin">
          <img src="../../../assets/images/userInfo/addIco.png" width="14" height="14" alt="" class="add-btn-img">
          <span>添加关键事件</span>
        </span>
      </div>
    </div>
    <!--学习发展信息-->
    <div class="list-wrap" id="tabs9Wrap">
      <div class="flex flex-align-center list-wrap-top" @click="tabs9Show = !tabs9Show">
        <img class="left-ico" src="../../../assets/images/userInfo/studyIco.png" alt="">
        <span class="font-16 mr-10 bold">学习发展信息</span>
        <img src="../../../assets/images/userInfo/down.png" :class="tabs9Show ? 'spanRotate' : 'spanReset'" width="14" height="14" alt="">
      </div>
      <div class="list-wrap-con" v-if="tabs9Show">
        <a-row class="mb-36" v-for="(item, index) in trainingTableData" :key="index">
          <a-col :span="5" class="aRowSpacing">
            <div class="flex flex-align-center">
              <div class="label-title">培训类型：</div>
              <a-select :disabled="!rank.isAdmin" v-model="item.place" class="full-width" allowClear placeholder="请选择">
                <a-select-option value="内部培训">内部培训</a-select-option>
                <a-select-option value="外部培训">外部培训</a-select-option>
              </a-select>
            </div>
          </a-col>
          <a-col :span="5" class="aRowSpacing">
            <div class="flex flex-align-center">
              <div class="label-title">起止时间：</div>
              <a-range-picker :disabled="!rank.isAdmin" :value="[item.stime ? moment(item.stime, dateFormat) : '', item.etime ? moment(item.etime, dateFormat) : '']" @change="(dates, dateStrings) => trainingTime(dates, dateStrings, index)"/>
            </div>
          </a-col>
          <a-col :span="5" class="aRowSpacing">
            <div class="flex flex-align-center">
              <div class="label-title">培训项目：</div>
              <a-input :disabled="!rank.isAdmin" class="full-width" v-model="item.major" placeholder="请输入"/>
            </div>
          </a-col>
          <a-col :span="5" class="aRowSpacing">
            <div class="flex flex-align-center">
              <div class="label-title">培训成果：</div>
              <a-input :disabled="!rank.isAdmin" class="full-width" v-model="item.mainDuty" placeholder="请输入"/>
            </div>
          </a-col>
          <a-col :span="2" v-if="rank.isAdmin">
            <div class="h-32 flex flex-align-center">
              <div class="pointer red" @click="delTrainingTableData(index)">删除</div>
            </div>
          </a-col>
        </a-row>
        <span class="add-btn" @click="addTraining" v-if="rank.isAdmin">
          <img src="../../../assets/images/userInfo/addIco.png" width="14" height="14" alt="" class="add-btn-img">
          <span>添加学习发展信息</span>
        </span>
      </div>
    </div>
    <!--关系网-->
    <div class="list-wrap" id="tabs11Wrap">
      <div class="flex flex-align-center list-wrap-top" @click="tabs11Show = !tabs11Show">
        <img class="left-ico" src="../../../assets/images/userInfo/relationshipIco.png" alt="">
        <span class="font-16 mr-10 bold">关系网</span>
        <img src="../../../assets/images/userInfo/down.png" :class="tabs11Show ? 'spanRotate' : 'spanReset'" width="14" height="14" alt="">
      </div>
      <div class="list-wrap-con" v-if="tabs11Show">
        <a-row class="mb-20" v-for="(item, index) in baseInfo.userInfo.ch999Relation" :key="index">
          <a-col :span="5" class="aRowSpacing">
            <div class="flex flex-align-center">
              <div class="label-title">公司内部亲属{{ index + 1 }}：</div>
              <ni-staff-select :disabled="!rank.isAdmin" v-model="item.ch999_id"/>
            </div>
          </a-col>
          <a-col :span="5" class="aRowSpacing">
            <div class="flex flex-align-center">
              <div class="label-title">亲属关系：</div>
              <a-select :disabled="!rank.isAdmin" class="full-width" v-model="item.relationType" placeholder="选择关系">
                <a-select-option :value="r.value" v-for="r in relation" :key="r.value">{{ r.label }}</a-select-option>
              </a-select>
            </div>
          </a-col>
          <a-col :span="2" v-if="rank.isAdmin">
            <div class="h-32 flex flex-align-center">
              <div class="pointer red" @click="delCh999Relation(index)">删除</div>
            </div>
          </a-col>
        </a-row>
        <span class="add-btn" @click="addRelation" v-if="rank.isAdmin">
          <img src="../../../assets/images/userInfo/addIco.png" width="14" height="14" alt="" class="add-btn-img">
          <span>添加内部亲属关系</span>
        </span>
        <div class="flex flex-align-center mt-16">
          <span class="flex-child-noshrink">是否有家属在竞争企业工作： </span>
          <a-select
            :disabled="!rank.isAdmin"
            v-model="baseInfo.ch999_userEx.is_race_work"
            style="width: 200px"
            placeholder="请选择"
            :options="[
              {
                label: '是',
                value: true,
              },
              {
                label: '否',
                value: false,
              },
            ]"
          ></a-select>
        </div>
        <div class="mt-20" v-if="baseInfo.ch999_userEx.is_race_work">
          <a-row class="mb-20" v-for="(item, index) in baseInfo.relativesRaceWork" :key="index">
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">竞争企业亲属{{ index + 1 }}：</div>
                <a-select :disabled="!rank.isAdmin" class="full-width" placeholder="选择关系" v-model="item.relationship">
                  <a-select-option :value="r.Value" v-for="r in info.relationshipList" :key="r.Value">{{ r.Key }}</a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">亲属就职单位：</div>
                <a-input :disabled="!rank.isAdmin" v-model="item.companyName" placeholder="请输入"/>
              </div>
            </a-col>
            <a-col :span="5" class="aRowSpacing">
              <div class="flex flex-align-center">
                <div class="label-title">就职岗位：</div>
                <a-input :disabled="!rank.isAdmin" v-model="item.postName" placeholder="请输入"/>
              </div>
            </a-col>
            <a-col :span="2" v-if="rank.isAdmin">
              <div class="h-32 flex flex-align-center">
                <div class="pointer red" @click="delRelativesRaceWork(index)">删除</div>
              </div>
            </a-col>
          </a-row>
          <span class="add-btn" @click="addRelativesRaceWork" v-if="rank.isAdmin">
          <img src="../../../assets/images/userInfo/addIco.png" width="14" height="14" alt="" class="add-btn-img">
          <span>添加竞争企业亲属关系</span>
        </span>
        </div>
      </div>
    </div>
    <!--系统信息-->
    <div class="list-wrap" id="tabs12Wrap">
      <div class="flex flex-align-center list-wrap-top" @click="tabs12Show = !tabs12Show">
        <img class="left-ico" src="../../../assets/images/userInfo/systemInfoIco.png" alt="">
        <span class="font-16 mr-10 bold">系统信息</span>
        <img src="../../../assets/images/userInfo/down.png" :class="tabs12Show ? 'spanRotate' : 'spanReset'" width="14" height="14" alt="">
      </div>
      <div class="list-wrap-con" v-if="tabs12Show">
        <a-row class="mb-36">
          <a-col style="width: 88.5%">
            <div class="flex flex-align-center">
              <div class="label-title">权限角色：</div>
              <a-select
                :disabled="!rank.isAdmin && !rank.isCheck"
                class="full-width"
                v-model="baseInfo.userInfo.Roles"
                mode="multiple"
                :maxTagCount="6"
                allowClear
                showSearch
                optionFilterProp="children"
                :filterOption="filterOption"
              >
                <a-select-option :value="r.id.toString()" v-for="r in info.RoleList" :key="r.id">{{ r.name }}</a-select-option>
              </a-select>
            </div>
          </a-col>
        </a-row>
        <a-row class="mb-36">
<!--          <a-col :span="5" class="aRowSpacing">-->
<!--            <div class="flex flex-align-center">-->
<!--              <div class="label-title">岗位分类：</div>-->
<!--              <a-tree-select-->
<!--                :disabled="!rank.isAdmin"-->
<!--                class="full-width"-->
<!--                v-if="jobCate.length > 0"-->
<!--                :treeData="jobCate"-->
<!--                showSearch-->
<!--                treeNodeFilterProp="label"-->
<!--                :treeDefaultExpandedKeys="[baseInfo.userInfo.jobCateId]"-->
<!--                :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"-->
<!--                v-model="baseInfo.userInfo.jobCateId"-->
<!--                placeholder="请选择"-->
<!--              >-->
<!--              </a-tree-select>-->
<!--            </div>-->
<!--          </a-col>-->
          <a-col :span="5" class="aRowSpacing">
            <div class="flex flex-align-center">
              <div class="label-title">员工类别：</div>
              <a-select
                :disabled="!rank.isAdmin"
                class="full-width"
                showSearch
                placeholder="请选择"
                v-model="baseInfo.userInfo.isshixi"
                allowClear
                :filterOption="filterOption"
              >
                <a-select-option :value="u.id" v-for="u in info.userType" :key="u.id">{{ u.name }}</a-select-option>
              </a-select>
            </div>
          </a-col>
          <a-col :span="5" class="aRowSpacing">
            <div class="flex flex-align-center">
              <div class="label-title">填写日志：</div>
              <a-select :disabled="!rank.isAdmin" v-model="baseInfo.userInfo.isWriteLog" class="full-width" placeholder="请选择">
                <a-select-option :value="true">是</a-select-option>
                <a-select-option :value="false">否</a-select-option>
                <a-select-option :value="null">不生效</a-select-option>
              </a-select>
            </div>
          </a-col>
          <a-col :span="5" class="aRowSpacing">
            <div class="flex flex-align-center">
              <div class="label-title">主岗位标签：</div>
              <a-select
                class="full-width"
                :disabled="!rank.isAdmin"
                allowClear
                v-model="baseInfo.userInfo.mainStation"
                placeholder="请选择"
              >
                <a-select-option :value="m.id" v-for="m in info.mainStation" :key="m.id">{{ m.name }}</a-select-option>
              </a-select>
            </div>
          </a-col>
          <a-col :span="5">
            <div class="flex flex-align-center">
              <div class="label-title">考勤类型：</div>
              <a-cascader
                :disabled="!rank.isAdmin && !rank.isCheck"
                class="full-width"
                :displayRender="displayRender"
                @change="scheduleIdChange"
                allowClear
                :showSearch="true"
                :value="baseInfo.userSchedule"
                placeholder="分类/考勤类型"
                :options="info.scheduleList"
              ></a-cascader>
            </div>
          </a-col>
        </a-row>
        <a-row class="mb-36">
          <a-col :span="5" class="aRowSpacing">
            <div class="flex flex-align-center">
              <div class="label-title">夏装工服尺寸：</div>
              <div class="flex-child-average required">
                <a-select
                  :disabled="!rank.isAdmin && !rank.isCheck"
                  placeholder="请选择"
                  v-model="baseInfo.userInfo.dressSizeSummer"
                  allowClear
                  class="full-width"
                >
                  <a-select-option v-for="item in info.dressSizeSummerList" :value="item" :key="item">{{ item }}</a-select-option>
                </a-select>
              </div>
            </div>
          </a-col>
          <a-col :span="5">
            <div class="flex flex-align-center">
              <div class="label-title">冬装工服尺寸：</div>
              <div class="flex-child-average required">
                <a-select
                  :disabled="!rank.isAdmin && !rank.isCheck"
                  placeholder="请选择"
                  v-model="baseInfo.userInfo.dressSize"
                  allowClear
                  class="full-width"
                >
                  <a-select-option v-for="item in info.dressSizeList" :value="item" :key="item">{{ item }}</a-select-option>
                </a-select>
              </div>
            </div>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="5" class="aRowSpacing">
            <div class="flex flex-align-center">
              <div class="label-title">暂停登录：</div>
              <a-switch
                @change="changeIsLogin"
                :disabled="!rank.isAdmin && !rank.isCheck"
                v-model="baseInfo.userInfo.islogin"
                checkedChildren="是"
                unCheckedChildren="否"></a-switch>
            </div>
          </a-col>
<!--          <a-col :span="5" class="aRowSpacing">-->
<!--            <div class="flex flex-align-center">-->
<!--              <div class="label-title">资料锁定：</div>-->
<!--              <div class="flex-child-average relative">-->
<!--                <a-switch :disabled="!rank.isAdmin" v-model="baseInfo.userInfo.lockzl" checkedChildren="是" unCheckedChildren="否"></a-switch>-->
<!--                <i class="required1">*</i>-->
<!--              </div>-->
<!--            </div>-->
<!--          </a-col>-->
        </a-row>
      </div>
    </div>
    <div class="footer-btn-wrap padding">
      <div v-if="rank.isAdmin || rank.isCheck" class="footer-btn footer-btn-sure" style="margin-right: 16px;border:none" @click="saveUserBaseInfo">保存</div>
      <div class="footer-btn">返回</div>
    </div>
    <a-modal
      :title="performanceVisTit"
      :visible="performanceVis"
      @cancel="performanceVis = false"
      :footer="null"
      class="performanceModel"
      :width="550"
    >
      <div class="title">{{ performanceVisTit }}</div>
      <div class="flex">
        <div class="performanceVis-wrap">
          <div class="performanceVis-item" v-for="item in userYearAchievementModal" :key="item.month">{{ item.month }}月：{{ item.ratio}}</div>
        </div>
      </div>
    </a-modal>
    <a-modal
      :title="interviewSelect?.id ? '编辑面谈记录' : '新增面谈记录'"
      :visible="interviewVis"
      @cancel="interviewVisHide"
      @ok="addInterviewHandle"
      class="interviewModel"
      width="1000px"
    >
      <a-form-model
        ref="interViewForm"
        :model="interviewSelect"
        :rules="interViewRules"
        layout="inline"
      >
        <div class="flex flex-justify-between mb-16">
          <a-form-model-item label="面谈时间" prop="interviewDate">
            <a-date-picker
              v-model="interviewSelect.interviewDate"
              format="YYYY-MM-DD"
              placeholder="请选择面谈时间"
              style="width: 100%"
            />
          </a-form-model-item>
          <a-form-model-item label="员工状态" prop="ch999State">
            <a-select class="full-width" v-model="interviewSelect.ch999State" allowClear placeholder="请选择" :options="interviewOptions1"></a-select>
          </a-form-model-item>
        </div>
        <div class="flex flex-justify-between mb-16" v-if="$route.query.id && (parseInt($route.query.id) !== $store.state.userInfo.UserID)">
          <a-form-model-item label="岗位匹配度" prop="jobMatch">
            <a-select class="full-width" v-model="interviewSelect.jobMatch" allowClear placeholder="请选择" :options="interviewOptions2"></a-select>
          </a-form-model-item>
          <a-form-model-item label="产出与职级薪等匹配度" prop="outputSalaryMatch" v-if="$route.query.id && (parseInt($route.query.id) !== $store.state.userInfo.UserID)">
            <a-select class="full-width" v-model="interviewSelect.outputSalaryMatch" allowClear placeholder="请选择" :options="interviewOptions2"></a-select>
          </a-form-model-item>
        </div>
        <div class="flex flex-justify-between mb-16">
          <a-form-model-item label="面谈人" prop="interviewerId">
            <ni-staff-select
              v-model="interviewSelect.interviewerId"
              show-type="name"
              placeholder="请选择面谈人"
              @change="(keys, outputObject) => interViewChange(keys, outputObject)"
            />
          </a-form-model-item>
        </div>
        <div class="flex flex-justify-between">
          <a-form-model-item label="面谈内容" prop="interviewContent" class="interview-textarea-wrap">
            <a-textarea
              class="interview-textarea"
              v-model="interviewSelect.interviewContent"
              placeholder="请输入面谈内容"
              allowClear
              :rows="1"
            />
          </a-form-model-item>
        </div>
      </a-form-model>
    </a-modal>
    <a-modal
      :title="talentInventorySelect?.id ? '编辑盘点记录' : '新增盘点记录'"
      :visible="talentInventoryVis"
      @cancel="talentInventoryVisHide"
      @ok="addTalentInventoryHandle"
      class="interviewModel"
      width="1000px"
    >
      <a-form-model
        ref="talentInventoryForm"
        :model="talentInventorySelect"
        :rules="talentInventoryRules"
        layout="inline"
      >
        <div class="flex flex-justify-between mb-16">
          <a-form-model-item label="盘点类型" prop="kind">
            <a-select class="full-width" v-model="talentInventorySelect.kind" allowClear placeholder="请选择" :options="inventoryKind"></a-select>
          </a-form-model-item>
          <a-form-model-item label="盘点日期" prop="inventoryDate">
            <a-date-picker
              v-model="talentInventorySelect.inventoryDate"
              format="YYYY-MM-DD"
              placeholder="请选择盘点日期"
              style="width: 100%"
            />
          </a-form-model-item>
        </div>
        <div class="flex flex-justify-between mb-16">
          <a-form-model-item label="胜任力评分（S）" prop="score">
            <a-input-number :min="0" :max="100" :step="0.1" style="width: 100%" v-model="talentInventorySelect.score" placeholder="请输入胜任力评分"/>
          </a-form-model-item>
          <a-form-model-item label="潜力评分（Q）" prop="potentialScore">
            <a-input-number :min="0" :max="100" :step="0.1" style="width: 100%" v-model="talentInventorySelect.potentialScore" placeholder="请输入潜力评分"/>
          </a-form-model-item>
        </div>
        <div class="flex flex-justify-between mb-16">
          <a-form-model-item label="高潜评分（GQ）" prop="highPotentialScore">
            <a-input-number :min="0" :max="100" :step="0.1" style="width: 100%" v-model="talentInventorySelect.highPotentialScore" placeholder="请输入高潜评分"/>
          </a-form-model-item>
          <a-form-model-item label="季度评级" prop="rating">
            <a-select class="full-width" v-model="talentInventorySelect.rating" allowClear placeholder="请选择季度评级" :options="quarterOptions"></a-select>
          </a-form-model-item>
        </div>
        <div class="flex flex-justify-between mb-16">
<!--          <a-form-model-item label="盘点评分" prop="score">-->
<!--            <a-input-number :min="0" :max="100" :step="0.1" style="width: 100%" v-model="talentInventorySelect.score" placeholder="请输入盘点评分"/>-->
<!--          </a-form-model-item>-->
          <a-form-model-item label="参与评价人员">
            <ni-staff-select
              :multiple="true"
              :maxTagCount="1"
              v-model="talentInventorySelect.participateStaffIds"
              style="width:100%"
              show-type="name"
              placeholder="请选择参与评价人员"
            />
          </a-form-model-item>
        </div>
        <div class="flex flex-justify-between mb-16">
          <a-form-model-item label="评价内容" class="interview-textarea-wrap">
            <a-textarea
              class="interview-textarea"
              v-model="talentInventorySelect.comment"
              placeholder="请输入评价内容"
              allowClear
              :rows="1"
              :maxLength="500"
            />
            <div style="text-align: end; opacity: 0.5;margin-top: -8px; line-height: 15px">{{talentInventorySelect.comment ? talentInventorySelect.comment.length : 0}} / 500</div>
          </a-form-model-item>
        </div>
        <div class="flex flex-justify-between mb-16">
          <a-form-model-item label="下阶段用人建议" class="interview-textarea-wrap">
            <a-textarea
              class="interview-textarea"
              v-model="talentInventorySelect.nextStageAdvice"
              placeholder="请输入评价内容"
              allowClear
              :rows="1"
              :maxLength="500"
            />
            <div style="text-align: end; opacity: 0.5;margin-top: -8px; line-height: 15px">{{talentInventorySelect.nextStageAdvice ? talentInventorySelect.nextStageAdvice.length : 0}} / 500</div>
          </a-form-model-item>
        </div>
        <div class="flex flex-justify-between">
          <a-form-model-item label="附件" class="interview-textarea-wrap">
            <uploader
              :buttonName="['上传附件','手机上传']"
              :fileList.sync="talentInventorySelect.attachmentList"
            />
          </a-form-model-item>
        </div>
      </a-form-model>
    </a-modal>
    <delete-user-modal :ch999Id="baseInfo.userInfo.ch999_id" ref="deleteUserModalRef"/>
  </div>
</template>

<script lang="jsx">
  import { reactive, toRefs, watch, getCurrentInstance, nextTick, ref, onMounted, onBeforeUnmount, computed } from 'vue'
  import moment from 'moment'
  import { NiAreaSelect, NiDepartSelect, NiStaffSelect } from '@jiuji/nine-ui'
  import { treeWalk } from '~/util/treeUtils'
  import freshmanTags from './components/freshman-tags.vue'
  import { handleJson } from './components/handleJson'
  import citySelect from '~/components/city-select'
  import cloneDeep from 'lodash/cloneDeep'
  import { registerNaturOptions, marryOptions, quarterOptions, englishLeaveOptions } from './components/constants'
  import uploader from '~/components/uploader/index.vue'
  import FilePreview from './components/filePreview.vue'
  import DeleteUserModal from './components/delete-user-modal.vue'

  export default {
    components: {
      freshmanTags,
      NiStaffSelect,
      NiAreaSelect,
      NiDepartSelect,
      citySelect,
      uploader,
      FilePreview,
      DeleteUserModal
    },
    props: {
      info: {
        type: Object,
        default: () => {}
      },
      rank: '',
      educationList: {
        type: Array,
        default: () => []
      }
    },
    setup (props, { emit }) {
      const root = getCurrentInstance().proxy
      const { $tnt, $api, $message, $set, $indicator, $store, $route } = root
      const interViewForm = ref(null)
      const talentInventoryForm = ref(null)
      const interViewRules = {
        interviewDate: [
          { required: true, message: '请选择面谈时间', trigger: 'change' }
        ],
        ch999State: [
          { required: true, message: '请选择员工状态', trigger: 'change' }
        ],
        jobMatch: [
          { required: true, message: '请选择岗位匹配度', trigger: 'change' }
        ],
        outputSalaryMatch: [
          { required: true, message: '请选择产出与职级薪等匹配度', trigger: 'change' }
        ],
        interviewerId: [
          { required: true, message: '请选择面谈人', trigger: 'change' }
        ],
        interviewContent: [
          { required: true, message: '请输入面谈内容', trigger: 'blur' }
        ]
      }
      const talentInventoryRules = {
        kind: [
          { required: true, message: '请选择盘点类型', trigger: 'change' }
        ],
        inventoryDate: [
          { required: true, message: '请选择盘点日期', trigger: 'change' }
        ],
        // score: [
        //   { required: true, message: '请输入盘点评分', trigger: 'blur' }
        // ],
        comment: [
          { required: true, message: '请输入评价内容', trigger: 'blur' }
        ]
      }
      const state = reactive({
        canWatchVerification: false,
        baseInfo: cloneDeep(props.info),
        payListCur: [], // 选中的薪酬类别
        zhijiCur: [], // 选中的职级
        zhijiData: [], // 职级
        dateFormat: 'YYYY/MM/DD',
        tabs1Show: true,
        tabs2Show: true,
        tabs3Show: true,
        tabs4Show: true,
        tabs5Show: false,
        tabs6Show: false,
        tabs7Show: false,
        tabs8Show: false,
        tabs9Show: false,
        tabs10Show: true,
        tabs11Show: false,
        tabs12Show: false,
        tabs13Show: true,
        tabs15Show: true,
        tabs14Show: false,
        dimissionOptions: [],
        interestTags: '', // 兴趣爱好
        interestTagsBtnLoading: false, // 兴趣保存按钮
        performanceVis: false, // 绩效弹窗
        performanceVisTit: '绩效平均系数',
        relation: [],
        jobCate: [], // 岗位分类树
        tabsArr: [
          {
            idName: 'tabs1Wrap',
            label: '在职信息',
            value: 1
          },
          {
            idName: 'tabs2Wrap',
            label: '个人信息',
            value: 2
          },
          {
            idName: 'tabs13Wrap',
            label: '家庭背景',
            value: 13
          },
          {
            idName: 'tabs15Wrap',
            label: '家庭成员',
            value: 15
          },
          {
            idName: 'tabs3Wrap',
            label: '紧急联系人',
            value: 3
          },
          {
            idName: 'tabs4Wrap',
            label: '教育经历',
            value: 4
          },
          {
            idName: 'tabs10Wrap',
            label: '外部工作经历',
            value: 10
          },
          {
            idName: 'tabs5Wrap',
            label: '公司任职履历',
            value: 5
          },
          {
            idName: 'tabs6Wrap',
            label: '绩效信息',
            value: 6
          },
          {
            idName: 'tabs14Wrap',
            label: '面谈记录',
            value: 14
          },
          {
            idName: 'tabs7Wrap',
            label: '人才盘点信息',
            value: 7
          },
          {
            idName: 'tabs8Wrap',
            label: '关键事件',
            value: 8
          },
          {
            idName: 'tabs9Wrap',
            label: '学习发展信息',
            value: 9
          },
          {
            idName: 'tabs11Wrap',
            label: '关系网',
            value: 11
          },
          {
            idName: 'tabs12Wrap',
            label: '系统信息',
            value: 12
          },
        ],
        tabsActiveIndex: 1,
        // 教育经历
        educationTableData: [],
        // 学习发展经历
        trainingTableData: [],
        // 工作经历
        workTableData: [],
        // 紧急联系人
        emergencyContacts: [],
        // 家庭成员
        familyList: [],
        interviewColumnsOrg: [
          {
            title: '面谈日期',
            dataIndex: 'interviewDate',
          },
          {
            title: '员工状态',
            dataIndex: 'ch999State',
            scopedSlots: { customRender: 'ch999State' },
          },
          {
            title: '岗位匹配度',
            dataIndex: 'jobMatch',
            scopedSlots: { customRender: 'jobMatch' },
          },
          {
            title: '产出与职级薪等匹配度',
            dataIndex: 'outputSalaryMatch',
            scopedSlots: { customRender: 'outputSalaryMatch' },
          },
          {
            title: '面谈人',
            dataIndex: 'interviewer',
          },
          {
            title: '面谈内容',
            dataIndex: 'interviewContent',
            scopedSlots: { customRender: 'interviewContent' },
            width: 800
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 120,
            scopedSlots: { customRender: 'actionSlot' }
          }
        ],
        interview: [],
        // 人才盘点
        talentInventoryOrg: [
          {
            title: '盘点日期',
            dataIndex: 'inventoryDate',
            width: 120,
          },
          {
            title: '盘点类型',
            dataIndex: 'kindStr',
            width: 120,
            customRender: (text, record, index) => {
              return (
                <span>{text || '-'}</span>
              )
            }
          },
          // {
          //   title: '盘点评分',
          //   dataIndex: 'score',
          //   width: 90,
          //   customRender: (text, record, index) => {
          //     return (
          //       <span>{ record.kindStr && text ? text : '-'}</span>
          //     )
          //   }
          // },
          {
            title: '胜任力评分（S）',
            dataIndex: 'score',
            width: 120,
            customRender: (text, record, index) => {
              return (
                <span>{text || '-'}</span>
              )
            }
          },
          {
            title: '潜力评分（Q）',
            dataIndex: 'potentialScore',
            width: 100,
            customRender: (text, record, index) => {
              return (
                <span>{text || '-'}</span>
              )
            }
          },
          {
            title: '高潜评分（GQ）',
            dataIndex: 'highPotentialScore',
            width: 100,
            customRender: (text, record, index) => {
              return (
                <span>{text || '-'}</span>
              )
            }
          },
          {
            title: '季度评级',
            dataIndex: 'rating',
            width: 90,
            customRender: (text, record, index) => {
              return (
                <span>{text || '-'}</span>
              )
            }
          },
          {
            title: '参与评价人员',
            dataIndex: 'participateStaffNames',
            width: 150,
            customRender: (text, record, index) => {
              return (
                <span>{text || '-'}</span>
              )
            }
          },
          {
            title: '评价内容',
            dataIndex: 'comment',
            width: 250,
            scopedSlots: { customRender: 'commentSlot' }
          },
          {
            title: '下阶段用人建议',
            dataIndex: 'nextStageAdvice',
            width: 250,
            customRender: (text, record, index) => {
              return (
                <span>{text || '-'}</span>
              )
            }
          },
          {
            title: '录入人',
            dataIndex: 'addUser',
            width: 80,
            customRender: (text, record, index) => {
              return (
                <span>{text || '-'}</span>
              )
            }
          },
          {
            title: '附件',
            dataIndex: 'attachmentList',
            width: 200,
            scopedSlots: { customRender: 'attachmentListSlot' }
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 120,
            scopedSlots: { customRender: 'actionSlot' }
          }
        ],
        talentInventory: [],
        // 关键事件
        keyEvents: [],
        userResumeList: [],
        userYearAchievement: [],
        userYearAchievementList: [],
        userYearAchievementModal: [],
        userAge: undefined,
        department: [], // 部门树
        userInfoBtnLoading: false,
        interviewSelect: {},
        interviewVis: false,
        interviewOptions1: [
          {
            label: '试用期',
            value: 1
          },
          {
            label: '成长期',
            value: 2
          },
          {
            label: '成熟期',
            value: 3
          },
          {
            label: '瓶颈期',
            value: 4
          }
        ],
        interviewOptions2: [
          {
            label: 'S 高度匹配',
            value: 'S'
          },
          {
            label: 'A 良好匹配',
            value: 'A'
          },
          {
            label: 'B 基本匹配',
            value: 'B'
          },
          {
            label: 'C 不太匹配',
            value: 'C'
          }
        ],
        talentInventoryVis: false,
        talentInventorySelect: {
          comment: undefined,
          id: undefined,
          inventoryDate: undefined,
          kind: undefined,
          participateStaffIds: undefined,
          nextStageAdvice: undefined,
          attachmentList: undefined,
          score: undefined,
          potentialScore: undefined,
          highPotentialScore: undefined,
          rating: undefined,
        },
        inventoryKind: [],
        positionTypeOptions: [
          {
            name: '管理岗',
            id: 1
          },
          {
            name: '专业岗',
            id: 2
          },
          {
            name: '支撑岗',
            id: 3
          }
        ]
      })

      async function getVerificationRank () {
        const res = await $api.user.getVerificationRank({ staffId: $route.query.id || $store.state.userInfo.UserID }).catch(err => {
          $message.error(err.message)
        })
        state.canWatchVerification = res || false
      }
      getVerificationRank()

      const tabs = ref(null)
      const isSticky = ref(false)
      const setSticky = function () {
        if (tabs.value) {
          const top = tabs.value.getBoundingClientRect().top
          isSticky.value = Math.round(top) === 68
        } else {
          isSticky.value = false
        }
      }
      const interviewColumns = computed(() => {
        return !$route.query.id || (parseInt($route.query.id) === $store.state.userInfo.UserID) ? state.interviewColumnsOrg.filter(item => !['jobMatch', 'outputSalaryMatch', 'action'].includes(item.dataIndex)) : state.interviewColumnsOrg
      })
      const talentInventoryColumns = computed(() => {
        return !$route.query.id || (parseInt($route.query.id) === $store.state.userInfo.UserID) ? state.talentInventoryOrg.filter(item => !['action'].includes(item.dataIndex)) : state.talentInventoryOrg
      })
      const educationDisabled = computed(() => {
        const { educationTableData } = state
        return !!educationTableData.length
      })
      const educationIndex = computed(() => {
        const { educationTableData } = state
        if (!educationTableData.length || !educationTableData.some(d => d.etime)) return -1
        const item = educationTableData.filter(d => d.etime).reduce((acc, obj) => {
          return (moment(acc.etime).isAfter(moment(obj.etime))) ? acc : obj
        })
        return educationTableData.findIndex(d => d.etime === item.etime)
      })
      const educationChange = function (item, index) {
        if (educationIndex.value === index && item.Education) {
          state.baseInfo.userInfo.Education = item.Education
        }
        item.EducationType = undefined
      }
      onMounted(() => {
        const wrapper = document.querySelector('#overWrap')
        wrapper.addEventListener('scroll', setSticky)
      })

      onBeforeUnmount(() => {
        const wrapper = document.querySelector('#overWrap')
        wrapper.removeEventListener('scroll', setSticky)
      })

      const EducationTypeListOptions = (val) => {
        return props.educationList?.find(it => it.value === val)?.children?.map(it => ({
          ...it,
          value: +it.value
        })) || []
      }
      state.userYearAchievement = props.info && props.info.yearAchievementList.length ? [props.info.yearAchievementList[0]] : []
      state.userYearAchievementList = props.info && props.info.yearAchievementList.length > 1 ? props.info.yearAchievementList.slice(1, props.info.yearAchievementList.length) : []
      const handleData = (val) => {
        state.baseInfo.userInfo.area1id = val.userInfo.area1id ? val.userInfo.area1id.toString() : '' // 所属地区
        state.baseInfo.userInfo.departCode = val.userInfo.departCode ? val.userInfo.departCode.toString() : '' // 部门
        state.baseInfo.userInfo.Roles = val.userInfo.Roles ? typeof val.userInfo.Roles === 'string' ? val.userInfo.Roles.split(',') : val.userInfo.Roles : [] // 处理角色回填
        state.baseInfo.userInfo.ReserveType = val.userInfo.ReserveType ? val.userInfo.ReserveType.toString() : undefined // 员工组类别
        state.baseInfo.userInfo.iszaizhi = val.userInfo.iszaizhi.toString() // 是否在职
        state.baseInfo.userInfo.usersex = val.userInfo.usersex !== null ? val.userInfo.usersex.toString() : undefined // 性别
        state.baseInfo.userInfo.mainStation = val.userInfo.mainStation || [] // 主岗位
        state.baseInfo.userInfo.isshixi = val.userInfo.isshixi || [] // 员工类别
        state.baseInfo.userInfo.WorkKeys = val.userInfo.WorkKeys ? val.userInfo.WorkKeys : undefined // 主要职责
        state.baseInfo.userInfo.EduLevel = val.userInfo.EduLevel ? val.userInfo.EduLevel.toString() : undefined // 学历
        state.baseInfo.userInfo.EducationType = val.userInfo.EducationType ? val.userInfo.EducationType : undefined // 院校性质
        state.baseInfo.userInfo.lockzl = !!val.userInfo.lockzl // 锁定资料
        state.baseInfo.userInfo.islogin = !!val.userInfo.islogin // 限制登录
        state.zhijiCur = val.userInfo.Szhiji && val.userInfo.zhiji ? [val.userInfo.Szhiji.toString(), val.userInfo.zhiji.toString()] : [] // 职级
        state.baseInfo.userInfo.EducationType = val.userInfo.EducationType ? val.userInfo.EducationType : undefined // 院校性质
        if (val.jobCate) {
          treeWalk(val.jobCate, (node) => {
            node.selectable = !node.children
          })
        }
        state.jobCate = handleArray(val.jobCate)

        // 关系网
        // if (val.userInfo.ch999Relation.length === 0) {
        //   state.baseInfo.userInfo.ch999Relation.push({
        //     relationType: '',
        //     typeName: '',
        //     ch999_id: '',
        //     ch999_name: '',
        //   })
        // }
        state.dimissionOptions = val?.quitTagList?.map(it => ({
          label: it.Key,
          value: it.Value + '',
          disabled: it.Value === 3
        })) || []
        state.baseInfo.userInfo.QuitTag = val?.userInfo?.QuitTag || undefined
        // 数据分类 1-教育经历 2-外部工作经历 4-培训经历 5-关键事件 6-紧急联系人
        if (val.experience) {
          if (val.experience.length > 0) {
            let education = []
            let work = []
            let training = []
            let emergencyContacts = []
            let familyList = []
            let keyEvents = []
            val.experience.forEach(item => {
              if (item.kind === 1) {
                education.push(item)
              } else if (item.kind === 2) {
                work.push(item)
              } else if (item.kind === 4) {
                training.push(item)
              } else if (item.kind === 5) {
                keyEvents.push(item)
              } else if (item.kind === 6) {
                emergencyContacts.push(item)
              } else if (item.kind === 7) {
                familyList.push(item)
              }
            })
            if (education.length > 0) {
              state.educationTableData = education
            }
            if (work.length > 0) {
              state.workTableData = work
            }
            if (training.length > 0) {
              state.trainingTableData = training
            }
            if (emergencyContacts.length > 0) {
              state.emergencyContacts = emergencyContacts
            }
            if (familyList.length > 0) {
              state.familyList = familyList
            }
            if (keyEvents.length > 0) {
              state.keyEvents = keyEvents
            }
          }
        }
      }
      const handleArray = (data) => {
        // 处理字段名
        if (data && data.length > 0) {
          data.forEach((child) => {
            child.key = child.id
            child.value = child.id
            child.title = child.label
            if (child.children && child.children.length > 0) {
              handleArray(child.children)
            }
          })
          return data
        }
        return []
      }
      const inDateChange = (date, dateValue) => {
        // 入职时间
        state.baseInfo.userInfo.indate = dateValue ? (dateValue + ' 09:00:00') : null
      }
      const zhuanzhendateChange = (date, dateValue) => {
        // 转正时间
        state.baseInfo.userInfo.zhuanzhendate = dateValue
      }
      const offtimeChange = (date, dateValue) => {
        // 离职时间
        state.baseInfo.userInfo.offtime = dateValue
      }
      const workhtdateChange = (date, dateValue) => {
        // 劳动合同到期时间
        state.baseInfo.userInfo.workhtdate = dateValue
      }
      const birsdayChange = (date, dateValue) => {
        // 生日
        state.baseInfo.userInfo.birsday = dateValue
        state.userAge = calculateAge(state.baseInfo.userInfo.birsday)
      }
      const idEtimeChange = (date, dateValue) => {
        // 生日
        state.baseInfo.userInfo.IDEtime = dateValue
      }
      const getPositionData = () => { // 获取职级数据
        $api.user.getPositionRealData().then(res => {
          if (res.code === 0) {
            state.zhijiData = res.data
          } else {
            $message.error(res.userMsg)
          }
        })
      }
      getPositionData()
      const getDepartRoleTreeData = () => {
        // 获取部门
        $api.common.getAreaTree('area').then((res) => {
          if (res.code === 0) {
            state.areaTree = handleArray(res.data.areaOptions)
            state.department = handleArray(res.data.departOptions)
          } else {
            $message.error(res.userMsg)
          }
        })
      }
      getDepartRoleTreeData()
      const getZhiji = (val) => {
        // 职级
        if (val && val.length > 1) {
          state.baseInfo.userInfo.Szhiji = val[0]
          state.baseInfo.userInfo.zhiji = val[1]

          // 职级改变，计算年终奖系数
          state.baseInfo.zhijiXishu.forEach(item => {
            if (item.pid === val[0] * 1 && item.id === val[1] * 1) {
              state.baseInfo.userInfo.zjxishu = item.xishu
              state.baseInfo.userInfo.j_xishu = state.baseInfo.userInfo.zwxishu * 1 + state.baseInfo.userInfo.zjxishu * 1
            }
          })
        } else {
          state.baseInfo.userInfo.Szhiji = null
          state.baseInfo.userInfo.zhiji = null
        }
      }
      const filterOption = (input, option) => {
        // 搜索过滤
        return (
          option.componentOptions.children[0].text
            .toLowerCase()
            .indexOf(input.toLowerCase()) >= 0
        )
      }
      const saveInterestTags = () => {
        // 保存兴趣标签
        if (state.interestTags.selected.length > 3) {
          $message.error('兴趣标签最多只能选择3个')
          return
        }
        let tags = state.interestTags.selected.join(',')
        state.interestTagsBtnLoading = true
        $api.user.saveInterestTags(tags).then((res) => {
          if (res.code === 0) {
            $message.success(res.userMsg)
          } else {
            $message.error(res.userMsg)
          }
        }).finally(() => {
          state.interestTagsBtnLoading = false
        })
      }
      const getInterestTags = (id) => {
        // 获取兴趣爱好
        $api.user.getInterestTags(id).then((res) => {
          if (res.code === 0) {
            state.interestTags = res.data
          } else {
            $message.error(res.userMsg)
          }
        })
      }
      getInterestTags(state.baseInfo.userInfo.ch999_id)
      const openPerformanceVis = (performanceVisTit, item) => {
        state.performanceVisTit = performanceVisTit
        state.performanceVis = true
        if (item && item.achievements && item.achievements.length) {
          state.userYearAchievementModal = item.achievements
        }
      }
      const displayRender = ({ labels }) => {
        if (!labels || !labels.length) return ''
        return labels.slice(-1)[0]
      }
      const scheduleIdChange = (val) => {
        $set(state.baseInfo, 'userSchedule', val)
        $set(state.baseInfo.userInfo, 'scheduleId', state.baseInfo.userSchedule.slice(-1)[0])
      }
      const tabsChange = (item) => {
        state.tabsActiveIndex = item.value
        state[item.idName.replace('Wrap', 'Show')] = true
        nextTick(() => {
          let view = document.querySelector('#' + item.idName)
          const distanceFromTop = view.getBoundingClientRect().top - 138 // 获取目标元素与视口顶部之间的距离
          const currentPosition = document.querySelector('#overWrap').scrollTop
          const wrapper = document.querySelector('#overWrap')
          wrapper.scrollTo({
            top: distanceFromTop + currentPosition,
            behavior: 'smooth'
          })
        })
      }
      const addEducation = () => {
        const lastArr = state.educationTableData.slice(-1)
        if (lastArr.length) {
          const { Education, stime, etime, place, EducationType, DegreeTeachingMethod } = lastArr[0]
          if (!Education || !stime || !etime || !place || !EducationType || !DegreeTeachingMethod) {
            $message.warn('请完善上一条信息')
            return
          }
        }
        state.educationTableData.push({
          id: 0,
          kind: 1,
          stime: undefined,
          etime: undefined,
          place: undefined,
          major: undefined,
          mainDuty: undefined,
          zhicheng: undefined,
          ch999_id: $route.query.id || $store.state.userInfo.UserID,
          Education: undefined,
          EduLevel: undefined,
          DegreeTeachingMethod: undefined,
          EducationTypeStr: undefined,
          EducationType: undefined,
          files: [],
        })
      }
      const addRelation = () => {
        const lastArr = state.baseInfo.userInfo.ch999Relation.slice(-1)
        if (lastArr.length) {
          const { ch999_id: ch999Id, relationType } = lastArr[0]
          if (!ch999Id || !relationType) {
            $message.warn('请完善上一条信息')
            return
          }
        }
        state.baseInfo.userInfo.ch999Relation.push({
          relationType: undefined,
          typeName: undefined,
          ch999_id: undefined,
          ch999_name: undefined
        })
      }
      const addRelativesRaceWork = () => {
        const lastArr = state.baseInfo.relativesRaceWork.slice(-1)
        if (lastArr.length) {
          const { relationship, companyName, postName } = lastArr[0]
          if (!relationship || !companyName || !postName) {
            $message.warn('请完善上一条信息')
            return
          }
        }
        state.baseInfo.relativesRaceWork.push({
          id: 0,
          relationship: undefined,
          relationshipName: undefined,
          companyName: undefined,
          postName: undefined,
          ch999_id: $route.query.id || $store.state.userInfo.UserID,
        })
      }
      const interViewChange = (keys, outputObject) => {
        state.interviewSelect.interviewer = outputObject.staffName
      }
      const addInterview = (item) => {
        console.log(item)
        state.interviewVis = true
        if (item) {
          state.interviewSelect = { ...item }
        } else {
          state.interviewSelect = {
            id: undefined,
            interviewDate: undefined,
            ch999State: undefined,
            jobMatch: undefined,
            outputSalaryMatch: undefined,
            interviewer: undefined,
            interviewerId: undefined,
            interviewContent: undefined
          }
        }
      }
      const addInterviewHandle = () => {
        interViewForm.value.validate(val => {
          if (val) {
            let params = [{ ...state.interviewSelect }]
            params[0].interviewDate = moment(params[0].interviewDate).format('YYYY-MM-DD')
            params[0].ch999Id = params[0].ch999Id || $route.query.id
            $indicator.open()
            $api.user.interviewBatchAdd(params).then(async res => {
              if (res.code === 0) {
                const msg = state.interviewSelect.id ? '编辑面谈记录成功' : '新增面谈记录成功'
                $message.success(msg)
                interviewVisHide()
                await interviewBatchList()
              } else {
                $message.error(res.userMsg)
              }
            }).finally(() => {
              $indicator.close()
            })
          }
        })
      }
      const interviewVisHide = () => {
        state.interviewVis = false
        state.interviewSelect = {
          id: undefined,
          interviewDate: undefined,
          ch999State: undefined,
          jobMatch: undefined,
          outputSalaryMatch: undefined,
          interviewer: undefined,
          interviewerId: undefined,
          interviewContent: undefined
        }
        interViewForm.value.resetFields()
      }
      const interviewBatchList = () => {
        let params = {
          ch999Id: $route.query.id || $store.state.userInfo.UserID,
        }
        $api.user.interviewBatchList(params).then(res => {
          if (res.code === 0) {
            state.interview = res.data
          } else {
            $message.error(res.userMsg)
          }
        })
      }
      interviewBatchList()
      const interviewBatchDelete = (item) => {
        let params = {
          id: item.id
        }
        $api.user.interviewBatchDelete(params).then(res => {
          if (res.code === 0) {
            $message.success('删除面谈记录成功')
            interviewBatchList()
          } else {
            $message.error(res.userMsg)
          }
        })
      }
      const delInterview = (item, index) => {
        if (item.id) {
          interviewBatchDelete(item)
        } else {
          state.interview.splice(index, 1)
        }
      }
      const getInventoryInfo = () => {
        let params = {
          staffId: $route.query.id || $store.state.userInfo.UserID,
        }
        $api.user.getInventoryInfo(params).then(res => {
          if (res.code === 0) {
            state.talentInventory = res.data
          } else {
            $message.error(res.userMsg)
          }
        })
      }
      getInventoryInfo()
      const addTalentInventory = (item) => {
        console.log(item)
        state.talentInventoryVis = true
        if (item) {
          state.talentInventorySelect = {
            comment: item.comment,
            id: item.id,
            inventoryDate: item.inventoryDate,
            kind: item.kind,
            nextStageAdvice: item.nextStageAdvice,
            attachmentList: item.attachmentList,
            participateStaffIds: item.participateStaffIds ? item.participateStaffIds.split(',').map(Number) : undefined,
            score: item.score,
            potentialScore: item.potentialScore,
            highPotentialScore: item.highPotentialScore,
            rating: item.rating,
          }
        } else {
          state.talentInventorySelect = {
            comment: undefined,
            id: undefined,
            inventoryDate: undefined,
            kind: undefined,
            participateStaffIds: undefined,
            nextStageAdvice: undefined,
            attachmentList: undefined,
            score: undefined,
            potentialScore: undefined,
            highPotentialScore: undefined,
            rating: undefined,
          }
        }
      }
      const addTalentInventoryHandle = () => {
        talentInventoryForm.value.validate(val => {
          if (val) {
            const { score, potentialScore, highPotentialScore, rating } = state.talentInventorySelect
            if (!score && !potentialScore && !highPotentialScore && !rating) {
              $message.warn('胜任力评分、潜力评分、高潜评分、季度评级至少需要填写一个字段，请填写后再保存')
              return
            }
            let params = { ...state.talentInventorySelect }
            params.inventoryDate = moment(params.inventoryDate).format('YYYY-MM-DD')
            params.staffId = params.staffId || $route.query.id
            if (params.participateStaffIds && params.participateStaffIds.length) {
              params.participateStaffIds = params.participateStaffIds.join(',')
            } else {
              params.participateStaffIds = ''
            }
            if (params.attachmentList && params.attachmentList.length) {
              params.attachmentList = params.attachmentList.map(item => {
                return {
                  fid: item.fid,
                  fileName: item.fileName,
                  filePath: item.fileUrl || item.filePath,
                }
              })
            }
            console.log('params', params)
            $indicator.open()
            $api.user.saveOrUpdateInventoryScore(params).then(async res => {
              if (res.code === 0) {
                const msg = state.talentInventorySelect.id ? '编辑盘点记录成功' : '新增盘点记录成功'
                $message.success(msg)
                talentInventoryVisHide()
                await getInventoryInfo()
              } else {
                $message.error(res.userMsg)
              }
            }).finally(() => {
              $indicator.close()
            })
          }
        })
      }
      const talentInventoryVisHide = () => {
        state.talentInventoryVis = false
        state.talentInventorySelect = {
          comment: undefined,
          id: undefined,
          inventoryDate: undefined,
          score: undefined,
          potentialScore: undefined,
          highPotentialScore: undefined,
          rating: undefined,
          kind: undefined,
          participateStaffIds: undefined,
          nextStageAdvice: undefined,
          attachmentList: undefined,
        }
        talentInventoryForm.value.resetFields()
      }
      const talentInventoryBatchDelete = (item) => {
        let params = {
          id: item.id
        }
        $api.user.removeInventoryScore(params).then(res => {
          if (res.code === 0) {
            $message.success('删除盘点记录成功')
            getInventoryInfo()
          } else {
            $message.error(res.userMsg)
          }
        })
      }
      const addKeyEvents = () => {
        const lastArr = state.keyEvents.slice(-1)
        if (lastArr.length) {
          const { major, stime, mainDuty } = lastArr[0]
          if (!major || !stime || !mainDuty) {
            $message.warn('请完善上一条信息')
            return
          }
        }
        state.keyEvents.push({
          id: 0,
          kind: 5,
          stime: undefined,
          etime: undefined,
          place: undefined,
          major: undefined,
          mainDuty: undefined,
          zhicheng: undefined,
          ch999_id: $route.query.id || $store.state.userInfo.UserID,
          Education: undefined,
          EduLevel: undefined,
          DegreeTeachingMethod: undefined,
          EducationTypeStr: undefined,
          EducationType: undefined,
          files: []
        })
      }
      const addTraining = () => {
        const lastArr = state.trainingTableData.slice(-1)
        if (lastArr.length) {
          const { place, stime, etime, major, mainDuty } = lastArr[0]
          if (!place || !stime || !etime || !major || !mainDuty) {
            $message.warn('请完善上一条信息')
            return
          }
        }
        state.trainingTableData.push({
          id: 0,
          kind: 4,
          stime: undefined,
          etime: undefined,
          place: undefined,
          major: undefined,
          mainDuty: undefined,
          zhicheng: undefined,
          ch999_id: $route.query.id || $store.state.userInfo.UserID,
          Education: undefined,
          EduLevel: undefined,
          DegreeTeachingMethod: undefined,
          EducationTypeStr: undefined,
          EducationType: undefined,
          files: []
        })
      }
      const addWork = () => {
        const lastArr = state.workTableData.slice(-1)
        if (lastArr.length) {
          const { stime, etime, place, major, mainDuty, zhicheng } = lastArr[0]
          if (!stime || !etime || !place || !major || !mainDuty || !zhicheng) {
            $message.warn('请完善上一条信息')
            return
          }
        }
        state.workTableData.push({
          id: 0,
          kind: 2,
          stime: undefined,
          etime: undefined,
          place: undefined,
          major: undefined,
          mainDuty: undefined,
          zhicheng: undefined,
          ch999_id: $route.query.id || $store.state.userInfo.UserID,
          Education: undefined,
          EduLevel: undefined,
          DegreeTeachingMethod: undefined,
          EducationTypeStr: undefined,
          EducationType: undefined,
          files: []
        })
      }
      const addEmergency = () => {
        const lastArr = state.emergencyContacts.slice(-1)
        if (lastArr.length) {
          const { place, major, zhicheng, mainDuty } = lastArr[0]
          if (!place || !major || !zhicheng || !mainDuty) {
            $message.warn('请完善上一条信息')
            return
          }
        }
        state.emergencyContacts.push({
          id: 0,
          kind: 6,
          stime: undefined,
          etime: undefined,
          place: undefined,
          major: undefined,
          mainDuty: undefined,
          zhicheng: undefined,
          ch999_id: $route.query.id || $store.state.userInfo.UserID,
          Education: undefined,
          EduLevel: undefined,
          DegreeTeachingMethod: undefined,
          EducationTypeStr: undefined,
          EducationType: undefined,
          files: [],
          provinceId: undefined,
          cityId: undefined,
          countyId: undefined,
        })
      }
      const addFamily = () => {
        const lastArr = state.familyList.slice(-1)
        if (lastArr.length) {
          const { place, major, zhicheng, mainDuty } = lastArr[0]
          if (!place || !major || !zhicheng || !mainDuty) {
            $message.warn('请完善上一条信息')
            return
          }
        }
        state.familyList.push({
          id: 0,
          kind: 7,
          place: undefined,
          major: undefined,
          mainDuty: undefined,
          zhicheng: undefined,
          ch999_id: $route.query.id || $store.state.userInfo.UserID
        })
      }
      const educationTime = (dates, dateStrings, index, item) => {
        $set(state.educationTableData[index], 'stime', dateStrings[0])
        $set(state.educationTableData[index], 'etime', dateStrings[1])
        educationChange(item, index)
      }
      const trainingTime = (dates, dateStrings, index) => {
        $set(state.trainingTableData[index], 'stime', dateStrings[0])
        $set(state.trainingTableData[index], 'etime', dateStrings[1])
      }
      const workTime = (dates, dateStrings, index) => {
        $set(state.workTableData[index], 'stime', dateStrings[0])
        $set(state.workTableData[index], 'etime', dateStrings[1])
      }
      const delEmergency = (index) => {
        state.emergencyContacts.splice(index, 1)
      }
      const delFamily = (index) => {
        state.familyList.splice(index, 1)
      }
      const delEducation = (index) => {
        state.educationTableData.splice(index, 1)
      }
      const delKeyEvents = (index) => {
        state.keyEvents.splice(index, 1)
      }
      const delTrainingTableData = (index) => {
        state.trainingTableData.splice(index, 1)
      }
      const delWorkTableData = (index) => {
        state.workTableData.splice(index, 1)
      }
      const delCh999Relation = (index) => {
        state.baseInfo.userInfo.ch999Relation.splice(index, 1)
      }
      const delRelativesRaceWork = (index) => {
        state.baseInfo.relativesRaceWork.splice(index, 1)
      }
      const dcityidChange = (val) => {
        // 现住址
        state.baseInfo.userInfo.dcityid = val || []
      }
      const cityidChange = (val) => {
        // 家庭住址
        state.baseInfo.userInfo.cityid = val || []
      }
      const saveUserHomeInfo = () => {
        // 员工保存自己家庭地址信息
        if (
          !state.baseInfo.userInfo.cityid ||
          state.baseInfo.userInfo.cityid.length === 0 ||
          !state.baseInfo.userInfo.homeaddress ||
          !state.baseInfo.userInfo.homeaddrqu ||
          !state.baseInfo.userInfo.homeaddrdoor ||
          !state.baseInfo.userInfo.homeaddrrom
        ) {
          $message.error('请完整填写家庭详细地址')
          return
        }
        // if (
        //   !state.baseInfo.userInfo.dcityid ||
        //   state.baseInfo.userInfo.dcityid.length === 0 ||
        //   !state.baseInfo.userInfo.address
        // ) {
        //   $message.error('请完整填写详细住址')
        //   return
        // }
        let params = handleJson(state.baseInfo.userInfo)
        state.userInfoBtnLoading = true
        $api.user.saveUserHomeInfo(params)
          .then((res) => {
            if (res.code === 0) {
              $message.success(res.userMsg)
              emit('update')
            } else {
              $message.error(res.userMsg)
            }
          })
          .finally(() => {
            state.userInfoBtnLoading = false
          })
      }
      const saveUserBaseInfo = () => {
        let str = ''
        state.baseInfo.graduateTags?.forEach((item) => {
          str += item.id + ','
        })
        state.baseInfo.userInfo.graduateTags = str.slice(0, str.length - 1)
        let params = { ...handleJson(state.baseInfo.userInfo) }
        params.experience = [...state.educationTableData, ...state.trainingTableData, ...state.workTableData, ...state.emergencyContacts, ...state.familyList, ...state.keyEvents]
        params.is_race_work = state.baseInfo.ch999_userEx.is_race_work
        if (state.baseInfo.ch999_userEx.postUserId) {
          params.postUserId = state.baseInfo.ch999_userEx.postUserId
        }
        params.englishLevel = state.baseInfo.ch999_userEx.englishLevel
        if (!params.area1id) {
          checkIsRequire('tabs1Wrap', 'tabs1Show', 1)
          return $message.warning('请选择地区！')
        }
        if (!params.departCode) {
          checkIsRequire('tabs1Wrap', 'tabs1Show', 1)
          return $message.warning('请选择部门！')
        }
        if (!params.indate) {
          checkIsRequire('tabs1Wrap', 'tabs1Show', 1)
          return $message.warning('请选择入职时间！')
        }
        if (params.indate && params.zhuanzhendate && params.indate >= params.zhuanzhendate) {
          checkIsRequire('tabs1Wrap', 'tabs1Show', 1)
          return $message.warning('转正日期不能早于入职日期，请修改后重试')
        }
        if (!params.mainRole) {
          checkIsRequire('tabs1Wrap', 'tabs1Show', 1)
          return $message.warning('请选择岗位！')
        }
        if (!params.positionType) {
          checkIsRequire('tabs1Wrap', 'tabs1Show', 1)
          return $message.warning('请选择岗位类型！')
        }
        if (!params.mobile) {
          return $message.warning('请输入手机号！')
        }
        if (params.usersex === null || params.usersex === '' || params.usersex === undefined) {
          checkIsRequire('tabs2Wrap', 'tabs2Show', 2)
          return $message.warning('请选择性别！')
        }
        if (!params.nation) {
          checkIsRequire('tabs2Wrap', 'tabs2Show', 2)
          return $message.warning('请输入民族！')
        }
        if (!params.dressSizeSummer) {
          checkIsRequire('tabs12Wrap', 'tabs12Show', 12)
          return $message.warning('请选择夏装工服尺寸！')
        }
        if (!params.dressSize) {
          checkIsRequire('tabs12Wrap', 'tabs12Show', 12)
          return $message.warning('请选择冬装工服尺寸！')
        }
        if (state.baseInfo.ch999_userEx.nativeProvinceId && state.baseInfo.ch999_userEx.nativeCityId && state.baseInfo.ch999_userEx.nativeCountyId) {
          params.nativeProvinceId = state.baseInfo.ch999_userEx.nativeProvinceId
          params.nativeCityId = state.baseInfo.ch999_userEx.nativeCityId
          params.nativeCountyId = state.baseInfo.ch999_userEx.nativeCountyId
        }
        params.newVersion = true
        $indicator.open()
        $api.user.saveUserBaseInfo(params).then(res => {
          if (res.code === 0) {
            emit('update')
            if (params.islogin) {
              root.$refs.deleteUserModalRef?.toDeleteUser()
            }
            $message.success(res.userMsg)
          } else {
            $message.error(res.userMsg)
          }
        }).finally(() => {
          $indicator.close()
        })
      }
      const checkIsRequire = (id, flag, value) => {
        state.tabsActiveIndex = value
        state[flag] = true
        let view = document.querySelector('#' + id)
        const distanceFromTop = view.getBoundingClientRect().top - 70 // 获取目标元素与视口顶部之间的距离
        const currentPosition = document.querySelector('#overWrap').scrollTop
        const wrapper = document.querySelector('#overWrap')
        wrapper.scrollTo({
          top: distanceFromTop + currentPosition,
          behavior: 'smooth'
        })
      }
      const calculateAge = (birthday) => {
        if (birthday) {
          const startDate = new Date()
          const endDate = new Date(birthday)
          return Math.abs(moment.duration(endDate - startDate).years())
        } else {
          return '请选择生日'
        }
      }
      const changeDepart = (value, node) => {
        state.baseInfo.userInfo.departCode = value
      }
      const businessChange = (keys, outputObject, originObj, option) => {
        state.baseInfo.userInfo.businessTrainCh999Name = outputObject.staffName
      }
      const cultureChange = (keys, outputObject, originObj, option) => {
        state.baseInfo.userInfo.cultureTrainCh999Name = outputObject.staffName
      }
      async function getCh999UserEnumList () {
        let { code, data } = await $api.office.staffList.getCh999UserEnumList()
        if (code === 0) {
          state.relation = data.relationTypeEnum || []
        }
      }
      getCh999UserEnumList()
      const formatWord = function (val) {
        return val.replace(/\n/g, '<br>')
      }
      const inventoryResultGetEnums = () => {
        $api.user.inventoryResultGetEnums().then(res => {
          if (res.code === 0) {
            state.inventoryKind = res.data.inventoryKind || []
          }
        })
      }
      inventoryResultGetEnums()
      watch(() => props.info, (newVal, oldVal) => {
        state.baseInfo = cloneDeep(props.info)
        if (state.baseInfo) {
          if (state.baseInfo.userInfo) state.userAge = state.baseInfo.userInfo.birsday ? calculateAge(state.baseInfo.userInfo.birsday) : undefined
          handleData(newVal)
        }
      }, {
        deep: true
      })

      if (state.baseInfo) {
        if (state.baseInfo.userInfo) state.userAge = state.baseInfo.userInfo.birsday ? calculateAge(state.baseInfo.userInfo.birsday) : undefined
        handleData(state.baseInfo)
      }

      watch(() => state.baseInfo.ch999_userEx.is_race_work, (newVal, oldVal) => {
        if (newVal === true && oldVal === false && !state.baseInfo.relativesRaceWork.length) {
          state.baseInfo.relativesRaceWork.push({
            id: 0,
            relationship: undefined,
            relationshipName: undefined,
            companyName: undefined,
            postName: undefined,
            ch999_id: $route.query.id || $store.state.userInfo.UserID,
          })
        }
        if (newVal === false && oldVal === true) {
          state.baseInfo.relativesRaceWork = []
        }
      }, {
        immediate: true,
        deep: true
      })

      const jiguanCityidChange = function (val) {
        state.baseInfo.ch999_userEx.nativeProvinceId = val.length ? val[0] : null
        state.baseInfo.ch999_userEx.nativeCityId = val.length ? val[1] : null
        state.baseInfo.ch999_userEx.nativeCountyId = val.length ? val[2] : null
      }
      const jinjiidChange = function (val, index) {
        state.emergencyContacts[index].provinceId = val.length ? val[0] : null
        state.emergencyContacts[index].cityId = val.length ? val[1] : null
        state.emergencyContacts[index].countyId = val.length ? val[2] : null
      }
      const isValidEmail = (email) => {
        if (email) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
          return emailRegex.test(email)
        } else {
          return false
        }
      }
      const emailChange = () => {
        const result = isValidEmail(state.baseInfo.userInfo.email)
        if (!result) {
          $message.error('请输入正确的邮箱格式！')
          state.baseInfo.userInfo.email = undefined
        }
      }
      function changeIsLogin (val) {
        val && (root.$refs.deleteUserModalRef?.open())
      }
      return {
        moment,
        ...toRefs(state),
        changeIsLogin,
        inDateChange,
        zhuanzhendateChange,
        offtimeChange,
        getZhiji,
        filterOption,
        birsdayChange,
        workhtdateChange,
        idEtimeChange,
        saveInterestTags,
        openPerformanceVis,
        displayRender,
        scheduleIdChange,
        tabsChange,
        addEducation,
        educationTime,
        addRelation,
        addRelativesRaceWork,
        addEmergency,
        addKeyEvents,
        trainingTime,
        addTraining,
        workTime,
        addWork,
        delEmergency,
        delEducation,
        delKeyEvents,
        delTrainingTableData,
        delWorkTableData,
        delCh999Relation,
        delRelativesRaceWork,
        dcityidChange,
        cityidChange,
        saveUserBaseInfo,
        changeDepart,
        EducationTypeListOptions,
        saveUserHomeInfo,
        isSticky,
        tabs,
        addInterview,
        delInterview,
        addInterviewHandle,
        businessChange,
        cultureChange,
        interViewChange,
        interviewVisHide,
        interViewForm,
        interViewRules,
        formatWord,
        interviewColumns,
        educationDisabled,
        educationChange,
        talentInventoryColumns,
        talentInventoryForm,
        talentInventoryRules,
        addTalentInventory,
        addTalentInventoryHandle,
        talentInventoryVisHide,
        talentInventoryBatchDelete,
        addFamily,
        delFamily,
        jiguanCityidChange,
        registerNaturOptions,
        marryOptions,
        jinjiidChange,
        englishLeaveOptions,
        emailChange,
        quarterOptions
      }
    }
  }
</script>

<style lang="scss" scoped>
.base-info {
  .tabs-list-com {
    padding: 20px;
    position: sticky;
    top: 0;
    background: #FFFFFF;
    margin: 0 -20px;
    z-index: 99;
    .tabs-list-item {
      cursor: pointer;
      margin-right: 20px;
      padding: 4px 13px;
      background: #f5f5f5;
      border-radius: 50px;
      border: 1px solid #f5f5f5;
    }
    .tabs-list-item-active{
      color: #1890FF;
      border: 1px solid #1890FF;
      background: rgba(35,157,252,0.1);
    }
    &.box-shadow{
      box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05);
    }
  }
  .list-wrap{
    margin-bottom: 16px;
    .list-wrap-top{
      cursor: pointer;
      padding: 8px 20px;
      background: rgba(24,144,255,0.05);
      border-radius: 8px;
      .left-ico {
        width: 20px;
        height: 20px;
        margin-right: 10px;
      }
    }
    .list-wrap-con {
      padding: 20px 20px 14px 20px;
      .performance-wrap {
        width:128px;
        border: 1px solid #E7F3FF;
        text-align: center;
        margin-right: 20px;
        border-radius: 4px;
        margin-bottom: 36px;
        .top {
          height: 40px;
          line-height: 40px
        }
        .bottom {
          height: 40px;
          line-height: 40px;
          background: rgba(24,144,255,0.1);
        }
      }
    }
  }
}

.performanceDot {
  width: 6px;
  height: 6px;
  background: #1890FF;
  border-radius: 100%;
  margin-right: 10px;
  margin-top: -2px;
}

.mb-24 {
  margin-bottom: 24px;
}
.spanRotate {
  transform: rotate(180deg);
  transition: transform .5s;
}
.spanReset {
  transform: rotate(0deg);
  transition: transform .5s;
}

.footer-btn-wrap{
  padding: 12px 20px;
  position: fixed;
  bottom: 0;
  left: 0;
  margin-top: 20px;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  width: 100%;
  z-index: 99;
  .footer-btn {
    cursor: pointer;
    width: 82px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 4px;
    border: 1px solid #DEDEDE;
  }
  .footer-btn-sure {
    color: #FFFFFF;
    background: #1890FF;
  }
}

.label-title {
  width: 148px;
  text-align: right;
  flex-shrink: 0;
}

.companyIco-title {
  width: 190px;
}

.col-blue {
  color: #1890ff;
}

.color_82 {
  color: #828282;
}

.aRowSpacing {
  margin-right: 30px;
}

.mr-60 {
  margin-right: 60px;
}

.mb-36{
  margin-bottom: 36px
}
.mb-30{
  margin-bottom: 30px;
}

.add-btn {
  padding: 8px 16px;
  background: #E6F3FF;
  border-radius: 30px;
  font-size: 14px;
  font-weight: 400;
  color: #239DFC;
  cursor: pointer;
  .add-btn-img {
    vertical-align: middle;
    margin-top: -3px;
    margin-right: 2px;
  }
}

.performanceModel {
  .title {
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #EAF5FF;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
    color: #333333;
  }
  :deep(.ant-modal-body) {
    padding: 20px 24px;
  }
}

.performanceVis-wrap {
  display: flex;
  flex-wrap: wrap;
  .performanceVis-item{
    width: 25%;
    color: #333333;
    text-align: center;
    margin: 16px 0 24px 0;
  }
}

.h-32 {
  height: 32px;
}

.required {
  position: relative;
  &::after {
    position: absolute;
    content: '*';
    color: red;
    right: -16px;
    top: 6px;
    font-size: 20px;
  }
}

.required1 {
  position: absolute;
  left: 50px;
  font-size: 20px;
  color: red;
  top: 2px;
}

.required-interview {
  position: relative;
  &::after {
    position: absolute;
    content: '*';
    color: red;
    right: -16px;
    top: 20%;
    font-size: 20px;
  }
}

.interview-textarea {
  :deep(.ant-input-textarea-clear-icon) {
    top: 42%;
    right: 16px;
    margin: 0;
  }
}

.interview-table {
  margin: 14px 0 20px 0;
  :deep(.ant-table-thead > tr > th) {
    font-size: 14px;
    font-weight: 600;
  }
}

.interviewModel {
  :deep(.ant-form-item-label) {
    width: 180px;
  }
  :deep(.ant-form-item-control-wrapper) {
    width: 280px;
  }
  .interview-textarea-wrap {
    :deep(.ant-form-item-control-wrapper) {
      width: 755px;
    }
  }
  :deep(textarea){
    height: 100px;
    overflow-y: auto;
    resize: none;
  }
  :deep(.ant-form-inline .ant-form-item-with-help) {
    margin: 0;
  }
}

.talentInventory-wrap {
  padding: 0 30px;
  margin-bottom: 30px;
  .text1 {
    width: 100px;
    color: #828282;
  }
  .text2 {
    color: #555;
  }
  .text3 {
    color: #000;
    font-weight: 500;
    word-break: break-all;
  }
  .talentInventory-text-wrap {
    min-width: 230px;
    margin-right: 20px;
    line-height: 35px;
  }
}
</style>
<style lang="scss">
.interviewPoperLayTable > .ant-popover-content > .ant-popover-inner {
  width: 600px;
}
</style>
