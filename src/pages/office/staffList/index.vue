<template>
  <div style="margin-top: 10px">
    <div class="staff-header">
      <span class="title">工作人员列表</span>
      <a-button type="link" title="点击进入人员异动列表页面" @click="jumpToNewWin('/staticpc/#/human-resources/personnel-change')">人员异动列表<a-icon type="right" v-if="$tnt.xtenant < 1000"/></a-button>
      <a-button type="danger" size="small" v-if="$tnt.xtenant < 1000" @click="jumpToNewWin('/Ch999User/ch999List', 'replace')">回到旧版<a-icon type="right" /></a-button>
    </div>

    <div>
      <div class="tab-header">
        <div class="tab tab-m" :class="activeTab === 1 ? 'active' : ''" @click="changeTab(1)">在职人员列表</div>
        <div class="tab" :class="activeTab === 0 ? 'active' : ''" @click="changeTab(0)">离职人员列表</div>
      </div>
      <ni-list-page :pushFilterToLocation="false">
        <ni-filter
          :labelWidth="100"
          :form="searchData"
          :loading="loading"
          :unfoldCount="9"
          :settingAble="true"
          @filter="getStaffListPage"
          @reset="clearSearchData"
          :do-quick-search-when-filter="false"
          @quickSearchClick="quickSearchClick"
        >
          <ni-filter-item label="部门">
            <ni-depart-select
              v-model:value="searchData.departIds"
              placeholder="请选择部门"
              :multiple="true"
              returnedValue="ALL"
              @change="selectDepart"
            />
          </ni-filter-item>
          <ni-filter-item label="学历">
            <a-select allowClear v-model="searchData.education" placeholder="请选择学历" :options="searchConfig.EDUCATION"></a-select>
          </ni-filter-item>
          <ni-filter-item v-if="showSearch && $tnt.xtenant < 1000" label="院校性质">
            <a-select allowClear mode="multiple" placeholder="请选择院校性质" v-model="searchData.schoolType" :options="searchConfig.colleagueNatureEnum"></a-select>
          </ni-filter-item>
          <ni-filter-item label="本科类别" v-if="searchData.education === 5">
            <a-select allowClear v-model="searchData.eduLevel" placeholder="请选择本科类别" :options="searchConfig.eduLevelEnum"></a-select>
          </ni-filter-item>
          <ni-filter-item label="职位">
            <a-tree-select
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              v-model="searchData.positions"
              :tree-data="searchConfig.dutyList"
              tree-checkable
              allowClear
              :maxTagCount="3"
              treeNodeFilterProp="label"
              :replaceFields="{ children: 'children', title: 'name', value: 'id' }"
              placeholder="请选择职位"
            />
          </ni-filter-item>
          <ni-filter-item label="职级">
            <a-tree-select
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              v-model="searchData.childRanks"
              :tree-data="searchConfig.userPosition"
              tree-checkable
              allowClear
              treeNodeFilterProp="label"
              :maxTagCount="3"
              :replaceFields="{ children: 'children', title: 'lable', value: 'value' }"
              placeholder="请选择职级"
            />
          </ni-filter-item>
          <ni-filter-item label="员工类别">
            <a-select mode="multiple" allowClear v-model="searchData.staffType" placeholder="请选择员工类别" :options="searchConfig.staffTypeEnum"></a-select>
          </ni-filter-item>
          <ni-filter-item label="地区">
            <!-- <ni-area-select
              v-model="searchData.areaIds"
              :allow-clear="true"
              id="search-area-id"
              multiple
              show-search
              placeholder="请选择地区"
              :max-tag-count="2"
            /> -->
            <a-tree-select
              :dropdown-style="{maxHeight: '400px'}"
              v-model="searchData.areaIds"
              :allow-clear="true"
              tree-checkable
              treeNodeFilterProp="label"
              :treeData="searchConfig.LimitedAreaData"
              multiple
              show-search
              placeholder="请选择地区"
              :max-tag-count="2"
            />
          </ni-filter-item>
          <ni-filter-item label="门店级别">
            <a-select allowClear v-model="searchData.storeLevel" placeholder="请选择门店级别" :options="searchConfig.SHOPSTYPE"></a-select>
          </ni-filter-item>
          <ni-filter-item label="姓名">
            <!-- <ni-staff-select allowClear v-model:value="searchData.ch999Id" placeholder="请输入姓名或工号" /> -->
            <a-select
              v-model="searchData.ch999Id"
              placeholder="请输入姓名或工号"
              style="width: 100%"
              allowClear
              :filter-option="false"
              :options="userSelect.addList"
              showSearch
              :not-found-content="userSelect.fetching ? undefined : null"
              @search="(value) => getUserList(value)"
            >
              <a-spin v-if="userSelect.fetching" slot="notFoundContent" size="small" />
            </a-select>
          </ni-filter-item>
          <ni-filter-item label="门店类别">
            <a-select allowClear v-model="searchData.storeType" placeholder="请选择门店类别" :options="searchConfig.SHOPS"></a-select>
          </ni-filter-item>

          <ni-filter-item v-if="showSearch" label="在职状态">
            <a-select allowClear showSearch optionFilterProp="children" placeholder="请选择在职状态" v-model="searchData.dateOfEmployment" :options="searchConfig.WORKINGSTATE"></a-select>
          </ni-filter-item>
          <ni-filter-item v-if="showSearch" label="招行代码">
            <a-select allowClear placeholder="请选择招行代码" v-model="searchData.cmbData">
              <a-select-option :value="1">
                √
              </a-select-option>
              <a-select-option :value="0">
                ×
              </a-select-option>
            </a-select>
          </ni-filter-item>
          <ni-filter-item v-if="showSearch" label="薪资类别">
            <a-select allowClear showSearch optionFilterProp="children" placeholder="请选择薪资类别" v-model="searchData.salaryType" :options="searchConfig.salaryTypeEnum"></a-select>
          </ni-filter-item>
          <ni-filter-item v-if="showSearch" label="五险">
            <a-select allowClear showSearch optionFilterProp="children" placeholder="请选择五险状态" v-model="searchData.fiveRisks" :options="searchConfig.INSURANCE"></a-select>
          </ni-filter-item>
          <ni-filter-item v-if="showSearch" label="登录日期">
            <a-select allowClear showSearch optionFilterProp="children" placeholder="请选择登录日期" v-model="searchData.loginDate" :options="searchConfig.LOGIN"></a-select>
          </ni-filter-item>
          <ni-filter-item v-if="showSearch" label="性别">
            <a-select allowClear showSearch optionFilterProp="children" placeholder="请选择性别" v-model="searchData.gender" :options="searchConfig.SEX"></a-select>
          </ni-filter-item>
          <ni-filter-item v-if="showSearch" label="年龄">
            <a-select allowClear showSearch optionFilterProp="children" placeholder="请选择年龄范围" v-model="searchData.age" :options="searchConfig.AGE"></a-select>
          </ni-filter-item>
          <ni-filter-item v-if="showSearch" label="承诺书">
            <a-select allowClear showSearch optionFilterProp="children" placeholder="请选择是否签署承诺书" v-model="searchData.letter" :options="searchConfig.PROMISE"></a-select>
          </ni-filter-item>
          <ni-filter-item v-if="showSearch" label="星座">
            <a-select allowClear showSearch optionFilterProp="children" placeholder="请选择星座" v-model="searchData.constellation" :options="searchConfig.SIGN"></a-select>
          </ni-filter-item>
          <ni-filter-item v-if="showSearch" label="工资发放">
            <a-select allowClear showSearch optionFilterProp="children" placeholder="请选择工资发放方式" v-model="searchData.salaryPaymentMethod" :options="searchConfig.WAGES"></a-select>
          </ni-filter-item>
          <ni-filter-item v-if="showSearch" label="工作单位">
            <a-input v-model="searchData.companyName" allowClear placeholder="请输入之前工作单位" />
          </ni-filter-item>
          <ni-filter-item v-if="showSearch" label="生日月份">
            <a-select allowClear showSearch optionFilterProp="children" placeholder="请选择生日月份" v-model="searchData.birthDayMonth" :options="searchConfig.BIRTHDAY"></a-select>
          </ni-filter-item>
          <ni-filter-item v-if="showSearch" label="籍贯">
            <a-input v-model="searchData.nativePlace" allowClear placeholder="请输入籍贯" />
          </ni-filter-item>
          <ni-filter-item v-if="showSearch" label="角色">
            <div style="display:flex">
              <a-select style="flex: 0 0 80px" v-model="searchData.roleType" :options="$tnt.xtenant < 1000 ? searchConfig.ROLEJIUJI : searchConfig.ROLE"></a-select>
              <a-select style="flex: 1"  v-if="$tnt.xtenant >= 1000" allowClear showSearch optionFilterProp="children" placeholder="请选择角色" :maxTagCount="1" mode="multiple" v-model="searchData.roles">
                <a-select-option v-for="item in searchConfig.ROLEEMG" :value="item.value" :key="item.value">
                  {{item.name}}
                </a-select-option>
              </a-select>
              <PostErmissionsRoles
                v-if="$tnt.xtenant < 1000"
                v-model="searchData.roles"
                :options="postErmissionsRoles"
                size="small"
                label-key="name"
                :multiple="true"
                clearable
                :collapse-tags="true"
                :filterable="true"
              />
            </div>
          </ni-filter-item>
          <ni-filter-item v-if="showSearch" label="岗位">
            <a-select allowClear showSearch optionFilterProp="children" placeholder="请选择岗位" v-model="searchData.mainStation" :options="searchConfig.positionEnum"></a-select>
          </ni-filter-item>
          <ni-filter-item v-if="showSearch && $tnt.xtenant >= 1000" label="院校性质">
            <a-select allowClear mode="multiple" placeholder="请选择院校性质" v-model="searchData.schoolType" :options="searchConfig.colleagueNatureEnum"></a-select>
          </ni-filter-item>
          <ni-filter-item v-if="showSearch" label="裙带关系">
            <div style="display: flex; align-items: center;">
              <a-checkbox v-model="searchData.relation">
                存在
              </a-checkbox>
              <a-select v-if="searchData.relation" allowClear showSearch optionFilterProp="children" placeholder="关系类别" style="flex:1" v-model="searchData.relationType" :options="searchConfig.RELATIONTYPE"></a-select>
            </div>
          </ni-filter-item>
          <ni-filter-item v-if="showSearch" label="入职日期">
            <a-range-picker
              v-model="searchData.entryDate"
              :disabled-date="disabledDate"
              style="width: 100%;"
            />
          </ni-filter-item>
          <ni-filter-item v-if="showSearch && $tnt.xtenant === 0" label="标签">
            <a-select allowClear showSearch mode="multiple" optionFilterProp="children" placeholder="请选择员工标签" v-model="searchData.studentLabel" :options="searchConfig.graduateTagsList"></a-select>
          </ni-filter-item>
          <ni-filter-item label="合同到期时间" v-if="showSearch">
            <a-range-picker v-model="searchData.contractTime" valueFormat="YYYY-MM-DD"/>
          </ni-filter-item>

          <ni-filter-item v-if="!showSearch" label="离职日期">
            <a-range-picker
              v-model="searchData.resignationDate"
              :disabled-date="disabledDate"
              style="width: 100%;"
            />
          </ni-filter-item>
          <ni-filter-item v-if="!showSearch" label="离职标签">
            <a-select allowClear showSearch optionFilterProp="children" placeholder="请选择离职标签" v-model="searchData.resignationLabel" :options="searchConfig.quitTagsEnum"></a-select>
          </ni-filter-item>
        </ni-filter>
        <NiTable
          :columns="COLUMNS"
          :rowKey="(record) => record.ch999Id"
          size="small"
          :bordered="true"
          :ignoreAffix="false"
          align="center"
          :loading="loading"
          :rowSelection="rank.includes('dkqz') ? { selectedRowKeys: tables.selectedRowKeys, onChange: onSelectChange } : null"
          :pagination="tables.pagination"
          :dataSource="tables.dataSource"
          @change="handleTableChange"
        >
          <div slot="action" class="flex flex-align-center flex-justify-between" style="height:36px" :class="{'is-loading': loading}">
            <div v-if="showSearch" class="flex-child-average flex flex-align-center" :style="{width: `calc(100% - ${formBoxWidth}px)`}">
              <a-checkbox v-if="rank.includes('dkqz')" class="flex-child-noshrink" style="width:62px" :checked="weightData.checked" @change="weightChange">权限</a-checkbox>
              <div class="text-left" style="overflow-x: auto;width: calc(100% - 68px)">
                <a-form style="width:1200px" layout="inline" v-if="weightData.checked" :form="weightData">
                  <a-form-item label="涉及地区">
                    <a-tree-select
                      style="width:200px"
                      :dropdown-style="{maxHeight: '400px'}"
                      v-model="weightData.areaIdList"
                      :allow-clear="true"
                      tree-checkable
                      treeNodeFilterProp="label"
                      :treeData="searchConfig.areaData"
                      multiple
                      size="small"
                      show-search
                      placeholder="请选择地区"
                      :max-tag-count="1"
                    />
                  </a-form-item>
                  <a-form-item label="权值">
                    <a-input style="width:200px" size="small" v-model="weightData.rank" placeholder="请输入权值">
                    </a-input>
                  </a-form-item>
                  <a-form-item>
                    <a-button size="small" title="批量授权" @click="setAuthorization('batchAuthorization', '')" type="danger">
                      批量授权
                    </a-button>
                  </a-form-item>
                  <a-form-item>
                    <a-button size="small" title="批量取消授权" @click="setAuthorization('cancelAuthorization', 1)" type="danger">
                      批量取消
                    </a-button>
                  </a-form-item>
                  <a-form-item label="绑定至">
                    <a-tree-select
                      style="width:200px"
                      :dropdown-style="{maxHeight: '400px'}"
                      :treeData="searchConfig.areaData"
                      v-model="weightData.targetAreaId"
                      :allow-clear="true"
                      size="small"
                      show-search
                      treeNodeFilterProp="label"
                      placeholder="请选择地区"
                    />
                  </a-form-item>
                  <a-form-item>
                    <a-button size="small" title="批量绑定" @click="setAuthorization('bulkBinds', '')" type="danger">
                      批量绑定
                    </a-button>
                  </a-form-item>
                  <a-form-item>
                    <a-button size="small" title="批量取消绑定" @click="setAuthorization('cancelAuthorization', 2)" type="danger">
                      批量取消
                    </a-button>
                  </a-form-item>
                </a-form>
              </div>
            </div>
            <div class="flex flex-align-center flex-child-noshrink" ref="buttons">
              <template v-if="$tnt.xtenant < 1000 && showSearch && isJiuJiImportExport">
                <a-dropdown>
                  <a-menu slot="overlay">
                    <a-sub-menu title="面谈记录导入">
                      <a-menu-item @click="exportTemplateInterview" :loading="exportLoading">模板下载</a-menu-item>
                      <a-menu-item @click="fileChangeHandleInterview(2)">批量导入</a-menu-item>
                    </a-sub-menu>
                    <a-menu-item @click="exportFilesInterview">面谈记录导出</a-menu-item>
                    <a-sub-menu title="关键事件导入" v-if="rank.includes('gjsj')">
                      <a-menu-item @click="exportTemplateEvent" :loading="exportLoading">模板下载</a-menu-item>
                      <a-menu-item @click="fileChangeHandleInterview(1)">批量导入</a-menu-item>
                    </a-sub-menu>
                    <a-sub-menu title="人才盘点评分导入">
                      <a-menu-item @click="exportTemplateInventory">模板下载</a-menu-item>
                      <a-menu-item @click="fileChangeHandleInterview(3)">批量导入</a-menu-item>
                    </a-sub-menu>
                    <a-sub-menu title="家庭成员导入">
                      <a-menu-item @click="downloadFamilyTemplate">模板下载</a-menu-item>
                      <a-menu-item @click="fileChangeHandleInterview(4)">批量导入</a-menu-item>
                    </a-sub-menu>
                  </a-menu>
                  <a-button size="small" style="margin-left: 8px">员工信息导入/导出</a-button>
                </a-dropdown>
                <form ref="tagFileForm" style="display: none" name="tagFileForm">
                  <input ref="tagFile" type="file" accept=".xls,.xlsx" name="tagFile"
                         @change="selectTagFile"/>
                </form>
              </template>
              <template v-if="$tnt.xtenant >= 1000">
                <a-dropdown v-if="isJiujiStaff && showSearch && rank.includes('pldr')">
                  <a-menu slot="overlay">
                    <a-menu-item key="exportTemplate" @click="exportTemplate" :loading="exportLoading"> <a-icon type="download" />模板下载</a-menu-item>
                    <a-menu-item key="cloud-upload" @click="fileChangeHandle"> <a-icon type="cloud-upload" />数据导入</a-menu-item>
                  </a-menu>
                  <a-button size="small" style="margin-left: 8px">批量导入<a-icon type="cloud-upload" /> </a-button>
                </a-dropdown>
                <a-dropdown v-if="showSearch && rank.includes('plgx')">
                  <a-menu slot="overlay">
                    <a-menu-item key="download" @click="exportFiles(true)"> <a-icon type="download" />数据导出</a-menu-item>
                    <a-menu-item key="updataData" @click="updataData"> <a-icon type="cloud-sync" />数据更新</a-menu-item>
                  </a-menu>
                  <a-button size="small" style="margin-left: 8px">批量更新</a-button>
                </a-dropdown>
                <a-button size="small" style="margin-left: 8px" @click="exportFiles(false)">批量导出</a-button>
                <a-button size="small" style="margin-left: 8px" @click="getLogs">操作日志</a-button>
              </template>
              <!--九机-->
              <template v-if="$tnt.xtenant < 1000">
                <a-dropdown v-if="rank.includes('plgx') && showSearch">
                  <a-menu slot="overlay">
                    <a-menu-item key="download" @click="exportFiles(true)">数据导出</a-menu-item>
                    <a-menu-item key="updataData" @click="updataData">数据更新</a-menu-item>
                  </a-menu>
                  <a-button size="small" style="margin-left: 8px">批量更新</a-button>
                </a-dropdown>
                <a-button size="small" style="margin-left: 8px" @click="exportFiles(false)" v-if="rank.includes('pldc')">批量导出</a-button>
                <a-button size="small" style="margin-left: 8px" @click="getLogs" v-if="rank.includes('czrz')">操作日志</a-button>
              </template>
            </div>

          </div>
        </NiTable>
      </ni-list-page>
    </div>

    <a-modal
      v-model="exportData.showWin"
      centered
      title="数据导出"
      ok-text="导出"
      :maskClosable="false"
      cancel-text="取消"
      destroyOnClose
      :z-index="100"
      @ok="startExport"
    >
      <a-spin style="min-height:400px" :tip="exportData.tips" :spinning="exportData.spinning">
        <a-tree-select
          show-search
          labelInValue
          style="width: 100%"
          v-model="exportData.value"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="请选择要导出的列"
          tree-checkable
          :tree-data="exportData.data"
          treeNodeFilterProp="label"
          allow-clear
          multiple
        >
        </a-tree-select>
      </a-spin>
    </a-modal>

    <a-modal
      v-model="log.show"
      centered
      title="操作日志"
      :maskClosable="false"
      :footer="null"
      destroyOnClose
      :z-index="100"
      :body-style="{maxHeight: '600px', overflowY: 'auto'}"
    >
      <log :logs="log.data" :spinning="log.spinning"></log>
    </a-modal>

    <a-modal
      v-model="editInfo.show"
      centered
      :title="`${editInfo.username} - ${editInfo.userId}`"
      :maskClosable="false"
      cancel-text="取消"
      destroyOnClose
      :z-index="100"
      :confirmLoading="editInfo.loading"
      @ok="saveUserInfo"
    >
      <a-spin :spinning="editInfo.spinning" tip="数据加载中，请稍后。。。">
        <div v-if="editInfo.spinning" style="width:520px;height:368px"></div>
        <a-form-model v-else ref="editInfoRef" :model="editInfo" :rules="rules" :label-col="{ span: 5 }" :wrapper-col="{ span: 15 }">
          <a-form-model-item label="权限角色" prop="roleId" v-if="$tnt.xtenant < 1000">
            <PostErmissionsRoles
              class="full-width"
              v-model="editInfo.roleId"
              :options="searchConfig.rolesByLonginInfoJiuji"
              size="small"
              label-key="name"
              :multiple="true"
              clearable
              :collapse-tags="true"
              :filterable="true"
            />
          </a-form-model-item>
          <a-form-model-item label="角色" prop="roleId" v-if="$tnt.xtenant >= 1000">
            <a-select :disabled="!rank.includes('rygl') && !rank.includes('rybj')" allowClear showSearch optionFilterProp="children" mode="multiple" placeholder="请选择角色" v-model="editInfo.roleId" :options="searchConfig.rolesByLonginInfo"></a-select>
          </a-form-model-item>
          <a-form-model-item label="考勤类型" prop="workAttendanceTypeKey">
            <a-select :disabled="!rank.includes('rygl') && !rank.includes('rybj')" allowClear showSearch optionFilterProp="children" placeholder="请选择考勤类型" v-model="editInfo.workAttendanceTypeKey" :options="searchConfig.attendanceEnum"></a-select>
          </a-form-model-item>
          <a-form-model-item label="夏装工服" prop="summerClothSize">
            <a-select :disabled="!rank.includes('rygl') && !rank.includes('rybj')" allowClear showSearch optionFilterProp="children" placeholder="请选择夏装工服尺寸" v-model="editInfo.summerClothSize" :options="searchConfig.clothingSizesSummerEnum"></a-select>
          </a-form-model-item>
          <a-form-model-item label="冬装工服" prop="winterClothSize">
            <a-select :disabled="!rank.includes('rygl') && !rank.includes('rybj')" allowClear showSearch optionFilterProp="children" placeholder="请选择冬装工服尺寸" v-model="editInfo.winterClothSize" :options="searchConfig.clothingSizesWinterEnum"></a-select>
          </a-form-model-item>
          <a-form-model-item label="暂停登录">
            <a-switch
              checked-children="是"
              v-model="editInfo.loginInhibited"
              @change="changeIsLogin"
              un-checked-children="否" />
          </a-form-model-item>
        </a-form-model>
      </a-spin>
    </a-modal>

    <input type="file" name="file" id="upload-file" accept=".xls,.xlsx" style="width:0;height:0;overflow:hidden;opacity:0;">
    <import-fail v-model="importFail.show" :totalCount="importFail.totalCount" :successCount="importFail.successCount" :failCount="importFail.failCount" :downloadLink="importFail.downloadLink"></import-fail>
    <delete-user-modal :ch999Id="editInfo.userId" ref="deleteUserModalRef"/>
  </div>
</template>

<script lang="jsx">
  import moment from 'moment'
  import { cloneDeep } from 'lodash'
  import { exportFile } from '../../../util/export'
  import Log from './components/log.vue'
  import { mapState } from 'vuex'
  import { computed, reactive, ref, nextTick, getCurrentInstance, watch, onMounted, onBeforeUnmount } from 'vue'
  import { NiListPage, NiFilter, NiFilterItem, NiDepartSelect, NiAreaSelect, NiTable } from '@jiuji/nine-ui'

  import { EDUCATION, PAGINATION, WORKINGSTATE, SALARYSTRUCTURE, INSURANCE, LOGIN, SEX, AGE, PROMISE, SIGN, WAGES, BIRTHDAY, SHOPSTYPE, SHOPS, ROLE, ROLEJIUJI } from './options'

  import PostErmissionsRoles from './components/post-ermissions-roles.vue'

  import ImportFail from '~/components/import-fail.vue'
  import DeleteUserModal from '~/pages/user/jiuji/components/delete-user-modal.vue'

  const workplaceFilter = {
    age: undefined,
    birthDayMonth: undefined,
    cmbData: undefined,
    companyName: undefined,
    constellation: undefined,
    dateOfEmployment: undefined,
    entryDate: null,
    entryDateEnd: '',
    entryDateStart: '',
    fiveRisks: undefined,
    gender: undefined,
    letter: undefined,
    loginDate: undefined,
    mainStation: undefined,
    nativePlace: '',
    payWay: undefined,
    relation: false,
    relationType: undefined,
    roleType: 2,
    roles: undefined,
    salaryPaymentMethod: undefined,
    salaryType: undefined,
    schoolType: undefined,
    studentLabel: undefined
  }
  const departureFilter = {
    resignationDateEnd: '',
    resignationDateStart: '',
    resignationDate: null,
    resignationLabel: undefined
  }
  const baseSearchData = {
    areaIds: undefined,
    ch999Id: undefined,
    childRanks: undefined,
    departIds: undefined,
    eduLevel: undefined,
    education: undefined,
    positions: undefined,
    ranks: undefined,
    staffType: undefined,
    storeLevel: undefined,
    storeType: undefined
  }

  export default {
    components: { NiListPage, NiFilter, NiFilterItem, NiDepartSelect, NiAreaSelect, NiTable, Log, PostErmissionsRoles, ImportFail, DeleteUserModal },
    computed: {
      ...mapState({
        rank: state => (state.userInfo || {}).Rank || [],
        Roles: state => (state.userInfo || '').Roles.split(','),
        mainRole: state => (state.userInfo || '').mainRole
      }),
      COLUMNS () {
        return [
          { title: '工号', dataIndex: 'ch999Id', fixed: 'left', width: 60 },
          {
            title: '用户名',
            dataIndex: 'ch999Name',
            fixed: 'left',
            width: 100,
            customRender: (text, record) => {
              return this.rank.includes('rygl') || record.chargerFlag ? <a href={`${this.$tnt.oaHost}/staticpc/#/user?id=${record.ch999Id}`} rel="noopener" target="_blank" >{text}</a> : <span>{text}</span>
            }
          },
          {
            title: '基本信息',
            dataIndex: 'baseInfo',
            children: [

              {
                title: '权限',
                dataIndex: 'address1',
                width: 50,
                forceHide: !(['dkqz', 'rybj', 'rygl', 'dqqx'].find(item => this.rank.includes(item))),
                customRender: (text, record) => {
                  return <a href={ `${this.$tnt.oaHost}/staticpc/#/hr/rank-setting?ch999Id=${record.ch999Id}` } rel="noopener" target="_blank" >查看</a>
                }
              },
              {
                title: '活动日志',
                dataIndex: 'logs',
                key: '3',
                hide: true,
                forceHide: this.$tnt.xtenant < 1000 && !this.rank.includes('hdrz'),
                width: 100,
                customRender: (text, record) => {
                  return <a href={`${this.$tnt.oaHost}/ch999User/userLogsView?userid=${record.ch999Id}&username=${encodeURIComponent(record.ch999Name)}`} rel="noopener" target="_blank" >查看</a>
                }
              },
              {
                title: '关系网',
                dataIndex: 'relationship',
                forceHide: !(this.rank.includes('gxck') || this.Roles.includes('559') || this.mainRole === 559),
                hide: true,
                width: 75,
                customRender: (text, record) => {
                  return <a href={`${this.$tnt.oaHost}/ch999user/ch999Relation?ch999_id=${record.ch999Id}`} rel="noopener" target="_blank" >查看</a>
                }
              },
              { title: '职位', dataIndex: 'position', width: 80 },
              { title: '职级', dataIndex: 'rank', width: 60 },
              { title: this.$tnt.xtenant < 1000 ? '岗位' : '主要角色', dataIndex: 'mainRole', width: 100 },
              { title: '地区', dataIndex: 'area', width: 80 },
              { title: '中心/大区', dataIndex: 'center', width: 90 },
              { title: '部门', dataIndex: 'depart', width: 100 },
              { title: '合同到期', dataIndex: 'contractExpirationTime', width: 100, hide: true },
              { title: '入职日期', dataIndex: 'entryDate', width: 140 },
              { title: '薪酬类别', dataIndex: 'salaryCategory', width: 120 },
              { title: '转正日期', dataIndex: 'confirmationDate', width: 120, hide: true },
              { title: '员工类别', dataIndex: 'ch999TypeName', width: 150, hide: true },
              { title: '事业线', dataIndex: 'lineDepart', forceHide: this.$tnt.xtenant < 1000, hide: true, width: 100 },
              { title: '事业线区域', dataIndex: 'lineAreaDepart', forceHide: this.$tnt.xtenant < 1000, hide: true, width: 100 },
              { title: '离职日期', dataIndex: 'dateOfResignation', forceHide: this.showSearch, width: 140, hide: true },
              { title: '离职标签', dataIndex: 'quitTagName', forceHide: this.showSearch, width: 100, hide: true },
            ]
          },
          {
            title: '个人信息',
            dataIndex: 'personInfo',
            children: [
              { title: '性别', dataIndex: 'gender', width: 50, hide: true },
              { title: '生日', dataIndex: 'birthDay', width: 120 },
              { title: '身份证号码', dataIndex: 'idNumber', width: 165, hide: true },
              { title: '工龄', dataIndex: 'workAge', width: 100, hide: true },
              { title: '年龄', dataIndex: 'age', width: 50, hide: true },
              { title: '政治面貌', dataIndex: 'politicsStatus', width: 100, hide: true },
              { title: '手机号码', dataIndex: 'mobile', width: 110, hide: true },
              { title: '学历', dataIndex: 'education', width: 90 },
              { title: '民族', dataIndex: 'nation', width: 50, hide: true },
              { title: 'QQ', dataIndex: 'qqNumber', width: 90, hide: true },
              { title: '五险', dataIndex: 'fiveInsurance', width: 50, hide: true },
            ]
          },
          {
            title: '教育经历',
            dataIndex: 'educationInfo',
            children: [
              { title: '毕业院校', dataIndex: 'graduation', width: 150, hide: true },
              { title: '院校性质', dataIndex: 'graduationType', width: 150 },
              { title: '专业', dataIndex: 'zhuanYe', width: 150, hide: true },
              { title: '教学形式', dataIndex: 'xueWei', width: 90, hide: true },
            ]
          },

          {
            title: '操作',
            fixed: 'right',
            dataIndex: 'actions',
            forceHide: this.$tnt.xtenant >= 1000 || (!this.rank.includes('ryck') && !this.rank.includes('rybj') && !this.rank.includes('rygl')),
            width: 50,
            customRender: (text, record) => {
              return <a-button style="margin:0;padding:0;font-size:12px" type="link" onClick={() => this.editUserInfo(record)}>编辑</a-button>
            }
          }
        ]
      }
    },
    setup (props, ctx) {
      let root = getCurrentInstance()
      const { $api, $message, $store, $confirm, $router, $route, $indicator, $tnt } = root.proxy.$root
      const loading = ref(false)
      const exportLoading = ref(false)
      const isJiujiStaff = ref(false)
      const exportData = reactive({ data: [], showWin: false, spinning: true, tips: '数据加载中，请稍候。。。', value: undefined })
      const activeTab = ref(1)
      const log = reactive({ show: false, data: [], spinning: false })
      const pageData = reactive({ pageData: [] })
      const rules = ref({
        roleId: [{ required: true, message: '请选择角色', trigger: 'change' }],
        workAttendanceTypeKey: [{ required: true, message: '请选择考勤类型', trigger: 'change' }],
        summerClothSize: [{ required: true, message: '请选择夏装工服', trigger: 'change' }],
        winterClothSize: [{ required: true, message: '请选择冬装工服', trigger: 'change' }]
      })
      const searchConfig = reactive({
        EDUCATION,
        WORKINGSTATE,
        SALARYSTRUCTURE,
        INSURANCE,
        LOGIN,
        SEX,
        AGE,
        SIGN,
        PROMISE,
        WAGES,
        BIRTHDAY,
        SHOPSTYPE,
        SHOPS,
        ROLE,
        ROLEJIUJI,
        areaData: [],
        attendanceEnum: [],
        rolesByLonginInfoJiuji: []
      })
      let searchData = reactive({
        areaIds: [$store.state.userInfo.areaid],
        ch999Id: undefined,
        childRanks: undefined,
        departIds: undefined,
        eduLevel: undefined,
        education: undefined,
        positions: undefined,
        ranks: undefined,
        staffType: undefined,
        storeLevel: undefined,
        storeType: undefined,
        age: undefined,
        birthDayMonth: undefined,
        cmbData: undefined,
        companyName: undefined,
        constellation: undefined,
        dateOfEmployment: undefined,
        entryDate: null,
        entryDateEnd: '',
        entryDateStart: '',
        fiveRisks: undefined,
        gender: undefined,
        letter: undefined,
        loginDate: undefined,
        mainStation: undefined,
        nativePlace: '',
        payWay: undefined,
        relation: false,
        relationType: undefined,
        roleType: 2,
        roles: undefined,
        salaryPaymentMethod: undefined,
        salaryType: undefined,
        schoolType: undefined,
        studentLabel: undefined,
        resignationDateEnd: '',
        resignationDateStart: '',
        resignationDate: null,
        resignationLabel: undefined,
        contractTime: undefined,
        pageSize: undefined
      })

      const queryAreaIds = ref([])
      const { areaIds } = $route.query
      if (areaIds?.length) {
        queryAreaIds.value = areaIds.split(',')
        searchData.areaIds = queryAreaIds.value
      }

      let userSelect = reactive({
        lastFetchId: 0,
        fetching: false,
        addList: []
      })

      userSelect.lastFetchId = 0
      let getUserList = debounce(fetchUser, 600)

      const weightData = reactive({
        checked: false,
        areaIdList: undefined,
        rank: '',
        targetAreaId: undefined
      })
      const tables = reactive({
        pagination: PAGINATION,
        selectedRows: [],
        dataSource: [],
        selectedRowKeys: []
      })
      let editInfo = reactive({
        show: false,
        spinning: true,
        loading: false,
        loginInhibited: false,
        roleId: undefined,
        roleName: '',
        summerClothSize: undefined,
        userId: '',
        username: '',
        winterClothSize: undefined,
        workAttendanceTypeKey: '',
        workAttendanceTypeValue: ''
      })
      const postErmissionsRoles = ref([])
      const showSearch = computed(() => {
        return activeTab.value === 1
      })

      function debounce (fn, delay) {
        let timer = null
        return (...args) => {
          clearTimeout(timer)
          timer = setTimeout(() => {
            fn(args)
          }, delay)
        }
      }

      async function fetchUser (value) {
        if (!value[0]) {
          return
        }
        userSelect.lastFetchId += 1
        const fetchId = userSelect.lastFetchId
        userSelect.addList = []
        userSelect.fetching = true
        let params = {}
        if (isNaN(Number(value[0]))) {
          params.staffName = value[0]
        } else {
          params.staffId = Number(value[0])
        }
        let res = await $api.office.staffList[activeTab.value === 1 ? 'getUserInfo' : 'getDimissionUserInfo'](params)
        if (res.code === 0) {
          if (fetchId !== userSelect.lastFetchId) {
            return
          }
          const data = res.data.map(user => ({
            label: user.staffName ? `${user.staffId}-${user.staffName}` : user.ch999Name ? `${user.ch999Id}-${user.ch999Name}` : '',
            value: user.staffId || user.ch999Id,
          }))
          userSelect.addList = data
          userSelect.fetching = false
        } else {
          userSelect.addList = []
          userSelect.fetching = false
          res.code === 2002 && $message.error(res.userMsg)
        }
      }

      function disabledDate (current) {
        return current && current > moment(new Date()).add(0, 'days')
      }

      function jumpToNewWin (url, type) {
        if (type === 'replace') {
          window.location.href = `${window.location.origin}${url}`
        }
        window.open(`${window.location.origin}${url}`)
      }
      function changeTab (tab) {
        activeTab.value = tab
        tables.pagination = { ...PAGINATION }
        userSelect.addList = []
        for (let item in searchData) {
          if (item === 'roleType') {
            searchData[item] = 2
          } else if (item === 'areaIds') {
            // searchData.areaIds = [$store.state.userInfo.areaid]
            searchData.areaIds = queryAreaIds.value?.length ? queryAreaIds.value : [$store.state.userInfo.areaid]
          } else {
            searchData[item] = undefined
          }
        }
        getStaffListPage()
      }

      function weightChange (e) {
        weightData.checked = e.target.checked
      }

      function selectDepart (value) {
        searchData.departIds = value
      }

      function handleTableChange (pagination) {
        tables.pagination = { ...pagination }
        searchData.pageSize = pagination.pageSize
        getStaffListPage()
      }

      function editUserInfo (row) {
        getAllScheduleInfo()
        getAllRolesByLonginInfo(row.ch999Id)
        // getCh999UserPageInfo(row.ch999Id)
        editInfo.username = row.ch999Name
        editInfo.userId = row.ch999Id
        editInfo.show = true
        editInfo.loading = true
        editInfo.spinning = true
      }
      const editInfoRef = ref(null)
      async function saveUserInfo () {
        editInfoRef.value.validate(async valid => {
          if (!valid) return false
          let params = { ...editInfo }
          delete params.show
          delete params.spinning
          delete params.loading
          editInfo.loading = true
          let { code, data = [], userMsg } = await $api.office.staffList.updateCh999UserPageInfo(params)
          if (code === 0) {
            if (params.loginInhibited && $tnt.xtenant < 1000) {
              root.proxy.$refs.deleteUserModalRef?.toDeleteUser()
            }
            editInfo.show = false
            $message.success('更新成功')
          } else {
            $message.error(userMsg)
          }
          editInfo.loading = false
        })
      }

      function exportTemplate () {
        exportLoading.value = true
        let params = {}
        exportFile({
          url: $api.office.staffList.getTemplate(),
          token: $store.state.token,
          method: 'get'
        }).then((res) => {
          if (!res) return
          $message.error(res?.userMsg || res?.msg || '导出文件失败')
          if (res?.code === 1000) { // 无效token
            $router.push('/login?redirect=' + window.location.href)
          }
        }).catch(e => {
          $message.error(`${e?.message || e}`)
        }).finally(() => {
          exportLoading.value = false
        })
      }

      const eventLoading = ref(false)
      function exportTemplateEvent () {
        eventLoading.value = true
        exportFile({
          url: '/cloudapi_nc/org_service/api/ch999User/keyEvents/templateExport?xservicename=oa-org',
          token: $store.state.token,
          method: 'get',
          contentType: 'json'
        }).then((res) => {
          if (!res) return
          $message.error(res?.userMsg || res?.msg || '导出文件失败')
          if (res?.code === 1000) { // 无效token
            $router.push('/login?redirect=' + window.location.href)
          }
        }).catch(e => {
          $message.error(`${e?.message || e}`)
        }).finally(() => {
          eventLoading.value = false
        })
      }

      const exportTemplateInterview = () => {
        // let params = {}
        // exportFile({
        //   url: '',
        //   token: $store.state.token,
        //   method: 'post',
        //   contentType: 'json',
        //   params
        // }).then((res) => {
        //   if (!res) return
        //   $message.error(res?.userMsg || res?.msg || '导出文件失败')
        //   if (res?.code === 1000) { // 无效token
        //     $router.push('/login?redirect=' + window.location.href)
        //   }
        // }).catch(e => {
        //   $message.error(`${e?.message || e}`)
        // }).finally(() => {
        //   exportLoading.value = false
        // })
        window.location.href = 'https://img2.ch999img.com/newstatic/54456/11340c23d24c08df.xlsx'
      }
      const exportTemplateInventory = () => {
        window.location.href = 'https://img2.ch999img.com/newstatic/38294/1a199de80362ed61.xlsx'
      }
      const exportFilesInterview = () => {
        let params = setFilterData()
        exportFile({
          url: '/cloudapi_nc/org_service/api/ch999Interview/export/v1?xservicename=oa-org',
          token: $store.state.token,
          method: 'post',
          contentType: 'json',
          params
        }).then((res) => {
          if (!res) return
          $message.error(res?.userMsg || res?.msg || '导出文件失败')
          if (res?.code === 1000) { // 无效token
            $router.push('/login?redirect=' + window.location.href)
          }
        }).catch(e => {
          $message.error(`${e?.message || e}`)
        }).finally(() => {
          exportLoading.value = false
        })
      }

      const tagFileForm = ref(null)
      const tagFile = ref(null)
      const importType = ref(null) // 1-关键事件 2-面谈记录
      const importFail = ref({
        totalCount: 0,
        successCount: 0,
        failCount: 0,
        downloadLink: '',
        show: false
      })
      const fileChangeHandleInterview = (type) => {
        importType.value = type
        tagFile.value.click()
      }
      const selectTagFile = async (e) => {
        let file = e.target.files[0]
        if (file && file.length === 0) return
        const regExcel = /.(xls|xlsx)$/i
        if (!regExcel.test(file.name)) {
          $message.error('仅支持导入Excel格式的文件')
          return
        }
        const formData = new FormData()
        formData.append('file', file)
        try {
          $message.info('导入中......')
          if ([1, 2].includes(importType.value)) {
            const apiKey = importType.value === 1 ? 'keyEventsImport' : 'interViewImport'
            let res = await $api.office.cooperation[apiKey](formData)
            if (res.code === 0) {
              $message.success('导入成功')
              getStaffListPage()
            } else if (res.code === 5000) {
              if (!res.data) {
                $message.error(res.userMsg)
                return
              }
              $confirm({
                title: '导入失败',
                content: res.userMsg,
                okText: '下载',
                cancelText: '取消',
                onOk: () => {
                  window.location.href = res.data
                }
              })
            } else {
              $message.error(res.userMsg)
            }
          }
          if (importType.value === 3 || importType.value === 4) {
            let res = importType.value === 3 ? await $api.office.cooperation.inventoryResultImport(formData) : await $api.office.staffList.staffFamilyMemberImportExcel(formData)
            if (res.code === 0) {
              $message.success('导入成功')
              getStaffListPage()
            } else if (res.code === 5005) {
              if (!res.data) {
                $message.error(res.userMsg)
                return
              }
              importFail.value.totalCount = res.data.total
              importFail.value.successCount = res.data.success
              importFail.value.failCount = res.data.fail
              importFail.value.downloadLink = res.data.link
              importFail.value.show = true
              // $confirm({
              //   title: '导入失败',
              //   content: h => <div class="flex">共<span>{ res.data.total }</span>条数据，其中<span class="blue">{ res.data.success }</span>条已校验成功，<span class="red">{ res.data.fail }</span>条校验失败</div>,
              //   okText: '下载',
              //   cancelText: '取消',
              //   onOk: () => {
              //     window.location.href = res.data.link
              //   }
              // })
            } else {
              $message.error(res.userMsg)
            }
          }
        } catch (e) {
          $message.error('导入失败')
        } finally {
          tagFileForm.value.reset()
        }
      }
      async function exportFiles (bool) {
        exportData.showWin = true
        exportData.spinning = true
        exportData.tips = '数据加载中，请稍候。。。'
        exportData.value = undefined
        exportData.isUpdateExport = bool
        if (this.$tnt.xtenant < 1000) {
          let params = {
            showCenter: !bool,
            isUpdateExport: bool
          }
          let { code, data = [], userMsg } = await $api.office.staffList.getUserListJiuJi(params)
          if (code === 0) {
            exportData.data = data
          } else {
            $message.error(userMsg)
          }
        } else {
          let { code, data = [], userMsg } = await $api.office.staffList.getUserList(!bool)
          if (code === 0) {
            exportData.data = data
          } else {
            $message.error(userMsg)
          }
        }
        exportData.spinning = false
      }

      async function startExport () {
        if (!exportData.value) {
          $message.error('请选择要导出的列')
          return
        }
        exportData.spinning = true
        exportData.tips = '正在导出，请稍候。。。'
        // let search = exportData.isUpdateExport ? { isZaiZhi: activeTab.value } : setFilterData(searchData, true)
        let search = setFilterData(searchData, true)
        search.isUpdateExport = exportData.isUpdateExport
        search.fields = exportData.value.reduce((pre, cur) => {
          pre[cur.value] = cur.label
          return pre
        }, {})
        exportFile({
          url: $api.office.staffList.exportFiles(),
          token: $store.state.token,
          method: 'post',
          params: search,
          contentType: 'json'
        }).then((res) => {
          if (!res) return
          $message.error(res?.userMsg || res?.msg || '导出文件失败')
          if (res?.code === 1000) { // 无效token
            $router.push('/login?redirect=' + window.location.href)
          }
        }).catch(e => {
          $message.error(`${e?.message || e}`)
        }).finally(() => {
          exportData.spinning = false
          exportData.showWin = false
          exportData.value = undefined
        })
      }

      async function getLogs () {
        log.show = true
        log.spinning = true
        let { code, data = [], userMsg } = await $api.office.staffList.getLogsList()
        if (code === 0) {
          log.data = data
        } else {
          $message.error(userMsg)
        }
        log.spinning = false
      }

      function fileChangeHandle () {
        $confirm({
          title: '温馨提示',
          content: '导入功能仅适用于系统初始化操作，导入操作将删除已存在的用户及相关数据，确认要进行操作吗？',
          okText: '确定',
          okType: 'danger',
          cancelText: '取消',
          onOk: async () => {
            let changeFun = async (e) => {
              try {
                $indicator.open()
                const data = new FormData()
                data.append('file', e.target.files[0])
                let res = await $api.office.staffList.importFiles(data)
                if (res.code === 0) {
                  $message.success('上传成功')
                  getStaffListPage()
                  return
                }
                throw res.userMsg || res.msg || '上传失败'
              } catch (e) {
                $message.error(`${(e && e.message) || e}`)
              } finally {
                fileRef.value = ''
                fileRef.removeEventListener('change', changeFun)
                $indicator.close()
              }
            }
            const fileRef = document.querySelector('#upload-file')
            fileRef.addEventListener('change', changeFun, false)
            fileRef.click()
          },
          onCancel () {
            console.log('Cancel')
          },
        })
      }

      function updataData () {
        $confirm({
          title: '温馨提示',
          content: '此操作会批量更新用户相关数据，确认要进行操作吗？',
          okText: '确定',
          okType: 'danger',
          cancelText: '取消',
          onOk: async () => {
            let fileChange = async (e) => {
              try {
                $indicator.open()
                const data = new FormData()
                data.append('file', e.target.files[0])
                let res = await $api.office.staffList.updateFiles(data)
                if (res.code === 0) {
                  $message.success('更新成功')
                  getStaffListPage()
                  return
                }
                throw res.userMsg || res.msg || '更新失败'
              } catch (e) {
                $message.error(`${(e && e.message) || e}`)
              } finally {
                fileRef.value = ''
                fileRef.removeEventListener('change', fileChange)
                $indicator.close()
              }
            }
            let fileRef = document.querySelector('#upload-file')
            fileRef.addEventListener('change', fileChange, false)
            fileRef.click()
          },
          onCancel () {
            console.log('Cancel')
          },
        })
      }

      async function setAuthorization (inter, cancelType = '') {
        let destList = tables.selectedRows
        if (!destList.length) {
          $message.info('请先勾选需要批量操作的列')
          return
        }
        // if (!weightData.rank) {
        //   $message.error('权值不能为空')
        //   return
        // }
        let ch999IdList = destList.map(row => row.ch999Id)
        if (!ch999IdList.length) {
          $message.error('没有需要设置的人员信息')
          return
        }
        let params = { ...weightData, ch999IdList }
        // if (params.targetAreaId) {
        //   params.targetAreaId = params.targetAreaId[0]
        // }
        cancelType && (params.cancelType = cancelType)
        delete params.checked
        $indicator.open()
        let { code, userMsg } = await $api.office.staffList[inter](params)
        if (code === 0) {
          tables.selectedRows = []
          tables.selectedRowKeys = []
          weightData.areaIdList = undefined
          weightData.rank = ''
          weightData.targetAreaId = undefined
          $message.success('操作成功')
          $indicator.close()
        } else {
          $message.error(userMsg)
          $indicator.close()
        }
      }

      async function getUserPositionList () {
        let { code, data = [], userMsg } = await $api.office.staffList.getUserPositionList()
        if (code === 0) {
          data.forEach(item => {
            item.children = item.children.map(node => {
              return { lable: node.lable, value: `${item.value}-${node.value}` }
            })
          })
          searchConfig.userPosition = data
        } else {
          $message.error(userMsg)
        }
      }

      async function getCh999UserPageInfo (userId) {
        let { code, data = [], userMsg } = await $api.office.staffList.getCh999UserPageInfo(userId)
        if (code === 0) {
          editInfo.roleId = data.roleId.length ? data.roleId : undefined
          editInfo.roleName = data.roleName
          editInfo.summerClothSize = data.summerClothSize || undefined
          editInfo.userId = data.userId
          editInfo.username = data.username
          editInfo.winterClothSize = data.winterClothSize || undefined
          editInfo.workAttendanceTypeKey = data.workAttendanceTypeKey || undefined
          editInfo.workAttendanceTypeValue = data.workAttendanceTypeValue
          editInfo.loginInhibited = data.loginInhibited
          nextTick(() => {
            editInfo.spinning = false
            editInfo.loading = false
          })
        } else {
          $message.error(userMsg)
        }
      }

      async function getAllRolesByLonginInfo (userId) {
        let { code, data = [], userMsg } = await $api.office.staffList.getAllRolesByLonginInfo(userId)
        if (code === 0) {
          searchConfig.rolesByLonginInfo = data
          getCh999UserPageInfo(userId)
        } else {
          $message.error(userMsg)
        }
      }

      async function getDutyList () {
        let { code, data = [], userMsg } = await $api.office.staffList.getDutyList()
        if (code === 0) {
          searchConfig.dutyList = [{ id: -1, name: '后端', children: data[0] }, { id: -2, name: '前端', children: data[1] }]
        } else {
          $message.error(userMsg)
        }
      }

      async function getRoleEmg () {
        let { code, data = [], userMsg } = await $api.office.staffList.listAllRoles()
        if (code === 0) {
          searchConfig.ROLEEMG = data
        } else {
          $message.error(userMsg)
        }
      }

      function setFilterData (form = searchData, needPage) {
        console.log('setFilterData', form)
        let params = cloneDeep(form)
        if (!needPage) {
          params.currentPage = tables.pagination.current
          params.pageSize = tables.pagination.pageSize
        }
        params.isZaiZhi = activeTab.value
        params.rank = weightData.rank
        params.relateAreaIds = weightData.areaIdList

        if (activeTab.value === 0) {
          let departure = {}
          Object.keys(departureFilter).forEach((key) => {
            if (params[key]) {
              departure[key] = params[key]
            }
            delete params[key]
          })
          Object.keys(workplaceFilter).forEach((key) => {
            delete params[key]
          })
          if (departure.resignationDate && departure.resignationDate.length) {
            departure.resignationDateStart = moment(departure.resignationDate[0]).format('YYYY-MM-DD')
            departure.resignationDateEnd = moment(departure.resignationDate[1]).format('YYYY-MM-DD')
            delete departure.resignationDate
          } else {
            delete departure.resignationDate
            delete departure.resignationDateStart
            delete departure.resignationDateEnd
          }
          Object.keys(departure).length && (params.liZhiAdvancedFilter = departure)
        } else {
          let workplace = {}
          Object.keys(workplaceFilter).forEach((key) => {
            if (params[key]) {
              workplace[key] = params[key]
            }
            delete params[key]
          })
          Object.keys(departureFilter).forEach((key) => {
            delete params[key]
          })
          if (workplace.entryDate && workplace.entryDate.length) {
            workplace.entryDateStart = moment(workplace.entryDate[0]).format('YYYY-MM-DD 00:00:00')
            workplace.entryDateEnd = moment(workplace.entryDate[1]).format('YYYY-MM-DD 23:59:59')
            delete workplace.entryDate
          } else {
            delete workplace.entryDate
            delete workplace.entryDateStart
            delete workplace.entryDateEnd
          }
          if (workplace.roles && workplace.roles.length) {
            workplace.roles = workplace.roles.join(',')
          } else {
            delete workplace.roleType
          }
          Object.keys(workplace).length && (params.zaiZhiAdvancedFilter = workplace)
          if (params.contractTime?.length) {
            params.startTime = moment(params.contractTime[0]).format('YYYY-MM-DD 00:00:00')
            params.endTime = moment(params.contractTime[1]).format('YYYY-MM-DD 23:59:59')
          }
          delete params.contractTime
        }

        return params
      }

      async function getStaffListPage (form) {
        loading.value = true
        form && (tables.pagination = { ...PAGINATION })
        let { code, data, userMsg } = await $api.office.staffList.getStaffListPage(setFilterData(form))
        if (code === 0) {
          tables.dataSource = data?.records || []
          tables.pagination.total = data.total || 0
        } else {
          $message.error(userMsg)
          tables.dataSource = []
          tables.pagination.total = 0
        }
        loading.value = false
      }

      async function getAllScheduleInfo () {
        let { code, data, userMsg } = await $api.office.staffList.getAllScheduleInfo()
        if (code === 0) {
          searchConfig.attendanceEnum = data
        } else {
          $message.error(userMsg)
        }
      }

      async function getCh999UserEnumList () {
        let { code, data, userMsg } = await $api.office.staffList.getCh999UserEnumList()
        if (code === 0) {
          searchConfig.WORKINGSTATE = data.date
          searchConfig.AGE = data.ageRangeEnum
          searchConfig.SIGN = data.constellationEnum
          searchConfig.LOGIN = data.loginDateEnum
          searchConfig.BIRTHDAY = data.monthsEnum
          searchConfig.WAGES = data.payWayEnum
          searchConfig.RELATIONTYPE = data.relationTypeEnum
          searchConfig.staffTypeEnum = data.staffTypeEnum
          searchConfig.graduateTagsList = data.graduateTagsList
          searchConfig.EDUCATION = data.qualificationsEnum
          searchConfig.colleagueNatureEnum = data.colleagueNatureEnum
          searchConfig.eduLevelEnum = data.eduLevelEnum
          searchConfig.quitTagsEnum = data.quitTagsEnum
          searchConfig.salaryTypeEnum = data.salaryTypeEnum
          searchConfig.positionEnum = data.positionEnum
          searchConfig.clothingSizesWinterEnum = data.clothingSizesWinterEnum
          searchConfig.clothingSizesSummerEnum = data.clothingSizesSummerEnum
        } else {
          $message.error(userMsg)
        }
      }

      let limited = $store.state.userInfo?.Rank

      async function getAreaData () {
        let str = window.sessionStorage.getItem('ni-area-select')
        if (!str) {
          let { code, data, userMsg } = await $api.office.staffList.getArea({ author: false, mode: 1, ranks: null, strategy: 1 })
          if (code === 0) {
            searchConfig.areaData = data
            if (limited && (limited.includes('rygl') || limited.includes('rybj'))) {
              searchConfig.LimitedAreaData = data
            }
            window.sessionStorage.setItem('ni-area-select', JSON.stringify(data))
          } else {
            $message.error(userMsg)
          }
        } else {
          searchConfig.areaData = JSON.parse(str)
          if (limited && (limited.includes('rygl') || limited.includes('rybj'))) {
            searchConfig.LimitedAreaData = JSON.parse(str)
          }
        }
        nextTick(() => {
          // searchData.areaIds = [$store.state.userInfo.areaid]
          searchData.areaIds = queryAreaIds.value?.length ? queryAreaIds.value : [$store.state.userInfo.areaid]
        })
      }

      async function getLimitedAreaData () {
        let { code, data, userMsg } = await $api.office.staffList.getArea({ author: false, mode: 2, ranks: ['ryck'], strategy: 1 })
        if (code === 0) {
          searchConfig.LimitedAreaData = data
          nextTick(() => {
            // searchData.areaIds = [$store.state.userInfo.areaid]
            searchData.areaIds = queryAreaIds.value?.length ? queryAreaIds.value : [$store.state.userInfo.areaid]
          })
        } else {
          $message.error(userMsg)
        }
      }

      async function isJiujiStaffFun () {
        let { code, data, userMsg } = await $api.office.staffList.isJiujiStaff()
        if (code === 0) {
          isJiujiStaff.value = data
        } else {
          $message.error(userMsg)
        }
      }

      getUserPositionList()
      getDutyList()
      getRoleEmg()
      getCh999UserEnumList()
      if (limited && !limited.includes('rygl') && !limited.includes('rybj')) {
        getLimitedAreaData()
      }
      if ($tnt.xtenant !== 0) {
        isJiujiStaffFun()
      }
      getAreaData()

      const onSelectChange = (selectedRowKeys, selectedRows) => {
        tables.selectedRowKeys = selectedRowKeys
        tables.selectedRows = selectedRows
      }

      const clearSearchData = () => {
        weightData.areaIdList = undefined
        weightData.rank = ''
        weightData.targetAreaId = undefined
      }
      const getRoles = async () => {
        let params = {
          // 部门id
          departCodes: [],
          // 1权限角色,2岗位角色
          roleKind: searchData.roleType
        }
        let { code, data = [], userMsg } = await $api.office.staffList.listAllRolesV1(params)
        if (code === 0) {
          postErmissionsRoles.value = data
        } else {
          $message.error(userMsg)
        }
      }
      watch(() => searchData.roleType, () => {
        searchData.roles = undefined
        if ($tnt.xtenant < 1000) {
          getRoles()
        }
      })
      const getRolesByLonginInfoJiuji = async () => {
        let params = {
          // 部门id
          departCodes: [],
          // 1权限角色,2岗位角色
          roleKind: 1
        }
        let { code, data = [], userMsg } = await $api.office.staffList.listAllRolesV1(params)
        if (code === 0) {
          searchConfig.rolesByLonginInfoJiuji = data
        } else {
          $message.error(userMsg)
        }
      }
      if ($tnt.xtenant < 1000) {
        getRoles()
        getRolesByLonginInfoJiuji()
      }
      const isJiuJiImportExport = computed(() => {
        return $store.state.userInfo.Rank.includes('gjsj') || $store.state.userInfo.Rank.includes('rygl') || $store.state.userInfo.Rank.includes('ryck') || $store.state.userInfo.Rank.includes('mtck')
      })

      const buttons = ref(null)
      const myObserver = ref(null)
      const formBoxWidth = ref(0)

      onMounted(() => {
        if (!myObserver.value) {
          myObserver.value = new ResizeObserver(
            (entries) => {
              entries?.forEach((entrie) => {
                const { width } = entrie?.contentRect || {}
                formBoxWidth.value = width
              })
            }
          )
        }
        myObserver.value.observe(buttons.value)
      })

      onBeforeUnmount(() => {
        if (myObserver.value) myObserver.value.disconnect()
        myObserver.value = null
      })

      const downloadFamilyTemplate = function () {
        window.location.href = 'https://img2.ch999img.com/newstatic/53624/189ae54bc117886d.xlsx'
      }

      const quickSearchClick = (data) => {
        Object.assign(searchData, cloneDeep(data))
        tables.pagination.pageSize = data.pageSize
        getStaffListPage()
      }

      function changeIsLogin (val) {
        val && $tnt.xtenant < 1000 && (root.proxy.$refs.deleteUserModalRef?.open())
      }
      return {
        changeIsLogin,
        activeTab,
        showSearch,
        tables,
        log,
        isJiujiStaff,
        userSelect,
        editInfo,
        searchConfig,
        weightData,
        loading,
        exportLoading,
        exportData,
        searchData,
        departureFilter,
        workplaceFilter,
        changeTab,
        handleTableChange,
        getStaffListPage,
        jumpToNewWin,
        weightChange,
        disabledDate,
        selectDepart,
        exportTemplate,
        exportFiles,
        startExport,
        updataData,
        // selectArea,
        getUserList,
        fileChangeHandle,
        setAuthorization,
        editUserInfo,
        saveUserInfo,
        getLogs,
        rules,
        editInfoRef,
        onSelectChange,
        clearSearchData,
        postErmissionsRoles,
        exportTemplateInterview,
        exportTemplateEvent,
        fileChangeHandleInterview,
        exportFilesInterview,
        selectTagFile,
        tagFileForm,
        tagFile,
        eventLoading,
        isJiuJiImportExport,
        exportTemplateInventory,
        buttons,
        formBoxWidth,
        downloadFamilyTemplate,
        importFail,
        quickSearchClick
      }
    }
  }
</script>

<style lang="scss" scoped>
.staff-header {
  margin-bottom: 10px;
  .title {
    display: block;
    float: left;
    margin-bottom: 0;
    padding-right: 12px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 600;
    font-size: 20px;
    line-height: 32px;
  }
}
.tab-header {
  display: flex;
  width: 100%;
  height: 45px;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 8px;
  .tab {
    padding: 12px 16px;
  }
  .tab-m {
    margin-right: 32px;
  }
  .active {
    color: #1890ff;
    text-shadow: 0 0 0.25px currentColor;
    // position: relative;
    border-bottom: 2px solid #1890ff;
  }
  // .active::before {
  //   content: '';
  //   position: absolute;
  //   width: 116px;
  //   background: #1890ff;
  //   border-radius: 2px;
  //   border-bottom: 2px solid #1890ff;
  //   top: 45px;
  //   left: 50%;
  //   transform: translateX(-50%);
  // }
}
.is-loading {
  cursor: not-allowed;
  pointer-events: none;
  opacity: 0.5
}

:deep(.ant-table-fixed-left .ant-table-body-inner) {
  margin-right: 0
}

:deep(.ant-pagination) {
  padding-right: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 0;
}

::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 3px;
  height: 3px;
}

::-webkit-scrollbar-thumb {
  cursor: pointer;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.6);
  transition: color 0.2s ease;
}
:deep(.nine-table){

  th,td{
    font-size: 12px;
  }

  .nine-table-bar-main{
    width: calc(100% - 150px);
  }
  .nine-table-bar-tool{
    display: none;
  }
  .nine-table-bar-setting{
    flex-shrink: 0;
  }
  .nine-table-bar-action{
    width: 100%;
  }

}

</style>
