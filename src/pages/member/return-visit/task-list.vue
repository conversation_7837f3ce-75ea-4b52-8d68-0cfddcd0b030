<template>
  <page :title="null" class="task-list">
    <template v-slot:header>
      <div>
        <span class="font-20 bold" style="color: rgba(0, 0, 0, 0.85)">回访任务列表</span>
      </div>
    </template>
    <a-card class="relative padding" style="min-width: 960px">
      <ni-list-page :pushFilterToLocation="false">
        <ni-filter :immediate="false"  :form="form" :loading="loading" :label-width="100" @filter="fetchData('search')">
          <ni-filter-item label="任务名称">
            <a-input v-model="form.taskName" :allowClear="true" />
          </ni-filter-item>
          <ni-filter-item label="线索来源">
            <a-select allow-clear
            :options="cluesList"
            v-model="form.sourceId"
            placeholder="请选择线索来源"
          />
          </ni-filter-item>
          <ni-filter-item label="下发时间">
            <a-range-picker @change="timeChange" format="YYYY-MM-DD HH:mm:ss"/>
          </ni-filter-item>
          <ni-filter-item label="包含已删除">
            <a-checkbox v-model="form.containDel" />
          </ni-filter-item>
        </ni-filter>
        <ni-table
          :columns="columns"
          :data-source="data"
          :pagination="pagination"
          :rowKey="(record, index) => index"
          @change="handleTableChange"
        >
          <div slot="tool" v-if="hasEditPermission">
            <a-button type="primary" class="margin-right" @click="addTasks('')">添加任务</a-button>
            <a href="javascript:;" class="padding-right" @click="addCluesSource">线索来源配置</a>
            <a href="javascript:;" class="padding-right" @click="addVerbalTrick">话术配置</a>
            <a href="javascript:;" class="padding-right" @click="labelTypePopup">标签类型配置</a>
            <a href="javascript:;" class="padding-right" @click="addLabel">标签配置</a>
            <a href="javascript:;" class="padding-right" @click="templateDownload">用户模板下载</a>
          </div>
          <div slot="delFlag" slot-scope="text, record">
            <div
              class="state-box"
              :class="{
              'state-blue': !record.delFlag,
              'state-gray': record.delFlag,
            }"
            >
              {{ record.delFlag ? '已删除' : '正常' }}
            </div>
          </div>
          <div v-if="!record.delFlag" slot="action" slot-scope="text, record">
            <template v-if="hasEditPermission">
              <a href="javascript:;" class="padding-right" @click="addTasks(record.id)">编辑</a>
              <a href="javascript:;" @click="showImportUser = true; id = record.id" type="primary" class="margin-right">导入用户</a>
            </template>
            <router-link :to="`/member/return-visit/stores-list?taskId=${record.id}&taskName=${record.taskName}&sourceId=${record.sourceId}`">查看详情</router-link>
            <a-popconfirm
              v-if="hasEditPermission"
              title="确定要删除吗？"
              ok-text="是"
              cancel-text="否"
              @confirm="deleteTask(record)"
            >
              <a class="margin-left" href="javascript:">删除</a>
            </a-popconfirm>
          </div>

          <a href="javascript:;" slot="scriptName" class="padding-right" :style="{color: record.scriptId === 0 ? '#333' : ''}" slot-scope="text, record" @click="record.scriptId === 0 ? '': openVerbalModal(record.scriptId)">{{record.scriptName}}</a>
        </ni-table>
      </ni-list-page>
    </a-card>

    <!-- 添加任务 -->
    <add-tasks ref="AddTasks" @successful="fetchData()" :cluesList="cluesList" :taskTypeStrList="taskTypeStrList"></add-tasks>
    <!-- 线索来源 -->
    <clues-source ref="CluesSource" @success="loadcluesList()"></clues-source>
    <!-- 话术配置 -->
    <verbal-trick ref="VerbalTrick"></verbal-trick>
    <!--  标签类型配置  -->
    <label-type ref="labelType"></label-type>
    <!-- 标签配置 -->
    <add-label ref="AddLabel"></add-label>
    <!-- 话术查看 -->
    <verbal-modal ref="VerbalModal" :hasEditPermission="hasEditPermission"></verbal-modal>

    <!-- 导入 -->
    <a-modal
      title="批量导入"
      :visible="importResults"
      @cancel="beforeCloseImportResult"
      :footer="null"
    >
      <div class="flex">
        <div class="progress-left">
          <a-progress type="circle" :percent="percent" />
        </div>
        <div class="progress-right">
          <div class="title">{{percent == '100' ? '导入完成': '正在导入'}}</div>
          <div style="color: blue">进度：{{successTotal + failTotal}} : {{total}}</div>
          <div style="color: green">成功：{{successTotal}}</div>
          <div style="color: red">失败：{{failTotal}}</div>
          <div class="footer-btn">
            <a-button type="primary" @click="exportToExcel" v-if="showUploadErrorData">下载失败数据</a-button>
            <a-button type="primary" @click="beforeCloseImportResult">关闭</a-button>
            <p class="grey-6 mt-8" v-if="showUploadErrorData">如需要查看失败数据时请及时下载，10分钟后不再支持下载！（系统缓存）</p>
          </div>
        </div>
      </div>
    </a-modal>
    <!-- 导入用户弹出窗 -->
    <a-modal
      title="导入用户"
      v-model="showImportUser"
      @ok="handleImportStart"
    >
      <a-form-model ref="importUser" :rules="importUserRule" :model="importUserForm">
        <a-form-model-item label="优先分配方式" prop="enableWeChat">
          <a-select placeholder="请选择" v-model="importUserForm.enableWeChat">
            <a-select-option :value="true">企业微信</a-select-option>
            <a-select-option :value="false">OA回访</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="导入用户" prop="file">
          <div class="flex">
            <a-upload
              accept=".xls,.xlsx"
              name="file"
              :multiple="false"
              :beforeUpload="() => false"
              :showUploadList="false"
              @change=" e => importEvt(e)"
            >
              <a-button type="primary" class="margin-right">导入用户</a-button>
            </a-upload>
            <div>注：请按所给用户模板格式提交</div>
          </div>
          <div v-if="importUserForm.file">{{importUserForm.file.name}}</div>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <!-- 关闭导入弹窗前的二次提示 -->
    <a-modal
      title="温馨提示"
      :visible="confirmCloseVisible"
      :closable="false"
      :keyboard="false"
      :maskClosable="false"
      @cancel="confirmModalCancel"
      @ok="confirmModalOk"
    >
      <div style="height: 140px">关闭该窗口后，后续无法下载导出错误的明细，请确认是否关闭?</div>
    </a-modal>
  </page>
</template>

<script>
  import AddTasks from './components/add-tasks'
  import { exportFile } from '~/util/export'
  import CluesSource from './components/clues-source'
  import VerbalTrick from './components/verbal-trick'
  import AddLabel from './components/add-label'
  import { NiListPage, NiFilter, NiFilterItem, NiTable } from '@jiuji/nine-ui'
  import api from '~/api'
  import VerbalModal from './components/verbal-modal'
  import LabelType from './components/label-type'
  import { mapState } from 'vuex'
  const columns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName',
    },
    {
      title: '任务类型',
      dataIndex: 'taskTypeStr',
      key: 'taskTypeStr',
      ellipsis: true
    },
    {
      title: '线索来源',
      dataIndex: 'sourceName',
      key: 'sourceName',
      ellipsis: true,
    },
    {
      title: '话术',
      dataIndex: 'scriptName',
      key: 'scriptName',
      ellipsis: true,
      scopedSlots: { customRender: 'scriptName' }
    },
    {
      title: '回访有效期',
      dataIndex: 'validTimeStr',
      key: 'validTimeStr',
      ellipsis: true,
      width: 200,
    },
    {
      title: '下发时间',
      dataIndex: 'sendTime',
      key: 'sendTime',
      ellipsis: true,
      width: 180,
    },
    {
      title: '分配线索量',
      dataIndex: 'sourceCount',
      key: 'sourceCount',
      ellipsis: true,
      width: 100,
    },
    {
      title: '回访量',
      dataIndex: 'visitCount',
      key: 'visitCount',
      ellipsis: true,
      width: 100,
    },
    {
      title: '转化量',
      dataIndex: 'changeCount',
      key: 'changeCount',
      ellipsis: true,
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'delFlag',
      width: 100,
      scopedSlots: { customRender: 'delFlag' },
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      ellipsis: true,
      width: 220,
      scopedSlots: { customRender: 'action' },
    },
  ]

  const taskTypeStrList = [
    { label: '大件购买用户回访', value: 1 },
    { label: '九机服务购买用户回访', value: 2 },
    { label: '年包购买用户回访', value: 3 },
    { label: '意向用户回访', value: 4 },
  ]
  export default {
    components: {
      AddTasks,
      CluesSource,
      VerbalTrick,
      AddLabel,
      NiListPage,
      NiFilter,
      NiFilterItem,
      NiTable,
      VerbalModal,
      LabelType
    },
    data () {
      return {
        columns,
        data: [],
        loading: false,
        form: {
          taskName: '',
          sourceId: '',
          sendStartTime: '',
          sendEndTime: '',
          containDel: false
        },
        showImportUser: false,
        importUserForm: {
          enableWeChat: this.$tnt.xtenant === 0,
          file: undefined
        },
        importUserRule: {
          enableWeChat: [
            { required: true, message: '请选择是否启用微信', trigger: 'change' }
          ],
          file: [
            { required: true, message: '请选择文件', trigger: 'change' }
          ]
        },
        pagination: {
          current: 1,
          pageSize: 10,
          pages: 1,
          total: 0,
          pageSizeOptions: ['10', '20', '35', '50'],
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: total => `共 ${this.pagination.total} 条记录 第 ${this.pagination.current} / ${this.pagination.pages}页`
        },
        taskTypeStrList,
        cluesList: [],
        importResults: false,
        id: '',
        failTotal: 0,
        importKey: '',
        nowProgress: 0,
        showUploadErrorData: false,
        successTotal: 0,
        total: 0,
        percent: 1,
        timer: null,
        isDownloadData: false, // 前端标识是否下载过失败数据，用于关闭弹窗前二次提示
        confirmCloseVisible: false, // 二次弹窗
      }
    },
    computed: {
      ...mapState({
        userInfo: state => state.userInfo || {},
      }),
      hasEditPermission () {
        return this.userInfo.Rank.includes('hflb')
      },
    },
    created () {
      this.fetchData()
      this.loadcluesList()
    },
    methods: {
      // 添加任务
      addTasks (id) {
        this.$refs.AddTasks.showModal(id)
      },
      // 线索来源设置
      addCluesSource () {
        this.$refs.CluesSource.showModal()
      },
      // 话术配置
      addVerbalTrick () {
        this.$refs.VerbalTrick.showModal()
      },
      // 标签类型配置
      labelTypePopup () {
        this.$refs.labelType.showModal()
      },
      // 标签设置
      addLabel () {
        this.$refs.AddLabel.showModal()
      },
      // 查看话术设置
      openVerbalModal (id) {
        this.$refs.VerbalModal.addLine(id)
      },
      timeChange (date, dateString) {
        this.form.sendStartTime = dateString[0]
        this.form.sendEndTime = dateString[1]
      },
      async fetchData (type = '') {
        if (type === 'search') {
          this.pagination.current = 1
          this.pagination.pageSize = 10
        }
        const params = {
          current: this.pagination.current,
          size: this.pagination.pageSize
        }
        for (const key in this.form) {
          params[key] = this.form[key]
        }
        params.containDel = params.containDel ? 1 : 0
        const { code, data, userMsg } = await api.member.crmVisitTaskList(params)
        if (code === 0) {
          this.data = data.records
          this.pagination.total = data.total
          this.pagination.pages = data.pages
        } else {
          this.$message.error(userMsg)
        }
      },
      handleTableChange (pagination, filters, sorter) {
        this.pagination = pagination
        this.fetchData()
      },
      async loadcluesList () {
        const { data, code, userMsg } = await api.member.cluesList()
        if (code === 0) {
          this.cluesList = []
          for (var item of data) {
            this.cluesList.push({ label: item.sourceName, value: item.id })
          }
        } else {
          this.$message.error(userMsg)
        }
      },
      async deleteTask (record) {
        const { code, userMsg } = await api.member.deleteTask(record.id)
        if (code === 0) {
          record.delFlag = true
          this.$message.success('删除成功')
        } else {
          this.$message.error(userMsg)
        }
      },
      // 模版下载
      templateDownload () {
        window.open('https://img.9xun.com/newstatic/35441/11da81897f24ba2e.xlsx')
      },
      async handleImportStart () {
        try {
          await this.$refs.importUser.validate()
          // this.importConfirmLoading = true
          let form = new FormData()
          form.append('taskId', this.id)
          form.append('enableWeChat', this.importUserForm.enableWeChat)
          form.append('file', this.importUserForm.file)
          let res = await api.member.crmImportUserInfo(form)
          if (res.code === 0) {
            this.importResults = true
            this.importKey = res.data.importKey
            this.total = res.data.total
            this.showUploadErrorData = res.data.showUploadErrorData
            this.importSetInterval()
          } else {
            this.$message.error(res?.userMsg || res?.msg || '导入失败')
          }
        } catch (e) {
          this.$message.error(`${(e && e.message) || e}`)
        }
      },
      async importEvt ({ file }) {
        this.importUserForm.file = file
      },
      importSetInterval () {
        this.timer = setInterval(() => {
          this.importUseInfo()
        }, 1000)
      },
      // 用户导入进度
      async importUseInfo () {
        try {
          let res = await api.member.crmImportProgress({
            importKey: this.importKey
          })
          if (res.code === 0) {
            if (res.data) {
              this.total = res.data.total
              this.successTotal = res.data.successTotal
              this.failTotal = res.data.failTotal
              this.percent = res.data.nowProgress
              this.showUploadErrorData = res.data.showUploadErrorData

              // 清除定时器
              if (res.data.nowProgress === 100) {
                clearInterval(this.timer)
              }
            }
          } else {
            this.$message.error(res?.userMsg || res?.msg || '导入失败')
            clearInterval(this.timer)
          }
        } catch (error) {
          console.error(error)
          clearInterval(this.timer)
        }
      },
      beforeCloseImportResult () {
        if (!this.isDownloadData && this.showUploadErrorData) {
          this.confirmCloseVisible = true
        } else {
          this.importResultsCancel()
        }
      },
      confirmModalCancel () {
        this.confirmCloseVisible = false
      },
      confirmModalOk () {
        this.confirmCloseVisible = false
        this.importResultsCancel()
      },
      importResultsCancel () {
        this.importResults = false
        // 关闭弹窗清除定时器
        clearInterval(this.timer)
      },
      exportToExcel () {
        this.isDownloadData = true
        let params = {
          importKey: this.importKey
        }
        exportFile({
          url: '/cloudapi_nc/org_service/api/crmVisitTask/importError/export/v1?xservicename=oa-org',
          token: this.$store.state.token,
          method: 'post',
          params,
        }).then((res) => {
          if (!res) return
          this.$message.error(res?.userMsg || res?.msg || '导出文件失败')
          if (res?.code === 1000) { // 无效token
            this.$router.push('/login?redirect=' + window.location.href)
          }
        }).catch(e => {
          this.$message.error(`${e?.message || e}`)
        }).finally(() => {
          this.exportLoading = false
        })
      },
    }
  }
</script>

<style lang="scss" scoped>
.task-list {
  .task-list-header {
    padding-bottom: 20px;
  }

}
.progress-left {
  width: 150px;
  height: 150px;
  margin: 0 20px;
}
.progress-right {
  .title {
    padding-bottom: 10px;
    font-size: 20px;
  }
  .footer-btn {
    padding-top: 10px;
  }
}
.state-box {
  padding: 4px;
  font-size: 14px;
  border-radius: 2px;
  font-weight: 400;
  line-height: 14px;
  width: fit-content;
  display: inline-block;
}
.state-blue {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}
.state-gray {
  color: #9C9C9C;
  background: rgba(156,156,156,0.1);
}
</style>
