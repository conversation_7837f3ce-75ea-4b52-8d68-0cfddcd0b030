<template>
  <div class="right-content flex-col">
    <a-card>
      <a-steps :current="currentStep" @change="changeStep">
        <a-step v-for="s in stepList" :key="s" :title="s"/>
      </a-steps>
    </a-card>
    <div class="steps-content mt-10">
      <a-form-model
        ref="detailFormRef"
        layout="vertical"
        :rules="rules"
        :model="editForm"
        >
        <div class="content" v-show="currentStep === 0">
          <div class="productDetail flex border mb-20">
            <ni-img :src="productDetail.imgUrl" :width="120" :height="120"></ni-img>
            <div class="des ml-20">
              <div class="flex flex-justify-between mb-5">
                <span class="font-16 bold black">{{productDetail.productName}}</span>
                <div v-if="!onlyRead">
                  <a class="red-tag mr-20" v-if="Object.keys(thirdData).length" @click="getThirdData(mkcId, false, false)">{{thirdText}}</a>
                  <a class="red-tag mr-20" v-else @click="getThirdData(mkcId, true, false)">{{thirdText}}</a>
                  <a class="red-tag" @click="getServicesEnum">3023完整查询</a>
                </div>
              </div>
              <div class="flex mb-5">
                <span class="w180"><span class="grey-9">mkc_id：</span>{{productDetail.mkcId}}</span>
                <span class="mr-20"><span class="grey-9">商品PPID：</span>{{productDetail.ppid}}</span>
                <a class="blue" v-if="!onlyRead" @click="bindVisible = true">改绑主站PPID</a>
              </div>
              <div class="flex mb-5">
                <span class="w180"><span class="grey-9">分类：</span>{{productDetail.categoryName}}</span>
                <span><span class="grey-9">主站PPID商品名称：</span>{{productDetail.productName}}</span>
              </div>
              <div class="flex">
                <span class="w180"><span class="grey-9">品牌：</span>{{productDetail.brandName}}</span>
                <span class="mr-20"><span class="grey-9">验机模板：</span>{{productDetail.templateName}}</span>
                <div v-if="refundData.length">
                  <span class="grey-9">退款记录：</span>
                  <span v-if="!onlyRead" class="red-tag" @click="showRefundList = true, isSearched = true">{{ refundData.length }} 次，点击查看</span>
                  <span v-if="!onlyRead" :class="[isSearched ? 'green' : 'grey-9']">{{isSearched ? '已查询' : '未查询'}}</span>
                  <span v-else class="red">{{ refundData.length }} 次</span>
                </div>
              </div>
            </div>
          </div>
          <table class="table-form mb-20">
            <tr class="title">
              <td class="w180 text-center">参数项</td>
              <td>参数值</td>
            </tr>
            <tr>
              <td class="w180 text-center"><i class="red">*</i>商品名称</td>
              <td>
                <a-form-model-item prop="productName">
                  <div class="blue lines-1 mb-5" v-if="editForm.extraProductName"><span class="blue-tag">{{ editForm.extraProductName }}</span></div>
                  <div class="flex flex-align-center">
                    <a-input v-model="editForm.productName" :disabled="onlyRead" :maxLength="50" placeholder="请输入" style="flex: 1" allowClear></a-input>
                    <span class="addon-after">{{editForm.productName.length}}/50</span>
                  </div>
                </a-form-model-item>
              </td>
            </tr>
            <tr>
              <td class="w180 text-center"><i class="red">*</i>颜色</td>
              <td>
                <a-form-model-item prop="productColor">
                  <div class="blue lines-1 mb-5" v-if="editForm.extraProductColor"><span class="blue-tag">{{ editForm.extraProductColor }}</span></div>
                  <div class="flex flex-align-center">
                    <a-input v-model="editForm.productColor" :disabled="onlyRead" :maxLength="50" placeholder="请输入" style="flex: 1" allowClear></a-input>
                    <span class="addon-after">{{editForm.productColor.length}}/50</span>
                  </div>
                </a-form-model-item>
              </td>
            </tr>
            <tr>
              <td class="w180 text-center"><i class="red">*</i>串号（IMEI）</td>
              <td>
                <a-form-model-item prop="imei">
                  <div class="blue lines-1 mb-5" v-if="editForm.extraImei"><span class="blue-tag">{{ editForm.extraImei }}</span></div>
                  <div class="flex flex-align-center">
                    <a-input v-model="editForm.imei" :disabled="onlyRead" :maxLength="50" placeholder="请输入" style="flex: 1" allowClear></a-input>
                    <span class="addon-after">{{editForm.imei.length}}/50</span>
                  </div>
                </a-form-model-item>
              </td>
            </tr>
            <tr v-if="productDetail.categoryId !== 12">
              <td class="w180 text-center"><i class="red">*</i>储存容量</td>
              <td>
                <a-form-model-item prop="productMemory">
                  <div class="blue lines-1 mb-5" v-if="editForm.extraProductMemory"><span class="blue-tag">{{ editForm.extraProductMemory }}</span></div>
                  <div class="flex flex-align-center">
                    <a-input v-model="editForm.productMemory" :disabled="onlyRead" :maxLength="20" placeholder="请输入" style="flex: 1" allowClear></a-input>
                    <span class="addon-after">{{editForm.productMemory.length}}/20</span>
                  </div>
                </a-form-model-item>
              </td>
            </tr>
            <tr v-if="productDetail.categoryId !== 12">
              <td class="w180 text-center">生产日期</td>
              <td>
                <a-form-model-item>
                  <div class="blue lines-1 mb-5" v-if="editForm.extraCreateDate"><span class="blue-tag">{{ editForm.extraCreateDate }}</span></div>
                  <datePicker :step="currentStep" :date="editForm.createDate" :disabled="onlyRead" width="200px" @change="val => getDate(val, 'createDate')"></datePicker>
                </a-form-model-item>
              </td>
            </tr>
            <tr v-if="productDetail.categoryId !== 12">
              <td class="w180 text-center">激活日期</td>
              <td>
                <a-form-model-item>
                  <div class="blue lines-1 mb-5" v-if="editForm.extraActivationDate"><span class="blue-tag">{{ editForm.extraActivationDate }}</span></div>
                  <div>
                    <span class="sigle-tag mr-5" :class="[editForm.activationFlagNew === 'true' ? 'selected' : '']" @click="!onlyRead ? changeFlag('true', 'activationFlagNew'): ''">未激活</span>
                    <span class="sigle-tag mr-5" :class="[editForm.activationFlagNew === 'false' ? 'selected' : '']"  @click="!onlyRead ? changeFlag('false', 'activationFlagNew'): ''">已激活</span>
                    <a-select v-if="editForm.activationFlagNew === 'false'" v-model="timeType" style="width:100px" @change="editForm.activationDate = null">
                      <a-select-option value="day">年-月-日</a-select-option>
                      <a-select-option value="month">年-月</a-select-option>
                    </a-select>
                    <datePicker :step="currentStep" v-if="editForm.activationFlagNew === 'false'" :date="editForm.activationDate" :disabled="onlyRead" width="200px" @change="val => getDate(val, 'activationDate')" :type="timeType === 'month' ? 'month' : ''"></datePicker>
                  </div>
                </a-form-model-item>
              </td>
            </tr>
            <tr v-if="productDetail.categoryId !== 12">
              <td class="w180 text-center">厂家保修到期日期</td>
              <td>
                <a-form-model-item>
                  <div class="blue lines-1 mb-5" v-if="editForm.extraWarrantyExpirationDate"><span class="blue-tag">{{ editForm.extraWarrantyExpirationDate }}</span></div>
                  <div>
                    <span class="sigle-tag mr-5" :class="[editForm.warrantyFlagNew === 'true' ? 'selected' : '']"  @click="!onlyRead ? changeFlag('true', 'warrantyFlagNew'): ''">已过保</span>
                    <span class="sigle-tag mr-5" :class="[editForm.warrantyFlagNew === 'false' ? 'selected' : '']" @click="!onlyRead ? changeFlag('false', 'warrantyFlagNew'): ''">未过保</span>
                    <datePicker :step="currentStep" v-if="editForm.warrantyFlagNew === 'false'" :date="editForm.warrantyExpirationDate" :disabled="onlyRead" width="200px" @change="val => getDate(val, 'warrantyExpirationDate')"></datePicker>
                    <a-checkbox v-model="editForm.extendedWarrantyFlag" v-if="editForm.warrantyFlagNew === 'false'" class="ml-5">有延保</a-checkbox>
                  </div>
                </a-form-model-item>
              </td>
            </tr>
            <!-- <tr v-if="productDetail.categoryId !== 12">
              <td class="w180 text-center">配件配置</td>
              <td>
                <a-form-model-item>
                  <a-input-group compact>
                    <a-select v-model="editForm.pzType" style="width:120px" @change="editForm.configValue = undefined" :disabled="onlyRead">
                      <a-select-option value="newConfig">新机配件</a-select-option>
                      <a-select-option value="recycleConfig">回收配件</a-select-option>
                    </a-select>
                    <a-select v-if="editForm.pzType==='newConfig'" style="width:350px" v-model="editForm.configValue" :disabled="onlyRead" allowClear placeholder="请选择">
                      <a-select-option v-for="(item, idx) in newPzOptions" :key="idx" :value="item.value">
                        {{item.label}}
                      </a-select-option>
                    </a-select>
                    <a-select v-else style="width:70%" v-model="editForm.configValue" :disabled="onlyRead" :maxTagCount="1" mode="multiple" allowClear placeholder="请选择">
                      <a-select-option v-for="item in recyclePzOpts" :value="item.ppid" :key="item.ppid" :label="item.productName">
                        {{ item.productName }}
                        <div class="font-10 grey-9"> 【ppid】{{ item.ppid }}、【{{item.area}}存量】{{ item.count }}</div>
                      </a-select-option>
                    </a-select>
                  </a-input-group>
                </a-form-model-item>
              </td>
            </tr> -->
            <textType v-for="(c, i) in attachmentListParam.childrenParamList" :key="i" :c="c" :editForm="editForm" :row="attachmentListParam" @change="val => dealText(val, c)" @reflashRules="reflashCheck"></textType>
          </table>
          <fileList v-model="attachments" :onlyRead="onlyRead"></fileList>
          <log ref="logRef" v-if="currentStep === 0" :id="editForm.usedGoodsId"></log>
        </div>
        <template v-for="(item, idx) in mainCateParam">
          <div class="content" :key="idx" v-show="currentStep === (idx + 1)">
            <table class="table-form mb-20">
              <tr class="title">
                <td class="w180 text-center">参数项</td>
                <td>参数值</td>
                <td v-if="['外观信息', '功能信息', '维修信息'].includes(item.name)" class="text-center">是否瑕疵</td>
                <td v-if="['外观信息', '功能信息'].includes(item.name)">瑕疵拍照</td>
              </tr>
              <textType v-for="(c, i) in item.childrenParamList" :key="i" :c="c" :editForm="editForm" :disabled="onlyRead" :row="item" @change="val => dealText(val, c, item)" @reflashRules="reflashCheck"></textType>
            </table>
            <fileList v-model="attachments" :onlyRead="onlyRead"></fileList>
            <log ref="logRef" v-if="currentStep === (idx + 1)" :id="editForm.usedGoodsId"></log>
          </div>
        </template>
        <div class="content" v-show="currentStep === mainCateParam.length + 1">
          <div class="border padding white-bg mb-10">
            <div class="title flex flex-align-center mb-10"><i class="mr-5"></i><span class="font-16 black bold">良品拍照</span></div>
            <div class="flex flex-align-center" style="padding:0 10px 10px">常规图：<span class="font-12 mr-5" style="color:#cfcfcf">该照片客户可见，请规范上传</span><a-icon type="sync" class="blue" @click="getSalfGoodsMpicNew"/></div>
            <div class="flex lp-photo flex-wrap" v-if="lpPhotos.length">
              <template v-for="(item, idx) in lpPhotos">
                <div class="box border font-12 relative flex flex-align-center" :key="idx" v-if="item.fileList.length">
                  <span>{{item.title + '（' + item.fileList.length + '）'}}</span>
                  <img class="check-img" src="../../../assets/images/img-viewer.png" alt="" >
                  <img-viewer v-if="item.fileList.length">
                    <div class="absolute img-view">
                      <ni-img :immediate="true" v-for="(f, index) in item.fileList" :key="index" :src="f.fileUrl" class="pointer"/>
                    </div>
                  </img-viewer>
                </div>
              </template>
            </div>
            <div class="flex flex-align-center" style="padding:10px">瑕疵图：<span class="font-12" style="color:#cfcfcf">该照片客户可见，请规范上传</span></div>
            <upload-img :picList="lpXcPhotos" :canAdd="false" :canDel="false" width="84px" height="84px" @previewImg="val => handlePreview(val, lpXcPhotos)"></upload-img>
          </div>
          <div class="border padding white-bg mb-20">
            <div class="title flex flex-align-center mb-10"><i class="mr-5"></i><span class="font-16 black bold">上架信息</span></div>
            <table class="sj-table">
              <tr>
                <td class="gray-bg">良品名称</td>
                <td>{{editForm.productName}}</td>
                <td class="gray-bg">颜色</td>
                <td>{{editForm.productColor}}</td>
                <td class="gray-bg">
                  <div><i class="red">*</i>成色</div>
                  <div class="font-12 grey-9" v-if="AutoGoodLevel">系统实时定级：<span class="red bold">{{ AutoGoodLevel }}</span></div>
                  <div class="font-12 red bold" v-else>自动定级失败，请联系良品运营组！</div>
                </td>
                <td>
                  <a-form-model-item prop="goodLevel">
                    <a-select v-model="editForm.goodLevel" placeholder="请选择" :disabled="onlyRead" allowClear>
                      <a-select-option v-for="item in gLevel" :key="item.value" :value="item.value">{{item.label}}</a-select-option>
                    </a-select>
                  </a-form-model-item>
                </td>
              </tr>
              <tr>
                <td class="gray-bg">串号</td>
                <td>{{editForm.imei}}</td>
                <td class="gray-bg">容量</td>
                <td>{{editForm.productMemory}}</td>
                <td class="gray-bg">回收成本</td>
                <td>{{ editForm.inprice }}</td>
              </tr>
              <tr>
                <td class="gray-bg">上次复检价</td>
                <td>{{ editForm.checkPrice }}</td>
                <td class="gray-bg">本次复检价</td>
                <td>{{editForm.curCheckPrice}} <a @click="getCurCheckPrice">刷新</a></td>
                <td class="gray-bg"><i class="red">*</i>良品售价</td>
                <td>
                  <a-form-model-item prop="price">
                    <a-input v-model="editForm.price" :disabled="onlyRead" placeholder="请输入" allowClear></a-input>
                  </a-form-model-item>
                </td>
              </tr>
              <tr>
                <td class="gray-bg">结果检测/描述</td>
                <td colspan="5">
                  <a-form-model-item>
                    <a-input :auto-size="{ minRows: 2 }" placeholder="请输入（所填内容在网站侧展示时会自动在末尾拼接文案“详细外观请参考真机实拍图”）" :disabled="true" type="textarea" :maxLength="200" v-model="editForm.checkResultDescription" @input="checkText" allowClear></a-input>
                  </a-form-model-item>
                  <div class="grey-9 text-right">
                    {{ editForm.checkResultDescription ? editForm.checkResultDescription.length: 0 }}/200
                  </div>
                  <div class="grey-9" style="padding: 0 22px 0 11px">
                    <div v-if="hasRedText">异常文字请注意（红色字体）：<span class="flex flex-wrap" v-html="noticeText"></span></div>
                  </div>
                </td>
              </tr>
            </table>
          </div>
          <fileList v-model="attachments" :onlyRead="onlyRead"></fileList>
          <log ref="logRef" v-if="currentStep === mainCateParam.length + 1" :id="editForm.usedGoodsId"></log>
        </div>
      </a-form-model>
    </div>
    <div class="fix-bottom white-bg border-top mt-10">
      <div class="flex flex-justify-end">
        <div class="flex flex-align-center" v-if="currentStep === stepList.length - 1">
          <span class="mr-5">完成验机自动打印回收背标</span>
          <a-switch v-model="isPrintFlag" size="small" @change="autoShelfFlagChange" :disabled="!Boolean(clientId)"></a-switch>
          <span class="ml-10 mr-10">背标打印，打印客户端</span>
          <a-select v-model="clientId" style="min-width: 120px" class="mr-10" @change="changePrint">
            <a-select-option v-for="(item, index) in printerList" :key="index" :value="item.clientId">{{item.clientId}}</a-select-option>
          </a-select>
        </div>
        <a-button v-if="currentStep > 0" class="mr-10" type="primary" @click="nextStep('prevStep')">上一步</a-button>
        <a-button v-if="currentStep < stepList.length - 1" class="mr-10" type="primary" @click="nextStep('nextStep')">下一步</a-button>
        <a-button class="mr-10" type="primary" @click="nextStep">保存验机信息</a-button>
        <a-button v-if="(currentStep === stepList.length - 1) && !isFinishedCheck" class="mr-10" type="primary" @click="nextStep('ok')">完成验机</a-button>
        <a-button v-if="(currentStep === stepList.length - 1) && isFinishedCheck" class="mr-10" type="primary" @click="nextStep('submit')">上架良品</a-button>
      </div>
    </div>
    <!--查看第三方数据-->
    <a-modal v-model="visible" title="查询第三方数据" :footer="null" width="400px">
      <div class="data-content">
        <div>3023数据查询结果：</div>
        <div v-if="thirdData.imei">序列号：{{thirdData.imei}}</div>
        <div v-if="thirdData.productName">型号：{{thirdData.productName}}</div>
        <div v-if="thirdData.specification">容量：{{thirdData.specification}}</div>
        <div v-if="thirdData.color">颜色：{{thirdData.color}}</div>
        <div v-if="thirdData.activationStatus">激活状态：{{thirdData.activationStatus}}</div>
        <div v-if="thirdData.activationDate">激活日期：{{thirdData.activationDate}}</div>
        <div v-if="thirdData.buyDate">购买日期：{{thirdData.buyDate}}</div>
        <div v-if="thirdData.warrantyStatus">保修状态：{{thirdData.warrantyStatus}}</div>
        <div v-if="thirdData.warrantyDate">保修日期：{{thirdData.warrantyDate}}</div>
        <div v-if="thirdData.manufactureDate">出产日期：{{thirdData.manufactureDate}}</div>
        <div v-if="thirdData.queryUser">查询人：{{thirdData.queryUser}}</div>
        <div v-if="thirdData.queryTime">查询时间：{{thirdData.queryTime}}</div>
      </div>
      <div class="footer">
        <a-button class="full-width" type="primary" @click="getThirdData(mkcId, true, false)">再次获取（剩余使用次数：{{leftTimes}}）</a-button>
      </div>
    </a-modal>
    <!--查看3023完整数据-->
    <a-modal v-model="visible2" :footer="null" width="400px">
      <div slot="title" class="font-14">
        3023完整查询（ <span class="red">慎用，特殊查询使用</span> ）
      </div>
      <a-select v-model="queryType" style="width:100%" placeholder="请选择需要查询的服务" class="mb-10">
        <a-select-option v-for="t, i, in typeOpts" :key="i" :value="t.value">{{t.label}}</a-select-option>
      </a-select>
      <div>查询结果：</div>
      <div style="height:300px" :class="[thirdAllData.length > 0 ? '' : 'flex flex-center']">
        <div class="data-content" v-if="thirdAllData.length > 0" style="max-height:300px;overflow-y:auto;">
          <div v-for="item, i in thirdAllData" :key="i">{{item.label}}：{{item.value}}</div>
        </div>
        <a-empty description="暂无数据"  v-else />
      </div>
      <div class="footer">
        <a-button class="full-width" type="primary" :loading="loading" @click="getAllThirdData">查询</a-button>
      </div>
    </a-modal>
    <!--改绑主站PPID-->
    <bind-ppid :visible="bindVisible" :productName="productDetail.productName" :mkcid="productDetail.mkcId" @close="bindVisible = false"></bind-ppid>
    <refund-list :show="showRefundList" ref="refundListRef" :refundData="refundData" @close="showRefundList = false"></refund-list>
  </div>
</template>
<script setup lang="jsx">
  import Vue, { ref, watch, getCurrentInstance, defineEmits, defineExpose } from 'vue'
  import bindPpid from '../lp-quality-check/bindPpid.vue'
  import refundList from '../lp-quality-check/refundList.vue'
  import textType from './type.vue'
  import datePicker from '../lp-quality-check/date.vue'
  import fileList from '../lp-quality-check/fileList.vue'
  import imgViewer from '~/components/img-viewer'
  import log from './log.vue'
  import { RULES, EDITFORM, NEWPZOPTIONS, gLevel, STEPLIST, textExampel } from './model.jsx'
  import { setRules, checkData } from './utils.js'
  import uploadImg from './uploadImg.vue'
  import Viewer from 'v-viewer'
  import api from '~/api/recovery'
  import bakMachinesApi from '@market/api/bakMachines.js'
  import { debounce } from '~/util/common'
  import { Steps } from 'ant-design-vue'
  import moment from 'moment'
  import cookie from 'js-cookie'

  Vue.use(Steps).use(Viewer)
  const { $store, $route, $message, $set, $indicator, $error, $confirm, $viewerApi, $nextTick } = getCurrentInstance().proxy
  const emit = defineEmits(['change', 'refrash'])
  const currentStep = ref(0)
  const stepList = ref([...STEPLIST])
  const mkcId = ref($route.query.mkc_id || '')
  const onlyRead = ref($route.query.onlyRead || false)
  const productDetail = ref({})
  const attachments = ref([])
  const refundData = ref([])
  const editForm = ref({ ...EDITFORM })
  const mainCateParam = ref([])
  const attachmentListParam = ref({})
  const thirdData = ref({})
  const visible = ref(false)
  const visible2 = ref(false)
  const bindVisible = ref(false)
  const showRefundList = ref(false)
  const leftTimes = ref(0)
  const thirdText = ref('获取第三方数据')
  const loading = ref(undefined)
  const isSearched = ref(false) // 是否查询过
  const typeOpts = ref([]) // 查询服务类型枚举
  const queryType = ref(undefined) // 查询服务类型
  const thirdAllData = ref([])
  // const newPzOptions = ref([...NEWPZOPTIONS])
  const rules = ref({ ...RULES })
  const lpXcPhotos = ref([])
  const checkedSteps = ref([])
  const logRef = ref(null)
  const timeType = ref('day')
  const AutoGoodLevel = ref('')
  const isFinishedCheck = ref(false) // 是否完成验机

  const changeStep = (val) => {
    currentStep.value = val
    saveCheckInfo()
  }
  const getProductCateParams = async (type) => {
    try {
      $indicator.open()
      const { code, data, userMsg } = await api.CheckMachineApi.getProductCateParams(mkcId.value)
      if (code === 0) {
        productDetail.value = data.productParamVO || {}
        attachments.value = data.attachments || []
        refundData.value = data.shouHouVOS || []
        isFinishedCheck.value = data.fixProductParamVO.isFinishedCheck
        // 获取瑕疵图
        lpXcPhotos.value = []
        data.mainCateParam.forEach(item => {
          if (['外观信息', '功能信息', '维修信息'].includes(item.name)) {
            item.childrenParamList.forEach(t => {
              if (t.isXc) {
                t.xcImages.forEach(t => {
                  $set(t, 'showOpr', false)
                })
                lpXcPhotos.value.push(...t.xcImages)
              }
            })
          }
        })
        data.mainCateParam?.forEach(item => { // 为了校验规则， 动态参数的值取出来放到editForm.value
          item.childrenParamList.forEach(t => {
            $set(editForm.value, 'info' + t.paramId, t.info)
          })
        })
        mainCateParam.value = data.mainCateParam.filter(t => t.name !== '附件清单') || [] // 剔除附件清单
        attachmentListParam.value = data.mainCateParam.find(t => t.name === '附件清单') || {}
        checkedSteps.value = data.fixProductParamVO.checkedSteps ? data.fixProductParamVO.checkedSteps?.split(',').map(t => Number(t)) : []
        if (type === 'first') {
          const num = mainCateParam.value.length + 2
          currentStep.value = (checkedSteps.value.length && checkedSteps.value.length < num) ? Math.max(...checkedSteps.value) + 1 : (checkedSteps.value.length && checkedSteps.value.length === num) ? Math.max(...checkedSteps.value) : 0
        }
        if (mainCateParam.value.length > 0) {
          stepList.value = [...STEPLIST]
          const arr = mainCateParam.value.map(item => item.name)
          stepList.value.splice(1, 0, ...arr)
        }
        if (data.fixProductParamVO.lpTips) { // 商品标签转格式
          data.fixProductParamVO.lpTips = data.fixProductParamVO.lpTips.split(',')
        } else {
          data.fixProductParamVO.lpTips = []
        }
        // if (data.fixProductParamVO.pzType === 'recycleConfig') {
        //   if (data.fixProductParamVO.configValue) { // 配件配置转格式
        //     data.fixProductParamVO.configValue = data.fixProductParamVO.configValue.split(',').map(t => Number(t))
        //   } else {
        //     data.fixProductParamVO.configValue = []
        //   }
        // }
        // if (!data.fixProductParamVO.pzType) {
        //   data.fixProductParamVO.pzType = 'newConfig'
        // }
        emit('change', data.fixProductParamVO.usedGoodsId)
        emit('refrash')
        Object.assign(editForm.value, data.fixProductParamVO)
        setRules(data.mainCateParam, rules.value, productDetail.value.categoryId)
        if (editForm.value.extraActivationDate && !editForm.value.activationDate) {
          editForm.value.activationDate = editForm.value.extraActivationDate?.replace(/\./g, '-')
        }
        // 激活日期处理
        if (editForm.value.activationDate) {
          const count = (editForm.value.activationDate.match(/-/g) || []).length
          timeType.value = count > 1 ? 'day' : 'month'
        }
        $nextTick(() => {
          if (Array.isArray(logRef.value)) {
            logRef.value[0]?.getLog(editForm.value.usedGoodsId)
          } else {
            logRef.value?.getLog(editForm.value.usedGoodsId)
          }
        })
        // if (!recyclePzOpts.value.length) {
        //   getRecyclePzOpts(editForm.value.areaId)
        // }
        if (currentStep.value === mainCateParam.value.length + 1) {
          getMatchLevel()
        }
        if (type === 'submit') {
          checkErrorInfo()
        } else if (type === 'ok') {
          detailFormRef.value.validate(valid => {
            if (valid) {
              finishCheck()
            } else {
              // 表单校验不通过
              setTimeout(() => {
                var errors = document.querySelector('.ant-form-explain')
                if (errors) {
                  errors.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest'
                  })
                  $message.error(errors.innerHTML || '请填写完整信息')
                }
              }, 100)
            }
          })
        }
      } else {
        $message.error(userMsg)
      }
    } catch (e) {
      console.log(e)
    } finally {
      $indicator.close()
    }
  }
  const handlePreview = (index, list) => {
    const picArr = list.map(t => t.fileUrl)
    $viewerApi({
      images: picArr
    }).view(index)
  }
  const dealText = (val, item, row) => {
    if (item.inputType === 0 && item.name === '操作系统' && val) {
      // 先将所有中文括号换成英文括号
      val = val.replace(/（/g, '(').replace(/）/g, ')')
      // 去除空格和括号及括号内的内容
      let str = val.replace(/\s*(\(.*?\)\s*|\s)/g, '')
      if (productDetail.value.categoryId === 1 && productDetail.value.brandId === 1 && !str.includes('iOS')) {
        editForm.value[`info${item.paramId}`] = 'iOS' + str
      } else if (productDetail.value.categoryId === 2 && productDetail.value.brandId === 1 && !str.includes('iPadOS')) {
        editForm.value[`info${item.paramId}`] = 'iPadOS' + str
      } else {
        editForm.value[`info${item.paramId}`] = str
      }
    }
    if (['外观信息', '功能信息', '维修信息'].includes(row.name)) { // 外观信息和功能信息,维修信息，单选或多选的时候选中了瑕疵项就将瑕疵开关开启
      if (Array.isArray(val)) {
        const flag = item.option.some(t => t.isXc && val.includes(t.id))
        item.isXc = flag
      } else {
        const obj = item.option.find(t => t.id === val) || {}
        item.isXc = obj.isXc
      }
    }
  }
  // 获取回收配件配置枚举
  // const recyclePzOpts = ref([]) // 回收配件清单
  // const getRecyclePzOpts = async (areaid) => {
  //   try {
  //     const { code, userMsg, data } = await api.getRecyclePzOpts(areaid)
  //     if (code === 0) {
  //       recyclePzOpts.value = data || []
  //     } else {
  //       $message.error(userMsg)
  //     }
  //   } catch (err) {
  //     console.error(err)
  //   }
  // }
  const changeFlag = (val, key) => {
    editForm.value[key] = val
    if (key === 'activationFlagNew' && val === 'true') {
      editForm.value.activationDate = null
    }
    if (key === 'warrantyFlagNew' && val === 'true') {
      editForm.value.warrantyExpirationDate = null
      editForm.value.extendedWarrantyFlag = false
      console.log('aaa', val, key, editForm.value.extendedWarrantyFlag)
    }
  }
  const dealParams = () => {
    let allParamArr = []
    mainCateParam.value && mainCateParam.value.forEach(t => {
      allParamArr.push(...t.childrenParamList)
    })
    attachmentListParam.value?.childrenParamList && allParamArr.push(...attachmentListParam.value?.childrenParamList)
    const allParams = allParamArr.map(t => {
      return {
        value: editForm.value[Object.keys(editForm.value).find(item => item === `info${t.paramId}`)], // 动态的参数值塞到对应的地方
        paramId: t.paramId,
        isXc: t.isXc,
        xcImages: t.xcImages,
        comment: t.option.map(item => {
          return {
            id: item.id,
            comment: item.comment
          }
        })
      }
    })
    // 过滤出含info的
    const formParams = Object.keys(editForm.value).filter(t => t.indexOf('info') < 0).reduce((obj, key) => { return Object.assign(obj, { [key]: editForm.value[key] }) }, {})
    const params = {
      ...formParams,
      gisground: productDetail.value.gisground,
      mkcId: productDetail.value.mkcId,
      ppid: productDetail.value.ppid,
      templateId: productDetail.value.templateId,
      param: allParams,
      attachments: attachments.value
    }
    params.lpTips = params.lpTips.join(',')// 商品标签转格式
    // if (params.pzType === 'recycleConfig') {
    //   if (params.configValue.length) { // 配件配置转格式
    //     params.configValue = params.configValue.join(',')
    //   } else {
    //     params.configValue = ''
    //   }
    // }
    return params
  }
  const saveCheckInfo = async (type) => {
    try {
      if (refundData.value.length && !isSearched.value) {
        $message.error(`该商品有${refundData.value.length}次维修记录，请查询核实后再提交`)
        return
      }
      $indicator.open()
      const params = dealParams()
      const str = Array.from({ length: currentStep.value + 1 }, (_, index) => index).join(',')
      if (!checkedSteps.value.includes(currentStep.value)) {
        params.checkedSteps = str
      }
      const { code, userMsg } = await api.CheckMachineApi.saveCheckInfo(params)
      if (code === 0) {
        $message.success(userMsg || '保存成功')
        if (type === 'nextStep') {
          currentStep.value++
        } else if (type === 'prevStep') {
          currentStep.value--
        }
        getProductCateParams(type)
      } else {
        $indicator.close()
        $message.error(userMsg || '保存失败')
      }
    } catch (err) {
      console.error(err)
    }
  }
  const detailFormRef = ref(null)
  const nextStep = (type) => {
    saveCheckInfo(type)
  }
  const getMatchLevel = async () => {
    const { code, userMsg, data } = await api.CheckMachineApi.matchLevel(editForm.value.usedGoodsId)
    if (code === 0) {
      AutoGoodLevel.value = data || ''
      if (AutoGoodLevel.value && !editForm.value.goodLevel) {
        const obj = gLevel.find(item => item.label === AutoGoodLevel.value)
        editForm.value.goodLevel = obj.value
      }
    } else {
      $message.error(userMsg)
    }
  }
  const getDate = (val, key) => {
    if (key === 'activationDate' && val) {
      editForm.value.activationFlagNew = 'false'
    } else if (key === 'warrantyExpirationDate' && val) {
      let date1 = new Date(val)
      let curDate = new Date(moment().format('YYYY-MM-DD'))
      if (date1 < curDate) {
        editForm.value.warrantyFlagNew = 'true'
      } else {
        editForm.value.warrantyFlagNew = 'false'
      }
    }
    editForm.value[key] = val
  }
  const hasRedText = ref(false)
  const noticeText = ref('')
  const checkText = debounce((e) => {
    const val = e.target.value.replace(/\s+/g, '')
    if (!val) return
    hasRedText.value = false
    noticeText.value = ''
    const words = val.split('')
    words.forEach(t => {
      if (textExampel.includes(t)) {
        noticeText.value += t
      } else {
        hasRedText.value = true
        noticeText.value += `<span class="red">${t}</span>`
      }
    })
  }, 500)
  const lpPhotos = ref([])
  // 获取良品拍照
  const getSalfGoodsMpicNew = async () => {
    try {
      $indicator.open()
      const { code, userMsg, data } = await api.getSalfGoodsMpicNew(mkcId.value)
      if (code === 0) {
        lpPhotos.value = data.strategies.filter(item => !item.xcFlag) || []
      } else {
        $message.error(userMsg)
      }
    } catch (err) {
      console.error(err)
    } finally {
      $indicator.close()
    }
  }
  getSalfGoodsMpicNew()
  // 获取本次复检价
  const getCurCheckPrice = async () => {
    try {
      $indicator.open()
      if (!editForm.value.reportNo) return $message.error('未复检，不能获取本次复检价！')
      const { code, userMsg, data } = await api.refreshPricePop(editForm.value.reportNo)
      if (code === 0) {
        editForm.value.curCheckPrice = data?.price?.price
      } else {
        $message.error(userMsg)
      }
    } catch (err) {
      console.error(err)
    } finally {
      $indicator.close()
    }
  }
  const isPrintFlag = ref(false)
  const clientId = ref('')
  const printerList = ref([])
  const changePrint = (val) => {
    clientId.value = val
    isPrintFlag.value = Boolean(val)
    cookie.set('ClientNo5', clientId.value, { expires: 300 }) // 回收背标打印，前端储存统一用ClientNo5
  }
  const getPrinterList = async () => {
    clientId.value = cookie.get('ClientNo5') || ''
    bakMachinesApi.getonlineCh999printer().then(res => {
      if (res.stats === 1) {
        printerList.value = res.data || []
        const num = printerList.value.filter(item => item.clientId === clientId.value).length
        if (num) {
          isPrintFlag.value = true
        } else {
          clientId.value = ''
        }
      } else {
        clientId.value = ''
        $message.error('当前地区无在线客户端！')
      }
    })
  }
  // 获取第三方数据
  const getThirdData = async (mkcId, refreshFlag, isFirst) => {
    try {
      const { code, data, exData, userMsg } = await api.getThirdData(mkcId, refreshFlag)
      if (code === 0 && data) {
        leftTimes.value = exData.leftTimes
        if (data.activationDate?.includes('{')) {
          data.activationDate = JSON.parse(data.activationDate)?.date || undefined
        }
        if (data.warrantyDate?.includes('{')) {
          data.warrantyDate = JSON.parse(data.warrantyDate)?.date || undefined
        }
        thirdData.value = data || {}
        thirdText.value = Object.keys(thirdData.value)?.length ? '查看第三方数据' : `获取第三方数据（未查询到，剩余使用次数:${leftTimes.value}）`
        visible.value = Object.keys(thirdData.value)?.length && !isFirst // 不自动打开
      } else {
        $message.error(userMsg)
      }
    } catch (e) {
      console.log(e)
    }
  }
  const getServicesEnum = async () => {
    try {
      const { code, data, userMsg } = await api.getServicesEnum(mkcId.value)
      if (code === 0) {
        visible2.value = true
        typeOpts.value = data.bizTypeEnum || []
        queryType.value = typeOpts.value.filter(item => item.selected)[0]?.value
        if (queryType.value) {
          getAllThirdData()
        }
      } else {
        $message.error(userMsg)
      }
    } catch (e) {
      console.log(e)
    }
  }
  const getAllThirdData = async () => {
    try {
      if (loading.value) return
      loading.value = true
      const { code, data, userMsg } = await api.getAllThirdData(mkcId.value, queryType.value)
      if (code === 0) {
        thirdAllData.value = data || []
      } else {
        $message.error(userMsg)
      }
      loading.value = false
    } catch (e) {
      console.log(e)
    }
  }

  const finishCheck = async (flag = false) => {
    try {
      $indicator.open()
      const params = {
        mkcId: mkcId.value,
        clientId: clientId.value,
        isPrintFlag: isPrintFlag.value,
        printArea: $store.state.userInfo.Area,
        isConfirm: flag
      }
      const { code, data, userMsg } = await api.CheckMachineApi.finishCheck(params)
      $indicator.close()
      if (code === 0) {
        $message.success(data || '提交成功')
        isFinishedCheck.value = true
        getProductCateParams()
      } else if (code === 1005) {
        $confirm({
          title: '温馨提示',
          content: userMsg,
          onOk: () => {
            finishCheck(true)
          }
        })
      } else {
        $message.error(userMsg)
      }
      isFinishedCheck.value = false
    } catch (e) {
      console.log(e)
    }
  }
  const checkErrorInfo = () => {
    if (!checkData(editForm.value.createDate, editForm.value.activationDate, editForm.value.warrantyExpirationDate)) { // 日期校验
      $error({
        title: '校验错误，请修改后再提交',
        content: () => <div class="flex flex-col"><span style="padding: 10px;background: #fbead5;border-radius: 5px;color: #f75521; margin: 10px 0;width: 250px;">时间关系错误：生产日期、当前时间</span><p style="font-size: 15px;line-height: 26px;">请确认生产日期 ＜ 激活时间 ＜ 当前日期 且 生产日期 ＜ 激活时间 ＜ 保修时间<br/> * 除“未激活”和“已过保”</p></div>,
        okText: '关闭',
        centered: true,
        onOk: () => {}
      })
      return
    }
    const p1 = editForm.value.productName !== productDetail.value.productName
    const p2 = editForm.value.extraProductColor && editForm.value.productColor !== editForm.value.extraProductColor
    const p3 = editForm.value.extraImei && editForm.value.imei !== editForm.value.extraImei
    const p4 = editForm.value.extraProductMemory && editForm.value.productMemory !== editForm.value.extraProductMemory
    if (p1 || p2 || p3 || p4) { // 商品名字,颜色，imei, 内存校验
      $confirm({
        title: '确认后将按以下内容提交，请谨慎确认',
        content: () => <div>{ p1 ? <div class="flex flex-col"><span style="padding: 10px;background: #fbead5;border-radius: 5px;color: #f75521; margin: 10px 0;width: 250px;">请确认商品名称是否正确</span><p style="font-size: 15px;line-height: 26px;">新机商品：{productDetail.value.productName} /当前验机商品：{editForm.value.productName}</p></div> : null }
        { p2 ? <div class="flex flex-col"><span style="padding: 10px;background: #fbead5;border-radius: 5px;color: #f75521; margin: 10px 0;width: 250px;">请确认商品颜色是否正确</span><p style="font-size: 15px;line-height: 26px;">机大侠商品：{editForm.value.extraProductColor} /当前验机商品：{editForm.value.productColor}</p></div> : null }
        { p3 ? <div class="flex flex-col"><span style="padding: 10px;background: #fbead5;border-radius: 5px;color: #f75521; margin: 10px 0;width: 250px;">请确认商品IMEI是否正确</span><p style="font-size: 15px;line-height: 26px;">机大侠商品：{editForm.value.extraImei} /当前验机商品：{editForm.value.imei}</p></div> : null }
        { p4 ? <div class="flex flex-col"><span style="padding: 10px;background: #fbead5;border-radius: 5px;color: #f75521; margin: 10px 0;width: 250px;">请确认商品内存是否正确</span><p style="font-size: 15px;line-height: 26px;">机大侠商品：{editForm.value.extraProductMemory} /当前验机商品：{editForm.value.productMemory}</p></div> : null }
        </div>,
        okText: '确认提交',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => {
          submit()
        },
      })
    } else {
      submit()
    }
  }
  const submit = async () => {
    try {
      $indicator.open()
      const { code, userMsg } = await api.CheckMachineApi.toUsedGoods({ mkcId: mkcId.value })
      $indicator.close()
      if (code === 0) {
        $message.success(userMsg || '提交成功')
        return
      }
      $message.error(userMsg)
    } catch (e) {
      console.log(e)
    }
  }
  const reflashCheck = (val) => {
    detailFormRef.value.validateField(val)
  }
  const init = () => {
    getProductCateParams('first')
    getThirdData(mkcId.value, false, true) // 进来自动刷的时候不打开弹窗
  }
  init()

  watch(() => currentStep.value, (val) => {
    if (val === mainCateParam.value.length + 1 && !printerList.value.length) {
      getPrinterList()
    }
  })

  defineExpose({
    getProductCateParams
  })
</script>
<style lang="less">
  .flex-1{ flex: 1;}
  .addon-after{min-width:40px; padding:0 10px; height: 32px; line-height:32px; margin-left:-4px; border: 1px solid #d9d9d9; border-radius: 4px; border-left: none;background: #f5f5f5; color: #d3d3d3; text-align: center;}
  .table-form{
    border-left:1px solid #ddd; border-top: 1px solid #ddd;
    td{ border-right:1px solid #ddd; border-bottom: 1px solid #ddd; padding:10px;}
    .title{ background: #f1f1f1;}
    .ant-form-item { margin-bottom: 0px; padding-bottom:0px;}
    .sigle-tag{border: 1px solid #ddd;border-radius: 20px;padding: 4px 10px; cursor: pointer; line-height: 32px;}
    .selected{border: 1px solid #40a9ff;color: #40a9ff; background: #eaf2ff;}
    .fjqdBox {
      border:1px solid #e8e8e8;
      border-radius: 4px;
      width: calc(100% - 90px);
      :deep(.ant-select-auto-complete.ant-select .ant-input) {border-width:0px}
    }
    .extra{
      right:5px;
      top:0;
      height: 32px; line-height: 32px;
      color:#c7c7c7;
      width:60px;
      text-align: right;
    }
  }
</style>
<style lang="less" scoped>
.right-content {
  width: calc(100% - 455px);
  .steps-content {
    background: #fff;
    padding: 12px;
    height: calc(100vh - 268px);
    overflow-y: auto;
    .red-tag{
      background: #fcdada;
      color: #ff4949;
      padding: 2px 8px;
      border-radius: 4px;
    }
  }
  .productDetail{
    padding: 10px;
    .des {
      line-height: 25px;
      flex:1;
    }
  }
  .w180{ width: 180px;}
  .title{
    i{
      width: 3px; height: 16px; background: #40a9ff; border-radius: 4px;
    }
  }
}
.data-content{
  line-height: 30px;
}
.footer{ padding: 20px 0px 0;}
.lp-photo {
  .box{ border-radius: 3px; padding: 3px 5px; margin:0 10px 10px 0; cursor: pointer;}
}
.check-img {
    width: 14px;
    height: 14px;
  }
.img-view {
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
  z-index: 99;
  opacity: 0;
  overflow: hidden;
}
.sj-table{
  border-top:1px solid #ddd;
  border-left:1px solid #ddd;
  td{ border-right:1px solid #ddd; border-bottom: 1px solid #ddd; padding:10px; width:calc(100%/6)}
  .gray-bg{
    background: #f5f5f5;
  }
  .ant-form-item { margin-bottom: 0px; padding-bottom:0px;}
}
.fix-bottom{
  height: 60px;
  padding: 10px;
}
.blue-tag {
  background: #eaf6ff;
  padding: 2px 5px;
  border-radius: 5px;
  font-size: 12px;
}
</style>
