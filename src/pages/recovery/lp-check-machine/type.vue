<template>
  <tr>
    <td class="w180 text-center"><i v-if="c.isReminder" class="red">*</i>{{c.name}}</td>
    <td>
      <a-form-model-item :prop="'info' + c.paramId" style="">
        <div class="blue lines-1" v-if="c.extraInfo">
          <a-tooltip placement="top">
            <template #title>
              <span>{{ c.extraInfo }}</span>
            </template>
            <span class="blue-tag">{{ c.extraInfo }}</span>
          </a-tooltip>
        </div>
        <!--文本框-->
        <template v-if="c.inputType === 0">
          <div class="flex flex-align-center">
            <a-input v-model="editForm[`info${c.paramId}`]" placeholder="请输入" class="flex-1" :disabled="disabled" :maxLength="50" allowClear @blur="$emit('change', editForm[`info${c.paramId}`])"></a-input>
            <span class="addon-after">{{editForm[`info${c.paramId}`].length}}/50</span>
          </div>
        </template>
        <!--多行文本框-->
        <template v-if="c.inputType === 1">
          <a-input :auto-size="{ minRows: 2 }" placeholder="请输入" class="flex-1" :disabled="disabled" type="textarea" :maxLength="200" v-model="editForm[`info${c.paramId}`]" allowClear></a-input>
          <span style="float:right; color:#d3d3d3">{{editForm[`info${c.paramId}`].length}}/200</span>
        </template>
        <!--单选-->
        <template v-if="c.inputType === 2">
          <a-radio-group v-model="editForm[`info${c.paramId}`]" v-if="disabled" class="flex flex-wrap">
            <div v-for="(item, idx) in c.option" :key="idx" class="flex flex-wrap">
              <a-radio-button :value="item.id" :class="[editForm[`info${c.paramId}`] === item.id && !item.isXc ? 'cur tag' : editForm[`info${c.paramId}`] === item.id && item.isXc ? 'red tag' : 'disabled tag']">
                {{item.name}}
              </a-radio-button>
              <div class="flex flex-align-center" v-if="editForm[`info${c.paramId}`] === item.id && item.commentFlag">
                <a-input :maxLength="20" placeholder="请输入备注" type="text" v-model="item.comment" style="width:180px" :disabled="true" allowClear></a-input>
                <span class="addon-after">{{item.comment.length}}/20</span>
              </div>
            </div>
          </a-radio-group>
          <a-radio-group v-model="editForm[`info${c.paramId}`]" v-else class="flex flex-wrap" @change="handelChange(c.paramId)">
            <div v-for="(item, idx) in c.option" :key="idx" class="flex flex-wrap">
              <a-radio-button :value="item.id" :class="[editForm[`info${c.paramId}`] === item.id && !item.isXc ? 'cur tag' : editForm[`info${c.paramId}`] === item.id && item.isXc ? 'red tag' : 'tag']"
              >{{item.name}}</a-radio-button>
              <div class="flex flex-align-center" v-if="editForm[`info${c.paramId}`] === item.id && item.commentFlag">
                <a-input :maxLength="20" placeholder="请输入备注" type="text" v-model="item.comment" @change="$emit('reflashRules', `info${c.paramId}`)" style="width:180px" allowClear></a-input>
                <span class="addon-after">{{item.comment.length}}/20</span>
              </div>
            </div>
          </a-radio-group>
        </template>
        <!--多选-->
        <template v-if="c.inputType === 3" >
          <a-checkbox-group v-model="editForm[`info${c.paramId}`]" v-if="disabled" class="flex flex-wrap">
            <div v-for="(item, idx) in c.option" :key="idx" class="flex flex-wrap">
              <a-checkbox :value="item.id" :class="[editForm[`info${c.paramId}`].includes(item.id) && !item.isXc ? 'cur tag' : editForm[`info${c.paramId}`].includes(item.id) && item.isXc ? 'red tag' : 'tag disabled']">{{item.name}}</a-checkbox>
              <div class="flex flex-align-center" v-if="editForm[`info${c.paramId}`].includes(item.id) && item.commentFlag">
                <a-input :maxLength="20" placeholder="请输入备注" type="text" v-model="item.comment" style="width:180px" :disabled="true" allowClear></a-input>
                <span class="addon-after">{{item.comment.length}}/20</span>
              </div>
            </div>
          </a-checkbox-group>
          <a-checkbox-group v-model="editForm[`info${c.paramId}`]" v-else class="flex flex-wrap" @change="handelChange(c.paramId)">
            <div v-for="item in c.option" :key="item.id" class="flex flex-wrap">
              <a-checkbox :value="item.id" :class="[editForm[`info${c.paramId}`].includes(item.id) && !item.isXc ? 'cur tag' : editForm[`info${c.paramId}`].includes(item.id) && item.isXc ? 'red tag' : 'tag']"
              >{{item.name}}</a-checkbox>
              <div class="flex flex-align-center" v-if="editForm[`info${c.paramId}`].includes(item.id) && item.commentFlag">
                <a-input :maxLength="20" placeholder="请输入备注" type="text" v-model="item.comment" @blur="$emit('reflashRules', `info${c.paramId}`)" style="width:180px" allowClear></a-input>
                <span class="addon-after">{{item.comment.length}}/20</span>
              </div>
            </div>
          </a-checkbox-group>
        </template>
        <!--日期-->
        <template v-if="c.inputType === 5">
          <a-date-picker v-model="editForm[`info${c.paramId}`]" :disabled="disabled" :format="['YYYY-MM-DD', 'YYYY/MM/DD']" valueFormat="YYYY-MM-DD 00:00:00" style="width:100%"></a-date-picker>
        </template>
        <!--数字-->
        <template v-if="c.inputType === 6">
          <a-input-number :ref="`input-${c.paramId}`" placeholder="请输入" :disabled="disabled" :precision="2" v-model="editForm[`info${c.paramId}`]" allowClear style="width:100%" ></a-input-number>
        </template>
        <!--单选可编辑-->
        <template v-if="c.inputType === 8">
          <a-auto-complete
            v-model="editForm[`info${c.paramId}`]"
            placeholder="请选择"
            :disabled="disabled"
            :filter-option="false"
            allowClear
          >
            <template slot="dataSource">
              <a-select-option v-for="(item, idx) in c.option" :key="idx" :value="item.name">{{ item.name }}</a-select-option>
            </template>
          </a-auto-complete>
        </template>
        <!--百分比-->
        <template v-if="c.inputType === 9">
          <div class="flex flex-align-center">
            <a-input-number :ref="`input-${c.paramId}`" placeholder="请输入" :disabled="disabled" :min="0" :max="100" :precision="0" v-model="editForm[`info${c.paramId}`]" allowClear style="width:30%"></a-input-number>
            <span class="addon-after">%</span>
          </div>
        </template>
      </a-form-model-item>
    </td>
    <td v-if="['外观信息', '功能信息', '维修信息'].includes(row.name)">
      <div class="flex flex-center">
        <img class="select-state-icon" :src="!c.isXc ? yes : no">
      </div>
    </td>
    <td v-if="['外观信息', '功能信息'].includes(row.name)">
      <upload-img v-if="c.isXc" :picList="c.xcImages" width="64px" height="64px" :canAdd="!disabled" :canDel="!disabled" @previewImg="val => handlePreview(val, c.xcImages)" @getImage="val => changeImageXc(val, c)"></upload-img>
    </td>
  </tr>
</template>

<script>
  import Vue, { getCurrentInstance } from 'vue'
  import uploadImg from './uploadImg.vue'
  import Viewer from 'v-viewer'
  import yes from '../../../assets/images/yes.png'
  import no from '../../../assets/images/no.png'

  Vue.use(Viewer)
  export default {
    components: { uploadImg },
    props: {
      c: {
        type: Object,
        default: () => { return {} }
      },
      editForm: {
        type: Object,
        default: () => { return {} }
      },
      disabled: {
        type: Boolean,
        default: false
      },
      row: {
        type: Object,
        default: () => { return {} }
      }
    },
    setup (props, { emit }) {
      const { $viewerApi } = getCurrentInstance().proxy
      const handelChange = (val) => {
        emit('change', props.editForm[`info${val}`])
        emit('reflashRules', `info${val}`)
      }
      const changeImageXc = (data, record) => {
        record.xcImages = [...data]
      }
      const handlePreview = (index, list) => {
        const picArr = list.map(t => t.fileUrl)
        $viewerApi({
          images: picArr
        }).view(index)
      }
      return {
        yes,
        no,
        handelChange,
        changeImageXc,
        handlePreview
      }
    }
  }
</script>

<style lang="scss" scoped>
.w180{width: 180px;}
.extr {top:-38px; right:0; width: 40%; text-align: right;}

.tag{
  margin:3px; border-radius: 16px !important; padding: 0px 10px; line-height:30px;font-size:14px; border: 1px solid #ccc; display: inline-block; cursor: pointer;
  &.cur{border: 1px solid #1890ff; color: #1890ff;}
  &.red{border: 1px solid #ff0000; color: #ff0000;}
  &.disabled{background: #f5f5f5; color: #999;}
  :deep(.ant-checkbox) {display: none}
  :deep(.ant-checkbox-wrapper){
    span{padding: 2px 5px;}
  }
}
:deep(.ant-radio-button-wrapper){padding:0 10px;}
:deep(.ant-form-vertical .ant-form-item) { padding-bottom:0}
.select-state-icon {
  width: 14px;
  height: 14px;
}
.blue-tag {
  background: #eaf6ff;
  padding: 2px 5px;
  border-radius: 5px;
  font-size: 12px;
}
</style>
