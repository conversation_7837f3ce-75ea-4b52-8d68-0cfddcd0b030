<template>
  <div>
    <a-card>
      <div class="flex flex-align-center">
        <span class="font-20 bold mr-10">良品验机</span>
        <a-button size="small" type="danger" class="mr-10" @click="recheckShortNumber"><a-icon type="edit" />录入机大侠短编号</a-button>
        <a-button v-if="ludashiBindShowFlag" size="small" type="danger" class="mr-16" @click="bindLudash"><a-icon type="edit" />录入鲁大师报告</a-button>
      </div>
    </a-card>
    <div class="flex flex-justify-between mt-10">
      <div id="ai-hui-shou">
        <a-tabs type="card" v-model="reportTabKey" @change="reportTabChange">
          <a-tab-pane key="4" tab="验机报告" >
            <inspection-report ref="inspectionRef" v-if="lpId && reportTabKey === '4'" :id="lpId"></inspection-report>
          </a-tab-pane>
          <a-tab-pane key="1" tab="复检报告">
            <a-icon v-if="loading" type="loading" style="font-size: 30px; margin: 150px 206px;"/>
            <iframe v-if="!aiFeatureShow" title="修改特征" name="myframe" :src="pjtSrc" ref="iframeRef" width="445px" height="740px" frameborder="0"></iframe>
            <div v-else class="ai-feature-popup">
              <div class="feature-content" v-if="propertyValues.length">
                <p class="feature-title flex flex-justify-between grey-9">
                  <span>复检人：{{ featureByAi.goodResult.checkUser || '-' }}</span>
                  <span v-if="rptShortCode">报告编号：{{ rptShortCode }}</span>
                </p>
                <div v-for="(arrItem, idx) in propertyValues" :key="idx" class="check-list flex flex-col margin-bottom">
                  <p class="arr-title">{{ arrItem && arrItem[0].typeName }}</p>
                  <div v-for="t in arrItem" :key="t.ppnId" class="flex item flex-justify-between margin-bottom">
                    <p class="flex flex-align-center">
                      <img class="select-state-icon" :src="t.isNormal ? yes : no">
                      <span class="grey-9">{{ t.ppnName }}</span>
                      <img v-if="t.machineHit" class="margin-left" height="14" src="../../../assets/images/jiuji-icon.png">
                    </p>
                    <p style="max-width:60%">{{ t.ppvName }}</p>
                  </div>
                </div>
              </div>
              <div class="recheck-agin-btn pointer">
                <div @click="recheckAgin" class="btn">重新复检</div>
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="2" tab="机大侠报告" v-if="rptShortCode">
            <jdx-report-small ref="jdxReportRef" :mkcid="mkcId" :isByMkcid="true" height="740px"></jdx-report-small>
          </a-tab-pane>
          <a-tab-pane key="3" tab="鲁大师报告" v-if="ludashiReport">
            <ludash-report ref="ludashReportRef" :mkcid="mkcId"></ludash-report>
          </a-tab-pane>
        </a-tabs>
      </div>
      <checkMachine ref="checkMachineRef" @change="getLpId" @refrash="refrash"></checkMachine>
    </div>
    <a-modal v-model="shortNumberShow" title="机大侠短编号" okText="提交并刷新" @ok="shortNumberHandleOk" :confirmLoading="shortNumberLoading">
      <a-form-model  ref="shortNumberRuleForm"
                     :model="shortNumberForm"
                     :label-col="{span: 5}" :wrapper-col="{ span: 15 }"
                     :rules="shortNumberRules">
        <a-form-model-item label="短编号" prop="shortNumber">
          <a-input v-model="shortNumberForm.shortNumber" placeholder="请输入" :maxLength="50" allowClear>
            <template slot="addonAfter">
              {{ shortNumberForm.shortNumber ? shortNumberForm.shortNumber.length : 0 }}/50
            </template>
          </a-input>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal v-model="ludashShow" title="鲁大师报告编号" okText="提交并刷新" @ok="ludashHandleOk" :confirmLoading="ludashLoading">
      <a-form-model  ref="ludashRuleForm"
                     :model="ludashForm"
                     :label-col="{span: 5}" :wrapper-col="{ span: 15 }"
                     :rules="ludashRules">
        <a-form-model-item label="报告ID" prop="ludash">
          <a-input v-model="ludashForm.id" placeholder="请输入" :maxLength="50" allowClear>
            <template slot="addonAfter">
              {{ ludashForm.id ? ludashForm.id.length : 0 }}/50
            </template>
          </a-input>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script setup lang="jsx">
  import { ref, getCurrentInstance, onMounted, onBeforeUnmount } from 'vue'
  import jdxReportSmall from '../jdx-report-small.vue'
  import ludashReport from '../ludash-report.vue'
  import checkMachine from './check-machine.vue'
  import inspectionReport from './inspection-report.vue'
  import { resetArray } from '../utils/tools'
  import Api from '~/api/recovery'
  import yes from '../../../assets/images/yes.png'
  import no from '../../../assets/images/no.png'

  const vm = getCurrentInstance().proxy
  const shortNumberShow = ref(false)
  const shortNumberLoading = ref(false)
  const shortNumberForm = ref({
    shortNumber: undefined
  })
  const shortNumberRules = ref({
    shortNumber: [{
      required: true,
      message: '请填写短编号',
      trigger: 'blur'
    }],
  })
  const ludashShow = ref(false)
  const ludashLoading = ref(false)
  const ludashForm = ref({
    id: undefined
  })
  const ludashRules = ref({
    id: [{
      required: true,
      message: '请填写报告ID',
      trigger: 'blur'
    }],
  })
  const loading = ref(true)
  const aiFeatureShow = ref(false)
  const rptShortCode = ref('')
  const ludashiBindShowFlag = ref(false)
  const ludashiReport = ref(undefined) // 鲁大师报告编号
  const baseData = ref({})
  const reportTabKey = ref('1')
  const featureByAi = ref({})
  const goodResult = ref({})
  const propertyValues = ref([])
  const mkcId = ref(vm.$route.query.mkc_id || '')
  const reportNo = ref('')
  const resultId = ref('')
  const pjtSrc = ref('')
  const showReportNo = ref(false) // 查询报告详情是否显示
  const otherPjtPid = ref(undefined)
  const valuationInfo = ref(null)

  const lpId = ref('')
  const getLpId = (val) => {
    lpId.value = val
  }
  const getReCheckInfo = () => {
    vm.$indicator.open()
    Api.getXcReCheckInfo(mkcId.value).then(async ({ code, userMsg, data }) => {
      if (code === 0) {
        baseData.value = Object.assign(baseData.value, data)
        resultId.value = data.checkResultId
        baseData.value.price = data.inprice
        rptShortCode.value = data.rptShortCode
        return
      }
      vm.$message.error(userMsg)
    }).then(() => {
      poupAiFeature()
    }).finally(() => {
      vm.$indicator.close()
    })
  }
  const isShowReportNo = async (reportNo, mkcId) => { // 查询报告详情是否显示
    try {
      const { code, data, userMsg } = await Api.isShowReportNo(reportNo, mkcId)
      if (code === 0) {
        showReportNo.value = data.showFlag
        otherPjtPid.value = data.pjtPid || ''
        return
      }
      vm.$message.error(userMsg)
    } catch (e) {
      console.error(e)
    }
  }
  const poupAiFeature = () => {
    vm.$nextTick(() => {
      if (resultId.value) {
        vm.$indicator.open()
        Api.getFeatureDetailByAi(resultId.value, baseData.value.price, baseData.value.eveResultId, baseData.value.basket_id, mkcId.value)
          .then(({ code, data, userMsg }) => {
            if (code === 0) {
              featureByAi.value = data || {}
              goodResult.value = data.goodResult || {}
              propertyValues.value = data.propertyValues.length > 0 ? resetArray(data.propertyValues) : []
              loading.value = false
              if (goodResult.value.rptShortCode) {
                rptShortCode.value = goodResult.value.rptShortCode
              }
              if (goodResult.value.reportNo) {
                isShowReportNo(goodResult.value.reportNo, mkcId.value)
              }
              return goodResult.value.reportNo
            }
            vm.$message.error(`请求商品特征失败 ${userMsg}`)
          })
          .then(reportNo => {
            if (!reportNo) {
              aiFeatureShow.value = false
              return postFeatureByLedder()
            }
            aiFeatureShow.value = true
          })
          .catch(error => {
            vm.$message.error(error.message)
            console.error(error)
          })
          .finally(() => {
            vm.$indicator.close()
          })
      } else {
        postFeatureByLedder()
      }
    })
  }
  const postFeatureByLedder = async (type) => { // 导师复检(B端)
    let accessToken = await getPjtToken()
    let config = {
      rptShortCode: rptShortCode.value,
      reportNo: type === 'hasShortCode' ? '' : showReportNo.value ? goodResult.value.reportNo || '' : '',
      accessToken: accessToken,
      mode: 2,
      productId: otherPjtPid.value || goodResult.value.pjtPid || baseData.value.pjtPid,
      theme: {
        primaryColor: '#1890FF', // 主色，string
        secondaryColor: '#dddddd',
        submitFontColor: '#ffffff'
      },
      submitBtnText: '提交并刷新'
    }
    let src = ''
    const dev = ['localhost', 'oa.dev.9ji.com', 'test01.oa.saas.ch999.cn'].includes(window.location.hostname)
    if (dev) {
      src = 'https://uat-jdx-open.aihuishou.com/h5/price-info?config='
    } else {
      src = 'https://jdx-open.aihuishou.com/h5/price-info?config='
    }
    pjtSrc.value = src + encodeURIComponent(JSON.stringify(config))
    loading.value = false
  }
  const getPjtToken = (forceRefresh) => {
    let params = {
      forceRefresh: forceRefresh || false,
      resultId: resultId.value
    }
    vm.$indicator.open()
    return new Promise(resolve => {
      Api.getPjtToken(params).then(({ code, data, userMsg, msg }) => {
        if (code === 0) {
          resolve(data.token)
          return
        }
        vm.$message.error(`获取拍机堂access_token失败 ${userMsg || msg}`)
      }).finally(() => {
        vm.$indicator.close()
      })
    })
  }
  const iframeRef = ref(null)
  const handleMessage = async (event) => {
    const { data: payload } = event
    // ['https://jdx.aihuishou.com'].includes(origin) && 域名判断
    if (payload.type === 'unauthorized') {
      let otherWindow = iframeRef.value.contentWindow
      let accessToken = await getPjtToken(true)
      otherWindow.postMessage(
        {
          type: 'refreshAccessToken',
          data: accessToken
        },
        '*'
      )
    } else if (payload.type === 'reportNo') {
      reportNo.value = payload.data
      fetchFinalPrice() // 估价
    } else if (payload.type === 'PARENT_MSG') {
      if (!['https://m.dev.9ji.com', 'https://m.9ji.com'].includes(event.origin)) return
      console.log('收到子页面消息:', event.data)
    }
  }
  const fetchFinalPrice = () => { // _reportNo：更新质检报告
    vm.$indicator.open()
    let params = {
      standard: 'B',
      reportNo: reportNo.value, // 质检报告编号
      areaId: vm.$store.state.userInfo.areaid,
      client: 'OA'
    }
    Api.getFinalPriceByJava(params).then(({ code, data, userMsg }) => {
      if (code === 0) {
        valuationInfo.value = data
        return data
      }
      vm.$message.error(`请求估价失败 ${userMsg}`)
    }).then(valuationInfo => {
      savePjtResult(valuationInfo)
    }).finally(() => {
      vm.$indicator.close()
    })
  }
  const savePjtResult = (valuationInfo) => {
    let resultType = 1
    if (resultId.value) {
      resultType = JSON.parse(resultId.value) ? 2 : 1
    }
    vm.$indicator.open()
    let params = {
      reportNo: reportNo.value, // 拍机堂质检编号
      pjtPid: goodResult.value.pjtPid, // 拍机堂商品id
      finalPrice: valuationInfo?.finalPrice,
      evaSalePrice: valuationInfo?.evaSalePrice,
      standard: 'B', // 质检标准
      resultType, // 估值结果类型 1-常规 2-复检
      activityAdded: valuationInfo?.activityAdded || goodResult.value.activityAdded || 0,
      realAddPrice: valuationInfo?.realAddPrice || goodResult.value.realAddPrice || 0,
      activeName: valuationInfo?.activeName || goodResult.value.activeName || '',
      activityMsg: valuationInfo?.msg || goodResult.value.activityMsg || '',
      evaShop: goodResult.value.evaShop || '',
      checkUser: vm.$store.state.userInfo.UserName,
      eveResultId: baseData.value.eveResultId || '',
      rptShortCode: rptShortCode.value
    }
    Api.savePjtResult(params).then(({ code, data, userMsg }) => {
      if (code === 0) {
        resultId.value = data // 导师复检id、或初检id
        return data
      }
      vm.$message.error(`保存拍机堂质检结果失败 ${userMsg}`)
    }).then(() => {
      saveFeatureByLeader()
    }).then(() => {
      submitXcRecheck()
    }).finally(() => {
      vm.$indicator.close()
    })
  }
  const saveFeatureByLeader = () => {
    aiFeatureShow.value = true
    poupAiFeature()
  }
  const recheckAgin = () => {
    aiFeatureShow.value = false
    vm.$nextTick(() => {
      postFeatureByLedder()
    })
  }
  const submitXcRecheck = async () => {
    try {
      vm.$indicator.open()
      const params = {
        mkcId: mkcId.value,
        checkResultId: resultId.value,
        isBidding: false,
        startingPrice: valuationInfo.value?.evaSalePrice || goodResult.value?.evaSalePrice || ''
      }
      const { code, userMsg } = await Api.submitXcRecheck(params)
      if (code === 0) {
        vm.$message.success(userMsg || '提交成功')
        checkMachineRef.value?.getProductCateParams()
      } else {
        vm.$message.error(userMsg || '提交失败')
      }
    } catch (e) {
      console.log(e)
    } finally {
      vm.$indicator.close()
    }
  }
  const inspectionRef = ref(null)
  const refrash = () => {
    inspectionRef.value?.init()
  }
  const checkLudashStatus = async () => { // 鲁大师状态
    try {
      const { code, data, userMsg } = await Api.checkLudashStatus(mkcId.value)
      if (code === 0) {
        ludashiReport.value = data.ludashiReport || ''
        ludashiBindShowFlag.value = data.ludashiBindShowFlag
      } else {
        vm.$message.error(userMsg || '请求失败')
      }
    } catch (e) {
      console.error(e)
    }
  }
  const recheckShortNumber = () => {
    shortNumberShow.value = true
    shortNumberForm.value.shortNumber = undefined
  }
  const bindLudash = () => {
    ludashShow.value = true
    ludashForm.value.id = undefined
  }
  const shortNumberRuleForm = ref(null)
  const checkMachineRef = ref(null)
  const shortNumberHandleOk = () => {
    shortNumberRuleForm.value.validate(async (valid) => {
      if (valid) {
        const query = {
          ppid: baseData.value.ppriceid || '',
          rptShortCode: shortNumberForm.value.shortNumber
        }
        shortNumberLoading.value = true
        const { code, userMsg } = await Api.rptShortCodeCheck(query)
        shortNumberLoading.value = false
        if (code === 0) {
          shortNumberShow.value = false
          aiFeatureShow.value = false
          rptShortCode.value = shortNumberForm.value.shortNumber
          postFeatureByLedder('hasShortCode')
          checkMachineRef.value?.getProductCateParams()
        } else {
          vm.$message.error(userMsg)
        }
      } else {
        return false
      }
    })
  }
  const ludashRuleForm = ref(null)
  const ludashReportRef = ref(null)
  const jdxReportRef = ref(null)
  const reportTabChange = (val) => {
    if (val === '2') {
      vm.$nextTick(() => {
        jdxReportRef.value?.getResultReport()
      })
    } else if (val === '3') {
      vm.$nextTick(() => {
        ludashReportRef.value?.getResultReport()
      })
    }
  }
  const ludashHandleOk = () => {
    ludashRuleForm.value.validate(async (valid) => {
      if (valid) {
        const params = {
          mkcId: mkcId.value || '',
          ludashiReport: ludashForm.value.id,
          ludashiReportBinder: vm.$store.state.userInfo.UserName
        }
        ludashLoading.value = true
        const { code, userMsg } = await Api.bindLudashReport(params)
        ludashLoading.value = false
        if (code === 0) {
          ludashShow.value = false
          ludashiReport.value = ludashForm.value.id
          vm.$nextTick(() => {
            ludashReportRef.value?.getResultReport()
          })
          checkMachineRef.value?.getProductCateParams()
        } else {
          vm.$message.error(userMsg)
        }
      } else {
        return false
      }
    })
  }

  const init = () => {
    getReCheckInfo()
    vm.$tnt.xtenant < 1000 && checkLudashStatus()
  }
  init()
  onMounted(() => {
    window.addEventListener('message', handleMessage)
  })
  onBeforeUnmount(() => {
    window.removeEventListener('message', handleMessage)
  })
</script>
<style lang="less" scoped>
#ai-hui-shou {
    width: 445px;
    overflow: hidden;
    background: #fff;
    border:1px solid #ddd;
    height: calc(100vh - 130px);
  }
:deep(.ant-card-body) { padding: 10px 24px;}
:deep(.ant-tabs-bar) {
  margin: 0;
}
.ai-feature-popup {
  position: relative;
  padding-bottom:60px;
  .feature-content {
    background: #ffffff;
    padding: 10px;
    height: calc(100vh - 235px);
    overflow-y: auto;
    .feature-title {
      border-bottom: 1px solid #efefefef;
      padding: 0 0 10px;
      margin-bottom: 10px;
    }
  }
  .recheck-agin-btn {
    padding: 10px 12px;
    position: absolute;
    width: 100%;
    bottom: 0px;
    left:0;
    background: #ffffff;
    .btn {
      border-radius: 30px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background: #1890ff;
      color: #ffffff;
    }
  }
}
:deep(.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab) {
  padding: 0 10px;
  border-left: none;
  border-top: none;
  &:last-child{border-right: none;}
}
.select-state-icon {
  width: 14px;
  height: 14px;
  margin-right: 12px;
}
</style>
