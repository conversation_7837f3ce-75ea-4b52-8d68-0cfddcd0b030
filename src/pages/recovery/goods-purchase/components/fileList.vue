<style lang="scss" scoped>
.op-m { margin-right: 10px;}
</style>
<template>
  <a-card class="mb-20" title="上传附件">
    <a-table :data-source="value" :columns="columns" :pagination="false" rowKey="fid"></a-table>
    <div v-if="!disabled">
      <uploader add-size :buttonName="['电脑上传','手机上传']" class="operation" @change="uploadedAttachments" ref="uploadInter"
    :showPreview="false" :maxSize="20971520" accept="image/*,.pdf,.pdfx,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.ofd"/>
    </div>
    <a-modal v-model="is.previewImg" :title="img.filename" width="40%" :footer="null" closable
              @cancel="closePreview">
      <a :href="img.url" target="_blank">
        <img ref="img" :src="img.url" width="100%"/>
      </a>
    </a-modal>

    <a-modal v-model="is.renameVisible" title="附件重命名" width="480px" closable @cancel="closeRename" @ok="renameHandler">
      <a-form-model destroy-on-close ref="form" :model="form" :rules="rules">
        <a-form-model-item prop="fileNameNoSuffix" label="文件名">
          <a-input v-model="form.fileNameNoSuffix" placeholder="输入文件名" :suffix="form.fileSuffix||''"/>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </a-card>
</template>
<script lang="jsx">
  import Vue from 'vue'
  import Uploader from '~/components/uploader'
  import { saveAs } from 'file-saver'
  import { Base64 } from 'js-base64'
  import 'viewerjs/dist/viewer.css'
  import Viewer from 'v-viewer'

  Vue.use(Viewer)

  export default {
    name: 'attachment',
    components: {
      uploader: Uploader
    },
    props: {
      value: {
        type: Array,
        default: []
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data () {
      const h = this.$createElement
      return {
        columns: [
          {
            title: '序号',
            width: '15%',
            dataIndex: 'index',
            customRender: (text, record, index) => {
              return <div>{index + 1}</div>
            }
          },
          {
            title: '文件名',
            width: '55%',
            dataIndex: 'fileName',
            customRender: (text, record) => {
              return <div style="color:#239DFC;cursor:pointer" onClick={() => this.preview(record)}> {text}</div>
            }
          },
          {
            title: '操作',
            width: '30%',
            key: 'op',
            customRender: (text, record, index) => {
              return <div><a class="op-m" onClick={() => this.download(record)}>下载</a>
                {!this.disabled ? <a class="op-m" onClick={() => this.openRename(record)}>重命名</a> : null }
                {!this.disabled ? <a class="op-m red" onClick={() => this.del(index)}>删除</a> : null }
            </div>
            }
          },
        ],
        is: {
          previewImg: false,
          renameVisible: false
        },
        img: {
          filename: '',
          url: '',
        },
        form: {
          fileNameNoSuffix: '',
          lastFileName: '',
          fileSuffix: ''
        },
        rules: {
          fileNameNoSuffix: [{ required: true, message: '请输入文件名', trigger: ['blur', 'change'] }]
        },
      }
    },
    mounted () {
      this.attachmentsIsImg()
    },
    methods: {
      async attachmentsIsImg () {
        for (let i = 0; i < this.value.length; i++) {
          const file = this.value[i]
          if (file.isImg === undefined) {
            file.isImg = await this.isImg(file.fileUrl)
            this.$set(this.value, i, file)
            this.$emit('input', this.value)
          }
        }
      },
      async uploadedAttachments (list) {
        const fileList = []
        for (const file of list) {
          await this.extendAttachmentInfo(file)
          fileList.push(file)
        }
        this.value.push(...fileList)
        this.$emit('input', this.value)
      },

      async extendAttachmentInfo (file) {
        Object.assign(file, {
          filePath: file.fileUrl,
          fileStatus: false,
          fileSize: file.size,
          fileSuffix: `.${file.fileName.split('.').pop()}`,
          isImg: await this.isImg(file.fileUrl)
        })
      },

      download (file) {
        saveAs(file.fileUrl, file.fileName)
      },

      del (idx) {
        this.$confirm({
          title: '你确定要删除此附件?',
          okText: '确定',
          okType: 'danger',
          cancelText: '取消',
          onOk: async () => {
            this.value.splice(idx, 1)
            this.$emit('input', this.value)
          }
        })
      },

      openRename (file) {
        const filenameArr = file.fileName.split('.')
        const suffix = `.${filenameArr.pop()}`
        const fileNameNoSuffix = filenameArr.join('.')
        this.form = {
          fileNameNoSuffix: fileNameNoSuffix,
          lastFileNameNoSuffix: fileNameNoSuffix,
          fileSuffix: suffix,
          fid: file.fid,
        }
        this.is.renameVisible = true
      },
      renameHandler () {
        this.$refs.form.validate(async valid => {
          if (valid) {
            if (this.form.lastFileNameNoSuffix === this.form.fileNameNoSuffix) {
              return this.$message.info('文件名未发生变化')
            }
            const file = this.value.find(item => item.fid === this.form.fid)
            if (file) {
              file.fileName = `${this.form.fileNameNoSuffix}${this.form.fileSuffix}`
            }
            return this.closeRename()
          }
        })
      },
      closeRename () {
        this.is.renameVisible = false
      },

      preview (file) {
        if (file.isImg) {
          this.img.url = file.fileUrl
          this.img.filename = file.fileName
          const urls = this.value.filter(item => item.isImg).map(item => item.fileUrl)
          const defaultIndex = urls.findIndex(item => item === this.img.url) || 0
          const viewer = this.$viewerApi({
            images: urls
          })
          viewer.view(defaultIndex)
        } else {
          if (file.id) {
            if (file.fileSuffix === '.pdf') {
              window.open(file.fileUrl, '_blank')
            } else {
              let url = ''
              let patternFileExtension = /\.([0-9a-z]+)(?:[?#]|$)/i
              let fileExtension = (file.fileUrl).match(patternFileExtension)
              if (!fileExtension) {
                url = file.fileUrl + file.fileSuffix
              } else {
                url = file.fileUrl
              }
              const suffix = url.split('.').pop()
              window.open('https://doc.9xun.com/onlinePreview?url=' + (suffix === 'xls' ? Base64.encode(url.replace('.xls', '.xlsx')) : Base64.encode(url)))
            }
          } else {
            this.download(file)
          }
        }
      },
      closePreview () {
        this.is.previewImg = false
      },
      isImg (url) {
        return new Promise(resolve => {
          let img = new Image()
          let width = 0
          img.src = url
          if (img.complete) {
            width = img.width
            return resolve(Boolean(width))
          } else {
            img.onload = () => {
              img.onload = null
              width = img.width
              return resolve(Boolean(width))
            }
            img.onerror = () => {
              width = img.width
              img.onerror = null
              return resolve(Boolean(width))
            }
          }
        })
      },
    }
  }
</script>
