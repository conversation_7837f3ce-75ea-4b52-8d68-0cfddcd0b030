/**
 * Created by ebi on 2018/10/22.
 */
import Vue from 'vue'
import './plugins/before-import'
import 'pdfjs-dist/es5/build/pdf.worker.entry'
import axios from 'axios'
import { createAxues } from 'axues'
import cookie from 'js-cookie'
import store from './store'
import router from './router'
import api from './api'
import App from './App.vue'
import bus from './util/bus'
import './polyfill'
import 'element-ui/lib/theme-chalk/index.css'

import './assets/style/main.css'
import 'font-awesome/css/font-awesome.min.css'

import page from './layout/page.vue'
import { NiImg, NiLog, NiListPage, NiFilter, NiFilterItem, NiTable } from '@jiuji/nine-ui'
import tabsMessage from './util/tabs-message'
import { initGlobalReportMqtt } from '~/util/mq-global-report'
import opJump from './pages/home/<USER>/oa-jump.vue'

import {
  <PERSON><PERSON>,
  Button,
  Card,
  Cascader,
  Checkbox,
  Col,
  DatePicker,
  Descriptions,
  Divider,
  Form,
  Icon,
  Input,
  InputNumber,
  message,
  Modal,
  notification,
  Popover,
  Radio,
  Row,
  Select,
  Spin,
  Statistic,
  Switch,
  Steps,
  Space,
  Table,
  Tabs,
  Tag,
  Carousel,
  Timeline,
  TimePicker,
  Tooltip,
  TreeSelect,
  Upload,
  AutoComplete,
  Empty,
  Progress,
  Popconfirm,
  FormModel,
  Collapse,
  Drawer,
  List,
  Transfer,
  Tree,
  Skeleton
} from 'ant-design-vue'

import WrappedModal from '~/components/WrappedModal'

import cssVars from 'css-vars-ponyfill'
import watermark from '~/util/watermark-dom'
import { dateFormat } from '~/util/common'
import DialogPlugin from '~/util/dialog'
import DirectiveRank from '~/directives/rank'
import ErrorReport from '~/util/error-report'
import Clipboard from 'clipboard'
import qs from 'qs'

import '~/plugins/commonComponents'

import Hls from 'hls.js'
import flvjs from 'flv.js'

Vue.config.productionTip = false
Vue.config.warnHandler = () => null
window.Hls = Hls
window.flvjs = flvjs
tabsMessage()

Vue.use(DirectiveRank)

cssVars({
  onlyLegacy: true,
  watch: true
})

const tnt = window.tenant
Vue.prototype.$tnt = tnt
Vue.prototype.$isSaaS = tnt.id > 2 // 是输出的系统
Vue.config.devtools = true
Vue.use(Tree).use(Transfer).use(Table).use(Card).use(Icon).use(Form).use(Input).use(InputNumber).use(Select).use(Switch).use(Radio).use(Checkbox).use(Button)
  .use(Row).use(Col).use(Divider).use(Cascader).use(TreeSelect).use(DatePicker).use(TimePicker).use(Upload).use(Tag).use(Popover)
  .use(Spin).use(Timeline).use(Tooltip).use(Tabs).use(Statistic).use(Modal).use(AutoComplete).use(Descriptions).use(Carousel)
  .use(Empty).use(Popconfirm).use(Progress).use(Collapse).use(FormModel).use(Drawer).use(List).use(Steps).use(Space).use(Alert).use(Skeleton)
Vue.component('page', page)
Vue.component('ni-list-page', NiListPage)
Vue.component('ni-filter', NiFilter)
Vue.component('ni-filter-item', NiFilterItem)
Vue.component('ni-table', NiTable)
Vue.component('NiImg', NiImg)
Vue.component('NiLog', NiLog)
Vue.component('ni-filter-item', NiFilterItem)
Vue.component('AModalWithLoading', WrappedModal)
Vue.component('op-jump', opJump)
Vue.use(DialogPlugin)

Vue.prototype.$message = message
Vue.prototype.$notification = notification
Vue.prototype.$info = Modal.info
Vue.prototype.$success = Modal.success
Vue.prototype.$error = Modal.error
Vue.prototype.$warning = Modal.warning
Vue.prototype.$confirm = Modal.confirm
Vue.prototype.$api = api
Vue.prototype.$bus = bus
Vue.prototype.$clipboard = Clipboard
Vue.prototype.$isDevProd = process.env.NODE_ENV === 'development'
Vue.prototype.$isIteng = [10002, 10003, 10004].includes(tnt.tenantId)
Vue.prototype.$indicator = {
  open () {
    store.commit('setLoading', true)
  },
  close () {
    store.commit('setLoading', false)
  }
}

store.commit('setToken', '589630920F9B45B2BAF22F17963400CF')
if (!store.state.token) {
  let token = ''
  if (process.env.NODE_ENV === 'development') {
    const search = window.location.search.replace(/\?/, '')
    let json = qs.parse(search)
    token = json.token || cookie.get('pcOaToken') || ''
    console.log('token:', json)
  } else {
    token = cookie.get('pcOaToken') || ''
  }
  store.commit('setToken', token)
}

const handleVisibilityChange = function () {
  if (!document.hidden) {
    console.log('页面显示了')
    const token = cookie.get('pcOaToken') || ''
    console.log(`当前获取的token:${token}`)
    console.log(`当前保存的token:${store.state.token}`)
    if (token && store.state.token !== token) {
      store.commit('setToken', token)
    }
  }
}
if (process.env.NODE_ENV !== 'development' && tnt.xtenant < 1000) {
  document.addEventListener('visibilitychange', handleVisibilityChange)
}

// https://github.com/rotick/axues
const axues = createAxues(axios, {
  requestConfig: () => ({
    timeout: 30000,
    headers: { Authorization: store.state.token }
  }),
  transformUseOptions (options) {
    if (options.errorOverlay) {
      options.errorHandleStrategy = 1
    }
    return options
  },
  responseHandle (response, {
    responseHandleStrategy,
    errorHandleStrategy
  }) {
    if (response.data.code === 0) {
      return responseHandleStrategy === 1 ? response.data : response.data.data
    }
    if (response.data.code === 1000) { // 未登录
      router.push('/login')
      return new Error('Unauthorized')
    }
    if (response.data.code === 1002) { // 无权限
      return new Error('无权限')
    }
    errorHandleStrategy !== 1 && message.error(response.data.userMsg)
    return new Error(response.data.userMsg)
  },
  errorHandle (err, { errorHandleStrategy }) {
    const errMsg = `AxiosError[${err.response.status}] ${err.config.url}: ${err.message}`
    errorHandleStrategy !== 1 && message.error(errMsg)
    return new Error(errMsg)
  },
  errorReport (err) {
    errorReport.sendLog('ERROR', err)
  },
  // 全局交互组件，都可以判断options.style来扩展样式
  overlayImplement: {
    loadingOpen (options) {
      store.commit('setLoading', true)
    },
    loadingClose () {
      store.commit('setLoading', false)
    },
    confirm (options) {
      return new Promise((resolve, reject) => {
        Modal.confirm({
          title: options.title,
          content: options.content,
          onOk: resolve,
          onCancel: reject
        })
      })
    },
    success (options) {
      if (!options.content) {
        message.success(options.title)
      } else {
        Modal.success({
          title: options.title,
          content: options.content,
          onOk: options.callback
        })
      }
    },
    error (options) {
      if (!options.content) {
        message.error(options.title)
      } else {
        Modal.error({
          title: options.title,
          content: options.content,
          onOk: options.callback
        })
      }
    }
  }
})
Vue.use(axues.vue2Plugin)

window.top.resizeIframe && window.top.resizeIframe()
var loadMark = (options) => {
  // const tntTitle = ((![53000, 50000].includes(tnt.xtenant)) || tnt.isShowWaterMark) ? tnt.title : ''
  const tntTitle = store.state.userInfo.isShowWaterMark ? store.state.outPutCompanyName : ''
  let config = Object.assign({
    watermark_txt: `${tntTitle} ${store.state.userInfo.UserName}(${store.state.userInfo.UserID}) \r\n ${dateFormat(Date.now(), 'yyyy-MM-dd hh:mm:ss')}`,
    watermark_cols: 0,
    watermark_width: 150,
    watermark_height: 100,
    watermark_x_space: 150,
    watermark_fontsize: '14px',
    watermark_alpha: 0.07
  }, options)
  watermark.init(config)
  window.hasWatermark = true
}
window.hasWatermark = false
var addMark = (options) => {
  if (store.state.userInfo) {
    loadMark(options)
  } else {
    store.dispatch('getUserInfo').then(() => {
      loadMark(options)
    })
  }
}
var delMark = () => {
  if (window.hasWatermark) {
    watermark.remove()
  }
  window.hasWatermark = false
}
bus.$on('addWatermark', addMark)
bus.$on('delWatermark', delMark)

Vue.prototype.$authById = function (id) {
  return new Promise((resolve, reject) => {
    let { auth } = store.state
    if (auth.length > 0) {
      resolve(auth.indexOf(id) > -1)
    } else {
      let timeout = null
      let clear = () => {
        timeout = null
        clearTimeout(timeout)
      }
      timeout = setTimeout(() => {
        clear()
        reject(new Error('timeout'))
      }, 2000)
      bus.$once('setAuth', (auth) => {
        if (timeout) {
          clear()
          resolve(auth.indexOf(id) > -1)
        }
      })
    }
  })
}
if (process.env.NODE_ENV === 'production') {
  window.errorReport = new ErrorReport({ serviceName: 'oa-pc' })
  Vue.config.errorHandler = (err, vm, info) => {
    errorReport.sendLog('ERROR', err)
    console.error(err)
  }
} else {
  window.errorReport = {
    sendLog: function () {
    }
  }
}
tnt.xtenant < 1000 && initGlobalReportMqtt()
/* eslint-disable no-new */
new Vue({
  el: '#app',
  store,
  router,
  render: h => h(App)
})
