{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    //    "strict": true,
    "allowJs": true,
    "jsx": "preserve",
    "importHelpers": true,
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "sourceMap": true,
    "baseUrl": ".",
    //    "types": [
    //      "webpack-env",
    //      "mocha",
    //      "chai"
    //    ],
    "paths": {
      "~/*": ["src/*"],
      "@/*": ["src/once-ending/*"],
      "@common/*": ["src/once-ending/common/*"],
      "@hr/*": ["src/once-ending/hr/*"],
      "@market/*": ["src/once-ending/market/*"]
    },
    "lib": ["esnext", "dom", "dom.iterable", "scripthost"]
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "tests/**/*.ts",
    "tests/**/*.tsx",
  ],
  "exclude": ["node_modules"]
}
