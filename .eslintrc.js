module.exports = {
  root: true,
  parser: 'vue-eslint-parser',
  parserOptions: {
    ecmaFeatures: {
      jsx: true
    },
    sourceType: 'module',
    ecmaVersion: 2020
  },
  extends: [
    // https://github.com/standard/standard/blob/master/docs/RULES-en.md
    'standard',
    // https://github.com/vuejs/eslint-plugin-vue#priority-a-essential-error-prevention
    // consider switching to `plugin:vue/strongly-recommended` or `plugin:vue/recommended` for stricter rules.
    // 'plugin:vue/essential',
  ],
  // required to lint *.vue files
  plugins: [
    'html',
    'vue',
  ],
  // add your custom rules here
  rules: {
    // script标签缩进设置
    'vue/script-indent': ['error', 2, {
      baseIndent: 1,
      switchCase: 0,
      ignores: [],
    }],
    'vue/multi-word-component-names': 'off',
    'vue/vue3-essential': 'off',
    'vue/no-mutating-props': 'off',
    // 'vue/script-setup-uses-vars': 'off',
    // allow debugger during development
    'no-debugger': process.env.NODE_ENV === 'production' ? 2 : 0,

    // allow trailing commas
    'comma-dangle': [0, 'always-multiline'],

    // 有人投了票, 此项不做要求, https://github.com/ecomfe/spec/issues/23
    'prefer-const': 'off',

    // // 放松部分未使用变量的检测:
    // 'no-unused-vars': [2,
    //   {
    //     vars: 'all', // 变量定义必须被使用
    //     args: 'none', // 对于函数形参不检测
    //     ignoreRestSiblings: true, // 忽略剩余子项 fn(...args)，{a, b, ...coords}
    //     caughtErrors: 'none', // 忽略 catch 语句的参数使用
    //   },
    // ],

    'quotes': ['error', 'single', { allowTemplateLiterals: true }],
    'quote-props': ['error', 'consistent-as-needed'],
    // 'vue/no-parsing-error': ['error', {
    //   // https://github.com/vuejs/eslint-plugin-vue/issues/370
    //   'invalid-first-character-of-tag-name': false,
    // }],

    // 以下是否有必要限制:
    'vue/no-unused-vars': 'off',
    // 'arrow-parens': [2, 'as-needed', { requireForBlockBody: true }],

    // TODO: 待改 (新版eslint报错的规则) (建议每次处理一条规则, 处理完commit后再处理下一条)
    'no-irregular-whitespace': 0,
    'no-undef': 0,
    'no-tabs': 0,
    'no-mixed-spaces-and-tabs': 0,
    'no-async-promise-executor': 0,
    'no-prototype-builtins': 0,
    'no-unused-vars': 0, // 这条改完后放开前面该规则的注释
    'vue/valid-v-model': 0,
    'vue/require-valid-default-prop': 0,
    'vue/require-prop-type-constructor': 0,
    'vue/no-unused-components': 0,
    'vue/no-parsing-error': 0, // 这条改完后放开前面该规则的注释
    'vue/valid-v-on': 0,
    'vue/require-render-return': 0,
    'vue/valid-v-for': 0,
    'vue/no-reserved-keys': 0,
    'vue/valid-v-if': 0,
    'vue/valid-template-root': 0,
    'vue/return-in-computed-property': 0,
    'vue/no-duplicate-attributes': 0,
    'vue/require-v-for-key': 0,
    'vue/no-use-v-if-with-v-for': 0,
    'no-void': ['error', { allowAsStatement: true }]
  },
  overrides: [
    {
      files: ['*.vue'],
      rules: {
        indent: 'off',
      },
    },
    // {
    //   files: ['src/pages/index.vue', 'src/pages/**/index.vue'],
    //   rules: {
    //     'vue/multi-word-component-names': 'off'
    //   }
    // },
  ],
}
