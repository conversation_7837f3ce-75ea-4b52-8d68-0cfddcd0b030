import { defineConfig, mergeConfig, loadEnv, splitVendorChunkPlugin } from 'vite'
import { createHtmlPlugin } from 'vite-plugin-html'
import { resolve } from 'path'
import axios from 'axios'
import moment from 'moment'
import legacy from '@vitejs/plugin-legacy'
import { visualizer } from 'rollup-plugin-visualizer'
import { proxyConfig } from './config/proxy.options'
import { baseConfig, createStyleImportPlugin, antdv1MmomentResolver, Antd1Resolver, requireTransform, renderBuiltUrl } from '@jiuji/vite-common'
import autoprefixer from 'autoprefixer'
import consoleBanner from './build/console-banner'

function toLineLowerCase (str) {
  return str.replace(/([A-Z])/g, (s, _, i) => {
    if (!i) return s.toLowerCase()
    return `-${s.toLowerCase()}`
  })
}

function getConfig (xtenant) {
  return new Promise((resolve, reject) => {
    void axios.get(
      'https://manager.saas.ch999.cn/saasManager/api/configProvider/oneConfig/v1',
      {
        params: { businessId: xtenant },
        headers: {
          Accept: 'application/json',
          secret: 'jiuji_service'
        }
      }
    ).then(response => {
      if (response.status >= 200 && response.status < 400) {
        if (response.data.code === 0) {
          resolve(response.data.data)
        } else {
          reject(new Error(response.data.userMsg))
          console.log(response.data.userMsg)
        }
      } else {
        reject(new Error(response.status + '-' + response.statusText))
        console.log(response.status + '-' + response.statusText)
      }
    })
  })
}

function getDevConfig (tenantName = 'dev') {
  return new Promise((resolve, reject) => {
    void axios({
      method: 'get',
      url: 'https://m.9ji.com/test/getTenantConfig'
    }).then(response => {
      if (response.status >= 200 && response.status < 400) {
        if (response.data.code === 0) {
          const tenant = response.data.data?.tenants.find(t => t.name === tenantName)
          if (!tenant) {
            reject(new Error('未获取到租户配置！！'))
            return
          }
          resolve(tenant)
        } else {
          reject(new Error(response.data.userMsg))
          console.log(response.data.userMsg)
        }
      } else {
        reject(new Error(response.status + '-' + response.statusText))
        console.log(response.status + '-' + response.statusText)
      }
    })
  })
}

export default defineConfig(async ({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())

  const xtenant = Number(env.VITE_XTENANT_ENV) || 0
  const version = moment().format('YYYYMMDDHHmmss')
  const tenant = env.VITE_DEV ? await getDevConfig() : await getConfig(xtenant)
  const isPreview = env.VITE_PREV === '1'
  console.log('当前是预览模式：', isPreview)
  console.log('当前租户：', tenant.title)
  return mergeConfig(baseConfig({ basePath: __dirname })(mode, command), {
    esbuild: {},
    resolve: {
      alias: [
        { find: 'vue', replacement: resolve(__dirname, 'node_modules/vue/dist/vue.runtime.esm.js') },
        { find: 'hls.js', replacement: resolve(__dirname, 'node_modules/hls.js') },
        { find: '@', replacement: resolve(__dirname, './src/once-ending') },
        { find: '@common', replacement: resolve(__dirname, './src/once-ending/common') },
        { find: '@layout', replacement: resolve(__dirname, './src/once-ending/common/layout') },
        { find: '@hr', replacement: resolve(__dirname, './src/once-ending/hr') },
        { find: '@market', replacement: resolve(__dirname, './src/once-ending/market') },
        { find: '@demo', replacement: resolve(__dirname, './src/once-ending/demo') },
        { find: '@retrieve', replacement: resolve(__dirname, './src/once-ending/retrieve') },
        { find: '@logistics', replacement: resolve(__dirname, './src/once-ending/logistics') },
        { find: '@operation', replacement: resolve(__dirname, './src/once-ending/operation') },
        { find: '@finance', replacement: resolve(__dirname, './src/once-ending/finance') },
        { find: '@member', replacement: resolve(__dirname, './src/once-ending/member') },
        { find: '@office', replacement: resolve(__dirname, './src/once-ending/office') },
      ]
    },
    define: {
      'process.title': '"browser"',
      'process.env.BUILD_TYPE': '\'vite\''
    },
    experimental: {
      renderBuiltUrl
    },
    css: {
      postcss: {
        plugins: [
          autoprefixer({
            overrideBrowserslist: [
              '> 0.5%',
              'last 2 versions',
              'ie > 11'
            ]
          })
        ]
      }
    },
    build: {
      assetsInlineLimit: 1024,
      rollupOptions: {
        output: {
          hoistTransitiveImports: false,
          assetFileNames: () => {
            return 'assets/[name].[hash].[ext]'
          }
        }
      }
    },
    plugins: [
      requireTransform(),
      antdv1MmomentResolver(),
      createStyleImportPlugin({
        resolves: [
          Antd1Resolver(),
          {
            ensureStyleFile: true,
            libraryName: 'element-ui',
            resolveStyle: (name) => {
              return `element-ui/lib/theme-chalk/${toLineLowerCase(name)}.css`
            },
            injectLibChange: (name) => {
              return `element-ui/lib/${toLineLowerCase(name)}`
            }
          }
        ]
      }),
      ...mode !== 'production'
        ? [
          createHtmlPlugin({
            template: './src/index.dev.html',
            entry: '/src/main.js',
            inject: {
              data: {
                htmlWebpackPlugin: {
                  options: {
                    tenant, version, tenantString: JSON.stringify(tenant)
                  }
                }
              }
            }
          })
        ] : [
          createHtmlPlugin({
            template: './src/index.html',
            filename: 'index.html',
            entry: '/src/main.js',
            inject: {
              data: {
                isPreview,
                htmlWebpackPlugin: {
                  options: {
                    version, tenantString: JSON.stringify(tenant)
                  }
                }
              }
            }
          }),
          legacy({
            targets: ['defaults']
          }),
          visualizer({
            gzipSize: true
          }),
          splitVendorChunkPlugin(),
          consoleBanner()
        ]
    ],
    preview: {
      open: true,
      ...proxyConfig(tenant)
    },
    server: {
      ...proxyConfig(tenant)
    }
  })
})
