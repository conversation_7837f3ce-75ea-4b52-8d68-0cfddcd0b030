export const proxyConfig = (tenant) => {
  return {
    port: { jiuji2: 3939, dev: 4949, yidian: 9968 }[tenant.name] || 9988,
    proxy: {
      '/mdevice': {
        target: (tenant.moaHost + '/oa'),
        // target: 'http://**************:8185',
        // target: 'http://***********:8089/',
        changeOrigin: true
      },
      '/manage-training': {
        target: tenant.moaHost,
        // target: 'http://**************:8185',
        // target: 'http://***********:8089/',
        changeOrigin: true
      },
      '/b2bTenant/api': {
        target: 'https://dc.999buy.com',
        changeOrigin: true
      },
      '/web': {
        // target: 'https://www.9ji.com/',
        target: 'https://m.dev.9ji.com/',
        changeOrigin: true
      },
      '/Evaluate': {
        target: tenant.oaHost,
        changeOrigin: true,
      },
      '/mainTain': {
        target: tenant.moaHost,
        changeOrigin: true,
      },
      '/productMkc': {
        target: tenant.moaHost,
        changeOrigin: true,
      },
      '/productMKC': {
        target: tenant.oaHost,
        changeOrigin: true,
      },
      '/member': {
        target: tenant.oaHost,
        changeOrigin: true,
      },
      '/shouhou': {
        target: tenant.oaHost,
        changeOrigin: true,
      },
      '/autoTransfer': {
        target: tenant.oaHost,
        // target: 'http://***********:8000/', // 谢熊坤
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/cloudapi_nc/, '')
      },
      '/office': {
        target: tenant.moaHost,
        // target: 'http://**********:11020/',
        changeOrigin: true
      },
      '/org_service': {
        // target: tenant.moaHost,
        target: 'http://***********:11011/', // 钱凯
        changeOrigin: true
      },
      '/salary_service': {
        // target: tenant.moaHost,
        target: 'http://**********:11030/',
        changeOrigin: true
      },
      '/cloudapi_nc/office': { // 办公接口测试代理
        target: tenant.moaHost,
        // target: 'http://**********:11020/',
        changeOrigin: true,
        pathRewrite: {
          '^/cloudapi_nc': '',
        },
      },
      '/instructor': {
        target: tenant.moaHost,
        // target: 'http://***********:8180/', // 李思仪服务
        // target: 'https://oa.9ji.com/',
        changeOrigin: true,
        pathRewrite: {
          '^/instructor': ''
        }
      },
      '/coach': {
        target: (tenant.moaHost + '/oa'),
        // target: 'https://demo.oa.9xun.com/',
        // target: 'https://oa.9ji.com/',
        changeOrigin: true
      },
      '/Upload': {
        target: (tenant.moaHost + '/oa'),
        // target: 'https://demo.oa.9xun.com/',
        // target: 'https://oa.9ji.com/',
        changeOrigin: true
      },
      '/api/getAreaInfoAll': {
        target: tenant.moaHost,
        changeOrigin: true,
      },
      '/StockingCheck': {
        target: (tenant.moaHost + '/oa'),
        changeOrigin: true,
      },
      '/api': {
        target: (tenant.moaHost + '/oa'),
        // target: 'https://demo.oa.9xun.com/',
        // target: 'https://oa.9ji.com/',
        // target: 'http://**********:12345', // 宋昌 dev
        changeOrigin: true
      },
      '/caiwu': {
        target: 'https://oa.dev.9ji.com/',
        changeOrigin: true
      },
      '/commonApi': {
        target: tenant.moaHost,
        // target: 'https://moa.9ji.com', //生产环境,慎用
        // target: 'https://moa.dev.9ji.com',
        // target: 'https://demo.oa.9xun.com/',
        // target: 'https://moa.zhilefang.com/oa',
        // target: 'https://moa.iteng.com/oa',
        // target: 'https://moa.dev.9ji.com/oa',
        // target: 'https://oa.dev.9ji.com/',
        changeOrigin: true,
      },
      '/mock': {
        target: 'http://**********:4523/mock/378452',
        changeOrigin: true,
        pathRewrite: {
          '^/mock': '',
        },
      },
      '/cloudapi_nc/oa-train-service': {
        target: tenant.moaHost,
        // target: 'https://oa.dev.9ji.com/',
        // target: 'http://***********:44944/', // 李思仪 dev
        // target: 'http://***********:4397/', // 李思仪 dep
        // target: 'http://**********:12306/', // 宋昌 dev
        // target: 'http://***********:44944/', // 应杰
        // target: 'http://**********:44944/', // 骆超
        // target: 'http://**********:12306/', // 刘希
        // target: 'http://**********:44944/', // 杨天翔
        // target: 'http://***********:44944/', // 刘兴祥
        // target: 'http://**********:12306/', // 和黎佳
        changeOrigin: true,
        pathRewrite: {
          '^/cloudapi_nc': '',
        },
      },
      '/NewApply': {
        target: (tenant.moaHost + '/oa'),
        changeOrigin: true
      },
      '/HelpCenter': {
        target: (tenant.moaHost + '/oa'),
        // target: 'https://moa.dev.9ji.com/',
        changeOrigin: true,
        pathRewrite: {
          '^/oa': ''
        }
      },
      '/noticeApi': {
        target: (tenant.moaHost + '/oa'),
        // target: 'http://**********:5002',
        changeOrigin: true,
        pathRewrite: {
          '^/oa': ''
        }
      },
      '/ajaxapi': {
        target: (tenant.moaHost + '/oa'),
        // target: 'http://**********:5002',
        changeOrigin: true,
        pathRewrite: {
          '^/oa': ''
        }
      },
      '/docapi': {
        target: tenant.moaHost,
        changeOrigin: true
      },
      '/ajaxApi': {
        target: tenant.moaHost,
        changeOrigin: true
      },
      '/ch999UserApi': {
        // target: 'http://**********:5006/',
        target: (tenant.moaHost + '/oa'),
        changeOrigin: true
      },
      '/Ch999User': {
        // target: 'https://moa.ch999.com',
        target: 'https://oa.dev.9ji.com/',
        // target: (tenant.moaHost + '/'),
        changeOrigin: true
      },
      '/ch999User': {
        target: 'https://oa.dev.9ji.com/',
        changeOrigin: true
      },
      '/app': {
        // target: 'https://oa.dev.9ji.com/',
        target: (tenant.moaHost + '/oa'),
        changeOrigin: true
      },
      '/oaapi': {
        // target: 'https://www.9ji.com',
        target: (tenant.moaHost + '/'),
        changeOrigin: true
      },
      '/cloudapi_nc/orderservice': {
        target: tenant.moaHost,
        // target: 'http://***********:10005/', // 应杰
        // target: 'http://***********:11005/', // 刘文武本地
        // target: 'http://***********:11005/', // 李思仪本地
        // target: 'http://**********:10052/', // 王梅y
        // target: 'http://**********:11005/', // 宋昌
        // target: 'http://**********2:11005/', //张友奎
        // target: 'http://**********:11008/', // 谢熊坤
        // target: 'http://***********:11005/', // 沈扬
        // target: 'http://***********:11005/', // 张欢
        changeOrigin: true,
        pathRewrite: {
          '^/cloudapi_nc': '',
        },
      },
      '/cloudapi_nc/org_service': {
        target: tenant.moaHost,
        // target: 'http://***********:11011/', // 刘文武本地
        // target: 'http://***********:11011/', // 李思仪本地
        // target: 'http://***********:11011/', // 杨郑宇本地
        // target: 'http://**********:11011/', // 骆超
        // target: 'http://***********:11011/', // 应杰
        // target: ' http://**********:11011/', // 刘希
        // target: 'http://**********:11011/', // 和黎佳
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/cloudapi_nc/, '')
        pathRewrite: {
          '^/cloudapi_nc': '',
        },
      },
      '/cloudapi_nc/coupon': {
        target: 'https://moa.dev.9ji.com/',
        changeOrigin: true,
      },
      '/tiemoCard': {
        target: 'https://oa.dev.9ji.com/',
        changeOrigin: true,
      },
      '/addorder': {
        target: 'https://oa.dev.9ji.com/',
        changeOrigin: true,
      },
      '/back': {
        target: 'https://oa.dev.9ji.com/',
        changeOrigin: true,
      },
      '/addOrder': {
        target: 'https://oa.dev.9ji.com/',
        changeOrigin: true,
      },
      '/Operator': {
        target: (tenant.moaHost + '/oa'),
        changeOrigin: true,
      },
      '/cloudapi_nc/afterservice': {
        target: tenant.moaHost,
        // target: 'http://***********:11007/', // 刘文武本地
        // target: 'http://***********:11007/', // 李思仪
        // target: 'http://***********:11007/', // 李权 注意不同的服务端口不同
        // target: 'http://**********:11007/', // 孙梓雄
        // target: 'http://***********:11007/', // 应杰
        // target: 'http://***********:11007/', // 戚魏清 注意不同的服务端口不同
        // target: 'http://**********2:11007/', // 应杰
        // target: 'http://***********:11007/', // 谢熊坤
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/cloudapi_nc/, '')
      },
      '/cloudapi_nc/ncSegments': {
        target: tenant.moaHost,
        // target: 'https://oa.dev.9ji.com/',
        // target: 'http://**********:10005/', // 刘希
        // target: 'http://***********:10005/',
        // target: 'http://**********:10005', // 骆超
        // target: 'http://**********:11010/', // 董 注意不同的服务端口不同
        // target: 'http://**********:10005', // 杨天翔
        // target: 'http://***********:10005', // 燕小江
        // target: 'http://***********:10005', // 付兴祥
        // target: 'http://***********:10005', // 张欢
        // target: 'http://***********:10005', // 聂凡
        changeOrigin: true,
        pathRewrite: {
          '^/cloudapi_nc': '',
        },
      },
      '/cloudapi_nc/workflow': {
        target: tenant.moaHost,
        // target: 'http://***********:16736/', // 刘文武本地
        // target: 'http://***********:11008/', // 李思仪
        // target: 'http://***********:11007/', // 李权 注意不同的服务端口不同
        changeOrigin: true,
        pathRewrite: {
          '^/cloudapi_nc': '',
        },
      },
      // '/cloudapi_nc/ncSegments/api': {
      //   // target: tenant.moaHost,
      //   target: 'http://***********:10005/', // 李云龙 注意不同的服务端口不同
      //   changeOrigin: true,
      //   pathRewrite: {
      //     '^/cloudapi_nc': '',
      //   },
      // },
      '/cloudapi_nc/ncSegments/api/workorderinfo': {
        target: tenant.moaHost,
        // target: 'http://***********:10005/', // 李云龙 注意不同的服务端口不同
        // target: 'http://**********:10005/', // 李云龙 注意不同的服务端口不同
        changeOrigin: true,
        pathRewrite: {
          '^/cloudapi_nc': '',
        },
      },
      '/cloudapi_nc/huishou/api': {
        target: tenant.pcHost,
        changeOrigin: true,
        pathRewrite: {
          '^/cloudapi_nc': '',
        },
      },
      '/cloudapi_nc/data-service': {
        target: 'https://www.9xun.com',
        changeOrigin: true,
      },
      '/cloudapi_nc': {
        target: tenant.moaHost,
        // target: 'https://moa.9ji.com/cloudapi_nc', //生产环境,慎用
        // target: 'http://***********:8081/',
        // target: 'https://demo.oa.9xun.com/',
        // target: 'http://***********:10005/', //李特杰
        // target: ' http://**********:10005/', // 刘希
        // target: 'http://***********:10005/', // 刘文武
        // target: 'http://**********:10005/', // 骆超
        // target: 'http://***********:10005/', // 戚魏清 注意不同的服务端口不同
        // target: 'http://***********:11007/', // 谢益达
        // target: 'http://***********:10005/', // 陈昌龙 注意不同的服务端口不同
        // target: 'http://***********:10005/', // 李云龙 注意不同的服务端口不同
        // target: 'http://***********:10086/', // 李权 注意不同的服务端口不同
        // target: 'http://***********:10005/', // 李思仪
        // target: 'http://***********:20005/', // 李思仪 dev环境
        // target: 'http://**********:10052/', // 王梅
        // target: 'http://***********:10005/', // 应杰
        // target: 'http://**********:10005/', // 杨天翔
        // target: 'https://moa.ch999.com/api',
        // target: 'http://**********:11010/', // 董 注意不同的服务端口不同
        // target: 'http://**********:10005/', // f
        // target: 'http://10.1.14.178:10039/', // 吴亚雄
        // target: 'http://10.1.14.175:10039/', // 陈刘明
        // target: 'http://**********2:10005/', // 张友奎
        // target: 'http://**********3:10038/', // 吴瑶瑶
        // target: 'http://10.1.14.64:10039/', // 刘昊楠
        // target: 'http://10.1.23.72:8081/', // 王农高
        // target: 'http://**********:10005/', // 和黎佳
        // target: 'http://10.1.14.39:10039/', // 李瑞霞
        // target: 'http://10.1.13.98:10005', // 方鑫
        // target: 'http://10.1.14.155:8081', // 张琳翔
        // target: 'http://***********:10005', // 付兴祥
        // target: 'http://10.1.23.140:10005', // 张欢
        // target: 'http://10.1.28.166:10005', // 聂凡
        // target: 'http://***********:10005', // 聂凡
        changeOrigin: true,
        pathRewrite: {
          '^/cloudapi_nc': '',
        },
      },
      '/smallpro': {
        target: 'http://***********:881/',
        changeOrigin: true
      },
      '/Smallpro': {
        target: 'http://***********:881/',
        changeOrigin: true
      },
      '/oa_finance': {
        target: 'http://************:11017',
        changeOrigin: true
      },
      '/keliu': {
        target: (tenant.moaHost + '/keliu'),
        changeOrigin: true,
        pathRewrite: {
          '^/keliu': ''
        }
      },
      '/medium': {
        target: (tenant.moaHost + '/oa'),
        // target: 'https://demo.oa.9xun.com/',
        // target: 'https://oa.9ji.com',
        changeOrigin: true
      },
      '/newstatic': {
        target: 'https://img2.ch999img.com',
        // target: 'https://demo.oa.9xun.com/',
        changeOrigin: true
      },
      '/ch999user': {
        target: (tenant.moaHost + '/oa'),
        // target: 'https://demo.oa.9xun.com/',
        // target: 'http://oa.9ji.com/',
        changeOrigin: true
      },
      '/ValuationApi': {
        // target: 'https://demo.oa.9xun.com/',
        target: (tenant.moaHost + '/oa'),
        // target: 'https://moa.ch999.com/oa/',
        changeOrigin: true
      },
      '/valuationapi': {
        target: (tenant.moaHost + '/oa'),
        // target: 'https://demo.oa.9xun.com/',
        changeOrigin: true
      },
      '/Valuation ': {
        target: (tenant.moaHost + '/oa'),
        // target: 'https://demo.oa.9xun.com/',
        changeOrigin: true
      },
      '/recoverIndex': {
        target: (tenant.moaHost + '/oa'),
        // target: 'https://demo.oa.9xun.com/',
        // target: 'http://oa.9ji.com/',
        changeOrigin: true
      },
      '/recoverindexapi': {
        target: (tenant.moaHost + '/oa'),
        // target: 'https://demo.oa.9xun.com/',
        // target: 'http://oa.9ji.com/',
        changeOrigin: true
      },
      '/recoverIndexApi': {
        target: (tenant.moaHost + '/oa'),
        // target: 'https://moa.ch999.com/oa/',
        changeOrigin: true,
      },
      '/orderservice': {
        target: tenant.moaHost,
        // target: 'http://***********:11005/',
        // target: 'http://***********:11005/', // 李思仪本地
        // target: 'http://************:4049/', // 沈扬
        // target: 'http://***********:11005/', // 张欢
        changeOrigin: true,
      },
      '/zeus': {
        // target: tenant.moaHost,
        target: 'http://***********:9988',
        changeOrigin: true
      },
      '/rtc': {
        // target: tenant.moaHost,
        target: 'https://webrtc.saas.ch999.cn',
        changeOrigin: true
      },
      '/kcApi': {
        target: (tenant.moaHost + '/oa'),
        changeOrigin: true
      },
      '/tousu': {
        target: 'https://oa.dev.9ji.com/', // 测试环境
        changeOrigin: true,
      },
      '/HROAApi': {
        target: (tenant.moaHost + '/oa'),
        changeOrigin: true
      },
      '/productKC': {
        target: 'https://test01.oa.saas.ch999.cn/',
        changeOrigin: true
      },
      '/Index': {
        target: (tenant.moaHost + '/oa'),
        changeOrigin: true,
      },
      '/index': {
        target: (tenant.moaHost + '/oa'),
        // target: 'http://**********:62697/',
        changeOrigin: true,
      },
      '/login': {
        target: 'https://oa.dev.9ji.com/',
        changeOrigin: true,
      },
      '/piao': {
        target: 'https://oa.dev.9ji.com/',
        changeOrigin: true,
      },
      '/OAMenu': {
        target: (tenant.moaHost + '/oa'),
        // target: 'http://**********:62697/',
        changeOrigin: true,
      },
      '/Suggestion': {
        target: 'https://oa.dev.9ji.com/',
        changeOrigin: true,
      },
      '/kcapi': {
        target: 'https://oa.dev.9ji.com/',
        changeOrigin: true,
      },
      '/BeDirectConnections': {
        target: 'https://oa.dev.9ji.com/',
        changeOrigin: true,
      },
    }
  }
}
