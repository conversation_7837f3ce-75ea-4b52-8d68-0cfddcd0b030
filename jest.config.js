module.exports = {
  preset: '@vue/cli-plugin-unit-jest',
  collectCoverage: true,
  collectCoverageFrom: [
    '**/@hr/views/custom-benefits/**/*.{js,vue,jsx}',
    '!**/stories/**',
    '!**/test/**',
    '!**/node_modules/**',
  ],
  moduleNameMapper: {
    '~/(.*)$': '<rootDir>/src/$1',
    '@/(.*)$': '<rootDir>/src/once-ending/$1',
    '@common/(.*)$': '<rootDir>/src/once-ending/common/$1',
    '@hr/(.*)$': '<rootDir>/src/once-ending/hr/$1'
  },
  coverageDirectory: 'coverage'
}
