module.exports = function (api) {
  api.cache(true)
  return {
    presets: [
      [
        '@babel/preset-env',
        {
          modules: false
        }
      ],
      // '@vue/babel-preset-jsx',
      '@vue/cli-plugin-babel/preset'
    ],
    plugins: [
      ['component', { libraryName: 'element-ui', styleLibraryName: 'theme-chalk' }],
      ['import', { libraryName: 'ant-design-vue', libraryDirectory: 'es', style: true }],
      // ['import', { libraryName: 'antDesignVue', libraryDirectory: 'es', style: 'css' }], // 测试用
      [
        '@babel/plugin-transform-runtime',
        {
          corejs: false,
          helpers: false,
          regenerator: true,
          useESModules: false
        }
      ],
      '@babel/plugin-transform-object-assign',
      '@babel/plugin-proposal-object-rest-spread',
      '@babel/plugin-syntax-dynamic-import',
      '@babel/plugin-proposal-optional-chaining'
    ]
    // 'env': {
    //   'development': {
    //     'plugins': [
    //       'dynamic-import-node'
    //     ]
    //   }
    // }
  }
}
