{"name": "oa-pc", "description": "OA系统", "author": "<PERSON>naebi", "private": true, "scripts": {"lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --fix --ext .js,.vue src", "test:unit": "vue-cli-service test:unit", "dev": "cross-env VITE_XTENANT_ENV=0 NODE_OPTIONS=--max-old-space-size=8192 vite --host --config vite.config.js", "dev:dev": "cross-env VITE_DEV=1 NODE_OPTIONS=--max-old-space-size=8192 vite --host --config vite.config.js", "dev:sass": "cross-env VITE_XTENANT_ENV=50000 NODE_OPTIONS=--max-old-space-size=8192 vite --host --config vite.config.js", "dev:zlf": "cross-env VITE_XTENANT_ENV=1000 NODE_OPTIONS=--max-old-space-size=8192 vite --config vite.config.js", "dev:cdyp": "cross-env VITE_XTENANT_ENV=137000 NODE_OPTIONS=--max-old-space-size=8192 vite --config vite.config.js", "dev:chixin": "cross-env VITE_XTENANT_ENV=132000 NODE_OPTIONS=--max-old-space-size=8192 vite --config vite.config.js", "dev:hlt": "cross-env VITE_XTENANT_ENV=53000 NODE_OPTIONS=--max-old-space-size=8192 vite --config vite.config.js", "dev:hwx": "cross-env VITE_XTENANT_ENV=51000 NODE_OPTIONS=--max-old-space-size=8192 vite --config vite.config.js", "dev:iteng": "cross-env VITE_XTENANT_ENV=4000 NODE_OPTIONS=--max-old-space-size=8192 vite --config vite.config.js", "dev:gziteng": "cross-env VITE_XTENANT_ENV=3021 NODE_OPTIONS=--max-old-space-size=8192 vite --config vite.config.js", "dev:hn7xing": "cross-env VITE_XTENANT_ENV=58000 NODE_OPTIONS=--max-old-space-size=8192 vite --config vite.config.js", "preview": "cross-env VITE_XTENANT_ENV=0 NODE_OPTIONS=--max-old-space-size=8192 vite preview --config vite.config.js && npm run build:help-entry && npm run build:dev-info && npm run build:annotation", "build:prev": "cross-env VITE_XTENANT_ENV=0 VITE_PREV=1 NODE_OPTIONS=--max-old-space-size=8192 vite build --config vite.config.js && npm run build:help-entry && npm run build:dev-info && npm run build:annotation", "build": "npm run lint && cross-env VITE_XTENANT_ENV=0 NODE_OPTIONS=--max-old-space-size=8192 vite build --config vite.config.js && npm run build:help-entry && npm run build:dev-info && npm run build:annotation", "build:help-entry": "vite build --config build/help-entry.config.js", "build:dev-info": "vite build --config build/dev-info.config.js", "build:annotation": "vite build --config build/notation.config.js", "sonar:scan": "node sonar-project.js", "commit": "git-cz"}, "dependencies": {"@antv/g2": "^4.2.0", "@antv/g6": "^3.4.1", "@antv/hierarchy": "^0.4.0", "@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-plugin-stencil": "^2.1.5", "@antv/x6-plugin-transform": "^2.1.8", "@jiuji/dplayer": "^1.26.0", "@jiuji/live-player": "^1.0.1", "@jiuji/nine-editor": "^0.3.45", "@jiuji/nine-ui": "^2.0.0-rc.258", "@jiuji/nine-ui-dev": "^2.0.0-beta.146", "@jiuji/nine-upload": "^3.4.0", "@jiuji/qr-uploader": "^1.0.3", "@jiuji/stats": "^1.0.6", "@jiuji/vue-dplayer": "^1.0.1", "@moefe/vue-aplayer": "^2.0.0-beta.5", "@popperjs/core": "^2.11.8", "@x-quantum/form-builder": "^1.0.4", "@x-quantum/vue-use": "^1.0.2", "@x-quantum/x-widgets": "^1.0.4", "ant-design-vue": "^1.7.3", "aplayer": "^1.10.1", "axios": "^0.21.1", "axues": "^0.6.1", "bignumber.js": "^9.1.1", "buffer": "^6.0.3", "caniuse-lite": "^1.0.30001198", "clipboard": "^2.0.8", "clone": "^2.1.2", "conscript": "^0.2.0", "core-js": "^3.6.4", "cropperjs": "^1.5.6", "css-vars-ponyfill": "^2.2.1", "downloadjs": "^1.4.7", "dplayer": "^1.26.0", "echarts": "4.9.0", "echarts5": "npm:echarts@^5.5.0", "element-ui": "^2.13.0", "es6-map": "^0.1.5", "es6-promise": "^4.2.5", "exif-js": "^2.3.0", "file-saver": "^2.0.0-rc.4", "flipbook-vue": "^0.8.0", "flv.js": "^1.6.2", "font-awesome": "^4.7.0", "git-cz": "^4.7.6", "hls.js": "^1.2.0", "html2canvas": "^1.3.3", "insert-css": "^2.0.0", "intersection-observer": "^0.5.0", "jquery": "^3.4.1", "js-base64": "^3.7.2", "js-calendar-converter": "^0.0.6", "js-cookie": "^2.2.0", "jsbarcode": "^3.11.0", "jspdf": "^2.4.0", "kt-canvas-table": "^1.0.4", "lodash": "^4.17.20", "md5": "^2.2.1", "medium-zoom": "^1.0.6", "moment": "^2.24.0", "mqtt": "^4.2.8", "nanoid": "^2.1.11", "onscan.js": "^1.5.2", "pdfjs-dist": "2.5.207", "pinyin-match": "^1.2.5", "process": "^0.11.10", "qrcode": "^1.4.4", "qrious": "^4.0.2", "qs": "^6.9.1", "scrollbooster": "^3.0.2", "silvermine-videojs-quality-selector": "=1.1.2", "smoothscroll-polyfill": "^0.4.3", "sortablejs": "^1.15.0", "spark-md5": "^3.0.1", "stompjs": "^2.3.3", "uuid": "^3.4.0", "v-charts": "^1.19.0", "v-viewer": "^1.5.1", "vee-validate": "^2.0.0-beta.17", "video.js": "=6.6.3", "videojs-contrib-hls": "=5.14.1", "videojs-flash": "^2.2.1", "videojs-hotkeys": "=0.2.20", "viewerjs": "^1.5.0", "vue": "^2.7.0", "vue-aplayer": "^1.6.1", "vue-awesome": "^3.1.3", "vue-awesome-swiper": "^3.1.3", "vue-croppa": "^1.3.8", "vue-dplayer": "0.0.10", "vue-draggable-resizable": "^2.2.0", "vue-iframe-print": "^1.0.1", "vue-jstree": "^2.1.6", "vue-print-nb": "^1.6.3", "vue-router": "^3.6.5", "vue-virtual-scroller": "^1.1.2", "vue2-teleport": "^1.1.4", "vuedraggable": "^2.24.3", "vuex": "^3.1.3", "web-streams-polyfill": "^2.1.0", "webrtc-adapter": "^7.7.0", "webstomp-client": "^1.2.4", "xlsx": "^0.17.1", "gsap": "^3.10.4"}, "devDependencies": {"@commitlint/cli": "^12.0.1", "@commitlint/config-conventional": "^12.0.1", "@jiuji/oa-group-cli": "^1.0.1", "@jiuji/vite-common": "^1.0.12", "@vitejs/plugin-legacy": "^4.0.4", "@vue/cli-service": "^5.0.8", "autoprefixer": "^9.7.4", "babel-eslint": "^10.1.0", "cache-loader": "^4.1.0", "commitizen": "^4.2.3", "cross-env": "^6.0.3", "css-loader": "^0.28.1", "cz-customizable": "^6.3.0", "eslint": "^7.28.0", "eslint-config-enough": "^0.4.3", "eslint-config-standard": "^14.1.1", "eslint-loader": "^4.0.2", "eslint-plugin-html": "^6.0.2", "eslint-plugin-import": "^2.21.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^7.20.0", "husky": "^4.3.0", "less": "^3.12.2", "less-loader": "^4.1.0", "lint-staged": "^10.5.4", "mockjs": "^1.1.0", "postcss-loader": "^3.0.0", "quill": "^1.3.6", "rimraf": "^2.6.2", "rollup-plugin-visualizer": "^5.9.0", "sass": "^1.27.0", "sass-loader": "^10.0.1", "sonarqube-scanner": "^2.8.1", "standard": "^17.0.0", "style-loader": "^0.23.1", "ts-loader": "^8.0.2", "typescript": "^4.7.1", "url-loader": "^0.5.8", "validator": "^13.7.0", "vite": "^4.3.8", "vite-plugin-html": "^3.2.0", "vue-demi": "^0.13.1", "vue-eslint-parser": "^7.1.0", "vue-loader": "^15.10.0", "webpack": "^4.17.1", "yargs": "^16.2.0"}, "engines": {"node": ">=6"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -e $HUSKY_GIT_PARAMS"}}, "lint-staged": {"src/**/*.{js,vue}": "eslint --fix"}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}}