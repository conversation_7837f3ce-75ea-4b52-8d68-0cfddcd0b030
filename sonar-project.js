const yargs = require('yargs/yargs')
const { hideBin } = require('yargs/helpers')
const argv = yargs(hideBin(process.argv)).argv

const projectKey = argv.projectKey || 'fe-oa-pc-dev'
const sources = argv.sources || 'src'

const scanner = require('sonarqube-scanner')

scanner(
  {
    serverUrl: 'http://**************:19000',
    token: '528fbfcf1af6a60b155396232a73157a8cd30551',
    options: {
      'sonar.projectKey': `${projectKey}:sonarqube-scanner`,
      'sonar.projectName': `${projectKey}`,
      'sonar.projectDescription': 'oa后台管理',
      'sonar.sources': `${sources}`
    }
  },
  () => process.exit()
)
