import { mount, createLocalVue } from '@vue/test-utils'
import antDesignVue from 'ant-design-vue'
import welfareEdit from '@hr/views/custom-benefits/components/welfare-edit.vue'
// import welfareSettings from '@hr/views/custom-benefits/components/welfare-settings.vue'
const localVue = createLocalVue()
localVue.use(antDesignVue)
describe('Test welfare-edit.vue component', () => {
  let wrapper
  beforeEach(() => {
    wrapper = mount(welfareEdit, {
      propsData: {
        editType: 0,
        width: '35%',
        visible: true,
        welfareInfo: {
          id: '',
          subsidyName: undefined,
          fixedValue: undefined,
          remark: undefined,
          isDelete: false,
          isFixedValue: false,
        }
      },
      data () {
        return {
          labelCol: { span: 4 },
          wrapperCol: { span: 18 },
          rules: {
            subsidyName: [
              {
                required: true,
                message: '请输入补贴名称',
                trigger: 'blur'
              },
              {
                max: 20,
                message: '补贴名称过长，最大字符限制20'
              }
            ],
            fixedValue: [
              {
                required: true,
                type: 'number',
                message: '请输入固定值',
                trigger: 'blur'
              }
            ],
            remark: [
              {
                required: true,
                message: '请输入补贴备注信息',
                trigger: 'blur'
              },
              {
                max: 200,
                message: '备注信息不能超过200个字符',
                trigger: 'blur'
              }
            ],
            isDelete: [
              {
                required: true,
                message: '请选择是否启用',
                trigger: 'blur'
              }
            ]
          }
        }
      },
      localVue
    })
  })
  it('shouid reader welfare-edit.vue component', () => {
    expect(wrapper.exists()).toBe(true)
  })
  it('shouid trigger okModal with click ok', async () => {
    const okBtn = wrapper.findAll('.ant-btn').at(1)
    const okModal = jest.fn()
    await wrapper.setMethods({
      okModal: okModal
    })
    await okBtn.trigger('click')
    expect(okModal).toHaveBeenCalled()
  })
  it('shouid trigger closeModal with click cancel', async () => {
    const cancelBtn = wrapper.findAll('.ant-btn').at(0)
    const closeModal = jest.fn()
    await wrapper.setMethods({
      closeModal: closeModal
    })
    await cancelBtn.trigger('click')
    expect(closeModal).toHaveBeenCalled()
  })
})
// describe('Test welfare-settings.vue component', () => {
//   let wrapper
//   beforeEach(() => {
//     wrapper = mount(welfareSettings, {
//       data () {
//         return {
//           columns: [
//             {
//               title: '补贴名称',
//               align: 'center',
//               dataIndex: 'subsidyName'
//             },
//             {
//               title: '固定值',
//               align: 'center',
//               dataIndex: 'fixedValue'
//             },
//             {
//               title: '备注',
//               align: 'center',
//               dataIndex: 'remark'
//             },
//             {
//               title: '操作',
//               align: 'center',
//               scopedSlots: { customRender: 'operation' }
//             },
//             {
//               title: '启用',
//               dataIndex: 'isDelete',
//               align: 'center',
//               scopedSlots: { customRender: 'isDelete' }
//             }
//           ], // 表格头
//           visible: false, // 新建页面显示与否
//           editType: 0, // 0 新增 1 修改
//           searchValue: '', // 搜索值
//           tableData: [], // 数据
//           totalData: 0, // 总条数
//           pageSize: 10, // 每页条数
//           current: 1, // 当前页
//           welfareInfo: { // 默认新建数据
//             id: '',
//             subsidyName: undefined,
//             fixedValue: undefined,
//             remark: undefined,
//             isDelete: false,
//             isFixedValue: false,
//           }
//         }
//       },
//       localVue
//     })
//   })
//   it('shouid reader welfare-settings.vue component', () => {
//     expect(wrapper.exists()).toBe(true)
//   })
// })
