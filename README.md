<!-- TOC -->
- [ui示例](#ui示例)
- [调试](#调试)
- [代码检查](#代码检查)
- [组件库](#组件库)
- [样式/静态资源](#样式静态资源)
- [状态管理](#状态管理)
- [路由](#路由)
- [数据交互](#数据交互)
- [登录及用户校验](#登录及用户校验)
- [cookie](#cookie)
- [权限校验](#权限校验)

<!-- /TOC -->

## ui示例
https://preview.pro.ant.design

## 调试

1. 下载安装最新版 [nodejs](http://nodejs.cn/download/)
2. npm 国内镜像、内网私有源配置
   ```bash
   # 配置npm下载源
   npm config set registry https://registry.npm.taobao.org/
   # 配置九机内网下载源
   npm config set @jiuji:registry http://npm.9ji.com:4873/  // 其他内部包
   npm config set @x-quantum:registry http://npm.9ji.com:4873/  // 表单生成器
   # 查看是否配置成功
   npm config list
   ```
3. clone并进入此项目
4. 安装依赖

	```bash
	npm install
	```
5. 运行服务并调试

	``` bash
	npm run dev <tenantName>
	# tenantName: jiuji (不填时的默认值) | zlf | iteng | demo | ...
    # 栗如 npm run dev jiuji
	```
 ```js
// 调试配置 @webpack.congig.js
 devServer: {
       host: '0.0.0.0',
       port: { jiuji: 1000, iteng: 2000 }[tenantName] || 4396, // 租户端口配置
       proxy: devProxy(options),
     },

// 租户token配置 @main.js
    // console.log('租户token配置', tnt)
    switch (tnt.xtenant) {
      case 0: // jiuji
        cookie.set('pcOaToken', 'E1E7801BAFEA4D7EBF98F4E547F0A35A')
        break
      case 5: // iteng
        cookie.set('pcOaToken', '3189CE2C0E3A4116ADD0C14EE8D87A04')
        break
      case 2:
        cookie.set('pcOaToken', '3189CE2C0E3A4116ADD0C14EE8D87A04')
        break
      default:
        cookie.set('pcOaToken', 'B153F114D4A04087849C97DE21CD98B0')
    }
```
 
    登录: 线上环境登录成功后, 将 cookie 中的 pcOaToken 复制到 main.js  
    
	因为浏览器限制跨域请求，调试过程请做代理来访问api服务，配置`build/dev-proxy.js`即可（更改之后要重启服务）  
	 调试模式使用了租户配置，启动不同的服务使用不同的端口号，
	 jiuji为1000，对应运行命令为 npm run dev
	 iteng为2000，对应运行命令为 npm run dev:iteng
	 其他的端口为4396；如有需要，请自行添加
6. 编译

	```bash
	npm run build
	```
7. 发布

	把要发布的合并到主分支，在 jenkins（https://ci.9ji.com/view/%E5%89%8D%E7%AB%AF%E7%A7%9F%E6%88%B7/）上发布即可
        
    发布到仿真： 把开发分支合并到 test 分支，在 jenkins 上点发布即可

  

## 代码检查
- 为了开发统一，也为了代码简洁易懂，更为了防止低级错误，项目使用了最严格代码检查`eslint standard`，详情：https://github.com/feross/standard/blob/master/RULES.md#javascript-standard-style
- 强烈建议开发时使用 webstorm 编辑器，如果写了不合规范的代码，编辑器会报错，并提供修复错误代码的功能。
- 每次build前先运行风格检查`npm run lint`，没有报错则表示没问题，`npm run lint:fix`会修复一些错误，但很多还得自己改

## 组件库
项目已集成了[Ant Design of Vue](https://vuecomponent.github.io/ant-design-vue/docs/vue/introduce-cn/)，提供了较丰富的基础组件

使用组件请参考[Ant Design of Vue文档](https://vuecomponent.github.io/ant-design-vue/docs/vue/introduce-cn/)

如果需要其他的组件，请自行安装依赖或者写了放在components目录下，注意，组件需要能在任意地方引用，请编写可复用组件，如果只用在局部，直接写页面上或者用mixin

## 样式/静态资源
- 所有的静态资源都放入assets文件夹
- flex能满足的布局就不要用其他布局方式
- 公共样式里集成了颜色和其他一些常用的样式，不要写很多不必要的样式

	如：
	```html
	<div class="flex flex-center">
	  <span class="font-16 blue">16px蓝色文字，左右上下都居中</span>
	</div>
	```
- 图片等比缩放，文字/元素流式布局
- 项目集成了[font-awesome](http://fontawesome.io/icons/)，能用字体图标的尽量用字体图标

## 状态管理
状态管理一般用来做全局的变量管理，如从订单列表页点到订单详情页，删除了此订单，再返回订单列表，此时订单列表需要变化。做法是把订单列表的数据存在store中，改变订单状态时提交一个mutation来改变这个state，一改变则引用到这个state的地方都会自动更新。

注意：
1. 不要把所有的数据都存入store，这样反而更难维护，仅仅存入多处引用的数据，比如说用户的token，购物车的数量，热搜词，用户的基本信息等。
2. 相似的数据，不要添加多个state，用getter来取
3. 如果项目较大，请使用module，方便管理

可以安装vue-devtool来调试store，详情请参考[vuex文档](https://vuex.vuejs.org/zh-cn/)

## 路由
- 除了首页，所有组件都用异步组件，router.js已定义好load方法。
- oa项目的页面默认需要登录的，不需要登录的, 给组件加`meta: {requireAuth: false}`
- 把路由归类，放在children里

## 数据交互
为了统一管理，所有的api接口都在`src/api/index.js`里写方法，并指定参数。这样做是为了让每个接口的参数都透明化，方便更改/维护，也方便后端查看接口的参数。如登录方法：
```javascript
login(param={username:'',password:''}){
  return fetch('/api/login', params, 'post')
}
//或
login(username='',password=''){
  return fetch('/api/userLogin', {username,password}, 'post')
}
```

api已注入vue实例，可在组件中调用`this.$api.someMethod()`，如调用登录方法：
```javascript
this.$api.login({username:'aaa',password:'bbb'}).then(res=>{
//res是api返回的内容
})
```

fetch函数是对axios的封装，在`src/api/fetch.js`，如有需求，可更改axios的配置，详情参考[axios的文档](https://github.com/mzabriskie/axios)  

涉及微服务的请参考`src/api/fetch-xservice.js`
   该文件通过封装暴露不同的微服务函数，使用方式和fetch保持一致
   ```javascript
   export function manageTraining (url = '', params = {}, method = 'get', contentType = 'form') {
     return xserviceFetch('manageTraining', url, params, method, contentType)
   }
   export function orderservice (url = '', params = {}, method = 'get', contentType = 'form') {
     return xserviceFetch('orderservice', url, params, method, contentType)
   }
   ```

## 登录及用户校验
- 每个请求都会在header里带`Authorization`，Authorization的值由登录接口返回，状态管理中对应的是token，如需更改，提交mutations中的setToken方法，为了XX天免登录功能，以及防止刷新页面丢失，token还存sessionStorage中，可根据需要改为存cookie或localStorage，注意`api/fetch.js`也需要更改。
- 登录判断只要判断store里的token有没有值就行了。`if(!this.$store.token){do someThing}`
- Authorization由程序做对称加密或者由OAuth服务提供，如果加密算法需要其他字段请直接与token拼接，比如用户id为86232，则Authorization为token_86232。
- 除了非必要登录的接口程序需获取header里的`Authorization`对用户进行校验。

## cookie
cookie请使用[js-cookie](https://github.com/js-cookie/js-cookie)库，如需要，可参考m-template项目注入组件中，示例（更多请查看文档）：
```javascript
this.$cookie.set('key',value,{expires:1});//时间单位为天
this.$cookie.get('key')
this.$cookie.remove('key')
```
cookie仅仅用来存储临时的并需要过期的内容，其他情况尽量用sessionStorage或localStorage

## 权限校验

`store` 中拿用户权限列表  

```javascript
    ...mapState({
        rank: state => (state.userInfo || {}).Rank || [] // rank对应用户的权限列表
      })
      canOperation () {
        return (this.rank.indexOf('6c0') + 1) && this.statusStr !== 'finish'
      }
```

一些简单情景, 可以用`v-rank` 指令隐藏或disable元素 [文档](./src/directives/rank/README.md)    
```vue
<div v-rank="['6f7']"> 某权限操作 </div>
<div v-rank:remove="['6f7']"> 某权限操作 </div>
<div v-rank:disable="['6f7']"> 某权限操作 </div>
```

